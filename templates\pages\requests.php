<?php
/**
 * 用户需求页面
 */

// 包含工具辅助函数（包含formatNumber函数）
require_once ROOT_PATH . '/app/helpers/tool-helpers.php';

/**
 * 根据投票数量获取对应的图标和样式
 */
function getVoteIcon($votes) {
    if ($votes >= 100) {
        return [
            'icon' => '🔥',
            'color' => 'text-red-400',
            'textColor' => 'text-red-400',
            'animation' => 'fire-animation',
            'label' => 'Super Hot Request - ' . $votes . ' votes'
        ];
    } elseif ($votes >= 50) {
        return [
            'icon' => '⭐',
            'color' => 'text-yellow-400',
            'textColor' => 'text-yellow-400',
            'animation' => 'star-twinkle',
            'label' => 'Popular Request - ' . $votes . ' votes'
        ];
    } elseif ($votes >= 20) {
        return [
            'icon' => '🚀',
            'color' => 'text-blue-400',
            'textColor' => 'text-blue-400',
            'animation' => 'rocket-rise',
            'label' => 'Rising Request - ' . $votes . ' votes'
        ];
    } elseif ($votes >= 10) {
        return [
            'icon' => '💡',
            'color' => 'text-green-400',
            'textColor' => 'text-green-400',
            'animation' => 'bulb-glow',
            'label' => 'Good Idea - ' . $votes . ' votes'
        ];
    } elseif ($votes >= 5) {
        return [
            'icon' => '👍',
            'color' => 'text-purple-400',
            'textColor' => 'text-purple-400',
            'animation' => '',
            'label' => 'Nice Suggestion - ' . $votes . ' votes'
        ];
    } elseif ($votes >= 1) {
        return [
            'icon' => '💭',
            'color' => 'text-gray-400',
            'textColor' => 'text-gray-400',
            'animation' => '',
            'label' => 'Initial Idea - ' . $votes . ' votes'
        ];
    } else {
        return [
            'icon' => '📝',
            'color' => 'text-gray-300',
            'textColor' => 'text-gray-300',
            'animation' => '',
            'label' => 'New Proposal - 0 votes'
        ];
    }
}

$currentPage = 'requests';
$breadcrumbs = [
    ['name' => 'Home', 'url' => '/'],
    ['name' => 'Tool Requests']
];

// 包含动态SEO助手
require_once ROOT_PATH . '/app/helpers/dynamic-seo.php';

// 获取动态SEO数据
$seoData = getDynamicSEOData('requests');

// 获取筛选参数（支持POST和GET）
$status = $_POST['status'] ?? $_GET['status'] ?? 'all';
$categoryFilter = $_POST['category'] ?? $_GET['category'] ?? 'all'; // 重命名避免冲突
$sort = $_POST['sort'] ?? $_GET['sort'] ?? 'latest';
$page = max(1, intval($_POST['page'] ?? $_GET['page'] ?? 1));

// 确保categoryFilter是字符串
if (is_array($categoryFilter)) {
    $categoryFilter = 'all'; // 如果意外是数组，重置为默认值
}
$limit = 5;
$offset = ($page - 1) * $limit;

// 构建查询条件
$whereConditions = [];
$params = [];

if ($status !== 'all') {
    $whereConditions[] = "r.status = ?";
    $params[] = $status;
}

if ($categoryFilter !== 'all') {
    $whereConditions[] = "r.category = ?";
    $params[] = $categoryFilter;
}

$whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

// 排序逻辑
$orderBy = match($sort) {
    'votes' => 'r.votes DESC, r.created_at DESC',
    'oldest' => 'r.created_at ASC',
    default => 'r.created_at DESC'
};

// 获取需求列表
$sql = "
    SELECT r.*, m.username, m.first_name, m.last_name,
           CASE WHEN rv.user_id IS NOT NULL THEN 1 ELSE 0 END as user_voted
    FROM pt_user_requests r
    LEFT JOIN pt_member m ON r.user_id = m.id
    LEFT JOIN pt_request_votes rv ON r.id = rv.request_id AND rv.user_id = ?
    {$whereClause}
    ORDER BY {$orderBy}
    LIMIT {$limit} OFFSET {$offset}
";

$userVoteParams = [isset($_SESSION['user_id']) ? $_SESSION['user_id'] : 0];
// 获取总数用于分页
$countSql = "SELECT COUNT(*) FROM pt_user_requests r {$whereClause}";
$countStmt = $pdo->prepare($countSql);
$countStmt->execute($params);
$totalRequests = $countStmt->fetchColumn();
$totalPages = ceil($totalRequests / $limit);

$requests = $pdo->prepare($sql);
$requests->execute(array_merge($userVoteParams, $params));
$requestsList = $requests->fetchAll();

// 调试信息已删除

// 获取统计数据
$stats = $pdo->query("
    SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
        SUM(CASE WHEN status = 'accepted' THEN 1 ELSE 0 END) as accepted,
        SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed
    FROM pt_user_requests
")->fetch();

// 从数据库获取工具分类
$categoryStmt = $pdo->query("SELECT slug, name FROM pt_tool_category WHERE status = 'active' ORDER BY sort_order ASC, name ASC");
$dbCategories = $categoryStmt->fetchAll();

// 构建分类数组
$categories = [];
foreach ($dbCategories as $categoryItem) {
    $categories[$categoryItem['slug']] = $categoryItem['name'];
}

// 添加其他分类
$categories['other'] = 'Other';

// 状态颜色函数
function getStatusColor($status) {
    $colors = [
        'pending' => 'bg-yellow-100 text-yellow-800',
        'reviewing' => 'bg-blue-100 text-blue-800',
        'accepted' => 'bg-green-100 text-green-800',
        'rejected' => 'bg-red-100 text-red-800',
        'completed' => 'bg-purple-100 text-purple-800'
    ];

    return $colors[$status] ?? 'bg-gray-100 text-gray-800';
}

include_once ROOT_PATH . '/templates/layout/header.php';
?>

<!-- 自定义样式 -->
<style>
    /* Line clamp utility classes */
    .line-clamp-4 {
        display: -webkit-box;
        -webkit-line-clamp: 4;
        line-clamp: 4;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .hover\:line-clamp-none:hover {
        display: block;
        -webkit-line-clamp: unset;
        line-clamp: unset;
        -webkit-box-orient: unset;
        overflow: visible;
    }

    /* 卡片悬停效果增强 */
    .group:hover .group-hover\:text-blue-300 {
        color: #93c5fd;
    }

    /* 投票按钮动画 */
    .group\/vote:hover .group-hover\/vote\:scale-110 {
        transform: scale(1.1);
    }

    /* 渐变背景动画 */
    .bg-gradient-to-br {
        background-size: 200% 200%;
        animation: gradient-shift 8s ease infinite;
    }

    @keyframes gradient-shift {
        0%, 100% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
    }

    /* 阴影效果 */
    .shadow-yellow-400\/20 {
        box-shadow: 0 10px 25px -3px rgba(251, 191, 36, 0.2), 0 4px 6px -2px rgba(251, 191, 36, 0.1);
    }

    .shadow-blue-400\/20 {
        box-shadow: 0 10px 25px -3px rgba(96, 165, 250, 0.2), 0 4px 6px -2px rgba(96, 165, 250, 0.1);
    }

    /* 投票图标动画效果 */

    /* 🔥 超高热度 - 燃烧动画效果 */
    .fire-animation {
        animation: fire-flicker 2s ease-in-out infinite alternate;
    }

    @keyframes fire-flicker {
        0%, 100% {
            transform: scale(1) rotate(-1deg);
            filter: hue-rotate(0deg) brightness(1);
        }
        25% {
            transform: scale(1.05) rotate(1deg);
            filter: hue-rotate(10deg) brightness(1.1);
        }
        50% {
            transform: scale(0.98) rotate(-0.5deg);
            filter: hue-rotate(-5deg) brightness(0.95);
        }
        75% {
            transform: scale(1.02) rotate(0.5deg);
            filter: hue-rotate(5deg) brightness(1.05);
        }
    }

    /* ⭐ 高热度 - 闪烁效果 */
    .star-twinkle {
        animation: star-twinkle 1.5s ease-in-out infinite;
    }

    @keyframes star-twinkle {
        0%, 100% {
            opacity: 1;
            transform: scale(1);
            filter: brightness(1);
        }
        50% {
            opacity: 0.7;
            transform: scale(1.1);
            filter: brightness(1.3);
        }
    }

    /* 🚀 中高热度 - 上升动画 */
    .rocket-rise {
        animation: rocket-rise 3s ease-in-out infinite;
    }

    @keyframes rocket-rise {
        0%, 100% {
            transform: translateY(0px) rotate(0deg);
        }
        25% {
            transform: translateY(-3px) rotate(-1deg);
        }
        50% {
            transform: translateY(-5px) rotate(0deg);
        }
        75% {
            transform: translateY(-2px) rotate(1deg);
        }
    }

    /* 💡 中等热度 - 发光效果 */
    .bulb-glow {
        animation: bulb-glow 2.5s ease-in-out infinite;
    }

    @keyframes bulb-glow {
        0%, 100% {
            filter: brightness(1) drop-shadow(0 0 0px rgba(34, 197, 94, 0));
            transform: scale(1);
        }
        50% {
            filter: brightness(1.2) drop-shadow(0 0 8px rgba(34, 197, 94, 0.6));
            transform: scale(1.05);
        }
    }
</style>

<!-- 主要内容区域 -->
<div class="pt-8">
    <!-- Hero区域 -->
    <section class="bg-black py-6">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 class="text-4xl md:text-5xl font-bold text-white mb-6">
                Tool Requests
            </h1>
            <p class="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
                Request new AI tools and vote on community suggestions to help us prioritize development
            </p>

            <!-- 统计数据 -->
            <div class="grid grid-cols-2 md:grid-cols-4 gap-6 mt-12">
                <div class="bg-gray-900 border border-gray-800 p-6">
                    <div class="text-3xl font-bold text-white"><?= formatNumber($stats['total']) ?></div>
                    <div class="text-gray-400 mt-2">Total Requests</div>
                </div>
                <div class="bg-gray-900 border border-gray-800 p-6">
                    <div class="text-3xl font-bold text-yellow-400"><?= formatNumber($stats['pending']) ?></div>
                    <div class="text-gray-400 mt-2">Pending</div>
                </div>
                <div class="bg-gray-900 border border-gray-800 p-6">
                    <div class="text-3xl font-bold text-green-400"><?= formatNumber($stats['accepted']) ?></div>
                    <div class="text-gray-400 mt-2">Accepted</div>
                </div>
                <div class="bg-gray-900 border border-gray-800 p-6">
                    <div class="text-3xl font-bold text-blue-400"><?= formatNumber($stats['completed']) ?></div>
                    <div class="text-gray-400 mt-2">Completed</div>
                </div>
            </div>
        </div>
    </section>

    <!-- 主内容区域 -->
    <section class="bg-black py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-7 gap-8">
                <!-- 左侧：提交表单 -->
                <div class="lg:col-span-3">
                    <div class="bg-gray-900 border border-gray-800 p-8 sticky top-24">
                        <?php if (isset($_SESSION['user_id'])): ?>
                            <h2 class="text-xl font-semibold text-white mb-6">Submit Tool Request</h2>

                            <!-- URL分析区域 -->
                            <div class="mb-6 p-4 bg-gray-800 border border-gray-700">
                                <h3 class="text-lg font-medium text-white mb-4">🔍 Analyze Existing Tool</h3>
                                <div class="flex gap-3">
                                    <input type="url" id="tool-url" placeholder="Enter tool website URL..."
                                           class="flex-1 px-3 py-2 bg-gray-700 border border-gray-600 text-white focus:border-accent focus:outline-none">
                                    <button type="button" id="analyze-btn"
                                            class="px-6 py-2 bg-blue-600 text-white hover:bg-blue-700 transition-colors">
                                        Analyze
                                    </button>
                                </div>
                                <div id="analysis-status" class="mt-2 text-sm text-gray-400"></div>
                            </div>
                        <form id="request-form" class="space-y-4">
                            <div>
                                <label for="title" class="block text-sm font-medium text-gray-300 mb-2">Title</label>
                                <input type="text" id="title" name="title" maxlength="200" required
                                       class="w-full px-3 py-2 bg-gray-800 border border-gray-700 text-white text-sm focus:outline-none focus:border-accent"
                                       oninput="updateCharCount('title'); generateSlugFromTitleRequests()">
                                <div class="text-xs text-gray-400 mt-1" data-count-for="title">
                                    0/200 characters
                                </div>
                            </div>

                            <div>
                                <label for="slug" class="block text-sm font-medium text-gray-300 mb-2">URL Slug</label>
                                <input type="text" id="slug" name="slug" minlength="3" maxlength="60" required
                                       class="w-full px-3 py-2 bg-gray-800 border border-gray-700 text-white text-sm focus:outline-none focus:border-accent"
                                       pattern="[a-z0-9\-]+"
                                       placeholder="auto-generated-from-title"
                                       oninput="validateSlugRequests(this); updateSlugCountRequests()">
                                <div class="text-xs text-gray-400 mt-1">
                                    <span id="slug-count-requests">0</span>/60 characters (min 3) - SEO-friendly URL identifier
                                    <span id="slug-status-requests" class="ml-2"></span>
                                </div>
                            </div>

                            <div>
                                <label for="category" class="block text-sm font-medium text-gray-300 mb-2">Category</label>
                                <select id="category" name="category" required
                                        class="w-full px-3 py-2 bg-gray-800 border border-gray-700 text-white text-sm focus:outline-none focus:border-accent">
                                    <?php foreach ($categories as $value => $label): ?>
                                        <option value="<?= $value ?>"><?= $label ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div>
                                <label for="description" class="block text-sm font-medium text-gray-300 mb-2">Description</label>
                                <textarea id="description" name="description" rows="4" maxlength="1000" required
                                          class="w-full px-3 py-2 bg-gray-800 border border-gray-700 text-white text-sm focus:outline-none focus:border-accent"
                                          placeholder="Describe your feature request in detail..."
                                          oninput="updateCharCount('description')"></textarea>
                                <div class="text-xs text-gray-400 mt-1" data-count-for="description">
                                    0/1000 characters
                                </div>
                            </div>

                            <button type="submit" class="submit-btn w-full bg-accent text-white py-3 px-4 text-sm font-medium hover:bg-blue-700 hover:text-white transition-all duration-200 rounded-md transform hover:scale-105">
                                Submit Request
                            </button>
                        </form>
                        <?php else: ?>
                            <!-- 未登录用户提示 -->
                            <div class="text-center py-12">
                                <div class="mb-6">
                                    <div class="w-16 h-16 bg-gray-800 border border-gray-700 mx-auto mb-4 flex items-center justify-center">
                                        <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-9a2 2 0 00-2-2H6a2 2 0 00-2 2v9a2 2 0 002 2zm10-12V6a2 2 0 00-2-2H8a2 2 0 00-2 2v3m8 0V6a2 2 0 00-2-2H8a2 2 0 00-2 2v3"></path>
                                        </svg>
                                    </div>
                                    <h3 class="text-lg font-medium text-white mb-2">Submit Tool Request</h3>
                                    <p class="text-gray-400 mb-6">Please log in to submit a feature request and use AI analysis tools</p>
                                </div>
                                <a href="/auth/login" class="inline-block bg-accent text-white py-3 px-6 font-medium hover:bg-blue-700 hover:text-white transition-colors">
                                    Log In
                                </a>
                            </div>
                        <?php endif; ?>
                </div>
            </div>

            <!-- 右侧：需求列表 -->
            <div class="lg:col-span-4">
                <!-- 筛选和排序 -->
                <div class="bg-gray-900 border border-gray-800 p-6 mb-8">
                    <form method="POST" action="/requests" id="filter-form">
                        <div class="flex items-center gap-3 overflow-x-auto">
                            <!-- 状态筛选 -->
                            <div class="flex items-center gap-2 flex-shrink-0">
                                <label class="text-sm font-medium text-gray-300 whitespace-nowrap">Status:</label>
                                <select name="status" id="status-filter" class="px-2 py-2 bg-gray-800 border border-gray-700 text-white text-sm focus:outline-none focus:border-accent w-20">
                                    <option value="all" <?= $status === 'all' ? 'selected' : '' ?>>All</option>
                                    <option value="pending" <?= $status === 'pending' ? 'selected' : '' ?>>Pending</option>
                                    <option value="reviewing" <?= $status === 'reviewing' ? 'selected' : '' ?>>Reviewing</option>
                                    <option value="accepted" <?= $status === 'accepted' ? 'selected' : '' ?>>Accepted</option>
                                    <option value="completed" <?= $status === 'completed' ? 'selected' : '' ?>>Completed</option>
                                </select>
                            </div>

                            <!-- 分类筛选 -->
                            <div class="flex items-center gap-2 flex-shrink-0">
                                <label class="text-sm font-medium text-gray-300 whitespace-nowrap">Category:</label>
                                <select name="category" id="category-filter" class="px-2 py-2 bg-gray-800 border border-gray-700 text-white text-sm focus:outline-none focus:border-accent w-24">
                                    <option value="all" <?= $categoryFilter === 'all' ? 'selected' : '' ?>>All</option>
                                    <?php foreach ($categories as $value => $label): ?>
                                        <option value="<?= $value ?>" <?= $categoryFilter === $value ? 'selected' : '' ?>><?= htmlspecialchars($label) ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <!-- 排序 -->
                            <div class="flex items-center gap-2 flex-shrink-0">
                                <label class="text-sm font-medium text-gray-300 whitespace-nowrap">Sort:</label>
                                <select name="sort" id="sort-filter" class="px-2 py-2 bg-gray-800 border border-gray-700 text-white text-sm focus:outline-none focus:border-accent w-20">
                                    <option value="latest" <?= $sort === 'latest' ? 'selected' : '' ?>>Latest</option>
                                    <option value="votes" <?= $sort === 'votes' ? 'selected' : '' ?>>Most Voted</option>
                                    <option value="oldest" <?= $sort === 'oldest' ? 'selected' : '' ?>>Oldest</option>
                                </select>
                            </div>

                            <!-- 清除筛选按钮 -->
                            <button type="button" id="clear-filters" class="px-3 py-2 bg-gray-700 text-white text-sm hover:bg-gray-600 transition-colors whitespace-nowrap flex-shrink-0 ml-auto">
                                Clear
                            </button>
                        </div>
                    </form>
                </div>



                <!-- 需求列表 -->
                <div id="requests-container" class="space-y-6">
                    <?php if (empty($requestsList)): ?>
                        <div class="text-center py-12">
                            <div class="text-gray-400 mb-4">
                                <i class="fas fa-inbox text-4xl"></i>
                            </div>
                            <h3 class="text-lg font-medium text-gray-300 mb-2">No requests found</h3>
                            <p class="text-gray-400">Be the first to submit a tool request!</p>
                        </div>
                    <?php else: ?>
                        <?php foreach ($requestsList as $request): ?>
                            <div class="bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl p-6 hover:border-gray-600 hover:shadow-lg transition-all duration-300 group">
                                <div class="flex items-start space-x-6">
                                    <!-- 投票区域 - 优化设计 -->
                                    <div class="flex flex-col items-center min-w-[90px]">
                                        <button onclick="voteRequest(<?= $request['id'] ?>)"
                                                class="group/vote relative bg-gradient-to-br from-gray-800 to-gray-700 hover:from-gray-700 hover:to-gray-600 border-2 <?= $request['user_voted'] ? 'border-blue-400 shadow-blue-400/20 shadow-lg' : 'border-gray-600 hover:border-blue-400' ?> rounded-2xl p-4 transition-all duration-300 transform hover:scale-105 hover:shadow-xl <?= !isset($_SESSION['user_id']) ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer' ?>"
                                                <?= !isset($_SESSION['user_id']) ? 'disabled title="Please login to vote"' : 'title="Click to vote"' ?>>
                                            <div class="text-center">
                                                <?php
                                                $votes = (int)$request['votes'];
                                                $iconData = getVoteIcon($votes);
                                                ?>
                                                <div class="text-3xl mb-2 transition-transform duration-200 group-hover/vote:scale-110 <?= $iconData['animation'] ?>"
                                                     aria-label="<?= $iconData['label'] ?>"><?= $iconData['icon'] ?></div>
                                                <div class="text-xl font-bold <?= $iconData['color'] ?> transition-colors"><?= $request['votes'] ?></div>
                                                <div class="text-xs font-medium <?= $iconData['textColor'] ?> uppercase tracking-wide">votes</div>
                                            </div>

                                        </button>
                                    </div>

                                    <!-- 需求内容 - 优化布局 -->
                                    <div class="flex-1 min-w-0">
                                        <!-- 标题和标签区域 -->
                                        <div class="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-3 mb-4">
                                            <h3 class="text-xl font-bold text-white leading-tight group-hover:text-blue-300 transition-colors duration-200">
                                                <a href="/ideas/<?= htmlspecialchars($request['slug']) ?>"
                                                   class="hover:text-blue-300 transition-colors duration-200"
                                                   target="_blank"
                                                   rel="noopener noreferrer">
                                                    <?= htmlspecialchars($request['title']) ?>
                                                </a>
                                            </h3>
                                            <div class="flex items-center gap-2 flex-shrink-0">
                                                <span class="px-3 py-1.5 text-xs font-semibold rounded-full <?= getStatusColor($request['status']) ?> shadow-sm">
                                                    <?= ucfirst($request['status']) ?>
                                                </span>
                                                <span class="px-3 py-1.5 text-xs font-semibold rounded-full bg-gradient-to-r from-gray-700 to-gray-600 text-gray-200 shadow-sm">
                                                    <?= htmlspecialchars($categories[$request['category']] ?? ucfirst($request['category'])) ?>
                                                </span>
                                            </div>
                                        </div>

                                        <!-- 描述内容 -->
                                        <div class="mb-5">
                                            <p class="text-gray-300 leading-relaxed line-clamp-4 hover:line-clamp-none transition-all duration-300"><?= htmlspecialchars($request['description']) ?></p>
                                        </div>

                                        <!-- 元信息 -->
                                        <div class="flex items-center justify-between text-sm">
                                            <div class="flex items-center space-x-4 text-gray-400">
                                                <span class="flex items-center">
                                                    <svg class="w-4 h-4 mr-1.5" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                                                    </svg>
                                                    <?= htmlspecialchars($request['username'] ?? 'Anonymous') ?>
                                                </span>
                                                <span class="flex items-center">
                                                    <svg class="w-4 h-4 mr-1.5" fill="currentColor" viewBox="0 0 20 20">
                                                        <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                                                    </svg>
                                                    <?= date('M j, Y', strtotime($request['created_at'])) ?>
                                                </span>
                                            </div>
                                        </div>

                                        <!-- 管理员回复 - 简洁金色边框 -->
                                        <?php if (!empty($request['admin_reply'])): ?>
                                            <div class="mt-6 p-4 border-2 border-yellow-400 rounded-lg">
                                                <p class="text-sm font-semibold text-yellow-400 mb-2">Official Response</p>
                                                <p class="text-sm text-gray-300 leading-relaxed"><?= htmlspecialchars($request['admin_reply']) ?></p>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>

                <!-- Load More 按钮 -->
                <div id="load-more-container" class="mt-12 flex justify-center">
                    <?php if ($page < $totalPages): ?>
                        <div id="load-more-section" class="text-center">
                            <button id="load-more-btn" onclick="loadMoreRequests()"
                                    class="px-8 py-3 bg-accent text-white font-medium hover:bg-blue-700 hover:text-white transition-all duration-200 rounded-md transform hover:scale-105">
                                Load More Requests
                            </button>
                            <div id="loading-indicator" class="text-gray-400 mt-4" style="display: none;">
                                <i class="fas fa-spinner fa-spin mr-2"></i>
                                Loading...
                            </div>
                        </div>
                    <?php elseif ($totalRequests > 0): ?>
                        <div class="text-gray-400 text-lg text-center">
                            <i class="fas fa-check-circle mr-2"></i>
                            No more requests to load
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </section>
</div>

<!-- AI分析进度模态框 -->
<div id="ai-analysis-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center">
    <div class="bg-gray-900 border border-gray-700 rounded-lg p-8 max-w-md w-full mx-4">
        <div class="text-center">
            <div class="mb-6">
                <div class="inline-flex items-center justify-center w-16 h-16 bg-blue-600 rounded-full mb-4">
                    <svg class="w-8 h-8 text-white animate-spin" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-white mb-2">AI Analysis in Progress</h3>
                <p class="text-gray-400 mb-4">Please wait while we analyze the website...</p>
            </div>

            <!-- 进度步骤 -->
            <div class="space-y-3">
                <div class="flex items-center text-sm">
                    <div id="step-1-icon" class="w-5 h-5 mr-3 flex-shrink-0">
                        <div class="w-5 h-5 border-2 border-blue-600 rounded-full animate-pulse"></div>
                    </div>
                    <span id="step-1-text" class="text-blue-400">Crawling website content...</span>
                </div>

                <div class="flex items-center text-sm">
                    <div id="step-2-icon" class="w-5 h-5 mr-3 flex-shrink-0">
                        <div class="w-5 h-5 border-2 border-gray-600 rounded-full"></div>
                    </div>
                    <span id="step-2-text" class="text-gray-500">Extracting main content...</span>
                </div>

                <div class="flex items-center text-sm">
                    <div id="step-3-icon" class="w-5 h-5 mr-3 flex-shrink-0">
                        <div class="w-5 h-5 border-2 border-gray-600 rounded-full"></div>
                    </div>
                    <span id="step-3-text" class="text-gray-500">AI analysis processing...</span>
                </div>

                <div class="flex items-center text-sm">
                    <div id="step-4-icon" class="w-5 h-5 mr-3 flex-shrink-0">
                        <div class="w-5 h-5 border-2 border-gray-600 rounded-full"></div>
                    </div>
                    <span id="step-4-text" class="text-gray-500">Generating form data...</span>
                </div>
            </div>

            <!-- 取消按钮 -->
            <button id="cancel-analysis" class="mt-6 px-4 py-2 bg-gray-700 text-white text-sm rounded-md hover:bg-gray-600 transition-colors">
                Cancel
            </button>
        </div>
    </div>
</div>

<style>
/* 确保所有按钮和选择框不透明 */
button, select, input, textarea {
    opacity: 1 !important;
}

button:hover, select:hover, input:hover, textarea:hover {
    opacity: 1 !important;
}

button:disabled, select:disabled, input:disabled, textarea:disabled {
    opacity: 0.7 !important;
    cursor: not-allowed !important;
}

/* 投票按钮样式 */
.vote-btn {
    min-width: 60px;
    transition: all 0.2s ease;
}

.vote-btn:hover {
    transform: translateY(-1px);
    border-color: var(--accent-color) !important;
}

.vote-btn.voted {
    border-color: var(--accent-color) !important;
    background-color: rgba(37, 99, 235, 0.1) !important;
}

/* 选择框选中状态 */
select:focus {
    border-color: var(--accent-color) !important;
    box-shadow: 0 0 0 1px var(--accent-color) !important;
}

/* 表单输入框选中状态 */
input:focus, textarea:focus {
    border-color: var(--accent-color) !important;
    box-shadow: 0 0 0 1px var(--accent-color) !important;
}

/* 提交按钮状态 */
.submit-btn {
    transition: all 0.2s ease;
}

.submit-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(37, 99, 235, 0.3);
}

.submit-btn:disabled {
    opacity: 0.7 !important;
    transform: none !important;
    box-shadow: none !important;
}
</style>

<script>
// Load More 功能
let currentPage = <?= intval($page) ?>;
const totalPages = <?= intval($totalPages) ?>;

function loadMoreRequests() {
    if (currentPage >= totalPages) {
        return;
    }

    const loadBtn = document.getElementById('load-more-btn');
    const loadingIndicator = document.getElementById('loading-indicator');

    if (!loadBtn || !loadingIndicator) {
        return;
    }

    // 显示加载状态
    loadBtn.style.display = 'none';
    loadingIndicator.style.display = 'block';

    const formData = new FormData();
    formData.append('page', currentPage + 1);
    formData.append('status', '<?= htmlspecialchars($status, ENT_QUOTES) ?>');
    formData.append('category', '<?= htmlspecialchars($categoryFilter, ENT_QUOTES) ?>');
    formData.append('sort', '<?= htmlspecialchars($sort, ENT_QUOTES) ?>');

    fetch('/ajax/load-more-requests.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            // 将新内容添加到容器中
            const container = document.getElementById('requests-container');
            if (result.html && result.html.trim()) {
                container.insertAdjacentHTML('beforeend', result.html);
                currentPage++;
            }

            // 检查是否还有更多数据
            if (result.hasMore === false) {
                // 隐藏按钮和加载指示器
                loadBtn.style.display = 'none';
                loadingIndicator.style.display = 'none';

                // 在容器中添加完成提示，而不是替换整个内容
                const loadMoreContainer = document.getElementById('load-more-container');
                const completionMessage = document.createElement('div');
                completionMessage.className = 'text-gray-400 text-lg';
                completionMessage.innerHTML = '<i class="fas fa-check-circle mr-2"></i>No more requests to load';

                // 清空容器并添加完成消息
                loadMoreContainer.innerHTML = '';
                loadMoreContainer.appendChild(completionMessage);
            } else {
                // 恢复按钮显示，隐藏加载指示器
                loadBtn.style.display = 'block';
                loadingIndicator.style.display = 'none';
            }
        } else {
            showMessage('Failed to load more requests', 'error');
            loadBtn.style.display = 'block';
            loadingIndicator.style.display = 'none';
        }
    })
    .catch(error => {
        showMessage('Network error occurred', 'error');
        loadBtn.style.display = 'block';
        loadingIndicator.style.display = 'none';
    });
}

// 安全验证函数
function containsProhibitedContent(text) {
    const prohibitedPatterns = [
        // 脚本标签
        /<script/i,
        /<\/script>/i,
        /<iframe/i,
        /<object/i,
        /<embed/i,

        // 事件处理器
        /javascript:/i,
        /vbscript:/i,
        /onload/i,
        /onerror/i,
        /onclick/i,
        /onmouseover/i,

        // 网址模式
        /https?:\/\//i,
        /ftp:\/\//i,
        /www\./i,
        /\.com/i,
        /\.org/i,
        /\.net/i,
        /\.edu/i,
        /\.gov/i,

        // SQL注入
        /union\s+select/i,
        /drop\s+table/i,
        /delete\s+from/i,

        // 命令注入
        /\$\(/i,
        /`[^`]*`/i,
        /\|\s*nc\s/i,
        /\|\s*wget/i,
        /\|\s*curl/i,
    ];

    return prohibitedPatterns.some(pattern => pattern.test(text));
}

function validateInput(input, fieldName) {
    const value = input.value.trim();
    const errorElement = document.getElementById(fieldName + '-error');

    // 移除之前的错误提示
    if (errorElement) {
        errorElement.remove();
    }

    // 检查禁止内容
    if (containsProhibitedContent(value)) {
        showFieldError(input, 'Contains prohibited content. Please use plain text only.');
        return false;
    }

    // 长度检查
    if (fieldName === 'title') {
        if (value.length > 0 && value.length < 10) {
            showFieldError(input, 'Title must be at least 10 characters long.');
            return false;
        }
    } else if (fieldName === 'description') {
        if (value.length > 0 && value.length < 20) {
            showFieldError(input, 'Description must be at least 20 characters long.');
            return false;
        }
    }

    // 检查是否只包含特殊字符
    if (value.length > 0 && /^[^a-zA-Z0-9\s]+$/.test(value)) {
        showFieldError(input, 'Must contain alphanumeric characters.');
        return false;
    }

    return true;
}

function showFieldError(input, message) {
    const errorDiv = document.createElement('div');
    errorDiv.id = input.name + '-error';
    errorDiv.className = 'text-red-400 text-xs mt-1';
    errorDiv.textContent = message;
    input.parentNode.appendChild(errorDiv);
}

// 字符计数和实时验证
document.getElementById('title').addEventListener('input', function() {
    updateCharCount('title');
    validateInput(this, 'title');
});

document.getElementById('description').addEventListener('input', function() {
    updateCharCount('description');
    validateInput(this, 'description');
});

// 筛选器事件绑定

// 筛选器自动提交
document.getElementById('status-filter').addEventListener('change', function() {
    document.getElementById('filter-form').submit();
});

document.getElementById('category-filter').addEventListener('change', function() {
    document.getElementById('filter-form').submit();
});

document.getElementById('sort-filter').addEventListener('change', function() {
    document.getElementById('filter-form').submit();
});

// 清除筛选按钮
document.getElementById('clear-filters').addEventListener('click', function() {
    window.location.href = '/requests';
});

// URL分析功能
let analysisAbortController = null;

document.getElementById('analyze-btn').addEventListener('click', async function() {
    const url = document.getElementById('tool-url').value.trim();
    const statusDiv = document.getElementById('analysis-status');
    const btn = this;

    if (!url) {
        statusDiv.textContent = '❌ Please enter a valid URL';
        statusDiv.className = 'mt-2 text-sm text-red-400';
        return;
    }

    // 验证URL格式
    try {
        new URL(url);
    } catch (e) {
        statusDiv.textContent = '❌ Please enter a valid URL format';
        statusDiv.className = 'mt-2 text-sm text-red-400';
        return;
    }

    // 先检查配额是否足够，避免显示进度模态框
    btn.disabled = true;
    btn.textContent = 'Checking quota...';
    statusDiv.textContent = '🔍 Checking quota availability...';
    statusDiv.className = 'mt-2 text-sm text-blue-400';

    try {
        // 检查配额
        const quotaResponse = await fetch('/api/check-quota.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                action: 'tool_analysis' // 自定义检查10配额
            })
        });

        const quotaResult = await quotaResponse.json();

        if (!quotaResult.success) {
            statusDiv.textContent = '❌ Failed to check quota. Please try again.';
            statusDiv.className = 'mt-2 text-sm text-red-400';
            btn.disabled = false;
            btn.textContent = 'Analyze';
            return;
        }

        // 检查是否有足够的10配额
        const userQuota = quotaResult.current_quota;
        if (userQuota.used + 10 > userQuota.total) {
            statusDiv.textContent = '❌ Insufficient quota! Tool analysis requires 10 quota. Please upgrade your subscription plan.';
            statusDiv.className = 'mt-2 text-sm text-red-400';
            btn.disabled = false;
            btn.textContent = 'Analyze';
            return;
        }

    } catch (error) {
        statusDiv.textContent = '❌ Failed to check quota. Please try again.';
        statusDiv.className = 'mt-2 text-sm text-red-400';
        btn.disabled = false;
        btn.textContent = 'Analyze';
        return;
    }

    // 配额检查通过，显示模态框并开始分析
    showAnalysisModal();
    btn.textContent = 'Analyzing...';
    statusDiv.textContent = '';

    // 创建AbortController用于取消请求
    analysisAbortController = new AbortController();

    try {
        // 模拟进度步骤
        await simulateProgress();

        const response = await fetch('/ajax/analyze-tool.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ url: url }),
            signal: analysisAbortController.signal
        });

        const result = await response.json();

        if (result.success) {
            // 完成最后一步
            updateProgressStep(4, 'completed', 'Form data generated successfully!');

            // 延迟一下让用户看到完成状态
            await new Promise(resolve => setTimeout(resolve, 1000));

            // 填充表单
            document.getElementById('title').value = result.data.title;
            if (result.data.slug) {
                document.getElementById('slug').value = result.data.slug;
                updateSlugCountRequests();
                validateSlugRequests(document.getElementById('slug'));
            }
            document.getElementById('category').value = result.data.category;
            document.getElementById('description').value = result.data.description;

            // 更新字符计数
            updateCharCount('title');
            updateCharCount('description');

            // 隐藏模态框
            hideAnalysisModal();

            statusDiv.textContent = '✅ Analysis complete! Form filled automatically.';
            statusDiv.className = 'mt-2 text-sm text-green-400';

            // 清空URL输入框
            document.getElementById('tool-url').value = '';
        } else {
            hideAnalysisModal();
            statusDiv.textContent = '❌ ' + (result.message || 'Analysis failed');
            statusDiv.className = 'mt-2 text-sm text-red-400';
        }
    } catch (error) {
        if (error.name === 'AbortError') {
            statusDiv.textContent = '⏹️ Analysis cancelled';
            statusDiv.className = 'mt-2 text-sm text-yellow-400';
        } else {
            hideAnalysisModal();
            statusDiv.textContent = '❌ Network error occurred';
            statusDiv.className = 'mt-2 text-sm text-red-400';
        }
    } finally {
        btn.disabled = false;
        btn.textContent = 'Analyze';
        analysisAbortController = null;
    }
});

// 模态框控制函数
function showAnalysisModal() {
    document.getElementById('ai-analysis-modal').classList.remove('hidden');
    // 重置所有步骤状态
    resetProgressSteps();
    // 开始第一步
    updateProgressStep(1, 'active', 'Crawling website content...');
}

function hideAnalysisModal() {
    document.getElementById('ai-analysis-modal').classList.add('hidden');
}

function resetProgressSteps() {
    for (let i = 1; i <= 4; i++) {
        const icon = document.getElementById(`step-${i}-icon`);
        const text = document.getElementById(`step-${i}-text`);

        icon.innerHTML = '<div class="w-5 h-5 border-2 border-gray-600 rounded-full"></div>';
        text.className = 'text-gray-500';
    }
}

function updateProgressStep(step, status, message) {
    const icon = document.getElementById(`step-${step}-icon`);
    const text = document.getElementById(`step-${step}-text`);

    if (status === 'active') {
        icon.innerHTML = '<div class="w-5 h-5 border-2 border-blue-600 rounded-full animate-pulse"></div>';
        text.className = 'text-blue-400';
        text.textContent = message;
    } else if (status === 'completed') {
        icon.innerHTML = '<div class="w-5 h-5 bg-green-600 rounded-full flex items-center justify-center"><svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg></div>';
        text.className = 'text-green-400';
        text.textContent = message;
    }
}

async function simulateProgress() {
    // 步骤1: 爬取网站内容
    updateProgressStep(1, 'active', 'Crawling website content...');
    await new Promise(resolve => setTimeout(resolve, 1500));
    updateProgressStep(1, 'completed', 'Website content crawled successfully');

    // 步骤2: 提取主要内容
    updateProgressStep(2, 'active', 'Extracting main content...');
    await new Promise(resolve => setTimeout(resolve, 1000));
    updateProgressStep(2, 'completed', 'Main content extracted');

    // 步骤3: AI分析处理
    updateProgressStep(3, 'active', 'AI analysis processing...');
    await new Promise(resolve => setTimeout(resolve, 2000));
    updateProgressStep(3, 'completed', 'AI analysis completed');

    // 步骤4: 生成表单数据
    updateProgressStep(4, 'active', 'Generating form data...');
}

// 取消分析按钮
document.getElementById('cancel-analysis').addEventListener('click', function() {
    if (analysisAbortController) {
        analysisAbortController.abort();
    }
    hideAnalysisModal();
});

// 字符计数更新函数
function updateCharCount(fieldId) {
    const field = document.getElementById(fieldId);
    const countElement = document.querySelector(`[data-count-for="${fieldId}"]`);

    if (field && countElement) {
        const currentLength = field.value.length;
        const maxLength = fieldId === 'title' ? 200 : 1000;
        countElement.textContent = `${currentLength}/${maxLength} characters`;

        // 更新颜色
        if (currentLength > maxLength * 0.9) {
            countElement.className = 'text-xs text-red-400';
        } else if (currentLength > maxLength * 0.7) {
            countElement.className = 'text-xs text-yellow-400';
        } else {
            countElement.className = 'text-xs text-gray-400';
        }
    }
}

// 提交需求表单
document.getElementById('request-form')?.addEventListener('submit', async function(e) {
    e.preventDefault();

    // 前端验证
    const titleInput = document.getElementById('title');
    const descriptionInput = document.getElementById('description');

    if (!validateInput(titleInput, 'title') || !validateInput(descriptionInput, 'description')) {
        showMessage('Please fix the errors above before submitting.', 'error');
        return;
    }

    // 最终安全检查
    if (containsProhibitedContent(titleInput.value) || containsProhibitedContent(descriptionInput.value)) {
        showMessage('Your request contains prohibited content. Please use plain text only.', 'error');
        return;
    }

    const formData = new FormData(this);
    const button = this.querySelector('button[type="submit"]');
    const originalText = button.textContent;

    button.textContent = 'Submitting...';
    button.disabled = true;
    
    try {
        const response = await fetch('/ajax/submit-request.php', {
            method: 'POST',
            body: formData
        });

        // 检查HTTP状态
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        // 解析JSON响应
        const result = await response.json();

        if (result.success) {
            showMessage(result.message, 'success');
            this.reset();

            // 重置字符计数器
            const titleCounter = document.querySelector('[data-count-for="title"]');
            const descriptionCounter = document.querySelector('[data-count-for="description"]');
            if (titleCounter) titleCounter.textContent = '0/200 characters';
            if (descriptionCounter) descriptionCounter.textContent = '0/1000 characters';

            // 2秒后刷新页面显示新需求
            setTimeout(() => {
                window.location.reload();
            }, 2000);
        } else {
            showMessage(result.message, 'error');
        }
    } catch (error) {
        showMessage('Network error occurred: ' + error.message, 'error');
    } finally {
        button.textContent = originalText;
        button.disabled = false;
    }
});

// 投票功能
function voteRequest(requestId) {
    <?php if (!isset($_SESSION['user_id'])): ?>
        showMessage('Please login to vote', 'error');
        return;
    <?php endif; ?>

    fetch('/ajax/vote-request.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ request_id: requestId })
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            // 简单刷新页面以显示最新状态
            window.location.reload();
        } else {
            showMessage(result.message || 'Failed to vote', 'error');
        }
    })
    .catch(error => {
        showMessage('Network error occurred. Please try again.', 'error');
    });
}

// Load More函数在页面顶部已定义

// 消息提示函数
function showMessage(message, type) {
    type = type || 'info';
    const messageDiv = document.createElement('div');
    messageDiv.className = `fixed top-4 right-4 p-4 rounded-md z-50 ${
        type === 'success' ? 'bg-green-100 text-green-800 border border-green-200' : 
        'bg-red-100 text-red-800 border border-red-200'
    }`;
    messageDiv.textContent = message;
    
    document.body.appendChild(messageDiv);
    
    setTimeout(() => {
        messageDiv.remove();
    }, 3000);
}

// 从标题生成slug (requests页面版本)
function generateSlugFromTitleRequests() {
    const titleInput = document.getElementById('title');
    const slugInput = document.getElementById('slug');

    if (!titleInput.value.trim()) {
        slugInput.value = '';
        updateSlugCountRequests();
        return;
    }

    // 简化的slug生成逻辑（前端版本）
    let slug = titleInput.value.toLowerCase();

    // 移除特殊字符
    slug = slug.replace(/[^a-z0-9\s\-]/g, '');

    // 移除常见停用词
    const stopWords = ['add', 'create', 'build', 'make', 'develop', 'improve', 'for', 'with', 'to', 'in', 'on', 'at', 'the', 'a', 'an', 'tool', 'feature'];
    const words = slug.split(/\s+/).filter(word =>
        word.length > 1 && !stopWords.includes(word)
    );

    // 限制词数和长度
    slug = words.slice(0, 5).join('-');
    if (slug.length > 60) {
        slug = slug.substring(0, 60).replace(/-[^-]*$/, '');
    }

    slugInput.value = slug || 'tool-request';
    updateSlugCountRequests();
    validateSlugRequests(slugInput);
}

// 更新slug字符计数 (requests页面版本)
function updateSlugCountRequests() {
    const slugInput = document.getElementById('slug');
    const counter = document.getElementById('slug-count-requests');
    if (slugInput && counter) {
        counter.textContent = slugInput.value.length;
    }
}

// 验证slug (requests页面版本)
function validateSlugRequests(input) {
    const slug = input.value.trim();
    const statusSpan = document.getElementById('slug-status-requests');

    if (!slug) {
        statusSpan.innerHTML = '';
        return;
    }

    // 格式验证
    if (!/^[a-z0-9\-]+$/.test(slug)) {
        statusSpan.innerHTML = '<span class="text-red-400">❌ Only lowercase letters, numbers, and hyphens allowed</span>';
        return;
    }

    if (slug.length < 3) {
        statusSpan.innerHTML = '<span class="text-red-400">❌ Too short (min 3 characters)</span>';
        return;
    }

    if (slug.length > 60) {
        statusSpan.innerHTML = '<span class="text-red-400">❌ Too long (max 60 characters)</span>';
        return;
    }

    // 检查唯一性
    statusSpan.innerHTML = '<span class="text-blue-400">⏳ Checking availability...</span>';

    fetch('/ajax/check-slug.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ slug: slug })
    })
    .then(response => response.json())
    .then(data => {
        if (data.available) {
            statusSpan.innerHTML = '<span class="text-green-400">✅ Available</span>';
        } else {
            statusSpan.innerHTML = '<span class="text-red-400">❌ Already taken</span>';
        }
    })
    .catch(error => {
        statusSpan.innerHTML = '<span class="text-gray-400">⚠️ Unable to check</span>';
    });
}
</script>

<?php include_once ROOT_PATH . '/templates/layout/footer.php'; ?>
