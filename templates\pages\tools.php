<?php
/**
 * 工具列表页面
 */

$currentPage = 'tools';
$breadcrumbs = [
    ['name' => 'Home', 'url' => '/'],
    ['name' => 'Tools']
];

// 包含动态SEO助手
require_once ROOT_PATH . '/app/helpers/dynamic-seo.php';

// 获取动态SEO数据
$seoData = getDynamicSEOData('tools');

// 包含公共头部
include_once ROOT_PATH . '/templates/layout/header.php';
?>

<!-- 工具页面内容 -->
<section class="py-20 bg-black">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- 页面标题 -->
        <div class="text-center mb-16">
            <h1 class="text-4xl md:text-5xl font-bold mb-6">All Free Online Tools</h1>
            <p class="text-xl text-gray-400 max-w-3xl mx-auto">
                Discover our complete collection of professional-grade online tools. From development and design to productivity and security - find the perfect tool for your project needs.
            </p>
        </div>
        
        <!-- 搜索栏 -->
        <div class="max-w-2xl mx-auto mb-16">
            <div class="relative">
                <input type="text"
                       id="searchInput"
                       placeholder="Search tools..."
                       class="w-full px-6 py-4 bg-gray-900 border border-gray-700 text-white placeholder-gray-400 focus:border-accent focus:outline-none text-lg">
                <div class="absolute right-4 top-4">
                    <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                </div>
            </div>
        </div>
        
        <!-- 工具筛选和排序 -->
        <div class="flex flex-col md:flex-row justify-between items-center mb-12">
            <div class="flex flex-wrap gap-4 mb-4 md:mb-0">
                <select id="categoryFilter" class="bg-gray-900 border border-gray-700 text-white px-4 py-2 focus:border-accent focus:outline-none">
                    <option value="">All Categories</option>
                    <?php
                    // 获取所有分类用于筛选
                    $categories = getToolCategories();
                    foreach ($categories as $slug => $category): ?>
                    <option value="<?= htmlspecialchars($slug) ?>"><?= htmlspecialchars($category['name']) ?></option>
                    <?php endforeach; ?>
                </select>

                <select id="sortFilter" class="bg-gray-900 border border-gray-700 text-white px-4 py-2 focus:border-accent focus:outline-none">
                    <option value="name">Sort by Name</option>
                    <option value="popular">Most Popular</option>
                    <option value="newest">Newest First</option>
                    <option value="featured">Featured First</option>
                </select>
            </div>

            <div class="text-gray-400 text-sm">
                <span id="toolCount">Loading...</span> tools available
            </div>
        </div>

        <!-- 所有工具列表 -->
        <div class="mb-16">
            <div id="toolsGrid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"></div>

            <!-- 加载更多按钮 -->
            <div id="loadMoreContainer" class="text-center mt-12 hidden">
                <button id="loadMoreBtn" class="btn btn-primary btn-lg">
                    <span id="loadMoreText">Load More Tools</span>
                    <span id="loadMoreSpinner" class="hidden ml-2">
                        <svg class="animate-spin h-4 w-4 inline" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                    </span>
                </button>
            </div>

            <div id="noResults" class="text-center py-12 hidden">
                <div class="text-gray-400 text-lg">No tools found matching your criteria.</div>
            </div>

            <div id="loadingInitial" class="text-center py-12">
                <div class="text-gray-400 text-lg">Loading tools...</div>
            </div>
        </div>
    </div>
</section>

<script>
// 工具数据和分页功能
let allTools = [];
let currentPage = 1;
let totalPages = 1;
let hasMore = false;
let isLoading = false;
let currentFilters = {
    category: '',
    sort: 'name',
    search: ''
};

// 页面加载时获取工具数据
document.addEventListener('DOMContentLoaded', function() {
    loadTools(1, true);

    // 绑定筛选事件
    document.getElementById('categoryFilter').addEventListener('change', handleFilterChange);
    document.getElementById('sortFilter').addEventListener('change', handleFilterChange);
    document.getElementById('searchInput').addEventListener('input', debounce(handleFilterChange, 500));

    // 绑定加载更多按钮
    document.getElementById('loadMoreBtn').addEventListener('click', loadMoreTools);
});

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 从API加载工具数据
async function loadTools(page = 1, reset = false) {
    if (isLoading) return false;

    isLoading = true;
    showLoading(page === 1);

    try {
        // 构建API URL
        const params = new URLSearchParams({
            page: page,
            limit: 9,
            category: currentFilters.category,
            sort: currentFilters.sort,
            search: currentFilters.search
        });

        const response = await fetch(`/api/tools.php?${params}`);
        const data = await response.json();

        if (data.success) {
            if (reset) {
                allTools = data.tools;
            } else {
                allTools = [...allTools, ...data.tools];
            }

            currentPage = data.pagination.current_page;
            totalPages = data.pagination.total_pages;
            hasMore = data.pagination.has_more;

            renderTools(reset);
            updateToolCount(data.pagination.total);
            updateLoadMoreButton();

            return true;
        } else {
            console.error('Failed to load tools:', data.message);
            showError('Failed to load tools. Please try again.');
            return false;
        }
    } catch (error) {
        console.error('Error loading tools:', error);
        showError('Network error. Please check your connection.');
        return false;
    } finally {
        isLoading = false;
        hideLoading();
    }
}

// 处理筛选变化
function handleFilterChange() {
    // 更新当前筛选条件
    currentFilters.category = document.getElementById('categoryFilter').value;
    currentFilters.sort = document.getElementById('sortFilter').value;
    currentFilters.search = document.getElementById('searchInput').value;

    // 重新加载第一页
    loadTools(1, true);
}

// 加载更多工具
async function loadMoreTools() {
    if (hasMore && !isLoading) {
        await loadTools(currentPage + 1, false);
    }
}

// 显示加载状态
function showLoading(initial = false) {
    if (initial) {
        document.getElementById('loadingInitial').classList.remove('hidden');
        document.getElementById('toolsGrid').innerHTML = '';
    } else {
        document.getElementById('loadMoreText').textContent = 'Loading...';
        document.getElementById('loadMoreSpinner').classList.remove('hidden');
        document.getElementById('loadMoreBtn').disabled = true;
    }
}

// 隐藏加载状态
function hideLoading() {
    document.getElementById('loadingInitial').classList.add('hidden');
    document.getElementById('loadMoreText').textContent = 'Load More Tools';
    document.getElementById('loadMoreSpinner').classList.add('hidden');
    document.getElementById('loadMoreBtn').disabled = false;
}

// 显示错误信息
function showError(message) {
    const errorDiv = document.createElement('div');
    errorDiv.className = 'text-center py-12 text-red-400';
    errorDiv.textContent = message;
    document.getElementById('toolsGrid').appendChild(errorDiv);
}

// 更新加载更多按钮
function updateLoadMoreButton() {
    const container = document.getElementById('loadMoreContainer');
    if (hasMore) {
        container.classList.remove('hidden');
    } else {
        container.classList.add('hidden');
    }
}

// 渲染工具列表
function renderTools(reset = false) {
    const toolsGrid = document.getElementById('toolsGrid');
    const noResults = document.getElementById('noResults');

    if (allTools.length === 0) {
        if (reset) toolsGrid.innerHTML = '';
        noResults.classList.remove('hidden');
        return;
    }

    noResults.classList.add('hidden');

    const toolsHTML = allTools.map(tool => `
        <a href="${tool.url || '/tools/' + tool.category_slug + '/' + tool.slug}"
           class="bg-gray-900 border border-gray-800 p-6 hover:border-accent transition-all duration-300 group block"
           title="${tool.name} - ${tool.description || 'Online tool'}">

            <!-- 工具图标和标签 -->
            <div class="flex justify-between items-start mb-4">
                <div class="text-4xl group-hover:scale-110 transition-transform duration-300">
                    ${tool.icon || '🔧'}
                </div>

                ${tool.featured ? '<span class="bg-accent text-white text-xs px-2 py-1 font-medium">FEATURED</span>' : ''}
            </div>

            <!-- 工具名称 -->
            <h3 class="text-xl font-semibold mb-2 group-hover:text-accent transition-colors duration-300">
                ${tool.name}
            </h3>

            <!-- 工具描述 -->
            <p class="text-gray-400 text-sm mb-4 leading-relaxed">
                ${tool.description || 'No description available'}
            </p>

            <!-- 工具标签 -->
            ${tool.tags && tool.tags.length > 0 ? `
            <div class="flex flex-wrap gap-2 mb-4">
                ${tool.tags.slice(0, 3).map(tag => `
                    <span class="bg-gray-800 text-gray-300 text-xs px-2 py-1">${tag}</span>
                `).join('')}
            </div>
            ` : ''}

            <!-- 工具统计 -->
            <div class="flex items-center justify-between text-xs text-gray-500">
                <span>${tool.view_count || 0} views</span>
                ${tool.category_name ? `<span>${tool.category_name}</span>` : ''}
            </div>
        </a>
    `).join('');

    if (reset) {
        toolsGrid.innerHTML = toolsHTML;
    } else {
        toolsGrid.insertAdjacentHTML('beforeend', toolsHTML);
    }
}

// 更新工具数量显示
function updateToolCount(total = null) {
    const count = total !== null ? total : allTools.length;
    document.getElementById('toolCount').textContent = count;
}

// 添加结构化数据用于SEO
function addStructuredData() {
    const structuredData = {
        "@context": "https://schema.org",
        "@type": "CollectionPage",
        "name": "All Free Online Tools",
        "description": "Browse our complete collection of 100+ free professional online tools. Development, design, productivity, security, and utility tools for developers and creators.",
        "url": window.location.href,
        "mainEntity": {
            "@type": "ItemList",
            "numberOfItems": allTools.length,
            "itemListElement": allTools.map((tool, index) => ({
                "@type": "SoftwareApplication",
                "position": index + 1,
                "name": tool.name,
                "description": tool.description,
                "url": tool.url,
                "applicationCategory": tool.category_name,
                "operatingSystem": "Web Browser",
                "offers": {
                    "@type": "Offer",
                    "price": "0",
                    "priceCurrency": "USD"
                }
            }))
        }
    };

    const script = document.createElement('script');
    script.type = 'application/ld+json';
    script.textContent = JSON.stringify(structuredData);
    document.head.appendChild(script);
}

// 在工具加载完成后添加结构化数据
document.addEventListener('DOMContentLoaded', function() {
    loadTools().then(() => {
        addStructuredData();
    });
});
</script>

<?php include_once ROOT_PATH . '/templates/layout/footer.php'; ?>
