<?php
/**
 * 工具文件分析控制器
 */

class ToolAnalysisController {
    
    /**
     * 分析上传的工具文件
     */
    public function analyze() {
        try {
            // 检查文件上传
            if (!isset($_FILES['file']) || $_FILES['file']['error'] !== UPLOAD_ERR_OK) {
                return ApiResponse::error(['message' => 'No file uploaded or upload error'], 400);
            }

            $uploadedFile = $_FILES['file'];

            // 验证文件类型
            $allowedExtensions = ['php', 'html', 'htm'];
            $fileExtension = strtolower(pathinfo($uploadedFile['name'], PATHINFO_EXTENSION));

            if (!in_array($fileExtension, $allowedExtensions)) {
                return ApiResponse::error(['message' => 'Invalid file type. Only PHP, HTML, and HTM files are allowed.'], 400);
            }

            // 验证文件大小 (最大2MB)
            $maxFileSize = 2 * 1024 * 1024;
            if ($uploadedFile['size'] > $maxFileSize) {
                return ApiResponse::error(['message' => 'File too large. Maximum size is 2MB.'], 400);
            }

            // 读取文件内容
            $fileContent = file_get_contents($uploadedFile['tmp_name']);
            if ($fileContent === false) {
                return ApiResponse::error(['message' => 'Failed to read file content'], 500);
            }

            // 提取基础信息
            $basicInfo = $this->extractBasicInfo($fileContent, $uploadedFile['name']);
            
            // 尝试AI分析
            $result = $this->performAnalysis($fileContent, $basicInfo);
            
            return ApiResponse::success($result);
            
        } catch (Exception $e) {
            error_log('Tool analysis error: ' . $e->getMessage());
            return ApiResponse::error(['message' => 'Analysis failed: ' . $e->getMessage()], 500);
        }
    }
    
    /**
     * 提取基础信息
     */
    private function extractBasicInfo($content, $filename) {
        $info = [
            'original_name' => $filename,
            'size' => strlen($content),
            'hash' => md5($content),
            'detected_type' => 'unknown'
        ];
        
        // 检测文件类型
        if (strpos($content, '<?php') !== false) {
            $info['detected_type'] = 'php';
        } elseif (strpos($content, '<!DOCTYPE html') !== false || strpos($content, '<html') !== false) {
            $info['detected_type'] = 'html';
        }
        
        // 提取标题
        if (preg_match('/<title[^>]*>(.*?)<\/title>/is', $content, $matches)) {
            $info['title'] = trim(strip_tags($matches[1]));
        } elseif (preg_match('/<h1[^>]*>(.*?)<\/h1>/is', $content, $matches)) {
            $info['title'] = trim(strip_tags($matches[1]));
        }
        
        // 提取描述
        if (preg_match('/<meta[^>]*name=["\']description["\'][^>]*content=["\']([^"\']*)["\'][^>]*>/i', $content, $matches)) {
            $info['description'] = trim($matches[1]);
        }
        
        // 提取关键词
        if (preg_match('/<meta[^>]*name=["\']keywords["\'][^>]*content=["\']([^"\']*)["\'][^>]*>/i', $content, $matches)) {
            $info['keywords'] = trim($matches[1]);
        }
        
        return $info;
    }
    
    /**
     * 执行分析
     */
    private function performAnalysis($fileContent, $basicInfo) {
        try {
            // 确保必要的常量已定义
            if (!defined('APP_INITIALIZED')) {
                define('APP_INITIALIZED', true);
            }

            // 加载AI服务
            require_once ROOT_PATH . '/classes/AIService.php';
            $aiService = new AIService();
            
            // 尝试AI分析
            if ($aiService->isAvailable()) {
                try {
                    $aiAnalysis = $aiService->analyzeToolFile($fileContent, $basicInfo);
                    
                    return array_merge($aiAnalysis, [
                        'file_info' => $basicInfo,
                        'analysis_timestamp' => date('Y-m-d H:i:s'),
                        'ai_service_available' => true,
                        'method' => 'ai_analysis'
                    ]);
                    
                } catch (Exception $aiError) {
                    error_log('AI Analysis failed: ' . $aiError->getMessage());
                    // 降级到备用分析
                }
            }
            
            // 备用分析
            $fallbackAnalysis = $this->generateFallbackAnalysis($basicInfo);
            return array_merge($fallbackAnalysis, [
                'file_info' => $basicInfo,
                'analysis_timestamp' => date('Y-m-d H:i:s'),
                'ai_service_available' => false,
                'method' => 'fallback_analysis'
            ]);
            
        } catch (Exception $e) {
            throw new Exception('Analysis failed: ' . $e->getMessage());
        }
    }
    
    /**
     * 生成备用分析结果
     */
    private function generateFallbackAnalysis($basicInfo) {
        $name = $basicInfo['title'] ?? 'Uploaded Tool';
        $slug = strtolower(preg_replace('/[^a-zA-Z0-9\s]/', '', $name));
        $slug = preg_replace('/\s+/', '-', trim($slug));
        $slug = $slug ?: 'uploaded-tool';
        
        $tags = [];
        if (isset($basicInfo['keywords'])) {
            $tags = array_map('trim', explode(',', $basicInfo['keywords']));
        }
        if (empty($tags)) {
            $tags = ['tool', 'utility'];
        }
        
        // 根据内容推测图标
        $icon = '🔧'; // 默认图标
        $nameAndDesc = strtolower(($basicInfo['title'] ?? '') . ' ' . ($basicInfo['description'] ?? ''));
        
        if (strpos($nameAndDesc, 'color') !== false) $icon = '🎨';
        elseif (strpos($nameAndDesc, 'password') !== false) $icon = '🔐';
        elseif (strpos($nameAndDesc, 'qr') !== false || strpos($nameAndDesc, 'code') !== false) $icon = '📱';
        elseif (strpos($nameAndDesc, 'calculator') !== false) $icon = '🧮';
        elseif (strpos($nameAndDesc, 'text') !== false || strpos($nameAndDesc, 'editor') !== false) $icon = '📝';
        elseif (strpos($nameAndDesc, 'image') !== false || strpos($nameAndDesc, 'photo') !== false) $icon = '🖼️';
        
        // 推测分类
        $category = 'utility';
        if (strpos($nameAndDesc, 'design') !== false || strpos($nameAndDesc, 'color') !== false) $category = 'design';
        elseif (strpos($nameAndDesc, 'security') !== false || strpos($nameAndDesc, 'password') !== false) $category = 'security';
        elseif (strpos($nameAndDesc, 'developer') !== false || strpos($nameAndDesc, 'code') !== false) $category = 'development';
        elseif (strpos($nameAndDesc, 'text') !== false || strpos($nameAndDesc, 'editor') !== false) $category = 'text';
        
        return [
            'name' => $name,
            'slug' => $slug,
            'description' => $basicInfo['description'] ?? 'A useful tool uploaded for analysis',
            'tags' => $tags,
            'icon' => $icon,
            'category' => $category,
            'difficulty' => 'intermediate',
            'file_type' => $basicInfo['detected_type'],
            'confidence' => 0.7,
            'fallback' => true
        ];
    }
}
?>
