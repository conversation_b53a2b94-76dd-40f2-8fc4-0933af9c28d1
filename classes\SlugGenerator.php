<?php
/**
 * Slug生成器类
 * 用于生成SEO友好的URL标识符
 */

class SlugGenerator {
    
    /**
     * 停用词列表（需要移除的词汇）
     */
    private static $stopWords = [
        // 动词
        'add', 'create', 'build', 'make', 'develop', 'improve', 'enhance', 'update', 'upgrade',
        'generate', 'design', 'implement', 'integrate', 'optimize', 'customize', 'configure',
        
        // 介词和连词
        'for', 'with', 'to', 'in', 'on', 'at', 'by', 'from', 'of', 'and', 'or', 'but',
        'that', 'which', 'where', 'when', 'how', 'why',
        
        // 冠词
        'the', 'a', 'an',
        
        // 其他常见词
        'tool', 'feature', 'functionality', 'system', 'application', 'app', 'online',
        'web', 'website', 'platform', 'service', 'solution', 'software'
    ];
    
    /**
     * 从标题生成slug
     * 
     * @param string $title 需求标题
     * @return string 生成的slug
     */
    public static function generateFromTitle($title) {
        // 转换为小写
        $slug = strtolower($title);
        
        // 移除特殊字符，只保留字母、数字、空格和连字符
        $slug = preg_replace('/[^a-z0-9\s\-]/', '', $slug);
        
        // 分割成单词
        $words = preg_split('/\s+/', $slug);
        
        // 移除停用词
        $filteredWords = array_filter($words, function($word) {
            return !in_array(trim($word), self::$stopWords) && strlen(trim($word)) > 1;
        });
        
        // 保留重要的技术词汇和工具名称
        $importantWords = self::extractImportantWords($filteredWords);

        // 如果过滤后没有词汇，使用原始词汇（移除停用词但保留其他）
        if (empty($importantWords)) {
            $importantWords = array_filter($words, function($word) {
                return !in_array(trim($word), self::$stopWords) && strlen(trim($word)) > 2;
            });
        }

        // 如果还是没有词汇，至少保留一些有意义的词
        if (empty($importantWords)) {
            $importantWords = array_filter($words, function($word) {
                return strlen(trim($word)) > 3; // 保留长度大于3的词
            });
        }
        
        // 限制词汇数量（最多5个词）
        $importantWords = array_slice($importantWords, 0, 5);
        
        // 用连字符连接
        $slug = implode('-', $importantWords);
        
        // 清理多余的连字符
        $slug = preg_replace('/-+/', '-', $slug);
        $slug = trim($slug, '-');
        
        // 限制长度（保持在60字符以内）
        if (strlen($slug) > 60) {
            $slug = substr($slug, 0, 60);
            $slug = rtrim($slug, '-');
        }
        
        return $slug ?: 'tool-request';
    }
    
    /**
     * 提取重要词汇（技术术语、工具名称等）
     * 
     * @param array $words 词汇数组
     * @return array 重要词汇数组
     */
    private static function extractImportantWords($words) {
        $importantPatterns = [
            // 技术词汇
            'api', 'json', 'xml', 'html', 'css', 'js', 'javascript', 'php', 'python', 'sql',
            'pdf', 'csv', 'excel', 'word', 'markdown', 'yaml',

            // 工具类型
            'generator', 'converter', 'formatter', 'validator', 'editor', 'builder', 'analyzer',
            'scanner', 'parser', 'compiler', 'optimizer', 'compressor', 'minifier',

            // 功能词汇
            'search', 'filter', 'sort', 'export', 'import', 'upload', 'download',
            'preview', 'template', 'theme', 'plugin', 'widget', 'dashboard',

            // 设计词汇
            'logo', 'icon', 'favicon', 'color', 'palette', 'font', 'typography',
            'layout', 'responsive', 'mobile', 'desktop',

            // 安全词汇
            'password', 'encryption', 'hash', 'security', 'auth', 'login', 'oauth',

            // 数据词汇
            'database', 'backup', 'sync', 'migration', 'import', 'export',

            // AI词汇
            'ai', 'artificial', 'intelligence', 'machine', 'learning', 'neural', 'chatbot',

            // 产品和业务词汇
            'product', 'requirements', 'prompt', 'brief', 'conversion', 'idea',
            'specification', 'workflow', 'process', 'automation', 'management',
            'planning', 'strategy', 'analysis', 'report', 'metrics', 'tracking',

            // 其他重要词汇
            'qr', 'code', 'barcode', 'meta', 'seo', 'analytics', 'tracking',
            'notification', 'email', 'sms', 'webhook', 'integration'
        ];
        
        $result = [];
        foreach ($words as $word) {
            $word = trim($word);
            if (strlen($word) > 1) {
                // 检查是否是重要词汇
                if (in_array($word, $importantPatterns) || 
                    strlen($word) > 4 || 
                    preg_match('/^[a-z]+[0-9]+$/', $word)) { // 如css3, html5等
                    $result[] = $word;
                }
            }
        }
        
        return $result;
    }
    
    /**
     * 检查slug是否唯一
     * 
     * @param string $slug 要检查的slug
     * @param PDO $pdo 数据库连接
     * @param int|null $excludeId 排除的ID（用于编辑时）
     * @return bool 是否唯一
     */
    public static function isUnique($slug, $pdo, $excludeId = null) {
        $sql = "SELECT COUNT(*) FROM pt_user_requests WHERE slug = ?";
        $params = [$slug];
        
        if ($excludeId) {
            $sql .= " AND id != ?";
            $params[] = $excludeId;
        }
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        
        return $stmt->fetchColumn() == 0;
    }
    
    /**
     * 生成唯一的slug
     * 
     * @param string $title 标题
     * @param PDO $pdo 数据库连接
     * @param int|null $excludeId 排除的ID
     * @return string 唯一的slug
     */
    public static function generateUnique($title, $pdo, $excludeId = null) {
        $baseSlug = self::generateFromTitle($title);
        $slug = $baseSlug;
        $counter = 2;
        
        while (!self::isUnique($slug, $pdo, $excludeId)) {
            $slug = $baseSlug . '-' . $counter;
            $counter++;
        }
        
        return $slug;
    }
    
    /**
     * 批量为现有需求生成slug
     * 
     * @param PDO $pdo 数据库连接
     * @return array 处理结果
     */
    public static function generateForExistingRequests($pdo) {
        $results = ['success' => 0, 'failed' => 0, 'errors' => []];
        
        try {
            // 获取所有需要生成slug的需求（slug为空或以request-开头的临时slug）
            $stmt = $pdo->query("SELECT id, title, slug FROM pt_user_requests WHERE slug LIKE 'request-%' ORDER BY id");
            $requests = $stmt->fetchAll();
            
            foreach ($requests as $request) {
                try {
                    $newSlug = self::generateUnique($request['title'], $pdo, $request['id']);
                    
                    $updateStmt = $pdo->prepare("UPDATE pt_user_requests SET slug = ? WHERE id = ?");
                    $updateStmt->execute([$newSlug, $request['id']]);
                    
                    $results['success']++;
                } catch (Exception $e) {
                    $results['failed']++;
                    $results['errors'][] = "ID {$request['id']}: " . $e->getMessage();
                }
            }
        } catch (Exception $e) {
            $results['errors'][] = "General error: " . $e->getMessage();
        }
        
        return $results;
    }
}
