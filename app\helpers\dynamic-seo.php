<?php
/**
 * Dynamic SEO Helper Functions
 * Replaces hardcoded SEO values with database settings
 */

// Include common functions if not already loaded
if (!function_exists('getCurrentURL')) {
    require_once __DIR__ . '/functions.php';
}

/**
 * Get setting value from database
 */
function getSetting($key, $default = '') {
    static $settings = null;
    
    if ($settings === null) {
        $settings = [];
        try {
            // Include database connection if not already included
            if (!isset($GLOBALS['pdo'])) {
                // 使用统一的数据库连接
                require_once dirname(dirname(__DIR__)) . '/includes/database-connection.php';
                $GLOBALS['pdo'] = $pdo;
            }
            
            $sql = "SELECT setting_key, setting_value FROM pt_system_config WHERE is_public = 1";
            $stmt = $GLOBALS['pdo']->query($sql);
            while ($row = $stmt->fetch()) {
                $settings[$row['setting_key']] = $row['setting_value'];
            }
        } catch (Exception $e) {
            error_log("Settings fetch error: " . $e->getMessage());
        }
    }
    
    return $settings[$key] ?? $default;
}

/**
 * Get dynamic SEO data for pages
 */
function getDynamicSEOData($page = 'home', $customData = []) {
    // Base settings from database
    $baseTitle = getSetting('site_title', 'Free AI-Powered Online Tools Platform - Prompt2Tool');
    $baseDescription = getSetting('site_description', 'Discover 100+ free online tools powered by AI. HTML formatter, CSS minifier, image converter, SEO analyzer, and more. Boost your productivity with Prompt2Tool.');

    // Page-specific SEO data
    $seoData = [];

    switch ($page) {
        case 'home':
            $seoData = [
                'title' => 'Free AI-Powered Online Tools Platform - Prompt2Tool',
                'description' => 'Discover 100+ free online tools powered by AI. HTML formatter, CSS minifier, image converter, SEO analyzer, and more. Boost your productivity.',
                'og_title' => 'Free AI-Powered Online Tools - Prompt2Tool',
                'og_description' => 'Access 100+ free online tools powered by AI. Perfect for developers, designers, and digital marketers.',
                'og_image' => getSetting('og_image', '/assets/images/og-default.jpg'),
                'twitter_card' => getSetting('twitter_card', 'summary_large_image'),
                'twitter_site' => getSetting('twitter_handle', '@Prompt2Tool'),
                'canonical' => getCurrentURL()
            ];
            break;
            
        case 'tools':
            $seoData = [
                'title' => 'Free Online Tools - Prompt2Tool',
                'description' => 'Browse 100+ free professional online tools for development, design, productivity, security, and more. All tools work in your browser.',
                'og_title' => 'Free Online Tools - Prompt2Tool',
                'og_description' => 'Browse 100+ free professional online tools for development, design, productivity, security, and more. All tools work in your browser.',
                'og_image' => getSetting('og_image', '/assets/images/og-tools.jpg'),
                'twitter_card' => getSetting('twitter_card', 'summary_large_image'),
                'twitter_site' => getSetting('twitter_handle', '@Prompt2Tool'),
                'canonical' => getCurrentURL()
            ];
            break;

        case 'categories':
            $seoData = [
                'title' => 'Tool Categories - Prompt2Tool',
                'description' => 'Browse all tool categories including development, design, productivity, security, and utility tools. Find the perfect category for your needs.',
                'og_title' => 'Tool Categories - Prompt2Tool',
                'og_description' => 'Browse all tool categories including development, design, productivity, security, and utility tools. Find the perfect category for your needs.',
                'og_image' => getSetting('og_image', '/assets/images/og-categories.jpg'),
                'twitter_card' => getSetting('twitter_card', 'summary_large_image'),
                'twitter_site' => getSetting('twitter_handle', '@Prompt2Tool'),
                'canonical' => getCurrentURL()
            ];
            break;

        case 'requests':
            $seoData = [
                'title' => 'Tool Requests - Submit Ideas & Vote - Prompt2Tool',
                'description' => 'Submit feature requests for new AI tools, vote on community suggestions, and help shape the future of Prompt2Tool platform. Your ideas matter!',
                'og_title' => 'Tool Requests - Submit Ideas & Vote - Prompt2Tool',
                'og_description' => 'Submit feature requests for new AI tools, vote on community suggestions, and help shape the future of Prompt2Tool platform.',
                'og_image' => getSetting('og_image', '/assets/images/og-requests.jpg'),
                'twitter_card' => getSetting('twitter_card', 'summary_large_image'),
                'twitter_site' => getSetting('twitter_handle', '@Prompt2Tool'),
                'canonical' => getCurrentURL()
            ];
            break;

        case 'about':
            $seoData = [
                'title' => 'About Our AI-Powered Tools Mission - Prompt2Tool',
                'description' => 'Learn about Prompt2Tool\'s mission to provide free, professional-grade online tools powered by AI for developers, designers, and creators worldwide.',
                'og_title' => 'About Our AI-Powered Tools Mission - Prompt2Tool',
                'og_description' => 'Learn about Prompt2Tool\'s mission to provide free, professional-grade online tools powered by AI for creators worldwide.',
                'og_image' => getSetting('og_image', '/assets/images/og-about.jpg'),
                'twitter_card' => getSetting('twitter_card', 'summary'),
                'twitter_site' => getSetting('twitter_handle', '@Prompt2Tool'),
                'canonical' => getCurrentURL()
            ];
            break;

        case 'pricing':
            $seoData = [
                'title' => 'Pricing Plans & API Credits - Prompt2Tool',
                'description' => 'Choose the perfect plan for your needs. Free tier with 1,000 API credits, Basic, Premium, and Enterprise plans. Simple, transparent pricing for AI tools.',
                'og_title' => 'Pricing Plans & API Credits - Prompt2Tool',
                'og_description' => 'Simple, transparent pricing for AI-powered tools. Free tier available with 1,000 API credits. Choose from Basic, Premium, or Enterprise plans.',
                'og_image' => getSetting('og_image', '/assets/images/og-pricing.jpg'),
                'twitter_card' => getSetting('twitter_card', 'summary'),
                'twitter_site' => getSetting('twitter_handle', '@Prompt2Tool'),
                'canonical' => getCurrentURL()
            ];
            break;

        case 'contact':
            $seoData = [
                'title' => 'Contact Us for Support & Feedback - Prompt2Tool',
                'description' => 'Get in touch with Prompt2Tool team for support, feedback, or suggestions. Multiple contact options available with quick response times.',
                'og_title' => 'Contact Us for Support & Feedback - Prompt2Tool',
                'og_description' => 'Get in touch with Prompt2Tool team for support, feedback, or suggestions. Multiple contact options with quick response.',
                'og_image' => getSetting('og_image', '/assets/images/og-contact.jpg'),
                'twitter_card' => getSetting('twitter_card', 'summary'),
                'twitter_site' => getSetting('twitter_handle', '@Prompt2Tool'),
                'canonical' => getCurrentURL()
            ];
            break;

        case 'help':
            $seoData = [
                'title' => 'Help Center & Support Guide - Prompt2Tool',
                'description' => 'Get help with Prompt2Tool\'s free online tools. Find answers to common questions, tutorials, and support for all our tools and features.',
                'og_title' => 'Help Center & Support Guide - Prompt2Tool',
                'og_description' => 'Get help with Prompt2Tool\'s free online tools. Find answers to common questions and comprehensive support.',
                'og_image' => getSetting('og_image', '/assets/images/og-help.jpg'),
                'twitter_card' => getSetting('twitter_card', 'summary'),
                'twitter_site' => getSetting('twitter_handle', '@Prompt2Tool'),
                'canonical' => getCurrentURL()
            ];
            break;

        case 'privacy':
            $seoData = [
                'title' => 'Privacy Policy & Data Protection - Prompt2Tool',
                'description' => 'Read Prompt2Tool\'s privacy policy to understand how we collect, use, and protect your personal information when using our free online tools.',
                'og_title' => 'Privacy Policy & Data Protection - Prompt2Tool',
                'og_description' => 'Learn about Prompt2Tool\'s commitment to protecting your privacy and personal data when using our tools.',
                'og_image' => getSetting('og_image', '/assets/images/og-privacy.jpg'),
                'twitter_card' => getSetting('twitter_card', 'summary'),
                'twitter_site' => getSetting('twitter_handle', '@Prompt2Tool'),
                'canonical' => getCurrentURL()
            ];
            break;

        case 'terms':
            $seoData = [
                'title' => 'Terms of Service & Usage Rules - Prompt2Tool',
                'description' => 'Read Prompt2Tool\'s terms of service to understand the rules and guidelines for using our free online tools and services.',
                'og_title' => 'Terms of Service & Usage Rules - Prompt2Tool',
                'og_description' => 'Learn about the terms and conditions for using Prompt2Tool\'s free online tools and services.',
                'og_image' => getSetting('og_image', '/assets/images/og-terms.jpg'),
                'twitter_card' => getSetting('twitter_card', 'summary'),
                'twitter_site' => getSetting('twitter_handle', '@Prompt2Tool'),
                'canonical' => getCurrentURL()
            ];
            break;

        case 'category':
        case 'tool':
        case '404':
            // These pages will provide custom data via $customData parameter
            $seoData = [
                'title' => $baseTitle,
                'description' => $baseDescription,
                'og_title' => getSetting('og_title', 'Free Online Tools - Prompt2Tool'),
                'og_description' => getSetting('og_description', 'Free online tools powered by AI.'),
                'og_image' => getSetting('og_image', '/assets/images/og-default.jpg'),
                'twitter_card' => getSetting('twitter_card', 'summary_large_image'),
                'twitter_site' => getSetting('twitter_handle', '@Prompt2Tool'),
                'canonical' => getCurrentURL()
            ];
            break;

        default:
            $seoData = [
                'title' => $baseTitle,
                'description' => $baseDescription,
                'og_title' => getSetting('og_title', 'Free Online Tools - Prompt2Tool'),
                'og_description' => getSetting('og_description', 'Free online tools powered by AI.'),
                'og_image' => getSetting('og_image', '/assets/images/og-default.jpg'),
                'twitter_card' => getSetting('twitter_card', 'summary_large_image'),
                'twitter_site' => getSetting('twitter_handle', '@Prompt2Tool'),
                'canonical' => getCurrentURL()
            ];
            break;
    }
    
    // Merge with custom data
    return array_merge($seoData, $customData);
}

/**
 * Get social media links
 */
function getSocialLinks() {
    return [
        'github' => getSetting('github_url', 'https://github.com/prompt2tool'),
        'twitter' => getSetting('twitter_url', 'https://twitter.com/prompt2tool'),
        'linkedin' => getSetting('linkedin_url', 'https://linkedin.com/company/prompt2tool')
    ];
}

/**
 * Get company information
 */
function getCompanyInfo() {
    $copyrightText = getSetting('copyright_text', 'Prompt2Tool. All rights reserved.');

    return [
        'description' => getSetting('company_description', 'AI-powered free online tools platform providing development, design, SEO, and various practical tools to boost your productivity.'),
        'copyright' => '© ' . date('Y') . ' ' . $copyrightText,
        'copyright_text' => $copyrightText, // Raw text without year and symbol
        'email' => getSetting('contact_email', '<EMAIL>')
    ];
}

/**
 * Get tracking codes
 */
function getTrackingCodes() {
    return [
        'google_analytics' => getSetting('google_analytics_id', ''),
        'google_adsense' => getSetting('google_adsense_id', '')
    ];
}

/**
 * Generate meta tags HTML
 */
function generateMetaTags($page = 'home', $customData = []) {
    $seo = getDynamicSEOData($page, $customData);

    $html = '';
    $html .= '<title>' . htmlspecialchars($seo['title']) . '</title>' . "\n";
    $html .= '<meta name="description" content="' . htmlspecialchars($seo['description']) . '">' . "\n";
    $html .= '<meta name="robots" content="index,follow">' . "\n";
    $html .= '<link rel="canonical" href="' . htmlspecialchars($seo['canonical']) . '">' . "\n";
    
    // Open Graph tags
    $html .= '<meta property="og:title" content="' . htmlspecialchars($seo['og_title']) . '">' . "\n";
    $html .= '<meta property="og:description" content="' . htmlspecialchars($seo['og_description']) . '">' . "\n";
    $html .= '<meta property="og:type" content="website">' . "\n";
    $html .= '<meta property="og:url" content="' . htmlspecialchars($seo['canonical']) . '">' . "\n";
    $html .= '<meta property="og:image" content="' . getFullURL($seo['og_image']) . '">' . "\n";
    $html .= '<meta property="og:site_name" content="Prompt2Tool">' . "\n";
    
    // Twitter Cards
    $html .= '<meta name="twitter:card" content="' . htmlspecialchars($seo['twitter_card']) . '">' . "\n";
    $html .= '<meta name="twitter:title" content="' . htmlspecialchars($seo['og_title']) . '">' . "\n";
    $html .= '<meta name="twitter:description" content="' . htmlspecialchars($seo['og_description']) . '">' . "\n";
    $html .= '<meta name="twitter:image" content="' . getFullURL($seo['og_image']) . '">' . "\n";
    $html .= '<meta name="twitter:site" content="' . htmlspecialchars($seo['twitter_site']) . '">' . "\n";
    
    return $html;
}



/**
 * Get formatted copyright text with current year
 */
function getCopyright() {
    $copyrightText = getSetting('copyright_text', 'Prompt2Tool. All rights reserved.');
    return '© ' . date('Y') . ' ' . $copyrightText;
}

/**
 * Get copyright text without year (for editing)
 */
function getCopyrightText() {
    return getSetting('copyright_text', 'Prompt2Tool. All rights reserved.');
}

/**
 * Get category-specific SEO content
 */
function getCategorySEOContent($categorySlug, $categoryName, $categoryDescription) {
    $seoContent = [
        'design' => [
            'title' => 'Free Design & Media Tools for Creatives - Prompt2Tool',
            'description' => 'Discover professional design and media tools for free. Image converters, editors, color tools, and creative utilities for designers and content creators.',
            'og_title' => 'Free Design & Media Tools for Creatives - Prompt2Tool',
            'og_description' => 'Professional design and media tools for free. Image converters, editors, and creative utilities.'
        ],
        'productivity' => [
            'title' => 'Free Productivity Tools & Text Utilities - Prompt2Tool',
            'description' => 'Boost your productivity with free online tools. Text processors, calculators, converters, and workflow enhancers for professionals and students.',
            'og_title' => 'Free Productivity Tools & Text Utilities - Prompt2Tool',
            'og_description' => 'Boost your productivity with free online tools. Text processors, calculators, and workflow enhancers.'
        ],
        'marketing' => [
            'title' => 'Free Marketing & SEO Tools for Digital Growth - Prompt2Tool',
            'description' => 'Accelerate your digital marketing with free SEO tools, analytics utilities, and marketing automation tools. Perfect for marketers and businesses.',
            'og_title' => 'Free Marketing & SEO Tools for Digital Growth - Prompt2Tool',
            'og_description' => 'Accelerate your digital marketing with free SEO tools, analytics utilities, and marketing automation.'
        ],
        'utilities' => [
            'title' => 'Free General Utility Tools & Converters - Prompt2Tool',
            'description' => 'Essential free utility tools and converters for everyday tasks. File converters, generators, validators, and general-purpose productivity tools.',
            'og_title' => 'Free General Utility Tools & Converters - Prompt2Tool',
            'og_description' => 'Essential free utility tools and converters for everyday tasks. File converters and generators.'
        ],
        'security' => [
            'title' => 'Free Security & Privacy Tools Online - Prompt2Tool',
            'description' => 'Protect your digital life with free security tools. Password generators, encryption utilities, privacy tools, and cybersecurity resources.',
            'og_title' => 'Free Security & Privacy Tools Online - Prompt2Tool',
            'og_description' => 'Protect your digital life with free security tools. Password generators and privacy utilities.'
        ]
    ];

    // Return specific content for the category, or generate generic content
    if (isset($seoContent[$categorySlug])) {
        return $seoContent[$categorySlug];
    }

    // Fallback for other categories
    return [
        'title' => 'Free ' . $categoryName . ' Tools & Utilities - Prompt2Tool',
        'description' => 'Discover essential free ' . strtolower($categoryName) . ' tools and utilities. ' . $categoryDescription . ' Perfect for professionals.',
        'og_title' => 'Free ' . $categoryName . ' Tools & Utilities - Prompt2Tool',
        'og_description' => 'Essential free ' . strtolower($categoryName) . ' tools and utilities for professionals.'
    ];
}

// Helper functions getCurrentURL() and getFullURL() are defined in app/helpers/functions.php
?>
