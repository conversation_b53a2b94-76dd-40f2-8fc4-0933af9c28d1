<?php
/**
 * AI建议API - 使用Qwen3-8B模型生成各种建议
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// 检查登录状态
session_start();
if (!isset($_SESSION['admin_logged_in']) || !$_SESSION['admin_logged_in']) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit;
}

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit;
}

// 获取请求数据
$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['type'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'Missing required parameters']);
    exit;
}

$type = $input['type'];
$data = $input['data'] ?? [];

// System Prompts for different suggestion types
$systemPrompts = [
    'slug' => 'You are a SEO expert. Generate exactly 3 short URL slugs using lowercase letters and hyphens. Each slug should be 2-4 words maximum, focus on main keywords, and be SEO-friendly. Return ONLY a JSON array like: ["html-formatter", "code-beautifier", "web-tool"]',

    'category' => 'You are a tool categorization expert. Analyze the tool and recommend the EXACT category name from the provided list. Return ONLY a JSON object like: {"category": "Development", "confidence": 0.95, "reason": "This is a coding tool"}',

    'description' => 'You are a technical copywriter. Create exactly 3 different tool descriptions. Return ONLY a JSON object like: {"concise": "One sentence description", "detailed": "Two to three sentence detailed description", "marketing": "Engaging benefit-focused description"}',

    'tags' => 'You are a content tagging expert. Generate exactly 5 relevant, searchable tags for this tool. Focus on functionality, technology, and user search terms. Return ONLY a JSON array like: ["html", "formatter", "code", "beautifier", "web"]',

    'name' => 'You are a product naming expert. Suggest exactly 3 improved tool names that are clear, professional, and memorable. Each name should be 1-4 words with spaces between words and clearly indicate the tool\'s purpose. Use proper spacing like "HTML Code Cleaner" not "HTMLCodeCleaner". Return ONLY a JSON array like: ["HTML Formatter", "Code Beautifier", "Web Tool"]',

    'icon' => 'You are a UI/UX expert. Recommend exactly 3 appropriate emoji icons that visually represent this tool\'s function. Choose universally recognizable emojis. Return ONLY a JSON array like: ["🔧", "⚡", "🎯"]'
];

if (!isset($systemPrompts[$type])) {
    echo json_encode(['success' => false, 'error' => 'Invalid suggestion type']);
    exit;
}

// 使用统一的数据库连接
require_once dirname(__DIR__) . '/includes/database-connection.php';

try {
    
    // 智能选择API密钥（负载均衡）
    $apiKeyConfig = selectOptimalAPIKey($pdo);

    if (!$apiKeyConfig) {
        throw new Exception('AI service temporarily unavailable');
    }

    // 构建用户提示词
    $userPrompt = buildUserPrompt($type, $data);

    // 调用AI模型（支持重试机制）
    $response = callGLMModelWithRetry($pdo, $systemPrompts[$type], $userPrompt);
    
    // 解析响应
    $suggestions = parseResponse($type, $response);
    
    echo json_encode([
        'success' => true,
        'type' => $type,
        'suggestions' => $suggestions,
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Failed to generate suggestions. Please try again later.'
    ]);
}

/**
 * 构建用户提示词
 */
function buildUserPrompt($type, $data) {
    switch ($type) {
        case 'slug':
            $toolName = $data['tool_name'] ?? '';
            $existingSlugs = $data['existing_slugs'] ?? [];
            return "Tool name: \"{$toolName}\"\nExisting slugs to avoid: " . implode(', ', $existingSlugs) . "\n\nGenerate 3 SEO-friendly slug options in JSON format: [\"slug1\", \"slug2\", \"slug3\"]";
            
        case 'category':
            $toolName = $data['tool_name'] ?? '';
            $description = $data['description'] ?? '';
            $categories = $data['categories'] ?? [];
            $categoryList = implode(', ', array_column($categories, 'name'));
            return "Tool name: \"{$toolName}\"\nDescription: \"{$description}\"\nAvailable categories: {$categoryList}\n\nRecommend the best category in JSON format: {\"category\": \"category_name\", \"confidence\": 0.95, \"reason\": \"explanation\"}";
            
        case 'description':
            $toolName = $data['tool_name'] ?? '';
            $category = $data['category'] ?? '';
            $features = $data['features'] ?? '';
            return "Tool name: \"{$toolName}\"\nCategory: \"{$category}\"\nKey features: \"{$features}\"\n\nGenerate 3 description styles in JSON format: {\"concise\": \"1 sentence\", \"detailed\": \"2-3 sentences\", \"marketing\": \"engaging description\"}";
            
        case 'tags':
            $toolName = $data['tool_name'] ?? '';
            $category = $data['category'] ?? '';
            $description = $data['description'] ?? '';
            return "Tool name: \"{$toolName}\"\nCategory: \"{$category}\"\nDescription: \"{$description}\"\n\nGenerate relevant tags in JSON format: [\"tag1\", \"tag2\", \"tag3\", \"tag4\", \"tag5\"]";
            
        case 'name':
            $currentName = $data['current_name'] ?? '';
            $description = $data['description'] ?? '';
            $category = $data['category'] ?? '';
            return "Current name: \"{$currentName}\"\nDescription: \"{$description}\"\nCategory: \"{$category}\"\n\nSuggest 3 improved names in JSON format: [\"name1\", \"name2\", \"name3\"]";
            
        case 'icon':
            $toolName = $data['tool_name'] ?? '';
            $category = $data['category'] ?? '';
            $description = $data['description'] ?? '';
            return "Tool name: \"{$toolName}\"\nCategory: \"{$category}\"\nDescription: \"{$description}\"\n\nRecommend 3 appropriate emoji icons in JSON format: [\"🔧\", \"⚡\", \"🎯\"]";
            
        default:
            return '';
    }
}

/**
 * 调用AI模型
 */
function callGLMModel($apiKey, $systemPrompt, $userPrompt) {
    $url = 'https://open.bigmodel.cn/api/paas/v4/chat/completions';

    $payload = [
        'model' => 'glm-4-flash',
        'messages' => [
            [
                'role' => 'system',
                'content' => $systemPrompt
            ],
            [
                'role' => 'user',
                'content' => $userPrompt
            ]
        ],
        'max_tokens' => 1024,
        'temperature' => 0.3,
        'top_p' => 0.9,
        'stream' => false
    ];
    
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => json_encode($payload),
        CURLOPT_HTTPHEADER => [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $apiKey
        ],
        CURLOPT_TIMEOUT => 30,
        CURLOPT_SSL_VERIFYPEER => false
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        throw new Exception("Network error occurred");
    }

    if ($httpCode !== 200) {
        throw new Exception("Service temporarily unavailable");
    }

    $data = json_decode($response, true);

    if (!$data || !isset($data['choices'][0]['message']['content'])) {
        throw new Exception("Invalid response received");
    }
    
    return $data['choices'][0]['message']['content'];
}

/**
 * 解析AI响应
 */
function parseResponse($type, $response) {
    // 尝试提取JSON
    $jsonMatch = [];
    if (preg_match('/\{.*\}|\[.*\]/s', $response, $jsonMatch)) {
        $json = json_decode($jsonMatch[0], true);
        if ($json !== null) {
            return $json;
        }
    }
    
    // 如果JSON解析失败，返回原始响应
    return ['raw_response' => $response];
}

/**
 * 智能选择最优的API Key（负载均衡）
 */
function selectOptimalAPIKey($pdo) {
    try {
        // 获取所有活跃的API Keys，按使用次数排序
        $stmt = $pdo->prepare("
            SELECT
                ak.id as key_id,
                ak.api_key,
                ak.name as key_name,
                ak.usage_count
            FROM pt_service_key ak
            JOIN pt_service_platform ap ON ak.platform_id = ap.id
            WHERE ap.code = 'bigmodel' AND ak.is_active = 1
            ORDER BY ak.usage_count ASC, RAND()
            LIMIT 1
        ");
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_ASSOC);

    } catch (PDOException $e) {
        return null;
    }
}

/**
 * 智能选择最优的API Key（排除指定的Key ID）
 */
function selectOptimalAPIKeyExcluding($pdo, $excludeKeyIds = []) {
    try {
        // 构建排除条件
        $excludeCondition = '';
        $params = [];

        if (!empty($excludeKeyIds)) {
            $placeholders = str_repeat('?,', count($excludeKeyIds) - 1) . '?';
            $excludeCondition = " AND ak.id NOT IN ($placeholders)";
            $params = $excludeKeyIds;
        }

        // 获取可用的API密钥（排除已使用的）
        $sql = "
            SELECT
                ak.id as key_id,
                ak.api_key,
                ak.name as key_name,
                ak.usage_count
            FROM pt_service_key ak
            JOIN pt_service_platform ap ON ak.platform_id = ap.id
            WHERE ap.code = 'bigmodel' AND ak.is_active = 1" . $excludeCondition . "
            ORDER BY ak.usage_count ASC, RAND()
            LIMIT 1
        ";

        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetch(PDO::FETCH_ASSOC);

    } catch (PDOException $e) {
        return null;
    }
}

/**
 * 更新API Key使用统计
 */
function updateKeyUsage($pdo, $keyId) {
    try {
        $stmt = $pdo->prepare("
            UPDATE pt_service_key
            SET usage_count = usage_count + 1,
                last_used_at = NOW()
            WHERE id = ?
        ");
        $stmt->execute([$keyId]);
    } catch (PDOException $e) {
        // 更新失败不影响主流程
    }
}

/**
 * 带重试机制的AI模型调用
 */
function callGLMModelWithRetry($pdo, $systemPrompt, $userPrompt, $maxRetries = 3) {
    $attemptCount = 0;
    $usedKeyIds = [];

    while ($attemptCount < $maxRetries) {
        $attemptCount++;

        // 获取配置（排除已经尝试过的Key）
        $apiKeyConfig = selectOptimalAPIKeyExcluding($pdo, $usedKeyIds);
        if (!$apiKeyConfig) {
            break;
        }

        // 记录使用的Key ID
        $usedKeyIds[] = $apiKeyConfig['key_id'];

        // 尝试调用API
        $result = callGLMModel($apiKeyConfig['api_key'], $systemPrompt, $userPrompt);

        if ($result) {
            // 成功，更新使用统计并返回结果
            updateKeyUsage($pdo, $apiKeyConfig['key_id']);
            return $result;
        }
    }

    return null;
}
?>
