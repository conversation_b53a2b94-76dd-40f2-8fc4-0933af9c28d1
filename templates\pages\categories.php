<?php
/**
 * 工具分类总览页面
 */

$currentPage = 'categories';
$breadcrumbs = [
    ['name' => 'Home', 'url' => '/'],
    ['name' => 'Tool Categories']
];

// 使用统一的数据库连接
require_once ROOT_PATH . '/includes/database-connection.php';

// 包含工具助手
require_once ROOT_PATH . '/app/helpers/tool-helpers.php';

// 包含动态SEO助手
require_once ROOT_PATH . '/app/helpers/dynamic-seo.php';

// 获取动态SEO数据
$seoData = getDynamicSEOData('categories');

// 包含公共头部
include_once ROOT_PATH . '/templates/layout/header.php';

// 获取所有工具分类及其工具数量
function getCategoriesWithToolCount($page = 1, $perPage = 9) {
    global $pdo;

    try {
        // 计算偏移量
        $offset = ($page - 1) * $perPage;

        // 获取总分类数
        $countStmt = $pdo->query("
            SELECT COUNT(*) as total
            FROM pt_tool_category tc
            WHERE tc.status = 'active'
        ");
        $totalCategories = $countStmt->fetch()['total'];

        // 获取当前页的分类
        $stmt = $pdo->prepare("
            SELECT tc.*, COUNT(t.id) as tool_count
            FROM pt_tool_category tc
            LEFT JOIN pt_tool t ON tc.id = t.category_id AND t.status = 'active'
            WHERE tc.status = 'active'
            GROUP BY tc.id
            ORDER BY tool_count DESC, tc.sort_order, tc.name
            LIMIT ? OFFSET ?
        ");
        $stmt->execute([$perPage, $offset]);
        $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $result = [];
        foreach ($categories as $category) {
            $result[] = [
                'id' => $category['id'],
                'name' => $category['name'],
                'slug' => $category['slug'],
                'description' => $category['description'],
                'icon' => $category['icon'] ?? '📁',
                'sort_order' => $category['sort_order'],
                'tool_count' => $category['tool_count']
            ];
        }

        return [
            'categories' => $result,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $perPage,
                'total' => $totalCategories,
                'total_pages' => ceil($totalCategories / $perPage),
                'has_prev' => $page > 1,
                'has_next' => $page < ceil($totalCategories / $perPage)
            ]
        ];
    } catch (Exception $e) {
        // 如果数据库查询失败，记录错误并返回基础分类
        error_log('Error loading categories with tool count: ' . $e->getMessage());
        return [
            'categories' => getToolCategories(),
            'pagination' => [
                'current_page' => 1,
                'per_page' => $perPage,
                'total' => 0,
                'total_pages' => 1,
                'has_prev' => false,
                'has_next' => false
            ]
        ];
    }
}

// 获取首页分类数据

$page = 1; // 首次加载显示第一页
$perPage = 9;

// 获取分类数据
$categoryData = getCategoriesWithToolCount($page, $perPage);
$allCategories = $categoryData['categories'];
$pagination = $categoryData['pagination'];


?>

<!-- 分类页面内容 -->
<section class="py-20 bg-black">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- 页面标题 -->
        <div class="text-center mb-16">
            <h1 class="text-4xl md:text-5xl font-bold mb-6">Tool Categories</h1>
            <p class="text-xl text-gray-400 max-w-3xl mx-auto">
                Explore our comprehensive collection of tools organized by category. Find the perfect tool for your specific needs.
            </p>
        </div>

        <!-- 统计信息 -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
            <?php
            // 计算真实统计数据
            $totalCategories = count($allCategories);
            $totalTools = array_sum(array_column($allCategories, 'tool_count'));
            $activeCategories = count(array_filter($allCategories, function($cat) {
                return $cat['tool_count'] > 0;
            }));
            ?>
            <div class="text-center">
                <div class="text-4xl font-bold text-accent mb-2"><?= $totalCategories ?></div>
                <div class="text-gray-400">Total Categories</div>
            </div>
            <div class="text-center">
                <div class="text-4xl font-bold text-accent mb-2"><?= $totalTools ?></div>
                <div class="text-gray-400">Total Tools</div>
            </div>
            <div class="text-center">
                <div class="text-4xl font-bold text-accent mb-2"><?= $activeCategories ?></div>
                <div class="text-gray-400">Active Categories</div>
            </div>
        </div>

        <!-- 分类网格 -->
        <div id="categoriesGrid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <?php foreach ($allCategories as $category): ?>
            <a href="/tools/<?= htmlspecialchars($category['slug']) ?>"
               class="bg-gray-900 border border-gray-800 p-8 hover:border-accent transition-all duration-300 group block relative"
               title="<?= htmlspecialchars($category['name']) ?> - <?= htmlspecialchars($category['description']) ?>">

                <!-- 工具数量徽章 -->
                <div class="absolute top-4 right-4 bg-accent text-white text-xs px-2 py-1 rounded-full">
                    <?= $category['tool_count'] ?> tool<?= $category['tool_count'] != 1 ? 's' : '' ?>
                </div>

                <!-- 分类图标 -->
                <div class="text-5xl mb-6 group-hover:scale-110 transition-transform duration-300">
                    <?= htmlspecialchars($category['icon']) ?>
                </div>

                <!-- 分类名称 -->
                <h3 class="text-2xl font-semibold mb-4 group-hover:text-accent transition-colors duration-300">
                    <?= htmlspecialchars($category['name']) ?>
                </h3>

                <!-- 分类描述 -->
                <p class="text-gray-400 mb-6 leading-relaxed">
                    <?= htmlspecialchars($category['description']) ?>
                </p>

                <!-- 查看按钮 -->
                <div class="flex items-center text-accent group-hover:translate-x-2 transition-transform duration-300">
                    <span class="text-sm mr-1">Explore Tools</span>
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                </div>
            </a>
            <?php endforeach; ?>
        </div>

        <!-- Load More 按钮 -->
        <?php if ($pagination['has_next']): ?>
        <div class="text-center mt-16">
            <button id="loadMoreBtn" onclick="loadMoreCategories()"
                    class="bg-accent text-white px-8 py-4 text-lg font-medium hover:bg-blue-600 transition-colors duration-200">
                Load More Categories
            </button>
        </div>
        <?php endif; ?>

        <!-- 底部行动号召 -->
        <div class="text-center mt-20">
            <h2 class="text-3xl font-bold mb-6">Can't Find What You're Looking For?</h2>
            <p class="text-xl text-gray-400 mb-8 max-w-2xl mx-auto">
                We're constantly adding new tools and categories. Check back regularly or suggest a tool you'd like to see.
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a href="/tools"
                   class="bg-accent text-white px-8 py-4 text-lg font-medium hover:bg-blue-700 hover:text-white transition-all duration-200 transform hover:scale-105">
                    Browse All Tools
                </a>
                <a href="/requests" class="btn btn-secondary btn-lg">
                    Suggest a Tool
                </a>
            </div>
        </div>
    </div>
</section>

<script>
// 分页变量
let currentPage = <?= $pagination['current_page'] ?>;
let totalPages = <?= $pagination['total_pages'] ?>;
let hasMore = <?= $pagination['has_next'] ? 'true' : 'false' ?>;
let isLoading = false;

// 加载更多分类
async function loadMoreCategories() {
    if (!hasMore || isLoading) return;

    isLoading = true;
    const loadMoreBtn = document.getElementById('loadMoreBtn');

    // 更新按钮状态
    loadMoreBtn.disabled = true;
    loadMoreBtn.textContent = 'Loading...';

    try {
        const response = await fetch(`/api/categories.php?page=${currentPage + 1}`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        const data = await response.json();

        if (data.success) {
            // 生成HTML并添加到网格中
            const categoriesGrid = document.getElementById('categoriesGrid');
            let categoriesHtml = '';

            data.categories.forEach(category => {
                categoriesHtml += `
                <a href="/tools/${category.slug}"
                   class="bg-gray-900 border border-gray-800 p-8 hover:border-accent transition-all duration-300 group block relative"
                   title="${category.name} - ${category.description}">

                    <!-- 工具数量徽章 -->
                    <div class="absolute top-4 right-4 bg-accent text-white text-xs px-2 py-1 rounded-full">
                        ${category.tool_count} tool${category.tool_count != 1 ? 's' : ''}
                    </div>

                    <!-- 分类图标 -->
                    <div class="text-5xl mb-6 group-hover:scale-110 transition-transform duration-300">
                        ${category.icon}
                    </div>

                    <!-- 分类名称 -->
                    <h3 class="text-2xl font-semibold mb-4 group-hover:text-accent transition-colors duration-300">
                        ${category.name}
                    </h3>

                    <!-- 分类描述 -->
                    <p class="text-gray-400 mb-6 leading-relaxed">
                        ${category.description}
                    </p>

                    <!-- 查看按钮 -->
                    <div class="flex items-center text-accent group-hover:translate-x-2 transition-transform duration-300">
                        <span class="text-sm mr-1">Explore Tools</span>
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </div>
                </a>`;
            });

            categoriesGrid.insertAdjacentHTML('beforeend', categoriesHtml);

            // 更新分页状态
            currentPage = data.pagination.current_page;
            hasMore = data.pagination.has_next;

            // 如果没有更多内容，隐藏按钮
            if (!hasMore) {
                loadMoreBtn.style.display = 'none';
            }
        } else {
            console.error('Failed to load more categories');
        }
    } catch (error) {
        console.error('Error loading more categories:', error);
    } finally {
        isLoading = false;

        // 恢复按钮状态
        if (hasMore) {
            loadMoreBtn.disabled = false;
            loadMoreBtn.textContent = 'Load More Categories';
        }
    }
}
</script>

<?php include_once ROOT_PATH . '/templates/layout/footer.php'; ?>
