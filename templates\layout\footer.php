    </main>



    <!-- 页脚 -->
    <footer class="bg-gray-900 border-t border-gray-800">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <!-- 公司信息 -->
                <div class="col-span-1 md:col-span-1">
                    <div class="flex items-center space-x-2 mb-4">
                        <div class="w-8 h-8 bg-accent flex items-center justify-center">
                            <span class="text-white font-bold text-lg">AI</span>
                        </div>
                        <span class="text-xl font-bold text-white">Prompt2Tool</span>
                    </div>
                    <p class="text-gray-400 text-sm leading-relaxed mb-4">
                        <?php
                        // 使用动态公司描述
                        if (function_exists('getCompanyInfo')) {
                            $companyInfo = getCompanyInfo();
                            echo htmlspecialchars($companyInfo['description']);
                        } else {
                            echo 'AI-powered free online tools platform providing development, design, SEO, and various practical tools to boost your productivity.';
                        }
                        ?>
                    </p>
                    <div class="flex space-x-4">
                        <?php
                        // 获取动态社交链接
                        if (function_exists('getSocialLinks')) {
                            $socialLinks = getSocialLinks();
                        } else {
                            $socialLinks = [
                                'github' => 'https://github.com/prompt2tool',
                                'twitter' => 'https://twitter.com/prompt2tool',
                                'linkedin' => 'https://linkedin.com/company/prompt2tool'
                            ];
                        }
                        ?>

                        <?php if (!empty($socialLinks['github'])): ?>
                        <a href="<?= htmlspecialchars($socialLinks['github']) ?>" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition-colors duration-200" aria-label="GitHub">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                            </svg>
                        </a>
                        <?php endif; ?>

                        <?php if (!empty($socialLinks['twitter'])): ?>
                        <a href="<?= htmlspecialchars($socialLinks['twitter']) ?>" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition-colors duration-200" aria-label="Twitter">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                            </svg>
                        </a>
                        <?php endif; ?>

                        <?php if (!empty($socialLinks['linkedin'])): ?>
                        <a href="<?= htmlspecialchars($socialLinks['linkedin']) ?>" target="_blank" rel="noopener noreferrer" class="text-gray-400 hover:text-white transition-colors duration-200" aria-label="LinkedIn">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                            </svg>
                        </a>
                        <?php endif; ?>
                    </div>
                </div>
                
                <!-- 工具分类 -->
                <div>
                    <h3 class="text-lg font-semibold mb-4 text-white">Tool Categories</h3>
                    <ul class="space-y-2">
                        <?php
                        // 显示工具数量最多的8个分类
                        $topCategories = getTopToolCategories(8);
                        foreach ($topCategories as $slug => $category): ?>
                        <li><a href="/tools/<?= $slug ?>" class="text-gray-400 hover:text-white transition-colors duration-200 text-sm">
                            <?= htmlspecialchars($category['icon']) ?> <?= htmlspecialchars($category['name']) ?>
                        </a></li>
                        <?php endforeach; ?>
                        <li class="pt-2">
                            <a href="/categories" class="text-gray-500 hover:text-white transition-colors duration-200 text-xs">
                                View All Categories →
                            </a>
                        </li>
                    </ul>
                </div>
                
                <!-- 热门工具 -->
                <div>
                    <h3 class="text-lg font-semibold mb-4 text-white">Popular Tools</h3>
                    <ul class="space-y-2">
                        <?php
                        $popularTools = getPopularTools(8);
                        foreach ($popularTools as $tool): ?>
                        <li>
                            <a href="/tools/<?= !empty($tool['category_slug']) ? htmlspecialchars($tool['category_slug']) . '/' : '' ?><?= htmlspecialchars($tool['slug']) ?>"
                               class="text-gray-400 hover:text-white transition-colors duration-200 text-sm">
                                <?= htmlspecialchars($tool['name']) ?>
                            </a>
                        </li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                
                <!-- 帮助支持 -->
                <div>
                    <h3 class="text-lg font-semibold mb-4 text-white">Help & Support</h3>
                    <ul class="space-y-2">
                        <li><a href="/about" class="text-gray-400 hover:text-white transition-colors duration-200 text-sm">About Us</a></li>
                        <li><a href="/contact" class="text-gray-400 hover:text-white transition-colors duration-200 text-sm">Contact Us</a></li>
                        <li><a href="/help" class="text-gray-400 hover:text-white transition-colors duration-200 text-sm">Help Center</a></li>
                        <li><a href="/privacy" class="text-gray-400 hover:text-white transition-colors duration-200 text-sm">Privacy Policy</a></li>
                        <li><a href="/terms" class="text-gray-400 hover:text-white transition-colors duration-200 text-sm">Terms of Service</a></li>
                    </ul>
                </div>
            </div>
            
            <!-- 底部版权信息 -->
            <div class="border-t border-gray-800 mt-8 pt-8">
                <div class="text-center">
                    <p class="text-gray-400 text-sm">
                        <?php
                        // Use dynamic copyright if available
                        if (function_exists('getCopyright')) {
                            echo htmlspecialchars(getCopyright());
                        } else {
                            echo '&copy; ' . date('Y') . ' Prompt2Tool. All rights reserved.';
                        }

                        // 检查sitemap.xml是否存在，如果存在则显示链接（仅对搜索引擎蜘蛛可见）
                        if (defined('ROOT_PATH')) {
                            $sitemapPath = ROOT_PATH . '/public/sitemap.xml';
                        } elseif (defined('PUBLIC_PATH')) {
                            $sitemapPath = PUBLIC_PATH . '/sitemap.xml';
                        } else {
                            // 备用路径检测
                            $sitemapPath = dirname(dirname(__DIR__)) . '/public/sitemap.xml';
                        }

                        if (file_exists($sitemapPath)) {
                            // 检测是否为搜索引擎蜘蛛
                            $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
                            $isBot = false;

                            // 常见搜索引擎蜘蛛UA关键词
                            $botKeywords = [
                                'Googlebot',
                                'Bingbot',
                                'Slurp',           // Yahoo
                                'DuckDuckBot',     // DuckDuckGo
                                'Baiduspider',     // Baidu
                                'YandexBot',       // Yandex
                                'facebookexternalhit', // Facebook
                                'Twitterbot',      // Twitter
                                'LinkedInBot',     // LinkedIn
                                'WhatsApp',        // WhatsApp
                                'Applebot',        // Apple
                                'SemrushBot',      // SEMrush
                                'AhrefsBot',       // Ahrefs
                                'MJ12bot',         // Majestic
                                'DotBot',          // Moz
                                'crawler',
                                'spider',
                                'bot'
                            ];

                            foreach ($botKeywords as $keyword) {
                                if (stripos($userAgent, $keyword) !== false) {
                                    $isBot = true;
                                    break;
                                }
                            }

                            // 只对搜索引擎蜘蛛显示sitemap链接
                            if ($isBot) {
                                echo ' | <a href="/sitemap.xml" class="text-gray-400 hover:text-white transition-colors duration-200">Sitemap</a>';
                            }
                        }
                        ?>
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- 搜索模态框 -->
    <div id="search-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-gray-900 border border-gray-700 w-full max-w-2xl mx-4">
            <div class="p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-white">Search Tools</h3>
                    <button class="text-gray-400 hover:text-white" onclick="closeModal('search-modal')">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <div class="relative">
                    <input type="text"
                           id="search-input"
                           placeholder="Search for tools..."
                           class="w-full px-4 py-3 bg-black border border-gray-700 text-white placeholder-gray-400 focus:border-accent focus:outline-none"
                           autocomplete="off">
                    <div class="absolute right-3 top-3">
                        <svg id="search-icon" class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        <div id="search-loading" class="w-5 h-5 border-2 border-gray-400 border-t-accent rounded-full animate-spin hidden"></div>
                    </div>
                </div>

                <!-- 搜索结果区域 -->
                <div id="search-results" class="mt-4 max-h-96 overflow-y-auto hidden">
                    <!-- 搜索结果将在这里显示 -->
                </div>

                <!-- 搜索提示 -->
                <div id="search-hints" class="mt-4">
                    <p class="text-gray-400 text-sm mb-2">Try searching for:</p>
                    <div class="flex flex-wrap gap-2">
                        <button class="bg-gray-800 hover:bg-blue-600 text-gray-300 hover:text-white px-3 py-1 rounded-full text-sm transition-colors" onclick="performSearch('HTML')">HTML</button>
                        <button class="bg-gray-800 hover:bg-blue-600 text-gray-300 hover:text-white px-3 py-1 rounded-full text-sm transition-colors" onclick="performSearch('Image')">Image</button>
                        <button class="bg-gray-800 hover:bg-blue-600 text-gray-300 hover:text-white px-3 py-1 rounded-full text-sm transition-colors" onclick="performSearch('Password')">Password</button>
                        <button class="bg-gray-800 hover:bg-blue-600 text-gray-300 hover:text-white px-3 py-1 rounded-full text-sm transition-colors" onclick="performSearch('QR')">QR</button>
                        <button class="bg-gray-800 hover:bg-blue-600 text-gray-300 hover:text-white px-3 py-1 rounded-full text-sm transition-colors" onclick="performSearch('CSS')">CSS</button>
                        <button class="bg-gray-800 hover:bg-blue-600 text-gray-300 hover:text-white px-3 py-1 rounded-full text-sm transition-colors" onclick="performSearch('Color')">Color</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 工具状态警告 (仅在非发布状态的工具页面显示) -->
    <div id="tool-status-warning" class="fixed top-20 left-1/2 transform -translate-x-1/2 bg-yellow-900 border border-yellow-600 rounded-lg p-4 max-w-md mx-auto opacity-0 invisible transition-all duration-300 z-50" style="display: none;">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                </svg>
            </div>
            <div class="ml-3">
                <h3 id="status-warning-title" class="text-sm font-medium text-yellow-400"></h3>
                <div id="status-warning-message" class="mt-1 text-sm text-yellow-300"></div>
            </div>
            <button id="close-status-warning" class="ml-auto flex-shrink-0 text-yellow-400 hover:text-yellow-300">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
    </div>

    <!-- 收藏按钮 (仅在工具页面显示) -->
    <button id="favorite-tool-btn" class="fixed bottom-20 right-6 bg-gray-800 text-white p-3 border border-gray-600 hover:bg-gray-700 hover:border-gray-500 transition-all duration-200 opacity-0 invisible" style="display: none;">
        <svg class="favorite-icon w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd"></path>
        </svg>
    </button>

    <!-- 返回顶部按钮 -->
    <button id="back-to-top" class="fixed bottom-6 right-6 bg-accent text-white p-3 hover:bg-blue-700 hover:text-white transition-all duration-200 opacity-0 invisible">
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
        </svg>
    </button>

    <!-- 收藏按钮样式 -->
    <style>
    #favorite-tool-btn {
        transition: all 0.3s ease;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    }

    #favorite-tool-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(0, 0, 0, 0.4);
    }

    #favorite-tool-btn:disabled {
        pointer-events: none;
        opacity: 0.6;
    }

    #favorite-tool-btn .favorite-icon {
        transition: transform 0.2s ease;
    }

    #favorite-tool-btn:hover .favorite-icon {
        transform: scale(1.1);
    }

    /* 收藏按钮动画效果 */
    @keyframes heartBeat {
        0% { transform: scale(1); }
        14% { transform: scale(1.3); }
        28% { transform: scale(1); }
        42% { transform: scale(1.3); }
        70% { transform: scale(1); }
    }

    #favorite-tool-btn.favorited .favorite-icon {
        animation: heartBeat 1s ease-in-out;
    }
    </style>

    <!-- JavaScript文件 -->
    <!-- 工具函数库 -->
    <script src="/assets/js/utils/helpers.js"></script>

    <!-- UI组件 -->
    <script src="/assets/js/components/ui/modal.js"></script>
    <script src="/assets/js/components/ui/form-handler.js"></script>

    <!-- 功能组件 -->
    <script src="/assets/js/components/navigation.js"></script>
    <script src="/assets/js/components/tools/tool-manager.js"></script>

    <!-- 主应用 -->
    <script src="/assets/js/app.js"></script>
    
    <!-- 移动端菜单脚本 -->
    <script>
        // 移动端菜单切换
        document.getElementById('mobile-menu-btn')?.addEventListener('click', function() {
            const menu = document.getElementById('mobile-menu');
            menu.classList.toggle('hidden');
        });

        // 搜索模态框
        function closeModal(modalId) {
            document.getElementById(modalId).classList.add('hidden');
        }

        // 返回顶部按钮和收藏按钮
        window.addEventListener('scroll', function() {
            const backToTop = document.getElementById('back-to-top');
            const favoriteBtn = document.getElementById('favorite-tool-btn');

            if (window.scrollY > 300) {
                backToTop.classList.remove('opacity-0', 'invisible');
                // 如果是工具页面，也显示收藏按钮
                if (favoriteBtn && favoriteBtn.style.display !== 'none') {
                    favoriteBtn.classList.remove('opacity-0', 'invisible');
                }
            } else {
                backToTop.classList.add('opacity-0', 'invisible');
                if (favoriteBtn) {
                    favoriteBtn.classList.add('opacity-0', 'invisible');
                }
            }
        });

        document.getElementById('back-to-top')?.addEventListener('click', function() {
            window.scrollTo({ top: 0, behavior: 'smooth' });
        });

        // 搜索模态框打开
        document.querySelectorAll('[data-modal="search-modal"]').forEach(btn => {
            btn.addEventListener('click', function() {
                document.getElementById('search-modal').classList.remove('hidden');
                document.querySelector('#search-modal input').focus();
                // 清空之前的搜索结果
                clearSearchResults();
            });
        });

        // 点击模态框外部关闭
        document.getElementById('search-modal')?.addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal('search-modal');
            }
        });

        // ESC键关闭模态框
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeModal('search-modal');
            }
        });

        // 搜索功能 - 优化版本
        let searchTimeout;
        let isSearching = false;
        const searchInput = document.getElementById('search-input');
        const searchResults = document.getElementById('search-results');
        const searchHints = document.getElementById('search-hints');
        const searchIcon = document.getElementById('search-icon');
        const searchLoading = document.getElementById('search-loading');

        if (searchInput) {
            // 使用防抖处理输入事件
            searchInput.addEventListener('input', debounce(function() {
                const query = this.value.trim();

                if (query.length < 2) {
                    clearSearchResults();
                    return;
                }

                // 只有在不搜索时才发起新搜索
                if (!isSearching) {
                    performSearchRequest(query);
                }
            }, 500)); // 增加延迟到500ms

            // 回车键搜索
            searchInput.addEventListener('keydown', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    const query = this.value.trim();
                    if (query.length >= 2) {
                        // 清除防抖定时器，立即搜索
                        clearTimeout(searchTimeout);
                        performSearchRequest(query);
                    }
                }
            });
        }

        // 防抖函数
        function debounce(func, wait) {
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(searchTimeout);
                    func.apply(this, args);
                };
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(later, wait);
            };
        }

        // 执行搜索请求 - 优化版本
        function performSearchRequest(query) {
            // 防止重复搜索
            if (isSearching) {
                return;
            }

            isSearching = true;
            showSearchLoading(true);

            // 使用POST请求发送表单数据
            const formData = new FormData();
            formData.append('query', query);

            fetch('/api/search-tools.php', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                isSearching = false;
                showSearchLoading(false);
                displaySearchResults(data);
            })
            .catch(error => {
                isSearching = false;
                showSearchLoading(false);
                console.error('Search error:', error);
                displaySearchError();
            });
        }

        // 显示搜索加载状态
        function showSearchLoading(loading) {
            if (loading) {
                searchIcon.classList.add('hidden');
                searchLoading.classList.remove('hidden');
            } else {
                searchIcon.classList.remove('hidden');
                searchLoading.classList.add('hidden');
            }
        }

        // 显示搜索结果
        function displaySearchResults(data) {
            if (!data.success || data.results.length === 0) {
                displayNoResults(data.query);
                return;
            }

            const maxDisplayResults = 8; // 最多显示8个结果
            const displayResults = data.results.slice(0, maxDisplayResults);
            const hasMoreResults = data.results.length > maxDisplayResults;

            let resultsHTML = '<div class="border-t border-gray-700 pt-4">';
            resultsHTML += `<div class="text-sm text-gray-400 mb-4 flex items-center justify-between">
                <span>Found ${data.total} result${data.total !== 1 ? 's' : ''} for "${data.query}"</span>
                ${hasMoreResults ? `<span class="text-blue-400">Showing ${maxDisplayResults} of ${data.total}</span>` : ''}
            </div>`;

            displayResults.forEach((tool, index) => {
                resultsHTML += `
                    <a href="${tool.url}"
                       class="block p-4 hover:bg-gray-800 border border-transparent hover:border-blue-500 hover:border-opacity-30 border-b border-b-gray-800 last:border-b-0 transition-all duration-200 group rounded-md"
                       onclick="closeModal('search-modal')">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <h4 class="text-white font-semibold mb-2 group-hover:text-blue-400 transition-colors">${tool.highlighted_name}</h4>
                                <p class="text-gray-400 text-sm mb-3 line-clamp-2 leading-relaxed">${tool.highlighted_description}</p>
                                <div class="flex items-center text-xs text-gray-500">
                                    <span class="bg-blue-600 text-white px-3 py-1 rounded-full text-xs font-medium mr-3 shadow-sm">${tool.category}</span>
                                    <span class="flex items-center">
                                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                        </svg>
                                        ${tool.view_count} views
                                    </span>
                                </div>
                            </div>
                            <div class="ml-4 text-blue-500 group-hover:text-blue-400 transition-colors">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </div>
                        </div>
                    </a>
                `;
            });

            // 如果有更多结果，添加"查看所有结果"链接
            if (hasMoreResults) {
                resultsHTML += `
                    <div class="border-t border-gray-700 pt-3 mt-3">
                        <a href="/tools?search=${encodeURIComponent(data.query)}"
                           class="block text-center py-3 text-blue-400 hover:text-blue-300 font-medium transition-colors"
                           onclick="closeModal('search-modal')">
                            <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                            View all ${data.total} results
                        </a>
                    </div>
                `;
            }

            resultsHTML += '</div>';

            searchResults.innerHTML = resultsHTML;
            searchResults.classList.remove('hidden');
            searchHints.classList.add('hidden');
        }

        // 显示无结果
        function displayNoResults(query) {
            const noResultsHTML = `
                <div class="border-t border-gray-700 pt-4">
                    <div class="text-center py-8">
                        <svg class="w-12 h-12 text-gray-600 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                        <h3 class="text-white font-medium mb-2">No tools found</h3>
                        <p class="text-gray-400 text-sm">No tools match "${query}". Try different keywords.</p>
                    </div>
                </div>
            `;

            searchResults.innerHTML = noResultsHTML;
            searchResults.classList.remove('hidden');
            searchHints.classList.add('hidden');
        }

        // 显示搜索错误
        function displaySearchError() {
            const errorHTML = `
                <div class="border-t border-gray-700 pt-4">
                    <div class="text-center py-8">
                        <svg class="w-12 h-12 text-red-500 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <h3 class="text-white font-medium mb-2">Search Error</h3>
                        <p class="text-gray-400 text-sm">Something went wrong. Please try again.</p>
                    </div>
                </div>
            `;

            searchResults.innerHTML = errorHTML;
            searchResults.classList.remove('hidden');
            searchHints.classList.add('hidden');
        }

        // 清空搜索结果
        function clearSearchResults() {
            searchResults.classList.add('hidden');
            searchHints.classList.remove('hidden');
            searchResults.innerHTML = '';
            if (searchInput) {
                searchInput.value = '';
            }
        }

        // 执行预设搜索
        function performSearch(query) {
            if (searchInput) {
                searchInput.value = query;
                performSearchRequest(query);
            }
        }

        // 全局函数，供外部调用
        window.performSearch = performSearch;
        window.clearSearchResults = clearSearchResults;

        // 工具页面浏览量统计函数
        function trackToolPageView(toolSlug) {
            // 发送统计请求
            fetch('/api/track-view.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    tool_slug: toolSlug
                })
            }).catch(error => {
                // View tracking failed silently
            });
        }

        // 自动检测工具页面并统计
        document.addEventListener('DOMContentLoaded', function() {
            // 检查当前页面是否是工具页面
            const path = window.location.pathname;

            // 只支持分类路由格式：/tools/category/tool-slug
            const toolPageWithCategory = path.match(/^\/tools\/([^\/]+)\/([^\/]+)$/);

            if (toolPageWithCategory) {
                // 带分类的工具页面 - 唯一支持的格式
                const category = toolPageWithCategory[1];
                const toolSlug = toolPageWithCategory[2];
                const fullToolPath = `${category}/${toolSlug}`;

                trackToolPageView(toolSlug);

                // 初始化收藏功能，传递完整路径
                initializeFavoriteFeature(toolSlug, fullToolPath);
            }
        });

        // 初始化收藏功能
        function initializeFavoriteFeature(toolSlug, fullToolPath) {
            const favoriteBtn = document.getElementById('favorite-tool-btn');
            if (!favoriteBtn) return;

            // 显示收藏按钮
            favoriteBtn.style.display = 'block';

            let isFavorited = false;
            let isLoading = false;
            let toolId = null;
            let toolData = null; // 存储工具的完整信息

            // 获取工具ID并检查收藏状态
            getToolIdAndCheckFavorite(toolSlug, fullToolPath);

            // 收藏按钮点击事件
            favoriteBtn.addEventListener('click', function() {
                if (isLoading || !toolId) return;
                toggleFavorite();
            });

            // 获取工具ID并检查收藏状态
            async function getToolIdAndCheckFavorite(slug, fullPath) {
                try {
                    // 首先获取工具ID和完整信息
                    const response = await fetch(`/api/get-tool-id.php?slug=${encodeURIComponent(slug)}&full_path=${encodeURIComponent(fullPath)}`);
                    const result = await response.json();

                    if (result.success && result.tool_id) {
                        toolId = result.tool_id;
                        toolData = result; // 保存工具的完整信息

                        // 检查工具状态，只有 active 状态的工具才显示收藏按钮
                        if (result.tool_status === 'active') {
                            checkFavoriteStatus();
                        } else {
                            console.log('Tool is not active, hiding favorite button');
                            favoriteBtn.style.display = 'none';
                        }
                    } else {
                        console.log('Tool not found or no ID available');
                        favoriteBtn.style.display = 'none';
                    }
                } catch (error) {
                    console.error('Error getting tool ID:', error);
                    favoriteBtn.style.display = 'none';
                }
            }

            // 检查收藏状态
            async function checkFavoriteStatus() {
                if (!toolId) return;

                try {
                    const response = await fetch('/api/favorites.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: `action=check&tool_id=${toolId}`
                    });

                    const result = await response.json();

                    if (result.success) {
                        isFavorited = result.is_favorited;
                        updateButtonState();
                    }
                } catch (error) {
                    console.error('Error checking favorite status:', error);
                }
            }

            // 切换收藏状态
            async function toggleFavorite() {
                if (isLoading || !toolId) return;

                setLoadingState(true);

                try {
                    const response = await fetch('/api/favorites.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: `action=toggle&tool_id=${toolId}`
                    });

                    const result = await response.json();

                    if (result.success) {
                        isFavorited = result.is_favorited;
                        updateButtonState(true); // 添加动画效果

                        // 根据操作类型显示不同颜色的消息
                        const messageType = result.action === 'added' ? 'success' : 'info';
                        showMessage(result.message, messageType);
                    } else {
                        if (result.error_code === 'NOT_LOGGED_IN') {
                            showMessage('Please login to use favorites feature', 'error');
                            setTimeout(() => {
                                window.location.href = '/auth/login';
                            }, 2000);
                        } else {
                            showMessage(result.message || 'Failed to update favorite status', 'error');
                        }
                    }
                } catch (error) {
                    console.error('Error toggling favorite:', error);
                    showMessage('Network error occurred', 'error');
                } finally {
                    setLoadingState(false);
                }
            }

            // 更新按钮状态
            function updateButtonState(animate = false) {
                const icon = favoriteBtn.querySelector('.favorite-icon');

                if (isFavorited) {
                    // 已收藏状态 - 实心红色
                    icon.innerHTML = `
                        <path fill="currentColor" fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd"></path>
                    `;
                    favoriteBtn.classList.remove('bg-gray-800', 'border-gray-600', 'hover:bg-gray-700', 'hover:border-gray-500');
                    favoriteBtn.classList.add('bg-red-600', 'border-red-600', 'hover:bg-red-700', 'hover:border-red-700');
                    favoriteBtn.title = 'Remove from favorites';

                    // 添加心跳动画
                    if (animate) {
                        favoriteBtn.classList.add('favorited');
                        setTimeout(() => {
                            favoriteBtn.classList.remove('favorited');
                        }, 1000);
                    }
                } else {
                    // 未收藏状态 - 空心灰色
                    icon.innerHTML = `
                        <path fill="none" stroke="currentColor" stroke-width="2" fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd"></path>
                    `;
                    favoriteBtn.classList.remove('bg-red-600', 'border-red-600', 'hover:bg-red-700', 'hover:border-red-700');
                    favoriteBtn.classList.add('bg-gray-800', 'border-gray-600', 'hover:bg-gray-700', 'hover:border-gray-500');
                    favoriteBtn.title = 'Add to favorites';
                }
            }

            // 设置加载状态
            function setLoadingState(loading) {
                isLoading = loading;
                favoriteBtn.disabled = loading;

                const icon = favoriteBtn.querySelector('.favorite-icon');

                if (loading) {
                    icon.innerHTML = `
                        <svg class="animate-spin w-5 h-5" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                    `;
                    favoriteBtn.title = 'Loading...';
                } else {
                    updateButtonState();
                }
            }

        // 初始化状态警告功能
        function initializeStatusWarning(toolSlug, fullToolPath) {
            const warningElement = document.getElementById('tool-status-warning');
            const titleElement = document.getElementById('status-warning-title');
            const messageElement = document.getElementById('status-warning-message');
            const closeButton = document.getElementById('close-status-warning');

            if (!warningElement) return;

            // 获取工具状态信息
            getToolStatus(toolSlug, fullToolPath);

            // 关闭按钮事件
            closeButton?.addEventListener('click', function() {
                hideStatusWarning();
            });

            // 获取工具状态
            async function getToolStatus(slug, fullPath) {
                try {
                    const response = await fetch(`/api/get-tool-id.php?slug=${encodeURIComponent(slug)}&full_path=${encodeURIComponent(fullPath)}`);
                    const result = await response.json();

                    if (result.success && result.tool_status && result.tool_status !== 'active') {
                        // 检查用户是否登录
                        const sessionResponse = await fetch('/api/check-session.php');
                        const sessionResult = await sessionResponse.json();

                        if (sessionResult.logged_in) {
                            showStatusWarning(result.tool_status);
                        }
                    }
                } catch (error) {
                    console.error('Error getting tool status:', error);
                }
            }

            // 显示状态警告
            function showStatusWarning(status) {
                let title = '';
                let message = '';

                switch (status) {
                    case 'coming_soon':
                        title = 'Tool Under Review';
                        message = 'This tool is currently under review and not yet publicly available. You can preview it because you\'re logged in.';
                        break;
                    case 'inactive':
                        title = 'Tool Currently Inactive';
                        message = 'This tool is currently inactive and not publicly available. You can preview it because you\'re logged in.';
                        break;
                    default:
                        return;
                }

                titleElement.textContent = title;
                messageElement.textContent = message;

                warningElement.style.display = 'block';
                setTimeout(() => {
                    warningElement.classList.remove('opacity-0', 'invisible');
                }, 100);

                // 5秒后自动隐藏
                setTimeout(() => {
                    hideStatusWarning();
                }, 5000);
            }

            // 隐藏状态警告
            function hideStatusWarning() {
                warningElement.classList.add('opacity-0', 'invisible');
                setTimeout(() => {
                    warningElement.style.display = 'none';
                }, 300);
            }
        }

        // 在页面加载完成后初始化状态警告
        document.addEventListener('DOMContentLoaded', function() {
            // 检查是否为工具页面
            const currentPath = window.location.pathname;
            const toolPageWithCategory = currentPath.match(/^\/tools\/([^\/]+)\/([^\/]+)$/);

            if (toolPageWithCategory) {
                const category = toolPageWithCategory[1];
                const toolSlug = toolPageWithCategory[2];
                const fullToolPath = `${category}/${toolSlug}`;

                // 初始化状态警告功能
                initializeStatusWarning(toolSlug, fullToolPath);
            }
        });

            // 显示消息提示
            function showMessage(message, type = 'info') {
                // 创建消息元素
                const messageEl = document.createElement('div');
                messageEl.className = `fixed top-4 right-4 z-50 px-6 py-3 text-white font-medium transition-all duration-300 transform translate-x-full ${
                    type === 'success' ? 'bg-green-600' :
                    type === 'error' ? 'bg-red-600' :
                    type === 'info' ? 'bg-orange-600' : 'bg-blue-600'
                }`;
                messageEl.textContent = message;

                document.body.appendChild(messageEl);

                // 显示动画
                setTimeout(() => {
                    messageEl.classList.remove('translate-x-full');
                }, 100);

                // 自动隐藏
                setTimeout(() => {
                    messageEl.classList.add('translate-x-full');
                    setTimeout(() => {
                        if (messageEl.parentNode) {
                            messageEl.parentNode.removeChild(messageEl);
                        }
                    }, 300);
                }, 3000);
            }
        }
    </script>
</body>
</html>
