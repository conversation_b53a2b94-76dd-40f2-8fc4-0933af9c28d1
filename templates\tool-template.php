<?php
/**
 * {{TOOL_NAME}} Tool Page
 * Zero border-radius design - no rounded corners
 */

$currentPage = 'tool-{{TOOL_SLUG}}';
$breadcrumbs = [
    ['name' => 'Home', 'url' => '/'],
    ['name' => 'Tools', 'url' => '/tools'],
    ['name' => '{{TOOL_CATEGORY}}', 'url' => '/tools/{{TOOL_CATEGORY_SLUG}}'],
    ['name' => '{{TOOL_NAME}}']
];

// Include dynamic SEO helper
require_once ROOT_PATH . '/app/helpers/dynamic-seo.php';

// Generate SEO data
$seoData = [
    'title' => '{{TOOL_NAME}} Online - Prompt2Tool',
    'description' => '{{TOOL_DESCRIPTION}}',
    'keywords' => '{{TOOL_TAGS}}',
    'og_title' => '{{TOOL_NAME}} Online - Prompt2Tool',
    'og_description' => '{{TOOL_DESCRIPTION}}',
    'canonical' => getCurrentURL()
];

// Include common header
include_once ROOT_PATH . '/templates/layout/header.php';
?>

<!-- Tool page main content -->
<div class="bg-gray-900 text-white">

    <!-- Hero Section with SEO optimized content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-12">
        <div class="text-center mb-12">
            <div class="flex items-center justify-center mb-4">
                <div class="bg-blue-600 p-3 mr-4">
                    <div class="w-10 h-10 text-white text-2xl flex items-center justify-center">{{TOOL_ICON}}</div>
                </div>
                <div class="text-left">
                    <h1 class="text-4xl font-bold text-white mb-2">{{TOOL_NAME}}</h1>
                    <p class="text-xl text-gray-400">{{TOOL_DESCRIPTION}}</p>
                </div>
            </div>
        </div>

        <!-- Tool main content -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Input area -->
            <div class="bg-gray-800 border border-gray-700 p-6">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-xl font-semibold text-white">Input</h2>
                    <div class="flex space-x-2">
                        <button id="clearInput" class="px-3 py-1 bg-red-600 text-white text-sm hover:bg-red-700 transition-colors">
                            <i class="fas fa-times mr-1"></i>Clear
                        </button>
                        <button id="pasteInput" class="px-3 py-1 bg-blue-600 text-white text-sm hover:bg-blue-700 transition-colors">
                            <i class="fas fa-paste mr-1"></i>Paste
                        </button>
                    </div>
                </div>
                
                {{INPUT_SECTION}}
            </div>

            <!-- Output area -->
            <div class="bg-gray-800 border border-gray-700 p-6">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-xl font-semibold text-white">Output</h2>
                    <div class="flex space-x-2">
                        <button id="copyOutput" class="px-3 py-1 bg-green-600 text-white text-sm hover:bg-green-700 transition-colors">
                            <i class="fas fa-copy mr-1"></i>Copy
                        </button>
                        <button id="downloadOutput" class="px-3 py-1 bg-purple-600 text-white text-sm hover:bg-purple-700 transition-colors">
                            <i class="fas fa-download mr-1"></i>Download
                        </button>
                    </div>
                </div>
                
                {{OUTPUT_SECTION}}
            </div>
        </div>

        <!-- FAQ Section -->
        <div class="px-8 pt-16 pb-16">
            <h2 class="text-3xl font-bold text-white text-center mb-8">Frequently Asked Questions</h2>
            <div class="max-w-4xl mx-auto space-y-6">
                {{FAQ_SECTION}}
            </div>
        </div>
    </div>

<script>
// Tool functionality JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Initialize DOM elements
    const clearBtn = document.getElementById('clearInput');
    const pasteBtn = document.getElementById('pasteInput');
    const copyBtn = document.getElementById('copyOutput');
    const downloadBtn = document.getElementById('downloadOutput');

    {{JAVASCRIPT_FUNCTIONALITY}}

    // Common functionality
    if (clearBtn) {
        clearBtn.addEventListener('click', function() {
            // Clear input logic
            {{CLEAR_FUNCTION}}
        });
    }

    if (pasteBtn) {
        pasteBtn.addEventListener('click', async function() {
            try {
                const text = await navigator.clipboard.readText();
                {{PASTE_FUNCTION}}
            } catch (err) {
                // Failed to read clipboard
            }
        });
    }

    if (copyBtn) {
        copyBtn.addEventListener('click', function() {
            {{COPY_FUNCTION}}
        });
    }

    if (downloadBtn) {
        downloadBtn.addEventListener('click', function() {
            {{DOWNLOAD_FUNCTION}}
        });
    }

    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        if (e.ctrlKey && e.key === 'Enter') {
            e.preventDefault();
            {{PROCESS_FUNCTION}}
        }
        if (e.ctrlKey && e.key === 'c' && e.target.tagName !== 'INPUT' && e.target.tagName !== 'TEXTAREA') {
            e.preventDefault();
            if (copyBtn) copyBtn.click();
        }
    });

    {{INITIALIZATION_CODE}}
});
</script>

</div>

<?php include_once ROOT_PATH . '/templates/layout/footer.php'; ?>
