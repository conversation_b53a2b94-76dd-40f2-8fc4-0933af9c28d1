<?php
/**
 * 工具模型类
 * 处理工具相关的数据库操作
 */

// 防止直接访问
if (!defined('ROOT_PATH')) {
    exit('Direct access not allowed');
}

// 加载基础模型
require_once ROOT_PATH . '/control-panel/models/BaseModel.php';

class ToolModel extends BaseModel {
    
    protected $table = 'pt_tool';
    protected $primaryKey = 'id';
    protected $fillable = [
        'name', 'slug', 'description', 'category_id', 'icon', 'color',
        'status', 'featured', 'premium', 'difficulty_level', 'tags',
        'meta_title', 'meta_description', 'meta_keywords',
        'usage_count', 'rating', 'rating_count', 'file_path',
        'config', 'dependencies', 'version', 'author',
        'documentation_url', 'demo_url', 'github_url',
        'created_by', 'updated_by', 'published_at'
    ];
    
    /**
     * 根据slug查找工具
     */
    public function findBySlug($slug) {
        return $this->findWhere(['slug' => $slug]);
    }
    
    /**
     * 获取已发布的工具
     */
    public function getPublishedTools($limit = null) {
        $conditions = ['status' => 'published'];
        $orderBy = 'published_at DESC';
        
        return $this->findAll($conditions, $orderBy, $limit);
    }
    
    /**
     * 获取特色工具
     */
    public function getFeaturedTools($limit = 10) {
        $conditions = [
            'status' => 'published',
            'featured' => 1
        ];
        $orderBy = 'usage_count DESC';
        
        return $this->findAll($conditions, $orderBy, $limit);
    }
    
    /**
     * 根据分类获取工具
     */
    public function getToolsByCategory($categoryId, $limit = null) {
        $conditions = [
            'category_id' => $categoryId,
            'status' => 'published'
        ];
        $orderBy = 'name ASC';
        
        return $this->findAll($conditions, $orderBy, $limit);
    }
    
    /**
     * 搜索工具
     */
    public function searchTools($keyword, $filters = []) {
        $sql = "SELECT t.*, c.name as category_name FROM {$this->table} t
                LEFT JOIN pt_tool_category c ON t.category_id = c.id
                WHERE t.status = 'published'";
        $params = [];
        
        // 关键词搜索
        if (!empty($keyword)) {
            $sql .= " AND (t.name LIKE :keyword OR t.description LIKE :keyword OR t.tags LIKE :keyword)";
            $params['keyword'] = "%{$keyword}%";
        }
        
        // 分类筛选
        if (!empty($filters['category_id'])) {
            $sql .= " AND t.category_id = :category_id";
            $params['category_id'] = $filters['category_id'];
        }
        
        // 难度级别筛选
        if (!empty($filters['difficulty_level'])) {
            $sql .= " AND t.difficulty_level = :difficulty_level";
            $params['difficulty_level'] = $filters['difficulty_level'];
        }
        
        // 是否付费筛选
        if (isset($filters['premium'])) {
            $sql .= " AND t.premium = :premium";
            $params['premium'] = $filters['premium'] ? 1 : 0;
        }
        
        // 评分筛选
        if (!empty($filters['min_rating'])) {
            $sql .= " AND t.rating >= :min_rating";
            $params['min_rating'] = $filters['min_rating'];
        }
        
        // 排序
        $orderBy = $filters['sort'] ?? 'usage_count';
        $orderDirection = $filters['order'] ?? 'DESC';
        $sql .= " ORDER BY t.{$orderBy} {$orderDirection}";
        
        return $this->db->fetchAll($sql, $params);
    }
    
    /**
     * 获取热门工具
     */
    public function getPopularTools($limit = 10, $days = 30) {
        $sql = "
            SELECT t.*, c.name as category_name,
                   COUNT(u.id) as recent_usage
            FROM {$this->table} t
            LEFT JOIN pt_tool_category c ON t.category_id = c.id
            LEFT JOIN pt_tool_usage u ON t.id = u.tool_id
                AND u.created_at >= DATE_SUB(NOW(), INTERVAL :days DAY)
            WHERE t.status = 'published'
            GROUP BY t.id
            ORDER BY recent_usage DESC, t.usage_count DESC
            LIMIT :limit
        ";
        
        return $this->db->fetchAll($sql, ['days' => $days, 'limit' => $limit]);
    }
    
    /**
     * 获取最新工具
     */
    public function getLatestTools($limit = 10) {
        $conditions = ['status' => 'published'];
        $orderBy = 'published_at DESC';
        
        return $this->findAll($conditions, $orderBy, $limit);
    }
    
    /**
     * 查找工具文件，支持多种文件类型
     * @param string $slug 工具slug
     * @return array|null 返回文件信息数组 ['path' => '文件路径', 'type' => '文件类型']，未找到返回null
     */
    private function findToolFile($slug) {
        $supportedExtensions = ['php', 'html', 'htm'];

        foreach ($supportedExtensions as $ext) {
            $filePath = ROOT_PATH . '/templates/pages/tools/' . $slug . '.' . $ext;
            if (file_exists($filePath)) {
                return [
                    'path' => $filePath,
                    'type' => $ext
                ];
            }
        }

        return null;
    }

    /**
     * 重写update方法以处理slug变更时的文件重命名
     */
    public function update($id, $data) {
        // 获取当前工具信息
        $currentTool = $this->find($id);
        if (!$currentTool) {
            return null;
        }

        $oldSlug = $currentTool['slug'];
        $newSlug = $data['slug'] ?? $oldSlug;

        // 如果slug发生变化，需要重命名物理文件
        if ($oldSlug !== $newSlug) {
            $oldFileInfo = $this->findToolFile($oldSlug);

            if ($oldFileInfo) {
                $fileType = $oldFileInfo['type'];
                $oldFilePath = $oldFileInfo['path'];
                $newFilePath = ROOT_PATH . '/templates/pages/tools/' . $newSlug . '.' . $fileType;

                // 重命名文件
                if (!rename($oldFilePath, $newFilePath)) {
                    throw new Exception("Failed to rename tool file from {$oldSlug}.{$fileType} to {$newSlug}.{$fileType}");
                }

                // 更新数据库中的file_type字段
                $data['file_type'] = $fileType;
            }
        }

        // 调用父类的update方法
        return parent::update($id, $data);
    }

    /**
     * 重写delete方法以删除对应的物理文件
     */
    public function delete($id) {
        // 获取当前工具信息
        $tool = $this->find($id);
        if ($tool) {
            // 查找并删除对应的物理文件
            $fileInfo = $this->findToolFile($tool['slug']);
            if ($fileInfo) {
                unlink($fileInfo['path']);
            }
        }

        // 调用父类的delete方法
        return parent::delete($id);
    }

    /**
     * 增加使用次数
     */
    public function incrementUsageCount($id) {
        $sql = "UPDATE {$this->table} SET usage_count = usage_count + 1 WHERE id = :id";
        return $this->db->query($sql, ['id' => $id]);
    }
    
    /**
     * 更新评分
     */
    public function updateRating($id, $rating) {
        $tool = $this->find($id);
        if (!$tool) {
            return false;
        }
        
        $currentRating = $tool['rating'] ?: 0;
        $currentCount = $tool['rating_count'] ?: 0;
        
        // 计算新的平均评分
        $newCount = $currentCount + 1;
        $newRating = (($currentRating * $currentCount) + $rating) / $newCount;
        
        return $this->update($id, [
            'rating' => round($newRating, 2),
            'rating_count' => $newCount
        ]);
    }
    
    /**
     * 获取工具统计信息
     */
    public function getStatistics() {
        $stats = [
            'total' => $this->count(),
            'published' => $this->count(['status' => 'published']),
            'draft' => $this->count(['status' => 'draft']),
            'featured' => $this->count(['featured' => 1]),
            'premium' => $this->count(['premium' => 1]),
            'by_category' => [],
            'by_difficulty' => [],
            'total_usage' => 0,
            'avg_rating' => 0
        ];
        
        // 按分类统计
        $sql = "
            SELECT c.name, COUNT(t.id) as count
            FROM pt_tool_category c
            LEFT JOIN {$this->table} t ON c.id = t.category_id
            GROUP BY c.id, c.name
            ORDER BY count DESC
        ";
        $categoryStats = $this->db->fetchAll($sql);
        foreach ($categoryStats as $stat) {
            $stats['by_category'][$stat['name']] = $stat['count'];
        }
        
        // 按难度统计
        $sql = "SELECT difficulty_level, COUNT(*) as count FROM {$this->table} GROUP BY difficulty_level";
        $difficultyStats = $this->db->fetchAll($sql);
        foreach ($difficultyStats as $stat) {
            $stats['by_difficulty'][$stat['difficulty_level']] = $stat['count'];
        }
        
        // 总使用次数
        $stats['total_usage'] = $this->sum('usage_count') ?: 0;
        
        // 平均评分
        $stats['avg_rating'] = round($this->avg('rating') ?: 0, 2);
        
        return $stats;
    }
    
    /**
     * 获取工具使用趋势
     */
    public function getUsageTrend($days = 30) {
        $sql = "
            SELECT DATE(u.created_at) as date, COUNT(*) as usage_count
            FROM tool_usage u
            WHERE u.created_at >= DATE_SUB(NOW(), INTERVAL :days DAY)
            GROUP BY DATE(u.created_at)
            ORDER BY date ASC
        ";
        
        return $this->db->fetchAll($sql, ['days' => $days]);
    }
    
    /**
     * 获取工具使用排行
     */
    public function getUsageRanking($limit = 20, $days = null) {
        $sql = "
            SELECT t.id, t.name, t.slug, c.name as category_name,
                   COUNT(u.id) as usage_count
            FROM {$this->table} t
            LEFT JOIN pt_tool_category c ON t.category_id = c.id
            LEFT JOIN pt_tool_usage u ON t.id = u.tool_id
        ";
        
        $params = [];
        if ($days) {
            $sql .= " WHERE u.created_at >= DATE_SUB(NOW(), INTERVAL :days DAY)";
            $params['days'] = $days;
        }
        
        $sql .= "
            GROUP BY t.id
            ORDER BY usage_count DESC
            LIMIT :limit
        ";
        $params['limit'] = $limit;
        
        return $this->db->fetchAll($sql, $params);
    }
    
    /**
     * 检查slug唯一性
     */
    public function isSlugUnique($slug, $excludeId = null) {
        $conditions = ['slug' => $slug];
        
        if ($excludeId) {
            $sql = "SELECT COUNT(*) FROM {$this->table} WHERE slug = :slug AND id != :id";
            return $this->db->fetchColumn($sql, ['slug' => $slug, 'id' => $excludeId]) == 0;
        }
        
        return !$this->exists($conditions);
    }
    
    /**
     * 生成唯一slug
     */
    public function generateUniqueSlug($name, $excludeId = null) {
        $slug = $this->slugify($name);
        $originalSlug = $slug;
        $counter = 1;
        
        while (!$this->isSlugUnique($slug, $excludeId)) {
            $slug = $originalSlug . '-' . $counter;
            $counter++;
        }
        
        return $slug;
    }
    
    /**
     * 将字符串转换为slug
     */
    private function slugify($text) {
        // 转换为小写
        $text = strtolower($text);
        
        // 替换非字母数字字符为连字符
        $text = preg_replace('/[^a-z0-9]+/', '-', $text);
        
        // 移除开头和结尾的连字符
        $text = trim($text, '-');
        
        return $text;
    }
    
    /**
     * 批量更新状态
     */
    public function batchUpdateStatus($ids, $status) {
        if (empty($ids)) {
            return 0;
        }
        
        $data = ['status' => $status];
        if ($status === 'published') {
            $data['published_at'] = date('Y-m-d H:i:s');
        }
        
        $placeholders = implode(',', array_fill(0, count($ids), '?'));
        $sql = "UPDATE {$this->table} SET status = ?, updated_at = NOW()";
        
        if ($status === 'published') {
            $sql .= ", published_at = NOW()";
        }
        
        $sql .= " WHERE id IN ({$placeholders})";
        
        $params = array_merge([$status], $ids);
        $stmt = $this->db->query($sql, $params);
        
        return $stmt->rowCount();
    }
    
    /**
     * 批量设置特色状态
     */
    public function batchSetFeatured($ids, $featured = true) {
        if (empty($ids)) {
            return 0;
        }
        
        $placeholders = implode(',', array_fill(0, count($ids), '?'));
        $sql = "UPDATE {$this->table} SET featured = ?, updated_at = NOW() WHERE id IN ({$placeholders})";
        
        $params = array_merge([$featured ? 1 : 0], $ids);
        $stmt = $this->db->query($sql, $params);
        
        return $stmt->rowCount();
    }
    
    /**
     * 获取相关工具
     */
    public function getRelatedTools($toolId, $limit = 5) {
        $tool = $this->find($toolId);
        if (!$tool) {
            return [];
        }
        
        $sql = "
            SELECT t.*, c.name as category_name
            FROM {$this->table} t
            LEFT JOIN pt_tool_category c ON t.category_id = c.id
            WHERE t.id != :tool_id 
            AND t.status = 'published'
            AND (t.category_id = :category_id OR t.tags LIKE :tags)
            ORDER BY 
                CASE WHEN t.category_id = :category_id THEN 1 ELSE 2 END,
                t.usage_count DESC
            LIMIT :limit
        ";
        
        return $this->db->fetchAll($sql, [
            'tool_id' => $toolId,
            'category_id' => $tool['category_id'],
            'tags' => "%{$tool['tags']}%",
            'limit' => $limit
        ]);
    }
    
    /**
     * 记录工具使用
     */
    public function recordUsage($toolId, $userId = null, $ipAddress = null) {
        $usageData = [
            'tool_id' => $toolId,
            'user_id' => $userId,
            'ip_address' => $ipAddress,
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        // 插入使用记录
        $this->db->insert('pt_tool_usage', $usageData);
        
        // 增加工具使用计数
        $this->incrementUsageCount($toolId);
    }
}
?>
