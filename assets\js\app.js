/**
 * AI Tools Platform - 主应用JavaScript文件
 * 基于原生JavaScript，无框架依赖
 */

// 主应用类
class App {
    constructor() {
        this.components = new Map();
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.initializeComponents();
        this.setupMobileHandlers();
    }

    setupEventListeners() {
        // DOM加载完成事件
        document.addEventListener('DOMContentLoaded', () => {
            this.onDOMReady();
        });

        // 页面可见性变化
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.onPageHidden();
            } else {
                this.onPageVisible();
            }
        });

        // 窗口大小变化
        window.addEventListener('resize', this.debounce(() => {
            this.onWindowResize();
        }, 250));

        // 页面滚动
        window.addEventListener('scroll', this.throttle(() => {
            this.onPageScroll();
        }, 16));
    }

    initializeComponents() {
        // 初始化导航组件
        if (typeof Navigation !== 'undefined') {
            this.components.set('navigation', new Navigation());
        }

        // 初始化工具管理器
        if (typeof ToolManager !== 'undefined') {
            this.components.set('toolManager', new ToolManager());
        }

        // 初始化模态框组件
        if (typeof Modal !== 'undefined') {
            this.components.set('modal', new Modal());
        }

        // 初始化表单处理器
        if (typeof FormHandler !== 'undefined') {
            this.components.set('formHandler', new FormHandler());
        }

        // 初始化认证组件
        if (typeof AuthManager !== 'undefined') {
            this.components.set('authManager', new AuthManager());
        }

        // 初始化UI组件
        this.initUIComponents();
    }

    initUIComponents() {
        // 初始化所有按钮
        this.initButtons();
        
        // 初始化表单
        this.initForms();
        
        // 初始化模态框
        this.initModals();
        
        // 初始化工具提示
        this.initTooltips();
    }

    initButtons() {
        document.querySelectorAll('.btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.handleButtonClick(e);
            });
        });
    }

    initForms() {
        document.querySelectorAll('form').forEach(form => {
            form.addEventListener('submit', (e) => {
                this.handleFormSubmit(e);
            });
        });
    }

    initModals() {
        document.querySelectorAll('[data-modal]').forEach(trigger => {
            trigger.addEventListener('click', (e) => {
                e.preventDefault();
                const modalId = trigger.dataset.modal;
                this.openModal(modalId);
            });
        });
    }

    initTooltips() {
        document.querySelectorAll('[data-tooltip]').forEach(element => {
            element.addEventListener('mouseenter', (e) => {
                this.showTooltip(e.target);
            });
            
            element.addEventListener('mouseleave', (e) => {
                this.hideTooltip(e.target);
            });
        });
    }

    setupMobileHandlers() {
        if (this.isMobile()) {
            document.body.classList.add('mobile');
            
            // 移动端触摸事件
            document.addEventListener('touchstart', (e) => {
                this.onTouchStart(e);
            });
            
            // 禁用双击缩放
            document.addEventListener('touchend', (e) => {
                if (e.target.tagName !== 'INPUT' && e.target.tagName !== 'TEXTAREA') {
                    e.preventDefault();
                }
            });
        }
    }



    // 事件处理方法
    onDOMReady() {
        document.body.classList.add('loaded');
    }

    onPageHidden() {
        // Page hidden event
    }

    onPageVisible() {
        // Page visible event
    }

    onWindowResize() {
        const width = window.innerWidth;

        // 更新移动端状态
        if (width <= 768) {
            document.body.classList.add('mobile');
        } else {
            document.body.classList.remove('mobile');
        }
    }

    onPageScroll() {
        const scrollY = window.scrollY;
        
        // 导航栏滚动效果
        const navbar = document.querySelector('.navbar');
        if (navbar) {
            if (scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        }
    }

    onTouchStart() {
        // 移动端触摸处理
    }

    // UI交互方法
    handleButtonClick(e) {
        const button = e.target.closest('.btn');
        if (!button) return;

        // 添加点击动画
        button.classList.add('clicked');
        setTimeout(() => {
            button.classList.remove('clicked');
        }, 150);


    }

    handleFormSubmit() {
        // 表单提交处理
    }

    openModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.add('active');
            document.body.classList.add('modal-open');

        }
    }

    closeModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.remove('active');
            document.body.classList.remove('modal-open');

        }
    }

    showTooltip(element) {
        const text = element.dataset.tooltip;
        if (!text) return;

        const tooltip = document.createElement('div');
        tooltip.className = 'tooltip';
        tooltip.textContent = text;
        document.body.appendChild(tooltip);

        // 定位工具提示
        const rect = element.getBoundingClientRect();
        tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
        tooltip.style.top = rect.top - tooltip.offsetHeight - 8 + 'px';
    }

    hideTooltip() {
        const tooltip = document.querySelector('.tooltip');
        if (tooltip) {
            tooltip.remove();
        }
    }

    // 工具方法
    isMobile() {
        return window.innerWidth <= 768 || /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    }

    getBrowserInfo() {
        const ua = navigator.userAgent;
        if (ua.includes('Chrome')) return 'Chrome';
        if (ua.includes('Firefox')) return 'Firefox';
        if (ua.includes('Safari')) return 'Safari';
        if (ua.includes('Edge')) return 'Edge';
        return 'Unknown';
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }






}

// 启动应用
const app = new App();

// 全局暴露app实例
window.App = app;
