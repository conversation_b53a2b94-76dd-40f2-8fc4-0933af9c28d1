<?php
/**
 * 工具浏览量统计API
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

// 获取POST数据
$input = json_decode(file_get_contents('php://input'), true);
$toolSlug = $input['tool_slug'] ?? '';

if (empty($toolSlug)) {
    http_response_code(400);
    echo json_encode(['error' => 'Tool slug is required']);
    exit;
}

try {
    // 使用统一的数据库连接
    require_once dirname(__DIR__) . '/includes/database-connection.php';

    // 启动会话以获取用户ID
    session_start();
    $userId = $_SESSION['user_id'] ?? null;

    // 获取工具ID
    $stmt = $pdo->prepare("SELECT id FROM pt_tool WHERE slug = ?");
    $stmt->execute([$toolSlug]);
    $tool = $stmt->fetch();

    if (!$tool) {
        echo json_encode(['success' => false, 'message' => 'Tool not found']);
        exit;
    }

    $toolId = $tool['id'];

    // 更新工具浏览量
    $stmt = $pdo->prepare("UPDATE pt_tool SET view_count = view_count + 1 WHERE id = ?");
    $stmt->execute([$toolId]);

    // 如果用户已登录，记录工具使用情况
    if ($userId) {
        $stmt = $pdo->prepare("
            INSERT INTO pt_tool_usage (tool_id, user_id, usage_type, status, ip_address, user_agent, used_at)
            VALUES (?, ?, 'web_usage', 'success', ?, ?, NOW())
        ");
        $stmt->execute([
            $toolId,
            $userId,
            $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1',
            $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown'
        ]);
    }

    echo json_encode(['success' => true, 'message' => 'View tracked successfully']);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Database error: ' . $e->getMessage()]);
}
?>
