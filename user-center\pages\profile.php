<?php
/**
 * 用户中心个人资料页面
 */



// 判断登录方式
$isGoogleUser = empty($user['password']);
$loginMethod = $isGoogleUser ? 'Google' : 'Email';
?>

<!-- 页面标题 -->
<div class="mb-8">
    <h1 class="text-3xl font-bold text-gray-900">Profile Settings</h1>
    <p class="text-gray-600 mt-2">Manage your account information and preferences</p>
</div>

<!-- 成功/错误消息 -->
<div id="message-container" class="mb-6 hidden">
    <div id="message" class="p-4 border"></div>
</div>

<!-- 主要内容区域 -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
    <!-- 左侧：基本信息 -->
    <div class="lg:col-span-2 space-y-8">
        <!-- 基本信息卡片 -->
        <div class="replicate-card">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Basic Information</h3>
                <p class="text-sm text-gray-500 mt-1">Update your personal details</p>
            </div>
            <div class="p-6">
                <form id="profile-form" class="space-y-6">
                    <input type="hidden" name="action" value="update_profile">
                    
                    <!-- 头像区域 -->
                    <div class="flex items-center space-x-6">
                        <div class="relative">
                            <?php if (!empty($user['avatar'])): ?>
                                <img src="<?= htmlspecialchars($user['avatar']) ?>"
                                     alt="Profile Photo"
                                     class="w-20 h-20 object-cover border border-gray-300"
                                     id="avatar-preview">
                            <?php else: ?>
                                <div class="w-20 h-20 bg-black text-white flex items-center justify-center font-bold text-2xl" id="avatar-preview">
                                    <?= strtoupper(substr($user['first_name'] ?? $user['username'] ?? 'U', 0, 1)) ?>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div>
                            <h4 class="text-sm font-medium text-gray-900">Profile Photo</h4>
                            <p class="text-sm text-gray-500">Upload a new avatar for your profile</p>
                            <input type="file" id="avatar-upload" accept="image/*" class="hidden">
                            <button type="button" onclick="document.getElementById('avatar-upload').click()"
                                    class="mt-2 replicate-btn replicate-btn-secondary text-sm">
                                Change Photo
                            </button>
                        </div>
                    </div>
                    
                    <!-- 用户名 -->
                    <div>
                        <label for="username" class="block text-sm font-medium text-gray-700 mb-2">Username</label>
                        <input type="text" id="username" name="username" maxlength="50"
                               value="<?= htmlspecialchars($user['username']) ?>"
                               class="w-full px-3 py-2 border border-gray-300 bg-white text-sm focus:outline-none focus:border-black"
                               required>
                        <p class="text-xs text-gray-500 mt-1">Maximum 50 characters</p>
                    </div>
                    
                    <!-- 名字和姓氏 -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="first_name" class="block text-sm font-medium text-gray-700 mb-2">First Name</label>
                            <input type="text" id="first_name" name="first_name" maxlength="50"
                                   value="<?= htmlspecialchars($user['first_name'] ?? '') ?>"
                                   class="w-full px-3 py-2 border border-gray-300 bg-white text-sm focus:outline-none focus:border-black">
                            <p class="text-xs text-gray-500 mt-1">Maximum 50 characters</p>
                        </div>
                        <div>
                            <label for="last_name" class="block text-sm font-medium text-gray-700 mb-2">Last Name</label>
                            <input type="text" id="last_name" name="last_name" maxlength="50"
                                   value="<?= htmlspecialchars($user['last_name'] ?? '') ?>"
                                   class="w-full px-3 py-2 border border-gray-300 bg-white text-sm focus:outline-none focus:border-black">
                            <p class="text-xs text-gray-500 mt-1">Maximum 50 characters</p>
                        </div>
                    </div>
                    
                    <!-- 邮箱（只读） -->
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
                        <div class="flex items-center space-x-3">
                            <input type="email" id="email" 
                                   value="<?= htmlspecialchars($user['email']) ?>"
                                   class="flex-1 px-3 py-2 border border-gray-300 bg-gray-50 text-sm text-gray-500"
                                   readonly>
                            <?php if ($user['email_verified']): ?>
                                <span class="replicate-badge replicate-badge-success">Verified</span>
                            <?php else: ?>
                                <button type="button" onclick="resendVerification()" 
                                        class="replicate-btn replicate-btn-accent text-sm">
                                    Verify Email
                                </button>
                            <?php endif; ?>
                        </div>
                        <p class="text-xs text-gray-500 mt-1">Email cannot be changed as it's your unique identifier</p>
                    </div>
                    
                    <!-- 个人简介 -->
                    <div>
                        <label for="bio" class="block text-sm font-medium text-gray-700 mb-2">Bio</label>
                        <textarea id="bio" name="bio" rows="4" maxlength="200"
                                  class="w-full px-3 py-2 border border-gray-300 bg-white text-sm focus:outline-none focus:border-black"
                                  placeholder="Tell us about yourself..."><?= htmlspecialchars($user['bio'] ?? '') ?></textarea>
                        <div class="flex justify-between items-center mt-1">
                            <p class="text-xs text-gray-500">Brief description about yourself</p>
                            <span class="text-xs text-gray-400" id="bio-counter">
                                <span id="bio-count"><?= strlen($user['bio'] ?? '') ?></span>/200
                            </span>
                        </div>
                    </div>
                    
                    <!-- 提交按钮 -->
                    <div class="flex justify-end">
                        <button type="submit" class="replicate-btn replicate-btn-primary">
                            Save Changes
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- 安全设置卡片 -->
        <div class="replicate-card">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Security Settings</h3>
                <p class="text-sm text-gray-500 mt-1">Manage your account security</p>
            </div>
            <div class="p-6">
                <!-- 登录方式显示 -->
                <div class="mb-6">
                    <h4 class="text-sm font-medium text-gray-900 mb-2">Login Method</h4>
                    <div class="flex items-center space-x-3">
                        <?php if ($isGoogleUser): ?>
                            <div class="flex items-center space-x-2">
                                <svg class="w-5 h-5 text-red-500" viewBox="0 0 24 24">
                                    <path fill="currentColor" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                                    <path fill="currentColor" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                                    <path fill="currentColor" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                                    <path fill="currentColor" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                                </svg>
                                <span class="text-sm text-gray-700">Google</span>
                                <span class="replicate-badge replicate-badge-info">Connected</span>
                            </div>
                            <p class="text-xs text-gray-500 mt-1">Managed through Google. Change password in your Google account.</p>
                        <?php else: ?>
                            <div class="flex items-center space-x-2">
                                <svg class="w-5 h-5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                </svg>
                                <span class="text-sm text-gray-700">Email & Password</span>
                                <span class="replicate-badge replicate-badge-success">Active</span>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- 密码修改（仅邮箱用户） -->
                <?php if (!$isGoogleUser): ?>
                <div class="border-t border-gray-200 pt-6">
                    <h4 class="text-sm font-medium text-gray-900 mb-4">Change Password</h4>
                    <form id="password-form" class="space-y-4">
                        <input type="hidden" name="action" value="change_password">

                        <div>
                            <label for="current_password" class="block text-sm font-medium text-gray-700 mb-2">Current Password</label>
                            <input type="password" id="current_password" name="current_password"
                                   class="w-full px-3 py-2 border border-gray-300 bg-white text-sm focus:outline-none focus:border-black"
                                   required>
                        </div>

                        <div>
                            <label for="new_password" class="block text-sm font-medium text-gray-700 mb-2">New Password</label>
                            <div class="flex space-x-2">
                                <input type="password" id="new_password" name="new_password"
                                       class="flex-1 px-3 py-2 border border-gray-300 bg-white text-sm focus:outline-none focus:border-black"
                                       minlength="8" required>
                                <button type="button" onclick="generateRandomPassword()"
                                        class="replicate-btn replicate-btn-secondary text-sm px-3">
                                    Generate
                                </button>
                                <button type="button" onclick="togglePasswordVisibility('new_password')"
                                        class="px-3 py-2 border border-gray-300 bg-white text-gray-600 hover:text-gray-800">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                    </svg>
                                </button>
                            </div>
                            <p class="text-xs text-gray-500 mt-1">Must be at least 8 characters long</p>
                        </div>

                        <div>
                            <label for="confirm_password" class="block text-sm font-medium text-gray-700 mb-2">Confirm New Password</label>
                            <div class="flex space-x-2">
                                <input type="password" id="confirm_password" name="confirm_password"
                                       class="flex-1 px-3 py-2 border border-gray-300 bg-white text-sm focus:outline-none focus:border-black"
                                       required>
                                <button type="button" onclick="togglePasswordVisibility('confirm_password')"
                                        class="px-3 py-2 border border-gray-300 bg-white text-gray-600 hover:text-gray-800">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>

                        <div class="flex justify-end">
                            <button type="submit" class="replicate-btn replicate-btn-primary">
                                Change Password
                            </button>
                        </div>
                    </form>
                </div>
                <?php endif; ?>
            </div>
        </div>


    </div>

    <!-- 右侧：账户信息 -->
    <div class="space-y-6">
        <!-- 账户状态卡片 -->
        <div class="replicate-card">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Account Status</h3>
            </div>
            <div class="p-6 space-y-4">
                <!-- 订阅类型 -->
                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-600">Subscription</span>
                    <span class="replicate-badge replicate-badge-<?= $user['subscription_type'] === 'free' ? 'info' : 'success' ?>">
                        <?= ucfirst($user['subscription_type']) ?>
                    </span>
                </div>

                <!-- 账户状态 -->
                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-600">Status</span>
                    <span class="replicate-badge replicate-badge-<?= $user['status'] === 'active' ? 'success' : 'warning' ?>">
                        <?= ucfirst($user['status']) ?>
                    </span>
                </div>

                <!-- 邮箱验证状态 -->
                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-600">Email Verified</span>
                    <span class="replicate-badge replicate-badge-<?= $user['email_verified'] ? 'success' : 'danger' ?>">
                        <?= $user['email_verified'] ? 'Yes' : 'No' ?>
                    </span>
                </div>

                <!-- 注册时间 -->
                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-600">Member Since</span>
                    <span class="text-sm text-gray-900">
                        <?= date('M j, Y', strtotime($user['created_at'])) ?>
                    </span>
                </div>

                <!-- 最后登录 -->
                <?php if ($user['last_login']): ?>
                <div class="flex justify-between items-center">
                    <span class="text-sm text-gray-600">Last Login</span>
                    <span class="text-sm text-gray-900">
                        <?= timeAgo($user['last_login']) ?>
                    </span>
                </div>
                <?php endif; ?>
            </div>
        </div>





        <!-- 危险操作区域 -->
        <div class="bg-white border border-red-200 p-6">
            <div class="flex justify-between items-center">
                <div>
                    <h3 class="text-lg font-medium text-gray-900">Delete Account</h3>
                    <p class="text-sm text-gray-500 mt-1">
                        Permanently delete account and data. Cannot be undone.
                    </p>
                </div>
                <button type="button" onclick="showDeleteAccountModal()"
                        class="bg-red-600 text-white px-4 py-2 text-sm font-medium hover:bg-red-700 transition-colors">
                    Delete
                </button>
            </div>
        </div>
    </div>
</div>



<!-- 删除账户确认模态框 -->
<div id="delete-account-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white max-w-md w-full p-6">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold text-red-600">Delete Account</h3>
                <button type="button" onclick="hideDeleteAccountModal()" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <div class="mb-6">
                <div class="bg-red-50 border border-red-200 p-4 mb-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-red-800">Warning</h3>
                            <div class="mt-2 text-sm text-red-700">
                                <p>This action will permanently delete your account and all associated data including:</p>
                                <ul class="list-disc list-inside mt-2">
                                    <li>Profile information</li>
                                    <li>API keys and usage history</li>
                                    <li>All account settings</li>
                                </ul>
                                <p class="mt-2 font-medium">This action cannot be undone.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <form id="delete-account-form" class="space-y-4">
                    <div>
                        <label for="confirm-email" class="block text-sm font-medium text-gray-700 mb-2">
                            Type your email address to confirm: <span class="font-mono text-sm"><?= htmlspecialchars($user['email']) ?></span>
                        </label>
                        <input type="email" id="confirm-email" name="confirm_email"
                               class="w-full px-3 py-2 border border-gray-300 bg-white text-sm focus:outline-none focus:border-red-500"
                               placeholder="Enter your email address" required>
                    </div>

                    <div class="flex space-x-3">
                        <button type="button" onclick="hideDeleteAccountModal()"
                                class="flex-1 bg-gray-200 text-gray-800 py-2 px-4 text-sm font-medium hover:bg-gray-300 transition-colors">
                            Cancel
                        </button>
                        <button type="submit"
                                class="flex-1 bg-red-600 text-white py-2 px-4 text-sm font-medium hover:bg-red-700 transition-colors">
                            Delete Account
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript功能 -->
<script>
// 显示消息
function showMessage(message, type = 'success') {
    const container = document.getElementById('message-container');
    const messageDiv = document.getElementById('message');

    container.classList.remove('hidden');
    messageDiv.className = `p-4 border ${type === 'success' ? 'bg-green-50 border-green-200 text-green-800' : 'bg-red-50 border-red-200 text-red-800'}`;
    messageDiv.textContent = message;

    // 3秒后自动隐藏
    setTimeout(() => {
        container.classList.add('hidden');
    }, 3000);
}

// 处理个人资料表单提交
document.getElementById('profile-form').addEventListener('submit', async function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    const button = this.querySelector('button[type="submit"]');
    const originalText = button.textContent;

    button.textContent = 'Saving...';
    button.disabled = true;

    try {
        const response = await fetch('ajax/profile-actions.php', {
            method: 'POST',
            body: formData
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();

        if (result.success) {
            // 显示成功状态 - 延长显示时间让用户看清楚
            button.className = 'replicate-btn btn-success';
            button.textContent = '✓ Saved Successfully!';
            button.disabled = true;

            showMessage(result.message, 'success');

            // 3秒后恢复原状
            setTimeout(() => {
                button.className = 'replicate-btn replicate-btn-primary';
                button.textContent = originalText;
                button.disabled = false;
            }, 3000);
        } else {
            // 显示错误状态
            button.className = 'replicate-btn btn-error';
            button.textContent = '✗ Save Failed!';
            button.disabled = true;

            showMessage(result.message, 'error');

            // 3秒后恢复原状
            setTimeout(() => {
                button.className = 'replicate-btn replicate-btn-primary';
                button.textContent = originalText;
                button.disabled = false;
            }, 3000);
        }
    } catch (error) {
        showMessage('An error occurred while saving your profile', 'error');
        button.textContent = originalText;
        button.disabled = false;
    }
});

// 处理密码修改表单提交
<?php if (!$isGoogleUser): ?>
document.getElementById('password-form').addEventListener('submit', async function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    const button = this.querySelector('button[type="submit"]');
    const originalText = button.textContent;

    // 验证密码匹配
    const newPassword = formData.get('new_password');
    const confirmPassword = formData.get('confirm_password');

    if (newPassword !== confirmPassword) {
        showMessage('New passwords do not match', 'error');
        return;
    }

    button.textContent = 'Changing...';
    button.disabled = true;

    try {
        const response = await fetch('ajax/profile-actions.php', {
            method: 'POST',
            body: formData
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();

        if (result.success) {
            // 显示成功状态 - 延长显示时间
            button.className = 'replicate-btn btn-success';
            button.textContent = '✓ Password Changed!';
            button.disabled = true;

            showMessage(result.message, 'success');
            this.reset(); // 清空表单

            // 3秒后恢复原状
            setTimeout(() => {
                button.className = 'replicate-btn replicate-btn-primary';
                button.textContent = originalText;
                button.disabled = false;
            }, 3000);
        } else {
            // 显示错误状态
            button.className = 'replicate-btn btn-error';
            button.textContent = '✗ Change Failed!';
            button.disabled = true;

            showMessage(result.message, 'error');

            // 3秒后恢复原状
            setTimeout(() => {
                button.className = 'replicate-btn replicate-btn-primary';
                button.textContent = originalText;
                button.disabled = false;
            }, 3000);
        }
    } catch (error) {

        // 显示网络错误状态
        button.className = 'replicate-btn btn-error';
        button.textContent = '✗ Network Error!';
        button.disabled = true;

        showMessage('Network error occurred while changing password', 'error');

        // 3秒后恢复原状
        setTimeout(() => {
            button.className = 'replicate-btn replicate-btn-primary';
            button.textContent = originalText;
            button.disabled = false;
        }, 3000);
    }
});
<?php endif; ?>

// 重新发送邮箱验证
async function resendVerification() {
    const button = event.target;
    const originalText = button.textContent;

    button.textContent = 'Sending...';
    button.disabled = true;

    try {
        const formData = new FormData();
        formData.append('action', 'resend_verification');

        const response = await fetch('ajax/profile-actions.php', {
            method: 'POST',
            body: formData
        });

        const result = await response.json();

        if (result.success) {
            showMessage(result.message, 'success');
        } else {
            showMessage(result.message, 'error');
        }
    } catch (error) {
        showMessage('An error occurred while sending verification email', 'error');
    } finally {
        button.textContent = originalText;
        button.disabled = false;
    }
}

// 头像上传处理
document.getElementById('avatar-upload').addEventListener('change', async function(e) {
    const file = e.target.files[0];
    if (!file) return;

    // 验证文件类型
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
        showMessage('Invalid file type. Only JPEG, PNG, GIF, and WebP are allowed', 'error');
        return;
    }

    // 验证文件大小 (5MB)
    if (file.size > 5 * 1024 * 1024) {
        showMessage('File too large. Maximum size is 5MB', 'error');
        return;
    }

    const formData = new FormData();
    formData.append('action', 'upload_avatar');
    formData.append('avatar', file);

    // 显示上传中状态
    const changePhotoBtn = document.querySelector('button[onclick="document.getElementById(\'avatar-upload\').click()"]');
    const originalBtnText = changePhotoBtn.textContent;
    changePhotoBtn.className = 'replicate-btn btn-loading';
    changePhotoBtn.textContent = 'Uploading...';
    changePhotoBtn.disabled = true;

    try {
        const response = await fetch('ajax/profile-actions.php', {
            method: 'POST',
            body: formData
        });

        const result = await response.json();

        if (result.success) {
            // 更新头像预览
            const preview = document.getElementById('avatar-preview');
            if (result.avatar_url) {
                preview.innerHTML = `<img src="${result.avatar_url}" alt="Profile Photo" class="w-20 h-20 object-cover border border-gray-300">`;

                // 更新右上角头像
                updateHeaderAvatar(result.avatar_url);
            }
            showMessage(result.message, 'success');

            // 显示成功状态 - 延长显示时间
            changePhotoBtn.classList.remove('replicate-btn-secondary');
            changePhotoBtn.classList.add('bg-green-600', 'border-green-600', 'text-white');
            changePhotoBtn.textContent = '✓ Avatar Uploaded!';

            setTimeout(() => {
                changePhotoBtn.classList.remove('bg-green-600', 'border-green-600', 'text-white');
                changePhotoBtn.classList.add('replicate-btn-secondary');
                changePhotoBtn.textContent = originalBtnText;
                changePhotoBtn.disabled = false;
            }, 3000);
        } else {
            showMessage(result.message, 'error');

            // 显示错误状态
            changePhotoBtn.classList.remove('replicate-btn-secondary');
            changePhotoBtn.classList.add('bg-red-600', 'border-red-600', 'text-white');
            changePhotoBtn.textContent = '✗ Upload Failed!';

            setTimeout(() => {
                changePhotoBtn.classList.remove('bg-red-600', 'border-red-600', 'text-white');
                changePhotoBtn.classList.add('replicate-btn-secondary');
                changePhotoBtn.textContent = originalBtnText;
                changePhotoBtn.disabled = false;
            }, 3000);
        }
    } catch (error) {
        showMessage('An error occurred while uploading avatar', 'error');

        // 恢复按钮状态
        changePhotoBtn.textContent = originalBtnText;
        changePhotoBtn.disabled = false;
    }

    // 清空文件输入
    e.target.value = '';
});









// 更新右上角头像
function updateHeaderAvatar(avatarUrl) {
    const headerAvatar = document.getElementById('header-avatar');
    if (headerAvatar) {
        headerAvatar.outerHTML = `<img src="${avatarUrl}" alt="Profile Photo" class="w-8 h-8 object-cover border border-gray-300" id="header-avatar">`;
    }
}









// 生成随机密码
function generateRandomPassword() {
    // 密码字符集：大小写字母、数字、特殊字符
    const lowercase = 'abcdefghijklmnopqrstuvwxyz';
    const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const numbers = '0123456789';
    const symbols = '!@#$%^&*()_+-=[]{}|;:,.<>?';

    // 确保密码包含各种字符类型
    let password = '';

    // 至少包含一个小写字母
    password += lowercase[Math.floor(Math.random() * lowercase.length)];

    // 至少包含一个大写字母
    password += uppercase[Math.floor(Math.random() * uppercase.length)];

    // 至少包含一个数字
    password += numbers[Math.floor(Math.random() * numbers.length)];

    // 至少包含一个特殊字符
    password += symbols[Math.floor(Math.random() * symbols.length)];

    // 填充剩余字符（总长度12位）
    const allChars = lowercase + uppercase + numbers + symbols;
    for (let i = 4; i < 12; i++) {
        password += allChars[Math.floor(Math.random() * allChars.length)];
    }

    // 打乱密码字符顺序
    password = password.split('').sort(() => Math.random() - 0.5).join('');

    // 设置到输入框
    const newPasswordInput = document.getElementById('new_password');
    const confirmPasswordInput = document.getElementById('confirm_password');

    newPasswordInput.value = password;
    confirmPasswordInput.value = password;

    // 临时显示密码
    const originalType1 = newPasswordInput.type;
    const originalType2 = confirmPasswordInput.type;

    newPasswordInput.type = 'text';
    confirmPasswordInput.type = 'text';

    // 显示成功消息
    showMessage('Random password generated and filled in both fields', 'success');

    // 3秒后隐藏密码
    setTimeout(() => {
        newPasswordInput.type = originalType1;
        confirmPasswordInput.type = originalType2;
    }, 3000);
}



// Bio字符计数
document.getElementById('bio').addEventListener('input', function() {
    const bioCount = document.getElementById('bio-count');
    const currentLength = this.value.length;
    bioCount.textContent = currentLength;

    // 接近限制时改变颜色
    const counter = document.getElementById('bio-counter');
    if (currentLength > 180) {
        counter.classList.add('text-red-500');
        counter.classList.remove('text-gray-400');
    } else if (currentLength > 150) {
        counter.classList.add('text-yellow-500');
        counter.classList.remove('text-gray-400', 'text-red-500');
    } else {
        counter.classList.add('text-gray-400');
        counter.classList.remove('text-red-500', 'text-yellow-500');
    }
});

// 切换密码显示/隐藏
function togglePasswordVisibility(inputId) {
    const input = document.getElementById(inputId);
    const button = event.target.closest('button');

    if (input.type === 'password') {
        input.type = 'text';
        button.innerHTML = `
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"></path>
            </svg>
        `;
    } else {
        input.type = 'password';
        button.innerHTML = `
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
            </svg>
        `;
    }
}



// 显示删除账户模态框
function showDeleteAccountModal() {
    document.getElementById('delete-account-modal').classList.remove('hidden');
    document.body.style.overflow = 'hidden';
}

// 隐藏删除账户模态框
function hideDeleteAccountModal() {
    document.getElementById('delete-account-modal').classList.add('hidden');
    document.body.style.overflow = 'auto';
    // 清空表单
    document.getElementById('delete-account-form').reset();
}

// 处理删除账户表单提交
document.getElementById('delete-account-form').addEventListener('submit', async function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    const button = this.querySelector('button[type="submit"]');
    const originalText = button.textContent;
    const confirmEmail = formData.get('confirm_email');
    const userEmail = '<?= htmlspecialchars($user['email']) ?>';

    // 验证邮箱匹配
    if (confirmEmail.toLowerCase() !== userEmail.toLowerCase()) {
        showMessage('Email address does not match. Please check and try again.', 'error');
        return;
    }

    button.className = 'flex-1 btn-loading';
    button.textContent = 'Deleting Account...';
    button.style.opacity = '1';
    button.disabled = true;

    try {
        formData.append('action', 'delete_account');

        const response = await fetch('ajax/profile-actions.php', {
            method: 'POST',
            body: formData
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();

        if (result.success) {
            // 显示成功状态
            button.className = 'flex-1 btn-success';
            button.textContent = '✓ Account Deleted';
            button.style.opacity = '1';

            showMessage('Account deleted successfully. Redirecting to homepage...', 'success');

            // 3秒后重定向到首页
            setTimeout(() => {
                window.location.replace('/');
            }, 3000);
        } else {
            // 显示错误状态
            button.className = 'flex-1 btn-error';
            button.textContent = '✗ Delete Failed';
            button.style.opacity = '1';

            showMessage(result.message || 'Failed to delete account', 'error');

            // 3秒后恢复原状
            setTimeout(() => {
                button.className = 'flex-1 bg-red-600 text-white py-2 px-4 text-sm font-medium hover:bg-red-700 transition-colors';
                button.textContent = originalText;
                button.style.opacity = '1';
                button.disabled = false;
            }, 3000);
        }
    } catch (error) {

        // 显示网络错误状态
        button.className = 'flex-1 btn-error';
        button.textContent = '✗ Network Error';
        button.style.opacity = '1';

        showMessage('Network error occurred while deleting account', 'error');

        // 3秒后恢复原状
        setTimeout(() => {
            button.className = 'flex-1 bg-red-600 text-white py-2 px-4 text-sm font-medium hover:bg-red-700 transition-colors';
            button.textContent = originalText;
            button.style.opacity = '1';
            button.disabled = false;
        }, 3000);
    }
});
</script>
