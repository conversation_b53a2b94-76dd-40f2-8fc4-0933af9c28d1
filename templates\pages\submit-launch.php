<?php
/**
 * 产品提交页面
 * 包含AI分析功能
 */

// 检查用户登录状态
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
if (!isset($_SESSION['user_id'])) {
    header('Location: /auth/login');
    exit;
}

// 使用统一的数据库连接
require_once ROOT_PATH . '/includes/database-connection.php';

// 获取分类数据
$categoryStmt = $pdo->query("SELECT slug, name, icon, color FROM pt_launch_categories WHERE is_active = 1 ORDER BY sort_order ASC, name ASC");
$categories = $categoryStmt->fetchAll();

// 获取技术分类数据
$techCategoryStmt = $pdo->query("SELECT slug, name FROM pt_tech_categories WHERE is_active = 1 ORDER BY sort_order ASC, name ASC");
$techCategories = $techCategoryStmt->fetchAll();

// 调试信息
error_log("Tech categories loaded: " . count($techCategories) . " items");
if (empty($techCategories)) {
    error_log("WARNING: No tech categories found in database!");
}

// 页面标题和描述
$pageTitle = "Submit Product Launch - AI Analysis - Prompt2Tool";
$pageDescription = "Submit your product launch with AI-powered analysis. Auto-fill forms from your website and get discovered by the Prompt2Tool community.";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($pageTitle) ?></title>
    <meta name="description" content="<?= htmlspecialchars($pageDescription) ?>">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom Styles -->
    <style>
        /* 自定义提示窗样式 */
        .custom-modal {
            backdrop-filter: blur(4px);
        }
        
        .custom-alert {
            animation: slideInDown 0.3s ease-out;
        }
        
        @keyframes slideInDown {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 文件上传区域样式 */
        .file-drop-zone {
            border: 2px dashed #4b5563;
            transition: all 0.3s ease;
        }
        
        .file-drop-zone.dragover {
            border-color: #3b82f6;
            background-color: rgba(59, 130, 246, 0.1);
        }

        /* 加载动画 */
        .loading-spinner {
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* 表单验证样式 */
        .form-error {
            border-color: #ef4444 !important;
        }
        
        .form-success {
            border-color: #10b981 !important;
        }

        /* 标签选择器样式 */
        .tag-selector {
            max-height: 200px;
            overflow-y: auto;
        }
        
        .tag-item {
            transition: all 0.2s ease;
        }
        
        .tag-item:hover {
            background-color: rgba(59, 130, 246, 0.1);
        }
        
        .tag-selected {
            background-color: rgba(59, 130, 246, 0.2);
            border-color: #3b82f6;
        }
    </style>
</head>

<body class="bg-gray-950 text-white min-h-screen">
    <!-- Cropper.js CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.5.12/cropper.min.css">

    <!-- 包含公共头部 -->
    <?php include ROOT_PATH . '/templates/layout/header.php'; ?>

    <main class="container mx-auto px-4 py-8">
        <!-- 页面标题 -->
        <div class="text-center mb-12">
            <h1 class="text-4xl md:text-5xl font-bold text-white mb-6">
                Submit Your Product
            </h1>
            <p class="text-xl text-gray-300 mb-6 max-w-3xl mx-auto">
                Submit your product launch to our community and get discovered by thousands of users
            </p>
        </div>

        <!-- 提交表单 -->
        <div class="max-w-4xl mx-auto">
            <form id="submit-launch-form" class="space-y-8">
                <!-- AI分析区域 -->
                <div class="bg-gradient-to-r from-blue-900/20 to-purple-900/20 border border-blue-500/30 rounded-xl p-6">
                    <div class="flex items-center mb-4">
                        <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-robot text-white"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-white">AI Website Analysis</h3>
                            <p class="text-sm text-gray-400">Let AI analyze your website and auto-fill the form</p>
                        </div>
                    </div>
                    
                    <div class="flex gap-3">
                        <div class="flex-1">
                            <input type="url" 
                                   id="website-url-input" 
                                   placeholder="https://your-website.com" 
                                   class="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-blue-500 transition-colors">
                        </div>
                        <button type="button"
                                id="ai-analyze-btn"
                                class="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white hover:text-white font-semibold rounded-lg transition-colors whitespace-nowrap">
                            <i class="fas fa-magic mr-2"></i>
                            AI Analyze
                        </button>
                    </div>
                    
                    <!-- AI分析结果显示区域 -->
                    <div id="ai-analysis-result" class="mt-4 hidden">
                        <div class="bg-gray-800/50 rounded-lg p-4 border border-gray-700">
                            <div class="flex items-center mb-2">
                                <i class="fas fa-check-circle text-green-400 mr-2"></i>
                                <span class="text-green-400 font-medium">Analysis Complete</span>
                            </div>
                            <p class="text-gray-300 text-sm mb-3">AI has analyzed your website and filled the form below. Please review and adjust as needed.</p>
                            <div id="analysis-summary" class="text-xs text-gray-400"></div>
                        </div>
                    </div>
                </div>

                <!-- 基础信息 -->
                <div class="bg-gray-900 rounded-xl p-6">
                    <h3 class="text-xl font-semibold text-white mb-6 flex items-center">
                        <i class="fas fa-info-circle mr-3 text-blue-400"></i>
                        Basic Information
                    </h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- 产品名称 -->
                        <div class="md:col-span-2">
                            <label for="product-name" class="block text-sm font-medium text-gray-300 mb-2">
                                Product Name <span class="text-red-400">*</span>
                            </label>
                            <input type="text" 
                                   id="product-name" 
                                   name="product_name" 
                                   required
                                   maxlength="100"
                                   class="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-blue-500 transition-colors"
                                   placeholder="Enter your product name">
                            <div class="text-xs text-gray-400 mt-1">Maximum 100 characters</div>
                        </div>

                        <!-- 网站URL -->
                        <div class="md:col-span-2">
                            <label for="website-url" class="block text-sm font-medium text-gray-300 mb-2">
                                Website URL <span class="text-red-400">*</span>
                            </label>
                            <input type="url" 
                                   id="website-url" 
                                   name="website_url" 
                                   required
                                   class="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-blue-500 transition-colors"
                                   placeholder="https://your-website.com">
                        </div>

                        <!-- 一句话描述 -->
                        <div class="md:col-span-2">
                            <label for="tagline" class="block text-sm font-medium text-gray-300 mb-2">
                                Tagline <span class="text-red-400">*</span>
                            </label>
                            <input type="text"
                                   id="tagline"
                                   name="tagline"
                                   required
                                   maxlength="150"
                                   class="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-blue-500 transition-colors"
                                   placeholder="A brief, compelling description of your product"
                                   oninput="updateCharCount('tagline', 'tagline-count', 150)">
                            <div class="flex justify-between text-xs text-gray-400 mt-1">
                                <span>Maximum 150 characters. This will be the first thing people see.</span>
                                <span id="tagline-count">0/150</span>
                            </div>
                        </div>

                        <!-- 详细描述 -->
                        <div class="md:col-span-2">
                            <label for="description" class="block text-sm font-medium text-gray-300 mb-2">
                                Description <span class="text-red-400">*</span>
                            </label>
                            <textarea id="description" 
                                      name="description" 
                                      required
                                      rows="6"
                                      maxlength="2000"
                                      class="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-blue-500 transition-colors resize-vertical"
                                      placeholder="Provide a detailed description of your product, its features, and benefits..."></textarea>
                            <div class="text-xs text-gray-400 mt-1">Maximum 2000 characters. Describe what your product does and why it's useful.</div>
                        </div>
                    </div>
                </div>

                <!-- 分类和标签 -->
                <div class="bg-gray-900 rounded-xl p-6">
                    <h3 class="text-xl font-semibold text-white mb-6 flex items-center">
                        <i class="fas fa-tags mr-3 text-purple-400"></i>
                        Category & Tags
                    </h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- 主分类 -->
                        <div>
                            <label for="category" class="block text-sm font-medium text-gray-300 mb-2">
                                Primary Category <span class="text-red-400">*</span>
                            </label>
                            <select id="category" 
                                    name="category" 
                                    required
                                    class="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-blue-500 transition-colors">
                                <option value="">Select a category</option>
                                <?php foreach ($categories as $category): ?>
                                    <option value="<?= htmlspecialchars($category['slug']) ?>" 
                                            data-icon="<?= htmlspecialchars($category['icon']) ?>"
                                            data-color="<?= htmlspecialchars($category['color']) ?>">
                                        <?= htmlspecialchars($category['name']) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <!-- 技术分类 -->
                        <div>
                            <label for="tech-category" class="block text-sm font-medium text-gray-300 mb-2">
                                Tech Category
                            </label>
                            <select id="tech-category"
                                    name="tech_category"
                                    class="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-blue-500 transition-colors">
                                <option value="">Select tech category (optional)</option>
                                <?php foreach ($techCategories as $techCategory): ?>
                                    <option value="<?= htmlspecialchars($techCategory['slug']) ?>">
                                        <?= htmlspecialchars($techCategory['name']) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <!-- 发布状态 -->
                        <div>
                            <label for="launch-status" class="block text-sm font-medium text-gray-300 mb-2">
                                Launch Status
                            </label>
                            <div class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-gray-300">
                                Coming Soon (Pending Review)
                            </div>
                            <input type="hidden" id="launch-status" name="launch_status" value="coming-soon">
                            <p class="text-xs text-gray-400 mt-1">Status will be updated after admin review</p>
                        </div>

                        <!-- 定价模式 -->
                        <div>
                            <label for="pricing-model" class="block text-sm font-medium text-gray-300 mb-2">
                                Pricing Model
                            </label>
                            <select id="pricing-model" 
                                    name="pricing_model" 
                                    class="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:border-blue-500 transition-colors">
                                <option value="unknown">Unknown</option>
                                <option value="free">Free</option>
                                <option value="freemium">Freemium</option>
                                <option value="paid">Paid</option>
                                <option value="enterprise">Enterprise</option>
                            </select>
                        </div>

                        <!-- 目标用户 -->
                        <div>
                            <label for="target-audience" class="block text-sm font-medium text-gray-300 mb-2">
                                Target Audience
                            </label>
                            <input type="text" 
                                   id="target-audience" 
                                   name="target_audience" 
                                   maxlength="500"
                                   class="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-blue-500 transition-colors"
                                   placeholder="e.g., Developers, Designers, Small businesses">
                        </div>
                    </div>

                    <!-- 标签选择 -->
                    <div class="mt-6">
                        <label class="block text-sm font-medium text-gray-300 mb-2">
                            Tags
                        </label>
                        <div class="bg-gray-800 border border-gray-600 rounded-lg p-4">
                            <div class="flex flex-wrap gap-2 mb-3" id="selected-tags">
                                <!-- 已选择的标签会显示在这里 -->
                            </div>
                            <input type="text" 
                                   id="tag-input" 
                                   placeholder="Type to add tags (press Enter or comma to add)"
                                   class="w-full bg-transparent text-white placeholder-gray-400 focus:outline-none">
                            <input type="hidden" id="tags-hidden" name="tags" value="">
                        </div>
                        <div class="text-xs text-gray-400 mt-1">Add relevant tags to help users discover your product</div>
                    </div>
                </div>

                <!-- 功能特性 -->
                <div class="bg-gray-900 rounded-xl p-6">
                    <h3 class="text-xl font-semibold text-white mb-6 flex items-center">
                        <i class="fas fa-star mr-3 text-yellow-400"></i>
                        Features & Benefits
                    </h3>

                    <div class="space-y-4">
                        <!-- 核心功能 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">
                                Key Features
                            </label>
                            <div id="features-container" class="space-y-2">
                                <div class="flex gap-2 feature-item">
                                    <input type="text"
                                           name="features[]"
                                           placeholder="Enter a key feature"
                                           class="flex-1 px-4 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-blue-500 transition-colors">
                                    <button type="button" class="px-3 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors remove-feature hidden">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                            <button type="button"
                                    id="add-feature-btn"
                                    class="mt-2 px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white text-sm rounded-lg transition-colors">
                                <i class="fas fa-plus mr-1"></i>
                                Add Feature
                            </button>
                            <div class="text-xs text-gray-400 mt-1">List the main features that make your product unique</div>
                        </div>

                        <!-- 使用场景 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">
                                Use Cases
                            </label>
                            <div id="usecases-container" class="space-y-2">
                                <div class="flex gap-2 usecase-item">
                                    <input type="text"
                                           name="use_cases[]"
                                           placeholder="Enter a use case"
                                           class="flex-1 px-4 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-blue-500 transition-colors">
                                    <button type="button" class="px-3 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors remove-usecase hidden">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                            <button type="button"
                                    id="add-usecase-btn"
                                    class="mt-2 px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white text-sm rounded-lg transition-colors">
                                <i class="fas fa-plus mr-1"></i>
                                Add Use Case
                            </button>
                            <div class="text-xs text-gray-400 mt-1">Describe how people can use your product</div>
                        </div>
                    </div>
                </div>

                <!-- 媒体上传 -->
                <div class="bg-gray-900 rounded-xl p-6">
                    <h3 class="text-xl font-semibold text-white mb-6 flex items-center">
                        <i class="fas fa-images mr-3 text-green-400"></i>
                        Media & Assets
                    </h3>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Logo上传 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">
                                Product Logo
                            </label>
                            <div class="file-drop-zone bg-gray-800 border-2 border-dashed border-gray-600 rounded-lg p-6 text-center cursor-pointer hover:border-blue-500 transition-colors"
                                 id="logo-drop-zone">
                                <div id="logo-upload-content">
                                    <i class="fas fa-cloud-upload-alt text-3xl text-gray-400 mb-2"></i>
                                    <p class="text-gray-400 mb-2">Drop your logo here or click to browse</p>
                                    <p class="text-xs text-gray-500">PNG, JPG, SVG up to 2MB</p>
                                </div>
                                <div id="logo-preview" class="hidden">
                                    <img id="logo-preview-img" src="" alt="Logo preview" class="max-w-full max-h-32 mx-auto rounded-lg">
                                    <button type="button" id="remove-logo" class="mt-2 px-3 py-1 bg-red-600 hover:bg-red-700 text-white text-xs rounded transition-colors">
                                        Remove
                                    </button>
                                </div>
                            </div>
                            <input type="file" id="logo-input" name="logo" accept="image/*" class="hidden">
                        </div>

                        <!-- 截图上传 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">
                                Screenshots (Optional)
                            </label>
                            <div class="file-drop-zone bg-gray-800 border-2 border-dashed border-gray-600 rounded-lg p-6 text-center cursor-pointer hover:border-blue-500 transition-colors"
                                 id="screenshots-drop-zone">
                                <div id="screenshots-upload-content">
                                    <i class="fas fa-images text-3xl text-gray-400 mb-2"></i>
                                    <p class="text-gray-400 mb-2">Drop screenshots here or click to browse</p>
                                    <p class="text-xs text-gray-500">PNG, JPG up to 5MB each, max 3 images</p>
                                </div>
                            </div>
                            <input type="file" id="screenshots-input" name="screenshots[]" accept="image/*" multiple class="hidden">
                            <div id="screenshots-preview" class="mt-4 grid grid-cols-2 gap-2 hidden"></div>
                        </div>
                    </div>
                </div>

                <!-- 社交链接 -->
                <div class="bg-gray-900 rounded-xl p-6">
                    <h3 class="text-xl font-semibold text-white mb-6 flex items-center">
                        <i class="fas fa-share-alt mr-3 text-pink-400"></i>
                        Social Links (Optional)
                    </h3>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="twitter-url" class="block text-sm font-medium text-gray-300 mb-2">
                                <i class="fab fa-twitter mr-1 text-blue-400"></i>
                                Twitter
                            </label>
                            <input type="url"
                                   id="twitter-url"
                                   name="social_twitter"
                                   class="w-full px-4 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-blue-500 transition-colors"
                                   placeholder="https://twitter.com/yourhandle">
                        </div>

                        <div>
                            <label for="linkedin-url" class="block text-sm font-medium text-gray-300 mb-2">
                                <i class="fab fa-linkedin mr-1 text-blue-600"></i>
                                LinkedIn
                            </label>
                            <input type="url"
                                   id="linkedin-url"
                                   name="social_linkedin"
                                   class="w-full px-4 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-blue-500 transition-colors"
                                   placeholder="https://linkedin.com/company/yourcompany">
                        </div>

                        <div>
                            <label for="github-url" class="block text-sm font-medium text-gray-300 mb-2">
                                <i class="fab fa-github mr-1 text-gray-400"></i>
                                GitHub
                            </label>
                            <input type="url"
                                   id="github-url"
                                   name="social_github"
                                   class="w-full px-4 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-blue-500 transition-colors"
                                   placeholder="https://github.com/yourusername">
                        </div>

                        <div>
                            <label for="website-url-social" class="block text-sm font-medium text-gray-300 mb-2">
                                <i class="fas fa-globe mr-1 text-green-400"></i>
                                Other Website
                            </label>
                            <input type="url"
                                   id="website-url-social"
                                   name="social_website"
                                   class="w-full px-4 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-blue-500 transition-colors"
                                   placeholder="https://your-other-website.com">
                        </div>
                    </div>
                </div>

                <!-- 视频教程 -->
                <div class="bg-gray-900 rounded-xl p-6">
                    <h3 class="text-xl font-semibold text-white mb-6 flex items-center">
                        <i class="fas fa-video mr-3 text-red-400"></i>
                        Video Tutorial (Optional)
                    </h3>

                    <div>
                        <label for="video-tutorial-url" class="block text-sm font-medium text-gray-300 mb-2">
                            <i class="fab fa-youtube mr-1 text-red-500"></i>
                            Tutorial Video URL
                        </label>
                        <input type="url"
                               id="video-tutorial-url"
                               name="video_tutorial_url"
                               class="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-blue-500 transition-colors"
                               placeholder="https://www.youtube.com/watch?v=...">
                        <div class="text-xs text-gray-400 mt-2">
                            <i class="fas fa-info-circle mr-1"></i>
                            Supports YouTube, Vimeo, and other video platforms. This will be displayed as an embedded player on your product page.
                        </div>
                    </div>
                </div>

                <!-- 提交按钮 -->
                <div class="bg-gray-900 rounded-xl p-6">
                    <div class="text-center">
                        <div class="mb-4">
                            <label class="flex items-center justify-center text-sm text-gray-300">
                                <input type="checkbox" id="terms-checkbox" required class="mr-2 rounded">
                                I agree to the <a href="/terms" class="text-blue-400 hover:text-blue-300 underline" target="_blank">Terms of Service</a>
                                and <a href="/privacy" class="text-blue-400 hover:text-blue-300 underline" target="_blank">Privacy Policy</a>
                            </label>
                        </div>

                        <button type="submit"
                                id="submit-btn"
                                class="px-8 py-4 bg-blue-600 hover:bg-blue-700 text-white hover:text-white font-semibold rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
                            <i class="fas fa-rocket mr-2"></i>
                            Submit for Review
                        </button>

                        <p class="text-xs text-gray-400 mt-3">
                            Your submission will be reviewed by our team before being published.
                        </p>
                    </div>
                </div>
            </form>
        </div>

        <!-- 自定义提示窗 -->
        <div id="custom-modal" class="fixed inset-0 bg-black bg-opacity-50 custom-modal hidden z-50 flex items-center justify-center p-4">
            <div class="bg-gray-900 rounded-xl p-6 max-w-md w-full mx-4 custom-alert border border-gray-700">
                <div class="flex items-center mb-4">
                    <div id="modal-icon" class="w-10 h-10 rounded-full flex items-center justify-center mr-3">
                        <i id="modal-icon-class" class="text-xl"></i>
                    </div>
                    <h3 id="modal-title" class="text-lg font-semibold text-white"></h3>
                </div>
                <p id="modal-message" class="text-gray-300 mb-6"></p>
                <div class="flex justify-end space-x-3">
                    <button id="modal-cancel" class="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors hidden">
                        Cancel
                    </button>
                    <button id="modal-confirm" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors">
                        OK
                    </button>
                </div>
            </div>
        </div>

        <!-- 加载提示窗 -->
        <div id="loading-modal" class="fixed inset-0 bg-black bg-opacity-50 custom-modal hidden z-50 flex items-center justify-center p-4">
            <div class="bg-gray-900 rounded-xl p-6 max-w-sm w-full mx-4 custom-alert border border-gray-700 text-center">
                <div class="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-spinner loading-spinner text-white text-xl"></i>
                </div>
                <h3 id="loading-title" class="text-lg font-semibold text-white mb-2">Processing...</h3>
                <p id="loading-message" class="text-gray-300 text-sm">Please wait while we process your request.</p>
            </div>
        </div>

        <!-- Logo裁剪模态框 -->
        <div id="crop-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
            <div class="bg-gray-900 rounded-xl p-6 max-w-2xl w-full mx-4 border border-gray-700">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-white">Crop Logo</h3>
                    <button id="crop-modal-close" class="text-gray-400 hover:text-gray-200">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <p class="text-gray-300 mb-4">Select a square area for your logo</p>
                <div class="mb-4">
                    <div id="crop-container" class="max-w-full max-h-96 overflow-hidden bg-gray-800 rounded-lg">
                        <img id="crop-image" class="max-w-full" style="display: none;">
                    </div>
                </div>
                <div class="flex justify-end space-x-3">
                    <button id="crop-cancel" class="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors">Cancel</button>
                    <button id="crop-confirm" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors">Crop & Upload</button>
                </div>
            </div>
        </div>
    </main>

    <!-- 包含公共底部 -->
    <?php include ROOT_PATH . '/templates/layout/footer.php'; ?>

    <!-- Cropper.js JavaScript -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.5.12/cropper.min.js"></script>

    <!-- Cropper.js JavaScript -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.5.12/cropper.min.js"></script>

    <!-- JavaScript -->
    <script>
        // 全局变量
        let selectedTags = [];
        let uploadedScreenshots = [];
        let logoFile = null;
        let cropper = null;

        // 自定义提示窗函数
        function showCustomAlert(title, message, type = 'info', showCancel = false) {
            const modal = document.getElementById('custom-modal');
            const modalIcon = document.getElementById('modal-icon');
            const modalIconClass = document.getElementById('modal-icon-class');
            const modalTitle = document.getElementById('modal-title');
            const modalMessage = document.getElementById('modal-message');
            const modalCancel = document.getElementById('modal-cancel');
            const modalConfirm = document.getElementById('modal-confirm');

            // 设置图标和颜色
            const config = {
                success: { icon: 'fas fa-check', bgColor: 'bg-green-600', iconColor: 'text-white' },
                error: { icon: 'fas fa-times', bgColor: 'bg-red-600', iconColor: 'text-white' },
                warning: { icon: 'fas fa-exclamation', bgColor: 'bg-yellow-600', iconColor: 'text-white' },
                info: { icon: 'fas fa-info', bgColor: 'bg-blue-600', iconColor: 'text-white' }
            };

            const typeConfig = config[type] || config.info;
            modalIcon.className = `w-10 h-10 rounded-full flex items-center justify-center mr-3 ${typeConfig.bgColor}`;
            modalIconClass.className = `${typeConfig.icon} text-xl ${typeConfig.iconColor}`;
            modalTitle.textContent = title;
            modalMessage.textContent = message;

            // 显示/隐藏取消按钮
            modalCancel.classList.toggle('hidden', !showCancel);

            modal.classList.remove('hidden');

            return new Promise((resolve) => {
                modalConfirm.onclick = () => {
                    modal.classList.add('hidden');
                    resolve(true);
                };
                modalCancel.onclick = () => {
                    modal.classList.add('hidden');
                    resolve(false);
                };
            });
        }

        // 显示加载提示
        function showLoading(title, message) {
            document.getElementById('loading-title').textContent = title;
            document.getElementById('loading-message').textContent = message;
            document.getElementById('loading-modal').classList.remove('hidden');
        }

        // 隐藏加载提示
        function hideLoading() {
            document.getElementById('loading-modal').classList.add('hidden');
        }

        // AI分析功能
        document.getElementById('ai-analyze-btn').addEventListener('click', async function() {
            const urlInput = document.getElementById('website-url-input');
            let url = urlInput.value.trim();

            if (!url) {
                showCustomAlert('URL Required', 'Please enter a website URL to analyze.', 'warning');
                return;
            }

            // 验证URL格式
            try {
                const urlObj = new URL(url);
                // 过滤URL参数，只保留协议、主机和路径
                url = urlObj.protocol + '//' + urlObj.hostname + (urlObj.pathname !== '/' ? urlObj.pathname : '');
                // 更新输入框显示清理后的URL
                urlInput.value = url;
                // 同步到正式的 Website URL 字段
                const websiteField = document.getElementById('website-url');
                if (websiteField) {
                    websiteField.value = url;
                }
            } catch (e) {
                showCustomAlert('Invalid URL', 'Please enter a valid website URL (including https://).', 'error');
                return;
            }

            // 先检查配额是否足够，避免显示分析进度
            const originalText = this.innerHTML;
            this.innerHTML = '<i class="fas fa-spinner loading-spinner mr-2"></i>Checking quota...';
            this.disabled = true;

            try {
                // 检查配额
                const quotaResponse = await fetch('/api/check-quota.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'submit_launch'
                    })
                });

                const quotaResult = await quotaResponse.json();

                if (!quotaResult.success) {
                    showCustomAlert('Quota Check Failed', 'Failed to check quota. Please try again.', 'error');
                    this.innerHTML = originalText;
                    this.disabled = false;
                    return;
                }

                if (!quotaResult.has_enough_quota) {
                    showCustomAlert('Insufficient Quota', quotaResult.message, 'error');
                    this.innerHTML = originalText;
                    this.disabled = false;
                    return;
                }

            } catch (error) {
                showCustomAlert('Quota Check Failed', 'Failed to check quota. Please try again.', 'error');
                this.innerHTML = originalText;
                this.disabled = false;
                return;
            }

            // 配额检查通过，显示分析加载状态
            this.innerHTML = '<i class="fas fa-spinner loading-spinner mr-2"></i>Analyzing...';
            showLoading('Analyzing Website', 'AI is analyzing your website and extracting information...');

            try {
                const response = await fetch('/ajax/analyze-launch.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `url=${encodeURIComponent(url)}`
                });

                let result;
                let responseText;
                try {
                    responseText = await response.text();
                    result = JSON.parse(responseText);
                } catch (parseError) {
                    console.error('JSON parse error:', parseError);
                    console.error('Response text:', responseText);
                    showCustomAlert('Analysis Failed', 'Server returned an invalid response. Please try again or fill the form manually.', 'error');
                    return;
                }

                if (result.success) {
                    // 填充表单字段
                    fillFormFromAnalysis(result.data);

                    // 显示分析结果
                    showAnalysisResult(result.data);

                    showCustomAlert('Analysis Complete', 'AI has successfully analyzed your website and filled the form. Please review and adjust as needed.', 'success');
                } else {
                    showCustomAlert('Analysis Failed', result.message || 'Failed to analyze the website. Please fill the form manually.', 'error');
                }
            } catch (error) {
                console.error('Analysis error:', error);
                showCustomAlert('Error', 'An error occurred during analysis. Please try again or fill the form manually.', 'error');
            } finally {
                hideLoading();
                this.innerHTML = originalText;
                this.disabled = false;
            }
        });

        // 填充表单数据
        function fillFormFromAnalysis(data) {
            // 基础信息
            if (data.product_name) document.getElementById('product-name').value = data.product_name;
            if (data.tagline) document.getElementById('tagline').value = data.tagline;
            if (data.description) document.getElementById('description').value = data.description;

            // 同步网站URL
            document.getElementById('website-url').value = document.getElementById('website-url-input').value;

            // 分类和状态
            if (data.category) document.getElementById('category').value = data.category;

            // 处理tech_category - AI返回的是slug值，直接设置
            if (data.tech_category) {
                const techCategorySelect = document.getElementById('tech-category');
                if (techCategorySelect) {
                    techCategorySelect.value = data.tech_category;
                }
            }

            // launch_status 固定为 coming-soon，不允许AI修改
            if (data.pricing_model) document.getElementById('pricing-model').value = data.pricing_model;
            if (data.target_audience) document.getElementById('target-audience').value = data.target_audience;

            // 标签
            if (data.tags && Array.isArray(data.tags)) {
                selectedTags = [...data.tags];
                updateTagsDisplay();
            }

            // 功能特性
            if (data.key_features && Array.isArray(data.key_features)) {
                fillDynamicFields('features-container', 'features[]', data.key_features, 'feature-item', 'remove-feature');
            }

            // 使用场景
            if (data.use_cases && Array.isArray(data.use_cases)) {
                fillDynamicFields('usecases-container', 'use_cases[]', data.use_cases, 'usecase-item', 'remove-usecase');
            }
        }

        // 填充动态字段
        function fillDynamicFields(containerId, inputName, values, itemClass, removeClass) {
            const container = document.getElementById(containerId);
            container.innerHTML = '';

            values.forEach((value, index) => {
                const div = document.createElement('div');
                div.className = `flex gap-2 ${itemClass}`;
                div.innerHTML = `
                    <input type="text"
                           name="${inputName}"
                           value="${value.replace(/"/g, '&quot;')}"
                           class="flex-1 px-4 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-blue-500 transition-colors">
                    <button type="button" class="px-3 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors ${removeClass} ${index === 0 ? 'hidden' : ''}">
                        <i class="fas fa-times"></i>
                    </button>
                `;
                container.appendChild(div);
            });

            // 如果没有值，添加一个空的输入框
            if (values.length === 0) {
                const div = document.createElement('div');
                div.className = `flex gap-2 ${itemClass}`;
                div.innerHTML = `
                    <input type="text"
                           name="${inputName}"
                           placeholder="Enter a ${itemClass.includes('feature') ? 'key feature' : 'use case'}"
                           class="flex-1 px-4 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-blue-500 transition-colors">
                    <button type="button" class="px-3 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors ${removeClass} hidden">
                        <i class="fas fa-times"></i>
                    </button>
                `;
                container.appendChild(div);
            }

            // 重新绑定删除按钮事件
            bindRemoveEvents();
        }

        // 显示分析结果
        function showAnalysisResult(data) {
            const resultDiv = document.getElementById('ai-analysis-result');
            const summaryDiv = document.getElementById('analysis-summary');

            let summary = 'Extracted: ';
            const extracted = [];

            if (data.product_name) extracted.push('Product name');
            if (data.tagline) extracted.push('Tagline');
            if (data.description) extracted.push('Description');
            if (data.category) extracted.push('Category');
            if (data.tech_category) extracted.push('Tech Category');
            if (data.key_features && data.key_features.length > 0) extracted.push(`${data.key_features.length} features`);
            if (data.tags && data.tags.length > 0) extracted.push(`${data.tags.length} tags`);

            summary += extracted.join(', ');
            summaryDiv.textContent = summary;

            resultDiv.classList.remove('hidden');
        }

        // 标签管理
        const tagInput = document.getElementById('tag-input');
        const selectedTagsContainer = document.getElementById('selected-tags');
        const tagsHidden = document.getElementById('tags-hidden');

        tagInput.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ',') {
                e.preventDefault();
                addTag();
            }
        });

        tagInput.addEventListener('blur', function() {
            if (this.value.trim()) {
                addTag();
            }
        });

        function addTag() {
            const tagText = tagInput.value.trim().replace(',', '');
            if (tagText && !selectedTags.includes(tagText) && selectedTags.length < 10) {
                selectedTags.push(tagText);
                updateTagsDisplay();
                tagInput.value = '';
            }
        }

        function removeTag(tagText) {
            selectedTags = selectedTags.filter(tag => tag !== tagText);
            updateTagsDisplay();
        }

        function updateTagsDisplay() {
            selectedTagsContainer.innerHTML = '';
            selectedTags.forEach(tag => {
                const tagElement = document.createElement('span');
                tagElement.className = 'inline-flex items-center px-3 py-1 bg-blue-600 text-white text-sm rounded-full';
                tagElement.innerHTML = `
                    ${tag}
                    <button type="button" onclick="removeTag('${tag}')" class="ml-2 text-blue-200 hover:text-white">
                        <i class="fas fa-times text-xs"></i>
                    </button>
                `;
                selectedTagsContainer.appendChild(tagElement);
            });

            tagsHidden.value = JSON.stringify(selectedTags);
        }

        // 动态字段管理
        function bindRemoveEvents() {
            // 功能特性删除按钮
            document.querySelectorAll('.remove-feature').forEach(btn => {
                btn.onclick = function() {
                    this.parentElement.remove();
                    updateRemoveButtons('features-container', 'remove-feature');
                };
            });

            // 使用场景删除按钮
            document.querySelectorAll('.remove-usecase').forEach(btn => {
                btn.onclick = function() {
                    this.parentElement.remove();
                    updateRemoveButtons('usecases-container', 'remove-usecase');
                };
            });
        }

        function updateRemoveButtons(containerId, buttonClass) {
            const container = document.getElementById(containerId);
            const buttons = container.querySelectorAll(`.${buttonClass}`);
            buttons.forEach((btn, index) => {
                btn.classList.toggle('hidden', index === 0);
            });
        }

        // 添加功能特性
        document.getElementById('add-feature-btn').addEventListener('click', function() {
            const container = document.getElementById('features-container');
            const div = document.createElement('div');
            div.className = 'flex gap-2 feature-item';
            div.innerHTML = `
                <input type="text"
                       name="features[]"
                       placeholder="Enter a key feature"
                       class="flex-1 px-4 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-blue-500 transition-colors">
                <button type="button" class="px-3 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors remove-feature">
                    <i class="fas fa-times"></i>
                </button>
            `;
            container.appendChild(div);
            bindRemoveEvents();
            updateRemoveButtons('features-container', 'remove-feature');
        });

        // 添加使用场景
        document.getElementById('add-usecase-btn').addEventListener('click', function() {
            const container = document.getElementById('usecases-container');
            const div = document.createElement('div');
            div.className = 'flex gap-2 usecase-item';
            div.innerHTML = `
                <input type="text"
                       name="use_cases[]"
                       placeholder="Enter a use case"
                       class="flex-1 px-4 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-blue-500 transition-colors">
                <button type="button" class="px-3 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors remove-usecase">
                    <i class="fas fa-times"></i>
                </button>
            `;
            container.appendChild(div);
            bindRemoveEvents();
            updateRemoveButtons('usecases-container', 'remove-usecase');
        });

        // 文件上传处理
        const logoDropZone = document.getElementById('logo-drop-zone');
        const logoInput = document.getElementById('logo-input');
        const logoPreview = document.getElementById('logo-preview');
        const logoPreviewImg = document.getElementById('logo-preview-img');
        const logoUploadContent = document.getElementById('logo-upload-content');

        // Logo上传
        logoDropZone.addEventListener('click', () => logoInput.click());
        logoDropZone.addEventListener('dragover', handleDragOver);
        logoDropZone.addEventListener('drop', handleLogoDrop);
        logoInput.addEventListener('change', handleLogoSelect);

        function handleDragOver(e) {
            e.preventDefault();
            e.currentTarget.classList.add('dragover');
        }

        function handleLogoDrop(e) {
            e.preventDefault();
            e.currentTarget.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleLogoFile(files[0]);
            }
        }

        function handleLogoSelect(e) {
            if (e.target.files.length > 0) {
                handleLogoFile(e.target.files[0]);
            }
        }

        function handleLogoFile(file) {
            if (!file.type.startsWith('image/')) {
                showCustomAlert('Invalid File', 'Please select an image file.', 'error');
                return;
            }

            if (file.size > 2 * 1024 * 1024) {
                showCustomAlert('File Too Large', 'Logo file must be less than 2MB.', 'error');
                return;
            }

            // 读取文件并显示裁剪模态框
            const reader = new FileReader();
            reader.onload = function(e) {
                const img = document.getElementById('crop-image');
                img.src = e.target.result;
                img.style.display = 'block';

                // 显示裁剪模态框
                document.getElementById('crop-modal').classList.remove('hidden');
                document.getElementById('crop-modal').classList.add('flex');

                // 初始化Cropper
                if (cropper) {
                    cropper.destroy();
                }
                cropper = new Cropper(img, {
                    aspectRatio: 1, // 强制正方形
                    viewMode: 1,
                    dragMode: 'move',
                    autoCropArea: 0.8,
                    restore: false,
                    guides: false,
                    center: false,
                    highlight: false,
                    cropBoxMovable: true,
                    cropBoxResizable: true,
                    toggleDragModeOnDblclick: false,
                });
            };
            reader.readAsDataURL(file);
        }

        // 裁剪模态框事件处理
        document.getElementById('crop-modal-close').addEventListener('click', closeCropModal);
        document.getElementById('crop-cancel').addEventListener('click', closeCropModal);

        document.getElementById('crop-confirm').addEventListener('click', function() {
            if (!cropper) return;

            // 获取裁剪后的canvas
            const canvas = cropper.getCroppedCanvas({
                width: 512,
                height: 512,
                imageSmoothingEnabled: true,
                imageSmoothingQuality: 'high'
            });

            // 转换为blob并设置为logoFile
            canvas.toBlob(function(blob) {
                // 创建新的File对象
                logoFile = new File([blob], 'cropped-logo.png', { type: 'image/png' });

                // 显示预览
                logoPreviewImg.src = canvas.toDataURL();
                logoUploadContent.classList.add('hidden');
                logoPreview.classList.remove('hidden');

                // 关闭裁剪模态框
                closeCropModal();
            }, 'image/png', 0.9);
        });

        function closeCropModal() {
            const modal = document.getElementById('crop-modal');
            modal.classList.add('hidden');
            modal.classList.remove('flex');

            if (cropper) {
                cropper.destroy();
                cropper = null;
            }

            // 清空输入框
            logoInput.value = '';
        }

        // 移除Logo
        document.getElementById('remove-logo').addEventListener('click', function() {
            logoFile = null;
            logoInput.value = '';
            logoUploadContent.classList.remove('hidden');
            logoPreview.classList.add('hidden');
        });

        // Screenshots上传处理
        const screenshotsDropZone = document.getElementById('screenshots-drop-zone');
        const screenshotsInput = document.getElementById('screenshots-input');
        const screenshotsPreview = document.getElementById('screenshots-preview');
        const screenshotsUploadContent = document.getElementById('screenshots-upload-content');

        // Screenshots上传事件
        screenshotsDropZone.addEventListener('click', () => screenshotsInput.click());
        screenshotsDropZone.addEventListener('dragover', handleDragOver);
        screenshotsDropZone.addEventListener('drop', handleScreenshotsDrop);
        screenshotsInput.addEventListener('change', function(e) {
            handleScreenshotsFiles(Array.from(e.target.files));
        });

        function handleScreenshotsDrop(e) {
            e.preventDefault();
            screenshotsDropZone.classList.remove('border-blue-500');
            const files = Array.from(e.dataTransfer.files);
            handleScreenshotsFiles(files);
        }

        function handleScreenshotsFiles(files) {
            // 限制最多3张图片
            const remainingSlots = 3 - uploadedScreenshots.length;
            if (files.length > remainingSlots) {
                showCustomAlert('Too Many Files', `You can only upload ${remainingSlots} more screenshot(s). Maximum is 3 images.`, 'warning');
                files = files.slice(0, remainingSlots);
            }

            files.forEach(file => {
                if (!file.type.startsWith('image/')) {
                    showCustomAlert('Invalid File', `${file.name} is not an image file.`, 'warning');
                    return;
                }

                if (file.size > 5 * 1024 * 1024) {
                    showCustomAlert('File Too Large', `${file.name} is too large. Maximum size is 5MB.`, 'warning');
                    return;
                }

                // 添加到上传列表
                uploadedScreenshots.push(file);
                displayScreenshotPreview(file, uploadedScreenshots.length - 1);
            });

            updateScreenshotsDisplay();
        }

        function displayScreenshotPreview(file, index) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const previewDiv = document.createElement('div');
                previewDiv.className = 'relative group';
                previewDiv.innerHTML = `
                    <img src="${e.target.result}" class="w-full h-32 object-cover rounded-lg border border-gray-600">
                    <button type="button" onclick="removeScreenshot(${index})"
                            class="absolute top-2 right-2 bg-red-600 hover:bg-red-700 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs opacity-0 group-hover:opacity-100 transition-opacity">
                        <i class="fas fa-times"></i>
                    </button>
                    <div class="absolute bottom-2 left-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded">
                        ${Math.round(file.size / 1024)}KB
                    </div>
                `;
                screenshotsPreview.appendChild(previewDiv);
            };
            reader.readAsDataURL(file);
        }

        function updateScreenshotsDisplay() {
            if (uploadedScreenshots.length > 0) {
                screenshotsPreview.classList.remove('hidden');
                screenshotsUploadContent.innerHTML = `
                    <i class="fas fa-images text-2xl text-gray-400 mb-2"></i>
                    <p class="text-gray-400 text-sm">Click to add more screenshots (${uploadedScreenshots.length}/3)</p>
                `;
            } else {
                screenshotsPreview.classList.add('hidden');
                screenshotsUploadContent.innerHTML = `
                    <i class="fas fa-images text-3xl text-gray-400 mb-2"></i>
                    <p class="text-gray-400 mb-2">Drop screenshots here or click to browse</p>
                    <p class="text-xs text-gray-500">PNG, JPG up to 5MB each, max 3 images</p>
                `;
            }
        }

        // 全局函数：移除截图
        window.removeScreenshot = function(index) {
            uploadedScreenshots.splice(index, 1);
            screenshotsPreview.innerHTML = '';

            // 重新显示所有截图
            uploadedScreenshots.forEach((file, newIndex) => {
                displayScreenshotPreview(file, newIndex);
            });

            updateScreenshotsDisplay();
        };

        // 表单提交
        document.getElementById('submit-launch-form').addEventListener('submit', async function(e) {
            e.preventDefault();

            // 验证必填字段
            const requiredFields = [
                { id: 'product-name', name: 'Product Name' },
                { id: 'website-url', name: 'Website URL' },
                { id: 'tagline', name: 'Tagline' },
                { id: 'description', name: 'Description' },
                { id: 'category', name: 'Category' }
                // launch-status 已固定为 coming-soon，无需验证
            ];

            for (const field of requiredFields) {
                const element = document.getElementById(field.id);
                if (!element.value.trim()) {
                    showCustomAlert('Missing Information', `Please fill in the ${field.name} field.`, 'warning');
                    element.focus();
                    return;
                }
            }

            // 验证条款同意
            if (!document.getElementById('terms-checkbox').checked) {
                showCustomAlert('Terms Required', 'Please agree to the Terms of Service and Privacy Policy.', 'warning');
                return;
            }

            // 显示确认对话框
            const confirmed = await showCustomAlert(
                'Submit Product Launch',
                'Are you sure you want to submit your product for review? You can edit it later if needed.',
                'info',
                true
            );

            if (!confirmed) return;

            // 检查配额是否足够
            try {
                const quotaCheck = await fetch('/api/check-quota.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'submit_launch'
                    })
                });

                const quotaResult = await quotaCheck.json();
                if (!quotaResult.success) {
                    showCustomAlert('Error', quotaResult.message || 'Quota check failed', 'error');
                    return;
                }

                if (!quotaResult.has_enough_quota) {
                    showCustomAlert('Insufficient Quota', quotaResult.message, 'error');
                    return;
                }
            } catch (error) {
                showCustomAlert('Error', 'Failed to check quota. Please try again.', 'error');
                return;
            }

            // 准备表单数据
            const formData = new FormData();

            // 基础字段
            requiredFields.forEach(field => {
                formData.append(field.id.replace('-', '_'), document.getElementById(field.id).value.trim());
            });

            // 可选字段
            const optionalFields = ['tech-category', 'pricing-model', 'target-audience', 'social_twitter', 'social_linkedin', 'social_github', 'social_website'];
            optionalFields.forEach(fieldId => {
                const element = document.getElementById(fieldId);
                if (element && element.value.trim()) {
                    formData.append(fieldId.replace('-', '_'), element.value.trim());
                }
            });

            // 标签
            formData.append('tags', JSON.stringify(selectedTags));

            // 功能特性
            const features = Array.from(document.querySelectorAll('input[name="features[]"]'))
                .map(input => input.value.trim())
                .filter(value => value);
            formData.append('key_features', JSON.stringify(features));

            // 使用场景
            const useCases = Array.from(document.querySelectorAll('input[name="use_cases[]"]'))
                .map(input => input.value.trim())
                .filter(value => value);
            formData.append('use_cases', JSON.stringify(useCases));

            // Logo文件
            if (logoFile) {
                formData.append('logo', logoFile);
            }

            // Screenshots文件
            uploadedScreenshots.forEach((file, index) => {
                formData.append(`screenshots[${index}]`, file);
            });

            // 提交表单
            const submitBtn = document.getElementById('submit-btn');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner loading-spinner mr-2"></i>Submitting...';
            submitBtn.disabled = true;

            showLoading('Submitting Product', 'Please wait while we process your submission...');

            try {
                const response = await fetch('/ajax/submit-launch.php', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    showCustomAlert('Success!', 'Your product has been submitted successfully! It will be reviewed by our team and published soon.', 'success');
                    // 重定向到产品列表页面
                    setTimeout(() => {
                        window.location.href = '/launches';
                    }, 2000);
                } else {
                    showCustomAlert('Submission Failed', result.message || 'Failed to submit your product. Please try again.', 'error');
                }
            } catch (error) {
                console.error('Submit error:', error);
                showCustomAlert('Error', 'An error occurred while submitting your product. Please try again.', 'error');
            } finally {
                hideLoading();
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }
        });

        // 初始化
        bindRemoveEvents();

        // 点击模态框外部关闭
        document.getElementById('custom-modal').addEventListener('click', function(e) {
            if (e.target === this) {
                this.classList.add('hidden');
            }
        });

        // 同步网站URL
        document.getElementById('website-url-input').addEventListener('input', function() {
            document.getElementById('website-url').value = this.value;
        });

        document.getElementById('website-url').addEventListener('input', function() {
            document.getElementById('website-url-input').value = this.value;
        });

        // 视频URL验证和预览
        document.getElementById('video-tutorial-url').addEventListener('input', function() {
            const url = this.value.trim();
            const container = this.parentElement;
            let existingPreview = container.querySelector('.video-preview');

            // 移除现有预览
            if (existingPreview) {
                existingPreview.remove();
            }

            if (url) {
                // 验证是否为支持的视频URL
                const isYouTube = /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([a-zA-Z0-9_-]+)/.test(url);
                const isVimeo = /vimeo\.com\/(\d+)/.test(url);
                const isEmbed = url.includes('embed');

                if (isYouTube || isVimeo || isEmbed) {
                    // 创建预览提示
                    const preview = document.createElement('div');
                    preview.className = 'video-preview mt-2 p-2 bg-green-900/20 border border-green-600/30 rounded text-sm text-green-300';
                    preview.innerHTML = '<i class="fas fa-check-circle mr-1"></i>Valid video URL detected. This will be embedded on your product page.';
                    container.appendChild(preview);
                } else if (url.startsWith('http')) {
                    // 创建警告提示
                    const preview = document.createElement('div');
                    preview.className = 'video-preview mt-2 p-2 bg-yellow-900/20 border border-yellow-600/30 rounded text-sm text-yellow-300';
                    preview.innerHTML = '<i class="fas fa-exclamation-triangle mr-1"></i>URL detected but may not be embeddable. It will be shown as an external link.';
                    container.appendChild(preview);
                }
            }
        });

        // 字符计数更新函数
        function updateCharCount(inputId, countId, maxLength) {
            const input = document.getElementById(inputId);
            const counter = document.getElementById(countId);
            if (input && counter) {
                const currentLength = input.value.length;
                counter.textContent = `${currentLength}/${maxLength}`;

                // 根据字符数量改变颜色
                if (currentLength > maxLength * 0.9) {
                    counter.classList.add('text-red-400');
                    counter.classList.remove('text-yellow-400', 'text-gray-400');
                } else if (currentLength > maxLength * 0.7) {
                    counter.classList.add('text-yellow-400');
                    counter.classList.remove('text-red-400', 'text-gray-400');
                } else {
                    counter.classList.add('text-gray-400');
                    counter.classList.remove('text-red-400', 'text-yellow-400');
                }
            }
        }

        // 页面加载完成后初始化字符计数
        document.addEventListener('DOMContentLoaded', function() {
            updateCharCount('description', 'desc-count', 2000);
            updateCharCount('tagline', 'tagline-count', 150);
        });
    </script>
</body>
</html>
