<?php
/**
 * Admin Account Edit Page
 */

// Security check
if (!defined('SECURE_ACCESS')) {
    exit('Direct access not allowed');
}

// 使用统一的数据库连接
require_once dirname(dirname(__DIR__)) . '/includes/database-connection.php';
require_once __DIR__ . '/../includes/GoogleAuthenticator.php';

// Check if user has 2FA enabled
$has2FA = false;
try {
    $adminId = $_SESSION['admin_id'] ?? 1;
    $checkSql = "SELECT two_factor_enabled FROM pt_manager WHERE id = ?";
    $checkStmt = $pdo->prepare($checkSql);
    $checkStmt->execute([$adminId]);
    $has2FA = (bool)$checkStmt->fetchColumn();
} catch (PDOException $e) {
    error_log("Database error checking 2FA: " . $e->getMessage());
}

// Handle form submission
$message = '';
$messageType = '';

// No longer using session messages due to header issues

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'update') {
    // Validate input
    $username = trim($_POST['username'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $display_name = trim($_POST['display_name'] ?? '');
    $role = $_POST['role'] ?? '';
    $description = trim($_POST['description'] ?? '');
    $twoFactorCode = trim($_POST['two_factor_code'] ?? '');

    // Check if this is the super admin user
    $isSuperAdmin = ($username === 'Prompt2Tool' || $_SESSION['admin_username'] === 'Prompt2Tool');

    // Protect super admin role from being changed
    if ($isSuperAdmin) {
        $role = 'admin'; // Force super admin role
    }

    // Basic validation
    if (empty($username) || empty($email)) {
        $message = 'Username and email are required.';
        $messageType = 'error';
    } elseif ($has2FA && empty($twoFactorCode)) {
        $message = '2FA verification code is required.';
        $messageType = 'error';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $message = 'Please enter a valid email address.';
        $messageType = 'error';
    } elseif ($isSuperAdmin && $username !== 'Prompt2Tool') {
        $message = 'Super Administrator username cannot be changed.';
        $messageType = 'error';
    } else {
        try {
            // Get current admin ID from session
            $adminId = $_SESSION['admin_id'] ?? 1;

            // Check if username or email already exists (excluding current user)
            $checkSql = "SELECT id FROM pt_manager WHERE (username = ? OR email = ?) AND id != ?";
            $checkStmt = $pdo->prepare($checkSql);
            $checkStmt->execute([$username, $email, $adminId]);

            if ($checkStmt->fetch()) {
                $message = 'Username or email already exists.';
                $messageType = 'error';
            } elseif ($has2FA) {
                // If 2FA is enabled, verify the 2FA code
                $secretSql = "SELECT two_factor_secret FROM pt_manager WHERE id = ?";
                $secretStmt = $pdo->prepare($secretSql);
                $secretStmt->execute([$adminId]);
                $secret = $secretStmt->fetchColumn();

                if (!$secret) {
                    $message = '2FA secret not found. Please contact administrator.';
                    $messageType = 'error';
                } else {
                    $ga = new GoogleAuthenticator();
                    if (!$ga->verifyCode($secret, $twoFactorCode)) {
                        $message = 'Invalid 2FA verification code.';
                        $messageType = 'error';
                    } else {
                        // 2FA verified, proceed with account update
                        $canUpdateAccount = true;
                    }
                }
            } else {
                // No 2FA required, can update account
                $canUpdateAccount = true;
            }

            // Update account if all verifications passed
            if (isset($canUpdateAccount) && $canUpdateAccount) {
                // Update admin information in database (handle null values)
                $updateSql = "UPDATE pt_manager SET username = ?, email = ?, display_name = ?, role = ?, description = ?, updated_at = NOW() WHERE id = ?";
                $updateStmt = $pdo->prepare($updateSql);
                $updateResult = $updateStmt->execute([
                    $username,
                    $email,
                    $display_name ?: null,
                    $role,
                    $description ?: null,
                    $adminId
                ]);

                if ($updateResult) {
                    // Update session data
                    $_SESSION['admin_username'] = $username;
                    $_SESSION['admin_email'] = $email;
                    $_SESSION['admin_display_name'] = $display_name;
                    $_SESSION['admin_role'] = $role;
                    $_SESSION['admin_description'] = $description;

                    // Log activity
                    try {
                        require_once __DIR__ . '/../includes/activity-logger.php';
                        logProfileUpdate($adminId);
                    } catch (Exception $e) {
                        // Ignore activity log errors
                    }

                    $message = 'Account information updated successfully!';
                    $messageType = 'success';

                    // Use JavaScript redirect to avoid header issues
                    echo "<script>
                        setTimeout(function() {
                            window.location.href = 'index.php?page=account';
                        }, 1500);
                    </script>";

                    // Clear form data
                    $_POST = [];
                } else {
                    $message = 'Failed to update account information.';
                    $messageType = 'error';
                }
            }
        } catch (PDOException $e) {
            error_log("Database error in account update: " . $e->getMessage());
            $message = 'Database error occurred. Please try again.';
            $messageType = 'error';
        }
    }
}

// Get current user data from database
$currentUser = [
    'username' => 'admin',
    'email' => '<EMAIL>',
    'display_name' => 'Administrator',
    'role' => 'admin',
    'description' => ''
];

try {
    $adminId = $_SESSION['admin_id'] ?? 1;
    $userSql = "SELECT username, email, display_name, role, COALESCE(description, '') as description, created_at, last_login FROM pt_manager WHERE id = ?";
    $userStmt = $pdo->prepare($userSql);
    $userStmt->execute([$adminId]);
    $userData = $userStmt->fetch();

    if ($userData) {
        // Ensure all fields have default values
        $currentUser = [
            'username' => $userData['username'] ?? 'admin',
            'email' => $userData['email'] ?? '<EMAIL>',
            'display_name' => $userData['display_name'] ?? 'Administrator',
            'role' => $userData['role'] ?? 'admin',
            'description' => $userData['description'] ?? '',
            'created_at' => $userData['created_at'] ?? null,
            'last_login' => $userData['last_login'] ?? null
        ];
    }
} catch (PDOException $e) {
    error_log("Database error in account page: " . $e->getMessage());
}
?>

<div class="max-w-4xl mx-auto">
    <div class="mb-6">
        <h1 class="text-2xl font-semibold text-gray-900">Edit Account</h1>
        <p class="text-gray-600 mt-2">Manage your account information and settings</p>
        <?php
        $isSuperAdminPage = ($currentUser['role'] === 'admin' && $currentUser['username'] === 'Prompt2Tool');
        ?>

        <?php if ($isSuperAdminPage): ?>
        <div class="mt-3 p-3 bg-red-50 border border-red-200 rounded">
            <div class="flex items-center">
                <i class="fas fa-crown text-red-500 mr-2"></i>
                <span class="text-sm text-red-700">You are the Super Administrator. Username and Role are protected and cannot be changed for security reasons.</span>
            </div>
        </div>
        <?php endif; ?>



        <?php if ($has2FA && $messageType !== 'success'): ?>
        <div class="mt-3 p-3 bg-blue-50 border border-blue-200 rounded">
            <div class="flex items-center">
                <i class="fas fa-shield-alt text-blue-500 mr-2"></i>
                <span class="text-sm text-blue-700">Two-factor authentication is enabled. You'll need to provide your 2FA code to update your account information.</span>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <!-- Success/Error Messages -->
    <?php if ($message): ?>
    <div class="mb-6 p-4 border <?= $messageType === 'success' ? 'bg-green-50 border-green-200 text-green-700' : 'bg-red-50 border-red-200 text-red-700' ?>">
        <div class="flex">
            <div class="flex-shrink-0">
                <i class="fas <?= $messageType === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle' ?>"></i>
            </div>
            <div class="ml-3">
                <p class="text-sm"><?= htmlspecialchars($message ?? '') ?></p>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- 账户信息表单 -->
    <div class="bg-white shadow border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-medium text-gray-900">Basic Information</h2>
        </div>
        <div class="p-6">
            <form method="POST" action="?page=account&action=update">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Username -->
                    <div>
                        <label for="username" class="block text-sm font-medium text-gray-700 mb-2">Username</label>
                        <?php
                        $isSuperAdminDisplay = ($currentUser['role'] === 'admin' && $currentUser['username'] === 'Prompt2Tool');
                        ?>

                        <?php if ($isSuperAdminDisplay): ?>
                            <!-- Super Administrator - Username cannot be changed -->
                            <div class="w-full px-3 py-2 border border-gray-200 bg-gray-50 text-gray-700 rounded">
                                <div class="flex items-center justify-between">
                                    <span class="font-medium"><?= htmlspecialchars($currentUser['username']) ?></span>
                                    <span class="inline-flex items-center px-2 py-1 text-xs font-medium bg-red-100 text-red-800 rounded">
                                        <i class="fas fa-lock mr-1"></i>
                                        Protected
                                    </span>
                                </div>
                                <p class="text-sm text-gray-500 mt-1">This username cannot be changed for security reasons</p>
                            </div>
                            <input type="hidden" name="username" value="<?= htmlspecialchars($currentUser['username']) ?>">
                        <?php else: ?>
                            <!-- Regular users can change username -->
                            <input type="text" id="username" name="username" value="<?= htmlspecialchars($currentUser['username'] ?? '') ?>"
                                   class="w-full px-3 py-2 border border-gray-300 focus:outline-none focus:border-accent" required>
                        <?php endif; ?>
                    </div>

                    <!-- Email -->
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
                        <input type="email" id="email" name="email" value="<?= htmlspecialchars($currentUser['email'] ?? '') ?>"
                               class="w-full px-3 py-2 border border-gray-300 focus:outline-none focus:border-accent" required>
                    </div>

                    <!-- Display Name -->
                    <div>
                        <label for="display_name" class="block text-sm font-medium text-gray-700 mb-2">Display Name</label>
                        <input type="text" id="display_name" name="display_name" value="<?= htmlspecialchars($currentUser['display_name'] ?? '') ?>"
                               class="w-full px-3 py-2 border border-gray-300 focus:outline-none focus:border-accent">
                    </div>

                    <!-- Role -->
                    <div>
                        <label for="role" class="block text-sm font-medium text-gray-700 mb-2">Role</label>
                        <?php
                        $isSuperAdmin = ($currentUser['role'] === 'admin' && $currentUser['username'] === 'Prompt2Tool');
                        ?>

                        <?php if ($isSuperAdmin): ?>
                            <!-- Super Administrator - Role cannot be changed -->
                            <div class="w-full px-3 py-2 border border-gray-200 bg-gray-50 text-gray-700 rounded">
                                <div class="flex items-center justify-between">
                                    <span class="font-medium">Super Administrator</span>
                                    <span class="inline-flex items-center px-2 py-1 text-xs font-medium bg-red-100 text-red-800 rounded">
                                        <i class="fas fa-lock mr-1"></i>
                                        Protected
                                    </span>
                                </div>
                                <p class="text-sm text-gray-500 mt-1">This role cannot be changed for security reasons</p>
                            </div>
                            <input type="hidden" name="role" value="admin">
                        <?php else: ?>
                            <!-- Regular users can change role -->
                            <select id="role" name="role"
                                    class="w-full px-3 py-2 border border-gray-300 focus:outline-none focus:border-accent">
                                <option value="admin" <?= $currentUser['role'] === 'admin' ? 'selected' : '' ?>>Administrator</option>
                                <option value="manager" <?= $currentUser['role'] === 'manager' ? 'selected' : '' ?>>Manager</option>
                                <option value="editor" <?= $currentUser['role'] === 'editor' ? 'selected' : '' ?>>Editor</option>
                            </select>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Description -->
                <div class="mt-6">
                    <label for="description" class="block text-sm font-medium text-gray-700 mb-2">Personal Description</label>
                    <textarea id="description" name="description" rows="3"
                              class="w-full px-3 py-2 border border-gray-300 focus:outline-none focus:border-accent"
                              placeholder="Tell us about yourself..."><?= htmlspecialchars($currentUser['description'] ?? '') ?></textarea>
                </div>

                <?php if ($has2FA): ?>
                <!-- 2FA Verification Code -->
                <div class="mt-6">
                    <label for="two_factor_code" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-shield-alt text-blue-500 mr-1"></i>
                        Two-Factor Authentication Code
                    </label>
                    <input type="text" id="two_factor_code" name="two_factor_code" maxlength="6" required
                           class="w-full px-3 py-2 border border-gray-300 focus:outline-none focus:border-accent text-center text-lg font-mono"
                           placeholder="000000">
                    <p class="mt-1 text-sm text-gray-500">Enter the 6-digit code from your authenticator app</p>
                </div>
                <?php endif; ?>

                <!-- Hidden Fields -->
                <input type="hidden" name="action" value="update">

                <!-- Buttons -->
                <div class="mt-6 flex justify-end space-x-3">
                    <button type="button" class="px-4 py-2 border border-gray-300 text-gray-700 hover:bg-gray-50" onclick="window.location.reload()">
                        Cancel
                    </button>
                    <button type="submit" class="px-4 py-2 bg-accent text-white hover:bg-blue-700">
                        Save Changes
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Account Status -->
    <div class="mt-6 bg-white shadow border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-medium text-gray-900">Account Status</h2>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                    <div class="text-sm font-medium text-gray-700">Account Status</div>
                    <div class="mt-1">
                        <span class="inline-flex px-2 py-1 text-xs font-medium bg-green-100 text-green-800">
                            Active
                        </span>
                    </div>
                </div>
                <div>
                    <div class="text-sm font-medium text-gray-700">Created</div>
                    <div class="mt-1 text-sm text-gray-900"><?= (!empty($currentUser['created_at'])) ? date('Y-m-d H:i:s', strtotime($currentUser['created_at'])) : 'N/A' ?></div>
                </div>
                <div>
                    <div class="text-sm font-medium text-gray-700">Last Login</div>
                    <div class="mt-1 text-sm text-gray-900"><?= (!empty($currentUser['last_login'])) ? date('Y-m-d H:i:s', strtotime($currentUser['last_login'])) : 'Never' ?></div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// 2FA code input validation
<?php if ($has2FA): ?>
document.addEventListener('DOMContentLoaded', function() {
    const twoFactorInput = document.getElementById('two_factor_code');

    if (twoFactorInput) {
        // Only allow numbers
        twoFactorInput.addEventListener('input', function() {
            this.value = this.value.replace(/[^0-9]/g, '');

            // Limit to 6 digits
            if (this.value.length > 6) {
                this.value = this.value.slice(0, 6);
            }
        });

        // Form validation
        document.querySelector('form').addEventListener('submit', function(e) {
            const twoFactorCode = twoFactorInput.value;

            if (twoFactorCode.length !== 6) {
                e.preventDefault();
                alert('Please enter a complete 6-digit 2FA code');
                twoFactorInput.focus();
                return false;
            }
        });
    }
});
<?php endif; ?>
</script>
