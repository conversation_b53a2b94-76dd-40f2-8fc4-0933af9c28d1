<?php
/**
 * API认证类
 * 处理API认证和授权
 */

require_once ROOT_PATH . '/control-panel/models/UserModel.php';
require_once ROOT_PATH . '/control-panel/models/AdminModel.php';

class ApiAuth {
    
    private static $currentUser = null;
    private static $currentAdmin = null;
    private static $authType = null;
    
    /**
     * 验证API Token
     */
    public static function validateToken($token) {
        if (empty($token)) {
            return false;
        }
        
        // 检查是否为JWT Token
        if (self::isJwtToken($token)) {
            return self::validateJwtToken($token);
        }
        
        // 检查是否为API Key
        return self::validateApiKey($token);
    }
    
    /**
     * 验证JWT Token
     */
    private static function validateJwtToken($token) {
        try {
            $payload = self::decodeJwtToken($token);
            
            if (!$payload || !isset($payload['user_id']) || !isset($payload['type'])) {
                return false;
            }
            
            // 检查Token是否过期
            if (isset($payload['exp']) && $payload['exp'] < time()) {
                return false;
            }
            
            // 根据类型加载用户或管理员
            if ($payload['type'] === 'user') {
                $userModel = new UserModel();
                $user = $userModel->find($payload['user_id']);
                
                if (!$user || $user['status'] !== 'active') {
                    return false;
                }
                
                self::$currentUser = $user;
                self::$authType = 'user';
                return true;
                
            } elseif ($payload['type'] === 'admin') {
                $adminModel = new AdminModel();
                $admin = $adminModel->find($payload['user_id']);
                
                if (!$admin || $admin['status'] !== 'active') {
                    return false;
                }
                
                self::$currentAdmin = $admin;
                self::$authType = 'admin';
                return true;
            }
            
            return false;
            
        } catch (Exception $e) {
            return false;
        }
    }
    
    /**
     * 验证API Key
     */
    private static function validateApiKey($apiKey) {
        $userModel = new UserModel();
        $user = $userModel->findByApiToken($apiKey);
        
        if (!$user || $user['status'] !== 'active') {
            return false;
        }
        
        self::$currentUser = $user;
        self::$authType = 'api_key';
        return true;
    }
    
    /**
     * 生成JWT Token
     */
    public static function generateJwtToken($userId, $type = 'user', $expiresIn = 3600) {
        $header = [
            'typ' => 'JWT',
            'alg' => 'HS256'
        ];
        
        $payload = [
            'user_id' => $userId,
            'type' => $type,
            'iat' => time(),
            'exp' => time() + $expiresIn,
            'iss' => $_SERVER['HTTP_HOST'] ?? 'api.prompt2tool.com'
        ];
        
        $headerEncoded = self::base64UrlEncode(json_encode($header));
        $payloadEncoded = self::base64UrlEncode(json_encode($payload));
        
        $signature = hash_hmac('sha256', $headerEncoded . '.' . $payloadEncoded, self::getJwtSecret(), true);
        $signatureEncoded = self::base64UrlEncode($signature);
        
        return $headerEncoded . '.' . $payloadEncoded . '.' . $signatureEncoded;
    }
    
    /**
     * 解码JWT Token
     */
    private static function decodeJwtToken($token) {
        $parts = explode('.', $token);
        
        if (count($parts) !== 3) {
            return false;
        }
        
        list($headerEncoded, $payloadEncoded, $signatureEncoded) = $parts;
        
        // 验证签名
        $signature = self::base64UrlDecode($signatureEncoded);
        $expectedSignature = hash_hmac('sha256', $headerEncoded . '.' . $payloadEncoded, self::getJwtSecret(), true);
        
        if (!hash_equals($signature, $expectedSignature)) {
            return false;
        }
        
        // 解码payload
        $payload = json_decode(self::base64UrlDecode($payloadEncoded), true);
        
        return $payload;
    }
    
    /**
     * 检查是否为JWT Token
     */
    private static function isJwtToken($token) {
        return substr_count($token, '.') === 2;
    }
    
    /**
     * 获取当前用户
     */
    public static function getCurrentUser() {
        return self::$currentUser;
    }
    
    /**
     * 获取当前管理员
     */
    public static function getCurrentAdmin() {
        return self::$currentAdmin;
    }
    
    /**
     * 获取认证类型
     */
    public static function getAuthType() {
        return self::$authType;
    }
    
    /**
     * 检查是否已认证
     */
    public static function isAuthenticated() {
        return self::$currentUser !== null || self::$currentAdmin !== null;
    }
    
    /**
     * 检查是否为管理员
     */
    public static function isAdmin() {
        return self::$currentAdmin !== null;
    }
    
    /**
     * 检查是否为用户
     */
    public static function isUser() {
        return self::$currentUser !== null;
    }
    
    /**
     * 检查权限
     */
    public static function hasPermission($permission) {
        if (self::$currentAdmin) {
            // 管理员权限检查
            require_once ROOT_PATH . '/control-panel/classes/PermissionManager.php';
            $permissionManager = new PermissionManager();
            return $permissionManager->hasPermission(self::$currentAdmin['id'], $permission);
        }
        
        return false;
    }
    
    /**
     * 检查角色
     */
    public static function hasRole($role) {
        if (self::$currentAdmin) {
            return self::$currentAdmin['role'] === $role;
        }
        
        return false;
    }
    
    /**
     * 获取用户ID
     */
    public static function getUserId() {
        if (self::$currentUser) {
            return self::$currentUser['id'];
        }
        
        if (self::$currentAdmin) {
            return self::$currentAdmin['id'];
        }
        
        return null;
    }
    
    /**
     * 从请求头获取Token
     */
    public static function getTokenFromRequest() {
        // 从Authorization头获取
        $authHeader = $_SERVER['HTTP_AUTHORIZATION'] ?? '';
        if (preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
            return $matches[1];
        }
        
        // 从X-API-Key头获取
        $apiKeyHeader = $_SERVER['HTTP_X_API_KEY'] ?? '';
        if (!empty($apiKeyHeader)) {
            return $apiKeyHeader;
        }
        
        // 从查询参数获取
        return $_GET['api_key'] ?? $_GET['token'] ?? null;
    }
    
    /**
     * 刷新Token
     */
    public static function refreshToken($token) {
        $payload = self::decodeJwtToken($token);
        
        if (!$payload) {
            return false;
        }
        
        // 生成新Token
        return self::generateJwtToken($payload['user_id'], $payload['type']);
    }
    
    /**
     * 注销Token（添加到黑名单）
     */
    public static function revokeToken($token) {
        // 这里可以实现Token黑名单功能
        // 暂时返回true
        return true;
    }
    
    /**
     * Base64 URL编码
     */
    private static function base64UrlEncode($data) {
        return rtrim(strtr(base64_encode($data), '+/', '-_'), '=');
    }
    
    /**
     * Base64 URL解码
     */
    private static function base64UrlDecode($data) {
        return base64_decode(str_pad(strtr($data, '-_', '+/'), strlen($data) % 4, '=', STR_PAD_RIGHT));
    }
    
    /**
     * 获取JWT密钥
     */
    private static function getJwtSecret() {
        // 从数据库配置获取
        try {
            // 使用统一的数据库连接
            require_once dirname(dirname(__DIR__)) . '/includes/database-connection.php';
            global $pdo;

            $stmt = $pdo->prepare("SELECT setting_value FROM pt_system_config WHERE setting_key = 'jwt_secret'");
            $stmt->execute();
            $result = $stmt->fetch();

            if ($result && !empty($result['setting_value'])) {
                return $result['setting_value'];
            }

            // 如果数据库中没有配置，返回默认值
            return 'your-secret-key-change-this-in-production';

        } catch (Exception $e) {
            // 数据库连接失败时使用默认值
            return 'your-secret-key-change-this-in-production';
        }
    }
    
    /**
     * 记录认证日志
     */
    public static function logAuth($action, $details = []) {
        $db = Database::getInstance();
        
        $logData = [
            'user_id' => self::getUserId(),
            'auth_type' => self::$authType,
            'action' => $action,
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'details' => json_encode($details),
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        try {
            $db->insert('auth_logs', $logData);
        } catch (Exception $e) {
            error_log('Failed to log auth action: ' . $e->getMessage());
        }
    }
}
?>
