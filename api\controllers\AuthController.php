<?php
/**
 * 认证控制器
 * 处理用户认证相关的API请求
 */

require_once ROOT_PATH . '/control-panel/models/UserModel.php';
require_once ROOT_PATH . '/control-panel/models/AdminModel.php';
require_once ROOT_PATH . '/control-panel/classes/SecurityManager.php';

class AuthController {
    
    private $userModel;
    private $adminModel;
    private $securityManager;
    
    public function __construct() {
        $this->userModel = new UserModel();
        $this->adminModel = new AdminModel();
        $this->securityManager = new SecurityManager();
    }
    
    /**
     * 用户登录
     */
    public function login() {
        try {
            $input = $this->getJsonInput();
            
            // 验证输入
            $errors = [];
            if (empty($input['email'])) {
                $errors['email'] = 'Email is required';
            }
            if (empty($input['password'])) {
                $errors['password'] = 'Password is required';
            }
            
            if (!empty($errors)) {
                ApiResponse::validationError($errors);
                return;
            }
            
            // 检查是否为管理员登录
            $isAdminLogin = isset($input['admin']) && $input['admin'] === true;
            
            if ($isAdminLogin) {
                // 管理员登录
                $admin = $this->adminModel->validateCredentials($input['email'], $input['password']);
                if (!$admin) {
                    ApiResponse::error([
                        'message' => 'Invalid credentials',
                        'error_code' => 'INVALID_CREDENTIALS'
                    ], 401);
                    return;
                }
                
                // 更新登录信息
                $this->adminModel->updateLoginInfo($admin['id'], $this->getClientIp());
                
                // 生成Token
                $token = ApiAuth::generateJwtToken($admin['id'], 'admin', 3600 * 24); // 24小时
                $refreshToken = ApiAuth::generateJwtToken($admin['id'], 'admin', 3600 * 24 * 7); // 7天
                
                ApiResponse::success([
                    'user' => $this->formatAdminData($admin),
                    'token' => $token,
                    'refresh_token' => $refreshToken,
                    'expires_in' => 3600 * 24,
                    'token_type' => 'Bearer'
                ], 'Admin login successful');
                
            } else {
                // 普通用户登录
                $user = $this->validateUserCredentials($input['email'], $input['password']);
                if (!$user) {
                    ApiResponse::error([
                        'message' => 'Invalid credentials',
                        'error_code' => 'INVALID_CREDENTIALS'
                    ], 401);
                    return;
                }
                
                // 更新登录信息
                $this->userModel->updateLoginInfo($user['id'], $this->getClientIp());
                
                // 生成Token
                $token = ApiAuth::generateJwtToken($user['id'], 'user', 3600 * 24); // 24小时
                $refreshToken = ApiAuth::generateJwtToken($user['id'], 'user', 3600 * 24 * 7); // 7天
                
                ApiResponse::success([
                    'user' => $this->formatUserData($user),
                    'token' => $token,
                    'refresh_token' => $refreshToken,
                    'expires_in' => 3600 * 24,
                    'token_type' => 'Bearer'
                ], 'Login successful');
            }
            
        } catch (Exception $e) {
            ApiResponse::handleException($e);
        }
    }
    
    /**
     * 用户注册
     */
    public function register() {
        try {
            $input = $this->getJsonInput();
            
            // 验证输入
            $errors = $this->validateRegistrationInput($input);
            if (!empty($errors)) {
                ApiResponse::validationError($errors);
                return;
            }
            
            // 检查邮箱是否已存在
            if ($this->userModel->findByEmail($input['email'])) {
                ApiResponse::error([
                    'message' => 'Email already exists',
                    'error_code' => 'EMAIL_EXISTS'
                ], 409);
                return;
            }
            
            // 检查用户名是否已存在
            if (!empty($input['username']) && $this->userModel->findByUsername($input['username'])) {
                ApiResponse::error([
                    'message' => 'Username already exists',
                    'error_code' => 'USERNAME_EXISTS'
                ], 409);
                return;
            }
            
            // 验证密码强度
            $passwordValidation = $this->securityManager->validatePasswordStrength($input['password']);
            if (!$passwordValidation['valid']) {
                ApiResponse::validationError([
                    'password' => $passwordValidation['errors']
                ]);
                return;
            }
            
            // 创建用户
            $userData = [
                'email' => $input['email'],
                'username' => $input['username'] ?? null,
                'password' => $this->securityManager->hashPassword($input['password']),
                'first_name' => $input['first_name'] ?? '',
                'last_name' => $input['last_name'] ?? '',
                'status' => 'active',
                'referral_code' => strtoupper(substr(md5(uniqid()), 0, 8))
            ];
            
            // 处理推荐
            if (!empty($input['referral_code'])) {
                $referrer = $this->userModel->findWhere(['referral_code' => $input['referral_code']]);
                if ($referrer) {
                    $userData['referred_by'] = $referrer['id'];
                }
            }
            
            $user = $this->userModel->create($userData);
            
            if (!$user) {
                ApiResponse::serverError('Failed to create user');
                return;
            }
            
            // 生成Token
            $token = ApiAuth::generateJwtToken($user['id'], 'user', 3600 * 24);
            $refreshToken = ApiAuth::generateJwtToken($user['id'], 'user', 3600 * 24 * 7);
            
            ApiResponse::success([
                'user' => $this->formatUserData($user),
                'token' => $token,
                'refresh_token' => $refreshToken,
                'expires_in' => 3600 * 24,
                'token_type' => 'Bearer'
            ], 'Registration successful', 201);
            
        } catch (Exception $e) {
            ApiResponse::handleException($e);
        }
    }

    /**
     * Google登录
     */
    public function googleLogin() {
        try {
            $input = $this->getJsonInput();

            if (empty($input['credential'])) {
                ApiResponse::error(['message' => 'Google credential is required'], 400);
                return;
            }

            // 验证Google JWT Token
            $googleUser = $this->verifyGoogleToken($input['credential']);
            if (!$googleUser) {
                ApiResponse::error(['message' => 'Invalid Google credential'], 401);
                return;
            }

            // 查找或创建用户
            $user = $this->userModel->findByEmail($googleUser['email']);
            if (!$user) {
                // 创建新用户
                $userData = [
                    'email' => $googleUser['email'],
                    'name' => $googleUser['name'],
                    'avatar' => $googleUser['picture'] ?? null,
                    'google_id' => $googleUser['sub'],
                    'email_verified' => 1,
                    'provider' => 'google'
                ];

                $userId = $this->userModel->create($userData);
                $user = $this->userModel->find($userId);
            } else {
                // 更新Google ID如果没有
                if (empty($user['google_id'])) {
                    $this->userModel->update($user['id'], ['google_id' => $googleUser['sub']]);
                }
            }

            // 生成Token
            $token = ApiAuth::generateJwtToken($user['id'], 'user', 3600 * 24);
            $refreshToken = ApiAuth::generateJwtToken($user['id'], 'user', 3600 * 24 * 7);

            ApiResponse::success([
                'user' => $this->formatUserData($user),
                'token' => $token,
                'refresh_token' => $refreshToken,
                'expires_in' => 3600 * 24,
                'token_type' => 'Bearer',
                'redirect' => '/'
            ], 'Google login successful');

        } catch (Exception $e) {
            error_log("Google login error: " . $e->getMessage());
            ApiResponse::error(['message' => 'Google login failed'], 500);
        }
    }

    /**
     * Google注册
     */
    public function googleRegister() {
        try {
            $input = $this->getJsonInput();

            if (empty($input['credential'])) {
                ApiResponse::error(['message' => 'Google credential is required'], 400);
                return;
            }

            // 验证Google JWT Token
            $googleUser = $this->verifyGoogleToken($input['credential']);
            if (!$googleUser) {
                ApiResponse::error(['message' => 'Invalid Google credential'], 401);
                return;
            }

            // 检查邮箱是否已存在
            if ($this->userModel->findByEmail($googleUser['email'])) {
                ApiResponse::error([
                    'message' => 'Email already exists. Please use login instead.',
                    'error_code' => 'EMAIL_EXISTS'
                ], 409);
                return;
            }

            // 创建新用户
            $userData = [
                'email' => $googleUser['email'],
                'name' => $googleUser['name'],
                'avatar' => $googleUser['picture'] ?? null,
                'google_id' => $googleUser['sub'],
                'email_verified' => 1,
                'provider' => 'google'
            ];

            $userId = $this->userModel->create($userData);
            $user = $this->userModel->find($userId);

            // 生成Token
            $token = ApiAuth::generateJwtToken($user['id'], 'user', 3600 * 24);
            $refreshToken = ApiAuth::generateJwtToken($user['id'], 'user', 3600 * 24 * 7);

            ApiResponse::success([
                'user' => $this->formatUserData($user),
                'token' => $token,
                'refresh_token' => $refreshToken,
                'expires_in' => 3600 * 24,
                'token_type' => 'Bearer',
                'redirect' => '/'
            ], 'Google registration successful', 201);

        } catch (Exception $e) {
            error_log("Google registration error: " . $e->getMessage());
            ApiResponse::error(['message' => 'Google registration failed'], 500);
        }
    }

    /**
     * 验证Google JWT Token
     */
    private function verifyGoogleToken($credential) {
        try {
            // 获取Google OAuth配置
            $clientId = $this->getGoogleClientId();
            if (!$clientId) {
                return false;
            }

            // 解码JWT (简化版本，生产环境应使用Google Client Library)
            $parts = explode('.', $credential);
            if (count($parts) !== 3) {
                return false;
            }

            $payload = json_decode(base64_decode(str_replace(['-', '_'], ['+', '/'], $parts[1])), true);

            // 验证基本字段
            if (!isset($payload['iss'], $payload['aud'], $payload['exp'], $payload['email'])) {
                return false;
            }

            // 验证issuer
            if (!in_array($payload['iss'], ['accounts.google.com', 'https://accounts.google.com'])) {
                return false;
            }

            // 验证audience
            if ($payload['aud'] !== $clientId) {
                return false;
            }

            // 验证过期时间
            if ($payload['exp'] < time()) {
                return false;
            }

            return $payload;

        } catch (Exception $e) {
            error_log("Google token verification error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 获取Google Client ID
     */
    private function getGoogleClientId() {
        try {
            // 使用统一的数据库连接
            require_once ROOT_PATH . '/includes/database-connection.php';
            global $pdo;
            $stmt = $pdo->prepare("SELECT setting_value FROM pt_system_config WHERE setting_key = 'google_oauth_client_id'");
            $stmt->execute();
            $result = $stmt->fetch();
            return $result ? $result['setting_value'] : null;
        } catch (Exception $e) {
            error_log("Failed to get Google Client ID: " . $e->getMessage());
            return null;
        }
    }

    /**
     * 刷新Token
     */
    public function refresh() {
        try {
            $input = $this->getJsonInput();
            
            if (empty($input['refresh_token'])) {
                ApiResponse::error([
                    'message' => 'Refresh token is required',
                    'error_code' => 'REFRESH_TOKEN_REQUIRED'
                ], 400);
                return;
            }
            
            $newToken = ApiAuth::refreshToken($input['refresh_token']);
            
            if (!$newToken) {
                ApiResponse::error([
                    'message' => 'Invalid refresh token',
                    'error_code' => 'INVALID_REFRESH_TOKEN'
                ], 401);
                return;
            }
            
            ApiResponse::success([
                'token' => $newToken,
                'expires_in' => 3600 * 24,
                'token_type' => 'Bearer'
            ], 'Token refreshed successfully');
            
        } catch (Exception $e) {
            ApiResponse::handleException($e);
        }
    }
    
    /**
     * 用户登出
     */
    public function logout() {
        try {
            $token = ApiAuth::getTokenFromRequest();
            
            if ($token) {
                ApiAuth::revokeToken($token);
            }
            
            ApiResponse::success(null, 'Logout successful');
            
        } catch (Exception $e) {
            ApiResponse::handleException($e);
        }
    }
    
    /**
     * 获取当前用户信息
     */
    public function me() {
        try {
            if (ApiAuth::isUser()) {
                $user = ApiAuth::getCurrentUser();
                ApiResponse::success([
                    'user' => $this->formatUserData($user),
                    'type' => 'user'
                ]);
            } elseif (ApiAuth::isAdmin()) {
                $admin = ApiAuth::getCurrentAdmin();
                ApiResponse::success([
                    'user' => $this->formatAdminData($admin),
                    'type' => 'admin'
                ]);
            } else {
                ApiResponse::unauthorized();
            }
            
        } catch (Exception $e) {
            ApiResponse::handleException($e);
        }
    }
    
    /**
     * 验证用户凭据
     */
    private function validateUserCredentials($email, $password) {
        $user = $this->userModel->findByEmail($email);
        if (!$user) {
            return false;
        }
        
        if ($user['status'] !== 'active') {
            throw new AuthException('Account is not active');
        }
        
        if (!$this->securityManager->verifyPassword($password, $user['password'])) {
            return false;
        }
        
        return $user;
    }
    
    /**
     * 验证注册输入
     */
    private function validateRegistrationInput($input) {
        $errors = [];
        
        if (empty($input['email'])) {
            $errors['email'] = 'Email is required';
        } elseif (!filter_var($input['email'], FILTER_VALIDATE_EMAIL)) {
            $errors['email'] = 'Invalid email format';
        }
        
        if (empty($input['password'])) {
            $errors['password'] = 'Password is required';
        }
        
        if (!empty($input['username']) && strlen($input['username']) < 3) {
            $errors['username'] = 'Username must be at least 3 characters';
        }
        
        return $errors;
    }
    
    /**
     * 格式化用户数据
     */
    private function formatUserData($user) {
        return [
            'id' => $user['id'],
            'username' => $user['username'],
            'email' => $user['email'],
            'first_name' => $user['first_name'],
            'last_name' => $user['last_name'],
            'avatar' => $user['avatar'],
            'country' => $user['country'],
            'city' => $user['city'],
            'timezone' => $user['timezone'],
            'language' => $user['language'],
            'subscription_type' => $user['subscription_type'],
            'subscription_expires_at' => $user['subscription_expires_at'],
            'email_verified_at' => $user['email_verified_at'],
            'created_at' => $user['created_at'],
            'last_login_at' => $user['last_login_at']
        ];
    }
    
    /**
     * 格式化管理员数据
     */
    private function formatAdminData($admin) {
        return [
            'id' => $admin['id'],
            'username' => $admin['username'],
            'email' => $admin['email'],
            'first_name' => $admin['first_name'],
            'last_name' => $admin['last_name'],
            'role' => $admin['role'],
            'avatar' => $admin['avatar'],
            'department' => $admin['department'],
            'position' => $admin['position'],
            'created_at' => $admin['created_at'],
            'last_login_at' => $admin['last_login_at']
        ];
    }
    
    /**
     * 获取JSON输入
     */
    private function getJsonInput() {
        $input = json_decode(file_get_contents('php://input'), true);
        return $input ?: [];
    }
    
    /**
     * 获取客户端IP
     */
    private function getClientIp() {
        return $_SERVER['HTTP_X_FORWARDED_FOR'] ?? $_SERVER['REMOTE_ADDR'] ?? '';
    }
}
?>
