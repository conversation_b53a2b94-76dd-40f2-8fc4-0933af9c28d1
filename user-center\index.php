<?php
/**
 * 用户中心入口文件
 */

// 安全检查
if (!defined('SECURE_ACCESS')) {
    define('SECURE_ACCESS', true);
}

// 定义用户中心根目录
define('USER_CENTER_ROOT', __DIR__);
define('ROOT_PATH', dirname(__DIR__));

// 加载应用初始化
require_once ROOT_PATH . '/app/init.php';

// 检查用户登录状态
if (!isset($_SESSION['user_id'])) {
    header('Location: /auth/login');
    exit;
}

// 使用统一的数据库连接
require_once ROOT_PATH . '/includes/database-connection.php';

// 获取用户信息
try {
    
    $stmt = $pdo->prepare("SELECT * FROM pt_member WHERE id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        session_destroy();
        header('Location: /auth/login');
        exit;
    }
    
} catch (Exception $e) {
    error_log("User center error: " . $e->getMessage());
    header('Location: /auth/login');
    exit;
}

// 获取当前页面
$page = $_GET['page'] ?? 'dashboard';

// 允许的页面列表
$allowedPages = ['dashboard', 'profile', 'api-usage', 'my-requests', 'my-products', 'edit-product', 'prompt-generator', 'tool-generator', 'my-tools'];
if (!in_array($page, $allowedPages)) {
    $page = 'dashboard';
}

// 页面标题映射
$pageTitles = [
    'dashboard' => 'Dashboard',
    'profile' => 'Profile',
    'api-usage' => 'Usage',
    'my-requests' => 'Requests',
    'my-products' => 'Products',
    'edit-product' => 'Edit Product',
    'prompt-generator' => 'Prompts',
    'tool-generator' => 'Tools',
    'my-tools' => 'My Tools'
];

// 编辑产品页面直接使用 edit-product
// 不需要重定向了

$pageTitle = $pageTitles[$page] ?? 'User Center';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($pageTitle) ?> - User Center</title>
    <meta name="description" content="User dashboard for managing your account and accessing AI tools.">
    
    <!-- 安全头部 -->
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta http-equiv="X-XSS-Protection" content="1; mode=block">
    <meta name="referrer" content="strict-origin-when-cross-origin">
    
    <!-- 禁止搜索引擎索引 -->
    <meta name="robots" content="noindex, nofollow, noarchive, nosnippet">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- 自定义配置 -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#000000',
                        secondary: '#6b7280',
                        accent: '#2563eb',
                        success: '#059669',
                        warning: '#d97706',
                        danger: '#dc2626'
                    },
                    borderRadius: {
                        'none': '0',
                        DEFAULT: '0'
                    }
                }
            }
        }
    </script>

    <!-- 自定义样式 -->
    <style>
        /* 强制所有元素无圆角 */
        * {
            border-radius: 0 !important;
        }

        /* 卡片样式 */
        .replicate-card {
            background: white;
            border: 1px solid #e5e7eb;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
            transition: all 0.2s ease;
        }
        
        .replicate-card:hover {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        
        /* 按钮样式 */
        .replicate-btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.2s ease;
            border: 1px solid transparent;
            font-size: 0.875rem;
        }

        .replicate-btn-primary {
            background: #000000;
            color: white;
            border-color: #000000;
        }

        .replicate-btn-primary:hover {
            background: #1f2937;
            border-color: #1f2937;
        }

        .replicate-btn-secondary {
            background: white;
            color: #374151;
            border-color: #d1d5db;
        }

        .replicate-btn-secondary:hover {
            background: #f9fafb;
            border-color: #9ca3af;
        }

        .replicate-btn-accent {
            background: #2563eb;
            color: white;
            border-color: #2563eb;
        }

        .replicate-btn-accent:hover {
            background: #1d4ed8;
            border-color: #1d4ed8;
        }

        /* 侧边栏链接 */
        .sidebar-link {
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            padding: 0.75rem 1rem;
            color: #6b7280;
            text-decoration: none;
            font-weight: 500;
            font-size: 0.875rem;
        }
        
        .sidebar-link:hover {
            background-color: #f3f4f6;
            color: #1f2937;
        }
        
        .sidebar-link.active {
            background-color: #000000;
            color: white;
        }

        /* 统计卡片 */
        .stat-card {
            transition: transform 0.2s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-1px);
        }

        /* 主体背景 */
        body {
            background-color: #ffffff;
        }

        /* 内容区域背景 */
        .content-area {
            background-color: #f9fafb;
        }

        /* 标签样式 */
        .replicate-badge {
            display: inline-flex;
            align-items: center;
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .replicate-badge-success {
            background: #dcfce7;
            color: #166534;
        }

        .replicate-badge-warning {
            background: #fef3c7;
            color: #92400e;
        }

        .replicate-badge-danger {
            background: #fee2e2;
            color: #991b1b;
        }

        .replicate-badge-info {
            background: #dbeafe;
            color: #1e40af;
        }

        /* 禁用按钮样式 - 彻底禁止透明 */
        .replicate-btn:disabled,
        .replicate-btn[disabled],
        button:disabled,
        button[disabled] {
            opacity: 1 !important;
            cursor: not-allowed !important;
            background: #9ca3af !important;
            border-color: #9ca3af !important;
            color: white !important;
        }

        .replicate-btn:disabled:hover,
        .replicate-btn[disabled]:hover,
        button:disabled:hover,
        button[disabled]:hover {
            background: #6b7280 !important;
            border-color: #6b7280 !important;
            color: white !important;
            opacity: 1 !important;
            transform: none !important;
            box-shadow: none !important;
        }

        /* 强制覆盖所有可能的透明样式 */
        .replicate-btn,
        .replicate-btn:hover,
        .replicate-btn:focus,
        .replicate-btn:active,
        .replicate-btn:disabled,
        .replicate-btn[disabled] {
            opacity: 1 !important;
        }

        /* 特殊状态按钮强制不透明 */
        .btn-success,
        .btn-error,
        .btn-loading {
            opacity: 1 !important;
        }

        .btn-success:hover,
        .btn-error:hover,
        .btn-loading:hover,
        .btn-success:focus,
        .btn-error:focus,
        .btn-loading:focus,
        .btn-success:active,
        .btn-error:active,
        .btn-loading:active {
            opacity: 1 !important;
        }

        /* 全局防透明规则 - 覆盖任何可能的透明样式 */
        button,
        .replicate-btn,
        input[type="button"],
        input[type="submit"] {
            opacity: 1 !important;
        }

        button:hover,
        .replicate-btn:hover,
        input[type="button"]:hover,
        input[type="submit"]:hover {
            opacity: 1 !important;
        }

        /* 防止Tailwind或其他框架的透明类 */
        .opacity-50,
        .opacity-75,
        .opacity-25 {
            opacity: 1 !important;
        }

        /* 特别针对复制按钮和小按钮的透明问题 */
        button[onclick*="copyApiKey"],
        button[onclick*="deleteApiKey"],
        .text-gray-400,
        .text-gray-500,
        .text-gray-600 {
            opacity: 1 !important;
        }

        button[onclick*="copyApiKey"]:hover,
        button[onclick*="deleteApiKey"]:hover,
        .text-gray-400:hover,
        .text-gray-500:hover,
        .text-gray-600:hover {
            opacity: 1 !important;
        }

        /* 强制所有按钮状态不透明 */
        button:disabled,
        button[disabled],
        button:disabled:hover,
        button[disabled]:hover {
            opacity: 1 !important;
        }

        /* 特殊状态按钮的强制样式 */
        .btn-success:disabled,
        .btn-error:disabled,
        .btn-loading:disabled,
        .btn-success:disabled:hover,
        .btn-error:disabled:hover,
        .btn-loading:disabled:hover {
            opacity: 1 !important;
        }

        /* 成功状态按钮 */
        .btn-success {
            background: #059669 !important;
            border-color: #059669 !important;
            color: white !important;
            opacity: 1 !important;
            cursor: pointer !important;
            transition: all 0.2s ease !important;
        }

        .btn-success:hover {
            background: #047857 !important;
            border-color: #047857 !important;
            color: white !important;
            opacity: 1 !important;
            transform: translateY(-1px) !important;
            box-shadow: 0 4px 8px rgba(5, 150, 105, 0.3) !important;
        }

        /* 错误状态按钮 */
        .btn-error {
            background: #dc2626 !important;
            border-color: #dc2626 !important;
            color: white !important;
            opacity: 1 !important;
            cursor: pointer !important;
            transition: all 0.2s ease !important;
        }

        .btn-error:hover {
            background: #b91c1c !important;
            border-color: #b91c1c !important;
            color: white !important;
            opacity: 1 !important;
            transform: translateY(-1px) !important;
            box-shadow: 0 4px 8px rgba(220, 38, 38, 0.3) !important;
        }

        /* 加载状态按钮 */
        .btn-loading {
            background: #6b7280 !important;
            border-color: #6b7280 !important;
            color: white !important;
            opacity: 1 !important;
            cursor: wait !important;
            transition: all 0.2s ease !important;
        }

        .btn-loading:hover {
            background: #4b5563 !important;
            border-color: #4b5563 !important;
            color: white !important;
            opacity: 1 !important;
            transform: none !important;
            box-shadow: 0 2px 4px rgba(107, 114, 128, 0.3) !important;
        }
    </style>
</head>
<body class="bg-white">

<!-- 主容器 -->
<div class="flex h-screen bg-white">
    <!-- 侧边栏 -->
    <div class="w-64 bg-white border-r border-gray-200 flex-shrink-0">
        <?php include 'components/sidebar.php'; ?>
    </div>

    <!-- 主内容区域 -->
    <div class="flex-1 flex flex-col overflow-hidden">
        <!-- 顶部导航 -->
        <header class="bg-white border-b border-gray-200">
            <?php include 'components/header.php'; ?>
        </header>

        <!-- 页面内容 -->
        <main class="flex-1 overflow-x-hidden overflow-y-auto content-area">
            <div class="max-w-7xl mx-auto px-6 py-8">
                <?php
                // 包含页面内容
                $pageFile = "pages/{$page}.php";
                $fullPath = __DIR__ . "/pages/{$page}.php";

                if (file_exists($fullPath)) {
                    include $fullPath;
                } else {
                    echo '<div class="text-center py-12">';
                    echo '<h2 class="text-2xl font-bold text-gray-900 mb-4">Page Not Found</h2>';
                    echo '<p class="text-gray-600">The requested page could not be found.</p>';
                    echo '</div>';
                }
                ?>
            </div>
        </main>
    </div>
</div>

</body>
</html>
