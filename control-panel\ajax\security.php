<?php
/**
 * Security AJAX Handler
 */

// Start session
session_start();

// Security check
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

// Only handle POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// 使用统一的数据库连接
require_once dirname(dirname(__DIR__)) . '/includes/database-connection.php';
require_once __DIR__ . '/../includes/GoogleAuthenticator.php';

// Set JSON header
header('Content-Type: application/json');

$action = $_POST['action'] ?? '';
$response = ['success' => false, 'message' => ''];

switch ($action) {
    case 'toggle_2fa':
        $enabled = $_POST['enabled'] === 'true';
        $adminId = $_SESSION['admin_id'] ?? 1;

        try {
            if ($enabled) {
                // Generate new secret for 2FA (but don't enable yet)
                $ga = new GoogleAuthenticator();
                $secret = $ga->createSecret();

                // Store secret temporarily but don't enable 2FA yet
                $sql = "UPDATE pt_manager SET two_factor_secret = ? WHERE id = ?";
                $stmt = $pdo->prepare($sql);
                $stmt->execute([$secret, $adminId]);

                // Generate QR code URL
                $siteName = 'Prompt2Tool';
                $userEmail = $_SESSION['admin_email'] ?? '<EMAIL>';
                $qrCodeUrl = $ga->getQRCodeGoogleUrl($userEmail, $secret, $siteName);

                $response = [
                    'success' => true,
                    'message' => 'Please scan the QR code and verify with your authenticator app.',
                    'qr_code' => $qrCodeUrl,
                    'secret' => $secret,
                    'manual_entry' => "Site: $siteName\nAccount: $userEmail\nKey: $secret",
                    'setup_mode' => true
                ];
            } else {
                // Disable 2FA
                $sql = "UPDATE pt_manager SET two_factor_enabled = 0, two_factor_secret = NULL WHERE id = ?";
                $stmt = $pdo->prepare($sql);
                $stmt->execute([$adminId]);

                $response = [
                    'success' => true,
                    'message' => '2FA disabled successfully!'
                ];
            }
        } catch (PDOException $e) {
            error_log("Database error in 2FA toggle: " . $e->getMessage());
            $response = [
                'success' => false,
                'message' => 'Database error occurred. Please try again.'
            ];
        }
        break;

    case 'verify_2fa':
        $code = $_POST['code'] ?? '';
        $adminId = $_SESSION['admin_id'] ?? 1;

        try {
            // Get user's 2FA secret
            $sql = "SELECT two_factor_secret FROM pt_manager WHERE id = ?";
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$adminId]);
            $secret = $stmt->fetchColumn();

            if (!$secret) {
                $response = ['success' => false, 'message' => '2FA not set up for this account.'];
                break;
            }

            $ga = new GoogleAuthenticator();
            if ($ga->verifyCode($secret, $code)) {
                // Only now enable 2FA after successful verification
                $enableSql = "UPDATE pt_manager SET two_factor_enabled = 1 WHERE id = ?";
                $enableStmt = $pdo->prepare($enableSql);
                $enableStmt->execute([$adminId]);

                $response = ['success' => true, 'message' => '2FA enabled successfully!'];
            } else {
                $response = ['success' => false, 'message' => 'Invalid 2FA code. Please try again.'];
            }
        } catch (PDOException $e) {
            error_log("Database error in 2FA verification: " . $e->getMessage());
            $response = ['success' => false, 'message' => 'Database error occurred.'];
        }
        break;

    case 'verify_2fa_disable':
        $code = $_POST['code'] ?? '';
        $adminId = $_SESSION['admin_id'] ?? 1;

        try {
            // Get user's 2FA secret
            $sql = "SELECT two_factor_secret FROM pt_manager WHERE id = ? AND two_factor_enabled = 1";
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$adminId]);
            $secret = $stmt->fetchColumn();

            if (!$secret) {
                $response = ['success' => false, 'message' => '2FA not enabled for this account.'];
                break;
            }

            $ga = new GoogleAuthenticator();
            if ($ga->verifyCode($secret, $code)) {
                $response = ['success' => true, 'message' => '2FA code verified. Proceeding to disable 2FA.'];
            } else {
                $response = ['success' => false, 'message' => 'Invalid 2FA code. Please try again.'];
            }
        } catch (PDOException $e) {
            error_log("Database error in 2FA disable verification: " . $e->getMessage());
            $response = ['success' => false, 'message' => 'Database error occurred.'];
        }
        break;

    case 'cancel_2fa_setup':
        $adminId = $_SESSION['admin_id'] ?? 1;

        try {
            // Remove the temporary secret if setup is cancelled
            $sql = "UPDATE pt_manager SET two_factor_secret = NULL WHERE id = ? AND two_factor_enabled = 0";
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$adminId]);

            $response = ['success' => true, 'message' => '2FA setup cancelled.'];
        } catch (PDOException $e) {
            error_log("Database error in 2FA cancel: " . $e->getMessage());
            $response = ['success' => false, 'message' => 'Database error occurred.'];
        }
        break;
        
    case 'revoke_session':
        $sessionId = $_POST['session_id'] ?? '';
        $adminId = $_SESSION['admin_id'] ?? 1;

        if (empty($sessionId)) {
            $response = ['success' => false, 'message' => 'Session ID is required'];
            break;
        }

        try {
            // Revoke specific session (but not current session)
            $currentSessionId = session_id();
            if ($sessionId === $currentSessionId) {
                $response = ['success' => false, 'message' => 'Cannot revoke current session'];
                break;
            }

            $sql = "UPDATE pt_manager_session SET is_active = 0 WHERE session_id = ? AND manager_id = ?";
            $stmt = $pdo->prepare($sql);
            $result = $stmt->execute([$sessionId, $adminId]);

            if ($result && $stmt->rowCount() > 0) {
                $response = ['success' => true, 'message' => 'Session revoked successfully!'];
            } else {
                $response = ['success' => false, 'message' => 'Session not found or already revoked'];
            }
        } catch (PDOException $e) {
            error_log("Session revoke error: " . $e->getMessage());
            $response = ['success' => false, 'message' => 'Database error occurred'];
        }
        break;

    case 'revoke_all_sessions':
        $adminId = $_SESSION['admin_id'] ?? 1;
        $currentSessionId = session_id();

        try {
            // Revoke all sessions except current one
            $sql = "UPDATE pt_manager_session SET is_active = 0 WHERE manager_id = ? AND session_id != ?";
            $stmt = $pdo->prepare($sql);
            $result = $stmt->execute([$adminId, $currentSessionId]);

            $revokedCount = $stmt->rowCount();
            $response = ['success' => true, 'message' => "Revoked {$revokedCount} other sessions successfully!"];
        } catch (PDOException $e) {
            error_log("Sessions revoke error: " . $e->getMessage());
            $response = ['success' => false, 'message' => 'Database error occurred'];
        }
        break;
        
    default:
        $response = ['success' => false, 'message' => 'Invalid action'];
        break;
}

echo json_encode($response);
exit;
?>
