<?php
/**
 * 管理后台配置文件
 * 包含后台系统的核心配置参数
 */

// 防止直接访问
if (!defined('ROOT_PATH')) {
    exit('Direct access not allowed');
}

return [
    // 后台基础配置
    'admin' => [
        'name' => 'Prompt2Tool Control Panel',
        'version' => '1.0.0',
        'description' => 'Advanced management system for Prompt2Tool platform',
        'timezone' => 'UTC',
        'locale' => 'en_US',
        'theme' => 'dark'
    ],

    // 安全配置
    'security' => [
        'session_timeout' => 3600, // 1小时会话超时
        'max_login_attempts' => 5,  // 最大登录尝试次数
        'lockout_duration' => 900,  // 锁定时长(秒)
        'password_min_length' => 8,
        'require_2fa' => false,     // 是否要求双因素认证
        'ip_whitelist' => [],       // IP白名单
        'csrf_protection' => true,
        'secure_cookies' => true
    ],

    // 权限配置
    'permissions' => [
        'super_admin' => [
            'level' => 100,
            'permissions' => ['*'] // 所有权限
        ],
        'admin' => [
            'level' => 80,
            'permissions' => [
                'dashboard.view',
                'tools.*',
                'users.view',
                'users.edit',
                'analytics.view',
                'settings.view',
                'logs.view'
            ]
        ],
        'editor' => [
            'level' => 60,
            'permissions' => [
                'dashboard.view',
                'tools.view',
                'tools.create',
                'tools.edit',
                'analytics.view'
            ]
        ],
        'viewer' => [
            'level' => 40,
            'permissions' => [
                'dashboard.view',
                'tools.view',
                'analytics.view'
            ]
        ]
    ],

    // 分页配置
    'pagination' => [
        'default_per_page' => 20,
        'max_per_page' => 100,
        'show_page_numbers' => 5
    ],

    // 文件上传配置
    'upload' => [
        'max_file_size' => 10 * 1024 * 1024, // 10MB
        'allowed_types' => ['jpg', 'jpeg', 'png', 'gif', 'svg', 'webp'],
        'upload_path' => '/uploads/admin/',
        'create_thumbnails' => true,
        'thumbnail_sizes' => [
            'small' => [150, 150],
            'medium' => [300, 300],
            'large' => [600, 600]
        ]
    ],

    // 缓存配置
    'cache' => [
        'driver' => 'redis', // redis, file, memory
        'ttl' => 3600,       // 默认缓存时间
        'prefix' => 'admin_',
        'redis' => [
            'host' => '127.0.0.1',
            'port' => 6379,
            'database' => 1
        ]
    ],

    // 日志配置
    'logging' => [
        'enabled' => true,
        'level' => 'info', // debug, info, warning, error
        'max_files' => 30, // 保留日志文件数量
        'log_queries' => false, // 是否记录SQL查询
        'log_requests' => true, // 是否记录请求日志
        'channels' => [
            'access' => '/var/log/admin/access.log',
            'error' => '/var/log/admin/error.log',
            'security' => '/var/log/admin/security.log',
            'admin_actions' => '/var/log/admin/actions.log'
        ]
    ],

    // 邮件配置
    'mail' => [
        'driver' => 'smtp',
        'host' => 'smtp.gmail.com',
        'port' => 587,
        'encryption' => 'tls',
        'username' => '',
        'password' => '',
        'from_address' => '<EMAIL>',
        'from_name' => 'Prompt2Tool Admin'
    ],

    // 备份配置
    'backup' => [
        'enabled' => true,
        'schedule' => 'daily', // daily, weekly, monthly
        'retention_days' => 30,
        'backup_path' => '/backup/admin/',
        'include_uploads' => true,
        'compress' => true,
        'cloud_storage' => [
            'enabled' => false,
            'provider' => 's3', // s3, gcs, azure
            'bucket' => 'prompt2tool-backups',
            'path' => 'admin/'
        ]
    ],

    // API配置
    'api' => [
        'rate_limit' => [
            'enabled' => true,
            'requests_per_minute' => 60,
            'requests_per_hour' => 1000
        ],
        'authentication' => [
            'token_expiry' => 86400, // 24小时
            'refresh_token_expiry' => 604800 // 7天
        ]
    ],

    // 监控配置
    'monitoring' => [
        'enabled' => true,
        'performance_threshold' => 2.0, // 响应时间阈值(秒)
        'memory_threshold' => 128, // 内存使用阈值(MB)
        'disk_threshold' => 80,    // 磁盘使用阈值(%)
        'alerts' => [
            'email' => '<EMAIL>',
            'slack_webhook' => '',
            'telegram_bot_token' => '',
            'telegram_chat_id' => ''
        ]
    ],

    // 维护模式配置
    'maintenance' => [
        'enabled' => false,
        'message' => 'System is under maintenance. Please try again later.',
        'allowed_ips' => ['127.0.0.1'], // 维护期间允许访问的IP
        'retry_after' => 3600 // 建议重试时间(秒)
    ]
];
?>
