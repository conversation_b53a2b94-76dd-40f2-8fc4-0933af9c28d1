<?php
/**
 * 应用程序初始化文件
 * 负责加载配置、设置环境、定义常量等
 */

// 防止直接访问 - 检查是否通过正确的入口访问
if (!defined('ROOT_PATH')) {
    // 如果ROOT_PATH未定义，尝试从当前文件位置推断
    define('ROOT_PATH', dirname(__DIR__));
}

// 定义应用初始化标记 (必须在加载配置文件之前)
define('APP_INITIALIZED', true);

// 设置时区
date_default_timezone_set('UTC');

// 定义应用常量
define('APP_VERSION', '1.0.0');
define('APP_NAME', 'Prompt2Tool');
define('APP_ENV', 'development');

// 动态获取应用URL
$protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
$host = $_SERVER['HTTP_HOST'] ?? 'localhost';
define('APP_URL', $protocol . '://' . $host);

define('APP_DEBUG', APP_ENV === 'development');

// 定义路径常量
define('ASSETS_PATH', '/assets');
define('UPLOADS_PATH', ROOT_PATH . '/public/uploads');
define('CACHE_PATH', ROOT_PATH . '/cache');

// 创建必要的目录
$directories = [
    ROOT_PATH . '/app/helpers',
    ROOT_PATH . '/config',
    ROOT_PATH . '/templates/pages',
    UPLOADS_PATH,
    CACHE_PATH
];

foreach ($directories as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
}

// 加载配置文件 (如果存在)
$configFiles = [
    ROOT_PATH . '/config/app.php',
    ROOT_PATH . '/config/database.php'
];

foreach ($configFiles as $configFile) {
    if (file_exists($configFile)) {
        require_once $configFile;
    }
}

// 加载核心函数库
$helpersFile = ROOT_PATH . '/app/helpers/functions.php';
if (file_exists($helpersFile)) {
    require_once $helpersFile;
}

$seoHelpersFile = ROOT_PATH . '/app/helpers/seo-helpers.php';
if (file_exists($seoHelpersFile)) {
    require_once $seoHelpersFile;
}

$toolHelpersFile = ROOT_PATH . '/app/helpers/tool-helpers.php';
if (file_exists($toolHelpersFile)) {
    require_once $toolHelpersFile;
}

// 设置错误处理
if (APP_ENV === 'development') {
    ini_set('display_errors', 1);
    ini_set('display_startup_errors', 1);
    error_reporting(E_ALL);
} else {
    ini_set('display_errors', 0);
    ini_set('display_startup_errors', 0);
    error_reporting(0);
}

// 禁用日志文件 (节省磁盘空间)
ini_set('log_errors', 0);
ini_set('error_log', null);

// 设置安全头部
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');
header('Referrer-Policy: strict-origin-when-cross-origin');

// 启动会话 (安全配置)
if (session_status() === PHP_SESSION_NONE) {
    // 会话安全设置
    ini_set('session.cookie_httponly', 1);
    ini_set('session.cookie_secure', isset($_SERVER['HTTPS']) ? 1 : 0);
    ini_set('session.use_strict_mode', 1);

    session_start();
}

// 设置默认内容类型
header('Content-Type: text/html; charset=UTF-8');
