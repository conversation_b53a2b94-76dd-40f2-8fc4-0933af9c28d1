<?php
/**
 * 编辑产品发布页面
 */

// 检查管理员权限
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: auth/login.php');
    exit;
}

$currentPage = 'launches';
$productId = $_GET['id'] ?? null;

if (!$productId) {
    header('Location: ?page=launches');
    exit;
}

// 使用统一的数据库连接
require_once dirname(dirname(__DIR__)) . '/includes/database-connection.php';

try {
    // 获取产品信息
    $stmt = $pdo->prepare("
        SELECT l.*, m.username, m.email 
        FROM pt_product_launches l
        LEFT JOIN pt_member m ON l.user_id = m.id
        WHERE l.id = ?
    ");
    $stmt->execute([$productId]);
    $product = $stmt->fetch();

    if (!$product) {
        header('Location: ?page=launches');
        exit;
    }

    // 获取分类列表
    $categoryStmt = $pdo->query("
        SELECT slug, name
        FROM pt_launch_categories
        WHERE is_active = 1
        ORDER BY sort_order ASC, name ASC
    ");
    $categories = [];
    while ($row = $categoryStmt->fetch()) {
        $categories[$row['slug']] = $row['name'];
    }

    // 获取技术分类列表
    $techCategoryStmt = $pdo->query("
        SELECT slug, name
        FROM pt_tech_categories
        WHERE is_active = 1
        ORDER BY sort_order ASC, name ASC
    ");
    $techCategories = [];
    while ($row = $techCategoryStmt->fetch()) {
        $techCategories[$row['slug']] = $row['name'];
    }

    // 处理表单提交
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        try {
            $pdo->beginTransaction();

            // 检查slug唯一性（排除当前产品）
            $slugCheckStmt = $pdo->prepare("SELECT id FROM pt_product_launches WHERE slug = ? AND id != ?");
            $slugCheckStmt->execute([$_POST['slug'], $productId]);
            if ($slugCheckStmt->fetch()) {
                throw new Exception('Slug already exists. Please choose a different slug.');
            }

            // 处理文本输入转换为JSON格式
            $tags = array_filter(array_map('trim', explode(',', $_POST['tags'] ?? '')));
            $keyFeatures = array_filter(array_map('trim', explode(',', $_POST['key_features'] ?? '')));
            $useCases = array_filter(array_map('trim', explode(',', $_POST['use_cases'] ?? '')));
            $techStack = array_filter(array_map('trim', explode(',', $_POST['tech_stack'] ?? '')));
            $screenshots = array_filter(array_map('trim', explode(',', $_POST['screenshots'] ?? '')));

            // 处理社交链接
            $socialLinks = [];
            if (!empty($_POST['social_twitter'])) $socialLinks['twitter'] = $_POST['social_twitter'];
            if (!empty($_POST['social_linkedin'])) $socialLinks['linkedin'] = $_POST['social_linkedin'];
            if (!empty($_POST['social_github'])) $socialLinks['github'] = $_POST['social_github'];
            if (!empty($_POST['social_website'])) $socialLinks['website'] = $_POST['social_website'];

            // 更新产品信息
            $updateStmt = $pdo->prepare("
                UPDATE pt_product_launches SET
                    name = ?,
                    slug = ?,
                    tagline = ?,
                    description = ?,
                    website_url = ?,
                    logo_url = ?,
                    category = ?,
                    tags = ?,
                    key_features = ?,
                    target_audience = ?,
                    use_cases = ?,
                    pricing_model = ?,
                    launch_status = ?,
                    screenshots = ?,
                    tech_stack = ?,
                    tech_category = ?,
                    social_links = ?,
                    video_tutorial_url = ?,
                    innovation_score = ?,
                    status = ?,
                    admin_notes = ?,
                    featured_date = ?,
                    updated_at = NOW()
                WHERE id = ?
            ");

            // 处理精选日期
            $featuredDate = null;
            if ($_POST['status'] === 'approved' && isset($_POST['is_featured']) && $_POST['is_featured'] === '1') {
                $featuredDate = $_POST['featured_date'] ?: date('Y-m-d');
            }

            $updateStmt->execute([
                $_POST['name'],
                $_POST['slug'],
                $_POST['tagline'],
                $_POST['description'],
                $_POST['website_url'],
                $_POST['logo_url'],
                $_POST['category'],
                json_encode($tags),
                json_encode($keyFeatures),
                $_POST['target_audience'],
                json_encode($useCases),
                $_POST['pricing_model'],
                $_POST['launch_status'],
                json_encode($screenshots),
                json_encode($techStack),
                $_POST['tech_category'],
                json_encode($socialLinks),
                $_POST['video_tutorial_url'],
                $_POST['innovation_score'],
                $_POST['status'],
                $_POST['admin_notes'],
                $featuredDate,
                $productId
            ]);

            // 记录管理员操作日志
            $managerId = $_SESSION['manager_id'] ?? $_SESSION['admin_id'] ?? 1;
            $logStmt = $pdo->prepare("
                INSERT INTO pt_activity_log (manager_id, action, description, ip_address, user_agent, created_at)
                VALUES (?, ?, ?, ?, ?, NOW())
            ");
            $logStmt->execute([
                $managerId,
                'edit_launch',
                "Edited launch '{$_POST['name']}' (ID: {$productId})",
                $_SERVER['REMOTE_ADDR'] ?? null,
                $_SERVER['HTTP_USER_AGENT'] ?? null
            ]);

            $pdo->commit();
            $success = "Product updated successfully!";
            
            // 重新获取更新后的数据
            $stmt->execute([$productId]);
            $product = $stmt->fetch();

        } catch (Exception $e) {
            $pdo->rollback();
            $error = "Error updating product: " . $e->getMessage();
        }
    }

} catch (Exception $e) {
    $error = "Database error: " . $e->getMessage();
}
?>

<div class="space-y-6">
    <!-- 页面标题和导航 -->
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Edit Product Launch</h1>
            <p class="text-gray-600">Edit product information and settings</p>
        </div>
        <a href="?page=launches" class="bg-blue-600 text-white px-4 py-2 hover:bg-blue-700 rounded">
            <i class="fas fa-arrow-left mr-2"></i>Back to Launches
        </a>
    </div>

    <!-- 成功/错误消息 -->
    <?php if (isset($success)): ?>
        <div class="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded">
            <i class="fas fa-check-circle mr-2"></i><?= htmlspecialchars($success) ?>
        </div>
    <?php endif; ?>

    <?php if (isset($error)): ?>
        <div class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
            <i class="fas fa-exclamation-circle mr-2"></i><?= htmlspecialchars($error) ?>
        </div>
    <?php endif; ?>

    <!-- 产品信息卡片 -->
    <div class="bg-white rounded-lg shadow p-6">
        <div class="flex items-center space-x-4 mb-6">
            <?php if (!empty($product['logo_url'])): ?>
                <img src="<?= htmlspecialchars($product['logo_url']) ?>" 
                     alt="<?= htmlspecialchars($product['name']) ?> logo"
                     class="w-16 h-16 rounded-lg object-cover border border-gray-200">
            <?php else: ?>
                <div class="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center">
                    <i class="fas fa-rocket text-gray-400 text-xl"></i>
                </div>
            <?php endif; ?>
            <div>
                <h2 class="text-xl font-semibold text-gray-900"><?= htmlspecialchars($product['name']) ?></h2>
                <p class="text-gray-600">Created by: <?= htmlspecialchars($product['username']) ?> (<?= htmlspecialchars($product['email']) ?>)</p>
                <p class="text-sm text-gray-500">Created: <?= date('M j, Y', strtotime($product['created_at'])) ?></p>
            </div>
        </div>

        <!-- 编辑表单 -->
        <form method="POST" class="space-y-6">
            <!-- 基本信息 -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Product Name *</label>
                    <input type="text" name="name" value="<?= htmlspecialchars($product['name'] ?? '') ?>"
                           class="w-full border border-gray-300 rounded-md px-3 py-2" required>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Slug *</label>
                    <div class="relative">
                        <input type="text" name="slug" id="slug-input" value="<?= htmlspecialchars($product['slug'] ?? '') ?>"
                               class="w-full border border-gray-300 rounded-md px-3 py-2 pr-10" required>
                        <div id="slug-status" class="absolute right-3 top-1/2 transform -translate-y-1/2">
                            <i id="slug-icon" class="fas fa-spinner fa-spin text-gray-400 hidden"></i>
                        </div>
                    </div>
                    <p id="slug-message" class="text-xs mt-1">URL-friendly identifier (will be checked for uniqueness)</p>
                </div>
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Tagline</label>
                <input type="text" name="tagline" value="<?= htmlspecialchars($product['tagline'] ?? '') ?>"
                       class="w-full border border-gray-300 rounded-md px-3 py-2"
                       placeholder="Brief description of your product">
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Description *</label>
                <textarea name="description" rows="4" class="w-full border border-gray-300 rounded-md px-3 py-2" required><?= htmlspecialchars($product['description'] ?? '') ?></textarea>
            </div>

            <!-- URLs -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Website URL *</label>
                    <input type="url" name="website_url" value="<?= htmlspecialchars($product['website_url'] ?? '') ?>"
                           class="w-full border border-gray-300 rounded-md px-3 py-2" required>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Logo</label>

                    <!-- 当前Logo预览 -->
                    <div id="logo-preview" class="mb-4 <?= empty($product['logo_url']) ? 'hidden' : '' ?>">
                        <div class="relative inline-block">
                            <?php
                            $logoSrc = $product['logo_url'] ?? '';
                            // 如果是相对路径，转换为完整URL
                            if ($logoSrc && !preg_match('/^https?:\/\//', $logoSrc)) {
                                $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
                                $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
                                $logoSrc = $protocol . '://' . $host . $logoSrc;
                            }
                            ?>
                            <img id="logo-image" src="<?= htmlspecialchars($logoSrc) ?>"
                                 alt="Product Logo" class="w-24 h-24 object-cover rounded-lg border border-gray-200">
                            <button type="button" onclick="removeLogo()"
                                    class="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors">
                                <i class="fas fa-times text-xs"></i>
                            </button>
                        </div>
                        <p class="text-xs text-gray-500 mt-1">Current logo</p>
                    </div>

                    <!-- Logo URL输入 -->
                    <div class="space-y-2">
                        <input type="text" name="logo_url" id="logo-url-input" value="<?= htmlspecialchars($product['logo_url'] ?? '') ?>"
                               class="w-full border border-gray-300 rounded-md px-3 py-2"
                               placeholder="Enter logo URL or upload a file">
                        <div class="flex space-x-2">
                            <button type="button" onclick="uploadLogo()"
                                    class="px-3 py-1 bg-green-500 text-white text-sm rounded hover:bg-green-600 transition-colors">
                                Upload New
                            </button>
                        </div>
                    </div>

                    <!-- 隐藏的文件上传输入 -->
                    <input type="file" id="logo-file-input" accept="image/*" class="hidden" onchange="handleLogoUpload(this)">
                </div>
            </div>

            <!-- 分类和状态 -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Category *</label>
                    <select name="category" class="w-full border border-gray-300 rounded-md px-3 py-2" required>
                        <?php foreach ($categories as $slug => $name): ?>
                            <option value="<?= $slug ?>" <?= $product['category'] === $slug ? 'selected' : '' ?>>
                                <?= htmlspecialchars($name) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Tech Category</label>
                    <select name="tech_category" class="w-full border border-gray-300 rounded-md px-3 py-2">
                        <option value="">Select Tech Category...</option>
                        <?php foreach ($techCategories as $slug => $name): ?>
                            <option value="<?= htmlspecialchars($slug) ?>" <?= ($product['tech_category'] ?? '') === $slug ? 'selected' : '' ?>>
                                <?= htmlspecialchars($name) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Launch Status</label>
                    <select name="launch_status" class="w-full border border-gray-300 rounded-md px-3 py-2">
                        <option value="coming-soon" <?= $product['launch_status'] === 'coming-soon' ? 'selected' : '' ?>>Coming Soon</option>
                        <option value="beta" <?= $product['launch_status'] === 'beta' ? 'selected' : '' ?>>Beta</option>
                        <option value="launched" <?= $product['launch_status'] === 'launched' ? 'selected' : '' ?>>Launched</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Pricing Model</label>
                    <select name="pricing_model" class="w-full border border-gray-300 rounded-md px-3 py-2">
                        <option value="free" <?= $product['pricing_model'] === 'free' ? 'selected' : '' ?>>Free</option>
                        <option value="freemium" <?= $product['pricing_model'] === 'freemium' ? 'selected' : '' ?>>Freemium</option>
                        <option value="paid" <?= $product['pricing_model'] === 'paid' ? 'selected' : '' ?>>Paid</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Video Tutorial URL</label>
                    <input type="url" name="video_tutorial_url" value="<?= htmlspecialchars($product['video_tutorial_url'] ?? '') ?>"
                           class="w-full border border-gray-300 rounded-md px-3 py-2"
                           placeholder="YouTube, Vimeo, etc.">
                </div>
            </div>

            <!-- 详细信息 -->
            <div class="space-y-4">
                <?php
                // 处理JSON字段，转换为逗号分隔的文本
                $tags = json_decode($product['tags'] ?? '[]', true) ?: [];
                $keyFeatures = json_decode($product['key_features'] ?? '[]', true) ?: [];
                $useCases = json_decode($product['use_cases'] ?? '[]', true) ?: [];
                $techStack = json_decode($product['tech_stack'] ?? '[]', true) ?: [];
                $screenshots = json_decode($product['screenshots'] ?? '[]', true) ?: [];
                $socialLinks = json_decode($product['social_links'] ?? '{}', true) ?: [];
                ?>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Tags</label>
                    <input type="text" name="tags" value="<?= htmlspecialchars(implode(', ', $tags)) ?>"
                           class="w-full border border-gray-300 rounded-md px-3 py-2"
                           placeholder="AI, Productivity, Automation (comma separated)">
                    <p class="text-xs text-gray-500 mt-1">Separate tags with commas</p>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Key Features</label>
                    <textarea name="key_features" rows="3" class="w-full border border-gray-300 rounded-md px-3 py-2"
                              placeholder="Feature 1, Feature 2, Feature 3 (comma separated)"><?= htmlspecialchars(implode(', ', $keyFeatures)) ?></textarea>
                    <p class="text-xs text-gray-500 mt-1">Separate features with commas</p>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Target Audience</label>
                    <input type="text" name="target_audience" value="<?= htmlspecialchars($product['target_audience'] ?? '') ?>"
                           class="w-full border border-gray-300 rounded-md px-3 py-2"
                           placeholder="Developers, designers, content creators">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Use Cases</label>
                    <textarea name="use_cases" rows="3" class="w-full border border-gray-300 rounded-md px-3 py-2"
                              placeholder="Use case 1, Use case 2, Use case 3 (comma separated)"><?= htmlspecialchars(implode(', ', $useCases)) ?></textarea>
                    <p class="text-xs text-gray-500 mt-1">Separate use cases with commas</p>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Tech Stack</label>
                    <input type="text" name="tech_stack" value="<?= htmlspecialchars(implode(', ', $techStack)) ?>"
                           class="w-full border border-gray-300 rounded-md px-3 py-2"
                           placeholder="React, Node.js, MongoDB (comma separated)">
                    <p class="text-xs text-gray-500 mt-1">Separate technologies with commas</p>
                </div>

                <!-- 产品截图管理 -->
                <div class="space-y-4">
                    <h4 class="text-md font-medium text-gray-900">Product Screenshots</h4>

                    <!-- 当前截图预览 -->
                    <div id="screenshots-preview" class="<?= empty($screenshots) ? 'hidden' : '' ?>">
                        <div id="screenshots-grid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
                            <?php foreach ($screenshots as $index => $screenshot): ?>
                            <div class="relative group screenshot-item" data-index="<?= $index ?>">
                                <img src="<?= htmlspecialchars($screenshot) ?>"
                                     alt="Screenshot <?= $index + 1 ?>"
                                     class="w-full h-32 object-cover rounded-lg border border-gray-200">
                                <div class="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                                    <button type="button"
                                            onclick="removeScreenshot(<?= $index ?>)"
                                            class="bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors"
                                            title="Remove screenshot">
                                        <i class="fas fa-times text-xs"></i>
                                    </button>
                                </div>
                                <div class="absolute bottom-2 left-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded">
                                    Screenshot <?= $index + 1 ?>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>

                    <!-- 截图URL输入 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Screenshot URLs</label>
                        <textarea name="screenshots" id="screenshots-textarea" rows="3" class="w-full border border-gray-300 rounded-md px-3 py-2"
                                  placeholder="https://example.com/screenshot1.jpg, https://example.com/screenshot2.jpg (comma separated)"
                                  onchange="updateScreenshotsPreview()"><?= htmlspecialchars(implode(', ', $screenshots)) ?></textarea>
                        <p class="text-xs text-gray-500 mt-1">Separate screenshot URLs with commas</p>
                        <div class="mt-2">
                            <button type="button" onclick="addScreenshotUrl()"
                                    class="px-3 py-1 bg-blue-500 text-white text-sm rounded hover:bg-blue-600 transition-colors">
                                Add Screenshot URL
                            </button>
                            <button type="button" onclick="uploadScreenshot()"
                                    class="px-3 py-1 bg-green-500 text-white text-sm rounded hover:bg-green-600 transition-colors ml-2">
                                Upload Screenshot
                            </button>
                        </div>
                        <p class="text-xs text-red-500 mt-1">Maximum 3 screenshots allowed</p>

                        <!-- 隐藏的截图文件上传输入 -->
                        <input type="file" id="screenshot-file-input" accept="image/*" multiple class="hidden" onchange="handleScreenshotUpload(this)">
                    </div>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Social Links</label>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <input type="url" name="social_twitter" value="<?= htmlspecialchars($socialLinks['twitter'] ?? '') ?>"
                               class="w-full border border-gray-300 rounded-md px-3 py-2"
                               placeholder="Twitter URL">
                        <input type="url" name="social_linkedin" value="<?= htmlspecialchars($socialLinks['linkedin'] ?? '') ?>"
                               class="w-full border border-gray-300 rounded-md px-3 py-2"
                               placeholder="LinkedIn URL">
                        <input type="url" name="social_github" value="<?= htmlspecialchars($socialLinks['github'] ?? '') ?>"
                               class="w-full border border-gray-300 rounded-md px-3 py-2"
                               placeholder="GitHub URL">
                        <input type="url" name="social_website" value="<?= htmlspecialchars($socialLinks['website'] ?? '') ?>"
                               class="w-full border border-gray-300 rounded-md px-3 py-2"
                               placeholder="Other Website URL">
                    </div>
                </div>
            </div>

            <!-- 管理员设置 -->
            <div class="border-t pt-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Admin Settings</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                        <select name="status" class="w-full border border-gray-300 rounded-md px-3 py-2">
                            <option value="pending" <?= $product['status'] === 'pending' ? 'selected' : '' ?>>Pending</option>
                            <option value="approved" <?= $product['status'] === 'approved' ? 'selected' : '' ?>>Approved</option>
                            <option value="rejected" <?= $product['status'] === 'rejected' ? 'selected' : '' ?>>Rejected</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Innovation Score (1-10)</label>
                        <input type="number" name="innovation_score" min="1" max="10"
                               value="<?= htmlspecialchars($product['innovation_score'] ?? '') ?>"
                               class="w-full border border-gray-300 rounded-md px-3 py-2">
                    </div>
                </div>

                <!-- 精选设置 -->
                <div class="mt-4">
                    <label class="flex items-center">
                        <input type="checkbox" name="is_featured" value="1" 
                               <?= !empty($product['featured_date']) ? 'checked' : '' ?>
                               class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                        <span class="ml-2 text-sm text-gray-700">Set as Featured</span>
                    </label>
                    <div class="mt-2">
                        <input type="date" name="featured_date"
                               value="<?= ($product['featured_date'] ?? '') ? date('Y-m-d', strtotime($product['featured_date'])) : '' ?>"
                               class="border border-gray-300 rounded-md px-3 py-2 text-sm">
                        <span class="text-xs text-gray-500 ml-2">Featured date (leave empty for today)</span>
                    </div>
                </div>

                <div class="mt-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Notes</label>
                    <textarea name="admin_notes" rows="3" class="w-full border border-gray-300 rounded-md px-3 py-2"
                              placeholder="Internal notes about this product..."><?= htmlspecialchars($product['admin_notes'] ?? '') ?></textarea>
                </div>
            </div>

            <!-- 提交按钮 -->
            <div class="flex justify-end space-x-3 pt-6 border-t">
                <a href="?page=launches" class="px-4 py-2 text-gray-600 hover:text-gray-800 border border-gray-300 rounded-md">
                    Cancel
                </a>
                <button type="submit" class="px-4 py-2 bg-blue-600 text-white hover:bg-blue-700 rounded-md">
                    Update Product
                </button>
            </div>
        </form>
    </div>
</div>

<!-- 自定义提示窗组件 -->
<div id="customAlert" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4 transform transition-all">
        <div class="p-6">
            <div class="flex items-center mb-4">
                <div id="alertIcon" class="flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center mr-3">
                    <i id="alertIconClass" class="text-xl"></i>
                </div>
                <h3 id="alertTitle" class="text-lg font-semibold text-gray-900"></h3>
            </div>
            <p id="alertMessage" class="text-gray-600 mb-6"></p>
            <div class="flex justify-end space-x-3">
                <button id="alertCancel" class="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors hidden">
                    Cancel
                </button>
                <button id="alertConfirm" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                    OK
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 成功/错误通知组件 -->
<div id="notification" class="fixed top-4 right-4 z-50 hidden">
    <div class="bg-white rounded-lg shadow-lg border-l-4 p-4 max-w-sm">
        <div class="flex items-center">
            <div id="notificationIcon" class="flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center mr-3">
                <i id="notificationIconClass" class="text-lg"></i>
            </div>
            <div class="flex-1">
                <p id="notificationMessage" class="text-sm font-medium text-gray-900"></p>
            </div>
            <button onclick="hideNotification()" class="ml-3 text-gray-400 hover:text-gray-600">
                <i class="fas fa-times"></i>
            </button>
        </div>
    </div>
</div>

<script>
// 通用URL处理函数
function getFullUrl(relativePath) {
    if (!relativePath) return '';

    // 如果已经是完整URL，直接返回
    if (relativePath.match(/^https?:\/\//)) {
        return relativePath;
    }

    // 如果是相对路径，转换为完整URL
    return window.location.protocol + '//' + window.location.host + relativePath;
}

// 自定义提示窗函数
function showAlert(title, message, type = 'info', confirmCallback = null) {
    const modal = document.getElementById('customAlert');
    const titleEl = document.getElementById('alertTitle');
    const messageEl = document.getElementById('alertMessage');
    const iconEl = document.getElementById('alertIcon');
    const iconClassEl = document.getElementById('alertIconClass');
    const cancelBtn = document.getElementById('alertCancel');
    const confirmBtn = document.getElementById('alertConfirm');

    titleEl.textContent = title;
    messageEl.textContent = message;

    // 设置图标和颜色
    switch(type) {
        case 'success':
            iconEl.className = 'flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center mr-3 bg-green-100';
            iconClassEl.className = 'text-xl fas fa-check text-green-600';
            confirmBtn.className = 'px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors';
            break;
        case 'error':
            iconEl.className = 'flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center mr-3 bg-red-100';
            iconClassEl.className = 'text-xl fas fa-times text-red-600';
            confirmBtn.className = 'px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors';
            break;
        case 'warning':
            iconEl.className = 'flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center mr-3 bg-yellow-100';
            iconClassEl.className = 'text-xl fas fa-exclamation-triangle text-yellow-600';
            confirmBtn.className = 'px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 transition-colors';
            break;
        case 'confirm':
            iconEl.className = 'flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center mr-3 bg-blue-100';
            iconClassEl.className = 'text-xl fas fa-question text-blue-600';
            confirmBtn.className = 'px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors';
            cancelBtn.classList.remove('hidden');
            confirmBtn.textContent = 'Confirm';
            break;
        default:
            iconEl.className = 'flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center mr-3 bg-blue-100';
            iconClassEl.className = 'text-xl fas fa-info text-blue-600';
            confirmBtn.className = 'px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors';
    }

    if (type !== 'confirm') {
        cancelBtn.classList.add('hidden');
        confirmBtn.textContent = 'OK';
    }

    modal.classList.remove('hidden');
    modal.classList.add('flex');

    // 处理确认按钮点击
    confirmBtn.onclick = function() {
        modal.classList.add('hidden');
        modal.classList.remove('flex');
        if (confirmCallback) confirmCallback();
    };

    // 处理取消按钮点击
    cancelBtn.onclick = function() {
        modal.classList.add('hidden');
        modal.classList.remove('flex');
    };

    // 点击外部关闭
    modal.onclick = function(e) {
        if (e.target === modal) {
            modal.classList.add('hidden');
            modal.classList.remove('flex');
        }
    };
}

// 显示通知
function showNotification(message, type = 'success') {
    const notification = document.getElementById('notification');
    const messageEl = document.getElementById('notificationMessage');
    const iconEl = document.getElementById('notificationIcon');
    const iconClassEl = document.getElementById('notificationIconClass');

    messageEl.textContent = message;

    switch(type) {
        case 'success':
            iconEl.className = 'flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center mr-3 bg-green-100';
            iconClassEl.className = 'text-lg fas fa-check text-green-600';
            notification.querySelector('.bg-white').className = 'bg-white rounded-lg shadow-lg border-l-4 border-green-500 p-4 max-w-sm';
            break;
        case 'error':
            iconEl.className = 'flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center mr-3 bg-red-100';
            iconClassEl.className = 'text-lg fas fa-times text-red-600';
            notification.querySelector('.bg-white').className = 'bg-white rounded-lg shadow-lg border-l-4 border-red-500 p-4 max-w-sm';
            break;
    }

    notification.classList.remove('hidden');

    // 3秒后自动隐藏
    setTimeout(() => {
        hideNotification();
    }, 3000);
}

// 隐藏通知
function hideNotification() {
    document.getElementById('notification').classList.add('hidden');
}

// 自动生成slug
document.querySelector('input[name="name"]').addEventListener('input', function() {
    const name = this.value;
    const slug = name.toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '') // 移除特殊字符
        .replace(/\s+/g, '-') // 空格替换为连字符
        .replace(/-+/g, '-') // 多个连字符合并为一个
        .trim('-'); // 移除首尾连字符

    document.querySelector('#slug-input').value = slug;
    checkSlugAvailability(slug);
});

// Slug检测功能
let slugCheckTimeout;
function checkSlugAvailability(slug) {
    if (!slug || slug.length < 3) {
        updateSlugStatus('', 'gray');
        return;
    }

    // 清除之前的超时
    clearTimeout(slugCheckTimeout);

    // 显示加载状态
    updateSlugStatus('Checking...', 'gray', 'fa-spinner fa-spin');

    // 延迟检查，避免频繁请求
    slugCheckTimeout = setTimeout(() => {
        const productId = <?= $productId ?>;
        fetch(`ajax/check-slug.php?slug=${encodeURIComponent(slug)}&exclude_id=${productId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    if (data.available) {
                        updateSlugStatus('Available', 'green', 'fa-check');
                    } else {
                        updateSlugStatus('Already exists', 'red', 'fa-times');
                    }
                } else {
                    updateSlugStatus('Check failed', 'red', 'fa-exclamation-triangle');
                }
            })
            .catch(error => {
                updateSlugStatus('Check failed', 'red', 'fa-exclamation-triangle');
            });
    }, 500);
}

function updateSlugStatus(message, color, icon = '') {
    const statusIcon = document.getElementById('slug-icon');
    const statusMessage = document.getElementById('slug-message');

    statusIcon.className = `fas ${icon} text-${color}-500`;
    statusIcon.classList.toggle('hidden', !icon);

    if (message) {
        statusMessage.textContent = message;
        statusMessage.className = `text-xs mt-1 text-${color}-600`;
    } else {
        statusMessage.textContent = 'URL-friendly identifier (will be checked for uniqueness)';
        statusMessage.className = 'text-xs mt-1 text-gray-500';
    }
}

// Slug输入监听
document.getElementById('slug-input').addEventListener('input', function() {
    checkSlugAvailability(this.value);
});

// 页面加载时检查当前slug和初始化截图预览
document.addEventListener('DOMContentLoaded', function() {
    const currentSlug = document.getElementById('slug-input').value;
    if (currentSlug) {
        checkSlugAvailability(currentSlug);
    }

    // 初始化截图预览
    updateScreenshotsPreview();

    // 监听截图URL输入变化
    document.getElementById('screenshots-textarea').addEventListener('input', function() {
        // 延迟更新预览，避免频繁更新
        clearTimeout(this.updateTimeout);
        this.updateTimeout = setTimeout(() => {
            updateScreenshotsPreview();
        }, 1000);
    });
});

// Logo管理功能

function removeLogo() {
    showAlert(
        'Remove Logo',
        'Are you sure you want to remove the current logo?',
        'confirm',
        function() {
            document.getElementById('logo-url-input').value = '';
            document.getElementById('logo-preview').classList.add('hidden');
            showNotification('Logo removed', 'success');
        }
    );
}

function uploadLogo() {
    document.getElementById('logo-file-input').click();
}

function handleLogoUpload(input) {
    if (input.files && input.files[0]) {
        const file = input.files[0];

        // 验证文件类型
        if (!file.type.startsWith('image/')) {
            showAlert('Error', 'Please select a valid image file', 'error');
            return;
        }

        // 验证文件大小 (最大5MB)
        if (file.size > 5 * 1024 * 1024) {
            showAlert('Error', 'Image file size must be less than 5MB', 'error');
            return;
        }

        // 显示上传进度
        showNotification('Uploading logo...', 'info');

        // 创建FormData对象
        const formData = new FormData();
        formData.append('file', file);
        formData.append('type', 'logo');

        // 传递产品名称用于生成文件名
        const productName = document.querySelector('input[name="name"]').value || 'product';
        formData.append('product_name', productName);

        // 如果有旧logo，传递给服务器以便删除
        const currentLogoUrl = document.getElementById('logo-url-input').value;
        if (currentLogoUrl) {
            formData.append('old_logo_url', currentLogoUrl);
        }

        // 上传文件
        fetch('ajax/upload-file.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 更新logo URL和预览
                document.getElementById('logo-url-input').value = data.url;

                // 转换为完整URL用于显示
                const fullUrl = getFullUrl(data.url);
                document.getElementById('logo-image').src = fullUrl;
                document.getElementById('logo-preview').classList.remove('hidden');
                showNotification(data.message, 'success');
            } else {
                showAlert('Upload Error', data.message, 'error');
            }
        })
        .catch(error => {
            showAlert('Upload Error', 'Failed to upload logo: ' + error.message, 'error');
        });

        // 清空文件输入
        input.value = '';
    }
}

// 精选复选框控制
document.querySelector('input[name="is_featured"]').addEventListener('change', function() {
    const dateInput = document.querySelector('input[name="featured_date"]');
    if (this.checked && !dateInput.value) {
        dateInput.value = new Date().toISOString().split('T')[0]; // 今天的日期
    } else if (!this.checked) {
        dateInput.value = '';
    }
});

// 截图管理功能
function removeScreenshot(index) {
    showAlert(
        'Remove Screenshot',
        'Are you sure you want to remove this screenshot?',
        'confirm',
        function() {
            const screenshotTextarea = document.getElementById('screenshots-textarea');
            const screenshots = screenshotTextarea.value.split(',').map(s => s.trim()).filter(s => s);

            // 删除指定索引的截图
            screenshots.splice(index, 1);

            // 更新textarea
            screenshotTextarea.value = screenshots.join(', ');

            // 更新预览
            updateScreenshotsPreview();

            showNotification('Screenshot removed successfully', 'success');
        }
    );
}

function updateScreenshotsPreview() {
    const screenshotTextarea = document.getElementById('screenshots-textarea');
    const screenshots = screenshotTextarea.value.split(',').map(s => s.trim()).filter(s => s);
    const previewContainer = document.getElementById('screenshots-preview');
    const screenshotsGrid = document.getElementById('screenshots-grid');

    // 限制最多3张截图
    if (screenshots.length > 3) {
        screenshots.splice(3);
        screenshotTextarea.value = screenshots.join(', ');
        showAlert('Limit Exceeded', 'Maximum 3 screenshots allowed. Extra screenshots have been removed.', 'warning');
    }

    // 清空当前预览
    screenshotsGrid.innerHTML = '';

    if (screenshots.length === 0) {
        previewContainer.classList.add('hidden');
        return;
    }

    // 显示预览容器
    previewContainer.classList.remove('hidden');

    // 为每个截图创建预览
    screenshots.forEach((screenshot, index) => {
        if (screenshot) {
            const screenshotDiv = document.createElement('div');
            screenshotDiv.className = 'relative group screenshot-item';
            screenshotDiv.setAttribute('data-index', index);

            screenshotDiv.innerHTML = `
                <img src="${screenshot}"
                     alt="Screenshot ${index + 1}"
                     class="w-full h-32 object-cover rounded-lg border border-gray-200"
                     onerror="this.parentElement.classList.add('border-red-300'); this.parentElement.innerHTML='<div class=\\'w-full h-32 bg-red-50 rounded-lg border border-red-300 flex items-center justify-center text-red-500 text-sm\\'>Failed to load image</div>';">
                <div class="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                    <button type="button"
                            onclick="removeScreenshot(${index})"
                            class="bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors"
                            title="Remove screenshot">
                        <i class="fas fa-times text-xs"></i>
                    </button>
                </div>
                <div class="absolute bottom-2 left-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded">
                    Screenshot ${index + 1}
                </div>
            `;

            screenshotsGrid.appendChild(screenshotDiv);
        }
    });
}

function addScreenshotUrl() {
    // 检查截图数量限制
    const screenshotTextarea = document.getElementById('screenshots-textarea');
    const currentScreenshots = screenshotTextarea.value.split(',').map(s => s.trim()).filter(s => s);

    if (currentScreenshots.length >= 3) {
        showAlert('Limit Reached', 'Maximum 3 screenshots allowed. Please remove an existing screenshot first.', 'warning');
        return;
    }

    // 创建一个简单的输入对话框
    const modal = document.getElementById('customAlert');
    const titleEl = document.getElementById('alertTitle');
    const messageEl = document.getElementById('alertMessage');
    const iconEl = document.getElementById('alertIcon');
    const iconClassEl = document.getElementById('alertIconClass');
    const cancelBtn = document.getElementById('alertCancel');
    const confirmBtn = document.getElementById('alertConfirm');

    titleEl.textContent = 'Add Screenshot URL';

    // 创建输入框
    messageEl.innerHTML = `
        <input type="url" id="screenshot-url-input"
               class="w-full border border-gray-300 rounded-md px-3 py-2 mt-2"
               placeholder="https://example.com/screenshot.jpg">
        <p class="text-xs text-gray-500 mt-1">Screenshots remaining: ${3 - currentScreenshots.length}</p>
    `;

    // 设置图标
    iconEl.className = 'flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center mr-3 bg-blue-100';
    iconClassEl.className = 'text-xl fas fa-plus text-blue-600';

    // 显示取消和确认按钮
    cancelBtn.classList.remove('hidden');
    confirmBtn.textContent = 'Add';
    confirmBtn.className = 'px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors';

    modal.classList.remove('hidden');
    modal.classList.add('flex');

    // 聚焦到输入框
    setTimeout(() => {
        document.getElementById('screenshot-url-input').focus();
    }, 100);

    // 处理确认按钮点击
    confirmBtn.onclick = function() {
        const urlInput = document.getElementById('screenshot-url-input');
        const url = urlInput.value.trim();

        if (url) {
            const currentUrls = screenshotTextarea.value.trim();

            if (currentUrls) {
                screenshotTextarea.value = currentUrls + ', ' + url;
            } else {
                screenshotTextarea.value = url;
            }

            updateScreenshotsPreview();
            showNotification('Screenshot URL added', 'success');
        }

        modal.classList.add('hidden');
        modal.classList.remove('flex');
    };

    // 处理取消按钮点击
    cancelBtn.onclick = function() {
        modal.classList.add('hidden');
        modal.classList.remove('flex');
    };

    // 处理回车键
    document.getElementById('screenshot-url-input').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            confirmBtn.click();
        }
    });
}



function uploadScreenshot() {
    // 检查截图数量限制
    const screenshotTextarea = document.getElementById('screenshots-textarea');
    const currentScreenshots = screenshotTextarea.value.split(',').map(s => s.trim()).filter(s => s);

    if (currentScreenshots.length >= 3) {
        showAlert('Limit Reached', 'Maximum 3 screenshots allowed. Please remove an existing screenshot first.', 'warning');
        return;
    }

    document.getElementById('screenshot-file-input').click();
}

function handleScreenshotUpload(input) {
    if (input.files && input.files.length > 0) {
        const files = Array.from(input.files);
        const screenshotTextarea = document.getElementById('screenshots-textarea');
        const currentScreenshots = screenshotTextarea.value.split(',').map(s => s.trim()).filter(s => s);

        // 检查总数量限制
        const totalAfterUpload = currentScreenshots.length + files.length;
        if (totalAfterUpload > 3) {
            const allowedCount = 3 - currentScreenshots.length;
            showAlert('Too Many Files',
                `You can only upload ${allowedCount} more screenshot(s). Maximum 3 screenshots allowed.`,
                'warning');
            input.value = '';
            return;
        }

        // 验证所有文件
        for (let i = 0; i < files.length; i++) {
            const file = files[i];

            // 验证文件类型
            if (!file.type.startsWith('image/')) {
                showAlert('Invalid File Type', `File "${file.name}" is not a valid image file.`, 'error');
                input.value = '';
                return;
            }

            // 验证文件大小 (最大5MB)
            if (file.size > 5 * 1024 * 1024) {
                showAlert('File Too Large', `File "${file.name}" is too large. Maximum size is 5MB.`, 'error');
                input.value = '';
                return;
            }
        }

        // 显示上传进度
        showNotification(`Uploading ${files.length} screenshot(s)...`, 'info');

        // 上传所有文件
        uploadScreenshotsSequentially(files, 0, []);

        // 清空文件输入
        input.value = '';
    }
}

// 顺序上传截图文件
function uploadScreenshotsSequentially(files, index, uploadedUrls) {
    if (index >= files.length) {
        // 所有文件上传完成
        if (uploadedUrls.length > 0) {
            const screenshotTextarea = document.getElementById('screenshots-textarea');
            const currentUrls = screenshotTextarea.value.trim();

            const newUrls = uploadedUrls.join(', ');
            if (currentUrls) {
                screenshotTextarea.value = currentUrls + ', ' + newUrls;
            } else {
                screenshotTextarea.value = newUrls;
            }

            updateScreenshotsPreview();
            showNotification(`Successfully uploaded ${uploadedUrls.length} screenshot(s)`, 'success');
        }
        return;
    }

    const file = files[index];
    const formData = new FormData();
    formData.append('file', file);
    formData.append('type', 'screenshot');

    // 传递产品名称用于生成文件名
    const productName = document.querySelector('input[name="name"]').value || 'product';
    formData.append('product_name', productName);

    fetch('ajax/upload-file.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            uploadedUrls.push(data.url);
            // 继续上传下一个文件
            uploadScreenshotsSequentially(files, index + 1, uploadedUrls);
        } else {
            showAlert('Upload Error', `Failed to upload "${file.name}": ${data.message}`, 'error');
        }
    })
    .catch(error => {
        showAlert('Upload Error', `Failed to upload "${file.name}": ${error.message}`, 'error');
    });
}



// 表单验证
document.querySelector('form').addEventListener('submit', function(e) {
    const name = document.querySelector('input[name="name"]').value.trim();
    const slug = document.querySelector('#slug-input').value.trim();
    const description = document.querySelector('textarea[name="description"]').value.trim();
    const websiteUrl = document.querySelector('input[name="website_url"]').value.trim();

    if (!name || !slug || !description || !websiteUrl) {
        e.preventDefault();
        showAlert('Validation Error', 'Please fill in all required fields (marked with *)', 'error');
        return false;
    }

    // 验证slug格式
    if (!/^[a-z0-9-]+$/.test(slug)) {
        e.preventDefault();
        showAlert('Invalid Slug', 'Slug can only contain lowercase letters, numbers, and hyphens', 'error');
        document.querySelector('#slug-input').focus();
        return false;
    }

    // 检查slug状态
    const slugIcon = document.getElementById('slug-icon');
    if (slugIcon.classList.contains('fa-times')) {
        e.preventDefault();
        showAlert('Slug Unavailable', 'The slug you entered is already in use. Please choose a different one.', 'error');
        document.querySelector('#slug-input').focus();
        return false;
    }

    if (slugIcon.classList.contains('fa-spinner')) {
        e.preventDefault();
        showAlert('Please Wait', 'Slug availability is still being checked. Please wait a moment.', 'warning');
        return false;
    }
});

// URL验证
document.querySelectorAll('input[type="url"]').forEach(input => {
    input.addEventListener('blur', function() {
        if (this.value && !this.value.match(/^https?:\/\/.+/)) {
            this.setCustomValidity('Please enter a valid URL starting with http:// or https://');
        } else {
            this.setCustomValidity('');
        }
    });
});

// 字符计数器
function addCharCounter(selector, maxLength) {
    const element = document.querySelector(selector);
    if (element) {
        const counter = document.createElement('div');
        counter.className = 'text-xs text-gray-500 mt-1';
        element.parentNode.appendChild(counter);

        function updateCounter() {
            const remaining = maxLength - element.value.length;
            counter.textContent = `${element.value.length}/${maxLength} characters`;
            counter.className = remaining < 0 ? 'text-xs text-red-500 mt-1' : 'text-xs text-gray-500 mt-1';
        }

        element.addEventListener('input', updateCounter);
        updateCounter();
    }
}

// 添加字符计数器
addCharCounter('input[name="tagline"]', 100);
addCharCounter('textarea[name="description"]', 1000);
</script>
