<?php
/**
 * QuickCalc - Minimal Four-Function Calculator Tool Page
 * Zero border-radius design - no rounded corners
 */

$currentPage = 'tool-jnic32d3anhui-tool-20250821031237';
$breadcrumbs = [
    ['name' => 'Home', 'url' => '/'],
    ['name' => 'Tools', 'url' => '/tools'],
    ['name' => 'AI', 'url' => '/tools/ai'],
    ['name' => 'QuickCalc - Minimal Four-Function Calculator']
];

// Include dynamic SEO helper
require_once ROOT_PATH . '/app/helpers/dynamic-seo.php';

// Generate SEO data
$seoData = [
    'title' => 'QuickCalc - Minimal Four-Function Calculator Online - Prompt2Tool',
    'description' => 'Free online calculator with four basic arithmetic operations. Simple, fast, and accurate calculations with decimal support and keyboard shortcuts.',
    'keywords' => 'calculator, arithmetic, addition, subtraction, multiplication, division, math, online calculator',
    'og_title' => 'QuickCalc - Minimal Four-Function Calculator Online - Prompt2Tool',
    'og_description' => 'Free online calculator with four basic arithmetic operations. Simple, fast, and accurate calculations with decimal support and keyboard shortcuts.',
    'canonical' => getCurrentURL()
];

// Include common header
include_once ROOT_PATH . '/templates/layout/header.php';
?>

<!-- Tool page main content -->
<div class="bg-gray-900 text-white">

    <!-- Hero Section with SEO optimized content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-12">
        <div class="text-center mb-12">
            <div class="flex items-center justify-center mb-4">
                <div class="bg-blue-600 p-3 mr-4">
                    <div class="w-10 h-10 text-white text-2xl flex items-center justify-center">🧮</div>
                </div>
                <div class="text-left">
                    <h1 class="text-4xl font-bold text-white mb-2">QuickCalc - Minimal Four-Function Calculator</h1>
                    <p class="text-xl text-gray-400">Simple and efficient calculator for basic arithmetic operations with decimal support</p>
                </div>
            </div>
        </div>

        <!-- Tool main content -->
        <div class="grid grid-cols-1 lg:grid-cols-1 gap-8">
            <!-- Calculator Interface -->
            <div class="bg-gray-800 border border-gray-700 p-6 max-w-md mx-auto">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-xl font-semibold text-white">Calculator</h2>
                    <div class="flex space-x-2">
                        <button id="clearAll" class="px-3 py-1 bg-red-600 text-white text-sm hover:bg-red-700 transition-colors">
                            <i class="fas fa-times mr-1"></i>Clear
                        </button>
                    </div>
                </div>
                
                <!-- Calculator Display -->
                <div class="mb-4">
                    <input 
                        type="text" 
                        id="display" 
                        readonly 
                        value="0"
                        class="w-full h-16 bg-gray-100 text-3xl font-mono text-gray-800 text-right px-4 py-2 border border-gray-300 focus:outline-none"
                        aria-label="Calculator display"
                    >
                </div>

                <!-- Calculator Buttons -->
                <div class="grid grid-cols-4 gap-2" id="calculator-grid">
                    <!-- Row 1 -->
                    <button class="calc-btn number-btn py-4 px-6 text-xl font-medium bg-white hover:bg-gray-50 border border-gray-300 text-gray-800 transition-colors duration-150 active:scale-95 focus:outline-none focus:ring-2 focus:ring-blue-300" data-number="7">7</button>
                    <button class="calc-btn number-btn py-4 px-6 text-xl font-medium bg-white hover:bg-gray-50 border border-gray-300 text-gray-800 transition-colors duration-150 active:scale-95 focus:outline-none focus:ring-2 focus:ring-blue-300" data-number="8">8</button>
                    <button class="calc-btn number-btn py-4 px-6 text-xl font-medium bg-white hover:bg-gray-50 border border-gray-300 text-gray-800 transition-colors duration-150 active:scale-95 focus:outline-none focus:ring-2 focus:ring-blue-300" data-number="9">9</button>
                    <button class="calc-btn operator-btn py-4 px-6 text-xl font-medium bg-blue-500 hover:bg-blue-600 text-white transition-colors duration-150 active:scale-95 focus:outline-none focus:ring-2 focus:ring-blue-300" data-operator="/">/</button>
                    
                    <!-- Row 2 -->
                    <button class="calc-btn number-btn py-4 px-6 text-xl font-medium bg-white hover:bg-gray-50 border border-gray-300 text-gray-800 transition-colors duration-150 active:scale-95 focus:outline-none focus:ring-2 focus:ring-blue-300" data-number="4">4</button>
                    <button class="calc-btn number-btn py-4 px-6 text-xl font-medium bg-white hover:bg-gray-50 border border-gray-300 text-gray-800 transition-colors duration-150 active:scale-95 focus:outline-none focus:ring-2 focus:ring-blue-300" data-number="5">5</button>
                    <button class="calc-btn number-btn py-4 px-6 text-xl font-medium bg-white hover:bg-gray-50 border border-gray-300 text-gray-800 transition-colors duration-150 active:scale-95 focus:outline-none focus:ring-2 focus:ring-blue-300" data-number="6">6</button>
                    <button class="calc-btn operator-btn py-4 px-6 text-xl font-medium bg-blue-500 hover:bg-blue-600 text-white transition-colors duration-150 active:scale-95 focus:outline-none focus:ring-2 focus:ring-blue-300" data-operator="*">×</button>
                    
                    <!-- Row 3 -->
                    <button class="calc-btn number-btn py-4 px-6 text-xl font-medium bg-white hover:bg-gray-50 border border-gray-300 text-gray-800 transition-colors duration-150 active:scale-95 focus:outline-none focus:ring-2 focus:ring-blue-300" data-number="1">1</button>
                    <button class="calc-btn number-btn py-4 px-6 text-xl font-medium bg-white hover:bg-gray-50 border border-gray-300 text-gray-800 transition-colors duration-150 active:scale-95 focus:outline-none focus:ring-2 focus:ring-blue-300" data-number="2">2</button>
                    <button class="calc-btn number-btn py-4 px-6 text-xl font-medium bg-white hover:bg-gray-50 border border-gray-300 text-gray-800 transition-colors duration-150 active:scale-95 focus:outline-none focus:ring-2 focus:ring-blue-300" data-number="3">3</button>
                    <button class="calc-btn operator-btn py-4 px-6 text-xl font-medium bg-blue-500 hover:bg-blue-600 text-white transition-colors duration-150 active:scale-95 focus:outline-none focus:ring-2 focus:ring-blue-300" data-operator="-">-</button>
                    
                    <!-- Row 4 -->
                    <button class="calc-btn number-btn py-4 px-6 text-xl font-medium bg-white hover:bg-gray-50 border border-gray-300 text-gray-800 transition-colors duration-150 active:scale-95 focus:outline-none focus:ring-2 focus:ring-blue-300" data-number="0">0</button>
                    <button class="calc-btn decimal-btn py-4 px-6 text-xl font-medium bg-white hover:bg-gray-50 border border-gray-300 text-gray-800 transition-colors duration-150 active:scale-95 focus:outline-none focus:ring-2 focus:ring-blue-300" data-decimal=".">.</button>
                    <button class="calc-btn equals-btn py-4 px-6 text-xl font-medium bg-green-500 hover:bg-green-600 text-white transition-colors duration-150 active:scale-95 focus:outline-none focus:ring-2 focus:ring-blue-300" data-equals="=">=</button>
                    <button class="calc-btn operator-btn py-4 px-6 text-xl font-medium bg-blue-500 hover:bg-blue-600 text-white transition-colors duration-150 active:scale-95 focus:outline-none focus:ring-2 focus:ring-blue-300" data-operator="+">+</button>
                    
                    <!-- Row 5 - Clear button spanning all columns -->
                    <button class="calc-btn clear-btn col-span-4 py-4 px-6 text-xl font-medium bg-red-500 hover:bg-red-600 text-white transition-colors duration-150 active:scale-95 focus:outline-none focus:ring-2 focus:ring-blue-300" data-clear="clear">Clear</button>
                </div>

                <!-- Calculator Status -->
                <div class="mt-4 text-center">
                    <div id="calculator-status" class="text-sm text-gray-400">
                        Ready for calculation
                    </div>
                </div>
            </div>
        </div>

        <!-- FAQ Section -->
        <div class="px-8 pt-16 pb-16">
            <h2 class="text-3xl font-bold text-white text-center mb-8">Frequently Asked Questions</h2>
            <div class="max-w-4xl mx-auto space-y-6">
                <div class="bg-gray-800 border border-gray-700 p-6">
                    <h3 class="text-xl font-semibold text-white mb-3">How do I use the calculator?</h3>
                    <p class="text-gray-300">Simply click the number buttons to enter numbers, then click an operator (+, -, ×, ÷), enter another number, and press equals (=) to get the result. You can also use your keyboard for input.</p>
                </div>
                
                <div class="bg-gray-800 border border-gray-700 p-6">
                    <h3 class="text-xl font-semibold text-white mb-3">What keyboard shortcuts are supported?</h3>
                    <p class="text-gray-300">You can use number keys (0-9), decimal point (.), operators (+, -, *, /), Enter key for equals, and Escape key to clear the calculator. All standard calculator keyboard inputs are supported.</p>
                </div>
                
                <div class="bg-gray-800 border border-gray-700 p-6">
                    <h3 class="text-xl font-semibold text-white mb-3">How many digits can I enter?</h3>
                    <p class="text-gray-300">The calculator supports up to 10 total digits including the decimal point. This ensures accurate calculations while preventing display overflow.</p>
                </div>
                
                <div class="bg-gray-800 border border-gray-700 p-6">
                    <h3 class="text-xl font-semibold text-white mb-3">What happens if I divide by zero?</h3>
                    <p class="text-gray-300">Division by zero will display "Error" on the calculator. Simply press Clear or the Clear button to reset and start a new calculation.</p>
                </div>
                
                <div class="bg-gray-800 border border-gray-700 p-6">
                    <h3 class="text-xl font-semibold text-white mb-3">Can I use decimal numbers?</h3>
                    <p class="text-gray-300">Yes! The calculator fully supports decimal numbers. Click the decimal point (.) button or press the period key on your keyboard. You can only add one decimal point per number.</p>
                </div>
            </div>
        </div>
    </div>

<script>
// Tool functionality JavaScript
document.addEventListener('DOMContentLoaded', function() {
    'use strict';
    
    // Variable declarations
    let currentInput = '';
    let previousInput = '';
    let operation = null;
    let shouldResetDisplay = false;
    
    // DOM element references
    const display = document.getElementById('display');
    const clearAllBtn = document.getElementById('clearAll');
    const calculatorGrid = document.getElementById('calculator-grid');
    const statusElement = document.getElementById('calculator-status');
    
    // Update display function
    function updateDisplay(value) {
        if (value === undefined || value === null) {
            display.value = '0';
        } else {
            display.value = value.toString();
        }
    }
    
    // Append number to current input
    function appendNumber(number) {
        if (shouldResetDisplay) {
            currentInput = '';
            shouldResetDisplay = false;
        }
        
        // Check digit limit (10 digits total including decimal point)
        if (currentInput.length >= 10) {
            statusElement.textContent = 'Maximum 10 digits allowed';
            statusElement.className = 'text-sm text-red-400';
            setTimeout(() => {
                statusElement.textContent = 'Ready for calculation';
                statusElement.className = 'text-sm text-gray-400';
            }, 2000);
            return;
        }
        
        if (currentInput === '0' && number !== '.') {
            currentInput = number;
        } else {
            currentInput += number;
        }
        
        updateDisplay(currentInput);
        statusElement.textContent = 'Entering number...';
        statusElement.className = 'text-sm text-blue-400';
    }
    
    // Append decimal point
    function appendDecimal() {
        if (shouldResetDisplay) {
            currentInput = '0';
            shouldResetDisplay = false;
        }
        
        // Check if decimal point already exists
        if (currentInput.includes('.')) {
            statusElement.textContent = 'Only one decimal point allowed';
            statusElement.className = 'text-sm text-red-400';
            setTimeout(() => {
                statusElement.textContent = 'Ready for calculation';
                statusElement.className = 'text-sm text-gray-400';
            }, 2000);
            return;
        }
        
        // Check digit limit
        if (currentInput.length >= 10) {
            statusElement.textContent = 'Maximum 10 digits allowed';
            statusElement.className = 'text-sm text-red-400';
            setTimeout(() => {
                statusElement.textContent = 'Ready for calculation';
                statusElement.className = 'text-sm text-gray-400';
            }, 2000);
            return;
        }
        
        if (currentInput === '') {
            currentInput = '0.';
        } else {
            currentInput += '.';
        }
        
        updateDisplay(currentInput);
        statusElement.textContent = 'Adding decimal point...';
        statusElement.className = 'text-sm text-blue-400';
    }
    
    // Set operation
    function setOperation(op) {
        if (currentInput === '') return;
        
        if (previousInput !== '' && operation !== null && !shouldResetDisplay) {
            calculate();
        }
        
        operation = op;
        previousInput = currentInput;
        currentInput = '';
        shouldResetDisplay = false;
        
        statusElement.textContent = `Operation: ${op === '*' ? '×' : op === '/' ? '÷' : op}`;
        statusElement.className = 'text-sm text-yellow-400';
    }
    
    // Perform calculation
    function calculate() {
        if (operation === null || previousInput === '' || currentInput === '') {
            return;
        }
        
        const prev = parseFloat(previousInput);
        const current = parseFloat(currentInput);
        let result;
        
        switch (operation) {
            case '+':
                result = prev + current;
                break;
            case '-':
                result = prev - current;
                break;
            case '*':
                result = prev * current;
                break;
            case '/':
                if (current === 0) {
                    updateDisplay('Error');
                    statusElement.textContent = 'Cannot divide by zero';
                    statusElement.className = 'text-sm text-red-400';
                    clearCalculator();
                    return;
                }
                result = prev / current;
                break;
            default:
                return;
        }
        
        // Round to prevent floating point errors and limit to 10 digits
        result = Math.round(result * 1000000000) / 1000000000;
        
        // Check if result exceeds display limit
        if (result.toString().length > 10) {
            updateDisplay('Error');
            statusElement.textContent = 'Result too large';
            statusElement.className = 'text-sm text-red-400';
            clearCalculator();
            return;
        }
        
        currentInput = result.toString();
        operation = null;
        previousInput = '';
        shouldResetDisplay = true;
        
        updateDisplay(result);
        statusElement.textContent = 'Calculation complete';
        statusElement.className = 'text-sm text-green-400';
        
        setTimeout(() => {
            statusElement.textContent = 'Ready for calculation';
            statusElement.className = 'text-sm text-gray-400';
        }, 2000);
    }
    
    // Clear calculator
    function clearCalculator() {
        currentInput = '';
        previousInput = '';
        operation = null;
        shouldResetDisplay = false;
        updateDisplay('0');
        statusElement.textContent = 'Calculator cleared';
        statusElement.className = 'text-sm text-green-400';
        
        setTimeout(() => {
            statusElement.textContent = 'Ready for calculation';
            statusElement.className = 'text-sm text-gray-400';
        }, 1000);
    }
    
    // Handle button clicks
    function handleButtonClick(event) {
        const target = event.target;
        
        if (target.hasAttribute('data-number')) {
            appendNumber(target.getAttribute('data-number'));
        } else if (target.hasAttribute('data-decimal')) {
            appendDecimal();
        } else if (target.hasAttribute('data-operator')) {
            setOperation(target.getAttribute('data-operator'));
        } else if (target.hasAttribute('data-equals')) {
            calculate();
        } else if (target.hasAttribute('data-clear')) {
            clearCalculator();
        }
    }
    
    // Event listeners
    calculatorGrid.addEventListener('click', handleButtonClick);
    
    if (clearAllBtn) {
        clearAllBtn.addEventListener('click', clearCalculator);
    }
    
    // Keyboard support
    document.addEventListener('keydown', function(e) {
        // Prevent default for calculator keys
        if ('0123456789+-*/.='.includes(e.key) || e.key === 'Enter' || e.key === 'Escape') {
            e.preventDefault();
        }
        
        if (e.key >= '0' && e.key <= '9') {
            appendNumber(e.key);
        } else if (e.key === '.') {
            appendDecimal();
        } else if (e.key === '+') {
            setOperation('+');
        } else if (e.key === '-') {
            setOperation('-');
        } else if (e.key === '*') {
            setOperation('*');
        } else if (e.key === '/') {
            setOperation('/');
        } else if (e.key === 'Enter' || e.key === '=') {
            calculate();
        } else if (e.key === 'Escape') {
            clearCalculator();
        }
    });
    
    // Initialize calculator
    updateDisplay('0');
    statusElement.textContent = 'Ready for calculation';
    statusElement.className = 'text-sm text-gray-400';
    
    // Add responsive behavior for small screens
    function handleResize() {
        if (window.innerWidth < 320) {
            calculatorGrid.className = 'grid grid-cols-1 gap-2';
        } else {
            calculatorGrid.className = 'grid grid-cols-4 gap-2';
        }
    }
    
    window.addEventListener('resize', handleResize);
    handleResize(); // Initial check
});
</script>

</div>

<?php include_once ROOT_PATH . '/templates/layout/footer.php'; ?>