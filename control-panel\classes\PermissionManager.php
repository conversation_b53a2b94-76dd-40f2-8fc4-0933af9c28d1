<?php
/**
 * 权限管理器类
 * 负责处理用户权限验证和角色管理
 */

// 防止直接访问
if (!defined('ROOT_PATH')) {
    exit('Direct access not allowed');
}

class PermissionManager {
    
    private $permissionsConfig;
    private $rolesConfig;
    private $cache;
    
    /**
     * 构造函数
     */
    public function __construct() {
        $this->loadConfigurations();
        $this->initializeCache();
    }
    
    /**
     * 加载权限配置
     */
    private function loadConfigurations() {
        $config = include ROOT_PATH . '/control-panel/config/permissions.php';
        $this->permissionsConfig = $config['permissions'];
        $this->rolesConfig = $config['roles'];
    }
    
    /**
     * 初始化缓存
     */
    private function initializeCache() {
        try {
            $this->cache = new Redis();
            $this->cache->connect('127.0.0.1', 6379);
            $this->cache->select(3); // 使用数据库3存储权限缓存
        } catch (Exception $e) {
            error_log('Redis connection failed for permissions: ' . $e->getMessage());
            $this->cache = null;
        }
    }
    
    /**
     * 检查用户是否具有特定权限
     */
    public function hasPermission($user, $permission) {
        // 检查缓存
        $cacheKey = "user_permission:{$user['id']}:{$permission}";
        if ($this->cache) {
            $cached = $this->cache->get($cacheKey);
            if ($cached !== false) {
                return (bool)$cached;
            }
        }
        
        // 超级管理员拥有所有权限
        if ($user['role'] === 'super_admin') {
            $this->cachePermission($cacheKey, true);
            return true;
        }
        
        // 检查角色权限
        $hasPermission = $this->checkRolePermission($user['role'], $permission);
        
        // 检查用户特定权限（如果有）
        if (!$hasPermission && isset($user['custom_permissions'])) {
            $hasPermission = $this->checkCustomPermissions($user['custom_permissions'], $permission);
        }
        
        // 缓存结果
        $this->cachePermission($cacheKey, $hasPermission);
        
        return $hasPermission;
    }
    
    /**
     * 检查角色权限
     */
    private function checkRolePermission($role, $permission) {
        if (!isset($this->rolesConfig[$role])) {
            return false;
        }
        
        $rolePermissions = $this->rolesConfig[$role]['permissions'];
        
        // 检查通配符权限
        if (in_array('*', $rolePermissions)) {
            return true;
        }
        
        // 检查具体权限
        if (in_array($permission, $rolePermissions)) {
            return true;
        }
        
        // 检查通配符匹配
        foreach ($rolePermissions as $rolePermission) {
            if (strpos($rolePermission, '*') !== false) {
                $pattern = str_replace('*', '.*', preg_quote($rolePermission, '/'));
                if (preg_match("/^{$pattern}$/", $permission)) {
                    return true;
                }
            }
        }
        
        return false;
    }
    
    /**
     * 检查自定义权限
     */
    private function checkCustomPermissions($customPermissions, $permission) {
        if (!is_array($customPermissions)) {
            $customPermissions = json_decode($customPermissions, true) ?: [];
        }
        
        return in_array($permission, $customPermissions);
    }
    
    /**
     * 缓存权限结果
     */
    private function cachePermission($cacheKey, $result) {
        if ($this->cache) {
            $this->cache->setex($cacheKey, 300, (int)$result); // 缓存5分钟
        }
    }
    
    /**
     * 获取用户所有权限
     */
    public function getUserPermissions($user) {
        $cacheKey = "user_all_permissions:{$user['id']}";
        
        if ($this->cache) {
            $cached = $this->cache->get($cacheKey);
            if ($cached !== false) {
                return json_decode($cached, true);
            }
        }
        
        $permissions = [];
        
        // 超级管理员拥有所有权限
        if ($user['role'] === 'super_admin') {
            $permissions = $this->getAllPermissions();
        } else {
            // 获取角色权限
            $permissions = $this->getRolePermissions($user['role']);
            
            // 添加自定义权限
            if (isset($user['custom_permissions'])) {
                $customPermissions = is_array($user['custom_permissions']) 
                    ? $user['custom_permissions'] 
                    : json_decode($user['custom_permissions'], true) ?: [];
                $permissions = array_unique(array_merge($permissions, $customPermissions));
            }
        }
        
        // 缓存结果
        if ($this->cache) {
            $this->cache->setex($cacheKey, 600, json_encode($permissions)); // 缓存10分钟
        }
        
        return $permissions;
    }
    
    /**
     * 获取所有可用权限
     */
    public function getAllPermissions() {
        $allPermissions = [];
        
        foreach ($this->permissionsConfig as $module => $permissions) {
            foreach ($permissions as $action => $description) {
                $allPermissions[] = "{$module}.{$action}";
            }
        }
        
        return $allPermissions;
    }
    
    /**
     * 获取角色权限
     */
    public function getRolePermissions($role) {
        if (!isset($this->rolesConfig[$role])) {
            return [];
        }
        
        $rolePermissions = $this->rolesConfig[$role]['permissions'];
        $expandedPermissions = [];
        
        foreach ($rolePermissions as $permission) {
            if ($permission === '*') {
                return $this->getAllPermissions();
            } elseif (strpos($permission, '*') !== false) {
                // 展开通配符权限
                $expandedPermissions = array_merge(
                    $expandedPermissions, 
                    $this->expandWildcardPermission($permission)
                );
            } else {
                $expandedPermissions[] = $permission;
            }
        }
        
        return array_unique($expandedPermissions);
    }
    
    /**
     * 展开通配符权限
     */
    private function expandWildcardPermission($wildcardPermission) {
        $pattern = str_replace('*', '.*', preg_quote($wildcardPermission, '/'));
        $allPermissions = $this->getAllPermissions();
        $matchedPermissions = [];
        
        foreach ($allPermissions as $permission) {
            if (preg_match("/^{$pattern}$/", $permission)) {
                $matchedPermissions[] = $permission;
            }
        }
        
        return $matchedPermissions;
    }
    
    /**
     * 获取所有角色
     */
    public function getAllRoles() {
        return $this->rolesConfig;
    }
    
    /**
     * 获取角色信息
     */
    public function getRoleInfo($role) {
        return $this->rolesConfig[$role] ?? null;
    }
    
    /**
     * 检查角色是否存在
     */
    public function roleExists($role) {
        return isset($this->rolesConfig[$role]);
    }
    
    /**
     * 获取角色层级
     */
    public function getRoleLevel($role) {
        if (!isset($this->rolesConfig[$role])) {
            return 0;
        }
        
        return $this->rolesConfig[$role]['level'];
    }
    
    /**
     * 比较角色权限级别
     */
    public function compareRoles($role1, $role2) {
        $level1 = $this->getRoleLevel($role1);
        $level2 = $this->getRoleLevel($role2);
        
        if ($level1 > $level2) {
            return 1;  // role1 权限更高
        } elseif ($level1 < $level2) {
            return -1; // role2 权限更高
        } else {
            return 0;  // 权限相等
        }
    }
    
    /**
     * 检查用户是否可以管理另一个用户
     */
    public function canManageUser($manager, $target) {
        // 超级管理员可以管理所有人
        if ($manager['role'] === 'super_admin') {
            return true;
        }
        
        // 不能管理自己
        if ($manager['id'] === $target['id']) {
            return false;
        }
        
        // 只能管理权限级别更低的用户
        return $this->compareRoles($manager['role'], $target['role']) > 0;
    }
    
    /**
     * 获取用户可分配的角色
     */
    public function getAssignableRoles($user) {
        $userLevel = $this->getRoleLevel($user['role']);
        $assignableRoles = [];
        
        foreach ($this->rolesConfig as $role => $config) {
            // 只能分配权限级别更低或相等的角色
            if ($config['level'] <= $userLevel) {
                // 超级管理员除外，只有超级管理员可以分配超级管理员角色
                if ($role === 'super_admin' && $user['role'] !== 'super_admin') {
                    continue;
                }
                $assignableRoles[$role] = $config;
            }
        }
        
        return $assignableRoles;
    }
    
    /**
     * 验证权限字符串格式
     */
    public function validatePermissionFormat($permission) {
        // 权限格式：module.action
        if (!preg_match('/^[a-z_]+\.[a-z_]+$/', $permission)) {
            return false;
        }
        
        $parts = explode('.', $permission);
        $module = $parts[0];
        $action = $parts[1];
        
        // 检查模块是否存在
        if (!isset($this->permissionsConfig[$module])) {
            return false;
        }
        
        // 检查动作是否存在
        if (!isset($this->permissionsConfig[$module][$action])) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 获取权限描述
     */
    public function getPermissionDescription($permission) {
        if (!$this->validatePermissionFormat($permission)) {
            return null;
        }
        
        $parts = explode('.', $permission);
        $module = $parts[0];
        $action = $parts[1];
        
        return $this->permissionsConfig[$module][$action];
    }
    
    /**
     * 获取权限组
     */
    public function getPermissionGroups() {
        $config = include ROOT_PATH . '/control-panel/config/permissions.php';
        return $config['permission_groups'] ?? [];
    }
    
    /**
     * 获取模块的所有权限
     */
    public function getModulePermissions($module) {
        if (!isset($this->permissionsConfig[$module])) {
            return [];
        }
        
        $permissions = [];
        foreach ($this->permissionsConfig[$module] as $action => $description) {
            $permissions["{$module}.{$action}"] = $description;
        }
        
        return $permissions;
    }
    
    /**
     * 清除用户权限缓存
     */
    public function clearUserPermissionCache($userId) {
        if (!$this->cache) {
            return;
        }
        
        // 清除用户相关的所有权限缓存
        $pattern = "user_*:{$userId}:*";
        $keys = $this->cache->keys($pattern);
        
        if (!empty($keys)) {
            $this->cache->del($keys);
        }
    }
    
    /**
     * 清除所有权限缓存
     */
    public function clearAllPermissionCache() {
        if (!$this->cache) {
            return;
        }
        
        $pattern = "user_*";
        $keys = $this->cache->keys($pattern);
        
        if (!empty($keys)) {
            $this->cache->del($keys);
        }
    }
    
    /**
     * 记录权限检查日志
     */
    public function logPermissionCheck($user, $permission, $result, $context = []) {
        $logData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'user_id' => $user['id'],
            'username' => $user['username'],
            'role' => $user['role'],
            'permission' => $permission,
            'result' => $result ? 'granted' : 'denied',
            'context' => $context,
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
        ];
        
        $logFile = '/var/log/prompt2tool/permission_checks.log';
        $logDir = dirname($logFile);
        
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        $logLine = json_encode($logData, JSON_UNESCAPED_UNICODE) . "\n";
        file_put_contents($logFile, $logLine, FILE_APPEND | LOCK_EX);
    }
    
    /**
     * 生成权限报告
     */
    public function generatePermissionReport($user) {
        $report = [
            'user_info' => [
                'id' => $user['id'],
                'username' => $user['username'],
                'role' => $user['role'],
                'role_info' => $this->getRoleInfo($user['role'])
            ],
            'permissions' => [
                'total_count' => 0,
                'by_module' => [],
                'detailed_list' => []
            ],
            'generated_at' => date('Y-m-d H:i:s')
        ];
        
        $userPermissions = $this->getUserPermissions($user);
        $report['permissions']['total_count'] = count($userPermissions);
        
        foreach ($userPermissions as $permission) {
            $parts = explode('.', $permission);
            $module = $parts[0];
            $action = $parts[1];
            
            if (!isset($report['permissions']['by_module'][$module])) {
                $report['permissions']['by_module'][$module] = [];
            }
            
            $report['permissions']['by_module'][$module][] = $action;
            $report['permissions']['detailed_list'][] = [
                'permission' => $permission,
                'description' => $this->getPermissionDescription($permission)
            ];
        }
        
        return $report;
    }
}

/**
 * 权限检查辅助函数
 */
function checkPermission($permission, $user = null) {
    static $permissionManager = null;
    
    if ($permissionManager === null) {
        $permissionManager = new PermissionManager();
    }
    
    if ($user === null) {
        $user = getCurrentAdmin();
    }
    
    if (!$user) {
        return false;
    }
    
    $result = $permissionManager->hasPermission($user, $permission);
    
    // 记录权限检查日志（仅在调试模式下）
    if (defined('DEBUG_PERMISSIONS') && DEBUG_PERMISSIONS) {
        $permissionManager->logPermissionCheck($user, $permission, $result);
    }
    
    return $result;
}

/**
 * 获取用户权限列表
 */
function getUserPermissionList($user = null) {
    static $permissionManager = null;
    
    if ($permissionManager === null) {
        $permissionManager = new PermissionManager();
    }
    
    if ($user === null) {
        $user = getCurrentAdmin();
    }
    
    if (!$user) {
        return [];
    }
    
    return $permissionManager->getUserPermissions($user);
}

/**
 * 检查角色权限级别
 */
function hasRoleLevel($requiredLevel, $user = null) {
    static $permissionManager = null;
    
    if ($permissionManager === null) {
        $permissionManager = new PermissionManager();
    }
    
    if ($user === null) {
        $user = getCurrentAdmin();
    }
    
    if (!$user) {
        return false;
    }
    
    $userLevel = $permissionManager->getRoleLevel($user['role']);
    return $userLevel >= $requiredLevel;
}
?>
