<?php
/**
 * 工具创建页面
 * 提供工具创建表单和处理功能
 */

// 定义根目录
define('ROOT_PATH', dirname(dirname(dirname(__DIR__))));

// 加载依赖
require_once dirname(__DIR__) . '/../auth/middleware.php';
require_once dirname(__DIR__) . '/../middleware/PermissionMiddleware.php';
require_once dirname(__DIR__) . '/../models/ToolModel.php';

// 权限检查
requirePermission('tools.create');

// 初始化模型
$toolModel = new ToolModel();

// 处理表单提交
$message = '';
$messageType = '';
$formData = [];

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $currentUser = getCurrentAdmin();
        
        // 获取表单数据
        $formData = [
            'name' => trim($_POST['name'] ?? ''),
            'description' => trim($_POST['description'] ?? ''),
            'category_id' => intval($_POST['category_id'] ?? 0),
            'icon' => trim($_POST['icon'] ?? 'fas fa-tools'),
            'color' => trim($_POST['color'] ?? '#6366f1'),
            'status' => $_POST['status'] ?? 'draft',
            'featured' => isset($_POST['featured']) ? 1 : 0,
            'premium' => isset($_POST['premium']) ? 1 : 0,
            'difficulty_level' => $_POST['difficulty_level'] ?? 'beginner',
            'tags' => trim($_POST['tags'] ?? ''),
            'meta_title' => trim($_POST['meta_title'] ?? ''),
            'meta_description' => trim($_POST['meta_description'] ?? ''),
            'meta_keywords' => trim($_POST['meta_keywords'] ?? ''),
            'file_path' => trim($_POST['file_path'] ?? ''),
            'version' => trim($_POST['version'] ?? '1.0.0'),
            'author' => trim($_POST['author'] ?? ''),
            'documentation_url' => trim($_POST['documentation_url'] ?? ''),
            'demo_url' => trim($_POST['demo_url'] ?? ''),
            'github_url' => trim($_POST['github_url'] ?? ''),
            'created_by' => $currentUser['id']
        ];
        
        // 验证必填字段
        $errors = [];
        if (empty($formData['name'])) {
            $errors[] = 'Tool name is required';
        }
        if (empty($formData['description'])) {
            $errors[] = 'Tool description is required';
        }
        if ($formData['category_id'] <= 0) {
            $errors[] = 'Please select a category';
        }
        
        if (!empty($errors)) {
            throw new Exception(implode(', ', $errors));
        }
        
        // 生成唯一slug
        $formData['slug'] = $toolModel->generateUniqueSlug($formData['name']);
        
        // 处理配置数据
        $config = [];
        if (!empty($_POST['config_keys'])) {
            foreach ($_POST['config_keys'] as $index => $key) {
                if (!empty($key) && isset($_POST['config_values'][$index])) {
                    $config[$key] = $_POST['config_values'][$index];
                }
            }
        }
        $formData['config'] = !empty($config) ? json_encode($config) : null;
        
        // 处理依赖数据
        $dependencies = [];
        if (!empty($_POST['dependencies'])) {
            $dependencies = array_filter(array_map('trim', explode(',', $_POST['dependencies'])));
        }
        $formData['dependencies'] = !empty($dependencies) ? json_encode($dependencies) : null;
        
        // 设置发布时间
        if ($formData['status'] === 'published') {
            $formData['published_at'] = date('Y-m-d H:i:s');
        }
        
        // 创建工具
        $tool = $toolModel->create($formData);
        
        if ($tool) {
            $message = 'Tool created successfully!';
            $messageType = 'success';
            
            // 重定向到工具列表或编辑页面
            if (isset($_POST['save_and_continue'])) {
                header("Location: edit.php?id={$tool['id']}");
                exit;
            } else {
                header('Location: index.php?message=' . urlencode($message));
                exit;
            }
        } else {
            throw new Exception('Failed to create tool');
        }
        
    } catch (Exception $e) {
        $message = $e->getMessage();
        $messageType = 'error';
    }
}

// 获取工具分类
require_once dirname(__DIR__) . '/../classes/Database.php';
$db = new Database();
$categories = $db->fetchAll("SELECT * FROM pt_tool_category WHERE status = 'active' ORDER BY sort_order, name");

$currentPage = 'tools';
include_once dirname(__DIR__) . '/../components/layout/header.php';
?>

<div class="min-h-screen bg-gray-100">
    <!-- 侧边栏 -->
    <div class="fixed inset-y-0 left-0 w-64 bg-gray-900">
        <?php include_once dirname(__DIR__) . '/../components/layout/sidebar.php'; ?>
    </div>
    
    <!-- 主内容区 -->
    <div class="ml-64">
        <!-- 顶部导航栏 -->
        <div class="bg-white shadow-sm border-b border-gray-200">
            <div class="px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-16">
                    <div class="flex items-center">
                        <nav class="flex space-x-4">
                            <a href="index.php" class="text-gray-500 hover:text-gray-700 px-3 py-2 text-sm font-medium">
                                <i class="fas fa-arrow-left mr-2"></i>Back to Tools
                            </a>
                        </nav>
                        <h1 class="ml-4 text-2xl font-semibold text-gray-900">Create New Tool</h1>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 页面内容 -->
        <div class="p-6">
            <!-- 消息提示 -->
            <?php if ($message): ?>
            <div class="mb-6 p-4 border-l-4 <?= $messageType === 'success' ? 'bg-green-50 border-green-400 text-green-700' : 'bg-red-50 border-red-400 text-red-700' ?>">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <i class="fas fa-<?= $messageType === 'success' ? 'check-circle' : 'exclamation-circle' ?>"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm"><?= htmlspecialchars($message) ?></p>
                    </div>
                </div>
            </div>
            <?php endif; ?>
            
            <!-- 创建表单 -->
            <div class="bg-white border border-gray-200">
                <form method="POST" id="createToolForm" class="space-y-6">
                    <!-- 基本信息 -->
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Basic Information</h3>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Tool Name *</label>
                                <input type="text" name="name" required 
                                       value="<?= htmlspecialchars($formData['name'] ?? '') ?>"
                                       class="w-full px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent"
                                       placeholder="Enter tool name">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Category *</label>
                                <select name="category_id" required 
                                        class="w-full px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent">
                                    <option value="">Select Category</option>
                                    <?php foreach ($categories as $category): ?>
                                    <option value="<?= $category['id'] ?>" 
                                            <?= ($formData['category_id'] ?? 0) == $category['id'] ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($category['name']) ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        
                        <div class="mt-6">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Description *</label>
                            <textarea name="description" rows="4" required
                                      class="w-full px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent"
                                      placeholder="Describe what this tool does..."><?= htmlspecialchars($formData['description'] ?? '') ?></textarea>
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Icon</label>
                                <input type="text" name="icon" 
                                       value="<?= htmlspecialchars($formData['icon'] ?? 'fas fa-tools') ?>"
                                       class="w-full px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent"
                                       placeholder="fas fa-tools">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Color</label>
                                <input type="color" name="color" 
                                       value="<?= htmlspecialchars($formData['color'] ?? '#6366f1') ?>"
                                       class="w-full h-10 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Difficulty Level</label>
                                <select name="difficulty_level" 
                                        class="w-full px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent">
                                    <option value="beginner" <?= ($formData['difficulty_level'] ?? 'beginner') === 'beginner' ? 'selected' : '' ?>>Beginner</option>
                                    <option value="intermediate" <?= ($formData['difficulty_level'] ?? '') === 'intermediate' ? 'selected' : '' ?>>Intermediate</option>
                                    <option value="advanced" <?= ($formData['difficulty_level'] ?? '') === 'advanced' ? 'selected' : '' ?>>Advanced</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="mt-6">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Tags</label>
                            <input type="text" name="tags" 
                                   value="<?= htmlspecialchars($formData['tags'] ?? '') ?>"
                                   class="w-full px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent"
                                   placeholder="tag1, tag2, tag3">
                            <p class="text-sm text-gray-500 mt-1">Separate tags with commas</p>
                        </div>
                    </div>
                    
                    <!-- 技术信息 -->
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Technical Information</h3>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">File Path</label>
                                <input type="text" name="file_path" 
                                       value="<?= htmlspecialchars($formData['file_path'] ?? '') ?>"
                                       class="w-full px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent"
                                       placeholder="/tools/example-tool.php">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Version</label>
                                <input type="text" name="version" 
                                       value="<?= htmlspecialchars($formData['version'] ?? '1.0.0') ?>"
                                       class="w-full px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent"
                                       placeholder="1.0.0">
                            </div>
                        </div>
                        
                        <div class="mt-6">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Author</label>
                            <input type="text" name="author" 
                                   value="<?= htmlspecialchars($formData['author'] ?? '') ?>"
                                   class="w-full px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent"
                                   placeholder="Author name">
                        </div>
                        
                        <div class="mt-6">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Dependencies</label>
                            <input type="text" name="dependencies" 
                                   value="<?= htmlspecialchars($formData['dependencies'] ?? '') ?>"
                                   class="w-full px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent"
                                   placeholder="dependency1, dependency2">
                            <p class="text-sm text-gray-500 mt-1">Separate dependencies with commas</p>
                        </div>
                        
                        <!-- 配置参数 -->
                        <div class="mt-6">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Configuration Parameters</label>
                            <div id="configContainer">
                                <div class="flex gap-2 mb-2">
                                    <input type="text" name="config_keys[]" placeholder="Parameter name" 
                                           class="flex-1 px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent">
                                    <input type="text" name="config_values[]" placeholder="Default value" 
                                           class="flex-1 px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent">
                                    <button type="button" onclick="removeConfigRow(this)" 
                                            class="px-3 py-2 bg-red-500 text-white hover:bg-red-600">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                            <button type="button" onclick="addConfigRow()" 
                                    class="mt-2 px-4 py-2 bg-gray-500 text-white hover:bg-gray-600">
                                <i class="fas fa-plus mr-2"></i>Add Parameter
                            </button>
                        </div>
                    </div>
                    
                    <!-- URL信息 -->
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">URLs</h3>
                        
                        <div class="grid grid-cols-1 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Documentation URL</label>
                                <input type="url" name="documentation_url" 
                                       value="<?= htmlspecialchars($formData['documentation_url'] ?? '') ?>"
                                       class="w-full px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent"
                                       placeholder="https://docs.example.com">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Demo URL</label>
                                <input type="url" name="demo_url" 
                                       value="<?= htmlspecialchars($formData['demo_url'] ?? '') ?>"
                                       class="w-full px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent"
                                       placeholder="https://demo.example.com">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">GitHub URL</label>
                                <input type="url" name="github_url" 
                                       value="<?= htmlspecialchars($formData['github_url'] ?? '') ?>"
                                       class="w-full px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent"
                                       placeholder="https://github.com/user/repo">
                            </div>
                        </div>
                    </div>
                    
                    <!-- SEO信息 -->
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">SEO Information</h3>
                        
                        <div class="space-y-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Meta Title</label>
                                <input type="text" name="meta_title" 
                                       value="<?= htmlspecialchars($formData['meta_title'] ?? '') ?>"
                                       class="w-full px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent"
                                       placeholder="SEO title for this tool">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Meta Description</label>
                                <textarea name="meta_description" rows="3"
                                          class="w-full px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent"
                                          placeholder="SEO description for this tool"><?= htmlspecialchars($formData['meta_description'] ?? '') ?></textarea>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Meta Keywords</label>
                                <input type="text" name="meta_keywords" 
                                       value="<?= htmlspecialchars($formData['meta_keywords'] ?? '') ?>"
                                       class="w-full px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent"
                                       placeholder="keyword1, keyword2, keyword3">
                            </div>
                        </div>
                    </div>
                    
                    <!-- 状态和选项 -->
                    <div class="p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Status & Options</h3>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                                <select name="status" 
                                        class="w-full px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent">
                                    <option value="draft" <?= ($formData['status'] ?? 'draft') === 'draft' ? 'selected' : '' ?>>Draft</option>
                                    <option value="published" <?= ($formData['status'] ?? '') === 'published' ? 'selected' : '' ?>>Published</option>
                                    <option value="archived" <?= ($formData['status'] ?? '') === 'archived' ? 'selected' : '' ?>>Archived</option>
                                </select>
                            </div>
                            
                            <div class="space-y-3">
                                <label class="flex items-center">
                                    <input type="checkbox" name="featured" value="1" 
                                           <?= ($formData['featured'] ?? 0) ? 'checked' : '' ?>
                                           class="mr-2 text-accent focus:ring-accent">
                                    <span class="text-sm font-medium text-gray-700">Featured Tool</span>
                                </label>
                                
                                <label class="flex items-center">
                                    <input type="checkbox" name="premium" value="1" 
                                           <?= ($formData['premium'] ?? 0) ? 'checked' : '' ?>
                                           class="mr-2 text-accent focus:ring-accent">
                                    <span class="text-sm font-medium text-gray-700">Premium Tool</span>
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 提交按钮 -->
                    <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-end space-x-3">
                        <a href="index.php" 
                           class="px-4 py-2 border border-gray-300 text-gray-700 hover:bg-gray-50">
                            Cancel
                        </a>
                        <button type="submit" name="save_and_continue" 
                                class="px-4 py-2 bg-gray-600 text-white hover:bg-gray-700">
                            Save & Continue Editing
                        </button>
                        <button type="submit" 
                                class="px-4 py-2 bg-accent text-white hover:bg-blue-600">
                            Create Tool
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// 添加配置参数行
function addConfigRow() {
    const container = document.getElementById('configContainer');
    const newRow = document.createElement('div');
    newRow.className = 'flex gap-2 mb-2';
    newRow.innerHTML = `
        <input type="text" name="config_keys[]" placeholder="Parameter name" 
               class="flex-1 px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent">
        <input type="text" name="config_values[]" placeholder="Default value" 
               class="flex-1 px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent">
        <button type="button" onclick="removeConfigRow(this)" 
                class="px-3 py-2 bg-red-500 text-white hover:bg-red-600">
            <i class="fas fa-trash"></i>
        </button>
    `;
    container.appendChild(newRow);
}

// 移除配置参数行
function removeConfigRow(button) {
    button.parentElement.remove();
}

// 表单验证
document.getElementById('createToolForm').addEventListener('submit', function(e) {
    const name = document.querySelector('input[name="name"]').value.trim();
    const description = document.querySelector('textarea[name="description"]').value.trim();
    const categoryId = document.querySelector('select[name="category_id"]').value;
    
    if (!name) {
        alert('Please enter a tool name');
        e.preventDefault();
        return;
    }
    
    if (!description) {
        alert('Please enter a tool description');
        e.preventDefault();
        return;
    }
    
    if (!categoryId) {
        alert('Please select a category');
        e.preventDefault();
        return;
    }
});
</script>

<?php include_once dirname(__DIR__) . '/../components/layout/footer.php'; ?>
