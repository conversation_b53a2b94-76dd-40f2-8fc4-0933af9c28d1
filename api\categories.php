<?php
/**
 * Categories API
 * 处理分类列表的Ajax请求
 */

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, X-Requested-With');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// 安全检查
if (!defined('SECURE_ACCESS')) {
    define('SECURE_ACCESS', true);
}

// 定义根目录
define('ROOT_PATH', dirname(__DIR__));

// 加载应用初始化
require_once ROOT_PATH . '/app/init.php';

// 使用统一的数据库连接
require_once ROOT_PATH . '/includes/database-connection.php';

// 包含工具助手
require_once ROOT_PATH . '/app/helpers/tool-helpers.php';

try {
    // 获取请求参数
    $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
    $perPage = 9;
    
    // 获取分类数据
    $categoryData = getCategoriesWithToolCount($page, $perPage);
    
    echo json_encode([
        'success' => true,
        'categories' => $categoryData['categories'],
        'pagination' => $categoryData['pagination']
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Internal server error',
        'error' => $e->getMessage()
    ]);
}

/**
 * 获取分类及工具数量（带分页）
 */
function getCategoriesWithToolCount($page = 1, $perPage = 9) {
    global $pdo;
    
    try {
        // 计算偏移量
        $offset = ($page - 1) * $perPage;
        
        // 获取总分类数
        $countStmt = $pdo->query("
            SELECT COUNT(*) as total
            FROM pt_tool_category tc
            WHERE tc.status = 'active'
        ");
        $totalCategories = $countStmt->fetch()['total'];
        
        // 获取当前页的分类
        $stmt = $pdo->prepare("
            SELECT tc.*, COUNT(t.id) as tool_count
            FROM pt_tool_category tc
            LEFT JOIN pt_tool t ON tc.id = t.category_id AND t.status = 'active'
            WHERE tc.status = 'active'
            GROUP BY tc.id
            ORDER BY tool_count DESC, tc.sort_order, tc.name
            LIMIT ? OFFSET ?
        ");
        $stmt->execute([$perPage, $offset]);
        $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $result = [];
        foreach ($categories as $category) {
            $result[] = [
                'id' => $category['id'],
                'name' => $category['name'],
                'slug' => $category['slug'],
                'description' => $category['description'],
                'icon' => $category['icon'] ?? '📁',
                'sort_order' => $category['sort_order'],
                'tool_count' => $category['tool_count']
            ];
        }

        return [
            'categories' => $result,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $perPage,
                'total' => $totalCategories,
                'total_pages' => ceil($totalCategories / $perPage),
                'has_prev' => $page > 1,
                'has_next' => $page < ceil($totalCategories / $perPage),
                'has_more' => $page < ceil($totalCategories / $perPage)
            ]
        ];
    } catch (Exception $e) {
        throw new Exception('Error loading categories: ' . $e->getMessage());
    }
}
?>
