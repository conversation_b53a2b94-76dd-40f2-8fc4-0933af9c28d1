<?php
/**
 * 数据库连接管理类
 * 提供数据库连接、查询和事务管理功能
 */

// 防止直接访问
if (!defined('ROOT_PATH')) {
    exit('Direct access not allowed');
}

class Database {
    
    private static $instance = null;
    private $connection = null;
    private $config;
    private $transactionLevel = 0;
    
    /**
     * 私有构造函数（单例模式）
     */
    private function __construct() {
        $this->loadConfig();
        $this->connect();
    }
    
    /**
     * 获取数据库实例
     */
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * 加载数据库配置
     */
    private function loadConfig() {
        $adminConfig = include ROOT_PATH . '/control-panel/config/admin.php';
        $this->config = $adminConfig['database'];
    }
    
    /**
     * 建立数据库连接
     */
    private function connect() {
        try {
            $dsn = "mysql:host={$this->config['host']};port={$this->config['port']};dbname={$this->config['database']};charset={$this->config['charset']}";
            
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::ATTR_PERSISTENT => $this->config['persistent'],
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES {$this->config['charset']} COLLATE {$this->config['collation']}"
            ];
            
            $this->connection = new PDO($dsn, $this->config['username'], $this->config['password'], $options);
            
            // 设置时区
            $this->connection->exec("SET time_zone = '{$this->config['timezone']}'");
            
        } catch (PDOException $e) {
            error_log('Database connection failed: ' . $e->getMessage());
            throw new Exception('Database connection failed');
        }
    }
    
    /**
     * 获取PDO连接对象
     */
    public function getConnection() {
        return $this->connection;
    }
    
    /**
     * 执行查询
     */
    public function query($sql, $params = []) {
        try {
            $stmt = $this->connection->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            error_log('Database query failed: ' . $e->getMessage() . ' SQL: ' . $sql);
            throw new Exception('Database query failed: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取单行数据
     */
    public function fetch($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetch();
    }
    
    /**
     * 获取多行数据
     */
    public function fetchAll($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetchAll();
    }
    
    /**
     * 获取单个值
     */
    public function fetchColumn($sql, $params = []) {
        $stmt = $this->query($sql, $params);
        return $stmt->fetchColumn();
    }
    
    /**
     * 插入数据
     */
    public function insert($table, $data) {
        $fields = array_keys($data);
        $placeholders = ':' . implode(', :', $fields);
        $sql = "INSERT INTO {$table} (" . implode(', ', $fields) . ") VALUES ({$placeholders})";
        
        $this->query($sql, $data);
        return $this->connection->lastInsertId();
    }
    
    /**
     * 更新数据
     */
    public function update($table, $data, $where, $whereParams = []) {
        $fields = [];
        foreach (array_keys($data) as $field) {
            $fields[] = "{$field} = :{$field}";
        }
        
        $sql = "UPDATE {$table} SET " . implode(', ', $fields) . " WHERE {$where}";
        $params = array_merge($data, $whereParams);
        
        $stmt = $this->query($sql, $params);
        return $stmt->rowCount();
    }
    
    /**
     * 删除数据
     */
    public function delete($table, $where, $params = []) {
        $sql = "DELETE FROM {$table} WHERE {$where}";
        $stmt = $this->query($sql, $params);
        return $stmt->rowCount();
    }
    
    /**
     * 开始事务
     */
    public function beginTransaction() {
        if ($this->transactionLevel === 0) {
            $this->connection->beginTransaction();
        } else {
            // 嵌套事务使用保存点
            $this->connection->exec("SAVEPOINT sp{$this->transactionLevel}");
        }
        $this->transactionLevel++;
    }
    
    /**
     * 提交事务
     */
    public function commit() {
        $this->transactionLevel--;
        if ($this->transactionLevel === 0) {
            $this->connection->commit();
        } else {
            // 释放保存点
            $this->connection->exec("RELEASE SAVEPOINT sp{$this->transactionLevel}");
        }
    }
    
    /**
     * 回滚事务
     */
    public function rollback() {
        $this->transactionLevel--;
        if ($this->transactionLevel === 0) {
            $this->connection->rollback();
        } else {
            // 回滚到保存点
            $this->connection->exec("ROLLBACK TO SAVEPOINT sp{$this->transactionLevel}");
        }
    }
    
    /**
     * 检查表是否存在
     */
    public function tableExists($table) {
        $sql = "SHOW TABLES LIKE :table";
        $result = $this->fetch($sql, ['table' => $table]);
        return !empty($result);
    }
    
    /**
     * 获取表结构
     */
    public function getTableStructure($table) {
        $sql = "DESCRIBE {$table}";
        return $this->fetchAll($sql);
    }
    
    /**
     * 执行原生SQL
     */
    public function execute($sql) {
        try {
            return $this->connection->exec($sql);
        } catch (PDOException $e) {
            error_log('Database execute failed: ' . $e->getMessage() . ' SQL: ' . $sql);
            throw new Exception('Database execute failed: ' . $e->getMessage());
        }
    }
    
    /**
     * 获取数据库信息
     */
    public function getDatabaseInfo() {
        return [
            'version' => $this->fetchColumn("SELECT VERSION()"),
            'charset' => $this->fetchColumn("SELECT @@character_set_database"),
            'collation' => $this->fetchColumn("SELECT @@collation_database"),
            'timezone' => $this->fetchColumn("SELECT @@time_zone"),
            'connection_id' => $this->fetchColumn("SELECT CONNECTION_ID()")
        ];
    }
    
    /**
     * 获取表列表
     */
    public function getTables() {
        $sql = "SHOW TABLES";
        $result = $this->fetchAll($sql);
        $tables = [];
        foreach ($result as $row) {
            $tables[] = array_values($row)[0];
        }
        return $tables;
    }
    
    /**
     * 获取表大小信息
     */
    public function getTableSizes() {
        $sql = "
            SELECT 
                table_name,
                ROUND(((data_length + index_length) / 1024 / 1024), 2) AS size_mb,
                table_rows
            FROM information_schema.tables 
            WHERE table_schema = :database
            ORDER BY (data_length + index_length) DESC
        ";
        
        return $this->fetchAll($sql, ['database' => $this->config['database']]);
    }
    
    /**
     * 优化表
     */
    public function optimizeTable($table) {
        $sql = "OPTIMIZE TABLE {$table}";
        return $this->execute($sql);
    }
    
    /**
     * 分析表
     */
    public function analyzeTable($table) {
        $sql = "ANALYZE TABLE {$table}";
        return $this->execute($sql);
    }
    
    /**
     * 检查表
     */
    public function checkTable($table) {
        $sql = "CHECK TABLE {$table}";
        return $this->fetchAll($sql);
    }
    
    /**
     * 修复表
     */
    public function repairTable($table) {
        $sql = "REPAIR TABLE {$table}";
        return $this->fetchAll($sql);
    }
    
    /**
     * 获取慢查询日志状态
     */
    public function getSlowQueryStatus() {
        return [
            'slow_query_log' => $this->fetchColumn("SELECT @@slow_query_log"),
            'long_query_time' => $this->fetchColumn("SELECT @@long_query_time"),
            'slow_query_log_file' => $this->fetchColumn("SELECT @@slow_query_log_file")
        ];
    }
    
    /**
     * 获取连接状态
     */
    public function getConnectionStatus() {
        $status = [];
        $variables = [
            'Connections',
            'Max_used_connections',
            'Threads_connected',
            'Threads_running',
            'Uptime'
        ];
        
        foreach ($variables as $variable) {
            $result = $this->fetch("SHOW STATUS LIKE :variable", ['variable' => $variable]);
            if ($result) {
                $status[$variable] = $result['Value'];
            }
        }
        
        return $status;
    }
    
    /**
     * 备份表结构
     */
    public function backupTableStructure($table) {
        $sql = "SHOW CREATE TABLE {$table}";
        $result = $this->fetch($sql);
        return $result['Create Table'] ?? '';
    }
    
    /**
     * 获取索引信息
     */
    public function getIndexes($table) {
        $sql = "SHOW INDEX FROM {$table}";
        return $this->fetchAll($sql);
    }
    
    /**
     * 关闭连接
     */
    public function close() {
        $this->connection = null;
    }
    
    /**
     * 析构函数
     */
    public function __destruct() {
        $this->close();
    }
    
    /**
     * 防止克隆
     */
    private function __clone() {}
    
    /**
     * 防止反序列化
     */
    public function __wakeup() {
        throw new Exception("Cannot unserialize singleton");
    }
}

/**
 * 数据库辅助函数
 */

/**
 * 获取数据库实例
 */
function db() {
    return Database::getInstance();
}

/**
 * 快速查询
 */
function dbQuery($sql, $params = []) {
    return db()->query($sql, $params);
}

/**
 * 快速获取单行
 */
function dbFetch($sql, $params = []) {
    return db()->fetch($sql, $params);
}

/**
 * 快速获取多行
 */
function dbFetchAll($sql, $params = []) {
    return db()->fetchAll($sql, $params);
}

/**
 * 快速插入
 */
function dbInsert($table, $data) {
    return db()->insert($table, $data);
}

/**
 * 快速更新
 */
function dbUpdate($table, $data, $where, $whereParams = []) {
    return db()->update($table, $data, $where, $whereParams);
}

/**
 * 快速删除
 */
function dbDelete($table, $where, $params = []) {
    return db()->delete($table, $where, $params);
}
?>
