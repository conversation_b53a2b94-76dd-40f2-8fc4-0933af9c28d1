/**
 * 工具管理器组件
 * 处理工具页面的交互和功能
 */
class ToolManager {
    constructor() {
        this.currentTool = null;
        this.searchTimeout = null;
        this.init();
    }

    init() {
        this.setupSearch();
        this.setupToolCards();
        this.setupFilters();
        this.setupFavorites();
        this.setupToolUsage();
    }

    /**
     * 设置搜索功能
     */
    setupSearch() {
        const searchInputs = document.querySelectorAll('input[type="text"][placeholder*="Search"]');
        
        searchInputs.forEach(input => {
            input.addEventListener('input', (e) => {
                clearTimeout(this.searchTimeout);
                this.searchTimeout = setTimeout(() => {
                    this.performSearch(e.target.value);
                }, 300);
            });

            // 搜索快捷键 (Ctrl/Cmd + K)
            document.addEventListener('keydown', (e) => {
                if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                    e.preventDefault();
                    input.focus();
                }
            });
        });
    }

    /**
     * 执行搜索
     */
    performSearch(query) {
        if (query.length < 2) {
            this.showAllTools();
            return;
        }


        
        // 隐藏所有工具卡片
        const toolCards = document.querySelectorAll('[data-tool-card]');
        let visibleCount = 0;

        toolCards.forEach(card => {
            const toolName = card.dataset.toolName?.toLowerCase() || '';
            const toolDescription = card.dataset.toolDescription?.toLowerCase() || '';
            const toolTags = card.dataset.toolTags?.toLowerCase() || '';
            
            const searchQuery = query.toLowerCase();
            
            if (toolName.includes(searchQuery) || 
                toolDescription.includes(searchQuery) || 
                toolTags.includes(searchQuery)) {
                card.style.display = 'block';
                visibleCount++;
            } else {
                card.style.display = 'none';
            }
        });

        // 显示搜索结果统计
        this.updateSearchResults(visibleCount, query);
    }

    /**
     * 显示所有工具
     */
    showAllTools() {
        const toolCards = document.querySelectorAll('[data-tool-card]');
        toolCards.forEach(card => {
            card.style.display = 'block';
        });

        // 隐藏搜索结果统计
        const searchResults = document.getElementById('search-results');
        if (searchResults) {
            searchResults.style.display = 'none';
        }
    }

    /**
     * 更新搜索结果显示
     */
    updateSearchResults(count, query) {
        let searchResults = document.getElementById('search-results');
        
        if (!searchResults) {
            searchResults = document.createElement('div');
            searchResults.id = 'search-results';
            searchResults.className = 'mb-6 p-4 bg-gray-900 border border-gray-800';
            
            const toolsContainer = document.querySelector('.tools-container') || document.querySelector('.grid');
            if (toolsContainer) {
                toolsContainer.parentNode.insertBefore(searchResults, toolsContainer);
            }
        }

        searchResults.style.display = 'block';
        searchResults.innerHTML = `
            <div class="flex items-center justify-between">
                <span class="text-gray-300">
                    Found <strong class="text-accent">${count}</strong> tools matching "<strong>${query}</strong>"
                </span>
                <button onclick="window.toolManager.clearSearch()" class="text-gray-400 hover:text-white">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        `;
    }

    /**
     * 清除搜索
     */
    clearSearch() {
        const searchInputs = document.querySelectorAll('input[type="text"][placeholder*="Search"]');
        searchInputs.forEach(input => {
            input.value = '';
        });
        this.showAllTools();
    }

    /**
     * 设置工具卡片交互
     */
    setupToolCards() {
        const toolCards = document.querySelectorAll('[data-tool-card]');
        
        toolCards.forEach(card => {
            // 添加数据属性用于搜索
            const toolName = card.querySelector('h3')?.textContent || '';
            const toolDescription = card.querySelector('p')?.textContent || '';
            const toolTags = Array.from(card.querySelectorAll('.tag')).map(tag => tag.textContent).join(' ');
            
            card.dataset.toolName = toolName;
            card.dataset.toolDescription = toolDescription;
            card.dataset.toolTags = toolTags;

            // 添加悬停效果
            card.addEventListener('mouseenter', () => {
                this.onToolCardHover(card);
            });

            // 添加点击统计
            card.addEventListener('click', () => {
                this.onToolCardClick(card);
            });
        });
    }

    /**
     * 工具卡片悬停处理
     */
    onToolCardHover(card) {
        // 预加载工具相关资源
        const toolSlug = card.dataset.toolSlug;
        if (toolSlug) {
            // 这里可以预加载工具页面资源
        }
    }

    /**
     * 工具卡片点击处理
     */
    onToolCardClick(card) {
        const toolSlug = card.dataset.toolSlug;
        if (toolSlug) {
            // 记录工具使用统计
            this.trackToolUsage(toolSlug);
        }
    }

    /**
     * 设置分类过滤器
     */
    setupFilters() {
        const filterButtons = document.querySelectorAll('[data-filter]');
        
        filterButtons.forEach(button => {
            button.addEventListener('click', () => {
                const filter = button.dataset.filter;
                this.applyFilter(filter);
                
                // 更新按钮状态
                filterButtons.forEach(btn => btn.classList.remove('active'));
                button.classList.add('active');
            });
        });
    }

    /**
     * 应用分类过滤器
     */
    applyFilter(category) {
        const toolCards = document.querySelectorAll('[data-tool-card]');
        
        toolCards.forEach(card => {
            const toolCategory = card.dataset.toolCategory;
            
            if (category === 'all' || toolCategory === category) {
                card.style.display = 'block';
            } else {
                card.style.display = 'none';
            }
        });
    }

    /**
     * 设置收藏功能
     */
    setupFavorites() {
        const favoriteButtons = document.querySelectorAll('[data-favorite]');
        
        favoriteButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                
                const toolSlug = button.dataset.favorite;
                this.toggleFavorite(toolSlug, button);
            });
        });

        // 加载已收藏的工具
        this.loadFavorites();
    }

    /**
     * 切换收藏状态
     */
    toggleFavorite(toolSlug, button) {
        let favorites = JSON.parse(localStorage.getItem('prompt2tool_favorites') || '[]');
        
        if (favorites.includes(toolSlug)) {
            // 移除收藏
            favorites = favorites.filter(slug => slug !== toolSlug);
            button.classList.remove('favorited');
            button.innerHTML = '☆';
            this.showNotification('Removed from favorites', 'info');
        } else {
            // 添加收藏
            favorites.push(toolSlug);
            button.classList.add('favorited');
            button.innerHTML = '★';
            this.showNotification('Added to favorites', 'success');
        }
        
        localStorage.setItem('prompt2tool_favorites', JSON.stringify(favorites));
    }

    /**
     * 加载收藏状态
     */
    loadFavorites() {
        const favorites = JSON.parse(localStorage.getItem('prompt2tool_favorites') || '[]');
        
        favorites.forEach(toolSlug => {
            const button = document.querySelector(`[data-favorite="${toolSlug}"]`);
            if (button) {
                button.classList.add('favorited');
                button.innerHTML = '★';
            }
        });
    }

    /**
     * 设置工具使用统计
     */
    setupToolUsage() {
        // 页面加载时记录页面访问
        if (window.location.pathname.includes('/tools/')) {
            const pathParts = window.location.pathname.split('/');
            if (pathParts.length >= 4) {
                const toolSlug = pathParts[3];
                this.trackToolUsage(toolSlug);
            }
        }
    }

    /**
     * 记录工具使用统计
     */
    trackToolUsage(toolSlug) {
        // 记录到本地存储
        let usage = JSON.parse(localStorage.getItem('prompt2tool_usage') || '{}');
        
        if (!usage[toolSlug]) {
            usage[toolSlug] = {
                count: 0,
                lastUsed: null,
                firstUsed: new Date().toISOString()
            };
        }
        
        usage[toolSlug].count++;
        usage[toolSlug].lastUsed = new Date().toISOString();
        
        localStorage.setItem('prompt2tool_usage', JSON.stringify(usage));
        
        // 发送到服务器 (如果需要)
        if (navigator.sendBeacon) {
            const data = new FormData();
            data.append('tool', toolSlug);
            data.append('action', 'view');
            navigator.sendBeacon('/api/analytics/tool-usage', data);
        }
    }

    /**
     * 显示通知
     */
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 z-50 p-4 border transition-all duration-300 ${
            type === 'success' ? 'bg-success border-green-600 text-white' :
            type === 'error' ? 'bg-danger border-red-600 text-white' :
            'bg-gray-900 border-gray-700 text-gray-300'
        }`;
        
        notification.innerHTML = `
            <div class="flex items-center justify-between">
                <span>${message}</span>
                <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-current opacity-70 hover:opacity-100">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // 自动移除
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 3000);
    }

    /**
     * 获取使用统计
     */
    getUsageStats() {
        return JSON.parse(localStorage.getItem('prompt2tool_usage') || '{}');
    }

    /**
     * 获取收藏列表
     */
    getFavorites() {
        return JSON.parse(localStorage.getItem('prompt2tool_favorites') || '[]');
    }
}

// 自动初始化
if (typeof window !== 'undefined') {
    window.ToolManager = ToolManager;
    
    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', () => {
        window.toolManager = new ToolManager();
    });
}
