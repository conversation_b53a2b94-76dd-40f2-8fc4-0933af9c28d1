<?php
/**
 * Product Launch Detail Page
 * Based on request-detail.php styles and structure
 */

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Get slug parameter
$slug = $_GET['slug'] ?? '';

if (empty($slug)) {
    header('Location: /launches');
    exit;
}

$currentPage = 'launch-detail';

// 使用统一的数据库连接
require_once ROOT_PATH . '/includes/database-connection.php';

// Database connection
try {

    // 获取产品详情
    $stmt = $pdo->prepare("
        SELECT l.*, m.username, m.avatar,
               CASE WHEN lv.user_id IS NOT NULL THEN 1 ELSE 0 END as user_voted
        FROM pt_product_launches l
        LEFT JOIN pt_member m ON l.user_id = m.id
        LEFT JOIN pt_launch_votes lv ON l.id = lv.launch_id AND lv.user_id = ?
        WHERE l.slug = ? AND l.status = 'approved'
    ");
    
    $userId = $_SESSION['user_id'] ?? 0;
    $stmt->execute([$userId, $slug]);
    $launch = $stmt->fetch();

    if (!$launch) {
        header('Location: /launches');
        exit;
    }

    // 增加浏览次数
    $viewStmt = $pdo->prepare("UPDATE pt_product_launches SET views = views + 1 WHERE id = ?");
    $viewStmt->execute([$launch['id']]);
    $launch['views']++; // 更新本地数据

    // 获取分类信息
    $categoryStmt = $pdo->prepare("SELECT name, icon, color FROM pt_launch_categories WHERE slug = ?");
    $categoryStmt->execute([$launch['category']]);
    $categoryInfo = $categoryStmt->fetch();

    // 获取相关产品（同分类的其他产品）
    $relatedStmt = $pdo->prepare("
        SELECT id, name, slug, tagline, logo_url, votes, views, launch_status
        FROM pt_product_launches
        WHERE category = ? AND slug != ? AND status = 'approved'
        ORDER BY votes DESC, created_at DESC
        LIMIT 6
    ");
    $relatedStmt->execute([$launch['category'], $slug]);
    $relatedLaunches = $relatedStmt->fetchAll();

} catch (Exception $e) {
    error_log("Launch detail error: " . $e->getMessage());
    header('Location: /launches');
    exit;
}

// 解析JSON字段
$tags = json_decode($launch['tags'] ?? '[]', true) ?: [];
$keyFeatures = json_decode($launch['key_features'] ?? '[]', true) ?: [];
$useCases = json_decode($launch['use_cases'] ?? '[]', true) ?: [];
$socialLinks = json_decode($launch['social_links'] ?? '[]', true) ?: [];
$techStack = json_decode($launch['tech_stack'] ?? '[]', true) ?: [];
$screenshots = json_decode($launch['screenshots'] ?? '[]', true) ?: [];

// 状态样式映射
$statusColors = [
    'coming-soon' => 'bg-blue-600 text-white',
    'beta' => 'bg-purple-600 text-white',
    'launched' => 'bg-green-600 text-white'
];

$statusNames = [
    'coming-soon' => 'Coming Soon',
    'beta' => 'Beta',
    'launched' => 'Launched'
];

$pricingColors = [
    'free' => 'bg-green-600 text-white',
    'freemium' => 'bg-blue-600 text-white',
    'paid' => 'bg-yellow-600 text-white',
    'enterprise' => 'bg-purple-600 text-white',
    'unknown' => 'bg-gray-600 text-white'
];

$pricingNames = [
    'free' => 'Free',
    'freemium' => 'Freemium',
    'paid' => 'Paid',
    'enterprise' => 'Enterprise',
    'unknown' => 'Unknown'
];

// SEO meta标签
$productTitle = htmlspecialchars($launch['name'] ?? '');
$pageTitle = (strlen($productTitle) > 35) ? substr($productTitle, 0, 32) . '...' : $productTitle;
$pageTitle .= ' - Launch Details - Prompt2Tool';

$productTagline = htmlspecialchars($launch['tagline'] ?? '');
$productDesc = $productTagline ?: htmlspecialchars($launch['description'] ?? '');
$pageDescription = "Discover " . $productTitle . " - " . $productDesc;
$pageDescription = (strlen($pageDescription) > 157) ? substr($pageDescription, 0, 154) . '...' : $pageDescription;

// 设置SEO数据
$seoData = [
    'title' => $pageTitle,
    'description' => $pageDescription,
    'og_url' => 'https://' . $_SERVER['HTTP_HOST'] . '/launch/' . $slug,
    'canonical' => 'https://' . $_SERVER['HTTP_HOST'] . '/launch/' . $slug,
    'og_image' => $launch['logo_url'] ? 'https://' . $_SERVER['HTTP_HOST'] . $launch['logo_url'] : null
];

// 投票图标函数
function getLaunchVoteIcon($votes) {
    if ($votes >= 100) {
        return ['icon' => '🔥', 'color' => 'text-red-400', 'animation' => 'fire-animation'];
    } elseif ($votes >= 50) {
        return ['icon' => '⭐', 'color' => 'text-yellow-400', 'animation' => 'star-twinkle'];
    } elseif ($votes >= 20) {
        return ['icon' => '🚀', 'color' => 'text-blue-400', 'animation' => 'rocket-rise'];
    } elseif ($votes >= 10) {
        return ['icon' => '💡', 'color' => 'text-green-400', 'animation' => 'bulb-glow'];
    } elseif ($votes >= 5) {
        return ['icon' => '👍', 'color' => 'text-purple-400', 'animation' => ''];
    } elseif ($votes >= 1) {
        return ['icon' => '💭', 'color' => 'text-gray-400', 'animation' => ''];
    } else {
        return ['icon' => '🆕', 'color' => 'text-gray-300', 'animation' => ''];
    }
}

// 包含公共头部
include_once ROOT_PATH . '/templates/layout/header.php';
?>

<!-- JSON-LD Structured Data -->
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "SoftwareApplication",
    "name": "<?= addslashes(htmlspecialchars($launch['name'] ?? '')) ?>",
    "description": "<?= addslashes(htmlspecialchars($launch['description'] ?? '')) ?>",
    "url": "<?= htmlspecialchars($launch['website_url'] ?? '') ?>",
    "dateCreated": "<?= date('c', strtotime($launch['created_at'])) ?>",
    "author": {
        "@type": "Person",
        "name": "<?= addslashes(htmlspecialchars($launch['username'] ?? 'Anonymous')) ?>"
    },
    "aggregateRating": {
        "@type": "AggregateRating",
        "ratingValue": "<?= min(5, max(1, $launch['votes'] / 10)) ?>",
        "ratingCount": "<?= $launch['votes'] ?>"
    },
    "applicationCategory": "<?= addslashes($categoryInfo['name'] ?? ucfirst($launch['category'])) ?>",
    "operatingSystem": "Web",
    <?php if ($launch['logo_url']): ?>
    "image": "<?= 'https://' . $_SERVER['HTTP_HOST'] . htmlspecialchars($launch['logo_url']) ?>",
    <?php endif; ?>
    "offers": {
        "@type": "Offer",
        "price": "<?= $launch['pricing_model'] === 'free' ? '0' : 'varies' ?>",
        "priceCurrency": "USD"
    }
}
</script>

<!-- 自定义样式 -->
<style>
    .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
    
    .line-clamp-3 {
        display: -webkit-box;
        -webkit-line-clamp: 3;
        line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    /* 投票动画样式 */
    .fire-animation { animation: fire-flicker 2s ease-in-out infinite alternate; }
    @keyframes fire-flicker {
        0%, 100% { transform: scale(1) rotate(-1deg); filter: hue-rotate(0deg) brightness(1); }
        25% { transform: scale(1.05) rotate(1deg); filter: hue-rotate(10deg) brightness(1.1); }
        50% { transform: scale(0.98) rotate(-0.5deg); filter: hue-rotate(-5deg) brightness(0.95); }
        75% { transform: scale(1.02) rotate(0.5deg); filter: hue-rotate(5deg) brightness(1.05); }
    }
    .star-twinkle { animation: star-twinkle 1.5s ease-in-out infinite; }
    @keyframes star-twinkle {
        0%, 100% { opacity: 1; transform: scale(1); filter: brightness(1); }
        50% { opacity: 0.7; transform: scale(1.1); filter: brightness(1.3); }
    }
    .rocket-rise { animation: rocket-rise 3s ease-in-out infinite; }
    @keyframes rocket-rise {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        25% { transform: translateY(-3px) rotate(-1deg); }
        50% { transform: translateY(-5px) rotate(0deg); }
        75% { transform: translateY(-2px) rotate(1deg); }
    }
    .bulb-glow { animation: bulb-glow 2.5s ease-in-out infinite; }
    @keyframes bulb-glow {
        0%, 100% { filter: brightness(1) drop-shadow(0 0 0px rgba(34, 197, 94, 0)); transform: scale(1); }
        50% { filter: brightness(1.2) drop-shadow(0 0 8px rgba(34, 197, 94, 0.6)); transform: scale(1.05); }
    }

    /* 自定义提示窗样式 */
    .custom-modal {
        backdrop-filter: blur(4px);
    }
    
    .custom-alert {
        animation: slideInDown 0.3s ease-out;
    }
    
    @keyframes slideInDown {
        from {
            opacity: 0;
            transform: translateY(-20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* 截图画廊样式 */
    .screenshot-gallery {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1rem;
    }

    .screenshot-item {
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .screenshot-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    }

    .screenshot-item img {
        opacity: 1 !important;
        visibility: visible !important;
    }

    /* 视频容器样式 */
    .video-container {
        max-width: 100%;
        margin: 0 auto;
    }

    .video-container iframe {
        border-radius: 0.5rem;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
    }

    @media (max-width: 768px) {
        .video-container {
            margin: 0 -1rem;
        }

        .video-container iframe {
            border-radius: 0;
        }
    }
</style>

<!-- Main Content -->
<main class="bg-black min-h-screen">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Breadcrumb -->
        <nav class="mb-6">
            <ol class="flex items-center space-x-2 text-sm text-gray-400">
                <li><a href="/" class="hover:text-white transition-colors">Home</a></li>
                <li><i class="fas fa-chevron-right text-xs"></i></li>
                <li><a href="/launches" class="hover:text-white transition-colors">Product Launches</a></li>
                <li><i class="fas fa-chevron-right text-xs"></i></li>
                <li class="text-gray-500 truncate max-w-xs" title="<?= htmlspecialchars($launch['name'] ?? '') ?>">
                    <?= htmlspecialchars(strlen($launch['name'] ?? '') > 50 ? substr($launch['name'] ?? '', 0, 47) . '...' : ($launch['name'] ?? '')) ?>
                </li>
            </ol>
        </nav>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Content -->
            <div class="lg:col-span-2">
                <!-- Product Header -->
                <div class="bg-gray-900 border border-gray-800 rounded-xl p-6 mb-6">
                    <div class="<?= !empty($launch['logo_url']) ? 'flex flex-col sm:flex-row items-start gap-6' : '' ?>">
                        <!-- Product Logo -->
                        <?php if (!empty($launch['logo_url'])): ?>
                        <div class="flex-shrink-0">
                            <img src="<?= htmlspecialchars($launch['logo_url']) ?>"
                                 alt="<?= htmlspecialchars($launch['name']) ?> logo"
                                 class="w-20 h-20 sm:w-24 sm:h-24 rounded-xl object-cover shadow-lg">
                        </div>
                        <?php endif; ?>

                        <!-- Product Info -->
                        <div class="<?= !empty($launch['logo_url']) ? 'flex-1 min-w-0' : '' ?>">
                            <div class="flex flex-wrap items-start justify-between mb-4">
                                <div class="flex-1 min-w-0">
                                    <h1 class="text-2xl lg:text-3xl font-bold text-white mb-2 break-words">
                                        <?= htmlspecialchars($launch['name'] ?? '') ?>
                                    </h1>

                                    <?php if (!empty($launch['tagline'])): ?>
                                        <p class="text-lg text-gray-300 mb-4 leading-relaxed">
                                            <?= htmlspecialchars($launch['tagline'] ?? '') ?>
                                        </p>
                                    <?php endif; ?>

                                    <div class="flex flex-wrap items-center gap-3 mb-4">
                                        <span class="text-sm text-gray-400">
                                            <i class="fas fa-calendar-alt mr-1"></i>
                                            Launched <?= date('M j, Y', strtotime($launch['created_at'])) ?>
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <!-- Status and Category Badges with Action Buttons -->
                            <div class="flex flex-wrap items-center gap-3 mb-4">
                                <span class="px-3 py-1.5 text-sm font-semibold rounded-full <?= $statusColors[$launch['launch_status']] ?? 'bg-gray-600 text-white' ?>">
                                    <?= $statusNames[$launch['launch_status']] ?? ucfirst($launch['launch_status']) ?>
                                </span>
                                <span class="px-3 py-1.5 text-sm font-semibold rounded-full <?= $pricingColors[$launch['pricing_model']] ?? 'bg-gray-600 text-white' ?>">
                                    <?= $pricingNames[$launch['pricing_model']] ?? ucfirst($launch['pricing_model']) ?>
                                </span>
                                <span class="px-3 py-1.5 text-sm font-semibold rounded-full bg-gradient-to-r from-gray-700 to-gray-600 text-gray-200">
                                    <i class="<?= $categoryInfo['icon'] ?? 'fas fa-tag' ?> mr-1"></i>
                                    <?= $categoryInfo['name'] ?? ucfirst($launch['category']) ?>
                                </span>

                                <!-- Action Buttons in same row -->
                                <?php if ($launch['launch_status'] !== 'coming-soon'): ?>
                                    <a href="<?= htmlspecialchars($launch['website_url']) ?>"
                                       target="_blank"
                                       rel="nofollow noopener noreferrer"
                                       onclick="trackLaunchClick(<?= $launch['id'] ?>)"
                                       class="inline-flex items-center px-3 py-1.5 bg-blue-600 hover:bg-blue-700 text-white hover:text-white text-sm font-semibold rounded-full transition-colors">
                                        <i class="fas fa-external-link-alt mr-1.5"></i>
                                        Visit
                                    </a>
                                <?php endif; ?>

                                <button onclick="shareLaunch(<?= $launch['id'] ?>, '<?= htmlspecialchars($launch['name']) ?>')"
                                        class="inline-flex items-center px-3 py-1.5 bg-gray-700 hover:bg-gray-600 text-white text-sm font-semibold rounded-full transition-colors">
                                    <i class="fas fa-share mr-1.5"></i>
                                    Share
                                </button>
                            </div>


                        </div>
                    </div>
                </div>

                <!-- Description -->
                <div class="bg-gray-900 border border-gray-800 rounded-xl p-6 mb-6">
                    <h2 class="text-xl font-bold text-white mb-4 flex items-center">
                        <i class="fas fa-info-circle mr-3 text-blue-400"></i>
                        About This Product
                    </h2>
                    <div class="prose prose-invert max-w-none">
                        <p class="text-gray-300 leading-relaxed whitespace-pre-line"><?= htmlspecialchars($launch['description'] ?? '') ?></p>
                    </div>
                </div>

                <!-- Key Features -->
                <?php if (!empty($keyFeatures)): ?>
                <div class="bg-gray-900 border border-gray-800 rounded-xl p-6 mb-6">
                    <h2 class="text-xl font-bold text-white mb-4 flex items-center">
                        <i class="fas fa-star mr-3 text-yellow-400"></i>
                        Key Features
                    </h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                        <?php foreach ($keyFeatures as $feature): ?>
                            <div class="flex items-start">
                                <i class="fas fa-check-circle text-green-400 mr-3 mt-1 flex-shrink-0"></i>
                                <span class="text-gray-300"><?= htmlspecialchars($feature) ?></span>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Use Cases -->
                <?php if (!empty($useCases)): ?>
                <div class="bg-gray-900 border border-gray-800 rounded-xl p-6 mb-6">
                    <h2 class="text-xl font-bold text-white mb-4 flex items-center">
                        <i class="fas fa-lightbulb mr-3 text-purple-400"></i>
                        Use Cases
                    </h2>
                    <div class="space-y-3">
                        <?php foreach ($useCases as $useCase): ?>
                            <div class="flex items-start">
                                <i class="fas fa-arrow-right text-blue-400 mr-3 mt-1 flex-shrink-0"></i>
                                <span class="text-gray-300"><?= htmlspecialchars($useCase) ?></span>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Video Tutorial -->
                <?php if (!empty($launch['video_tutorial_url'])): ?>
                <div class="bg-gray-900 border border-gray-800 rounded-xl p-6 mb-6">
                    <h2 class="text-xl font-bold text-white mb-4 flex items-center">
                        <i class="fas fa-video mr-3 text-red-400"></i>
                        Video Tutorial
                    </h2>
                    <div class="video-container">
                        <?php
                        $videoUrl = $launch['video_tutorial_url'];
                        $embedUrl = '';

                        // YouTube URL处理
                        if (preg_match('/(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([a-zA-Z0-9_-]+)/', $videoUrl, $matches)) {
                            $videoId = $matches[1];
                            $embedUrl = "https://www.youtube.com/embed/{$videoId}?rel=0&modestbranding=1";
                        }
                        // Vimeo URL处理
                        elseif (preg_match('/vimeo\.com\/(\d+)/', $videoUrl, $matches)) {
                            $videoId = $matches[1];
                            $embedUrl = "https://player.vimeo.com/video/{$videoId}";
                        }
                        // 其他嵌入式视频URL
                        elseif (strpos($videoUrl, 'embed') !== false) {
                            $embedUrl = $videoUrl;
                        }
                        ?>

                        <?php if ($embedUrl): ?>
                            <div class="relative w-full" style="padding-bottom: 56.25%; /* 16:9 aspect ratio */">
                                <iframe
                                    src="<?= htmlspecialchars($embedUrl) ?>"
                                    class="absolute top-0 left-0 w-full h-full rounded-lg"
                                    frameborder="0"
                                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                                    allowfullscreen>
                                </iframe>
                            </div>
                        <?php else: ?>
                            <div class="bg-gray-800 rounded-lg p-6 text-center">
                                <i class="fas fa-external-link-alt text-4xl text-gray-400 mb-4"></i>
                                <p class="text-gray-300 mb-4">Watch the tutorial video</p>
                                <a href="<?= htmlspecialchars($videoUrl) ?>"
                                   target="_blank"
                                   rel="noopener noreferrer"
                                   class="inline-flex items-center px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors">
                                    <i class="fas fa-play mr-2"></i>
                                    Open Video
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Screenshots Gallery -->
                <?php if (!empty($screenshots)): ?>
                <div class="bg-gray-900 border border-gray-800 rounded-xl p-6 mb-6">
                    <h2 class="text-xl font-bold text-white mb-4 flex items-center">
                        <i class="fas fa-images mr-3 text-green-400"></i>
                        Screenshots
                    </h2>
                    <?php $ssCount = count($screenshots); ?>
                    <div class="screenshot-gallery grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                        <?php foreach ($screenshots as $i => $screenshot): ?>
                            <?php
                                $index = $i + 1;
                                $alt = $ssCount > 1
                                    ? sprintf('%s - screenshot %d of %d', $launch['name'], $index, $ssCount)
                                    : sprintf('%s - product screenshot', $launch['name']);
                            ?>
                            <figure class="screenshot-item group bg-gray-800 rounded-xl overflow-hidden border border-gray-700 hover:border-gray-600 transition-all">
                                <!-- 固定16:9容器，避免依赖Tailwind的aspect插件导致高度为0 -->
                                <div class="relative w-full overflow-hidden bg-gray-900" style="padding-top:56.25%">
                                    <img src="<?= htmlspecialchars($screenshot) ?>"
                                         alt="<?= htmlspecialchars($alt) ?>"
                                         loading="lazy"
                                         class="absolute inset-0 w-full h-full object-cover cursor-zoom-in hover:scale-[1.02] transition-transform duration-200"
                                         onclick="openImageModal('<?= htmlspecialchars($screenshot) ?>')">
                                </div>
                                <?php if ($ssCount > 1): ?>
                                    <figcaption class="px-3 py-2 text-xs text-gray-400 bg-gray-900/50 border-t border-gray-700/60">
                                        Screenshot <?= $index ?> of <?= $ssCount ?>
                                    </figcaption>
                                <?php endif; ?>
                            </figure>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1">
                <!-- Vote Section -->
                <div class="bg-gray-900 border border-gray-800 rounded-xl p-6 mb-6">
                    <?php
                    $votes = (int)$launch['votes'];
                    $userVoted = $launch['user_voted'];
                    $isLoggedIn = isset($_SESSION['user_id']);
                    ?>

                    <div class="flex items-center justify-between mb-4">
                        <div>
                            <h3 class="text-lg font-semibold text-white">Support</h3>
                            <p class="text-sm text-gray-400">Show your love</p>
                        </div>
                        <div class="text-right">
                            <div class="text-2xl font-bold text-white"><?= number_format($votes) ?></div>
                            <div class="text-xs text-gray-400"><?= $votes === 1 ? 'vote' : 'votes' ?></div>
                        </div>
                    </div>

                    <button onclick="voteLaunch(<?= $launch['id'] ?>)"
                            class="group w-full flex items-center justify-center space-x-2 px-4 py-3 rounded-lg border-2 transition-all duration-200 <?= $userVoted ? 'bg-blue-600 border-blue-500 text-white' : 'bg-transparent border-gray-600 text-gray-300 hover:border-blue-500 hover:text-blue-400' ?> <?= !$isLoggedIn ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer' ?>"
                            <?= !$isLoggedIn ? 'disabled title="Please login to vote"' : ($userVoted ? 'title="You voted for this product"' : 'title="Vote for this product"') ?>>

                        <svg class="w-5 h-5 transition-transform duration-200 group-hover:scale-110"
                             fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                  d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
                        </svg>

                        <span class="font-medium">
                            <?= $userVoted ? 'Voted' : 'Vote' ?>
                        </span>
                    </button>

                    <?php if (!$isLoggedIn): ?>
                        <p class="text-xs text-gray-400 mt-3 text-center">
                            <a href="/login" class="text-blue-400 hover:text-blue-300 underline transition-colors">Sign in</a> to vote
                        </p>
                    <?php endif; ?>
                </div>

                <!-- Product Stats -->
                <div class="bg-gray-900 border border-gray-800 rounded-xl p-6 mb-6">
                    <h3 class="text-lg font-semibold text-white mb-4">Product Stats</h3>
                    <div class="space-y-3">
                        <div class="flex justify-between items-center">
                            <span class="text-gray-400">Views</span>
                            <span class="text-white font-semibold"><?= number_format($launch['views']) ?></span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-400">Clicks</span>
                            <span class="text-white font-semibold"><?= number_format($launch['clicks']) ?></span>
                        </div>
                        <?php if ($launch['innovation_score']): ?>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-400">Innovation Score</span>
                            <span class="text-white font-semibold"><?= $launch['innovation_score'] ?>/10</span>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Product Details -->
                <?php
                $hasProductDetails = !empty($launch['target_audience']) || !empty($launch['tech_category']);
                ?>
                <?php if ($hasProductDetails || !empty($launch['username'])): ?>
                <div class="bg-gray-900 border border-gray-800 rounded-xl p-6 mb-6">
                    <h3 class="text-lg font-semibold text-white mb-4">Product Details</h3>
                    <div class="space-y-3">
                        <?php if (!empty($launch['target_audience'])): ?>
                        <div>
                            <span class="text-gray-400 text-sm">Target Audience</span>
                            <p class="text-white"><?= htmlspecialchars($launch['target_audience']) ?></p>
                        </div>
                        <?php endif; ?>

                        <?php if (!empty($launch['tech_category'])): ?>
                        <div>
                            <span class="text-gray-400 text-sm">Technology</span>
                            <p class="text-white"><?= htmlspecialchars($launch['tech_category']) ?></p>
                        </div>
                        <?php endif; ?>

                        <?php if (!empty($launch['username'])): ?>
                        <div>
                            <span class="text-gray-400 text-sm">Submitted by</span>
                            <p class="text-white flex items-center">
                                <?php if (!empty($launch['avatar'])): ?>
                                    <img src="<?= htmlspecialchars($launch['avatar']) ?>" alt="Avatar" class="w-5 h-5 rounded-full mr-2">
                                <?php endif; ?>
                                <?= htmlspecialchars($launch['username']) ?>
                            </p>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Tags -->
                <?php if (!empty($tags)): ?>
                <div class="bg-gray-900 border border-gray-800 rounded-xl p-6 mb-6">
                    <h3 class="text-lg font-semibold text-white mb-4">Tags</h3>
                    <div class="flex flex-wrap gap-2">
                        <?php foreach ($tags as $tag): ?>
                            <span class="px-3 py-1 bg-blue-600/20 text-blue-300 text-sm rounded-full border border-blue-600/30">
                                <?= htmlspecialchars($tag) ?>
                            </span>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Social Links -->
                <?php if (!empty($socialLinks)): ?>
                <div class="bg-gray-900 border border-gray-800 rounded-xl p-6 mb-6">
                    <h3 class="text-lg font-semibold text-white mb-4">Social Links</h3>
                    <div class="space-y-3">
                        <?php foreach ($socialLinks as $platform => $url): ?>
                            <a href="<?= htmlspecialchars($url) ?>"
                               target="_blank"
                               rel="nofollow noopener noreferrer"
                               class="flex items-center text-gray-300 hover:text-white transition-colors">
                                <?php
                                $icons = [
                                    'twitter' => 'fab fa-twitter text-blue-400',
                                    'linkedin' => 'fab fa-linkedin text-blue-600',
                                    'github' => 'fab fa-github text-gray-400',
                                    'website' => 'fas fa-globe text-green-400'
                                ];
                                $icon = $icons[$platform] ?? 'fas fa-link text-gray-400';
                                ?>
                                <i class="<?= $icon ?> mr-3 w-5"></i>
                                <?= ucfirst($platform) ?>
                                <i class="fas fa-external-link-alt ml-auto text-xs"></i>
                            </a>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Related Products -->
                <?php if (!empty($relatedLaunches)): ?>
                <div class="bg-gray-900 border border-gray-800 rounded-xl p-6">
                    <h3 class="text-lg font-semibold text-white mb-4">Related Products</h3>
                    <div class="space-y-4">
                        <?php foreach ($relatedLaunches as $related): ?>
                            <div class="<?= !empty($related['logo_url']) ? 'flex items-start space-x-3' : '' ?> p-3 bg-gray-800/50 rounded-lg hover:bg-gray-800 transition-colors">
                                <?php if ($related['logo_url']): ?>
                                    <img src="<?= htmlspecialchars($related['logo_url']) ?>"
                                         alt="<?= htmlspecialchars($related['name']) ?>"
                                         class="w-10 h-10 rounded-lg object-cover flex-shrink-0">
                                <?php endif; ?>

                                <div class="<?= !empty($related['logo_url']) ? 'flex-1 min-w-0' : '' ?>">
                                    <a href="/launch/<?= htmlspecialchars($related['slug']) ?>"
                                       class="text-white hover:text-blue-300 font-medium text-sm transition-colors line-clamp-2">
                                        <?= htmlspecialchars($related['name']) ?>
                                    </a>
                                    <?php if ($related['tagline']): ?>
                                        <p class="text-gray-400 text-xs mt-1 line-clamp-2"><?= htmlspecialchars($related['tagline']) ?></p>
                                    <?php endif; ?>
                                    <div class="flex items-center space-x-3 mt-2 text-xs text-gray-500">
                                        <span><?= $related['votes'] ?> votes</span>
                                        <span><?= number_format($related['views']) ?> views</span>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>

                    <div class="mt-4 text-center">
                        <form method="POST" action="/launches" style="display: inline;">
                            <input type="hidden" name="category" value="<?= htmlspecialchars($launch['category']) ?>">
                            <button type="submit" class="text-blue-400 hover:text-blue-300 text-sm transition-colors bg-transparent border-none cursor-pointer">
                                View more in <?= $categoryInfo['name'] ?? ucfirst($launch['category']) ?> →
                            </button>
                        </form>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- 自定义提示窗 -->
    <div id="custom-modal" class="fixed inset-0 bg-black bg-opacity-50 custom-modal hidden z-50 flex items-center justify-center p-4">
        <div class="bg-gray-900 rounded-xl p-6 max-w-md w-full mx-4 custom-alert border border-gray-700">
            <div class="flex items-center mb-4">
                <div id="modal-icon" class="w-10 h-10 rounded-full flex items-center justify-center mr-3">
                    <i id="modal-icon-class" class="text-xl"></i>
                </div>
                <h3 id="modal-title" class="text-lg font-semibold text-white"></h3>
            </div>
            <p id="modal-message" class="text-gray-300 mb-6"></p>
            <div class="flex justify-end space-x-3">
                <button id="modal-cancel" class="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors hidden">
                    Cancel
                </button>
                <button id="modal-confirm" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors">
                    OK
                </button>
            </div>
        </div>
    </div>

    <!-- 图片模态框 -->
    <div id="image-modal" class="fixed inset-0 bg-black bg-opacity-90 hidden z-50 flex items-center justify-center p-4">
        <div class="relative max-w-4xl max-h-full">
            <button onclick="closeImageModal()" class="absolute top-4 right-4 text-white hover:text-gray-300 text-2xl z-10">
                <i class="fas fa-times"></i>
            </button>
            <img id="modal-image" src="" alt="" class="max-w-full max-h-full object-contain rounded-lg">
        </div>
    </div>
</main>

<!-- 包含公共底部 -->
<?php include_once ROOT_PATH . '/templates/layout/footer.php'; ?>

<!-- JavaScript -->
<script>
    // 自定义提示窗函数
    function showCustomAlert(title, message, type = 'info', showCancel = false) {
        const modal = document.getElementById('custom-modal');
        const modalIcon = document.getElementById('modal-icon');
        const modalIconClass = document.getElementById('modal-icon-class');
        const modalTitle = document.getElementById('modal-title');
        const modalMessage = document.getElementById('modal-message');
        const modalCancel = document.getElementById('modal-cancel');
        const modalConfirm = document.getElementById('modal-confirm');

        // 设置图标和颜色
        const config = {
            success: { icon: 'fas fa-check', bgColor: 'bg-green-600', iconColor: 'text-white' },
            error: { icon: 'fas fa-times', bgColor: 'bg-red-600', iconColor: 'text-white' },
            warning: { icon: 'fas fa-exclamation', bgColor: 'bg-yellow-600', iconColor: 'text-white' },
            info: { icon: 'fas fa-info', bgColor: 'bg-blue-600', iconColor: 'text-white' }
        };

        const typeConfig = config[type] || config.info;
        modalIcon.className = `w-10 h-10 rounded-full flex items-center justify-center mr-3 ${typeConfig.bgColor}`;
        modalIconClass.className = `${typeConfig.icon} text-xl ${typeConfig.iconColor}`;
        modalTitle.textContent = title;
        modalMessage.textContent = message;

        // 显示/隐藏取消按钮
        modalCancel.classList.toggle('hidden', !showCancel);

        modal.classList.remove('hidden');

        return new Promise((resolve) => {
            modalConfirm.onclick = () => {
                modal.classList.add('hidden');
                resolve(true);
            };
            modalCancel.onclick = () => {
                modal.classList.add('hidden');
                resolve(false);
            };
        });
    }

    // 投票功能
    async function voteLaunch(launchId) {
        <?php if (!isset($_SESSION['user_id'])): ?>
            showCustomAlert('Login Required', 'Please login to vote for products.', 'warning');
            return;
        <?php endif; ?>

        try {
            const response = await fetch('/ajax/vote-launch.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `launch_id=${launchId}`
            });

            const result = await response.json();

            if (result.success) {
                // 更新投票显示
                location.reload(); // 简单的重新加载，可以优化为动态更新
            } else {
                showCustomAlert('Vote Failed', result.message || 'Failed to vote. Please try again.', 'error');
            }
        } catch (error) {
            console.error('Vote error:', error);
            showCustomAlert('Error', 'An error occurred while voting. Please try again.', 'error');
        }
    }

    // 跟踪点击
    function trackLaunchClick(launchId) {
        fetch('/ajax/track-launch-click.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `launch_id=${launchId}`
        }).catch(error => console.error('Tracking error:', error));
    }

    // 分享功能
    async function shareLaunch(launchId, productName) {
        const url = window.location.href;
        const text = `Check out ${productName} - an amazing product launch!`;

        if (navigator.share) {
            try {
                await navigator.share({
                    title: productName,
                    text: text,
                    url: url
                });
            } catch (error) {
                if (error.name !== 'AbortError') {
                    fallbackShare(url, text);
                }
            }
        } else {
            fallbackShare(url, text);
        }
    }

    function fallbackShare(url, text) {
        if (navigator.clipboard) {
            navigator.clipboard.writeText(url).then(() => {
                showCustomAlert('Link Copied', 'Product link has been copied to clipboard!', 'success');
            }).catch(() => {
                showShareModal(url, text);
            });
        } else {
            showShareModal(url, text);
        }
    }

    function showShareModal(url, text) {
        const shareText = `Share this product:\n\n${text}\n${url}`;
        showCustomAlert('Share Product', shareText, 'info');
    }

    // 图片模态框功能
    function openImageModal(imageSrc) {
        const modal = document.getElementById('image-modal');
        const modalImage = document.getElementById('modal-image');

        modalImage.src = imageSrc;
        modal.classList.remove('hidden');

        // 防止背景滚动
        document.body.style.overflow = 'hidden';
    }

    function closeImageModal() {
        const modal = document.getElementById('image-modal');
        modal.classList.add('hidden');

        // 恢复背景滚动
        document.body.style.overflow = '';
    }

    // 点击模态框外部关闭
    document.getElementById('custom-modal').addEventListener('click', function(e) {
        if (e.target === this) {
            this.classList.add('hidden');
        }
    });

    document.getElementById('image-modal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeImageModal();
        }
    });

    // ESC键关闭模态框
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            document.getElementById('custom-modal').classList.add('hidden');
            closeImageModal();
        }
    });

    // 页面加载完成后的初始化
    document.addEventListener('DOMContentLoaded', function() {
        // 可以在这里添加页面初始化代码
        console.log('Launch detail page loaded');
    });
</script>
</body>
</html>
