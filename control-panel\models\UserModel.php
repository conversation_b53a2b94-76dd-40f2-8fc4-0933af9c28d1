<?php
/**
 * 用户模型类
 * 处理前端用户相关的数据库操作
 */

// 防止直接访问
if (!defined('ROOT_PATH')) {
    exit('Direct access not allowed');
}

// 加载基础模型
require_once ROOT_PATH . '/control-panel/models/BaseModel.php';

class UserModel extends BaseModel {
    
    protected $table = 'pt_member';
    protected $primaryKey = 'id';
    protected $fillable = [
        'username', 'email', 'password', 'first_name', 'last_name',
        'avatar', 'phone', 'country', 'city', 'timezone', 'language',
        'status', 'email_verified_at', 'phone_verified_at',
        'last_login_at', 'last_login_ip', 'login_count',
        'subscription_type', 'subscription_expires_at',
        'preferences', 'api_token', 'api_requests_count',
        'api_rate_limit', 'referral_code', 'referred_by'
    ];
    protected $hidden = ['password', 'api_token'];
    
    /**
     * 根据邮箱查找用户
     */
    public function findByEmail($email) {
        return $this->findWhere(['email' => $email]);
    }
    
    /**
     * 根据用户名查找用户
     */
    public function findByUsername($username) {
        return $this->findWhere(['username' => $username]);
    }
    
    /**
     * 根据API Token查找用户
     */
    public function findByApiToken($token) {
        return $this->findWhere(['api_token' => $token]);
    }
    
    /**
     * 获取用户统计信息
     */
    public function getStatistics() {
        $stats = [
            'total' => $this->count(),
            'active' => $this->count(['status' => 'active']),
            'inactive' => $this->count(['status' => 'inactive']),
            'verified' => 0,
            'premium' => 0,
            'by_country' => [],
            'by_subscription' => [],
            'registration_trend' => [],
            'login_trend' => []
        ];
        
        // 已验证用户
        $sql = "SELECT COUNT(*) FROM {$this->table} WHERE email_verified_at IS NOT NULL";
        $stats['verified'] = $this->db->fetchColumn($sql);
        
        // 付费用户
        $sql = "SELECT COUNT(*) FROM {$this->table} WHERE subscription_type != 'free' AND subscription_expires_at > NOW()";
        $stats['premium'] = $this->db->fetchColumn($sql);
        
        // 按国家统计
        $sql = "SELECT country, COUNT(*) as count FROM {$this->table} WHERE country IS NOT NULL GROUP BY country ORDER BY count DESC LIMIT 10";
        $countryStats = $this->db->fetchAll($sql);
        foreach ($countryStats as $stat) {
            $stats['by_country'][$stat['country']] = $stat['count'];
        }
        
        // 按订阅类型统计
        $sql = "SELECT subscription_type, COUNT(*) as count FROM {$this->table} GROUP BY subscription_type";
        $subscriptionStats = $this->db->fetchAll($sql);
        foreach ($subscriptionStats as $stat) {
            $stats['by_subscription'][$stat['subscription_type']] = $stat['count'];
        }
        
        // 注册趋势（最近30天）
        $sql = "
            SELECT DATE(created_at) as date, COUNT(*) as count
            FROM {$this->table}
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
            GROUP BY DATE(created_at)
            ORDER BY date ASC
        ";
        $stats['registration_trend'] = $this->db->fetchAll($sql);
        
        // 登录趋势（最近30天）
        $sql = "
            SELECT DATE(last_login_at) as date, COUNT(*) as count
            FROM {$this->table}
            WHERE last_login_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
            GROUP BY DATE(last_login_at)
            ORDER BY date ASC
        ";
        $stats['login_trend'] = $this->db->fetchAll($sql);
        
        return $stats;
    }
    
    /**
     * 搜索用户
     */
    public function searchUsers($keyword, $filters = []) {
        $sql = "SELECT * FROM {$this->table} WHERE 1=1";
        $params = [];
        
        // 关键词搜索
        if (!empty($keyword)) {
            $sql .= " AND (username LIKE :keyword OR email LIKE :keyword OR first_name LIKE :keyword OR last_name LIKE :keyword)";
            $params['keyword'] = "%{$keyword}%";
        }
        
        // 状态筛选
        if (!empty($filters['status'])) {
            $sql .= " AND status = :status";
            $params['status'] = $filters['status'];
        }
        
        // 国家筛选
        if (!empty($filters['country'])) {
            $sql .= " AND country = :country";
            $params['country'] = $filters['country'];
        }
        
        // 订阅类型筛选
        if (!empty($filters['subscription_type'])) {
            $sql .= " AND subscription_type = :subscription_type";
            $params['subscription_type'] = $filters['subscription_type'];
        }
        
        // 验证状态筛选
        if (isset($filters['verified'])) {
            if ($filters['verified']) {
                $sql .= " AND email_verified_at IS NOT NULL";
            } else {
                $sql .= " AND email_verified_at IS NULL";
            }
        }
        
        // 日期范围筛选
        if (!empty($filters['date_from'])) {
            $sql .= " AND created_at >= :date_from";
            $params['date_from'] = $filters['date_from'];
        }
        
        if (!empty($filters['date_to'])) {
            $sql .= " AND created_at <= :date_to";
            $params['date_to'] = $filters['date_to'] . ' 23:59:59';
        }
        
        // 排序
        $orderBy = $filters['sort'] ?? 'created_at';
        $orderDirection = $filters['order'] ?? 'DESC';
        $sql .= " ORDER BY {$orderBy} {$orderDirection}";
        
        $results = $this->db->fetchAll($sql, $params);
        return array_map([$this, 'hideFields'], $results);
    }
    
    /**
     * 获取活跃用户
     */
    public function getActiveUsers($days = 30, $limit = 100) {
        $sql = "
            SELECT * FROM {$this->table}
            WHERE last_login_at >= DATE_SUB(NOW(), INTERVAL :days DAY)
            AND status = 'active'
            ORDER BY last_login_at DESC
            LIMIT :limit
        ";
        
        $results = $this->db->fetchAll($sql, ['days' => $days, 'limit' => $limit]);
        return array_map([$this, 'hideFields'], $results);
    }
    
    /**
     * 获取新注册用户
     */
    public function getNewUsers($days = 7, $limit = 50) {
        $sql = "
            SELECT * FROM {$this->table}
            WHERE created_at >= DATE_SUB(NOW(), INTERVAL :days DAY)
            ORDER BY created_at DESC
            LIMIT :limit
        ";
        
        $results = $this->db->fetchAll($sql, ['days' => $days, 'limit' => $limit]);
        return array_map([$this, 'hideFields'], $results);
    }
    
    /**
     * 获取付费用户
     */
    public function getPremiumUsers() {
        $sql = "
            SELECT * FROM {$this->table}
            WHERE subscription_type != 'free' 
            AND subscription_expires_at > NOW()
            ORDER BY subscription_expires_at DESC
        ";
        
        $results = $this->db->fetchAll($sql);
        return array_map([$this, 'hideFields'], $results);
    }
    
    /**
     * 获取即将到期的订阅
     */
    public function getExpiringSubscriptions($days = 7) {
        $sql = "
            SELECT * FROM {$this->table}
            WHERE subscription_type != 'free'
            AND subscription_expires_at BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL :days DAY)
            ORDER BY subscription_expires_at ASC
        ";
        
        $results = $this->db->fetchAll($sql, ['days' => $days]);
        return array_map([$this, 'hideFields'], $results);
    }
    
    /**
     * 更新登录信息
     */
    public function updateLoginInfo($id, $ip) {
        $data = [
            'last_login_at' => date('Y-m-d H:i:s'),
            'last_login_ip' => $ip,
            'login_count' => $this->db->fetchColumn(
                "SELECT login_count FROM {$this->table} WHERE id = :id", 
                ['id' => $id]
            ) + 1
        ];
        
        return $this->update($id, $data);
    }
    
    /**
     * 验证邮箱
     */
    public function verifyEmail($id) {
        return $this->update($id, ['email_verified_at' => date('Y-m-d H:i:s')]);
    }
    
    /**
     * 验证手机
     */
    public function verifyPhone($id) {
        return $this->update($id, ['phone_verified_at' => date('Y-m-d H:i:s')]);
    }
    
    /**
     * 更新订阅
     */
    public function updateSubscription($id, $type, $expiresAt = null) {
        $data = ['subscription_type' => $type];
        
        if ($expiresAt) {
            $data['subscription_expires_at'] = $expiresAt;
        } elseif ($type === 'free') {
            $data['subscription_expires_at'] = null;
        }
        
        return $this->update($id, $data);
    }
    
    /**
     * 生成API Token
     */
    public function generateApiToken($id) {
        $token = bin2hex(random_bytes(32));
        $this->update($id, ['api_token' => $token]);
        return $token;
    }
    
    /**
     * 重置API Token
     */
    public function resetApiToken($id) {
        return $this->generateApiToken($id);
    }
    
    /**
     * 增加API请求计数
     */
    public function incrementApiRequests($id) {
        $sql = "UPDATE {$this->table} SET api_requests_count = api_requests_count + 1 WHERE id = :id";
        return $this->db->query($sql, ['id' => $id]);
    }
    
    /**
     * 重置API请求计数
     */
    public function resetApiRequests($id) {
        return $this->update($id, ['api_requests_count' => 0]);
    }
    
    /**
     * 获取用户偏好设置
     */
    public function getUserPreferences($id) {
        $user = $this->find($id);
        if (!$user || !$user['preferences']) {
            return [];
        }
        
        return json_decode($user['preferences'], true) ?: [];
    }
    
    /**
     * 更新用户偏好设置
     */
    public function updateUserPreferences($id, $preferences) {
        $preferencesJson = json_encode($preferences);
        return $this->update($id, ['preferences' => $preferencesJson]);
    }
    
    /**
     * 获取用户工具使用统计
     */
    public function getUserToolUsage($id, $days = 30) {
        $sql = "
            SELECT t.name, t.slug, COUNT(u.id) as usage_count
            FROM pt_tool_usage u
            JOIN pt_tool t ON u.tool_id = t.id
            WHERE u.user_id = :user_id
            AND u.created_at >= DATE_SUB(NOW(), INTERVAL :days DAY)
            GROUP BY t.id
            ORDER BY usage_count DESC
            LIMIT 20
        ";
        
        return $this->db->fetchAll($sql, ['user_id' => $id, 'days' => $days]);
    }
    
    /**
     * 获取用户活动日志
     */
    public function getUserActivityLog($id, $limit = 50) {
        $sql = "
            SELECT activity_type, description, ip_address, created_at
            FROM pt_member_activity_log
            WHERE user_id = :user_id
            ORDER BY created_at DESC
            LIMIT :limit
        ";
        
        return $this->db->fetchAll($sql, ['user_id' => $id, 'limit' => $limit]);
    }
    
    /**
     * 批量更新状态
     */
    public function batchUpdateStatus($ids, $status) {
        if (empty($ids)) {
            return 0;
        }
        
        $placeholders = implode(',', array_fill(0, count($ids), '?'));
        $sql = "UPDATE {$this->table} SET status = ?, updated_at = NOW() WHERE id IN ({$placeholders})";
        
        $params = array_merge([$status], $ids);
        $stmt = $this->db->query($sql, $params);
        
        return $stmt->rowCount();
    }
    
    /**
     * 导出用户数据
     */
    public function exportUsers($filters = [], $format = 'csv') {
        $users = $this->searchUsers('', $filters);
        
        if ($format === 'csv') {
            return $this->exportToCsv($users);
        } elseif ($format === 'json') {
            return json_encode($users, JSON_PRETTY_PRINT);
        }
        
        return $users;
    }
    
    /**
     * 导出为CSV格式
     */
    private function exportToCsv($users) {
        $output = fopen('php://temp', 'r+');
        
        // 写入标题行
        $headers = [
            'ID', 'Username', 'Email', 'First Name', 'Last Name',
            'Country', 'City', 'Status', 'Subscription Type',
            'Email Verified', 'Created At', 'Last Login'
        ];
        fputcsv($output, $headers);
        
        // 写入数据行
        foreach ($users as $user) {
            $row = [
                $user['id'],
                $user['username'],
                $user['email'],
                $user['first_name'],
                $user['last_name'],
                $user['country'],
                $user['city'],
                $user['status'],
                $user['subscription_type'],
                $user['email_verified_at'] ? 'Yes' : 'No',
                $user['created_at'],
                $user['last_login_at']
            ];
            fputcsv($output, $row);
        }
        
        rewind($output);
        $csv = stream_get_contents($output);
        fclose($output);
        
        return $csv;
    }
    
    /**
     * 获取国家列表
     */
    public function getCountries() {
        $sql = "SELECT DISTINCT country FROM {$this->table} WHERE country IS NOT NULL AND country != '' ORDER BY country";
        $results = $this->db->fetchAll($sql);
        return array_column($results, 'country');
    }
    
    /**
     * 获取推荐统计
     */
    public function getReferralStats($id) {
        $sql = "
            SELECT 
                COUNT(*) as total_referrals,
                COUNT(CASE WHEN status = 'active' THEN 1 END) as active_referrals,
                COUNT(CASE WHEN subscription_type != 'free' THEN 1 END) as premium_referrals
            FROM {$this->table}
            WHERE referred_by = :user_id
        ";
        
        return $this->db->fetch($sql, ['user_id' => $id]);
    }
}
?>
