<?php
/**
 * API管理辅助函数
 */

// 防止直接访问
if (!defined('APP_INITIALIZED')) {
    die('Direct access not allowed');
}

/**
 * 获取所有API平台
 */
function getAllPlatforms() {
    global $pdo;
    
    $stmt = $pdo->query("SELECT * FROM pt_service_platform WHERE is_active = 1 ORDER BY name");
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

/**
 * 获取平台的API密钥
 */
function getPlatformKeys($platformId, $activeOnly = true) {
    global $pdo;
    
    $sql = "SELECT * FROM pt_service_key WHERE platform_id = ?";
    $params = [$platformId];
    
    if ($activeOnly) {
        $sql .= " AND is_active = 1";
    }
    
    $sql .= " ORDER BY name";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

/**
 * 获取平台的可用模型
 */
function getPlatformModels($platformId, $activeOnly = true) {
    global $pdo;
    
    $sql = "SELECT * FROM pt_service_model WHERE platform_id = ?";
    $params = [$platformId];
    
    if ($activeOnly) {
        $sql .= " AND is_active = 1";
    }
    
    $sql .= " ORDER BY sort_order, model_name";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

/**
 * 获取平台配置
 */
function getPlatformConfig($platformId, $configKey = null) {
    global $pdo;
    
    if ($configKey) {
        $stmt = $pdo->prepare("SELECT config_value FROM pt_service_config WHERE platform_id = ? AND config_key = ?");
        $stmt->execute([$platformId, $configKey]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result ? $result['config_value'] : null;
    } else {
        $stmt = $pdo->prepare("SELECT config_key, config_value FROM pt_service_config WHERE platform_id = ?");
        $stmt->execute([$platformId]);
        $configs = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $result = [];
        foreach ($configs as $config) {
            $result[$config['config_key']] = $config['config_value'];
        }
        return $result;
    }
}

/**
 * 设置平台配置
 */
function setPlatformConfig($platformId, $configKey, $configValue) {
    global $pdo;
    
    $stmt = $pdo->prepare("
        INSERT INTO pt_service_config (platform_id, config_key, config_value)
        VALUES (?, ?, ?)
        ON DUPLICATE KEY UPDATE config_value = VALUES(config_value)
    ");
    
    return $stmt->execute([$platformId, $configKey, $configValue]);
}

/**
 * 获取可用的API密钥（负载均衡）
 */
function getAvailableApiKey($platformCode) {
    global $pdo;
    
    $stmt = $pdo->prepare("
        SELECT ak.*
        FROM pt_service_key ak
        JOIN pt_service_platform ap ON ak.platform_id = ap.id
        WHERE ap.code = ? AND ak.is_active = 1
        ORDER BY ak.usage_count ASC, RAND()
        LIMIT 1
    ");
    
    $stmt->execute([$platformCode]);
    return $stmt->fetch(PDO::FETCH_ASSOC);
}

/**
 * 更新API密钥使用统计
 */
function updateKeyUsage($keyId) {
    global $pdo;
    
    $stmt = $pdo->prepare("
        UPDATE pt_service_key
        SET usage_count = usage_count + 1, last_used_at = NOW()
        WHERE id = ?
    ");
    
    return $stmt->execute([$keyId]);
}

/**
 * 获取平台的默认模型
 */
function getPlatformDefaultModel($platformCode) {
    global $pdo;
    
    $stmt = $pdo->prepare("
        SELECT ac.config_value
        FROM pt_service_config ac
        JOIN pt_service_platform ap ON ac.platform_id = ap.id
        WHERE ap.code = ? AND ac.config_key = 'default_model'
    ");
    
    $stmt->execute([$platformCode]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($result) {
        return $result['config_value'];
    }
    
    // 如果没有配置默认模型，返回平台的第一个模型
    $stmt = $pdo->prepare("
        SELECT am.model_code
        FROM pt_service_model am
        JOIN pt_service_platform ap ON am.platform_id = ap.id
        WHERE ap.code = ? AND am.is_active = 1
        ORDER BY am.sort_order, am.model_name
        LIMIT 1
    ");
    
    $stmt->execute([$platformCode]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    return $result ? $result['model_code'] : null;
}

/**
 * 验证API密钥格式
 */
function validateApiKey($platformCode, $apiKey) {
    $patterns = [
        'bigmodel' => '/^[a-f0-9]{32}\.[a-zA-Z0-9_-]+$/', // 智谱AI格式
        'siliconflow' => '/^sk-[a-zA-Z0-9]{48}$/', // 硅基流动格式
        'aihubmix' => '/^[a-zA-Z0-9_-]{20,}$/' // AiHubMix格式（较宽松）
    ];
    
    if (!isset($patterns[$platformCode])) {
        return true; // 未知平台，不验证格式
    }
    
    return preg_match($patterns[$platformCode], $apiKey);
}

/**
 * 获取API统计信息
 */
function getApiStats() {
    global $pdo;
    
    $stats = [];
    
    // 总平台数
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM pt_service_platform WHERE is_active = 1");
    $stats['total_platforms'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];

    // 总密钥数
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM pt_service_key WHERE is_active = 1");
    $stats['total_keys'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];

    // 总模型数
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM pt_service_model WHERE is_active = 1");
    $stats['total_models'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];

    // 总使用次数
    $stmt = $pdo->query("SELECT SUM(usage_count) as total FROM pt_service_key");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    $stats['total_usage'] = $result['total'] ?: 0;
    
    // 按平台统计
    $stmt = $pdo->query("
        SELECT
            ap.name as platform_name,
            ap.code as platform_code,
            COUNT(DISTINCT ak.id) as key_count,
            COUNT(DISTINCT am.id) as model_count,
            COALESCE(SUM(ak.usage_count), 0) as usage_count
        FROM pt_service_platform ap
        LEFT JOIN pt_service_key ak ON ap.id = ak.platform_id AND ak.is_active = 1
        LEFT JOIN pt_service_model am ON ap.id = am.platform_id AND am.is_active = 1
        WHERE ap.is_active = 1
        GROUP BY ap.id, ap.name, ap.code
        ORDER BY ap.name
    ");
    
    $stats['by_platform'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    return $stats;
}

/**
 * 测试API密钥是否有效
 */
function testApiKey($platformCode, $apiKey) {
    // 这里可以实现实际的API测试逻辑
    // 目前只做格式验证
    return validateApiKey($platformCode, $apiKey);
}

/**
 * 获取推荐的模型配置
 */
function getRecommendedModels() {
    return [
        'bigmodel' => [
            'default' => 'glm-4.5-flash',
            'vision' => 'glm-4v',
            'audio' => 'glm-4-voice',
            'reasoning' => 'glm-4.5'
        ],
        'siliconflow' => [
            'default' => 'Qwen/Qwen2.5-72B-Instruct',
            'fast' => 'Qwen/Qwen2.5-7B-Instruct',
            'reasoning' => 'deepseek-ai/DeepSeek-V2.5'
        ],
        'aihubmix' => [
            'default' => 'claude-sonnet-4-20250514',
            'vision' => 'gpt-4.1',
            'reasoning' => 'claude-sonnet-4-20250514'
        ]
    ];
}

/**
 * 根据平台格式化模型代码
 */
function formatModelCodeForPlatform($platformCode, $modelCode) {
    switch ($platformCode) {
        case 'bigmodel':
            // BigModel直接使用模型名，不需要厂商前缀
            return $modelCode;

        case 'siliconflow':
            // SiliconFlow需要厂商前缀，如果没有则添加默认前缀
            if (strpos($modelCode, '/') === false) {
                // 根据模型名添加对应的厂商前缀
                if (strpos($modelCode, 'Qwen') === 0) {
                    return 'Qwen/' . $modelCode;
                } elseif (strpos($modelCode, 'DeepSeek') === 0) {
                    return 'deepseek-ai/' . $modelCode;
                } elseif (strpos($modelCode, 'Llama') === 0) {
                    return 'meta-llama/' . $modelCode;
                } elseif (strpos($modelCode, 'Yi') === 0) {
                    return '01-ai/' . $modelCode;
                }
            }
            return $modelCode;

        case 'aihubmix':
            // AiHubMix直接使用模型名
            return $modelCode;

        default:
            return $modelCode;
    }
}

/**
 * 验证模型代码格式是否正确
 */
function validateModelCodeFormat($platformCode, $modelCode) {
    switch ($platformCode) {
        case 'bigmodel':
            // BigModel模型应该以glm-开头
            return strpos($modelCode, 'glm-') === 0;

        case 'siliconflow':
            // SiliconFlow模型应该包含厂商前缀
            return strpos($modelCode, '/') !== false;

        case 'aihubmix':
            // AiHubMix支持多种模型格式
            return !empty($modelCode);

        default:
            return !empty($modelCode);
    }
}
?>
