<?php
/**
 * CSS Minifier Tool Page
 * 遵循01-UI设计规范.md - 零圆角设计
 */

$currentPage = 'tool-css-minifier';
$breadcrumbs = [
    ['name' => 'Home', 'url' => '/'],
    ['name' => 'Tools', 'url' => '/tools'],
    ['name' => 'Development', 'url' => '/tools/development'],
    ['name' => 'CSS Minifier']
];

// 包含动态SEO助手
require_once ROOT_PATH . '/app/helpers/dynamic-seo.php';

// 生成SEO数据 - 优化长度
$seoData = [
    'title' => 'Free CSS Minifier Online - Prompt2Tool',
    'description' => 'Minify and compress CSS code to reduce file size and improve website performance. Free online CSS optimization tool.',
    'keywords' => 'css minifier, css compressor, css optimizer, minify css, compress css, css file size reducer, online css minifier, free css minifier',
    'og_title' => 'Free CSS Minifier Online - Prompt2Tool',
    'og_description' => 'Minify and compress CSS code to reduce file size and improve website performance.',
    'canonical' => getCurrentURL()
];

// 包含公共头部
include_once ROOT_PATH . '/templates/layout/header.php';
?>

<!-- 工具页面主体 -->
<div class="bg-gray-900 text-white">

    <!-- Hero Section with SEO optimized content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-12">
        <div class="text-center mb-12">
            <div class="flex items-center justify-center mb-6">
                <div class="bg-green-600 p-4 mr-4">
                    <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                </div>
                <div class="text-left">
                    <h1 class="text-4xl font-bold text-white mb-2">CSS Minifier</h1>
                    <p class="text-xl text-gray-400">Compress and optimize your CSS code</p>
                </div>
            </div>
        </div>

        <!-- Tool Interface -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
            <!-- Input Area -->
            <div class="bg-gray-800 border border-gray-700 p-6">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-xl font-semibold text-white">Input CSS Code</h2>
                    <div class="flex space-x-2">
                        <button id="clearInput" class="px-3 py-1 bg-gray-700 text-gray-300 text-sm hover:bg-gray-600 transition-colors">
                            Clear
                        </button>
                        <button id="pasteInput" class="px-3 py-1 bg-blue-600 text-white text-sm hover:bg-blue-700 transition-colors">
                            Paste
                        </button>
                    </div>
                </div>
                <textarea 
                    id="cssInput" 
                    class="w-full h-96 bg-gray-900 border border-gray-600 text-gray-100 p-4 font-mono text-sm focus:outline-none focus:border-green-500 resize-none"
                    placeholder="Paste your CSS code here..."
                    spellcheck="false"
                ></textarea>
                <div class="mt-2 flex justify-between text-sm text-gray-400">
                    <span id="inputStats">0 characters, 0 lines</span>
                    <span id="inputSize">0 KB</span>
                </div>
            </div>

            <!-- Output Area -->
            <div class="bg-gray-800 border border-gray-700 p-6">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-xl font-semibold text-white">Minified CSS Code</h2>
                    <div class="flex space-x-2">
                        <div class="relative">
                            <button id="copyOutput" class="px-3 py-1 bg-green-600 text-white text-sm hover:bg-green-700 transition-colors">
                                Copy
                            </button>
                            <div id="copyTooltip" class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-700 text-white text-xs rounded pointer-events-none whitespace-nowrap z-50" style="opacity: 0; visibility: hidden; transition: opacity 0.2s, visibility 0.2s;">
                                Copy to clipboard
                            </div>
                        </div>
                        <div class="relative">
                            <button id="downloadOutput" class="px-3 py-1 bg-purple-600 text-white text-sm hover:bg-purple-700 transition-colors">
                                Download
                            </button>
                            <div id="downloadTooltip" class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-700 text-white text-xs rounded pointer-events-none whitespace-nowrap z-50" style="opacity: 0; visibility: hidden; transition: opacity 0.2s, visibility 0.2s;">
                                Download as file
                            </div>
                        </div>
                    </div>
                </div>
                <textarea 
                    id="cssOutput" 
                    class="w-full h-96 bg-gray-900 border border-gray-600 text-gray-100 p-4 font-mono text-sm focus:outline-none resize-none"
                    readonly
                    placeholder="Minified CSS will appear here..."
                ></textarea>
                <div class="mt-2 flex justify-between text-sm text-gray-400">
                    <span id="outputStats">0 characters, 0 lines</span>
                    <span id="outputSize">0 KB</span>
                </div>
            </div>
        </div>

        <!-- Minification Options -->
        <div class="bg-gray-800 border border-gray-700 p-6 mb-8">
            <h2 class="text-xl font-semibold text-white mb-4">CSS Minification Options</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="space-y-4">
                    <label class="flex items-center">
                        <input type="checkbox" id="removeComments" class="mr-2 bg-gray-900 border-gray-600 text-green-600 focus:ring-green-500" checked>
                        <span class="text-gray-300">Remove comments</span>
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" id="removeWhitespace" class="mr-2 bg-gray-900 border-gray-600 text-green-600 focus:ring-green-500" checked>
                        <span class="text-gray-300">Remove unnecessary whitespace</span>
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" id="removeSemicolons" class="mr-2 bg-gray-900 border-gray-600 text-green-600 focus:ring-green-500" checked>
                        <span class="text-gray-300">Remove unnecessary semicolons</span>
                    </label>
                </div>
                <div class="space-y-4">
                    <label class="flex items-center">
                        <input type="checkbox" id="shortenColors" class="mr-2 bg-gray-900 border-gray-600 text-green-600 focus:ring-green-500" checked>
                        <span class="text-gray-300">Shorten color values</span>
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" id="removeEmptyRules" class="mr-2 bg-gray-900 border-gray-600 text-green-600 focus:ring-green-500" checked>
                        <span class="text-gray-300">Remove empty rules</span>
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" id="optimizeValues" class="mr-2 bg-gray-900 border-gray-600 text-green-600 focus:ring-green-500" checked>
                        <span class="text-gray-300">Optimize property values</span>
                    </label>
                </div>
            </div>
        </div>

        <!-- 操作按钮和统计 -->
        <div class="mt-8 mb-16 text-center">
            <button id="minifyBtn" class="px-8 py-3 bg-green-600 text-white font-semibold hover:bg-green-700 transition-colors">
                Minify CSS
            </button>
            <div id="compressionStats" class="mt-4 text-gray-400 hidden">
                <p>Compression: <span id="compressionRatio" class="text-green-400 font-semibold"></span></p>
                <p>Size reduction: <span id="sizeReduction" class="text-green-400 font-semibold"></span></p>
            </div>
        </div>

        <!-- 消息提示区域 -->
        <div id="messageArea" class="mt-4 hidden">
            <div id="messageContent" class="max-w-md mx-auto p-4 border-l-4 text-sm">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <i id="messageIcon" class="fas"></i>
                    </div>
                    <div class="ml-3">
                        <p id="messageText"></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Use Cases Section -->
        <div class="mb-16">
            <h2 class="text-3xl font-bold text-white text-center mb-8">When to Use CSS Minifier</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div class="bg-gray-800 border border-gray-700 p-6">
                    <div class="bg-green-600 p-3 w-12 h-12 mb-4">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-white mb-3">Production Deployment</h3>
                    <p class="text-gray-300">Optimize CSS files for production to reduce file sizes and improve website loading speed for better user experience.</p>
                </div>

                <div class="bg-gray-800 border border-gray-700 p-6">
                    <div class="bg-blue-600 p-3 w-12 h-12 mb-4">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-white mb-3">Performance Optimization</h3>
                    <p class="text-gray-300">Reduce bandwidth usage and improve Core Web Vitals scores by minimizing CSS file sizes for better SEO rankings.</p>
                </div>

                <div class="bg-gray-800 border border-gray-700 p-6">
                    <div class="bg-purple-600 p-3 w-12 h-12 mb-4">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 7.172V5L8 4z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-white mb-3">Build Process Integration</h3>
                    <p class="text-gray-300">Integrate CSS minification into your build pipeline for automated optimization during development and deployment workflows.</p>
                </div>

                <div class="bg-gray-800 border border-gray-700 p-6">
                    <div class="bg-red-600 p-3 w-12 h-12 mb-4">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-white mb-3">Bandwidth Cost Reduction</h3>
                    <p class="text-gray-300">Lower hosting costs and improve mobile user experience by reducing data transfer requirements through CSS compression.</p>
                </div>

                <div class="bg-gray-800 border border-gray-700 p-6">
                    <div class="bg-orange-600 p-3 w-12 h-12 mb-4">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-white mb-3">CDN Optimization</h3>
                    <p class="text-gray-300">Optimize CSS files for Content Delivery Networks to ensure faster global distribution and reduced server load.</p>
                </div>

                <div class="bg-gray-800 border border-gray-700 p-6">
                    <div class="bg-indigo-600 p-3 w-12 h-12 mb-4">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-white mb-3">Framework Optimization</h3>
                    <p class="text-gray-300">Optimize CSS frameworks like Bootstrap, Tailwind, or custom stylesheets to remove unused code and improve performance.</p>
                </div>
            </div>
        </div>

        <!-- FAQ Section -->
        <div class="px-8 pt-8 pb-16">
            <h2 class="text-3xl font-bold text-white text-center mb-8">Frequently Asked Questions</h2>
            <div class="max-w-4xl mx-auto space-y-6">
                <div class="border-b border-gray-700 pb-6">
                    <h3 class="text-xl font-semibold text-white mb-3">What is CSS minification?</h3>
                    <p class="text-gray-300">CSS minification is the process of removing unnecessary characters, whitespace, comments, and optimizing code structure to reduce file size while maintaining functionality. This improves website loading speed and performance.</p>
                </div>

                <div class="border-b border-gray-700 pb-6">
                    <h3 class="text-xl font-semibold text-white mb-3">Is this CSS minifier free to use?</h3>
                    <p class="text-gray-300">Yes, our CSS minifier is completely free to use with no limitations. You can minify unlimited CSS code without any registration or payment required.</p>
                </div>

                <div class="border-b border-gray-700 pb-6">
                    <h3 class="text-xl font-semibold text-white mb-3">How much can CSS minification reduce file size?</h3>
                    <p class="text-gray-300">CSS minification typically reduces file size by 20-40%, depending on the original code structure. Files with extensive comments, whitespace, and verbose formatting see the greatest reduction.</p>
                </div>

                <div class="border-b border-gray-700 pb-6">
                    <h3 class="text-xl font-semibold text-white mb-3">Does minification affect CSS functionality?</h3>
                    <p class="text-gray-300">No, CSS minification only removes unnecessary characters and optimizes structure. The functionality and visual appearance of your styles remain exactly the same.</p>
                </div>

                <div class="border-b border-gray-700 pb-6">
                    <h3 class="text-xl font-semibold text-white mb-3">Is my CSS code stored on your servers?</h3>
                    <p class="text-gray-300">No, all minification is done locally in your browser. Your CSS code is never sent to our servers or stored anywhere. Your code remains completely private and secure.</p>
                </div>

                <div class="border-b border-gray-700 pb-6">
                    <h3 class="text-xl font-semibold text-white mb-3">What optimization features are included?</h3>
                    <p class="text-gray-300">Our minifier removes comments, unnecessary whitespace, optimizes color values, removes empty rules, and can optimize property values for maximum compression.</p>
                </div>

                <div class="border-b border-gray-700 pb-6">
                    <h3 class="text-xl font-semibold text-white mb-3">Can I use this for CSS frameworks?</h3>
                    <p class="text-gray-300">Yes, our minifier works perfectly with CSS frameworks like Bootstrap, Tailwind CSS, Foundation, and any other CSS framework or custom stylesheets.</p>
                </div>

                <div>
                    <h3 class="text-xl font-semibold text-white mb-3">How do I download the minified CSS?</h3>
                    <p class="text-gray-300">After minifying your CSS code, click the "Download" button to save the minified code as a .css file to your computer. You can also copy the code to your clipboard using the "Copy" button.</p>
                </div>
            </div>
        </div>
    </div>

<script>
// CSS Minifier functionality
document.addEventListener('DOMContentLoaded', function() {
    const cssInput = document.getElementById('cssInput');
    const cssOutput = document.getElementById('cssOutput');
    const minifyBtn = document.getElementById('minifyBtn');
    const clearInput = document.getElementById('clearInput');
    const pasteInput = document.getElementById('pasteInput');
    const copyOutput = document.getElementById('copyOutput');
    const downloadOutput = document.getElementById('downloadOutput');
    const copyTooltip = document.getElementById('copyTooltip');
    const downloadTooltip = document.getElementById('downloadTooltip');
    const inputStats = document.getElementById('inputStats');
    const outputStats = document.getElementById('outputStats');
    const inputSize = document.getElementById('inputSize');
    const outputSize = document.getElementById('outputSize');
    const compressionStats = document.getElementById('compressionStats');
    const compressionRatio = document.getElementById('compressionRatio');
    const sizeReduction = document.getElementById('sizeReduction');
    const messageArea = document.getElementById('messageArea');
    const messageContent = document.getElementById('messageContent');
    const messageIcon = document.getElementById('messageIcon');
    const messageText = document.getElementById('messageText');

    // 显示消息函数
    function showMessage(text, type = 'info') {
        const types = {
            success: { class: 'border-green-500 bg-green-900/20', icon: 'fa-check-circle text-green-400' },
            error: { class: 'border-red-500 bg-red-900/20', icon: 'fa-exclamation-circle text-red-400' },
            warning: { class: 'border-yellow-500 bg-yellow-900/20', icon: 'fa-exclamation-triangle text-yellow-400' },
            info: { class: 'border-blue-500 bg-blue-900/20', icon: 'fa-info-circle text-blue-400' }
        };

        const config = types[type] || types.info;
        messageContent.className = `max-w-md mx-auto p-4 border-l-4 text-sm ${config.class}`;
        messageIcon.className = `fas ${config.icon}`;
        messageText.textContent = text;
        messageArea.classList.remove('hidden');

        // 自动隐藏消息
        setTimeout(() => {
            messageArea.classList.add('hidden');
        }, 5000);
    }

    // Update statistics
    function updateStats() {
        const inputText = cssInput.value;
        const outputText = cssOutput.value;
        
        const inputBytes = new Blob([inputText]).size;
        const outputBytes = new Blob([outputText]).size;
        
        inputStats.textContent = `${inputText.length} characters, ${inputText.split('\n').length} lines`;
        outputStats.textContent = `${outputText.length} characters, ${outputText.split('\n').length} lines`;
        
        inputSize.textContent = `${(inputBytes / 1024).toFixed(2)} KB`;
        outputSize.textContent = `${(outputBytes / 1024).toFixed(2)} KB`;
        
        if (outputText && inputText) {
            const ratio = ((1 - outputBytes / inputBytes) * 100).toFixed(1);
            const reduction = ((inputBytes - outputBytes) / 1024).toFixed(2);
            
            compressionRatio.textContent = `${ratio}%`;
            sizeReduction.textContent = `${reduction} KB saved`;
            compressionStats.classList.remove('hidden');
        } else {
            compressionStats.classList.add('hidden');
        }
    }

    // Minify CSS function
    function minifyCSS(css) {
        const removeComments = document.getElementById('removeComments').checked;
        const removeWhitespace = document.getElementById('removeWhitespace').checked;
        const removeSemicolons = document.getElementById('removeSemicolons').checked;
        const shortenColors = document.getElementById('shortenColors').checked;
        const removeEmptyRules = document.getElementById('removeEmptyRules').checked;
        const optimizeValues = document.getElementById('optimizeValues').checked;

        let minified = css;

        // Remove comments
        if (removeComments) {
            minified = minified.replace(/\/\*[\s\S]*?\*\//g, '');
        }

        // Remove unnecessary whitespace
        if (removeWhitespace) {
            minified = minified.replace(/\s+/g, ' ');
            minified = minified.replace(/\s*{\s*/g, '{');
            minified = minified.replace(/;\s*/g, ';');
            minified = minified.replace(/\s*}\s*/g, '}');
            minified = minified.replace(/\s*,\s*/g, ',');
            minified = minified.replace(/\s*:\s*/g, ':');
        }

        // Remove unnecessary semicolons
        if (removeSemicolons) {
            minified = minified.replace(/;}/g, '}');
        }

        // Shorten color values
        if (shortenColors) {
            minified = minified.replace(/#([0-9a-f])\1([0-9a-f])\2([0-9a-f])\3/gi, '#$1$2$3');
        }

        // Remove empty rules
        if (removeEmptyRules) {
            minified = minified.replace(/[^{}]+{\s*}/g, '');
        }

        // Optimize values
        if (optimizeValues) {
            minified = minified.replace(/0px/g, '0');
            minified = minified.replace(/0em/g, '0');
            minified = minified.replace(/0%/g, '0');
            minified = minified.replace(/0 0 0 0/g, '0');
            minified = minified.replace(/0 0 0/g, '0');
            minified = minified.replace(/0 0/g, '0');
        }

        return minified.trim();
    }

    // Event listeners
    minifyBtn.addEventListener('click', function() {
        const input = cssInput.value.trim();
        if (!input) {
            showMessage('Please enter some CSS code to minify.', 'warning');
            cssInput.focus();
            return;
        }

        try {
            const minified = minifyCSS(input);
            cssOutput.value = minified;
            updateStats();
            showMessage('CSS minified successfully!', 'success');
        } catch (error) {
            showMessage('Error minifying CSS: ' + error.message, 'error');
        }
    });

    clearInput.addEventListener('click', function() {
        cssInput.value = '';
        cssOutput.value = '';
        updateStats();
        cssInput.focus();
    });

    pasteInput.addEventListener('click', async function() {
        try {
            const text = await navigator.clipboard.readText();
            cssInput.value = text;
            updateStats();
            showMessage('Content pasted successfully!', 'success');
        } catch (error) {
            showMessage('Unable to paste from clipboard. Please paste manually.', 'warning');
        }
    });

    copyOutput.addEventListener('click', async function() {
        if (!cssOutput.value) {
            showMessage('No minified CSS to copy.', 'warning');
            return;
        }

        try {
            await navigator.clipboard.writeText(cssOutput.value);

            // 显示临时提示
            if (copyTooltip) {
                copyTooltip.textContent = 'Copied!';
                copyTooltip.style.opacity = '1';
                copyTooltip.style.visibility = 'visible';

                setTimeout(() => {
                    copyTooltip.textContent = 'Copy to clipboard';
                    copyTooltip.style.opacity = '0';
                    copyTooltip.style.visibility = 'hidden';
                }, 2000);
            }

            showMessage('Minified CSS copied to clipboard!', 'success');
        } catch (error) {
            showMessage('Unable to copy to clipboard.', 'error');
        }
    });

    downloadOutput.addEventListener('click', function() {
        if (!cssOutput.value) {
            showMessage('No minified CSS to download.', 'warning');
            return;
        }

        const blob = new Blob([cssOutput.value], { type: 'text/css' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'minified.css';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        // 显示临时提示
        if (downloadTooltip) {
            downloadTooltip.textContent = 'Downloaded!';
            downloadTooltip.style.opacity = '1';
            downloadTooltip.style.visibility = 'visible';

            setTimeout(() => {
                downloadTooltip.textContent = 'Download as file';
                downloadTooltip.style.opacity = '0';
                downloadTooltip.style.visibility = 'hidden';
            }, 2000);
        }

        showMessage('CSS file downloaded successfully!', 'success');
    });

    // Auto-update stats on input change
    cssInput.addEventListener('input', updateStats);
    cssOutput.addEventListener('input', updateStats);

    // Initialize stats
    updateStats();
});
</script>

</div>

<?php include_once ROOT_PATH . '/templates/layout/footer.php'; ?>
