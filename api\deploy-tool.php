<?php
/**
 * Deploy Tool API Endpoint
 * Saves generated tool to database and file system
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit();
}

// 使用统一的数据库连接
require_once dirname(__DIR__) . '/includes/database-connection.php';

// 启动会话以获取用户信息
session_start();

try {
    // Get and validate input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        throw new Exception('Invalid JSON input');
    }
    
    // Validate required fields
    $requiredFields = ['name', 'slug', 'code'];
    foreach ($requiredFields as $field) {
        if (empty($input[$field])) {
            throw new Exception("Missing required field: {$field}");
        }
    }
    
    // Sanitize inputs
    $name = trim($input['name']);
    $slug = trim($input['slug']);
    $description = trim($input['description'] ?? '');
    $categoryId = $input['category_id'] ?? null;
    $icon = trim($input['icon'] ?? '🔧');
    $tags = trim($input['tags'] ?? '');
    $status = trim($input['status'] ?? 'coming_soon');
    $code = $input['code'];

    // 获取当前用户ID
    $currentUserId = $_SESSION['user_id'] ?? null;

    // Validate slug format
    if (!preg_match('/^[a-z0-9-]+$/', $slug)) {
        throw new Exception('Slug must contain only lowercase letters, numbers, and hyphens');
    }
    
    // Define file path
    $toolsDir = __DIR__ . '/../templates/pages/tools';

    // Create tools directory if it doesn't exist
    if (!is_dir($toolsDir)) {
        if (!mkdir($toolsDir, 0755, true)) {
            throw new Exception('Failed to create tools directory');
        }
    }

    // Smart slug and filename generation to avoid duplicates
    $originalSlug = $slug;
    $counter = 1;

    // Check for slug duplicates in database and generate unique slug
    while (true) {
        $stmt = $pdo->prepare("SELECT id FROM pt_tool WHERE slug = ?");
        $stmt->execute([$slug]);

        $filename = $slug . '.php';
        $filepath = $toolsDir . '/' . $filename;

        // If both database and file are available, we found a unique slug
        if (!$stmt->fetch() && !file_exists($filepath)) {
            break;
        }

        // Generate new slug with counter
        $slug = $originalSlug . '-' . $counter;
        $counter++;

        // Safety limit to prevent infinite loop
        if ($counter > 100) {
            throw new Exception('Unable to generate unique slug after 100 attempts');
        }
    }
    
    // Start database transaction
    $pdo->beginTransaction();
    
    try {
        // Insert tool into database
        $stmt = $pdo->prepare("
            INSERT INTO pt_tool (name, slug, description, category_id, icon, tags, sort_order, status, is_featured, file_type, created_by, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, 1, ?, 0, 'php', ?, NOW(), NOW())
        ");

        $stmt->execute([
            $name,
            $slug,
            $description,
            $categoryId,
            $icon,
            $tags,
            $status,
            $currentUserId
        ]);
        
        $toolId = $pdo->lastInsertId();
        
        // Save code to file
        if (file_put_contents($filepath, $code) === false) {
            throw new Exception('Failed to save tool file');
        }
        
        // Set proper file permissions
        chmod($filepath, 0644);
        
        // Commit transaction
        $pdo->commit();
        
        // Prepare response message
        $message = 'Tool deployed successfully';
        if ($slug !== $originalSlug) {
            $message .= " (renamed from '{$originalSlug}' to '{$slug}' to avoid conflicts)";
        }

        echo json_encode([
            'success' => true,
            'tool_id' => $toolId,
            'slug' => $slug,
            'original_slug' => $originalSlug,
            'filename' => $filename,
            'filepath' => $filepath,
            'renamed' => ($slug !== $originalSlug),
            'message' => $message
        ]);
        
    } catch (Exception $e) {
        // Rollback transaction
        $pdo->rollback();
        
        // Remove file if it was created
        if (file_exists($filepath)) {
            unlink($filepath);
        }
        
        throw $e;
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
