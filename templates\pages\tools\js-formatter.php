<?php
/**
 * JavaScript Formatter Tool Page
 * 遵循01-UI设计规范.md - 零圆角设计
 * 优化SEO落地页
 */

$currentPage = 'tool-js-formatter';
$breadcrumbs = [
    ['name' => 'Home', 'url' => '/'],
    ['name' => 'Tools', 'url' => '/tools'],
    ['name' => 'Development', 'url' => '/tools/development'],
    ['name' => 'JavaScript Formatter']
];

// 包含动态SEO助手
require_once ROOT_PATH . '/app/helpers/dynamic-seo.php';

// 生成SEO数据 - 优化长度
$seoData = [
    'title' => 'Free JavaScript Formatter Online - Prompt2Tool',
    'description' => 'Format and beautify JavaScript code instantly. Clean, indent, and organize your JS code for better readability and debugging.',
    'keywords' => 'javascript formatter, js beautifier, javascript code formatter, js minifier, javascript prettifier, code beautifier, online js formatter, free javascript formatter',
    'og_title' => 'Free JavaScript Formatter Online - Prompt2Tool',
    'og_description' => 'Format and beautify JavaScript code instantly. Clean, indent, and organize your JS code for better readability.',
    'canonical' => getCurrentURL()
];

// 包含公共头部
include_once ROOT_PATH . '/templates/layout/header.php';
?>

<!-- 工具页面主体 -->
<div class="bg-gray-900 text-white">

    <!-- 工具标题区域 -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-8">
        <div class="text-center mb-8">
            <div class="flex items-center justify-center mb-4">
                <div class="bg-yellow-600 p-3 mr-4">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                </div>
                <div class="text-left">
                    <h1 class="text-3xl font-bold text-white">JavaScript Formatter</h1>
                    <p class="text-gray-400 mt-1">Format and beautify your JavaScript code</p>
                </div>
            </div>
        </div>

        <!-- 工具主体 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- 输入区域 -->
            <div class="bg-gray-800 border border-gray-700 p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-white">Input JavaScript</h3>
                    <div class="flex space-x-2">
                        <button id="clearInput" class="px-3 py-1 bg-gray-700 text-gray-300 text-sm hover:bg-gray-600 transition-colors">
                            Clear
                        </button>
                        <button id="pasteInput" class="px-3 py-1 bg-blue-600 text-white text-sm hover:bg-blue-700 transition-colors">
                            Paste
                        </button>
                    </div>
                </div>
                <textarea 
                    id="jsInput" 
                    class="w-full h-96 bg-gray-900 border border-gray-600 text-gray-100 p-4 font-mono text-sm focus:outline-none focus:border-yellow-500 resize-none"
                    placeholder="Paste your JavaScript code here..."
                    spellcheck="false"
                ></textarea>
                <div class="mt-2 text-sm text-gray-400">
                    <span id="inputStats">0 characters, 0 lines</span>
                </div>
            </div>

            <!-- 输出区域 -->
            <div class="bg-gray-800 border border-gray-700 p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-white">Formatted JavaScript</h3>
                    <div class="flex space-x-2">
                        <div class="relative">
                            <button id="copyOutput" class="px-3 py-1 bg-green-600 text-white text-sm hover:bg-green-700 transition-colors">
                                Copy
                            </button>
                            <div id="copyTooltip" class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-700 text-white text-xs rounded pointer-events-none whitespace-nowrap z-50" style="opacity: 0; visibility: hidden; transition: opacity 0.2s, visibility 0.2s;">
                                Copy to clipboard
                            </div>
                        </div>
                        <div class="relative">
                            <button id="downloadOutput" class="px-3 py-1 bg-purple-600 text-white text-sm hover:bg-purple-700 transition-colors">
                                Download
                            </button>
                            <div id="downloadTooltip" class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-700 text-white text-xs rounded pointer-events-none whitespace-nowrap z-50" style="opacity: 0; visibility: hidden; transition: opacity 0.2s, visibility 0.2s;">
                                Download as file
                            </div>
                        </div>
                    </div>
                </div>
                <textarea 
                    id="jsOutput" 
                    class="w-full h-96 bg-gray-900 border border-gray-600 text-gray-100 p-4 font-mono text-sm focus:outline-none resize-none"
                    readonly
                    placeholder="Formatted JavaScript will appear here..."
                ></textarea>
                <div class="mt-2 text-sm text-gray-400">
                    <span id="outputStats">0 characters, 0 lines</span>
                </div>
            </div>
        </div>

        <!-- 格式化选项 -->
        <div class="mt-8 bg-gray-800 border border-gray-700 p-6">
            <h3 class="text-lg font-semibold text-white mb-4">Formatting Options</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Indentation</label>
                    <select id="indentType" class="w-full bg-gray-900 border border-gray-600 text-gray-100 p-2 focus:outline-none focus:border-yellow-500">
                        <option value="spaces">Spaces</option>
                        <option value="tabs">Tabs</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Indent Size</label>
                    <select id="indentSize" class="w-full bg-gray-900 border border-gray-600 text-gray-100 p-2 focus:outline-none focus:border-yellow-500">
                        <option value="2" selected>2</option>
                        <option value="4">4</option>
                        <option value="8">8</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Brace Style</label>
                    <select id="braceStyle" class="w-full bg-gray-900 border border-gray-600 text-gray-100 p-2 focus:outline-none focus:border-yellow-500">
                        <option value="collapse" selected>Collapse</option>
                        <option value="expand">Expand</option>
                        <option value="end-expand">End Expand</option>
                    </select>
                </div>
            </div>
            <div class="mt-4 flex flex-wrap gap-4">
                <label class="flex items-center">
                    <input type="checkbox" id="preserveNewlines" class="mr-2 bg-gray-900 border-gray-600 text-yellow-600 focus:ring-yellow-500">
                    <span class="text-gray-300">Preserve newlines</span>
                </label>
                <label class="flex items-center">
                    <input type="checkbox" id="spaceInParen" class="mr-2 bg-gray-900 border-gray-600 text-yellow-600 focus:ring-yellow-500">
                    <span class="text-gray-300">Space in parentheses</span>
                </label>
                <label class="flex items-center">
                    <input type="checkbox" id="jslintHappy" class="mr-2 bg-gray-900 border-gray-600 text-yellow-600 focus:ring-yellow-500">
                    <span class="text-gray-300">JSLint happy</span>
                </label>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="mt-8 text-center">
            <button id="formatBtn" class="px-8 py-3 bg-yellow-600 text-white font-semibold hover:bg-yellow-700 transition-colors">
                Format JavaScript
            </button>
        </div>

        <!-- 工具信息 -->
        <div class="mt-12 grid grid-cols-1 md:grid-cols-3 gap-8">
            <div class="bg-gray-800 border border-gray-700 p-6">
                <h4 class="text-lg font-semibold text-white mb-3">Features</h4>
                <ul class="text-gray-300 space-y-2">
                    <li>• Proper indentation</li>
                    <li>• Brace style options</li>
                    <li>• Newline preservation</li>
                    <li>• JSLint compatibility</li>
                </ul>
            </div>
            <div class="bg-gray-800 border border-gray-700 p-6">
                <h4 class="text-lg font-semibold text-white mb-3">Benefits</h4>
                <ul class="text-gray-300 space-y-2">
                    <li>• Improved readability</li>
                    <li>• Better code organization</li>
                    <li>• Easier debugging</li>
                    <li>• Team collaboration</li>
                </ul>
            </div>
            <div class="bg-gray-800 border border-gray-700 p-6">
                <h4 class="text-lg font-semibold text-white mb-3">Use Cases</h4>
                <ul class="text-gray-300 space-y-2">
                    <li>• Clean minified JS</li>
                    <li>• Standardize formatting</li>
                    <li>• Code review preparation</li>
                    <li>• Learning JS structure</li>
                </ul>
            </div>
        </div>

        <!-- FAQ Section -->
        <div class="px-8 pt-16 pb-16">
            <h2 class="text-3xl font-bold text-white text-center mb-8">Frequently Asked Questions</h2>
            <div class="max-w-4xl mx-auto space-y-6">
                <div class="border-b border-gray-700 pb-6">
                    <h3 class="text-xl font-semibold text-white mb-3">What is a JavaScript formatter?</h3>
                    <p class="text-gray-300">A JavaScript formatter is a tool that automatically organizes and beautifies JavaScript code by adding proper indentation, spacing, and line breaks. It transforms minified or poorly formatted code into clean, readable format that follows coding standards.</p>
                </div>

                <div class="border-b border-gray-700 pb-6">
                    <h3 class="text-xl font-semibold text-white mb-3">Is this JavaScript formatter free to use?</h3>
                    <p class="text-gray-300">Yes, our JavaScript formatter is completely free to use with no limitations. You can format unlimited JavaScript code without any registration or payment required.</p>
                </div>

                <div class="border-b border-gray-700 pb-6">
                    <h3 class="text-xl font-semibold text-white mb-3">Can I format minified JavaScript files?</h3>
                    <p class="text-gray-300">Absolutely! Our formatter is perfect for unminifying compressed JavaScript files. It will restore proper formatting, making the code readable and easier to debug or understand.</p>
                </div>

                <div class="border-b border-gray-700 pb-6">
                    <h3 class="text-xl font-semibold text-white mb-3">Does the formatter work with ES6+ JavaScript?</h3>
                    <p class="text-gray-300">Yes, our formatter supports modern JavaScript syntax including ES6, ES7, and newer features like arrow functions, async/await, destructuring, and template literals.</p>
                </div>

                <div class="border-b border-gray-700 pb-6">
                    <h3 class="text-xl font-semibold text-white mb-3">Is my JavaScript code stored on your servers?</h3>
                    <p class="text-gray-300">No, all formatting is done locally in your browser. Your JavaScript code is never sent to our servers or stored anywhere. Your code remains completely private and secure.</p>
                </div>

                <div class="border-b border-gray-700 pb-6">
                    <h3 class="text-xl font-semibold text-white mb-3">What formatting options are available?</h3>
                    <p class="text-gray-300">You can customize indentation type (spaces or tabs), indent size (2, 4, or 8), brace style, newline preservation, and JSLint compatibility to match your coding standards.</p>
                </div>

                <div class="border-b border-gray-700 pb-6">
                    <h3 class="text-xl font-semibold text-white mb-3">Can I use this for React or Node.js code?</h3>
                    <p class="text-gray-300">Yes, our formatter works with all JavaScript environments including React JSX, Node.js, Vue.js, Angular, and any other JavaScript framework or library code.</p>
                </div>

                <div>
                    <h3 class="text-xl font-semibold text-white mb-3">How do I download the formatted code?</h3>
                    <p class="text-gray-300">After formatting your JavaScript code, click the "Download" button to save the formatted code as a .js file to your computer. You can also copy the code to your clipboard using the "Copy" button.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// JavaScript Formatter functionality
document.addEventListener('DOMContentLoaded', function() {
    const jsInput = document.getElementById('jsInput');
    const jsOutput = document.getElementById('jsOutput');
    const formatBtn = document.getElementById('formatBtn');
    const clearInput = document.getElementById('clearInput');
    const pasteInput = document.getElementById('pasteInput');
    const copyOutput = document.getElementById('copyOutput');
    const downloadOutput = document.getElementById('downloadOutput');
    const copyTooltip = document.getElementById('copyTooltip');
    const downloadTooltip = document.getElementById('downloadTooltip');
    const inputStats = document.getElementById('inputStats');
    const outputStats = document.getElementById('outputStats');

    // Update statistics
    function updateStats() {
        const inputText = jsInput.value;
        const outputText = jsOutput.value;
        
        inputStats.textContent = `${inputText.length} characters, ${inputText.split('\n').length} lines`;
        outputStats.textContent = `${outputText.length} characters, ${outputText.split('\n').length} lines`;
    }

    // Format JavaScript function (basic implementation)
    function formatJS(js) {
        const indentType = document.getElementById('indentType').value;
        const indentSize = parseInt(document.getElementById('indentSize').value);
        const braceStyle = document.getElementById('braceStyle').value;
        const preserveNewlines = document.getElementById('preserveNewlines').checked;
        const spaceInParen = document.getElementById('spaceInParen').checked;
        const jslintHappy = document.getElementById('jslintHappy').checked;

        const indent = indentType === 'tabs' ? '\t' : ' '.repeat(indentSize);
        let formatted = js;
        let level = 0;
        let result = '';
        let inString = false;
        let stringChar = '';
        
        // Basic JavaScript formatter (simplified implementation)
        for (let i = 0; i < formatted.length; i++) {
            const char = formatted[i];
            const nextChar = formatted[i + 1];
            const prevChar = formatted[i - 1];
            
            // Handle strings
            if ((char === '"' || char === "'") && prevChar !== '\\') {
                if (!inString) {
                    inString = true;
                    stringChar = char;
                } else if (char === stringChar) {
                    inString = false;
                    stringChar = '';
                }
                result += char;
                continue;
            }
            
            if (inString) {
                result += char;
                continue;
            }
            
            // Handle braces and formatting
            if (char === '{') {
                if (braceStyle === 'expand') {
                    result += '\n' + indent.repeat(level) + '{\n';
                    level++;
                    result += indent.repeat(level);
                } else {
                    result += ' {\n';
                    level++;
                    result += indent.repeat(level);
                }
            } else if (char === '}') {
                level = Math.max(0, level - 1);
                result = result.trimEnd() + '\n' + indent.repeat(level) + '}';
                if (nextChar && nextChar !== ';' && nextChar !== ',' && nextChar !== ')') {
                    result += '\n' + indent.repeat(level);
                }
            } else if (char === ';') {
                result += ';\n' + indent.repeat(level);
            } else if (char === ',' && nextChar !== ' ') {
                result += ', ';
            } else if (char === '\n' || char === '\r') {
                if (preserveNewlines) {
                    result += '\n' + indent.repeat(level);
                } else {
                    // Skip extra newlines
                    if (result.slice(-1) !== '\n') {
                        result += ' ';
                    }
                }
            } else if (char === ' ' && (prevChar === ' ' || nextChar === ' ')) {
                // Skip multiple spaces
                continue;
            } else {
                result += char;
            }
        }
        
        // Clean up extra spaces and newlines
        result = result.replace(/\n\s*\n/g, '\n');
        result = result.replace(/^\s+|\s+$/g, '');
        
        return result;
    }

    // Event listeners
    formatBtn.addEventListener('click', function() {
        const input = jsInput.value.trim();
        if (!input) {
            alert('Please enter some JavaScript code to format.');
            return;
        }

        try {
            const formatted = formatJS(input);
            jsOutput.value = formatted;
            updateStats();
        } catch (error) {
            alert('Error formatting JavaScript: ' + error.message);
        }
    });

    clearInput.addEventListener('click', function() {
        jsInput.value = '';
        jsOutput.value = '';
        updateStats();
        jsInput.focus();
    });

    pasteInput.addEventListener('click', async function() {
        try {
            const text = await navigator.clipboard.readText();
            jsInput.value = text;
            updateStats();
        } catch (error) {
            alert('Unable to paste from clipboard. Please paste manually.');
        }
    });

    copyOutput.addEventListener('click', async function() {
        if (!jsOutput.value) {
            return;
        }

        try {
            await navigator.clipboard.writeText(jsOutput.value);

            // 显示临时提示
            if (copyTooltip) {
                copyTooltip.textContent = 'Copied!';
                copyTooltip.style.opacity = '1';
                copyTooltip.style.visibility = 'visible';

                setTimeout(() => {
                    copyTooltip.textContent = 'Copy to clipboard';
                    copyTooltip.style.opacity = '0';
                    copyTooltip.style.visibility = 'hidden';
                }, 2000);
            }
        } catch (error) {
            alert('Unable to copy to clipboard.');
        }
    });

    downloadOutput.addEventListener('click', function() {
        if (!jsOutput.value) {
            return;
        }

        const blob = new Blob([jsOutput.value], { type: 'application/javascript' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'formatted.js';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        // 显示临时提示
        if (downloadTooltip) {
            downloadTooltip.textContent = 'Downloaded!';
            downloadTooltip.style.opacity = '1';
            downloadTooltip.style.visibility = 'visible';

            setTimeout(() => {
                downloadTooltip.textContent = 'Download as file';
                downloadTooltip.style.opacity = '0';
                downloadTooltip.style.visibility = 'hidden';
            }, 2000);
        }
    });

    // Auto-update stats on input change
    jsInput.addEventListener('input', updateStats);
    jsOutput.addEventListener('input', updateStats);

    // Initialize stats
    updateStats();
});
</script>

<?php include_once ROOT_PATH . '/templates/layout/footer.php'; ?>
