<?php
/**
 * 管理后台登出处理
 * 安全地清除会话和Cookie
 */

// 定义根目录
define('ROOT_PATH', dirname(dirname(__DIR__)));

// 启动会话（如果尚未启动）
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// 记录登出日志
if (isset($_SESSION['admin_username'])) {
    $username = $_SESSION['admin_username'];
    $clientIP = $_SERVER['HTTP_CF_CONNECTING_IP'] ?? $_SERVER['HTTP_CLIENT_IP'] ?? $_SERVER['HTTP_X_FORWARDED_FOR'] ?? $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    error_log("Admin logout: {$username} from {$clientIP}");
}

// 清除所有会话数据
$_SESSION = array();

// 删除会话Cookie
if (ini_get("session.use_cookies")) {
    $params = session_get_cookie_params();
    setcookie(session_name(), '', time() - 42000,
        $params["path"], $params["domain"],
        $params["secure"], $params["httponly"]
    );
}

// 删除记住我Cookie和数据库Token
$adminId = $_SESSION['admin_id'] ?? null;

// 强制删除Remember Me Cookie（无论是否存在）
setcookie('admin_remember_token', '', time() - 3600, '/');
setcookie('admin_remember_token', '', time() - 3600, '/', '', false, true);
setcookie('admin_remember_token', '', time() - 3600, '/', '', true, true);

// 删除数据库中的remember token
if ($adminId) {
    try {
        // 使用统一的数据库连接
        require_once dirname(dirname(__DIR__)) . '/includes/database-connection.php';

        // 删除数据库中的remember token
        $deleteSql = "DELETE FROM pt_manager_token WHERE manager_id = ?";
        $deleteStmt = $pdo->prepare($deleteSql);
        $deleteStmt->execute([$adminId]);

        error_log("Remember token deleted for admin ID: {$adminId}");

    } catch (PDOException $e) {
        error_log("Remember token cleanup error: " . $e->getMessage());
        // 即使数据库操作失败，也要继续清理Cookie和会话
    }
}

// 销毁会话
session_destroy();

// 重定向到登录页面
header('Location: login.php?logged_out=1');
exit;
?>
