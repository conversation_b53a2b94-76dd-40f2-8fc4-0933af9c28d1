<?php
/**
 * 配额管理类
 * 处理用户API配额的扣除、增加和检查
 */

class QuotaManager {
    
    private $pdo;
    
    // 配额消耗规则
    const QUOTA_RULES = [
        'submit_launch' => 100,    // 提交产品启动
        'submit_request' => -20,   // 提交需求（奖励20配额）
        'chat_interaction' => 20,  // 对话交互
        'tool_generation' => 100   // 工具生成
    ];

    // 操作类型描述
    const ACTION_DESCRIPTIONS = [
        'submit_launch' => 'Product Launch Submission',
        'submit_request' => 'Request Submission Reward',
        'chat_interaction' => 'AI Chat Interaction',
        'tool_generation' => 'Tool Code Generation'
    ];
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
    }
    
    /**
     * 检查用户是否有足够配额
     */
    public function hasEnoughQuota($userId, $action) {
        $requiredQuota = self::QUOTA_RULES[$action] ?? 0;

        // 如果是奖励操作，直接返回true
        if ($requiredQuota <= 0) {
            return true;
        }

        $user = $this->getUserQuota($userId);
        return $user['api_used'] + $requiredQuota <= $user['api_quota'];
    }
    
    /**
     * 获取用户配额信息
     */
    public function getUserQuota($userId) {
        // 获取用户基本配额信息
        $stmt = $this->pdo->prepare("
            SELECT api_quota, subscription_type
            FROM pt_member
            WHERE id = ?
        ");
        $stmt->execute([$userId]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);

        // 从 pt_api_usage 表统计实际使用量
        $stmt = $this->pdo->prepare("
            SELECT COALESCE(SUM(quota_consumed), 0) as total_used
            FROM pt_api_usage
            WHERE user_id = ?
        ");
        $stmt->execute([$userId]);
        $apiUsed = (int)$stmt->fetchColumn();

        // 合并数据
        $user['api_used'] = $apiUsed;

        return $user;
    }
    
    /**
     * 消耗配额
     */
    public function consumeQuota($userId, $action, $description = null) {
        $quotaChange = self::QUOTA_RULES[$action] ?? 0;
        $actionDesc = $description ?? self::ACTION_DESCRIPTIONS[$action] ?? $action;
        
        try {
            $this->pdo->beginTransaction();
            
            // 检查配额是否足够（仅对扣除操作）
            if ($quotaChange > 0 && !$this->hasEnoughQuota($userId, $action)) {
                throw new Exception('Insufficient quota');
            }

            // 记录配额使用日志（这会自动更新使用量统计）
            $this->logQuotaUsage($userId, $action, $quotaChange);
            
            $this->pdo->commit();
            
            return [
                'success' => true,
                'quota_change' => $quotaChange,
                'action' => $actionDesc
            ];
            
        } catch (Exception $e) {
            $this->pdo->rollBack();
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 记录配额使用日志到API使用表
     */
    private function logQuotaUsage($userId, $action, $quotaChange) {
        // 使用更新后的 pt_api_usage 表记录配额使用
        $stmt = $this->pdo->prepare("
            INSERT INTO pt_api_usage (user_id, action, quota_consumed, ip_address, user_agent, created_at)
            VALUES (?, ?, ?, ?, ?, NOW())
        ");

        $ipAddress = $_SERVER['REMOTE_ADDR'] ?? null;
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? null;

        $stmt->execute([$userId, $action, abs($quotaChange), $ipAddress, $userAgent]);
    }
    

    
    /**
     * 获取用户配额使用历史
     */
    public function getQuotaHistory($userId, $limit = 20) {
        $stmt = $this->pdo->prepare("
            SELECT action, quota_consumed, created_at
            FROM pt_api_usage
            WHERE user_id = ?
            ORDER BY created_at DESC
            LIMIT ?
        ");
        $stmt->execute([$userId, $limit]);
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // 转换为统一格式
        $history = [];
        foreach ($results as $row) {
            $history[] = [
                'action' => $row['action'],
                'quota_change' => $row['quota_consumed'],
                'description' => self::ACTION_DESCRIPTIONS[$row['action']] ?? $row['action'],
                'created_at' => $row['created_at']
            ];
        }

        return $history;
    }
    
    /**
     * 获取配额不足的错误信息
     */
    public function getInsufficientQuotaMessage($action) {
        $required = self::QUOTA_RULES[$action] ?? 0;
        $actionName = self::ACTION_DESCRIPTIONS[$action] ?? $action;

        return "Insufficient quota! {$actionName} requires {$required} quota. Please upgrade your subscription plan.";
    }
    
    /**
     * 获取操作所需配额
     */
    public function getRequiredQuota($action) {
        return self::QUOTA_RULES[$action] ?? 0;
    }
    
    /**
     * 检查并显示配额警告
     */
    public function getQuotaWarning($userId) {
        $user = $this->getUserQuota($userId);
        $usagePercent = $user['api_quota'] > 0 ? ($user['api_used'] / $user['api_quota']) * 100 : 0;
        
        if ($usagePercent >= 90) {
            return [
                'level' => 'danger',
                'message' => 'Quota almost exhausted! Used ' . round($usagePercent, 1) . '%'
            ];
        } elseif ($usagePercent >= 75) {
            return [
                'level' => 'warning',
                'message' => 'High quota usage, used ' . round($usagePercent, 1) . '%'
            ];
        }
        
        return null;
    }
}
?>
