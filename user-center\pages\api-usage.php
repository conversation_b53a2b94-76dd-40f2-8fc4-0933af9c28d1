<?php
/**
 * API使用管理页面
 */

// 获取用户的API密钥
try {
    $stmt = $pdo->prepare("
        SELECT id, name, token, last_used, expires_at, created_at 
        FROM pt_member_tokens 
        WHERE user_id = ? AND type = 'api_key'
        ORDER BY created_at DESC
    ");
    $stmt->execute([$user['id']]);
    $apiKeys = $stmt->fetchAll();
} catch (Exception $e) {
    $apiKeys = [];
}

// 从 pt_api_usage 表统计实际使用的配额
$stmt = $pdo->prepare("SELECT COALESCE(SUM(quota_consumed), 0) as total_used FROM pt_api_usage WHERE user_id = ?");
$stmt->execute([$user['id']]);
$apiUsed = (int)$stmt->fetchColumn();

// 计算API使用率
$apiUsagePercent = $user['api_quota'] > 0 ? round(($apiUsed / $user['api_quota']) * 100, 1) : 0;

?>

<!-- 页面标题 -->
<div class="mb-8">
    <h1 class="text-3xl font-bold text-gray-900">Usage</h1>
    <p class="text-gray-600 mt-2">Monitor your API usage and quota</p>
</div>

<!-- 成功/错误消息 -->
<div id="message-container" class="mb-6 hidden">
    <div id="message" class="p-4 border"></div>
</div>

<!-- 主要内容区域 -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
    <!-- 左侧：API管理 -->
    <div class="lg:col-span-2 space-y-8">
        <!-- API管理卡片 -->
        <div class="replicate-card">
            <div class="p-6 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900">API Keys</h3>
                        <p class="text-sm text-gray-500 mt-1">Manage your API access keys</p>
                        <?php
                        // 计算API密钥限制
                        $maxKeys = 3; // 默认限制
                        switch ($user['subscription_type']) {
                            case 'free':
                                $maxKeys = 1;
                                break;
                            case 'basic':
                                $maxKeys = 3;
                                break;
                            case 'premium':
                                $maxKeys = 10;
                                break;
                            case 'enterprise':
                                $maxKeys = 50;
                                break;
                        }
                        $currentCount = count($apiKeys);
                        ?>
                        <p class="text-xs text-gray-400 mt-1">
                            <?= $currentCount ?> / <?= $maxKeys ?> keys used
                            (<?= ucfirst($user['subscription_type']) ?> plan)
                        </p>
                    </div>
                    <?php if ($currentCount >= $maxKeys): ?>
                        <button type="button" class="replicate-btn replicate-btn-primary text-sm" disabled>
                            Limit Reached
                        </button>

                    <?php else: ?>
                        <button type="button" onclick="showCreateApiKeyModal()"
                                class="replicate-btn replicate-btn-primary text-sm">
                            Create New Key
                        </button>
                    <?php endif; ?>
                </div>
            </div>
            <div class="p-6">
                <?php if (empty($apiKeys)): ?>
                    <div class="text-center py-8" id="empty-api-keys">
                        <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1721 9z"></path>
                        </svg>
                        <h4 class="text-lg font-medium text-gray-900 mb-2">No API Keys</h4>
                        <p class="text-gray-500">Create your first API key to start using our services</p>
                    </div>
                <?php else: ?>
                    <div class="space-y-4" id="api-keys-container">
                        <?php foreach ($apiKeys as $key): ?>
                        <div class="border border-gray-200 p-4" data-key-id="<?= $key['id'] ?>">
                            <div class="flex justify-between items-start">
                                <div class="flex-1">
                                    <h4 class="text-sm font-medium text-gray-900">
                                        <?= htmlspecialchars($key['name'] ?: 'Unnamed Key') ?>
                                    </h4>
                                    <p class="text-xs text-gray-500 mt-1">
                                        Created: <?= date('M j, Y', strtotime($key['created_at'])) ?>
                                    </p>
                                    <?php if ($key['last_used']): ?>
                                        <p class="text-xs text-gray-500">
                                            Last used: <?= timeAgo($key['last_used']) ?>
                                        </p>
                                    <?php endif; ?>
                                    <div class="mt-2">
                                        <code class="text-xs bg-gray-100 px-2 py-1 font-mono">
                                            <?= substr($key['token'], 0, 12) ?>...<?= substr($key['token'], -12) ?>
                                        </code>
                                    </div>
                                </div>
                                <div class="flex space-x-2">
                                    <button type="button" onclick="copyApiKey('<?= htmlspecialchars($key['token']) ?>')"
                                            class="text-gray-400 hover:text-gray-600">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                        </svg>
                                    </button>
                                    <button type="button" onclick="deleteApiKey(<?= $key['id'] ?>)"
                                            class="text-red-400 hover:text-red-600">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- 右侧：使用统计和升级 -->
    <div class="space-y-6">
        <!-- API配额卡片 -->
        <div class="replicate-card">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">API Usage</h3>
            </div>
            <div class="p-6">
                <!-- 使用量显示 -->
                <div class="mb-4">
                    <div class="flex justify-between items-center mb-2">
                        <span class="text-sm text-gray-600">Current Usage</span>
                        <span class="text-sm font-medium text-gray-900">
                            <?= number_format($apiUsed) ?> / <?= number_format($user['api_quota']) ?>
                        </span>
                    </div>
                    <div class="w-full bg-gray-200 h-2">
                        <?php
                        // 根据配额使用百分比确定进度条颜色和状态（与dashboard.php保持一致）
                        if ($apiUsagePercent >= 100) {
                            $progressColor = 'bg-red-800'; // 深红色 - 配额已耗尽
                            $statusText = 'Quota exhausted';
                            $statusIcon = '🚫';
                        } elseif ($apiUsagePercent >= 80) {
                            $progressColor = 'bg-red-600'; // 红色 - 警告即将用完
                            $statusText = 'Almost depleted';
                            $statusIcon = '⚠️';
                        } elseif ($apiUsagePercent >= 60) {
                            $progressColor = 'bg-orange-500'; // 橙色 - 建议节约使用
                            $statusText = 'High usage';
                            $statusIcon = '🔶';
                        } elseif ($apiUsagePercent >= 40) {
                            $progressColor = 'bg-yellow-500'; // 黄色 - 提醒注意用量
                            $statusText = 'Moderate usage';
                            $statusIcon = '🟡';
                        } elseif ($apiUsagePercent >= 10) {
                            $progressColor = 'bg-green-500'; // 绿色 - 正常使用
                            $statusText = 'Normal usage';
                            $statusIcon = '🟢';
                        } else {
                            $progressColor = 'bg-green-600'; // 深绿色 - 刚开始使用
                            $statusText = 'Just started';
                            $statusIcon = '✅';
                        }
                        ?>
                        <div class="<?= $progressColor ?> h-2" style="width: <?= min($apiUsagePercent, 100) ?>%"></div>
                    </div>
                    <div class="flex items-center justify-between mt-1">
                        <p class="text-xs text-gray-500"><?= $apiUsagePercent ?>% used</p>
                        <span class="text-xs text-gray-600"><?= $statusText ?></span>
                    </div>
                </div>

                <!-- 下次重置时间 -->
                <?php
                // 计算下次重置时间
                if ($user['api_reset_date']) {
                    // 如果有重置记录，下次重置时间是上次重置日期+1个月
                    $nextResetDate = date('M j, Y', strtotime($user['api_reset_date'] . ' +1 month'));
                } else {
                    // 如果从未重置过，下次重置时间是注册日期+1个月
                    $nextResetDate = date('M j, Y', strtotime($user['created_at'] . ' +1 month'));
                }
                ?>
                <div class="text-center">
                    <p class="text-xs text-gray-500">
                        Next quota reset: <?= $nextResetDate ?>
                    </p>
                </div>
            </div>
        </div>

        <!-- 升级计划卡片 -->
        <?php if ($user['subscription_type'] === 'free'): ?>
        <div class="replicate-card">
            <div class="p-6 border-b border-gray-200">
                <h3 class="text-lg font-semibold text-gray-900">Upgrade Plan</h3>
            </div>
            <div class="p-6">
                <div class="text-center">
                    <div class="mb-4">
                        <svg class="w-12 h-12 text-blue-600 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                        <h4 class="text-lg font-medium text-gray-900">Get More API Access</h4>
                        <p class="text-sm text-gray-500 mt-1">Upgrade to increase your API quota and unlock more features</p>
                    </div>
                    <a href="/pricing" class="w-full replicate-btn replicate-btn-primary text-center block">
                        View Plans
                    </a>
                </div>
            </div>
        </div>
        <?php endif; ?>


    </div>
</div>

<!-- 创建API密钥模态框 -->
<div id="create-api-key-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white max-w-md w-full p-6">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Create API Key</h3>
                <button type="button" onclick="hideCreateApiKeyModal()" class="text-gray-400 hover:text-gray-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <form id="create-api-key-form" class="space-y-4">
                <div>
                    <label for="api-key-name" class="block text-sm font-medium text-gray-700 mb-2">Key Name *</label>
                    <input type="text" id="api-key-name" name="name"
                           class="w-full px-3 py-2 border border-gray-300 bg-white text-sm focus:outline-none focus:border-black"
                           placeholder="e.g., My App API Key" required maxlength="100">
                    <p class="text-xs text-gray-500 mt-1">Choose a descriptive name to identify this key</p>
                </div>

                <div>
                    <label for="api-key-expires" class="block text-sm font-medium text-gray-700 mb-2">Expires In</label>
                    <select id="api-key-expires" name="expires_days"
                            class="w-full px-3 py-2 border border-gray-300 bg-white text-sm focus:outline-none focus:border-black">
                        <option value="0">Never</option>
                        <option value="30">30 days</option>
                        <option value="90">90 days</option>
                        <option value="365">1 year</option>
                    </select>
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Permissions</label>
                    <div class="space-y-2">
                        <label class="flex items-center">
                            <input type="checkbox" name="permissions[]" value="read" checked
                                   class="mr-2 text-black focus:ring-black">
                            <span class="text-sm text-gray-700">Read access</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" name="permissions[]" value="write"
                                   class="mr-2 text-black focus:ring-black">
                            <span class="text-sm text-gray-700">Write access</span>
                        </label>
                    </div>
                </div>

                <div class="flex justify-end space-x-3 pt-4">
                    <button type="button" onclick="hideCreateApiKeyModal()"
                            class="replicate-btn replicate-btn-secondary">
                        Cancel
                    </button>
                    <button type="submit" class="replicate-btn replicate-btn-primary">
                        Create Key
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div id="delete-confirm-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white max-w-md w-full p-6">
            <div class="flex items-center mb-4">
                <div class="w-10 h-10 bg-red-100 text-red-600 flex items-center justify-center mr-3">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-900">Delete API Key</h3>
                    <p class="text-sm text-gray-500">This action cannot be undone</p>
                </div>
            </div>

            <p class="text-gray-700 mb-6">Are you sure you want to delete this API key? Any applications using this key will lose access immediately.</p>

            <div class="flex justify-end space-x-3">
                <button type="button" onclick="hideDeleteConfirmModal()"
                        class="replicate-btn replicate-btn-secondary">
                    Cancel
                </button>
                <button type="button" onclick="confirmDeleteApiKey()"
                        class="replicate-btn replicate-btn-primary bg-red-600 hover:bg-red-700 border-red-600">
                    Delete Key
                </button>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript功能 -->
<script>
// 显示消息
function showMessage(message, type = 'success') {
    const container = document.getElementById('message-container');
    const messageDiv = document.getElementById('message');

    container.classList.remove('hidden');
    messageDiv.className = `p-4 border ${type === 'success' ? 'bg-green-50 border-green-200 text-green-800' : 'bg-red-50 border-red-200 text-red-800'}`;
    messageDiv.textContent = message;

    // 3秒后自动隐藏
    setTimeout(() => {
        container.classList.add('hidden');
    }, 3000);
}

// 显示创建API密钥模态框
function showCreateApiKeyModal() {
    document.getElementById('create-api-key-modal').classList.remove('hidden');
    document.body.style.overflow = 'hidden';
}

// 隐藏创建API密钥模态框
function hideCreateApiKeyModal() {
    document.getElementById('create-api-key-modal').classList.add('hidden');
    document.body.style.overflow = 'auto';
    document.getElementById('create-api-key-form').reset();
}

// 处理创建API密钥表单
document.getElementById('create-api-key-form').addEventListener('submit', async function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    formData.append('action', 'create_api_key');

    const button = this.querySelector('button[type="submit"]');
    const originalText = button.textContent;

    button.textContent = 'Creating...';
    button.disabled = true;

    try {
        const response = await fetch('ajax/profile-actions.php', {
            method: 'POST',
            body: formData
        });

        const result = await response.json();

        if (result.success) {
            // 显示成功状态 - 延长显示时间
            button.classList.remove('replicate-btn-primary');
            button.classList.add('bg-green-600', 'border-green-600', 'text-white');
            button.textContent = '✓ Key Created!';
            button.disabled = true;

            // 2秒后关闭模态框并显示结果
            setTimeout(() => {
                hideCreateApiKeyModal();
                showMessage(result.message, 'success');

                // 添加新的API密钥到列表
                addApiKeyToList(result.key_data);

                // 更新API密钥计数和按钮状态
                updateApiKeyCount();

                // 显示新创建的token
                showNewApiKeyToken(result.token);
            }, 2000);
        } else {
            // 显示错误状态
            button.classList.remove('replicate-btn-primary');
            button.classList.add('bg-red-600', 'border-red-600', 'text-white');
            button.textContent = '✗ Creation Failed!';
            button.disabled = true;

            showMessage(result.message, 'error');

            // 3秒后恢复原状
            setTimeout(() => {
                button.classList.remove('bg-red-600', 'border-red-600', 'text-white');
                button.classList.add('replicate-btn-primary');
                button.textContent = originalText;
                button.disabled = false;
            }, 3000);
        }
    } catch (error) {
        showMessage('An error occurred while creating API key', 'error');
    } finally {
        button.textContent = originalText;
        button.disabled = false;
    }
});

// 添加API密钥到列表
function addApiKeyToList(keyData) {
    // 检查是否有空状态
    const emptyState = document.querySelector('#empty-api-keys');
    if (emptyState) {
        // 替换空状态为容器
        emptyState.outerHTML = '<div class="space-y-4" id="api-keys-container"></div>';
    }

    const container = document.querySelector('#api-keys-container');
    if (!container) {
        // 如果没有容器，刷新页面
        location.reload();
        return;
    }

    const keyElement = document.createElement('div');
    keyElement.className = 'border border-gray-200 p-4';
    keyElement.setAttribute('data-key-id', keyData.id);
    keyElement.innerHTML = `
        <div class="flex justify-between items-start">
            <div class="flex-1">
                <h4 class="text-sm font-medium text-gray-900">${keyData.name}</h4>
                <p class="text-xs text-gray-500 mt-1">Created: ${new Date(keyData.created_at).toLocaleDateString()}</p>
                <div class="mt-2">
                    <code class="text-xs bg-gray-100 px-2 py-1 font-mono">
                        ${keyData.token.substring(0, 12)}...${keyData.token.substring(keyData.token.length - 12)}
                    </code>
                </div>
            </div>
            <div class="flex space-x-2">
                <button type="button" onclick="copyApiKey('${keyData.token}')"
                        class="text-gray-400 hover:text-gray-600">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                    </svg>
                </button>
                <button type="button" onclick="deleteApiKey(${keyData.id})"
                        class="text-red-400 hover:text-red-600">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                </button>
            </div>
        </div>
    `;

    container.appendChild(keyElement);
}

// 复制API密钥
async function copyApiKey(token) {
    try {
        await navigator.clipboard.writeText(token);
        showMessage('API key copied to clipboard', 'success');
    } catch (error) {
        // 降级方案
        const textArea = document.createElement('textarea');
        textArea.value = token;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        showMessage('API key copied to clipboard', 'success');
    }
}

// 删除API密钥变量
let deleteKeyId = null;

// 显示删除确认模态框
function deleteApiKey(keyId) {
    deleteKeyId = keyId;
    document.getElementById('delete-confirm-modal').classList.remove('hidden');
    document.body.style.overflow = 'hidden';
}

// 隐藏删除确认模态框
function hideDeleteConfirmModal() {
    document.getElementById('delete-confirm-modal').classList.add('hidden');
    document.body.style.overflow = 'auto';
    deleteKeyId = null;
}

// 确认删除API密钥
async function confirmDeleteApiKey() {
    if (!deleteKeyId) return;

    const button = document.querySelector('#delete-confirm-modal button[onclick="confirmDeleteApiKey()"]');
    const originalText = button.textContent;

    button.textContent = 'Deleting...';
    button.disabled = true;

    try {
        const formData = new FormData();
        formData.append('action', 'delete_api_key');
        formData.append('key_id', deleteKeyId);

        const response = await fetch('ajax/profile-actions.php', {
            method: 'POST',
            body: formData
        });

        const result = await response.json();

        if (result.success) {
            // 移除API密钥元素
            const keyElement = document.querySelector(`[data-key-id="${deleteKeyId}"]`);
            if (keyElement) {
                keyElement.remove();
            }

            // 检查是否需要显示空状态
            const container = document.querySelector('#api-keys-container');
            if (container && container.children.length === 0) {
                container.outerHTML = `
                    <div class="text-center py-8" id="empty-api-keys">
                        <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1721 9z"></path>
                        </svg>
                        <h4 class="text-lg font-medium text-gray-900 mb-2">No API Keys</h4>
                        <p class="text-gray-500">Create your first API key to start using our services</p>
                    </div>
                `;
            }

            hideDeleteConfirmModal();
            showMessage(result.message, 'success');

            // 更新API密钥计数
            updateApiKeyCount();
        } else {
            showMessage(result.message, 'error');
        }
    } catch (error) {
        showMessage('An error occurred while deleting API key', 'error');
    } finally {
        button.textContent = originalText;
        button.disabled = false;
    }
}

// 更新API密钥计数
function updateApiKeyCount() {
    // 这个函数可以用来更新页面上的计数显示
    // 如果需要的话，可以重新加载页面或者动态更新计数
    setTimeout(() => {
        location.reload();
    }, 1000);
}

// 显示新创建的API密钥token
function showNewApiKeyToken(token) {
    // 可以在这里添加显示完整token的逻辑
    // 比如显示一个特殊的模态框来展示完整的token
    console.log('New API key created:', token);
}
</script>
