<?php
/**
 * HTML Formatter Tool Page
 * 遵循01-UI设计规范.md - 零圆角设计
 */

$currentPage = 'tool-html-formatter';
$breadcrumbs = [
    ['name' => 'Home', 'url' => '/'],
    ['name' => 'Tools', 'url' => '/tools'],
    ['name' => 'Development', 'url' => '/tools/development'],
    ['name' => 'HTML Formatter']
];

// 包含动态SEO助手
require_once ROOT_PATH . '/app/helpers/dynamic-seo.php';

// 生成SEO数据 - 优化长度
$seoData = [
    'title' => 'Free HTML Formatter Online - Prompt2Tool',
    'description' => 'Format and beautify HTML code instantly. Clean, indent, and organize your markup for better readability and debugging.',
    'keywords' => 'html formatter, html beautifier, html code formatter, html prettifier, markup formatter, online html formatter, free html formatter, html indenter',
    'og_title' => 'Free HTML Formatter Online - Prompt2Tool',
    'og_description' => 'Format and beautify HTML code instantly. Clean, indent, and organize your HTML markup for better readability.',
    'canonical' => getCurrentURL()
];

// 包含公共头部
include_once ROOT_PATH . '/templates/layout/header.php';
?>

<!-- 工具页面主体 -->
<div class="bg-gray-900 text-white">

    <!-- Hero Section with SEO optimized content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-12">
        <div class="text-center mb-12">
            <div class="flex items-center justify-center mb-6">
                <div class="bg-blue-600 p-4 mr-4">
                    <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
                    </svg>
                </div>
                <div class="text-left">
                    <h1 class="text-4xl font-bold text-white mb-2">HTML Formatter</h1>
                    <p class="text-xl text-gray-400">Format and beautify your HTML code</p>
                </div>
            </div>
        </div>

        <!-- 工具主体 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- 输入区域 -->
            <div class="bg-gray-800 border border-gray-700 p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-white">Input HTML</h3>
                    <div class="flex space-x-2">
                        <button id="clearInput" class="px-3 py-1 bg-gray-700 text-gray-300 text-sm hover:bg-gray-600 transition-colors">
                            Clear
                        </button>
                        <button id="pasteInput" class="px-3 py-1 bg-blue-600 text-white text-sm hover:bg-blue-700 transition-colors">
                            Paste
                        </button>
                    </div>
                </div>
                <textarea 
                    id="htmlInput" 
                    class="w-full h-96 bg-gray-900 border border-gray-600 text-gray-100 p-4 font-mono text-sm focus:outline-none focus:border-blue-500 resize-none"
                    placeholder="Paste your HTML code here..."
                    spellcheck="false"
                ></textarea>
                <div class="mt-2 text-sm text-gray-400">
                    <span id="inputStats">0 characters, 0 lines</span>
                </div>
            </div>

            <!-- 输出区域 -->
            <div class="bg-gray-800 border border-gray-700 p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-white">Formatted HTML</h3>
                    <div class="flex space-x-2">
                        <div class="relative">
                            <button id="copyOutput" class="px-3 py-1 bg-green-600 text-white text-sm hover:bg-green-700 transition-colors">
                                Copy
                            </button>
                            <div id="copyTooltip" class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-700 text-white text-xs rounded pointer-events-none whitespace-nowrap z-50" style="opacity: 0; visibility: hidden; transition: opacity 0.2s, visibility 0.2s;">
                                Copy to clipboard
                            </div>
                        </div>
                        <div class="relative">
                            <button id="downloadOutput" class="px-3 py-1 bg-purple-600 text-white text-sm hover:bg-purple-700 transition-colors">
                                Download
                            </button>
                            <div id="downloadTooltip" class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-700 text-white text-xs rounded pointer-events-none whitespace-nowrap z-50" style="opacity: 0; visibility: hidden; transition: opacity 0.2s, visibility 0.2s;">
                                Download as file
                            </div>
                        </div>
                    </div>
                </div>
                <textarea 
                    id="htmlOutput" 
                    class="w-full h-96 bg-gray-900 border border-gray-600 text-gray-100 p-4 font-mono text-sm focus:outline-none resize-none"
                    readonly
                    placeholder="Formatted HTML will appear here..."
                ></textarea>
                <div class="mt-2 text-sm text-gray-400">
                    <span id="outputStats">0 characters, 0 lines</span>
                </div>
            </div>
        </div>

        <!-- 格式化选项 -->
        <div class="mt-8 bg-gray-800 border border-gray-700 p-6">
            <h3 class="text-lg font-semibold text-white mb-4">Formatting Options</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Indentation</label>
                    <select id="indentType" class="w-full bg-gray-900 border border-gray-600 text-gray-100 p-2 focus:outline-none focus:border-blue-500">
                        <option value="spaces">Spaces</option>
                        <option value="tabs">Tabs</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Indent Size</label>
                    <select id="indentSize" class="w-full bg-gray-900 border border-gray-600 text-gray-100 p-2 focus:outline-none focus:border-blue-500">
                        <option value="2" selected>2</option>
                        <option value="4">4</option>
                        <option value="8">8</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Max Line Length</label>
                    <select id="maxLineLength" class="w-full bg-gray-900 border border-gray-600 text-gray-100 p-2 focus:outline-none focus:border-blue-500">
                        <option value="80">80</option>
                        <option value="100">100</option>
                        <option value="120" selected>120</option>
                        <option value="0">No limit</option>
                    </select>
                </div>
            </div>
            <div class="mt-4 flex flex-wrap gap-4">
                <label class="flex items-center">
                    <input type="checkbox" id="preserveNewlines" class="mr-2 bg-gray-900 border-gray-600 text-blue-600 focus:ring-blue-500">
                    <span class="text-gray-300">Preserve newlines</span>
                </label>
                <label class="flex items-center">
                    <input type="checkbox" id="sortAttributes" class="mr-2 bg-gray-900 border-gray-600 text-blue-600 focus:ring-blue-500">
                    <span class="text-gray-300">Sort attributes</span>
                </label>
                <label class="flex items-center">
                    <input type="checkbox" id="removeComments" class="mr-2 bg-gray-900 border-gray-600 text-blue-600 focus:ring-blue-500">
                    <span class="text-gray-300">Remove comments</span>
                </label>
            </div>
        </div>

        <!-- 操作按钮 -->
        <div class="mt-8 mb-16 text-center">
            <button id="formatBtn" class="px-8 py-3 bg-blue-600 text-white font-semibold hover:bg-blue-700 transition-colors">
                Format HTML
            </button>
        </div>

        <!-- 消息提示区域 -->
        <div id="messageArea" class="mt-4 hidden">
            <div id="messageContent" class="max-w-md mx-auto p-4 border-l-4 text-sm">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <i id="messageIcon" class="fas"></i>
                    </div>
                    <div class="ml-3">
                        <p id="messageText"></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Use Cases Section -->
        <div class="mb-16">
            <h2 class="text-3xl font-bold text-white text-center mb-8">When to Use HTML Formatter</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div class="bg-gray-800 border border-gray-700 p-6">
                    <div class="bg-blue-600 p-3 w-12 h-12 mb-4">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-white mb-3">Debugging Minified HTML</h3>
                    <p class="text-gray-300">Unminify and format compressed HTML files to understand document structure and debug layout issues effectively.</p>
                </div>

                <div class="bg-gray-800 border border-gray-700 p-6">
                    <div class="bg-green-600 p-3 w-12 h-12 mb-4">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-white mb-3">Code Review Process</h3>
                    <p class="text-gray-300">Standardize HTML markup formatting across your team for consistent code reviews and better collaboration.</p>
                </div>

                <div class="bg-gray-800 border border-gray-700 p-6">
                    <div class="bg-purple-600 p-3 w-12 h-12 mb-4">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-white mb-3">Learning Web Development</h3>
                    <p class="text-gray-300">Study well-formatted HTML examples to understand proper markup structure, semantic elements, and accessibility patterns.</p>
                </div>

                <div class="bg-gray-800 border border-gray-700 p-6">
                    <div class="bg-red-600 p-3 w-12 h-12 mb-4">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 7.172V5L8 4z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-white mb-3">Legacy Code Maintenance</h3>
                    <p class="text-gray-300">Clean up and format old HTML templates to improve readability and maintainability for future development.</p>
                </div>

                <div class="bg-gray-800 border border-gray-700 p-6">
                    <div class="bg-orange-600 p-3 w-12 h-12 mb-4">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-white mb-3">Email Template Development</h3>
                    <p class="text-gray-300">Format HTML email templates for better organization and easier maintenance across different email clients.</p>
                </div>

                <div class="bg-gray-800 border border-gray-700 p-6">
                    <div class="bg-indigo-600 p-3 w-12 h-12 mb-4">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-white mb-3">SEO Optimization</h3>
                    <p class="text-gray-300">Organize HTML structure for better search engine optimization and improved semantic markup for accessibility.</p>
                </div>
            </div>
        </div>

        <!-- FAQ Section -->
        <div class="px-8 pt-8 pb-16">
            <h2 class="text-3xl font-bold text-white text-center mb-8">Frequently Asked Questions</h2>
            <div class="max-w-4xl mx-auto space-y-6">
                <div class="border-b border-gray-700 pb-6">
                    <h3 class="text-xl font-semibold text-white mb-3">What is an HTML formatter?</h3>
                    <p class="text-gray-300">An HTML formatter is a tool that automatically organizes and beautifies HTML markup by adding proper indentation, spacing, and line breaks. It transforms minified or poorly formatted HTML into clean, readable format that follows web standards.</p>
                </div>

                <div class="border-b border-gray-700 pb-6">
                    <h3 class="text-xl font-semibold text-white mb-3">Is this HTML formatter free to use?</h3>
                    <p class="text-gray-300">Yes, our HTML formatter is completely free to use with no limitations. You can format unlimited HTML code without any registration or payment required.</p>
                </div>

                <div class="border-b border-gray-700 pb-6">
                    <h3 class="text-xl font-semibold text-white mb-3">Can I format minified HTML files?</h3>
                    <p class="text-gray-300">Absolutely! Our formatter is perfect for unminifying compressed HTML files. It will restore proper formatting, making the markup readable and easier to debug or understand.</p>
                </div>

                <div class="border-b border-gray-700 pb-6">
                    <h3 class="text-xl font-semibold text-white mb-3">Does the formatter work with HTML5?</h3>
                    <p class="text-gray-300">Yes, our formatter supports HTML5 and all modern HTML elements including semantic tags, custom attributes, and the latest web standards.</p>
                </div>

                <div class="border-b border-gray-700 pb-6">
                    <h3 class="text-xl font-semibold text-white mb-3">Is my HTML code stored on your servers?</h3>
                    <p class="text-gray-300">No, all formatting is done locally in your browser. Your HTML code is never sent to our servers or stored anywhere. Your code remains completely private and secure.</p>
                </div>

                <div class="border-b border-gray-700 pb-6">
                    <h3 class="text-xl font-semibold text-white mb-3">What formatting options are available?</h3>
                    <p class="text-gray-300">You can customize indentation type (spaces or tabs), indent size, attribute sorting, comment removal, and newline preservation to match your coding standards.</p>
                </div>

                <div class="border-b border-gray-700 pb-6">
                    <h3 class="text-xl font-semibold text-white mb-3">Can I use this for email templates?</h3>
                    <p class="text-gray-300">Yes, our formatter works perfectly with HTML email templates, helping you organize complex table-based layouts and inline styles for better email client compatibility.</p>
                </div>

                <div class="mb-0">
                    <h3 class="text-xl font-semibold text-white mb-3">How do I download the formatted HTML?</h3>
                    <p class="text-gray-300 mb-0">After formatting your HTML code, click the "Download" button to save the formatted code as an .html file to your computer. You can also copy the code to your clipboard using the "Copy" button.</p>
                </div>
            </div>
        </div>
    </div>

<script>
// HTML Formatter functionality
document.addEventListener('DOMContentLoaded', function() {
    const htmlInput = document.getElementById('htmlInput');
    const htmlOutput = document.getElementById('htmlOutput');
    const formatBtn = document.getElementById('formatBtn');
    const clearInput = document.getElementById('clearInput');
    const pasteInput = document.getElementById('pasteInput');
    const copyOutput = document.getElementById('copyOutput');
    const downloadOutput = document.getElementById('downloadOutput');
    const copyTooltip = document.getElementById('copyTooltip');
    const downloadTooltip = document.getElementById('downloadTooltip');
    const inputStats = document.getElementById('inputStats');
    const outputStats = document.getElementById('outputStats');
    const messageArea = document.getElementById('messageArea');
    const messageContent = document.getElementById('messageContent');
    const messageIcon = document.getElementById('messageIcon');
    const messageText = document.getElementById('messageText');

    // 显示消息函数
    function showMessage(text, type = 'info') {
        const types = {
            success: { class: 'border-green-500 bg-green-900/20', icon: 'fa-check-circle text-green-400' },
            error: { class: 'border-red-500 bg-red-900/20', icon: 'fa-exclamation-circle text-red-400' },
            warning: { class: 'border-yellow-500 bg-yellow-900/20', icon: 'fa-exclamation-triangle text-yellow-400' },
            info: { class: 'border-blue-500 bg-blue-900/20', icon: 'fa-info-circle text-blue-400' }
        };

        const config = types[type] || types.info;
        messageContent.className = `max-w-md mx-auto p-4 border-l-4 text-sm ${config.class}`;
        messageIcon.className = `fas ${config.icon}`;
        messageText.textContent = text;
        messageArea.classList.remove('hidden');

        // 自动隐藏消息
        setTimeout(() => {
            messageArea.classList.add('hidden');
        }, 5000);
    }

    // Update statistics
    function updateStats() {
        const inputText = htmlInput.value;
        const outputText = htmlOutput.value;
        
        inputStats.textContent = `${inputText.length} characters, ${inputText.split('\n').length} lines`;
        outputStats.textContent = `${outputText.length} characters, ${outputText.split('\n').length} lines`;
    }

    // Format HTML function
    function formatHTML(html) {
        const indentType = document.getElementById('indentType').value;
        const indentSize = parseInt(document.getElementById('indentSize').value);
        const maxLineLength = parseInt(document.getElementById('maxLineLength').value);
        const preserveNewlines = document.getElementById('preserveNewlines').checked;
        const sortAttributes = document.getElementById('sortAttributes').checked;
        const removeComments = document.getElementById('removeComments').checked;

        let formatted = html;

        // Remove comments if requested
        if (removeComments) {
            formatted = formatted.replace(/<!--[\s\S]*?-->/g, '');
        }

        // Basic HTML formatting
        const indent = indentType === 'tabs' ? '\t' : ' '.repeat(indentSize);
        let level = 0;
        let result = '';
        
        // Simple HTML formatter (basic implementation)
        const tokens = formatted.match(/<\/?[^>]+>|[^<]+/g) || [];
        
        tokens.forEach(token => {
            if (token.trim()) {
                if (token.startsWith('</')) {
                    level = Math.max(0, level - 1);
                    result += indent.repeat(level) + token.trim() + '\n';
                } else if (token.startsWith('<') && !token.endsWith('/>')) {
                    result += indent.repeat(level) + token.trim() + '\n';
                    if (!token.match(/<(area|base|br|col|embed|hr|img|input|link|meta|param|source|track|wbr)/i)) {
                        level++;
                    }
                } else if (token.startsWith('<') && token.endsWith('/>')) {
                    result += indent.repeat(level) + token.trim() + '\n';
                } else {
                    const text = token.trim();
                    if (text) {
                        result += indent.repeat(level) + text + '\n';
                    }
                }
            }
        });

        return result.trim();
    }

    // Event listeners
    formatBtn.addEventListener('click', function() {
        const input = htmlInput.value.trim();
        if (!input) {
            showMessage('Please enter some HTML code to format.', 'warning');
            htmlInput.focus();
            return;
        }

        try {
            const formatted = formatHTML(input);
            htmlOutput.value = formatted;
            updateStats();
            showMessage('HTML formatted successfully!', 'success');
        } catch (error) {
            showMessage('Error formatting HTML: ' + error.message, 'error');
        }
    });

    clearInput.addEventListener('click', function() {
        htmlInput.value = '';
        htmlOutput.value = '';
        updateStats();
        htmlInput.focus();
    });

    pasteInput.addEventListener('click', async function() {
        try {
            const text = await navigator.clipboard.readText();
            htmlInput.value = text;
            updateStats();
            showMessage('Content pasted successfully!', 'success');
        } catch (error) {
            showMessage('Unable to paste from clipboard. Please paste manually.', 'warning');
        }
    });

    copyOutput.addEventListener('click', async function() {
        console.log('Copy button clicked'); // 调试信息
        console.log('copyTooltip element:', copyTooltip); // 调试tooltip元素

        if (!htmlOutput.value) {
            showMessage('No formatted HTML to copy.', 'warning');
            return;
        }

        try {
            await navigator.clipboard.writeText(htmlOutput.value);

            // 显示临时提示
            if (copyTooltip) {
                copyTooltip.textContent = 'Copied!';
                copyTooltip.style.opacity = '1';
                copyTooltip.style.visibility = 'visible';

                setTimeout(() => {
                    copyTooltip.textContent = 'Copy to clipboard';
                    copyTooltip.style.opacity = '0';
                    copyTooltip.style.visibility = 'hidden';
                }, 2000);
            }

            showMessage('Formatted HTML copied to clipboard!', 'success');
        } catch (error) {
            showMessage('Unable to copy to clipboard.', 'error');
        }
    });

    downloadOutput.addEventListener('click', function() {
        console.log('Download button clicked'); // 调试信息
        if (!htmlOutput.value) {
            showMessage('No formatted HTML to download.', 'warning');
            return;
        }

        const blob = new Blob([htmlOutput.value], { type: 'text/html' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'formatted.html';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        // 显示临时提示
        if (downloadTooltip) {
            downloadTooltip.textContent = 'Downloaded!';
            downloadTooltip.style.opacity = '1';
            downloadTooltip.style.visibility = 'visible';

            setTimeout(() => {
                downloadTooltip.textContent = 'Download as file';
                downloadTooltip.style.opacity = '0';
                downloadTooltip.style.visibility = 'hidden';
            }, 2000);
        }

        showMessage('HTML file downloaded successfully!', 'success');
    });



    // Auto-format on input change
    htmlInput.addEventListener('input', updateStats);
    htmlOutput.addEventListener('input', updateStats);

    // Initialize stats
    updateStats();
});
</script>

</div>

<?php include_once ROOT_PATH . '/templates/layout/footer.php'; ?>
