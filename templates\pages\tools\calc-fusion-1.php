<?php
/**
 * Calc-Fusion Tool Page
 * Zero border-radius design - no rounded corners
 */

$currentPage = 'tool-calc-fusion';
$breadcrumbs = [
    ['name' => 'Home', 'url' => '/'],
    ['name' => 'Tools', 'url' => '/tools'],
    ['name' => 'Calculators', 'url' => '/tools/calculators'],
    ['name' => 'Calc-Fusion']
];

// Include dynamic SEO helper
require_once ROOT_PATH . '/app/helpers/dynamic-seo.php';

// Generate SEO data
$seoData = [
    'title' => 'Calc-Fusion Online - Prompt2Tool',
    'description' => 'A simple calculator tool that performs basic arithmetic operations.',
    'keywords' => 'calculator, arithmetic, utilities',
    'og_title' => 'Calc-Fusion Online - Prompt2Tool',
    'og_description' => 'A simple calculator tool that performs basic arithmetic operations.',
    'canonical' => getCurrentURL()
];

// Include common header
include_once ROOT_PATH . '/templates/layout/header.php';
?>

<!-- Tool page main content -->
<div class="bg-gray-900 text-white">

    <!-- Hero Section with SEO optimized content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-12">
        <div class="text-center mb-12">
            <div class="flex items-center justify-center mb-4">
                <div class="bg-blue-600 p-3 mr-4">
                    <div class="w-10 h-10 text-white text-2xl flex items-center justify-center">🔧</div>
                </div>
                <div class="text-left">
                    <h1 class="text-4xl font-bold text-white mb-2">Calc-Fusion</h1>
                    <p class="text-xl text-gray-400">A simple calculator tool that performs basic arithmetic operations.</p>
                </div>
            </div>
        </div>

        <!-- Tool main content -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Input area -->
            <div class="bg-gray-800 border border-gray-700 p-6">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-xl font-semibold text-white">Calculator</h2>
                    <div class="flex space-x-2">
                        <button id="clearInput" class="px-3 py-1 bg-red-600 text-white text-sm hover:bg-red-700 transition-colors">
                            <i class="fas fa-times mr-1"></i>Clear
                        </button>
                        <button id="clearHistory" class="px-3 py-1 bg-orange-600 text-white text-sm hover:bg-orange-700 transition-colors">
                            <i class="fas fa-history mr-1"></i>Clear History
                        </button>
                    </div>
                </div>
                
                <div class="space-y-4">
                    <!-- Display Screen -->
                    <div class="bg-gray-900 border border-gray-600 p-4">
                        <div id="display" class="text-right text-white text-2xl font-mono min-h-[40px] flex items-center justify-end">0</div>
                        <div id="operation" class="text-right text-gray-400 text-sm mt-1 min-h-[20px]"></div>
                    </div>

                    <!-- Calculator Buttons -->
                    <div class="grid grid-cols-4 gap-2">
                        <!-- Row 1 -->
                        <button class="calc-btn operator bg-gray-700 hover:bg-gray-600 text-white p-3 font-semibold transition-colors" data-action="clear">C</button>
                        <button class="calc-btn operator bg-gray-700 hover:bg-gray-600 text-white p-3 font-semibold transition-colors" data-action="clear-entry">CE</button>
                        <button class="calc-btn operator bg-gray-700 hover:bg-gray-600 text-white p-3 font-semibold transition-colors" data-action="backspace">⌫</button>
                        <button class="calc-btn operator bg-blue-600 hover:bg-blue-700 text-white p-3 font-semibold transition-colors" data-operation="/">÷</button>

                        <!-- Row 2 -->
                        <button class="calc-btn number bg-gray-600 hover:bg-gray-500 text-white p-3 font-semibold transition-colors" data-number="7">7</button>
                        <button class="calc-btn number bg-gray-600 hover:bg-gray-500 text-white p-3 font-semibold transition-colors" data-number="8">8</button>
                        <button class="calc-btn number bg-gray-600 hover:bg-gray-500 text-white p-3 font-semibold transition-colors" data-number="9">9</button>
                        <button class="calc-btn operator bg-blue-600 hover:bg-blue-700 text-white p-3 font-semibold transition-colors" data-operation="*">×</button>

                        <!-- Row 3 -->
                        <button class="calc-btn number bg-gray-600 hover:bg-gray-500 text-white p-3 font-semibold transition-colors" data-number="4">4</button>
                        <button class="calc-btn number bg-gray-600 hover:bg-gray-500 text-white p-3 font-semibold transition-colors" data-number="5">5</button>
                        <button class="calc-btn number bg-gray-600 hover:bg-gray-500 text-white p-3 font-semibold transition-colors" data-number="6">6</button>
                        <button class="calc-btn operator bg-blue-600 hover:bg-blue-700 text-white p-3 font-semibold transition-colors" data-operation="-">−</button>

                        <!-- Row 4 -->
                        <button class="calc-btn number bg-gray-600 hover:bg-gray-500 text-white p-3 font-semibold transition-colors" data-number="1">1</button>
                        <button class="calc-btn number bg-gray-600 hover:bg-gray-500 text-white p-3 font-semibold transition-colors" data-number="2">2</button>
                        <button class="calc-btn number bg-gray-600 hover:bg-gray-500 text-white p-3 font-semibold transition-colors" data-number="3">3</button>
                        <button class="calc-btn operator bg-blue-600 hover:bg-blue-700 text-white p-3 font-semibold transition-colors" data-operation="+">+</button>

                        <!-- Row 5 -->
                        <button class="calc-btn number bg-gray-600 hover:bg-gray-500 text-white p-3 font-semibold col-span-2 transition-colors" data-number="0">0</button>
                        <button class="calc-btn number bg-gray-600 hover:bg-gray-500 text-white p-3 font-semibold transition-colors" data-action="decimal">.</button>
                        <button class="calc-btn operator bg-green-600 hover:bg-green-700 text-white p-3 font-semibold transition-colors" data-action="equals">=</button>
                    </div>

                    <!-- Error Display -->
                    <div id="error-message" class="hidden bg-red-600 border border-red-500 p-3 text-white text-sm">
                        <i class="fas fa-exclamation-triangle mr-2"></i>
                        <span id="error-text"></span>
                    </div>
                </div>
            </div>

            <!-- Output area -->
            <div class="bg-gray-800 border border-gray-700 p-6">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-xl font-semibold text-white">History & Results</h2>
                    <div class="flex space-x-2">
                        <button id="copyOutput" class="px-3 py-1 bg-green-600 text-white text-sm hover:bg-green-700 transition-colors">
                            <i class="fas fa-copy mr-1"></i>Copy
                        </button>
                        <button id="downloadOutput" class="px-3 py-1 bg-purple-600 text-white text-sm hover:bg-purple-700 transition-colors">
                            <i class="fas fa-download mr-1"></i>Download
                        </button>
                    </div>
                </div>
                
                <div class="space-y-4">
                    <!-- Current Result -->
                    <div class="bg-gray-900 border border-gray-600 p-4">
                        <h3 class="text-sm font-semibold text-gray-400 mb-2">Current Result</h3>
                        <div id="current-result" class="text-white text-xl font-mono">0</div>
                    </div>

                    <!-- Calculation History -->
                    <div class="bg-gray-900 border border-gray-600 p-4">
                        <h3 class="text-sm font-semibold text-gray-400 mb-2">Calculation History</h3>
                        <div id="history-list" class="space-y-2 max-h-64 overflow-y-auto">
                            <div class="text-gray-500 text-sm italic">No calculations yet</div>
                        </div>
                    </div>

                    <!-- Statistics -->
                    <div class="bg-gray-900 border border-gray-600 p-4">
                        <h3 class="text-sm font-semibold text-gray-400 mb-2">Statistics</h3>
                        <div class="grid grid-cols-2 gap-4 text-sm">
                            <div>
                                <span class="text-gray-400">Total Calculations:</span>
                                <span id="total-calculations" class="text-white ml-2">0</span>
                            </div>
                            <div>
                                <span class="text-gray-400">Last Operation:</span>
                                <span id="last-operation" class="text-white ml-2">None</span>
                            </div>
                        </div>
                    </div>

                    <!-- Success Message -->
                    <div id="success-message" class="hidden bg-green-600 border border-green-500 p-3 text-white text-sm">
                        <i class="fas fa-check-circle mr-2"></i>
                        <span id="success-text"></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- FAQ Section -->
        <div class="px-8 pt-16 pb-16">
            <h2 class="text-3xl font-bold text-white text-center mb-8">Frequently Asked Questions</h2>
            <div class="max-w-4xl mx-auto space-y-6">
                <div class="bg-gray-800 border border-gray-700 p-6">
                    <h3 class="text-xl font-semibold text-white mb-3">What operations does Calc-Fusion support?</h3>
                    <p class="text-gray-400">Calc-Fusion supports all basic arithmetic operations including addition (+), subtraction (−), multiplication (×), and division (÷). It also includes decimal number support and maintains a history of your calculations.</p>
                </div>

                <div class="bg-gray-800 border border-gray-700 p-6">
                    <h3 class="text-xl font-semibold text-white mb-3">How do I clear the calculator?</h3>
                    <p class="text-gray-400">You can use the "C" button to clear all entries and reset the calculator, or "CE" to clear only the current entry. The "⌫" button removes the last digit entered. You can also clear the entire calculation history using the "Clear History" button.</p>
                </div>

                <div class="bg-gray-800 border border-gray-700 p-6">
                    <h3 class="text-xl font-semibold text-white mb-3">Can I use keyboard shortcuts?</h3>
                    <p class="text-gray-400">Yes! You can use your keyboard for calculations. Number keys (0-9) input numbers, +, -, *, / for operations, Enter or = for equals, Escape for clear, and Backspace to delete the last digit. Press Ctrl+C to copy the current result.</p>
                </div>

                <div class="bg-gray-800 border border-gray-700 p-6">
                    <h3 class="text-xl font-semibold text-white mb-3">What happens if I divide by zero?</h3>
                    <p class="text-gray-400">Division by zero will display an error message. The calculator will show "Error: Division by zero" and you'll need to clear the calculator to continue with new calculations.</p>
                </div>

                <div class="bg-gray-800 border border-gray-700 p-6">
                    <h3 class="text-xl font-semibold text-white mb-3">Can I save my calculation history?</h3>
                    <p class="text-gray-400">Yes! You can download your calculation history as a text file using the "Download" button. You can also copy individual results or the entire history to your clipboard using the "Copy" button.</p>
                </div>
            </div>
        </div>
    </div>

<script>
// Tool functionality JavaScript
document.addEventListener('DOMContentLoaded', function() {
    // Initialize DOM elements
    const clearBtn = document.getElementById('clearInput');
    const clearHistoryBtn = document.getElementById('clearHistory');
    const copyBtn = document.getElementById('copyOutput');
    const downloadBtn = document.getElementById('downloadOutput');
    const display = document.getElementById('display');
    const operationDisplay = document.getElementById('operation');
    const currentResult = document.getElementById('current-result');
    const historyList = document.getElementById('history-list');
    const errorMessage = document.getElementById('error-message');
    const errorText = document.getElementById('error-text');
    const successMessage = document.getElementById('success-message');
    const successText = document.getElementById('success-text');
    const totalCalculations = document.getElementById('total-calculations');
    const lastOperation = document.getElementById('last-operation');

    // Calculator state
    let currentValue = '0';
    let previousValue = null;
    let operation = null;
    let waitingForOperand = false;
    let history = [];
    let calculationCount = 0;

    // Update display
    function updateDisplay() {
        display.textContent = currentValue;
        currentResult.textContent = currentValue;
        
        if (operation && previousValue !== null) {
            operationDisplay.textContent = `${previousValue} ${getOperationSymbol(operation)}`;
        } else {
            operationDisplay.textContent = '';
        }
    }

    // Get operation symbol for display
    function getOperationSymbol(op) {
        const symbols = {
            '+': '+',
            '-': '−',
            '*': '×',
            '/': '÷'
        };
        return symbols[op] || op;
    }

    // Show error message
    function showError(message) {
        errorText.textContent = message;
        errorMessage.classList.remove('hidden');
        setTimeout(() => {
            errorMessage.classList.add('hidden');
        }, 3000);
    }

    // Show success message
    function showSuccess(message) {
        successText.textContent = message;
        successMessage.classList.remove('hidden');
        setTimeout(() => {
            successMessage.classList.add('hidden');
        }, 2000);
    }

    // Add to history
    function addToHistory(calculation, result) {
        history.unshift({ calculation, result, timestamp: new Date() });
        calculationCount++;
        updateHistoryDisplay();
        updateStatistics();
    }

    // Update history display
    function updateHistoryDisplay() {
        if (history.length === 0) {
            historyList.innerHTML = '<div class="text-gray-500 text-sm italic">No calculations yet</div>';
            return;
        }

        historyList.innerHTML = history.slice(0, 10).map(item => `
            <div class="flex justify-between items-center py-2 border-b border-gray-700 last:border-b-0">
                <span class="text-gray-300 font-mono text-sm">${item.calculation}</span>
                <span class="text-white font-mono">${item.result}</span>
            </div>
        `).join('');
    }

    // Update statistics
    function updateStatistics() {
        totalCalculations.textContent = calculationCount;
        if (history.length > 0) {
            const lastCalc = history[0].calculation.split(' = ')[0];
            lastOperation.textContent = lastCalc;
        }
    }

    // Input number
    function inputNumber(num) {
        if (waitingForOperand) {
            currentValue = num;
            waitingForOperand = false;
        } else {
            currentValue = currentValue === '0' ? num : currentValue + num;
        }
        updateDisplay();
    }

    // Input decimal
    function inputDecimal() {
        if (waitingForOperand) {
            currentValue = '0.';
            waitingForOperand = false;
        } else if (currentValue.indexOf('.') === -1) {
            currentValue += '.';
        }
        updateDisplay();
    }

    // Clear all
    function clear() {
        currentValue = '0';
        previousValue = null;
        operation = null;
        waitingForOperand = false;
        updateDisplay();
    }

    // Clear entry
    function clearEntry() {
        currentValue = '0';
        updateDisplay();
    }

    // Backspace
    function backspace() {
        if (currentValue.length > 1) {
            currentValue = currentValue.slice(0, -1);
        } else {
            currentValue = '0';
        }
        updateDisplay();
    }

    // Perform operation
    function performOperation(nextOperation) {
        const inputValue = parseFloat(currentValue);

        if (previousValue === null) {
            previousValue = inputValue;
        } else if (operation) {
            const currentResult = calculate(previousValue, inputValue, operation);
            
            if (currentResult === null) {
                return;
            }

            const calculation = `${previousValue} ${getOperationSymbol(operation)} ${inputValue} = ${currentResult}`;
            addToHistory(calculation, currentResult);

            currentValue = String(currentResult);
            previousValue = currentResult;
        }

        waitingForOperand = true;
        operation = nextOperation;
        updateDisplay();
    }

    // Calculate result
    function calculate(firstOperand, secondOperand, operation) {
        try {
            let result;
            switch (operation) {
                case '+':
                    result = firstOperand + secondOperand;
                    break;
                case '-':
                    result = firstOperand - secondOperand;
                    break;
                case '*':
                    result = firstOperand * secondOperand;
                    break;
                case '/':
                    if (secondOperand === 0) {
                        showError('Division by zero is not allowed');
                        return null;
                    }
                    result = firstOperand / secondOperand;
                    break;
                default:
                    return null;
            }

            // Round to avoid floating point precision issues
            result = Math.round((result + Number.EPSILON) * 100000000) / 100000000;
            return result;
        } catch (error) {
            showError('Calculation error occurred');
            return null;
        }
    }

    // Equals operation
    function equals() {
        const inputValue = parseFloat(currentValue);

        if (previousValue !== null && operation) {
            const result = calculate(previousValue, inputValue, operation);
            
            if (result !== null) {
                const calculation = `${previousValue} ${getOperationSymbol(operation)} ${inputValue} = ${result}`;
                addToHistory(calculation, result);

                currentValue = String(result);
                previousValue = null;
                operation = null;
                waitingForOperand = true;
                updateDisplay();
            }
        }
    }

    // Event listeners for calculator buttons
    document.querySelectorAll('.calc-btn').forEach(button => {
        button.addEventListener('click', function() {
            const number = this.dataset.number;
            const op = this.dataset.operation;
            const action = this.dataset.action;

            if (number) {
                inputNumber(number);
            } else if (op) {
                performOperation(op);
            } else if (action) {
                switch (action) {
                    case 'clear':
                        clear();
                        break;
                    case 'clear-entry':
                        clearEntry();
                        break;
                    case 'backspace':
                        backspace();
                        break;
                    case 'decimal':
                        inputDecimal();
                        break;
                    case 'equals':
                        equals();
                        break;
                }
            }
        });
    });

    // Common functionality
    if (clearBtn) {
        clearBtn.addEventListener('click', function() {
            clear();
            showSuccess('Calculator cleared');
        });
    }

    if (clearHistoryBtn) {
        clearHistoryBtn.addEventListener('click', function() {
            history = [];
            calculationCount = 0;
            updateHistoryDisplay();
            updateStatistics();
            showSuccess('History cleared');
        });
    }

    if (copyBtn) {
        copyBtn.addEventListener('click', function() {
            const historyText = history.map(item => `${item.calculation}`).join('\n');
            const textToCopy = historyText || currentValue;
            
            navigator.clipboard.writeText(textToCopy).then(() => {
                showSuccess('Copied to clipboard');
            }).catch(() => {
                showError('Failed to copy to clipboard');
            });
        });
    }

    if (downloadBtn) {
        downloadBtn.addEventListener('click', function() {
            const historyText = history.map(item => 
                `${item.calculation} (${item.timestamp.toLocaleString()})`
            ).join('\n');
            
            const content = `Calc-Fusion Calculation History\n${'='.repeat(40)}\n\n${historyText || 'No calculations yet'}\n\nTotal Calculations: ${calculationCount}`;
            
            const blob = new Blob([content], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `calc-fusion-history-${new Date().toISOString().split('T')[0]}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            showSuccess('History downloaded');
        });
    }

    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Prevent default for calculator keys
        if (/[0-9+\-*/.=]/.test(e.key) || e.key === 'Enter' || e.key === 'Escape' || e.key === 'Backspace') {
            e.preventDefault();
        }

        if (/[0-9]/.test(e.key)) {
            inputNumber(e.key);
        } else if (e.key === '.') {
            inputDecimal();
        } else if (['+', '-', '*', '/'].includes(e.key)) {
            performOperation(e.key);
        } else if (e.key === 'Enter' || e.key === '=') {
            equals();
        } else if (e.key === 'Escape') {
            clear();
        } else if (e.key === 'Backspace') {
            backspace();
        } else if (e.ctrlKey && e.key === 'c' && e.target.tagName !== 'INPUT' && e.target.tagName !== 'TEXTAREA') {
            e.preventDefault();
            if (copyBtn) copyBtn.click();
        }
    });

    // Initialize display
    updateDisplay();
    updateHistoryDisplay();
    updateStatistics();
});
</script>

</div>

<?php include_once ROOT_PATH . '/templates/layout/footer.php'; ?>