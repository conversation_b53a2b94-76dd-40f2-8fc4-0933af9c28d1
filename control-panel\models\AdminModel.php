<?php
/**
 * 管理员模型类
 * 处理管理员相关的数据库操作
 */

// 防止直接访问
if (!defined('ROOT_PATH')) {
    exit('Direct access not allowed');
}

// 加载基础模型
require_once ROOT_PATH . '/control-panel/models/BaseModel.php';
require_once ROOT_PATH . '/control-panel/classes/SecurityManager.php';

class AdminModel extends BaseModel {
    
    protected $table = 'pt_manager';
    protected $primaryKey = 'id';
    protected $fillable = [
        'username', 'email', 'password', 'first_name', 'last_name', 
        'role', 'status', 'avatar', 'phone', 'department', 'position',
        'last_login_at', 'last_login_ip', 'login_count', 'failed_login_count',
        'locked_until', 'password_changed_at', 'two_factor_enabled',
        'two_factor_secret', 'remember_token', 'custom_permissions',
        'timezone', 'language', 'theme', 'notifications_enabled'
    ];
    protected $hidden = ['password', 'two_factor_secret', 'remember_token'];
    
    private $securityManager;
    
    /**
     * 构造函数
     */
    public function __construct() {
        parent::__construct();
        $this->securityManager = new SecurityManager();
    }
    
    /**
     * 根据用户名查找管理员
     */
    public function findByUsername($username) {
        return $this->findWhere(['username' => $username]);
    }
    
    /**
     * 根据邮箱查找管理员
     */
    public function findByEmail($email) {
        return $this->findWhere(['email' => $email]);
    }
    
    /**
     * 根据记住我Token查找管理员
     */
    public function findByRememberToken($token) {
        return $this->findWhere(['remember_token' => $token]);
    }
    
    /**
     * 验证登录凭据
     */
    public function validateCredentials($username, $password) {
        // 查找用户
        $admin = $this->findByUsername($username);
        if (!$admin) {
            $admin = $this->findByEmail($username);
        }
        
        if (!$admin) {
            return false;
        }
        
        // 检查账户状态
        if ($admin['status'] !== 'active') {
            throw new Exception('Account is not active');
        }
        
        // 检查账户是否被锁定
        if ($admin['locked_until'] && strtotime($admin['locked_until']) > time()) {
            throw new Exception('Account is temporarily locked');
        }
        
        // 验证密码
        if (!$this->securityManager->verifyPassword($password, $admin['password'])) {
            $this->recordFailedLogin($admin['id']);
            return false;
        }
        
        // 清除失败登录记录
        $this->clearFailedLogins($admin['id']);
        
        return $admin;
    }
    
    /**
     * 创建管理员
     */
    public function createAdmin($data) {
        // 验证用户名唯一性
        if ($this->findByUsername($data['username'])) {
            throw new Exception('Username already exists');
        }
        
        // 验证邮箱唯一性
        if ($this->findByEmail($data['email'])) {
            throw new Exception('Email already exists');
        }
        
        // 验证密码强度
        $passwordValidation = $this->securityManager->validatePasswordStrength($data['password']);
        if (!$passwordValidation['valid']) {
            throw new Exception('Password does not meet security requirements: ' . implode(', ', $passwordValidation['errors']));
        }
        
        // 哈希密码
        $data['password'] = $this->securityManager->hashPassword($data['password']);
        $data['password_changed_at'] = date('Y-m-d H:i:s');
        $data['status'] = $data['status'] ?? 'active';
        $data['role'] = $data['role'] ?? 'viewer';
        
        return $this->create($data);
    }
    
    /**
     * 更新管理员
     */
    public function updateAdmin($id, $data) {
        // 如果更新用户名，检查唯一性
        if (isset($data['username'])) {
            $existing = $this->findByUsername($data['username']);
            if ($existing && $existing['id'] != $id) {
                throw new Exception('Username already exists');
            }
        }
        
        // 如果更新邮箱，检查唯一性
        if (isset($data['email'])) {
            $existing = $this->findByEmail($data['email']);
            if ($existing && $existing['id'] != $id) {
                throw new Exception('Email already exists');
            }
        }
        
        // 如果更新密码，验证强度并哈希
        if (isset($data['password'])) {
            $passwordValidation = $this->securityManager->validatePasswordStrength($data['password']);
            if (!$passwordValidation['valid']) {
                throw new Exception('Password does not meet security requirements: ' . implode(', ', $passwordValidation['errors']));
            }
            
            $data['password'] = $this->securityManager->hashPassword($data['password']);
            $data['password_changed_at'] = date('Y-m-d H:i:s');
        }
        
        return $this->update($id, $data);
    }
    
    /**
     * 更新登录信息
     */
    public function updateLoginInfo($id, $ip) {
        $data = [
            'last_login_at' => date('Y-m-d H:i:s'),
            'last_login_ip' => $ip,
            'login_count' => $this->db->fetchColumn(
                "SELECT login_count FROM {$this->table} WHERE id = :id", 
                ['id' => $id]
            ) + 1
        ];
        
        return $this->update($id, $data);
    }
    
    /**
     * 记录失败登录
     */
    public function recordFailedLogin($id) {
        $admin = $this->find($id);
        if (!$admin) {
            return false;
        }
        
        $failedCount = $admin['failed_login_count'] + 1;
        $data = ['failed_login_count' => $failedCount];
        
        // 如果失败次数达到阈值，锁定账户
        $maxAttempts = 5; // 可以从配置文件读取
        if ($failedCount >= $maxAttempts) {
            $data['locked_until'] = date('Y-m-d H:i:s', time() + 900); // 锁定15分钟
        }
        
        return $this->update($id, $data);
    }
    
    /**
     * 清除失败登录记录
     */
    public function clearFailedLogins($id) {
        return $this->update($id, [
            'failed_login_count' => 0,
            'locked_until' => null
        ]);
    }
    
    /**
     * 设置记住我Token
     */
    public function setRememberToken($id, $token) {
        return $this->update($id, ['remember_token' => $token]);
    }
    
    /**
     * 清除记住我Token
     */
    public function clearRememberToken($id) {
        return $this->update($id, ['remember_token' => null]);
    }
    
    /**
     * 获取活跃管理员列表
     */
    public function getActiveAdmins() {
        return $this->findAll(['status' => 'active'], 'username ASC');
    }
    
    /**
     * 获取管理员统计信息
     */
    public function getStatistics() {
        $stats = [
            'total' => $this->count(),
            'active' => $this->count(['status' => 'active']),
            'inactive' => $this->count(['status' => 'inactive']),
            'locked' => 0,
            'by_role' => [],
            'recent_logins' => []
        ];
        
        // 统计被锁定的账户
        $sql = "SELECT COUNT(*) FROM {$this->table} WHERE locked_until > NOW()";
        $stats['locked'] = $this->db->fetchColumn($sql);
        
        // 按角色统计
        $sql = "SELECT role, COUNT(*) as count FROM {$this->table} GROUP BY role";
        $roleStats = $this->db->fetchAll($sql);
        foreach ($roleStats as $stat) {
            $stats['by_role'][$stat['role']] = $stat['count'];
        }
        
        // 最近登录
        $sql = "
            SELECT id, username, last_login_at, last_login_ip 
            FROM {$this->table} 
            WHERE last_login_at IS NOT NULL 
            ORDER BY last_login_at DESC 
            LIMIT 10
        ";
        $stats['recent_logins'] = $this->db->fetchAll($sql);
        
        return $stats;
    }
    
    /**
     * 搜索管理员
     */
    public function search($keyword, $filters = []) {
        $sql = "SELECT * FROM {$this->table} WHERE 1=1";
        $params = [];
        
        // 关键词搜索
        if (!empty($keyword)) {
            $sql .= " AND (username LIKE :keyword OR email LIKE :keyword OR first_name LIKE :keyword OR last_name LIKE :keyword)";
            $params['keyword'] = "%{$keyword}%";
        }
        
        // 角色筛选
        if (!empty($filters['role'])) {
            $sql .= " AND role = :role";
            $params['role'] = $filters['role'];
        }
        
        // 状态筛选
        if (!empty($filters['status'])) {
            $sql .= " AND status = :status";
            $params['status'] = $filters['status'];
        }
        
        // 部门筛选
        if (!empty($filters['department'])) {
            $sql .= " AND department = :department";
            $params['department'] = $filters['department'];
        }
        
        // 日期范围筛选
        if (!empty($filters['date_from'])) {
            $sql .= " AND created_at >= :date_from";
            $params['date_from'] = $filters['date_from'];
        }
        
        if (!empty($filters['date_to'])) {
            $sql .= " AND created_at <= :date_to";
            $params['date_to'] = $filters['date_to'] . ' 23:59:59';
        }
        
        $sql .= " ORDER BY created_at DESC";
        
        $results = $this->db->fetchAll($sql, $params);
        return array_map([$this, 'hideFields'], $results);
    }
    
    /**
     * 获取管理员权限
     */
    public function getAdminPermissions($id) {
        $admin = $this->find($id);
        if (!$admin) {
            return [];
        }
        
        $permissions = [];
        
        // 获取角色权限
        $permissionManager = new PermissionManager();
        $rolePermissions = $permissionManager->getRolePermissions($admin['role']);
        $permissions = array_merge($permissions, $rolePermissions);
        
        // 获取自定义权限
        if (!empty($admin['custom_permissions'])) {
            $customPermissions = json_decode($admin['custom_permissions'], true) ?: [];
            $permissions = array_merge($permissions, $customPermissions);
        }
        
        return array_unique($permissions);
    }
    
    /**
     * 设置自定义权限
     */
    public function setCustomPermissions($id, $permissions) {
        $permissionsJson = json_encode($permissions);
        return $this->update($id, ['custom_permissions' => $permissionsJson]);
    }
    
    /**
     * 启用双因素认证
     */
    public function enableTwoFactor($id, $secret) {
        return $this->update($id, [
            'two_factor_enabled' => 1,
            'two_factor_secret' => $secret
        ]);
    }
    
    /**
     * 禁用双因素认证
     */
    public function disableTwoFactor($id) {
        return $this->update($id, [
            'two_factor_enabled' => 0,
            'two_factor_secret' => null
        ]);
    }
    
    /**
     * 获取部门列表
     */
    public function getDepartments() {
        $sql = "SELECT DISTINCT department FROM {$this->table} WHERE department IS NOT NULL AND department != '' ORDER BY department";
        $results = $this->db->fetchAll($sql);
        return array_column($results, 'department');
    }
    
    /**
     * 获取职位列表
     */
    public function getPositions() {
        $sql = "SELECT DISTINCT position FROM {$this->table} WHERE position IS NOT NULL AND position != '' ORDER BY position";
        $results = $this->db->fetchAll($sql);
        return array_column($results, 'position');
    }
    
    /**
     * 批量更新状态
     */
    public function batchUpdateStatus($ids, $status) {
        if (empty($ids)) {
            return 0;
        }
        
        $placeholders = implode(',', array_fill(0, count($ids), '?'));
        $sql = "UPDATE {$this->table} SET status = ?, updated_at = NOW() WHERE id IN ({$placeholders})";
        
        $params = array_merge([$status], $ids);
        $stmt = $this->db->query($sql, $params);
        
        return $stmt->rowCount();
    }
    
    /**
     * 获取登录历史
     */
    public function getLoginHistory($id, $limit = 50) {
        // 这里需要一个单独的登录历史表，暂时返回基本信息
        $admin = $this->find($id);
        if (!$admin) {
            return [];
        }
        
        return [
            [
                'login_time' => $admin['last_login_at'],
                'ip_address' => $admin['last_login_ip'],
                'user_agent' => 'Unknown',
                'status' => 'success'
            ]
        ];
    }
}
?>
