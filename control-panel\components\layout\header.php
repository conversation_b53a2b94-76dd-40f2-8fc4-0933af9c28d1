<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <?php
    // Dynamic SEO metadata for each page
    $currentPage = $_GET['page'] ?? 'dashboard';
    $pageTitle = 'Dashboard - Control Panel';
    $pageDescription = 'Manage your AI tools and platform settings from the main dashboard.';

    switch($currentPage) {
        case 'profile':
            $pageTitle = 'Profile';
            $pageDescription = 'View your profile information and recent activity logs.';
            break;
        case 'account':
            $pageTitle = 'Account Settings';
            $pageDescription = 'Edit your account information, username, email and personal details.';
            break;
        case 'password':
            $pageTitle = 'Change Password';
            $pageDescription = 'Update your account password with secure two-factor authentication.';
            break;
        case 'security':
            $pageTitle = 'Security Settings';
            $pageDescription = 'Manage two-factor authentication and view active login sessions.';
            break;
        case 'tools':
            $pageTitle = 'Tools';
            $pageDescription = 'Manage and configure your AI tools and integrations.';
            break;
        case 'api':
            $pageTitle = 'API';
            $pageDescription = 'Manage multi-platform API keys and model configurations.';
            break;

        case 'users':
            $pageTitle = 'User';
            $pageDescription = 'Manage platform users, permissions and access controls.';
            break;
        case 'user-requests':
            $pageTitle = 'Requests';
            $pageDescription = 'Manage user feature requests and provide responses.';
            break;
        case 'settings':
            $pageTitle = 'Settings';
            $pageDescription = 'Configure platform settings, preferences and system options.';
            break;
        case 'analytics':
            $pageTitle = 'Analytics';
            $pageDescription = 'View platform analytics, usage statistics and performance metrics.';
            break;
        case 'messages':
            $pageTitle = 'Messages';
            $pageDescription = 'Manage and respond to user contact form submissions and inquiries.';
            break;
        case 'message-view':
            $pageTitle = 'Message Details';
            $pageDescription = 'View detailed contact message information and add admin notes.';
            break;
        case 'tool-categories':
            $pageTitle = 'Categories';
            $pageDescription = 'Manage tool categories, organization and display settings.';
            break;
        case 'prompt':
            $pageTitle = 'Prompt';
            $pageDescription = '';
            break;
        case 'program':
            $pageTitle = 'Program';
            $pageDescription = 'Generate custom tools with AI';
            break;
        case 'launches':
            $pageTitle = 'Launches';
            $pageDescription = 'Manage product launches and moderate submissions';
            break;
        default:
            $pageTitle = 'Prompt2Tool Admin';
            $pageDescription = 'Manage your AI tools and platform settings from the main dashboard.';
    }
    ?>
    <title><?= htmlspecialchars($pageTitle) ?></title>
    <meta name="description" content="<?= htmlspecialchars($pageDescription) ?>">
    
    <!-- 安全头部 -->
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta http-equiv="X-XSS-Protection" content="1; mode=block">
    <meta name="referrer" content="strict-origin-when-cross-origin">
    
    <!-- 禁止搜索引擎索引 -->
    <meta name="robots" content="noindex, nofollow, noarchive, nosnippet">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- CodeMirror -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.16/codemirror.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.16/theme/monokai.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.16/codemirror.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.16/mode/php/php.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.16/mode/xml/xml.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.16/mode/javascript/javascript.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.16/mode/css/css.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.16/mode/htmlmixed/htmlmixed.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.16/addon/edit/closebrackets.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.16/addon/edit/matchbrackets.min.js"></script>

    <!-- 自定义配置 - 参考前端设计规范 -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#000000',
                        secondary: '#1a1a1a',
                        accent: '#4e73df',
                        success: '#1cc88a',
                        warning: '#f6c23e',
                        danger: '#e74a3b'
                    },
                    borderRadius: {
                        'none': '0',
                        DEFAULT: '0'
                    }
                }
            }
        }
    </script>

    <!-- 自定义样式 - 遵循设计规范 -->
    <style>
        /* 强制所有元素为方正设计 - 零圆角原则 */
        * {
            border-radius: 0 !important;
        }

        /* 侧边栏样式 - 极简设计 */
        .sidebar-link {
            transition: all 0.2s ease;
            border-left: 3px solid transparent;
        }

        .sidebar-link:hover {
            background-color: #1a1a1a;
            border-left-color: #4e73df;
        }

        .sidebar-link.active {
            background-color: #1a1a1a;
            border-left-color: #4e73df;
            color: #ffffff;
        }

        /* 修复布局问题 */
        .main-content {
            width: 100%;
        }

        /* 按钮样式 - 功能优先 */
        .btn {
            border-radius: 0 !important;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        /* 卡片样式 - 直角设计 */
        .card {
            border-radius: 0 !important;
            box-shadow: none;
            border: 1px solid #e5e7eb;
        }

        /* 主体背景 - 浅色主题 */
        body {
            background-color: #f8fafc;
        }
    </style>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#000000',
                        secondary: '#1a1a1a',
                        accent: '#4e73df',
                        success: '#1cc88a',
                        warning: '#f6c23e',
                        danger: '#e74a3b'
                    }
                }
            }
        }
    </script>
    
    <!-- 自定义样式 -->
    <style>
        /* 管理后台专用样式 */
        .admin-card {
            background: white;
            border: 1px solid #e5e7eb;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        }
        
        .admin-card:hover {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        
        .sidebar-link {
            transition: all 0.2s ease;
        }
        
        .sidebar-link:hover {
            background-color: rgba(78, 115, 223, 0.1);
            color: #4e73df;
        }
        
        .sidebar-link.active {
            background-color: #4e73df;
            color: white;
        }
        
        /* 统计卡片动画 */
        .stat-card {
            transition: transform 0.2s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-2px);
        }
    </style>
</head>
<body class="bg-gray-50"><?php
/**
 * 管理后台头部布局
 * 包含基础HTML结构和样式
 */
?>
