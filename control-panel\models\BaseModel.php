<?php
/**
 * 基础模型类
 * 提供通用的数据库操作方法
 */

// 防止直接访问
if (!defined('ROOT_PATH')) {
    exit('Direct access not allowed');
}

// 加载数据库类
require_once ROOT_PATH . '/control-panel/classes/Database.php';

abstract class BaseModel {
    
    protected $db;
    protected $table;
    protected $primaryKey = 'id';
    protected $fillable = [];
    protected $hidden = [];
    protected $timestamps = true;
    protected $dateFormat = 'Y-m-d H:i:s';
    
    /**
     * 构造函数
     */
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * 查找单条记录
     */
    public function find($id) {
        $sql = "SELECT * FROM {$this->table} WHERE {$this->primaryKey} = :id LIMIT 1";
        $result = $this->db->fetch($sql, ['id' => $id]);
        
        if ($result) {
            return $this->hideFields($result);
        }
        
        return null;
    }
    
    /**
     * 查找所有记录
     */
    public function findAll($conditions = [], $orderBy = null, $limit = null) {
        $sql = "SELECT * FROM {$this->table}";
        $params = [];
        
        if (!empty($conditions)) {
            $whereClause = $this->buildWhereClause($conditions, $params);
            $sql .= " WHERE {$whereClause}";
        }
        
        if ($orderBy) {
            $sql .= " ORDER BY {$orderBy}";
        }
        
        if ($limit) {
            $sql .= " LIMIT {$limit}";
        }
        
        $results = $this->db->fetchAll($sql, $params);
        
        return array_map([$this, 'hideFields'], $results);
    }
    
    /**
     * 根据条件查找单条记录
     */
    public function findWhere($conditions) {
        $sql = "SELECT * FROM {$this->table}";
        $params = [];
        
        if (!empty($conditions)) {
            $whereClause = $this->buildWhereClause($conditions, $params);
            $sql .= " WHERE {$whereClause}";
        }
        
        $sql .= " LIMIT 1";
        
        $result = $this->db->fetch($sql, $params);
        
        if ($result) {
            return $this->hideFields($result);
        }
        
        return null;
    }
    
    /**
     * 分页查询
     */
    public function paginate($page = 1, $perPage = 20, $conditions = [], $orderBy = null) {
        $offset = ($page - 1) * $perPage;
        
        // 获取总数
        $countSql = "SELECT COUNT(*) FROM {$this->table}";
        $params = [];
        
        if (!empty($conditions)) {
            $whereClause = $this->buildWhereClause($conditions, $params);
            $countSql .= " WHERE {$whereClause}";
        }
        
        $total = $this->db->fetchColumn($countSql, $params);
        
        // 获取数据
        $sql = "SELECT * FROM {$this->table}";
        
        if (!empty($conditions)) {
            $sql .= " WHERE {$whereClause}";
        }
        
        if ($orderBy) {
            $sql .= " ORDER BY {$orderBy}";
        }
        
        $sql .= " LIMIT {$perPage} OFFSET {$offset}";
        
        $results = $this->db->fetchAll($sql, $params);
        $data = array_map([$this, 'hideFields'], $results);
        
        return [
            'data' => $data,
            'total' => $total,
            'per_page' => $perPage,
            'current_page' => $page,
            'last_page' => ceil($total / $perPage),
            'from' => $offset + 1,
            'to' => min($offset + $perPage, $total)
        ];
    }
    
    /**
     * 创建记录
     */
    public function create($data) {
        $data = $this->filterFillable($data);
        
        if ($this->timestamps) {
            $now = date($this->dateFormat);
            $data['created_at'] = $now;
            $data['updated_at'] = $now;
        }
        
        $id = $this->db->insert($this->table, $data);
        return $this->find($id);
    }
    
    /**
     * 更新记录
     */
    public function update($id, $data) {
        $data = $this->filterFillable($data);
        
        if ($this->timestamps) {
            $data['updated_at'] = date($this->dateFormat);
        }
        
        $affected = $this->db->update(
            $this->table, 
            $data, 
            "{$this->primaryKey} = :id", 
            ['id' => $id]
        );
        
        if ($affected > 0) {
            return $this->find($id);
        }
        
        return null;
    }
    
    /**
     * 删除记录
     */
    public function delete($id) {
        return $this->db->delete(
            $this->table, 
            "{$this->primaryKey} = :id", 
            ['id' => $id]
        );
    }
    
    /**
     * 软删除记录
     */
    public function softDelete($id) {
        return $this->update($id, ['deleted_at' => date($this->dateFormat)]);
    }
    
    /**
     * 恢复软删除的记录
     */
    public function restore($id) {
        return $this->update($id, ['deleted_at' => null]);
    }
    
    /**
     * 批量插入
     */
    public function batchInsert($data) {
        if (empty($data)) {
            return false;
        }
        
        $this->db->beginTransaction();
        
        try {
            $ids = [];
            foreach ($data as $row) {
                $ids[] = $this->db->insert($this->table, $this->filterFillable($row));
            }
            
            $this->db->commit();
            return $ids;
        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }
    
    /**
     * 批量更新
     */
    public function batchUpdate($data, $keyField = null) {
        if (empty($data)) {
            return false;
        }
        
        $keyField = $keyField ?: $this->primaryKey;
        
        $this->db->beginTransaction();
        
        try {
            $affected = 0;
            foreach ($data as $row) {
                if (isset($row[$keyField])) {
                    $id = $row[$keyField];
                    unset($row[$keyField]);
                    $affected += $this->db->update(
                        $this->table, 
                        $this->filterFillable($row), 
                        "{$keyField} = :id", 
                        ['id' => $id]
                    );
                }
            }
            
            $this->db->commit();
            return $affected;
        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }
    
    /**
     * 统计记录数
     */
    public function count($conditions = []) {
        $sql = "SELECT COUNT(*) FROM {$this->table}";
        $params = [];
        
        if (!empty($conditions)) {
            $whereClause = $this->buildWhereClause($conditions, $params);
            $sql .= " WHERE {$whereClause}";
        }
        
        return $this->db->fetchColumn($sql, $params);
    }
    
    /**
     * 检查记录是否存在
     */
    public function exists($conditions) {
        return $this->count($conditions) > 0;
    }
    
    /**
     * 获取最大值
     */
    public function max($field, $conditions = []) {
        $sql = "SELECT MAX({$field}) FROM {$this->table}";
        $params = [];
        
        if (!empty($conditions)) {
            $whereClause = $this->buildWhereClause($conditions, $params);
            $sql .= " WHERE {$whereClause}";
        }
        
        return $this->db->fetchColumn($sql, $params);
    }
    
    /**
     * 获取最小值
     */
    public function min($field, $conditions = []) {
        $sql = "SELECT MIN({$field}) FROM {$this->table}";
        $params = [];
        
        if (!empty($conditions)) {
            $whereClause = $this->buildWhereClause($conditions, $params);
            $sql .= " WHERE {$whereClause}";
        }
        
        return $this->db->fetchColumn($sql, $params);
    }
    
    /**
     * 获取平均值
     */
    public function avg($field, $conditions = []) {
        $sql = "SELECT AVG({$field}) FROM {$this->table}";
        $params = [];
        
        if (!empty($conditions)) {
            $whereClause = $this->buildWhereClause($conditions, $params);
            $sql .= " WHERE {$whereClause}";
        }
        
        return $this->db->fetchColumn($sql, $params);
    }
    
    /**
     * 获取总和
     */
    public function sum($field, $conditions = []) {
        $sql = "SELECT SUM({$field}) FROM {$this->table}";
        $params = [];
        
        if (!empty($conditions)) {
            $whereClause = $this->buildWhereClause($conditions, $params);
            $sql .= " WHERE {$whereClause}";
        }
        
        return $this->db->fetchColumn($sql, $params);
    }
    
    /**
     * 构建WHERE子句
     */
    protected function buildWhereClause($conditions, &$params) {
        $clauses = [];
        
        foreach ($conditions as $field => $value) {
            if (is_array($value)) {
                // IN 查询
                $placeholders = [];
                foreach ($value as $i => $v) {
                    $placeholder = ":{$field}_{$i}";
                    $placeholders[] = $placeholder;
                    $params[$field . '_' . $i] = $v;
                }
                $clauses[] = "{$field} IN (" . implode(', ', $placeholders) . ")";
            } else {
                $clauses[] = "{$field} = :{$field}";
                $params[$field] = $value;
            }
        }
        
        return implode(' AND ', $clauses);
    }
    
    /**
     * 过滤可填充字段
     */
    protected function filterFillable($data) {
        if (empty($this->fillable)) {
            return $data;
        }
        
        return array_intersect_key($data, array_flip($this->fillable));
    }
    
    /**
     * 隐藏敏感字段
     */
    protected function hideFields($data) {
        if (empty($this->hidden)) {
            return $data;
        }
        
        foreach ($this->hidden as $field) {
            unset($data[$field]);
        }
        
        return $data;
    }
    
    /**
     * 执行原生SQL查询
     */
    public function query($sql, $params = []) {
        return $this->db->fetchAll($sql, $params);
    }
    
    /**
     * 获取表名
     */
    public function getTable() {
        return $this->table;
    }
    
    /**
     * 获取主键字段名
     */
    public function getPrimaryKey() {
        return $this->primaryKey;
    }
}
?>
