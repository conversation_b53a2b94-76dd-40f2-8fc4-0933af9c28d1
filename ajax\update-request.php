<?php
/**
 * 更新用户需求
 */

session_start();
header('Content-Type: application/json');

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit;
}

// 检查用户登录
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'Please login first']);
    exit;
}

// 获取表单数据
$requestId = intval($_POST['request_id'] ?? 0);
$title = trim($_POST['title'] ?? '');
$description = trim($_POST['description'] ?? '');
$category = $_POST['category'] ?? '';

// 验证数据
if ($requestId <= 0) {
    echo json_encode(['success' => false, 'message' => 'Invalid request ID']);
    exit;
}

if (empty($title)) {
    echo json_encode(['success' => false, 'message' => 'Title is required']);
    exit;
}

if (strlen($title) < 5) {
    echo json_encode(['success' => false, 'message' => 'Title is too short (min 5 characters)']);
    exit;
}

if (strlen($title) > 200) {
    echo json_encode(['success' => false, 'message' => 'Title is too long (max 200 characters)']);
    exit;
}

if (empty($description)) {
    echo json_encode(['success' => false, 'message' => 'Description is required']);
    exit;
}

if (strlen($description) < 20) {
    echo json_encode(['success' => false, 'message' => 'Description is too short (min 20 characters)']);
    exit;
}

if (strlen($description) > 1000) {
    echo json_encode(['success' => false, 'message' => 'Description is too long (max 1000 characters)']);
    exit;
}

// 使用统一的数据库连接
require_once __DIR__ . '/includes/ajax-database.php';

try {

    // 验证分类
    $validCategories = [];
    $categoryStmt = $pdo->query("SELECT slug FROM pt_tool_category WHERE status = 'active'");
    $validCategories = $categoryStmt->fetchAll(PDO::FETCH_COLUMN);
    $validCategories[] = 'other';

    if (!in_array($category, $validCategories)) {
        echo json_encode(['success' => false, 'message' => 'Invalid category']);
        exit;
    }

    // 检查需求是否存在且属于当前用户，且状态为pending
    $checkStmt = $pdo->prepare("
        SELECT status FROM pt_user_requests 
        WHERE id = ? AND user_id = ?
    ");
    $checkStmt->execute([$requestId, $_SESSION['user_id']]);
    $existingRequest = $checkStmt->fetch();

    if (!$existingRequest) {
        echo json_encode(['success' => false, 'message' => 'Request not found or access denied']);
        exit;
    }

    if ($existingRequest['status'] !== 'pending') {
        echo json_encode(['success' => false, 'message' => 'Only pending requests can be edited']);
        exit;
    }

    // 更新需求
    $updateStmt = $pdo->prepare("
        UPDATE pt_user_requests 
        SET title = ?, description = ?, category = ?, updated_at = NOW()
        WHERE id = ? AND user_id = ?
    ");
    $updateStmt->execute([$title, $description, $category, $requestId, $_SESSION['user_id']]);

    if ($updateStmt->rowCount() > 0) {
        echo json_encode([
            'success' => true,
            'message' => 'Request updated successfully'
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'No changes made'
        ]);
    }

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Database error: ' . $e->getMessage()
    ]);
}
?>
