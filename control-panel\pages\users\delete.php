<?php
/**
 * 用户删除处理页面
 * 处理用户删除请求
 */

// 定义根目录
define('ROOT_PATH', dirname(dirname(dirname(__DIR__))));

// 加载依赖
require_once dirname(__DIR__) . '/../auth/middleware.php';
require_once dirname(__DIR__) . '/../middleware/PermissionMiddleware.php';
require_once dirname(__DIR__) . '/../models/UserModel.php';
require_once dirname(__DIR__) . '/../classes/Database.php';

// 权限检查
requirePermission('users.delete');

// 初始化模型
$userModel = new UserModel();
$db = Database::getInstance();

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Location: index.php?error=' . urlencode('Invalid request method'));
    exit;
}

// 获取用户ID
$userId = intval($_POST['id'] ?? 0);
if (!$userId) {
    header('Location: index.php?error=' . urlencode('User ID is required'));
    exit;
}

try {
    // 获取用户信息
    $user = $userModel->find($userId);
    if (!$user) {
        throw new Exception('User not found');
    }
    
    // 检查是否有相关数据
    $hasToolUsage = $db->fetchColumn(
        "SELECT COUNT(*) FROM pt_tool_usage WHERE user_id = :user_id",
        ['user_id' => $userId]
    ) > 0;
    
    $hasActivityLogs = $db->fetchColumn(
        "SELECT COUNT(*) FROM pt_member_activity_log WHERE user_id = :user_id",
        ['user_id' => $userId]
    ) > 0;
    
    $hasAnalyticsEvents = $db->fetchColumn(
        "SELECT COUNT(*) FROM pt_analytics_event WHERE user_id = :user_id",
        ['user_id' => $userId]
    ) > 0;
    
    $hasReferrals = $db->fetchColumn(
        "SELECT COUNT(*) FROM pt_member WHERE referred_by = :user_id",
        ['user_id' => $userId]
    ) > 0;
    
    // 开始事务
    $db->beginTransaction();
    
    try {
        if ($hasToolUsage || $hasActivityLogs || $hasAnalyticsEvents || $hasReferrals) {
            // 如果有相关数据，使用软删除
            $result = $userModel->softDelete($userId);
            $message = 'User has been archived due to existing activity records';
            $deletionType = 'soft';
        } else {
            // 如果没有相关数据，可以完全删除
            $result = $userModel->delete($userId);
            $message = 'User has been permanently deleted';
            $deletionType = 'hard';
        }
        
        if ($result) {
            // 记录删除操作
            $currentUser = getCurrentAdmin();
            $logData = [
                'admin_id' => $currentUser['id'],
                'activity_type' => 'user_delete',
                'description' => "Deleted user: {$user['email']} (ID: {$userId})",
                'target_type' => 'user',
                'target_id' => $userId,
                'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
                'metadata' => json_encode([
                    'user_email' => $user['email'],
                    'user_username' => $user['username'],
                    'deletion_type' => $deletionType,
                    'had_tool_usage' => $hasToolUsage,
                    'had_activity_logs' => $hasActivityLogs,
                    'had_analytics_events' => $hasAnalyticsEvents,
                    'had_referrals' => $hasReferrals
                ])
            ];
            
            $db->insert('pt_activity_log', $logData);
            
            // 提交事务
            $db->commit();
            
            header('Location: index.php?message=' . urlencode($message));
            exit;
        } else {
            throw new Exception('Failed to delete user');
        }
        
    } catch (Exception $e) {
        $db->rollback();
        throw $e;
    }
    
} catch (Exception $e) {
    header('Location: index.php?error=' . urlencode($e->getMessage()));
    exit;
}
?>
