<?php
/**
 * 配额消耗API
 * 执行配额扣除或增加操作
 */

header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');

// 检查用户登录状态
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

if (!isset($_SESSION['user_id'])) {
    echo json_encode([
        'success' => false,
        'message' => 'Please login first'
    ]);
    exit;
}

// 定义根路径
define('ROOT_PATH', dirname(__DIR__));
define('APP_INITIALIZED', true);

// 引入必要文件
require_once ROOT_PATH . '/includes/database-connection.php';
require_once ROOT_PATH . '/classes/QuotaManager.php';

try {
    // 获取请求数据
    $input = json_decode(file_get_contents('php://input'), true);
    $action = $input['action'] ?? '';
    $description = $input['description'] ?? null;
    
    if (empty($action)) {
        throw new Exception('Action type cannot be empty');
    }
    
    // 获取数据库连接
    $pdo = getDatabaseConnection();
    $quotaManager = new QuotaManager($pdo);
    
    // 执行配额操作
    $userId = $_SESSION['user_id'];
    $result = $quotaManager->consumeQuota($userId, $action, $description);
    
    if ($result['success']) {
        // 获取更新后的配额信息
        $userQuota = $quotaManager->getUserQuota($userId);
        
        echo json_encode([
            'success' => true,
            'message' => 'Quota operation successful',
            'quota_change' => $result['quota_change'],
            'action_description' => $result['action'],
            'current_quota' => [
                'used' => $userQuota['api_used'],
                'total' => $userQuota['api_quota'],
                'remaining' => $userQuota['api_quota'] - $userQuota['api_used']
            ]
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => $result['error']
        ]);
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
