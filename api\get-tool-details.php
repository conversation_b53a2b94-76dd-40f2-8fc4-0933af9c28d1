<?php
/**
 * 获取工具详情API
 * 用于My Tools页面的详情模态框
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

// 启动会话
session_start();

// 检查用户是否登录
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit;
}

// 只允许GET请求
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit;
}

$toolId = $_GET['id'] ?? '';

if (empty($toolId) || !is_numeric($toolId)) {
    echo json_encode(['success' => false, 'error' => 'Valid tool ID is required']);
    exit;
}

// 使用统一的数据库连接
require_once __DIR__ . '/../includes/database-connection.php';

try {
    $userId = $_SESSION['user_id'];
    
    // 查询工具详情，确保只能查看自己创建的工具
    $stmt = $pdo->prepare("
        SELECT t.*, tc.name as category_name, tc.slug as category_slug
        FROM pt_tool t
        LEFT JOIN pt_tool_category tc ON t.category_id = tc.id
        WHERE t.id = ? AND t.created_by = ?
        LIMIT 1
    ");
    $stmt->execute([$toolId, $userId]);
    $tool = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$tool) {
        echo json_encode(['success' => false, 'error' => 'Tool not found or access denied']);
        exit;
    }

    // 返回工具详情
    echo json_encode([
        'success' => true,
        'tool' => [
            'id' => $tool['id'],
            'name' => $tool['name'],
            'slug' => $tool['slug'],
            'description' => $tool['description'],
            'icon' => $tool['icon'],
            'tags' => $tool['tags'],
            'status' => $tool['status'],
            'view_count' => $tool['view_count'],
            'file_type' => $tool['file_type'],
            'category_name' => $tool['category_name'],
            'category_slug' => $tool['category_slug'],
            'created_at' => $tool['created_at'],
            'updated_at' => $tool['updated_at']
        ]
    ]);

} catch (Exception $e) {
    error_log("Get tool details error: " . $e->getMessage());
    echo json_encode(['success' => false, 'error' => 'Database error']);
}
?>
