RewriteEngine On

# API路由 - 指向上级目录
RewriteCond %{REQUEST_URI} ^/api/
RewriteRule ^api/(.*)$ ../api/index.php [QSA,L]

# 后台管理路由 - 指向上级目录
RewriteCond %{REQUEST_URI} ^/control-panel/
RewriteRule ^control-panel/(.*)$ ../control-panel/index.php [QSA,L]

# 前端路由 - 当前目录
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php [QSA,L]

# 安全设置
<FilesMatch "\.(log|bak|sql|env)$">
    Order deny,allow
    Deny from all
</FilesMatch>

# 阻止访问隐藏文件
<FilesMatch "^\.">
    Order deny,allow
    Deny from all
</FilesMatch>

# 静态文件缓存
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/svg+xml "access plus 1 year"
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
</IfModule>

# 安全头部
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>
