<?php
/**
 * 删除用户需求
 */

session_start();
header('Content-Type: application/json');

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit;
}

// 检查用户登录
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'Please login first']);
    exit;
}

// 获取请求ID
$requestId = intval($_POST['id'] ?? 0);

if ($requestId <= 0) {
    echo json_encode(['success' => false, 'message' => 'Invalid request ID']);
    exit;
}

// 使用统一的数据库连接
require_once __DIR__ . '/includes/ajax-database.php';

try {

    // 检查需求是否存在且属于当前用户，且状态为pending
    $checkStmt = $pdo->prepare("
        SELECT status FROM pt_user_requests 
        WHERE id = ? AND user_id = ?
    ");
    $checkStmt->execute([$requestId, $_SESSION['user_id']]);
    $existingRequest = $checkStmt->fetch();

    if (!$existingRequest) {
        echo json_encode(['success' => false, 'message' => 'Request not found or access denied']);
        exit;
    }

    if ($existingRequest['status'] !== 'pending') {
        echo json_encode(['success' => false, 'message' => 'Only pending requests can be deleted']);
        exit;
    }

    // 开始事务
    $pdo->beginTransaction();

    try {
        // 删除相关的投票记录
        $deleteVotesStmt = $pdo->prepare("DELETE FROM pt_request_votes WHERE request_id = ?");
        $deleteVotesStmt->execute([$requestId]);

        // 删除防重复记录
        $deleteDuplicatesStmt = $pdo->prepare("DELETE FROM pt_request_duplicates WHERE request_id = ?");
        $deleteDuplicatesStmt->execute([$requestId]);

        // 删除需求记录
        $deleteRequestStmt = $pdo->prepare("
            DELETE FROM pt_user_requests 
            WHERE id = ? AND user_id = ?
        ");
        $deleteRequestStmt->execute([$requestId, $_SESSION['user_id']]);

        if ($deleteRequestStmt->rowCount() > 0) {
            $pdo->commit();
            echo json_encode([
                'success' => true,
                'message' => 'Request deleted successfully'
            ]);
        } else {
            $pdo->rollBack();
            echo json_encode([
                'success' => false,
                'message' => 'Failed to delete request'
            ]);
        }

    } catch (Exception $e) {
        $pdo->rollBack();
        throw $e;
    }

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Database error: ' . $e->getMessage()
    ]);
}
?>
