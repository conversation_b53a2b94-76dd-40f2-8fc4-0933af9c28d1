<?php
/**
 * 角色管理器类
 * 负责处理用户角色的创建、修改和管理
 */

// 防止直接访问
if (!defined('ROOT_PATH')) {
    exit('Direct access not allowed');
}

// 加载权限管理器
require_once ROOT_PATH . '/control-panel/classes/PermissionManager.php';

class RoleManager {
    
    private $permissionManager;
    private $config;
    private $cache;
    
    /**
     * 构造函数
     */
    public function __construct() {
        $this->permissionManager = new PermissionManager();
        $this->loadConfiguration();
        $this->initializeCache();
    }
    
    /**
     * 加载配置
     */
    private function loadConfiguration() {
        $this->config = include ROOT_PATH . '/control-panel/config/permissions.php';
    }
    
    /**
     * 初始化缓存
     */
    private function initializeCache() {
        try {
            $this->cache = new Redis();
            $this->cache->connect('127.0.0.1', 6379);
            $this->cache->select(4); // 使用数据库4存储角色缓存
        } catch (Exception $e) {
            error_log('Redis connection failed for roles: ' . $e->getMessage());
            $this->cache = null;
        }
    }
    
    /**
     * 创建新角色
     */
    public function createRole($roleData, $creator) {
        // 验证创建者权限
        if (!$this->permissionManager->hasPermission($creator, 'admins.create')) {
            throw new Exception('Insufficient permissions to create roles');
        }
        
        // 验证角色数据
        $validationResult = $this->validateRoleData($roleData);
        if (!$validationResult['valid']) {
            throw new Exception('Invalid role data: ' . implode(', ', $validationResult['errors']));
        }
        
        // 检查角色是否已存在
        if ($this->roleExists($roleData['slug'])) {
            throw new Exception('Role already exists');
        }
        
        // 检查权限级别限制
        if ($roleData['level'] >= $this->permissionManager->getRoleLevel($creator['role'])) {
            throw new Exception('Cannot create role with equal or higher permission level');
        }
        
        // 准备角色数据
        $role = [
            'name' => $roleData['name'],
            'description' => $roleData['description'],
            'level' => $roleData['level'],
            'permissions' => $roleData['permissions'],
            'color' => $roleData['color'] ?? '#858796',
            'icon' => $roleData['icon'] ?? 'fas fa-user',
            'created_by' => $creator['id'],
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s'),
            'is_active' => true,
            'is_system' => false // 自定义角色
        ];
        
        // 保存角色（这里应该保存到数据库）
        $this->saveRoleToDatabase($roleData['slug'], $role);
        
        // 清除缓存
        $this->clearRoleCache();
        
        // 记录操作日志
        $this->logRoleOperation('create', $roleData['slug'], $creator, $role);
        
        return [
            'success' => true,
            'message' => 'Role created successfully',
            'role_slug' => $roleData['slug']
        ];
    }
    
    /**
     * 更新角色
     */
    public function updateRole($roleSlug, $roleData, $updater) {
        // 验证更新者权限
        if (!$this->permissionManager->hasPermission($updater, 'admins.edit')) {
            throw new Exception('Insufficient permissions to update roles');
        }
        
        // 检查角色是否存在
        if (!$this->roleExists($roleSlug)) {
            throw new Exception('Role not found');
        }
        
        // 获取现有角色信息
        $existingRole = $this->getRole($roleSlug);
        
        // 检查是否为系统角色
        if ($existingRole['is_system'] ?? false) {
            throw new Exception('Cannot modify system roles');
        }
        
        // 验证角色数据
        $validationResult = $this->validateRoleData($roleData, $roleSlug);
        if (!$validationResult['valid']) {
            throw new Exception('Invalid role data: ' . implode(', ', $validationResult['errors']));
        }
        
        // 检查权限级别限制
        if ($roleData['level'] >= $this->permissionManager->getRoleLevel($updater['role'])) {
            throw new Exception('Cannot set role level equal or higher than your own');
        }
        
        // 准备更新数据
        $updatedRole = array_merge($existingRole, [
            'name' => $roleData['name'],
            'description' => $roleData['description'],
            'level' => $roleData['level'],
            'permissions' => $roleData['permissions'],
            'color' => $roleData['color'] ?? $existingRole['color'],
            'icon' => $roleData['icon'] ?? $existingRole['icon'],
            'updated_by' => $updater['id'],
            'updated_at' => date('Y-m-d H:i:s')
        ]);
        
        // 保存更新（这里应该更新数据库）
        $this->saveRoleToDatabase($roleSlug, $updatedRole);
        
        // 清除相关缓存
        $this->clearRoleCache();
        $this->clearUsersPermissionCache($roleSlug);
        
        // 记录操作日志
        $this->logRoleOperation('update', $roleSlug, $updater, $updatedRole, $existingRole);
        
        return [
            'success' => true,
            'message' => 'Role updated successfully'
        ];
    }
    
    /**
     * 删除角色
     */
    public function deleteRole($roleSlug, $deleter) {
        // 验证删除者权限
        if (!$this->permissionManager->hasPermission($deleter, 'admins.delete')) {
            throw new Exception('Insufficient permissions to delete roles');
        }
        
        // 检查角色是否存在
        if (!$this->roleExists($roleSlug)) {
            throw new Exception('Role not found');
        }
        
        // 获取角色信息
        $role = $this->getRole($roleSlug);
        
        // 检查是否为系统角色
        if ($role['is_system'] ?? false) {
            throw new Exception('Cannot delete system roles');
        }
        
        // 检查是否有用户使用此角色
        $usersWithRole = $this->getUsersWithRole($roleSlug);
        if (!empty($usersWithRole)) {
            throw new Exception('Cannot delete role: ' . count($usersWithRole) . ' users are assigned to this role');
        }
        
        // 删除角色（这里应该从数据库删除）
        $this->deleteRoleFromDatabase($roleSlug);
        
        // 清除缓存
        $this->clearRoleCache();
        
        // 记录操作日志
        $this->logRoleOperation('delete', $roleSlug, $deleter, null, $role);
        
        return [
            'success' => true,
            'message' => 'Role deleted successfully'
        ];
    }
    
    /**
     * 获取角色信息
     */
    public function getRole($roleSlug) {
        $cacheKey = "role_info:{$roleSlug}";
        
        if ($this->cache) {
            $cached = $this->cache->get($cacheKey);
            if ($cached !== false) {
                return json_decode($cached, true);
            }
        }
        
        // 从配置文件获取系统角色
        if (isset($this->config['roles'][$roleSlug])) {
            $role = $this->config['roles'][$roleSlug];
            $role['is_system'] = true;
            
            if ($this->cache) {
                $this->cache->setex($cacheKey, 3600, json_encode($role));
            }
            
            return $role;
        }
        
        // 从数据库获取自定义角色
        $role = $this->getRoleFromDatabase($roleSlug);
        
        if ($role && $this->cache) {
            $this->cache->setex($cacheKey, 3600, json_encode($role));
        }
        
        return $role;
    }
    
    /**
     * 获取所有角色
     */
    public function getAllRoles($includeInactive = false) {
        $cacheKey = "all_roles:" . ($includeInactive ? 'with_inactive' : 'active_only');
        
        if ($this->cache) {
            $cached = $this->cache->get($cacheKey);
            if ($cached !== false) {
                return json_decode($cached, true);
            }
        }
        
        $roles = [];
        
        // 添加系统角色
        foreach ($this->config['roles'] as $slug => $role) {
            $role['slug'] = $slug;
            $role['is_system'] = true;
            $roles[$slug] = $role;
        }
        
        // 添加自定义角色
        $customRoles = $this->getCustomRolesFromDatabase($includeInactive);
        foreach ($customRoles as $slug => $role) {
            $role['slug'] = $slug;
            $role['is_system'] = false;
            $roles[$slug] = $role;
        }
        
        // 按权限级别排序
        uasort($roles, function($a, $b) {
            return $b['level'] - $a['level'];
        });
        
        if ($this->cache) {
            $this->cache->setex($cacheKey, 1800, json_encode($roles));
        }
        
        return $roles;
    }
    
    /**
     * 检查角色是否存在
     */
    public function roleExists($roleSlug) {
        // 检查系统角色
        if (isset($this->config['roles'][$roleSlug])) {
            return true;
        }
        
        // 检查自定义角色
        return $this->customRoleExists($roleSlug);
    }
    
    /**
     * 验证角色数据
     */
    private function validateRoleData($data, $excludeSlug = null) {
        $errors = [];
        
        // 验证必填字段
        $requiredFields = ['name', 'description', 'level', 'permissions'];
        foreach ($requiredFields as $field) {
            if (empty($data[$field])) {
                $errors[] = "Field '{$field}' is required";
            }
        }
        
        // 验证角色名称
        if (isset($data['name'])) {
            if (strlen($data['name']) < 2 || strlen($data['name']) > 50) {
                $errors[] = 'Role name must be between 2 and 50 characters';
            }
            
            if (!preg_match('/^[a-zA-Z0-9\s\-_]+$/', $data['name'])) {
                $errors[] = 'Role name contains invalid characters';
            }
        }
        
        // 验证权限级别
        if (isset($data['level'])) {
            if (!is_numeric($data['level']) || $data['level'] < 1 || $data['level'] > 99) {
                $errors[] = 'Permission level must be between 1 and 99';
            }
        }
        
        // 验证权限列表
        if (isset($data['permissions'])) {
            if (!is_array($data['permissions'])) {
                $errors[] = 'Permissions must be an array';
            } else {
                foreach ($data['permissions'] as $permission) {
                    if (!$this->permissionManager->validatePermissionFormat($permission) && $permission !== '*') {
                        $errors[] = "Invalid permission format: {$permission}";
                    }
                }
            }
        }
        
        // 验证颜色代码
        if (isset($data['color']) && !empty($data['color'])) {
            if (!preg_match('/^#[0-9A-Fa-f]{6}$/', $data['color'])) {
                $errors[] = 'Invalid color format (must be hex color code)';
            }
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }
    
    /**
     * 分配角色给用户
     */
    public function assignRole($userId, $roleSlug, $assigner) {
        // 验证分配者权限
        if (!$this->permissionManager->hasPermission($assigner, 'admins.edit')) {
            throw new Exception('Insufficient permissions to assign roles');
        }
        
        // 检查角色是否存在
        if (!$this->roleExists($roleSlug)) {
            throw new Exception('Role not found');
        }
        
        // 检查是否可以分配此角色
        $assignableRoles = $this->permissionManager->getAssignableRoles($assigner);
        if (!isset($assignableRoles[$roleSlug])) {
            throw new Exception('Cannot assign this role: insufficient permissions');
        }
        
        // 获取用户信息
        $user = $this->getUserById($userId);
        if (!$user) {
            throw new Exception('User not found');
        }
        
        // 检查是否可以管理此用户
        if (!$this->permissionManager->canManageUser($assigner, $user)) {
            throw new Exception('Cannot manage this user');
        }
        
        $oldRole = $user['role'];
        
        // 更新用户角色（这里应该更新数据库）
        $this->updateUserRole($userId, $roleSlug);
        
        // 清除用户权限缓存
        $this->permissionManager->clearUserPermissionCache($userId);
        
        // 记录操作日志
        $this->logRoleAssignment($userId, $oldRole, $roleSlug, $assigner);
        
        return [
            'success' => true,
            'message' => 'Role assigned successfully'
        ];
    }
    
    /**
     * 获取角色统计信息
     */
    public function getRoleStatistics() {
        $cacheKey = "role_statistics";
        
        if ($this->cache) {
            $cached = $this->cache->get($cacheKey);
            if ($cached !== false) {
                return json_decode($cached, true);
            }
        }
        
        $stats = [
            'total_roles' => 0,
            'system_roles' => 0,
            'custom_roles' => 0,
            'active_roles' => 0,
            'users_by_role' => [],
            'permission_distribution' => []
        ];
        
        $allRoles = $this->getAllRoles(true);
        $stats['total_roles'] = count($allRoles);
        
        foreach ($allRoles as $slug => $role) {
            if ($role['is_system']) {
                $stats['system_roles']++;
            } else {
                $stats['custom_roles']++;
            }
            
            if ($role['is_active'] ?? true) {
                $stats['active_roles']++;
            }
            
            // 统计用户数量
            $userCount = $this->getUserCountByRole($slug);
            $stats['users_by_role'][$slug] = [
                'name' => $role['name'],
                'count' => $userCount,
                'level' => $role['level']
            ];
            
            // 统计权限分布
            $permissionCount = count($role['permissions']);
            if (!isset($stats['permission_distribution'][$permissionCount])) {
                $stats['permission_distribution'][$permissionCount] = 0;
            }
            $stats['permission_distribution'][$permissionCount]++;
        }
        
        if ($this->cache) {
            $this->cache->setex($cacheKey, 900, json_encode($stats)); // 缓存15分钟
        }
        
        return $stats;
    }
    
    /**
     * 清除角色缓存
     */
    private function clearRoleCache() {
        if (!$this->cache) {
            return;
        }
        
        $patterns = ['role_*', 'all_roles*', 'role_statistics'];
        
        foreach ($patterns as $pattern) {
            $keys = $this->cache->keys($pattern);
            if (!empty($keys)) {
                $this->cache->del($keys);
            }
        }
    }
    
    /**
     * 清除使用特定角色的用户权限缓存
     */
    private function clearUsersPermissionCache($roleSlug) {
        $users = $this->getUsersWithRole($roleSlug);
        foreach ($users as $user) {
            $this->permissionManager->clearUserPermissionCache($user['id']);
        }
    }
    
    /**
     * 记录角色操作日志
     */
    private function logRoleOperation($operation, $roleSlug, $operator, $newData = null, $oldData = null) {
        $logData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'operation' => $operation,
            'role_slug' => $roleSlug,
            'operator_id' => $operator['id'],
            'operator_username' => $operator['username'],
            'operator_role' => $operator['role'],
            'new_data' => $newData,
            'old_data' => $oldData,
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
        ];
        
        $logFile = '/var/log/prompt2tool/role_operations.log';
        $logDir = dirname($logFile);
        
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        $logLine = json_encode($logData, JSON_UNESCAPED_UNICODE) . "\n";
        file_put_contents($logFile, $logLine, FILE_APPEND | LOCK_EX);
    }
    
    /**
     * 记录角色分配日志
     */
    private function logRoleAssignment($userId, $oldRole, $newRole, $assigner) {
        $logData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'operation' => 'role_assignment',
            'user_id' => $userId,
            'old_role' => $oldRole,
            'new_role' => $newRole,
            'assigner_id' => $assigner['id'],
            'assigner_username' => $assigner['username'],
            'assigner_role' => $assigner['role'],
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
        ];
        
        $logFile = '/var/log/prompt2tool/role_assignments.log';
        $logDir = dirname($logFile);
        
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        $logLine = json_encode($logData, JSON_UNESCAPED_UNICODE) . "\n";
        file_put_contents($logFile, $logLine, FILE_APPEND | LOCK_EX);
    }
    
    // 以下方法需要根据实际数据库实现
    
    /**
     * 保存角色到数据库（需要实现）
     */
    private function saveRoleToDatabase($slug, $roleData) {
        // TODO: 实现数据库保存逻辑
        // 这里应该将角色数据保存到数据库
    }
    
    /**
     * 从数据库获取角色（需要实现）
     */
    private function getRoleFromDatabase($slug) {
        // TODO: 实现数据库查询逻辑
        return null;
    }
    
    /**
     * 从数据库获取自定义角色（需要实现）
     */
    private function getCustomRolesFromDatabase($includeInactive = false) {
        // TODO: 实现数据库查询逻辑
        return [];
    }
    
    /**
     * 检查自定义角色是否存在（需要实现）
     */
    private function customRoleExists($slug) {
        // TODO: 实现数据库查询逻辑
        return false;
    }
    
    /**
     * 从数据库删除角色（需要实现）
     */
    private function deleteRoleFromDatabase($slug) {
        // TODO: 实现数据库删除逻辑
    }
    
    /**
     * 获取使用特定角色的用户（需要实现）
     */
    private function getUsersWithRole($roleSlug) {
        // TODO: 实现数据库查询逻辑
        return [];
    }
    
    /**
     * 根据ID获取用户（需要实现）
     */
    private function getUserById($userId) {
        // TODO: 实现数据库查询逻辑
        return null;
    }
    
    /**
     * 更新用户角色（需要实现）
     */
    private function updateUserRole($userId, $roleSlug) {
        // TODO: 实现数据库更新逻辑
    }
    
    /**
     * 获取角色的用户数量（需要实现）
     */
    private function getUserCountByRole($roleSlug) {
        // TODO: 实现数据库查询逻辑
        return 0;
    }
}
?>
