<?php
/**
 * Chat Completion API Endpoint
 * 处理与aiHubMix平台的聊天完成请求
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

// 使用统一的数据库连接
require_once dirname(__DIR__) . '/includes/database-connection.php';

try {
    // 获取请求数据
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);
    
    if (!$data) {
        throw new Exception('Invalid JSON data');
    }
    
    // 验证必需参数
    if (!isset($data['model']) || !isset($data['messages'])) {
        throw new Exception('Missing required parameters: model and messages');
    }
    
    // 获取aiHubMix平台配置
    $stmt = $pdo->prepare("SELECT id FROM pt_service_platform WHERE code = 'aihubmix' AND is_active = 1");
    $stmt->execute();
    $aihubmixPlatform = $stmt->fetch();
    
    if (!$aihubmixPlatform) {
        throw new Exception('AiHubMix platform not found or inactive');
    }
    
    $platformId = $aihubmixPlatform['id'];
    
    // 验证模型
    $stmt = $pdo->prepare("
        SELECT model_code, model_name, max_tokens 
        FROM pt_service_model 
        WHERE platform_id = ? AND model_code = ? AND is_active = 1
    ");
    $stmt->execute([$platformId, $data['model']]);
    $modelInfo = $stmt->fetch();
    
    if (!$modelInfo) {
        throw new Exception('Model not found or inactive: ' . $data['model']);
    }
    
    // 获取API密钥
    $stmt = $pdo->prepare("
        SELECT api_key 
        FROM pt_service_key 
        WHERE platform_id = ? AND is_active = 1 
        ORDER BY id ASC 
        LIMIT 1
    ");
    $stmt->execute([$platformId]);
    $apiKeyData = $stmt->fetch();
    
    if (!$apiKeyData) {
        throw new Exception('No active API key found for AiHubMix');
    }
    
    // 获取APP-Code配置
    $stmt = $pdo->prepare("
        SELECT config_value 
        FROM pt_service_config 
        WHERE platform_id = ? AND config_key = 'app_code'
    ");
    $stmt->execute([$platformId]);
    $appCodeData = $stmt->fetch();
    
    $appCode = $appCodeData ? $appCodeData['config_value'] : 'KXTM3281';
    
    // 构建请求数据
    $requestData = [
        'model' => $data['model'],
        'messages' => $data['messages'],
        'temperature' => $data['temperature'] ?? 0.7,
        'max_tokens' => min($data['max_tokens'] ?? 1000, $modelInfo['max_tokens']),
        'stream' => $data['stream'] ?? false
    ];
    
    // APP-Code将在headers中添加，不放在请求body中
    
    // 准备cURL请求
    $ch = curl_init();
    
    curl_setopt_array($ch, [
        CURLOPT_URL => 'https://api.aihubmix.com/v1/chat/completions',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => json_encode($requestData),
        CURLOPT_HTTPHEADER => [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $apiKeyData['api_key'],
            'APP-Code: ' . (!empty($appCode) ? $appCode : 'KXTM3281')
        ],
        CURLOPT_TIMEOUT => 60,
        CURLOPT_CONNECTTIMEOUT => 10,
        CURLOPT_SSL_VERIFYPEER => true,
        CURLOPT_USERAGENT => 'Prompt2Tool/1.0'
    ]);
    
    // 如果是流式响应，设置流式处理
    if ($data['stream'] ?? false) {
        curl_setopt($ch, CURLOPT_WRITEFUNCTION, function($curl, $chunk) {
            // $curl parameter is required by cURL callback but not used
            echo $chunk;
            ob_flush();
            flush();
            return strlen($chunk);
        });
        
        // 设置流式响应头
        header('Content-Type: text/event-stream');
        header('Cache-Control: no-cache');
        header('Connection: keep-alive');
    }
    
    // 执行请求
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    
    curl_close($ch);
    
    // 检查cURL错误
    if ($error) {
        throw new Exception('cURL error: ' . $error);
    }
    
    // 检查HTTP状态码
    if ($httpCode !== 200) {
        $errorResponse = json_decode($response, true);
        $errorMessage = $errorResponse['error']['message'] ?? 'Unknown API error';
        throw new Exception('API error (HTTP ' . $httpCode . '): ' . $errorMessage);
    }
    
    // 如果不是流式响应，直接返回结果
    if (!($data['stream'] ?? false)) {
        $responseData = json_decode($response, true);
        
        if (!$responseData) {
            throw new Exception('Invalid response from API');
        }
        
        // 记录API使用情况（可选）
        try {
            // 这里暂时注释掉，因为这个API不直接关联用户
            // 如果需要记录，需要先获取用户ID
            /*
            $stmt = $pdo->prepare("
                INSERT INTO pt_api_usage (user_id, action, quota_consumed, created_at)
                VALUES (?, ?, ?, NOW())
            ");
            $stmt->execute([$userId, 'api_call', 1]);
            */
        } catch (Exception $e) {
            // 记录失败不影响主要功能
            error_log('Failed to log API usage: ' . $e->getMessage());
        }
        
        // 返回响应
        echo json_encode($responseData);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'error' => [
            'message' => $e->getMessage(),
            'type' => 'internal_error'
        ]
    ]);
    
    // 记录错误日志
    error_log('Chat completion API error: ' . $e->getMessage());
}
?>
