<?php
/**
 * 查看单个联系表单消息
 */

// 检查用户权限
if (!isset($_SESSION['admin_logged_in']) || !$_SESSION['admin_logged_in']) {
    header('Location: login.php');
    exit;
}

$message_id = $_GET['id'] ?? '';
if (!$message_id) {
    header('Location: ?page=messages');
    exit;
}

// 获取消息详情
try {
    $stmt = $pdo->prepare("SELECT * FROM pt_contact_message WHERE id = ?");
    $stmt->execute([$message_id]);
    $message = $stmt->fetch();
    
    if (!$message) {
        header('Location: ?page=messages');
        exit;
    }
    
    // 标记为已读
    if ($message['status'] === 'unread') {
        $stmt = $pdo->prepare("UPDATE pt_contact_message SET status = 'read' WHERE id = ?");
        $stmt->execute([$message_id]);
        $message['status'] = 'read';
    }
    
} catch (Exception $e) {
    $error_message = "Error loading message: " . $e->getMessage();
}

// 处理操作
$action = $_POST['action'] ?? $_GET['action'] ?? '';

if ($action) {
    try {
        switch ($action) {
            case 'add_note':
                $admin_notes = trim($_POST['admin_notes'] ?? '');
                $stmt = $pdo->prepare("UPDATE pt_contact_message SET admin_notes = ? WHERE id = ?");
                $stmt->execute([$admin_notes, $message_id]);
                $success_message = "Notes saved successfully";
                $message['admin_notes'] = $admin_notes;
                break;

            case 'archive':
                $stmt = $pdo->prepare("UPDATE pt_contact_message SET status = 'archived' WHERE id = ?");
                $stmt->execute([$message_id]);
                $success_message = "Message archived successfully";
                $message['status'] = 'archived';
                break;

            case 'delete':
                $stmt = $pdo->prepare("DELETE FROM pt_contact_message WHERE id = ?");
                $stmt->execute([$message_id]);
                header('Location: ?page=messages&deleted=1');
                exit;
        }
    } catch (Exception $e) {
        $error_message = "Error: " . $e->getMessage();
    }
}
?>

<div class="space-y-6">
    <!-- 返回按钮 -->
    <div class="flex items-center space-x-4">
        <a href="?page=messages" class="flex items-center text-gray-600 hover:text-gray-900">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
            Back to Messages
        </a>
        
        <div class="flex-1">
            <h1 class="text-2xl font-bold text-gray-900">Message Details</h1>
        </div>
        
        <!-- 状态标签 -->
        <span class="inline-flex px-3 py-1 text-sm font-semibold leading-5 
            <?php
            switch($message['status']) {
                case 'unread': echo 'bg-red-100 text-red-800'; break;
                case 'read': echo 'bg-blue-100 text-blue-800'; break;
                case 'replied': echo 'bg-green-100 text-green-800'; break;
                case 'archived': echo 'bg-gray-100 text-gray-800'; break;
                default: echo 'bg-gray-100 text-gray-800';
            }
            ?>">
            <?= ucfirst($message['status']) ?>
        </span>
    </div>

    <!-- 成功/错误消息 -->
    <?php if (isset($success_message)): ?>
    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3">
        <?= htmlspecialchars($success_message) ?>
    </div>
    <?php endif; ?>

    <?php if (isset($error_message)): ?>
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3">
        <?= htmlspecialchars($error_message) ?>
    </div>
    <?php endif; ?>

    <!-- 消息详情 -->
    <div class="bg-white border border-gray-200 p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <!-- 联系人信息 -->
            <div>
                <h3 class="text-lg font-medium text-gray-900 mb-4">Contact Information</h3>
                <dl class="space-y-3">
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Name</dt>
                        <dd class="text-sm text-gray-900"><?= htmlspecialchars($message['name']) ?></dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Email</dt>
                        <dd class="text-sm text-gray-900">
                            <a href="mailto:<?= htmlspecialchars($message['email']) ?>" class="text-blue-600 hover:text-blue-800">
                                <?= htmlspecialchars($message['email']) ?>
                            </a>
                        </dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Subject</dt>
                        <dd class="text-sm text-gray-900"><?= htmlspecialchars($message['subject']) ?></dd>
                    </div>
                </dl>
            </div>

            <!-- 技术信息 -->
            <div>
                <h3 class="text-lg font-medium text-gray-900 mb-4">Technical Information</h3>
                <dl class="space-y-3">
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Submitted</dt>
                        <dd class="text-sm text-gray-900"><?= date('F j, Y g:i A', strtotime($message['created_at'])) ?></dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">IP Address</dt>
                        <dd class="text-sm text-gray-900"><?= htmlspecialchars($message['ip_address'] ?? 'N/A') ?></dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">User Agent</dt>
                        <dd class="text-sm text-gray-900 break-all"><?= htmlspecialchars(substr($message['user_agent'] ?? 'N/A', 0, 100)) ?></dd>
                    </div>
                </dl>
            </div>
        </div>

        <!-- 消息内容 -->
        <div class="border-t border-gray-200 pt-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Message</h3>
            <div class="bg-gray-50 p-4 border border-gray-200">
                <p class="text-gray-900 whitespace-pre-wrap"><?= htmlspecialchars($message['message']) ?></p>
            </div>
        </div>
    </div>

    <!-- 管理员回复 -->
    <div class="bg-white border border-gray-200 p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Admin Notes</h3>

        <form method="POST" class="space-y-4">
            <input type="hidden" name="action" value="add_note">

            <div>
                <label for="admin_notes" class="block text-sm font-medium text-gray-700 mb-2">
                    Internal Notes
                </label>
                <textarea
                    id="admin_notes"
                    name="admin_notes"
                    rows="6"
                    class="w-full px-3 py-2 border border-gray-300 focus:outline-none focus:border-accent"
                    placeholder="Add internal notes about this message..."
                ><?= htmlspecialchars($message['admin_notes'] ?? '') ?></textarea>
                <p class="mt-1 text-sm text-gray-500">
                    These notes are for internal use only and will not be sent to the user.
                </p>
            </div>

            <div class="flex justify-between items-center">
                <div class="flex space-x-4">
                    <button type="submit" class="bg-accent text-white px-6 py-2 hover:bg-blue-700 hover:text-white">
                        Save Notes
                    </button>

                    <a href="mailto:<?= htmlspecialchars($message['email']) ?>?subject=Re: <?= htmlspecialchars($message['subject']) ?>"
                       class="bg-blue-600 text-white px-6 py-2 hover:bg-blue-700">
                        Send Email Reply
                    </a>
                </div>

                <!-- 快速操作 -->
                <div class="flex space-x-2">
                    <?php if ($message['status'] !== 'archived'): ?>
                    <a href="?page=messages&action=archive&id=<?= $message['id'] ?>" 
                       class="bg-gray-500 text-white px-4 py-2 hover:bg-gray-600">
                        Archive
                    </a>
                    <?php endif; ?>
                    
                    <a href="?page=messages&action=delete&id=<?= $message['id'] ?>" 
                       class="bg-red-600 text-white px-4 py-2 hover:bg-red-700"
                       onclick="return confirm('Are you sure you want to delete this message?')">
                        Delete
                    </a>
                </div>
            </div>
        </form>
    </div>

    <!-- 相关消息 -->
    <?php
    // 查找同一邮箱的其他消息
    $stmt = $pdo->prepare("SELECT id, subject, status, created_at FROM pt_contact_message WHERE email = ? AND id != ? ORDER BY created_at DESC LIMIT 5");
    $stmt->execute([$message['email'], $message_id]);
    $related_messages = $stmt->fetchAll();
    ?>

    <?php if (!empty($related_messages)): ?>
    <div class="bg-white border border-gray-200 p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Other Messages from This Contact</h3>
        <div class="space-y-3">
            <?php foreach ($related_messages as $related): ?>
            <div class="flex items-center justify-between p-3 bg-gray-50 border border-gray-200">
                <div>
                    <div class="text-sm font-medium text-gray-900"><?= htmlspecialchars($related['subject']) ?></div>
                    <div class="text-sm text-gray-500"><?= date('M j, Y', strtotime($related['created_at'])) ?></div>
                </div>
                <div class="flex items-center space-x-3">
                    <span class="inline-flex px-2 py-1 text-xs font-semibold leading-4 
                        <?php
                        switch($related['status']) {
                            case 'unread': echo 'bg-red-100 text-red-800'; break;
                            case 'read': echo 'bg-blue-100 text-blue-800'; break;
                            case 'replied': echo 'bg-green-100 text-green-800'; break;
                            case 'archived': echo 'bg-gray-100 text-gray-800'; break;
                            default: echo 'bg-gray-100 text-gray-800';
                        }
                        ?>">
                        <?= ucfirst($related['status']) ?>
                    </span>
                    <a href="?page=message-view&id=<?= $related['id'] ?>" class="text-blue-600 hover:text-blue-800 text-sm">
                        View
                    </a>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
    <?php endif; ?>
</div>
