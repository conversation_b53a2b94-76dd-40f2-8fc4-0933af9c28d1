<?php
/**
 * 工具分类管理页面
 */

// 检查用户权限
if (!isset($_SESSION['admin_logged_in']) || !$_SESSION['admin_logged_in']) {
    header('Location: login.php');
    exit;
}

// 使用统一的数据库连接
require_once ROOT_PATH . '/includes/database-connection.php';

// 处理操作
$action = $_GET['action'] ?? '';
$category_id = $_GET['id'] ?? '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        switch ($_POST['action']) {
            case 'create':
                $stmt = $pdo->prepare("
                    INSERT INTO pt_tool_category (name, slug, description, icon, sort_order, status)
                    VALUES (?, ?, ?, ?, ?, 'active')
                ");
                $stmt->execute([
                    $_POST['name'],
                    $_POST['slug'],
                    $_POST['description'],
                    $_POST['icon'],
                    intval($_POST['sort_order'])
                ]);
                $success_message = "Category created successfully";
                break;
                
            case 'update':
                $stmt = $pdo->prepare("
                    UPDATE pt_tool_category
                    SET name = ?, slug = ?, description = ?, icon = ?, sort_order = ?, status = ?
                    WHERE id = ?
                ");
                $stmt->execute([
                    $_POST['name'],
                    $_POST['slug'],
                    $_POST['description'],
                    $_POST['icon'],
                    intval($_POST['sort_order']),
                    $_POST['status'],
                    intval($_POST['id'])
                ]);
                $success_message = "Category updated successfully";
                break;
                
            case 'delete':
                // 检查是否有工具使用此分类
                $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM pt_tool WHERE category_id = ?");
                $stmt->execute([intval($_POST['id'])]);
                $tool_count = $stmt->fetch()['count'];
                
                if ($tool_count > 0) {
                    $error_message = "Cannot delete category: {$tool_count} tools are using this category";
                } else {
                    $stmt = $pdo->prepare("DELETE FROM pt_tool_category WHERE id = ?");
                    $stmt->execute([intval($_POST['id'])]);
                    $success_message = "Category deleted successfully";
                }
                break;
        }
    } catch (Exception $e) {
        $error_message = "Error: " . $e->getMessage();
    }
}

// 分页设置
$page = isset($_GET['cat_page']) ? max(1, intval($_GET['cat_page'])) : 1;
$per_page = 10;
$offset = ($page - 1) * $per_page;

// 获取总分类数
$count_stmt = $pdo->query("SELECT COUNT(*) as total FROM pt_tool_category");
$total_categories = $count_stmt->fetch()['total'];
$total_pages = ceil($total_categories / $per_page);

// 获取当前页的分类
$stmt = $pdo->prepare("
    SELECT tc.*,
           COUNT(t.id) as tool_count
    FROM pt_tool_category tc
    LEFT JOIN pt_tool t ON tc.id = t.category_id
    GROUP BY tc.id
    ORDER BY tc.sort_order, tc.name
    LIMIT ? OFFSET ?
");
$stmt->execute([$per_page, $offset]);
$categories = $stmt->fetchAll();

// 获取编辑的分类
$edit_category = null;
if ($action === 'edit' && $category_id) {
    $stmt = $pdo->prepare("SELECT * FROM pt_tool_category WHERE id = ?");
    $stmt->execute([$category_id]);
    $edit_category = $stmt->fetch();
}
?>

<div class="space-y-6">
    <!-- 页面操作 -->
    <div class="flex justify-between items-center">
        <div>
            <p class="text-gray-600">Manage tool categories and their organization</p>
        </div>

        <button onclick="openCreateModal()" class="bg-accent text-white px-4 py-2 hover:bg-blue-700 hover:text-white">
            Add Category
        </button>
    </div>

    <!-- 成功/错误消息 -->
    <?php if (isset($success_message)): ?>
    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3">
        <?= htmlspecialchars($success_message) ?>
    </div>
    <?php endif; ?>

    <?php if (isset($error_message)): ?>
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3">
        <?= htmlspecialchars($error_message) ?>
    </div>
    <?php endif; ?>

    <!-- 分类列表 -->
    <div class="bg-white border border-gray-200">
        <div class="overflow-x-auto">
            <table class="w-full">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Slug</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tools</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php foreach ($categories as $category): ?>
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-10 w-10">
                                    <div class="h-10 w-10 flex items-center justify-center text-2xl bg-gray-100 rounded-lg">
                                        <?= htmlspecialchars($category['icon'] ?? '📁') ?>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <div class="text-sm font-medium text-gray-900"><?= htmlspecialchars($category['name']) ?></div>
                                    <div class="text-sm text-gray-500"><?= htmlspecialchars($category['description']) ?></div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 text-sm text-gray-900">
                            <code class="bg-gray-100 px-2 py-1"><?= htmlspecialchars($category['slug']) ?></code>
                        </td>
                        <td class="px-6 py-4 text-sm text-gray-900">
                            <span class="inline-flex px-2 py-1 text-xs font-semibold bg-blue-100 text-blue-800">
                                <?= $category['tool_count'] ?> tools
                            </span>
                        </td>
                        <td class="px-6 py-4 text-sm text-gray-900">
                            <?= $category['sort_order'] ?>
                        </td>
                        <td class="px-6 py-4">
                            <span class="inline-flex px-2 py-1 text-xs font-semibold leading-5 
                                <?= $category['status'] === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' ?>">
                                <?= ucfirst($category['status']) ?>
                            </span>
                        </td>
                        <td class="px-6 py-4 text-sm font-medium">
                            <div class="flex space-x-2">
                                <button onclick="editCategory(<?= htmlspecialchars(json_encode($category)) ?>)" 
                                        class="text-blue-600 hover:text-blue-900">Edit</button>
                                
                                <button onclick="deleteCategory(<?= $category['id'] ?>, '<?= htmlspecialchars($category['name']) ?>', <?= $category['tool_count'] ?>)" 
                                        class="text-red-600 hover:text-red-900">Delete</button>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>

        <!-- 分页导航 -->
        <?php if ($total_pages > 1): ?>
        <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
            <div class="flex-1 flex justify-between sm:hidden">
                <!-- 移动端分页 -->
                <?php if ($page > 1): ?>
                    <a href="?page=tool-categories&cat_page=<?= $page - 1 ?>" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        Previous
                    </a>
                <?php endif; ?>

                <?php if ($page < $total_pages): ?>
                    <a href="?page=tool-categories&cat_page=<?= $page + 1 ?>" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        Next
                    </a>
                <?php endif; ?>
            </div>

            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                    <p class="text-sm text-gray-700">
                        Showing <span class="font-medium"><?= $offset + 1 ?></span> to <span class="font-medium"><?= min($offset + $per_page, $total_categories) ?></span> of <span class="font-medium"><?= $total_categories ?></span> categories
                    </p>
                </div>

                <div>
                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                        <!-- 上一页 -->
                        <?php if ($page > 1): ?>
                            <a href="?page=tool-categories&cat_page=<?= $page - 1 ?>" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                <span class="sr-only">Previous</span>
                                <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                    <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                                </svg>
                            </a>
                        <?php else: ?>
                            <span class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-gray-100 text-sm font-medium text-gray-400">
                                <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                    <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                                </svg>
                            </span>
                        <?php endif; ?>

                        <!-- 页码 -->
                        <?php
                        $start_page = max(1, $page - 2);
                        $end_page = min($total_pages, $page + 2);

                        if ($start_page > 1): ?>
                            <a href="?page=tool-categories&cat_page=1" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">1</a>
                            <?php if ($start_page > 2): ?>
                                <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">...</span>
                            <?php endif; ?>
                        <?php endif; ?>

                        <?php for ($i = $start_page; $i <= $end_page; $i++): ?>
                            <?php if ($i == $page): ?>
                                <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600"><?= $i ?></span>
                            <?php else: ?>
                                <a href="?page=tool-categories&cat_page=<?= $i ?>" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50"><?= $i ?></a>
                            <?php endif; ?>
                        <?php endfor; ?>

                        <?php if ($end_page < $total_pages): ?>
                            <?php if ($end_page < $total_pages - 1): ?>
                                <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">...</span>
                            <?php endif; ?>
                            <a href="?page=tool-categories&cat_page=<?= $total_pages ?>" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50"><?= $total_pages ?></a>
                        <?php endif; ?>

                        <!-- 下一页 -->
                        <?php if ($page < $total_pages): ?>
                            <a href="?page=tool-categories&cat_page=<?= $page + 1 ?>" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                <span class="sr-only">Next</span>
                                <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                                </svg>
                            </a>
                        <?php else: ?>
                            <span class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-gray-100 text-sm font-medium text-gray-400">
                                <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                                </svg>
                            </span>
                        <?php endif; ?>
                    </nav>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- 创建/编辑分类模态框 -->
<div id="categoryModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-10 mx-auto p-6 border max-w-2xl shadow-lg bg-white rounded-lg">
        <div class="mt-3">
            <h3 id="modalTitle" class="text-xl font-semibold text-gray-900 mb-6 flex items-center">
                <i class="fas fa-folder-plus mr-3 text-blue-500"></i>
                Add Category
            </h3>
            
            <form id="categoryForm" method="POST">
                <input type="hidden" name="action" id="formAction" value="create">
                <input type="hidden" name="id" id="categoryId">
                
                <!-- 第一行 -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-tag mr-1 text-gray-400"></i>Name
                        </label>
                        <div class="relative">
                            <input type="text" name="name" id="categoryName" required
                                   class="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent pr-10"
                                   placeholder="Enter category name">
                            <button type="button" onclick="getCategoryAISuggestion('name')"
                                    class="absolute right-2 top-1/2 transform -translate-y-1/2 text-blue-500 hover:text-blue-700 transition-colors"
                                    title="AI Suggestions">
                                <i class="fas fa-robot text-sm"></i>
                            </button>
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-link mr-1 text-gray-400"></i>Slug *
                        </label>
                        <div class="relative">
                            <input type="text" name="slug" id="categorySlug" required
                                   class="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent pr-16"
                                   placeholder="auto-generated-slug"
                                   oninput="checkCategorySlugAvailability(this.value)">
                            <div class="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center space-x-1">
                                <button type="button" onclick="getCategoryAISuggestion('slug')"
                                        class="text-blue-500 hover:text-blue-700 transition-colors"
                                        title="AI Suggestions">
                                    <i class="fas fa-robot text-sm"></i>
                                </button>
                                <div id="slugStatus">
                                    <!-- Status icon will appear here -->
                                </div>
                            </div>
                        </div>
                        <div id="slugMessage" class="text-xs mt-1 hidden">
                            <!-- Status message will appear here -->
                        </div>
                    </div>
                </div>

                <!-- 第二行 -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-icons mr-1 text-gray-400"></i>Icon (Emoji)
                        </label>
                        <div class="relative">
                            <input type="text" name="icon" id="categoryIcon"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent pr-10"
                                   placeholder="🛠️">
                            <button type="button" onclick="getCategoryAISuggestion('icon')"
                                    class="absolute right-2 top-1/2 transform -translate-y-1/2 text-blue-500 hover:text-blue-700 transition-colors"
                                    title="AI Suggestions">
                                <i class="fas fa-robot text-sm"></i>
                            </button>
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-sort-numeric-up mr-1 text-gray-400"></i>Sort Order
                        </label>
                        <input type="number" name="sort_order" id="categorySortOrder" value="1" min="1"
                               class="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                </div>

                <!-- 第三行 - 仅编辑时显示状态 -->
                <div id="statusField" class="hidden mb-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="fas fa-toggle-on mr-1 text-gray-400"></i>Status
                            </label>
                            <select name="status" id="categoryStatus"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option value="active">Active</option>
                                <option value="inactive">Inactive</option>
                            </select>
                        </div>
                        <div></div> <!-- 空占位 -->
                    </div>
                </div>

                <!-- 描述字段 - 全宽 -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2 flex items-center justify-between">
                        <span><i class="fas fa-align-left mr-1 text-gray-400"></i>Description</span>
                        <button type="button" onclick="getCategoryAISuggestion('description')"
                                class="text-blue-500 hover:text-blue-700 transition-colors text-sm"
                                title="AI Suggestions">
                            <i class="fas fa-robot mr-1"></i>AI Generate
                        </button>
                    </label>
                    <textarea name="description" id="categoryDescription" rows="4"
                              class="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                              placeholder="Enter category description..."></textarea>
                </div>
                
                <!-- 按钮区域 -->
                <div class="flex justify-end space-x-4 pt-4 border-t border-gray-200">
                    <button type="button" onclick="closeModal()"
                            class="px-6 py-3 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors duration-200">
                        <i class="fas fa-times mr-2"></i>Cancel
                    </button>
                    <button type="submit"
                            class="px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-200">
                        <i class="fas fa-save mr-2"></i>Save Category
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div id="deleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg bg-white">
        <div class="mt-3 text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 bg-red-100 mb-4">
                <svg class="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">Delete Category</h3>
            <p id="deleteMessage" class="text-sm text-gray-500 mb-4"></p>
            
            <form id="deleteForm" method="POST" class="inline">
                <input type="hidden" name="action" value="delete">
                <input type="hidden" name="id" id="deleteId">
                
                <div class="flex justify-center space-x-4">
                    <button type="button" onclick="closeDeleteModal()" 
                            class="px-4 py-2 bg-gray-300 text-gray-700 hover:bg-gray-400">
                        Cancel
                    </button>
                    <button type="submit" id="confirmDeleteBtn"
                            class="px-4 py-2 bg-red-600 text-white hover:bg-red-700">
                        Delete Category
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- AI建议模态框 -->
<div id="categoryAISuggestionsModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex justify-between items-center mb-4">
                <h3 id="categoryAIModalTitle" class="text-lg font-medium text-gray-900 flex items-center">
                    <i class="fas fa-robot mr-2 text-blue-500"></i>
                    AI Suggestions
                </h3>
                <button onclick="closeCategoryAIModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <!-- 加载状态 -->
            <div id="categoryAILoading" class="text-center py-8">
                <div class="inline-flex items-center">
                    <div class="w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mr-3"></div>
                    <span class="text-gray-600">Generating AI suggestions...</span>
                </div>
            </div>

            <!-- 建议内容 -->
            <div id="categoryAISuggestions" class="hidden">
                <div id="categoryAISuggestionsContent"></div>

                <div class="flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-200">
                    <button onclick="retryCategoryAISuggestion()"
                            class="px-4 py-2 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 flex items-center">
                        <i class="fas fa-redo mr-2"></i>Retry
                    </button>
                    <button onclick="closeCategoryAIModal()"
                            class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">
                        Cancel
                    </button>
                </div>
            </div>

            <!-- 错误状态 -->
            <div id="categoryAIError" class="hidden">
                <div class="bg-red-50 border border-red-200 rounded-md p-4">
                    <div class="flex">
                        <i class="fas fa-exclamation-triangle text-red-400 mr-3 mt-0.5"></i>
                        <div>
                            <h3 class="text-sm font-medium text-red-800">Error</h3>
                            <p id="categoryAIErrorMessage" class="text-sm text-red-700 mt-1"></p>
                        </div>
                    </div>
                </div>
                <div class="flex justify-end space-x-3 mt-4">
                    <button onclick="retryCategoryAISuggestion()"
                            class="px-4 py-2 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 flex items-center">
                        <i class="fas fa-redo mr-2"></i>Retry
                    </button>
                    <button onclick="closeCategoryAIModal()"
                            class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">
                        Close
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Slug检测相关变量
let slugCheckTimeout = null;
let currentCategoryId = null;

// 自动生成slug
document.getElementById('categoryName').addEventListener('input', function() {
    const name = this.value;
    const slug = name.toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim('-');
    document.getElementById('categorySlug').value = slug;

    // 触发slug检查
    if (slug) {
        checkCategorySlugAvailability(slug);
    }
});

// 检查分类slug可用性
function checkCategorySlugAvailability(slug) {
    const statusIcon = document.getElementById('slugStatus');
    const statusMessage = document.getElementById('slugMessage');

    // 清除之前的超时
    if (slugCheckTimeout) {
        clearTimeout(slugCheckTimeout);
    }

    // 如果slug为空，清除状态
    if (!slug.trim()) {
        statusIcon.innerHTML = '';
        statusMessage.classList.add('hidden');
        return;
    }

    // 显示检查中状态
    statusIcon.innerHTML = '<div class="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>';
    statusMessage.textContent = 'Checking availability...';
    statusMessage.className = 'text-xs mt-1 text-blue-600';

    // 延迟检查，避免频繁请求
    slugCheckTimeout = setTimeout(() => {
        const excludeId = currentCategoryId || '';

        fetch(`/api/check-category-slug.php?slug=${encodeURIComponent(slug)}&exclude_id=${excludeId}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.text().then(text => {
                    try {
                        return JSON.parse(text);
                    } catch (e) {
                        console.error('Invalid JSON response:', text);
                        throw new Error('Invalid JSON response from server');
                    }
                });
            })
            .then(data => {
                if (data.success) {
                    if (data.available) {
                        // 可用 - 绿色
                        statusIcon.innerHTML = '<svg class="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg>';
                        statusMessage.textContent = data.message;
                        statusMessage.className = 'text-xs mt-1 text-green-600';
                    } else {
                        // 不可用 - 红色
                        statusIcon.innerHTML = '<svg class="w-4 h-4 text-red-500" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>';
                        statusMessage.textContent = data.message;
                        statusMessage.className = 'text-xs mt-1 text-red-600';
                    }
                } else {
                    // 错误状态
                    statusIcon.innerHTML = '<svg class="w-4 h-4 text-yellow-500" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg>';
                    statusMessage.textContent = data.error || 'Error checking availability';
                    statusMessage.className = 'text-xs mt-1 text-yellow-600';
                }
            })
            .catch(error => {
                console.error('Slug check error:', error);
                statusIcon.innerHTML = '<svg class="w-4 h-4 text-red-500" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg>';
                statusMessage.textContent = 'Error checking availability';
                statusMessage.className = 'text-xs mt-1 text-red-600';
            });
    }, 500); // 500ms延迟
}



function openCreateModal() {
    document.getElementById('modalTitle').innerHTML = '<i class="fas fa-folder-plus mr-3 text-blue-500"></i>Add Category';
    document.getElementById('formAction').value = 'create';
    document.getElementById('statusField').classList.add('hidden');
    document.getElementById('categoryForm').reset();

    // 清除slug检查状态
    currentCategoryId = null;
    document.getElementById('slugStatus').innerHTML = '';
    document.getElementById('slugMessage').classList.add('hidden');

    document.getElementById('categoryModal').classList.remove('hidden');
}

function editCategory(category) {
    document.getElementById('modalTitle').innerHTML = '<i class="fas fa-edit mr-3 text-blue-500"></i>Edit Category';
    document.getElementById('formAction').value = 'update';
    document.getElementById('categoryId').value = category.id;
    document.getElementById('categoryName').value = category.name;
    document.getElementById('categorySlug').value = category.slug;
    document.getElementById('categoryDescription').value = category.description || '';
    document.getElementById('categoryIcon').value = category.icon || '';
    document.getElementById('categorySortOrder').value = category.sort_order || 1;
    document.getElementById('categoryStatus').value = category.status || 'active';
    document.getElementById('statusField').classList.remove('hidden');

    // 设置当前分类ID并检查slug
    currentCategoryId = category.id;
    if (category.slug) {
        checkCategorySlugAvailability(category.slug);
    }

    document.getElementById('categoryModal').classList.remove('hidden');
}

function deleteCategory(id, name, toolCount) {
    document.getElementById('deleteId').value = id;
    
    if (toolCount > 0) {
        document.getElementById('deleteMessage').textContent = 
            `Cannot delete "${name}" because it has ${toolCount} tools. Please move or delete the tools first.`;
        document.getElementById('confirmDeleteBtn').style.display = 'none';
    } else {
        document.getElementById('deleteMessage').textContent = 
            `Are you sure you want to delete "${name}"? This action cannot be undone.`;
        document.getElementById('confirmDeleteBtn').style.display = 'inline-block';
    }
    
    document.getElementById('deleteModal').classList.remove('hidden');
}

function closeModal() {
    // 清除slug检查状态
    currentCategoryId = null;
    document.getElementById('slugStatus').innerHTML = '';
    document.getElementById('slugMessage').classList.add('hidden');

    document.getElementById('categoryModal').classList.add('hidden');
}

function closeDeleteModal() {
    document.getElementById('deleteModal').classList.add('hidden');
}

// 分类AI建议功能
let currentCategoryAIType = '';

// 获取分类AI建议
async function getCategoryAISuggestion(type) {
    currentCategoryAIType = type;

    // 显示模态框
    document.getElementById('categoryAISuggestionsModal').classList.remove('hidden');
    document.getElementById('categoryAILoading').classList.remove('hidden');
    document.getElementById('categoryAISuggestions').classList.add('hidden');
    document.getElementById('categoryAIError').classList.add('hidden');

    // 更新标题
    const titles = {
        'name': 'Category Name Suggestions',
        'slug': 'Category Slug Suggestions',
        'description': 'Category Description Suggestions',
        'icon': 'Category Icon Suggestions'
    };
    document.getElementById('categoryAIModalTitle').innerHTML = `<i class="fas fa-robot mr-2 text-blue-500"></i>${titles[type]}`;

    try {
        // 收集当前表单数据
        const data = collectCategoryFormData(type);

        // 调用AI API
        const response = await fetch('/api/ai-suggestions.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                type: type,
                data: data
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();

        if (result.success) {
            displayCategoryAISuggestions(type, result.suggestions);
        } else {
            throw new Error(result.error || 'Failed to generate suggestions');
        }

    } catch (error) {
        console.error('Category AI suggestion error:', error);
        showCategoryAIError(error.message);
    } finally {
        document.getElementById('categoryAILoading').classList.add('hidden');
    }
}

// 收集分类表单数据
function collectCategoryFormData(type) {
    const categoryName = document.getElementById('categoryName').value;
    const description = document.getElementById('categoryDescription').value;

    const data = {
        tool_name: categoryName, // 使用分类名称作为工具名称
        description: description,
        category: 'Category' // 固定为分类类型
    };

    // 根据类型添加特定数据
    switch (type) {
        case 'slug':
            // 获取现有slugs用于避免重复
            data.existing_slugs = []; // 这里可以从页面获取现有slugs
            break;
        case 'name':
            data.current_name = categoryName;
            break;
    }

    return data;
}

// 显示分类AI建议
function displayCategoryAISuggestions(type, suggestions) {
    const content = document.getElementById('categoryAISuggestionsContent');

    switch (type) {
        case 'slug':
            if (Array.isArray(suggestions)) {
                content.innerHTML = `
                    <div class="space-y-3">
                        <p class="text-sm text-gray-600 mb-4">Choose a slug from the AI suggestions:</p>
                        ${suggestions.map((slug, index) => `
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer"
                                 onclick="selectCategorySuggestion('slug', '${slug}')">
                                <div class="flex items-center space-x-3">
                                    <span class="inline-flex items-center justify-center w-6 h-6 bg-blue-100 text-blue-600 text-xs font-medium rounded-full">
                                        ${index + 1}
                                    </span>
                                    <span class="font-mono text-gray-900">${slug}</span>
                                </div>
                                <button class="text-blue-600 hover:text-blue-800">
                                    <i class="fas fa-check"></i>
                                </button>
                            </div>
                        `).join('')}
                    </div>
                `;
            }
            break;

        case 'description':
            if (suggestions.concise || suggestions.detailed || suggestions.marketing) {
                content.innerHTML = `
                    <div class="space-y-4">
                        <p class="text-sm text-gray-600 mb-4">Choose a description style:</p>
                        ${suggestions.concise ? `
                            <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 cursor-pointer"
                                 onclick="selectCategorySuggestion('description', \`${suggestions.concise.replace(/`/g, '\\`')}\`)" >
                                <h4 class="font-medium text-gray-800 mb-2">Concise</h4>
                                <p class="text-gray-600">${suggestions.concise}</p>
                            </div>
                        ` : ''}
                        ${suggestions.detailed ? `
                            <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 cursor-pointer"
                                 onclick="selectCategorySuggestion('description', \`${suggestions.detailed.replace(/`/g, '\\`')}\`)" >
                                <h4 class="font-medium text-gray-800 mb-2">Detailed</h4>
                                <p class="text-gray-600">${suggestions.detailed}</p>
                            </div>
                        ` : ''}
                        ${suggestions.marketing ? `
                            <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 cursor-pointer"
                                 onclick="selectCategorySuggestion('description', \`${suggestions.marketing.replace(/`/g, '\\`')}\`)" >
                                <h4 class="font-medium text-gray-800 mb-2">Marketing</h4>
                                <p class="text-gray-600">${suggestions.marketing}</p>
                            </div>
                        ` : ''}
                    </div>
                `;
            }
            break;

        case 'name':
            if (Array.isArray(suggestions)) {
                content.innerHTML = `
                    <div class="space-y-3">
                        <p class="text-sm text-gray-600 mb-4">Suggested names:</p>
                        ${suggestions.map((name, index) => `
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer"
                                 onclick="selectCategorySuggestion('name', '${name}')">
                                <div class="flex items-center space-x-3">
                                    <span class="inline-flex items-center justify-center w-6 h-6 bg-purple-100 text-purple-600 text-xs font-medium rounded-full">
                                        ${index + 1}
                                    </span>
                                    <span class="font-medium text-gray-900">${name}</span>
                                </div>
                                <button class="text-purple-600 hover:text-purple-800">
                                    <i class="fas fa-check"></i>
                                </button>
                            </div>
                        `).join('')}
                    </div>
                `;
            }
            break;

        case 'icon':
            if (Array.isArray(suggestions)) {
                content.innerHTML = `
                    <div class="space-y-3">
                        <p class="text-sm text-gray-600 mb-4">Suggested icons:</p>
                        <div class="grid grid-cols-3 gap-4">
                            ${suggestions.map(icon => `
                                <div class="flex flex-col items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer"
                                     onclick="selectCategorySuggestion('icon', '${icon}')">
                                    <span class="text-3xl mb-2">${icon}</span>
                                    <button class="text-sm text-blue-600 hover:text-blue-800">
                                        Select
                                    </button>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                `;
            }
            break;
    }

    document.getElementById('categoryAISuggestions').classList.remove('hidden');
}

// 选择分类建议
function selectCategorySuggestion(type, value) {
    switch (type) {
        case 'name':
            document.getElementById('categoryName').value = value;
            // 自动生成slug
            const slug = value.toLowerCase()
                .replace(/[^a-z0-9\s-]/g, '')
                .replace(/\s+/g, '-')
                .replace(/-+/g, '-')
                .trim('-');
            document.getElementById('categorySlug').value = slug;
            checkCategorySlugAvailability(slug);
            break;
        case 'slug':
            document.getElementById('categorySlug').value = value;
            checkCategorySlugAvailability(value);
            break;
        case 'description':
            document.getElementById('categoryDescription').value = value;
            break;
        case 'icon':
            document.getElementById('categoryIcon').value = value;
            break;
    }

    closeCategoryAIModal();
}

// 重试分类AI建议
function retryCategoryAISuggestion() {
    if (currentCategoryAIType) {
        // 重新调用当前类型的AI建议
        getCategoryAISuggestion(currentCategoryAIType);
    }
}

// 显示分类AI错误
function showCategoryAIError(message) {
    document.getElementById('categoryAIErrorMessage').textContent = message;
    document.getElementById('categoryAIError').classList.remove('hidden');
}

// 关闭分类AI模态框
function closeCategoryAIModal() {
    document.getElementById('categoryAISuggestionsModal').classList.add('hidden');
}

// 点击模态框外部关闭
document.getElementById('categoryModal').addEventListener('click', function(e) {
    if (e.target === this) closeModal();
});

document.getElementById('deleteModal').addEventListener('click', function(e) {
    if (e.target === this) closeDeleteModal();
});
</script>
