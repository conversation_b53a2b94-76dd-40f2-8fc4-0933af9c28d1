<?php
/**
 * 安全中间件
 * 在请求处理前执行各种安全检查
 */

// 防止直接访问
if (!defined('ROOT_PATH')) {
    exit('Direct access not allowed');
}

// 加载安全管理器
require_once ROOT_PATH . '/control-panel/classes/SecurityManager.php';

class SecurityMiddleware {
    
    private $securityManager;
    private $config;
    
    /**
     * 构造函数
     */
    public function __construct() {
        $this->securityManager = new SecurityManager();
        $this->config = include ROOT_PATH . '/control-panel/config/security.php';
    }
    
    /**
     * 处理请求前的安全检查
     */
    public function handle() {
        // 1. 检查IP封禁状态
        $this->checkIPBan();
        
        // 2. 检查访问频率限制
        $this->checkRateLimit();
        
        // 3. 验证CSRF Token（POST请求）
        $this->validateCSRF();
        
        // 4. 检测可疑活动
        $this->detectSuspiciousActivity();
        
        // 5. 过滤用户输入
        $this->filterUserInput();
        
        // 6. 设置安全会话
        $this->setupSecureSession();
        
        return true;
    }
    
    /**
     * 检查IP封禁状态
     */
    private function checkIPBan() {
        $ip = $this->securityManager->getClientIP();
        
        if ($this->securityManager->isIPBanned($ip)) {
            $this->securityManager->logSecurityEvent('banned_ip_access_attempt', [
                'ip' => $ip,
                'request_uri' => $_SERVER['REQUEST_URI'] ?? '',
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
            ]);
            
            http_response_code(403);
            header('Content-Type: application/json');
            echo json_encode([
                'error' => 'Access denied',
                'message' => 'Your IP address has been banned due to security violations',
                'code' => 'IP_BANNED'
            ]);
            exit;
        }
    }
    
    /**
     * 检查访问频率限制
     */
    private function checkRateLimit() {
        $result = $this->securityManager->checkRateLimit();
        
        if (!$result['allowed']) {
            http_response_code(429);
            header('Content-Type: application/json');
            header('Retry-After: ' . $result['retry_after']);
            
            echo json_encode([
                'error' => 'Rate limit exceeded',
                'message' => $result['message'],
                'retry_after' => $result['retry_after'],
                'code' => 'RATE_LIMIT_EXCEEDED'
            ]);
            exit;
        }
    }
    
    /**
     * 验证CSRF Token
     */
    private function validateCSRF() {
        // 只对POST、PUT、DELETE请求验证CSRF
        $method = $_SERVER['REQUEST_METHOD'] ?? 'GET';
        if (!in_array($method, ['POST', 'PUT', 'DELETE', 'PATCH'])) {
            return;
        }
        
        // 跳过API请求（如果有其他认证机制）
        $requestUri = $_SERVER['REQUEST_URI'] ?? '';
        if (strpos($requestUri, '/api/') !== false) {
            return;
        }
        
        $token = $_POST['csrf_token'] ?? $_SERVER['HTTP_X_CSRF_TOKEN'] ?? '';
        
        if (!$this->securityManager->validateCSRFToken($token)) {
            http_response_code(403);
            header('Content-Type: application/json');
            
            echo json_encode([
                'error' => 'CSRF validation failed',
                'message' => 'Invalid or missing CSRF token',
                'code' => 'CSRF_TOKEN_INVALID'
            ]);
            exit;
        }
    }
    
    /**
     * 检测可疑活动
     */
    private function detectSuspiciousActivity() {
        $requestUri = $_SERVER['REQUEST_URI'] ?? '';
        $queryString = $_SERVER['QUERY_STRING'] ?? '';
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        $postData = file_get_contents('php://input');
        
        // 检查请求URI
        $uriResult = $this->securityManager->detectSuspiciousActivity($requestUri, ['type' => 'uri']);
        if ($uriResult['suspicious']) {
            $this->handleSuspiciousActivity('uri', $uriResult, $requestUri);
        }
        
        // 检查查询字符串
        if (!empty($queryString)) {
            $queryResult = $this->securityManager->detectSuspiciousActivity($queryString, ['type' => 'query']);
            if ($queryResult['suspicious']) {
                $this->handleSuspiciousActivity('query', $queryResult, $queryString);
            }
        }
        
        // 检查POST数据
        if (!empty($postData)) {
            $postResult = $this->securityManager->detectSuspiciousActivity($postData, ['type' => 'post']);
            if ($postResult['suspicious']) {
                $this->handleSuspiciousActivity('post', $postResult, $postData);
            }
        }
        
        // 检查User Agent
        $agentResult = $this->checkSuspiciousUserAgent($userAgent);
        if ($agentResult['suspicious']) {
            $this->handleSuspiciousActivity('user_agent', $agentResult, $userAgent);
        }
    }
    
    /**
     * 检查可疑的User Agent
     */
    private function checkSuspiciousUserAgent($userAgent) {
        $suspiciousPatterns = [
            '/bot|crawler|spider|scraper/i',
            '/curl|wget|python|perl|ruby/i',
            '/scanner|exploit|hack|attack/i',
            '/^$/i' // 空User Agent
        ];
        
        foreach ($suspiciousPatterns as $pattern) {
            if (preg_match($pattern, $userAgent)) {
                return [
                    'suspicious' => true,
                    'type' => 'suspicious_user_agent',
                    'risk_level' => 'medium'
                ];
            }
        }
        
        return ['suspicious' => false];
    }
    
    /**
     * 处理可疑活动
     */
    private function handleSuspiciousActivity($source, $result, $data) {
        $ip = $this->securityManager->getClientIP();
        
        $this->securityManager->logSecurityEvent('suspicious_activity', [
            'source' => $source,
            'type' => $result['type'],
            'risk_level' => $result['risk_level'],
            'data' => substr($data, 0, 500), // 限制日志大小
            'ip' => $ip
        ]);
        
        // 根据风险等级采取行动
        switch ($result['risk_level']) {
            case 'critical':
                // 立即封禁IP
                $this->securityManager->banIP($ip, "Critical security violation: {$result['type']}");
                $this->blockRequest('Critical security violation detected');
                break;
                
            case 'high':
                // 记录并可能封禁
                $this->incrementThreatScore($ip);
                if ($this->getThreatScore($ip) >= 5) {
                    $this->securityManager->banIP($ip, "High threat score reached");
                    $this->blockRequest('Multiple security violations detected');
                }
                break;
                
            case 'medium':
                // 记录威胁分数
                $this->incrementThreatScore($ip);
                break;
        }
    }
    
    /**
     * 增加威胁分数
     */
    private function incrementThreatScore($ip) {
        $key = "threat_score:{$ip}";
        $redis = new Redis();
        
        try {
            $redis->connect('127.0.0.1', 6379);
            $redis->select(2);
            $score = $redis->incr($key);
            $redis->expire($key, 3600); // 1小时过期
            return $score;
        } catch (Exception $e) {
            return 1;
        }
    }
    
    /**
     * 获取威胁分数
     */
    private function getThreatScore($ip) {
        $key = "threat_score:{$ip}";
        $redis = new Redis();
        
        try {
            $redis->connect('127.0.0.1', 6379);
            $redis->select(2);
            return (int)$redis->get($key);
        } catch (Exception $e) {
            return 0;
        }
    }
    
    /**
     * 阻止请求
     */
    private function blockRequest($message) {
        http_response_code(403);
        header('Content-Type: application/json');
        
        echo json_encode([
            'error' => 'Security violation',
            'message' => $message,
            'code' => 'SECURITY_VIOLATION'
        ]);
        exit;
    }
    
    /**
     * 过滤用户输入
     */
    private function filterUserInput() {
        if (!$this->config['xss']['filter_input']) {
            return;
        }
        
        // 过滤GET参数
        if (!empty($_GET)) {
            $_GET = $this->recursiveFilter($_GET);
        }
        
        // 过滤POST参数
        if (!empty($_POST)) {
            $_POST = $this->recursiveFilter($_POST);
        }
        
        // 过滤Cookie
        if (!empty($_COOKIE)) {
            $_COOKIE = $this->recursiveFilter($_COOKIE);
        }
    }
    
    /**
     * 递归过滤数组
     */
    private function recursiveFilter($data) {
        if (is_array($data)) {
            foreach ($data as $key => $value) {
                $data[$key] = $this->recursiveFilter($value);
            }
        } else {
            $data = $this->securityManager->filterInput($data);
        }
        
        return $data;
    }
    
    /**
     * 设置安全会话
     */
    private function setupSecureSession() {
        if (session_status() === PHP_SESSION_ACTIVE) {
            return;
        }
        
        $sessionConfig = $this->config['session'];
        
        // 设置会话配置
        ini_set('session.name', $sessionConfig['name']);
        ini_set('session.cookie_lifetime', $sessionConfig['lifetime']);
        ini_set('session.cookie_httponly', $sessionConfig['cookie_httponly']);
        ini_set('session.cookie_secure', $sessionConfig['cookie_secure']);
        ini_set('session.cookie_samesite', $sessionConfig['cookie_samesite']);
        ini_set('session.use_strict_mode', $sessionConfig['use_strict_mode']);
        ini_set('session.gc_maxlifetime', $sessionConfig['gc_maxlifetime']);
        ini_set('session.entropy_length', $sessionConfig['entropy_length']);
        ini_set('session.hash_function', $sessionConfig['hash_function']);
        
        // 启动会话
        session_start();
        
        // 检查会话安全性
        $this->validateSessionSecurity();
        
        // 定期重新生成会话ID
        if (!isset($_SESSION['last_regeneration'])) {
            $_SESSION['last_regeneration'] = time();
        } elseif (time() - $_SESSION['last_regeneration'] > $sessionConfig['regenerate_id_interval']) {
            session_regenerate_id(true);
            $_SESSION['last_regeneration'] = time();
        }
    }
    
    /**
     * 验证会话安全性
     */
    private function validateSessionSecurity() {
        $ip = $this->securityManager->getClientIP();
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        
        // 检查IP地址变化
        if (isset($_SESSION['ip_address']) && $_SESSION['ip_address'] !== $ip) {
            if ($this->config['login']['ip_validation']) {
                $this->securityManager->logSecurityEvent('session_ip_mismatch', [
                    'old_ip' => $_SESSION['ip_address'],
                    'new_ip' => $ip
                ]);
                
                session_destroy();
                $this->blockRequest('Session security violation');
            }
        } else {
            $_SESSION['ip_address'] = $ip;
        }
        
        // 检查User Agent变化
        if (isset($_SESSION['user_agent']) && $_SESSION['user_agent'] !== $userAgent) {
            if ($this->config['login']['user_agent_validation']) {
                $this->securityManager->logSecurityEvent('session_user_agent_mismatch', [
                    'old_user_agent' => $_SESSION['user_agent'],
                    'new_user_agent' => $userAgent
                ]);
                
                session_destroy();
                $this->blockRequest('Session security violation');
            }
        } else {
            $_SESSION['user_agent'] = $userAgent;
        }
    }
    
    /**
     * 生成安全的随机字符串
     */
    public function generateSecureToken($length = 32) {
        return bin2hex(random_bytes($length));
    }
    
    /**
     * 验证文件上传安全性
     */
    public function validateFileUpload($file) {
        if (!$this->config['file_upload']['enabled']) {
            return ['valid' => false, 'error' => 'File upload is disabled'];
        }
        
        $config = $this->config['file_upload'];
        
        // 检查文件大小
        if ($file['size'] > $config['max_file_size']) {
            return ['valid' => false, 'error' => 'File size exceeds limit'];
        }
        
        // 检查文件扩展名
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (!in_array($extension, $config['allowed_extensions'])) {
            return ['valid' => false, 'error' => 'File type not allowed'];
        }
        
        // 检查MIME类型
        $mimeType = $file['type'];
        if (!in_array($mimeType, $config['allowed_mime_types'])) {
            return ['valid' => false, 'error' => 'MIME type not allowed'];
        }
        
        // 检查文件内容（简单的恶意软件检测）
        if ($config['scan_for_malware']) {
            $content = file_get_contents($file['tmp_name']);
            if ($this->containsMaliciousContent($content)) {
                return ['valid' => false, 'error' => 'Malicious content detected'];
            }
        }
        
        return ['valid' => true];
    }
    
    /**
     * 检查恶意内容
     */
    private function containsMaliciousContent($content) {
        $maliciousPatterns = [
            '/<script[^>]*>.*?<\/script>/is',
            '/javascript:/i',
            '/vbscript:/i',
            '/onload\s*=/i',
            '/onerror\s*=/i',
            '/eval\s*\(/i',
            '/exec\s*\(/i'
        ];
        
        foreach ($maliciousPatterns as $pattern) {
            if (preg_match($pattern, $content)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 清理临时文件
     */
    public function cleanupTempFiles() {
        $tempDir = $this->config['file_upload']['temp_path'];
        if (!is_dir($tempDir)) {
            return;
        }
        
        $files = glob($tempDir . '/*');
        $now = time();
        
        foreach ($files as $file) {
            if (is_file($file) && ($now - filemtime($file)) > 3600) { // 1小时后清理
                unlink($file);
            }
        }
    }
}

/**
 * 安全中间件初始化函数
 */
function initializeSecurityMiddleware() {
    $middleware = new SecurityMiddleware();
    return $middleware->handle();
}

// 自动执行安全检查（如果不是通过类调用）
if (!defined('SECURITY_MIDDLEWARE_MANUAL')) {
    initializeSecurityMiddleware();
}
?>
