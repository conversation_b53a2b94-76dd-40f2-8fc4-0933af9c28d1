<?php
/**
 * Change Password Page
 */

// Security check
if (!defined('SECURE_ACCESS')) {
    exit('Direct access not allowed');
}

// 使用统一的数据库连接
require_once dirname(dirname(__DIR__)) . '/includes/database-connection.php';
require_once __DIR__ . '/../includes/GoogleAuthenticator.php';

// Check if user has 2FA enabled
$has2FA = false;
try {
    $adminId = $_SESSION['admin_id'] ?? 1;
    $checkSql = "SELECT two_factor_enabled FROM pt_manager WHERE id = ?";
    $checkStmt = $pdo->prepare($checkSql);
    $checkStmt->execute([$adminId]);
    $has2FA = (bool)$checkStmt->fetchColumn();
} catch (PDOException $e) {
    error_log("Database error checking 2FA: " . $e->getMessage());
}

// Handle form submission
$message = '';
$messageType = '';

// No longer using session messages due to header issues

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'update') {
    $currentPassword = $_POST['current_password'] ?? '';
    $newPassword = $_POST['new_password'] ?? '';
    $confirmPassword = $_POST['confirm_password'] ?? '';
    $twoFactorCode = $_POST['two_factor_code'] ?? '';

    // Basic validation
    if (empty($currentPassword) || empty($newPassword) || empty($confirmPassword)) {
        $message = 'All password fields are required.';
        $messageType = 'error';
    } elseif ($has2FA && empty($twoFactorCode)) {
        $message = '2FA verification code is required.';
        $messageType = 'error';
    } elseif ($newPassword !== $confirmPassword) {
        $message = 'New password and confirmation do not match.';
        $messageType = 'error';
    } elseif (strlen($newPassword) < 8) {
        $message = 'Password must be at least 8 characters long.';
        $messageType = 'error';
    } elseif (!preg_match('/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/', $newPassword)) {
        $message = 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character.';
        $messageType = 'error';
    } else {
        try {
            $adminId = $_SESSION['admin_id'] ?? 1;

            // Get current password hash from database
            $currentSql = "SELECT password FROM pt_manager WHERE id = ?";
            $currentStmt = $pdo->prepare($currentSql);
            $currentStmt->execute([$adminId]);
            $currentHash = $currentStmt->fetchColumn();

            // Verify current password
            if (!password_verify($currentPassword, $currentHash)) {
                $message = 'Current password is incorrect.';
                $messageType = 'error';
            } elseif ($has2FA) {
                // If 2FA is enabled, verify the 2FA code
                $secretSql = "SELECT two_factor_secret FROM pt_manager WHERE id = ?";
                $secretStmt = $pdo->prepare($secretSql);
                $secretStmt->execute([$adminId]);
                $secret = $secretStmt->fetchColumn();

                if (!$secret) {
                    $message = '2FA secret not found. Please contact administrator.';
                    $messageType = 'error';
                } else {
                    $ga = new GoogleAuthenticator();
                    if (!$ga->verifyCode($secret, $twoFactorCode)) {
                        $message = 'Invalid 2FA verification code.';
                        $messageType = 'error';
                    } else {
                        // 2FA verified, proceed with password update
                        $canUpdatePassword = true;
                    }
                }
            } else {
                // No 2FA required, can update password
                $canUpdatePassword = true;
            }

            // Update password if all verifications passed
            if (isset($canUpdatePassword) && $canUpdatePassword) {
                $newHash = password_hash($newPassword, PASSWORD_DEFAULT);
                $updateSql = "UPDATE pt_manager SET password = ?, updated_at = NOW() WHERE id = ?";
                $updateStmt = $pdo->prepare($updateSql);
                $updateResult = $updateStmt->execute([$newHash, $adminId]);

                if ($updateResult) {
                    // Log activity
                    try {
                        require_once __DIR__ . '/../includes/activity-logger.php';
                        logPasswordChange($adminId);
                    } catch (Exception $e) {
                        // Ignore activity log errors
                    }

                    $message = 'Password updated successfully!';
                    $messageType = 'success';

                    // Use JavaScript redirect to avoid header issues
                    echo "<script>
                        setTimeout(function() {
                            window.location.href = 'index.php?page=password';
                        }, 1500);
                    </script>";

                    // Clear form data
                    $_POST = [];
                } else {
                    $message = 'Failed to update password.';
                    $messageType = 'error';
                }
            }
        } catch (PDOException $e) {
            error_log("Database error in password update: " . $e->getMessage());
            $message = 'Database error occurred. Please try again.';
            $messageType = 'error';
        }
    }
}
?>

<div class="max-w-2xl mx-auto">
    <div class="mb-6">
        <h1 class="text-2xl font-semibold text-gray-900">Change Password</h1>
        <p class="text-gray-600 mt-2">For account security, please update your password regularly</p>
        <?php if ($has2FA && $messageType !== 'success'): ?>
        <div class="mt-3 p-3 bg-blue-50 border border-blue-200 rounded">
            <div class="flex items-center">
                <i class="fas fa-shield-alt text-blue-500 mr-2"></i>
                <span class="text-sm text-blue-700">Two-factor authentication is enabled. You'll need to provide your 2FA code to change your password.</span>
            </div>
        </div>
        <?php endif; ?>
    </div>

    <!-- Success/Error Messages -->
    <?php if ($message): ?>
    <div class="mb-6 p-4 border <?= $messageType === 'success' ? 'bg-green-50 border-green-200 text-green-700' : 'bg-red-50 border-red-200 text-red-700' ?>">
        <div class="flex">
            <div class="flex-shrink-0">
                <i class="fas <?= $messageType === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle' ?>"></i>
            </div>
            <div class="ml-3">
                <p class="text-sm"><?= htmlspecialchars($message) ?></p>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- 修改密码表单 -->
    <div class="bg-white shadow border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-medium text-gray-900">Password Settings</h2>
        </div>
        <div class="p-6">
            <form method="POST" action="?page=password&action=update">
                <!-- Current Password -->
                <div class="mb-6">
                    <label for="current_password" class="block text-sm font-medium text-gray-700 mb-2">Current Password</label>
                    <input type="password" id="current_password" name="current_password" required
                           class="w-full px-3 py-2 border border-gray-300 focus:outline-none focus:border-accent"
                           placeholder="Enter your current password">
                </div>

                <!-- New Password -->
                <div class="mb-6">
                    <label for="new_password" class="block text-sm font-medium text-gray-700 mb-2">New Password</label>
                    <div class="relative">
                        <input type="password" id="new_password" name="new_password" required
                               class="w-full px-3 py-2 pr-32 border border-gray-300 focus:outline-none focus:border-accent"
                               placeholder="Enter your new password">
                        <div class="absolute inset-y-0 right-0 flex items-center space-x-1 pr-3">
                            <button type="button" id="toggle-password" class="text-gray-400 hover:text-gray-600" title="Show/Hide Password">
                                <i class="fas fa-eye" id="password-eye"></i>
                            </button>
                            <button type="button" id="generate-password" class="text-accent hover:text-blue-700" title="Generate Random Password">
                                <i class="fas fa-random"></i>
                            </button>
                        </div>
                    </div>
                    <p class="mt-1 text-sm text-gray-500">Password must be at least 8 characters with letters, numbers and special characters</p>

                    <!-- Password Generation Options -->
                    <div class="mt-2 p-3 bg-gray-50 border border-gray-200 hidden" id="password-options">
                        <div class="text-sm font-medium text-gray-700 mb-2">Password Generation Options</div>
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="flex items-center">
                                    <input type="checkbox" id="include-uppercase" checked class="mr-2">
                                    <span class="text-sm">Uppercase (A-Z)</span>
                                </label>
                            </div>
                            <div>
                                <label class="flex items-center">
                                    <input type="checkbox" id="include-lowercase" checked class="mr-2">
                                    <span class="text-sm">Lowercase (a-z)</span>
                                </label>
                            </div>
                            <div>
                                <label class="flex items-center">
                                    <input type="checkbox" id="include-numbers" checked class="mr-2">
                                    <span class="text-sm">Numbers (0-9)</span>
                                </label>
                            </div>
                            <div>
                                <label class="flex items-center">
                                    <input type="checkbox" id="include-symbols" checked class="mr-2">
                                    <span class="text-sm">Symbols (!@#$%)</span>
                                </label>
                            </div>
                        </div>
                        <div class="mt-3 flex items-center space-x-3">
                            <label class="text-sm font-medium text-gray-700">Length:</label>
                            <input type="range" id="password-length" min="8" max="32" value="16" class="flex-1">
                            <span id="length-display" class="text-sm font-medium text-gray-900">16</span>
                        </div>
                        <div class="mt-3 flex justify-end space-x-2">
                            <button type="button" id="cancel-generate" class="px-3 py-1 text-sm border border-gray-300 text-gray-700 hover:bg-gray-50">
                                Cancel
                            </button>
                            <button type="button" id="apply-generate" class="px-3 py-1 text-sm bg-accent text-white hover:bg-blue-700">
                                Generate & Apply
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Confirm New Password -->
                <div class="mb-6">
                    <label for="confirm_password" class="block text-sm font-medium text-gray-700 mb-2">Confirm New Password</label>
                    <input type="password" id="confirm_password" name="confirm_password" required
                           class="w-full px-3 py-2 border border-gray-300 focus:outline-none focus:border-accent"
                           placeholder="Confirm your new password">
                </div>

                <?php if ($has2FA): ?>
                <!-- 2FA Verification Code -->
                <div class="mb-6">
                    <label for="two_factor_code" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-shield-alt text-blue-500 mr-1"></i>
                        Two-Factor Authentication Code
                    </label>
                    <input type="text" id="two_factor_code" name="two_factor_code" maxlength="6" required
                           class="w-full px-3 py-2 border border-gray-300 focus:outline-none focus:border-accent text-center text-lg font-mono"
                           placeholder="000000">
                    <p class="mt-1 text-sm text-gray-500">Enter the 6-digit code from your authenticator app</p>
                </div>
                <?php endif; ?>

                <!-- Password Strength Indicator -->
                <div class="mb-6">
                    <div class="text-sm font-medium text-gray-700 mb-2">Password Strength</div>
                    <div class="flex space-x-1">
                        <div class="h-2 w-1/4 bg-gray-200" id="strength-1"></div>
                        <div class="h-2 w-1/4 bg-gray-200" id="strength-2"></div>
                        <div class="h-2 w-1/4 bg-gray-200" id="strength-3"></div>
                        <div class="h-2 w-1/4 bg-gray-200" id="strength-4"></div>
                    </div>
                    <p class="mt-1 text-sm text-gray-500" id="strength-text">Please enter password</p>
                </div>

                <!-- Hidden Fields -->
                <input type="hidden" name="action" value="update">

                <!-- Buttons -->
                <div class="flex justify-end space-x-3">
                    <button type="button" class="px-4 py-2 border border-gray-300 text-gray-700 hover:bg-gray-50" onclick="window.location.reload()">
                        Cancel
                    </button>
                    <button type="submit" class="px-4 py-2 bg-accent text-white hover:bg-blue-700">
                        Update Password
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Security Tips -->
    <div class="mt-6 bg-blue-50 border border-blue-200 p-4">
        <div class="flex">
            <div class="flex-shrink-0">
                <i class="fas fa-info-circle text-blue-400"></i>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-blue-800">Password Security Tips</h3>
                <div class="mt-2 text-sm text-blue-700">
                    <ul class="list-disc list-inside space-y-1">
                        <li>Use at least 8 characters</li>
                        <li>Include uppercase and lowercase letters, numbers and special characters</li>
                        <li>Avoid using personal information as passwords</li>
                        <li>Change passwords regularly</li>
                        <li>Don't use the same password on multiple websites</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Password strength detection
document.getElementById('new_password').addEventListener('input', function() {
    const password = this.value;
    const strength = checkPasswordStrength(password);
    updateStrengthIndicator(strength);
});

// Toggle password visibility
document.getElementById('toggle-password').addEventListener('click', function() {
    const passwordField = document.getElementById('new_password');
    const confirmField = document.getElementById('confirm_password');
    const eyeIcon = document.getElementById('password-eye');

    if (passwordField.type === 'password') {
        passwordField.type = 'text';
        confirmField.type = 'text';
        eyeIcon.className = 'fas fa-eye-slash';
    } else {
        passwordField.type = 'password';
        confirmField.type = 'password';
        eyeIcon.className = 'fas fa-eye';
    }
});

// Show password generation options
document.getElementById('generate-password').addEventListener('click', function() {
    const options = document.getElementById('password-options');
    options.classList.toggle('hidden');
});

// Cancel password generation
document.getElementById('cancel-generate').addEventListener('click', function() {
    document.getElementById('password-options').classList.add('hidden');
});

// Update length display
document.getElementById('password-length').addEventListener('input', function() {
    document.getElementById('length-display').textContent = this.value;
});

// Generate and apply password
document.getElementById('apply-generate').addEventListener('click', function() {
    const password = generateRandomPassword();
    const newPasswordField = document.getElementById('new_password');
    const confirmPasswordField = document.getElementById('confirm_password');

    // Set the generated password
    newPasswordField.value = password;
    confirmPasswordField.value = password;

    // Update strength indicator
    const strength = checkPasswordStrength(password);
    updateStrengthIndicator(strength);

    // Hide options
    document.getElementById('password-options').classList.add('hidden');

    // Show password temporarily
    newPasswordField.type = 'text';
    confirmPasswordField.type = 'text';
    document.getElementById('password-eye').className = 'fas fa-eye-slash';

    // Show success message
    showPasswordMessage('Random password generated and applied successfully!', 'success');
});

// Generate random password function
function generateRandomPassword() {
    const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const lowercase = 'abcdefghijklmnopqrstuvwxyz';
    const numbers = '0123456789';
    const symbols = '!@#$%^&*()_+-=[]{}|;:,.<>?';

    let charset = '';
    let password = '';

    // Build charset based on selected options
    if (document.getElementById('include-uppercase').checked) charset += uppercase;
    if (document.getElementById('include-lowercase').checked) charset += lowercase;
    if (document.getElementById('include-numbers').checked) charset += numbers;
    if (document.getElementById('include-symbols').checked) charset += symbols;

    if (charset === '') {
        showPasswordMessage('Please select at least one character type!', 'error');
        return '';
    }

    const length = parseInt(document.getElementById('password-length').value);

    // Ensure at least one character from each selected type
    if (document.getElementById('include-uppercase').checked) {
        password += uppercase[Math.floor(Math.random() * uppercase.length)];
    }
    if (document.getElementById('include-lowercase').checked) {
        password += lowercase[Math.floor(Math.random() * lowercase.length)];
    }
    if (document.getElementById('include-numbers').checked) {
        password += numbers[Math.floor(Math.random() * numbers.length)];
    }
    if (document.getElementById('include-symbols').checked) {
        password += symbols[Math.floor(Math.random() * symbols.length)];
    }

    // Fill the rest randomly
    for (let i = password.length; i < length; i++) {
        password += charset[Math.floor(Math.random() * charset.length)];
    }

    // Shuffle the password
    return password.split('').sort(() => Math.random() - 0.5).join('');
}

function checkPasswordStrength(password) {
    let score = 0;
    
    if (password.length >= 8) score++;
    if (/[a-z]/.test(password)) score++;
    if (/[A-Z]/.test(password)) score++;
    if (/[0-9]/.test(password)) score++;
    if (/[^A-Za-z0-9]/.test(password)) score++;
    
    return Math.min(score, 4);
}

function updateStrengthIndicator(strength) {
    const indicators = ['strength-1', 'strength-2', 'strength-3', 'strength-4'];
    const colors = ['bg-red-500', 'bg-orange-500', 'bg-yellow-500', 'bg-green-500'];
    const texts = ['Very Weak', 'Weak', 'Fair', 'Good', 'Strong'];

    // Reset all indicators
    indicators.forEach(id => {
        const element = document.getElementById(id);
        element.className = 'h-2 w-1/4 bg-gray-200';
    });

    // Set strength indicators
    for (let i = 0; i < strength; i++) {
        const element = document.getElementById(indicators[i]);
        element.className = `h-2 w-1/4 ${colors[Math.min(strength - 1, 3)]}`;
    }

    // Update text
    document.getElementById('strength-text').textContent = texts[strength] || 'Please enter password';
}

// Show password message function
function showPasswordMessage(message, type) {
    // Remove existing messages
    const existingMessage = document.querySelector('.password-message');
    if (existingMessage) {
        existingMessage.remove();
    }

    // Create new message
    const messageDiv = document.createElement('div');
    messageDiv.className = `password-message mt-2 p-2 text-sm ${type === 'success' ? 'bg-green-50 border border-green-200 text-green-700' : 'bg-red-50 border border-red-200 text-red-700'}`;
    messageDiv.innerHTML = `
        <div class="flex items-center">
            <i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'} mr-2"></i>
            <span>${message}</span>
        </div>
    `;

    // Insert after password options
    const passwordOptions = document.getElementById('password-options');
    passwordOptions.parentNode.insertBefore(messageDiv, passwordOptions.nextSibling);

    // Auto-remove after 3 seconds
    setTimeout(() => {
        messageDiv.remove();
    }, 3000);
}

// 2FA code input validation
<?php if ($has2FA): ?>
document.getElementById('two_factor_code').addEventListener('input', function() {
    // Only allow numbers
    this.value = this.value.replace(/[^0-9]/g, '');

    // Limit to 6 digits
    if (this.value.length > 6) {
        this.value = this.value.slice(0, 6);
    }
});

// Form validation
document.querySelector('form').addEventListener('submit', function(e) {
    const twoFactorCode = document.getElementById('two_factor_code').value;

    if (twoFactorCode.length !== 6) {
        e.preventDefault();
        showPasswordMessage('Please enter a complete 6-digit 2FA code', 'error');
        document.getElementById('two_factor_code').focus();
        return false;
    }
});
<?php endif; ?>
</script>
