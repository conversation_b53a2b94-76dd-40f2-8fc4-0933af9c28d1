<?php
/**
 * 用户工具生成器页面
 * 使用AI生成自定义工具
 */

// 登录检查已在主文件中完成，用户信息可用
// $user 和 $pdo 变量已在 index.php 中定义并可用

// 获取当前用户信息
$username = $_SESSION['username'] ?? 'user';
$currentTime = date('YmdHis');

// 获取工具分类
try {
    $stmt = $pdo->prepare("SELECT name FROM pt_tool_category WHERE status = 'active' ORDER BY name ASC");
    $stmt->execute();
    $toolCategories = $stmt->fetchAll(PDO::FETCH_COLUMN);
} catch (Exception $e) {
    $toolCategories = [];
}

// 获取AiHubMix的APP-Code配置
try {
    $stmt = $pdo->prepare("
        SELECT config_value
        FROM pt_service_config sc
        JOIN pt_service_platform sp ON sc.platform_id = sp.id
        WHERE sp.code = 'aihubmix' AND sc.config_key = 'app_code'
    ");
    $stmt->execute();
    $appCodeData = $stmt->fetch();

    // 设置APP-Code，优先使用数据库配置，备用默认值
    $appCode = $appCodeData ? $appCodeData['config_value'] : 'KXTM3281';
} catch (Exception $e) {
    // 数据库查询失败时使用默认值
    $appCode = 'KXTM3281';
}
?>

<style>
/* 工具生成器样式优化 */
#codeOutput {
    max-height: calc(100vh - 200px);
    min-height: 400px;
}

#toolPrompt {
    min-height: 120px;
}

/* 滚动条样式 */
#codeOutput::-webkit-scrollbar {
    width: 6px;
}

#codeOutput::-webkit-scrollbar-track {
    background: #f1f1f1;
}

#codeOutput::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

#codeOutput::-webkit-scrollbar-thumb:hover {
    background: #a1a1a1;
}
</style>

<!-- Tool Generator Interface - Full Screen -->
<div class="fixed inset-0 bg-white z-40 flex" style="margin-left: 260px; top: 64px;">

    <!-- 左侧面板 - 输入区域 -->
    <div class="w-1/2 border-r border-gray-200 flex flex-col h-full">
        <!-- 头部 -->
        <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-gray-900 to-gray-800 flex-shrink-0">
            <div class="flex items-center justify-between">
                <div>
                    <h2 class="text-xl font-semibold text-white flex items-center">
                        <svg class="w-6 h-6 mr-3 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                        </svg>
                        AI Tool Generator
                    </h2>
                    <p class="text-sm text-gray-300 mt-1">Create custom tools with AI - <?= htmlspecialchars($username) ?></p>
                </div>
            </div>
        </div>

        <!-- 表单内容 -->
        <div class="flex-1 p-4 overflow-y-auto bg-gray-50">
            <!-- 名称 -->
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Name *</label>
                <input type="text" id="toolName"
                       value="<?= htmlspecialchars($username) ?>-Tool-<?= $currentTime ?>"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white">
            </div>

            <!-- 标识符 -->
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Slug *</label>
                <input type="text" id="toolSlug"
                       value="<?= strtolower(htmlspecialchars($username)) ?>-tool-<?= $currentTime ?>"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white">
            </div>

            <!-- 分类 -->
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Category</label>
                <select id="toolCategory" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white">
                    <option value="">Select category</option>
                    <?php foreach ($toolCategories as $category): ?>
                        <option value="<?= htmlspecialchars($category) ?>" <?= strtolower($category) === 'ai' ? 'selected' : '' ?>><?= htmlspecialchars($category) ?></option>
                    <?php endforeach; ?>
                </select>
            </div>

            <!-- 图标 -->
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Icon</label>
                <input type="text" id="toolIcon"
                       value="🔧"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white">
            </div>

            <!-- 描述 -->
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                <textarea id="toolDescription"
                          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent h-20 resize-y bg-white"
                          placeholder="Brief description of the tool">Custom tool by <?= htmlspecialchars($username) ?> - <?= date('Y-m-d H:i:s') ?></textarea>
            </div>

            <!-- 标签 -->
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Tags</label>
                <input type="text" id="toolTags"
                       value="<?= htmlspecialchars($username) ?>, custom, tool"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white"
                       placeholder="tag1, tag2, tag3">
            </div>

            <!-- 状态 -->
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                <div class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-100 text-gray-600">
                    <i class="fas fa-clock mr-2 text-orange-500"></i>
                    Coming Soon (Pending Admin Review)
                </div>
                <p class="text-xs text-gray-500 mt-1">All user-generated tools require admin approval before going live</p>
            </div>

            <!-- 文件导入 -->
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Import File</label>
                <div class="flex space-x-2">
                    <input type="file" id="fileInput" accept=".md,.txt" class="hidden">
                    <button id="importFile" class="px-3 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-md text-sm border border-gray-600">
                        <i class="fas fa-file-import mr-1"></i>Import MD/TXT
                    </button>
                    <span id="fileName" class="text-xs text-gray-500 self-center"></span>
                </div>
                <p class="text-xs text-gray-500 mt-1">Supports .md and .txt files only (max 5000 characters)</p>
            </div>

            <!-- 提示词 -->
            <div class="mb-4 flex-1">
                <label class="block text-sm font-medium text-gray-700 mb-2">Tool Requirements *</label>
                <textarea id="toolPrompt"
                          placeholder="Describe the tool functionality, features, and requirements in detail..."
                          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent h-48 resize-y bg-white"></textarea>
                <div class="text-xs text-gray-500 mt-1">
                    <span id="promptCount">0</span>/5000 characters • Supports importing .md and .txt files
                </div>
            </div>

            <!-- 生成按钮 -->
            <button id="generateTool"
                    class="w-full bg-black hover:bg-gray-800 text-white font-semibold py-3 px-6 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
                <span id="generateText">
                    <i class="fas fa-magic mr-2"></i>Generate Tool with AI
                </span>
                <span id="loadingText" class="hidden">
                    <i class="fas fa-spinner fa-spin mr-2"></i>Generating... Please wait
                </span>
            </button>
        </div>
    </div>

    <!-- 右侧面板 - 代码输出 -->
    <div class="w-1/2 flex flex-col h-full">
        <!-- 状态栏 -->
        <div class="bg-gray-800 px-4 py-2 flex items-center justify-between flex-shrink-0">
            <div class="flex items-center space-x-2">
                <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                <span class="text-gray-300 text-sm ml-2">Generated Tool Code</span>
            </div>
            <div id="statusIndicator" class="text-xs text-gray-400">
                <!-- 状态信息将在这里显示 -->
            </div>
        </div>

        <!-- 代码内容 -->
        <div class="flex-1 p-2 bg-gray-50 overflow-hidden flex flex-col">
            <!-- 状态显示 -->
            <div id="statusDisplay" class="mb-2 p-2 bg-white border-l-4 border-gray-400 rounded-r-md hidden flex-shrink-0">
                <div class="flex items-center">
                    <i class="fas fa-info-circle text-gray-500 mr-2"></i>
                    <p class="text-gray-700"></p>
                </div>
            </div>

            <!-- 代码输出区域 -->
            <div class="flex-1 bg-gray-900 rounded-lg overflow-hidden flex flex-col min-h-0">
                <div class="flex items-center justify-between bg-gray-800 px-4 py-2 flex-shrink-0">
                    <span class="text-sm text-gray-300 flex items-center">
                        <span id="generationTime" class="hidden text-xs bg-gray-700 px-2 py-1 rounded mr-2">0s</span>
                    </span>
                    <div class="flex space-x-2">
                        <!-- 按钮已移除，改为自动部署 -->
                    </div>
                </div>
                <div id="codeOutput" class="flex-1 p-4 overflow-auto min-h-0" style="max-width: 100%; word-break: break-all;">
                    <pre class="text-gray-400 text-sm whitespace-pre-wrap" style="word-break: break-all; overflow-wrap: break-word;">Your generated code will appear here...</pre>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 部署成功预览模态框 -->
<div id="deploySuccessModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center">
    <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4 transform transition-all duration-300 scale-95" id="deploySuccessContent">
        <div class="p-6">
            <div class="flex items-center justify-center mb-4">
                <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
                    <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                </div>
            </div>

            <h3 class="text-xl font-semibold text-gray-900 text-center mb-2">Tool Deployed Successfully!</h3>
            <p class="text-gray-600 text-center mb-6">Your tool has been deployed and is ready to use.</p>

            <div class="bg-gray-50 rounded-lg p-4 mb-6">
                <div class="flex items-center justify-between mb-2">
                    <span class="text-sm font-medium text-gray-700">Tool URL:</span>
                    <span id="categoryBadge" class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"></span>
                </div>
                <div class="flex items-center space-x-2">
                    <input type="text" id="toolUrlInput" readonly
                           class="flex-1 px-3 py-2 bg-white border border-gray-300 rounded text-sm font-mono text-gray-800">
                    <button id="copyUrlBtn" class="px-3 py-2 bg-gray-800 text-white rounded hover:bg-gray-900 transition-colors text-sm">
                        <i class="fas fa-copy"></i>
                    </button>
                </div>
            </div>

            <div class="flex space-x-3">
                <button id="visitToolBtn" class="flex-1 px-4 py-2 bg-gray-800 text-white rounded hover:bg-gray-900 transition-colors font-medium">
                    Visit Tool
                </button>
                <button id="closeModalBtn" class="flex-1 px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors font-medium">
                    Close
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// PHP数据传递到JavaScript
const appCode = '<?= $appCode ?>';

// 工具生成器功能 - 完全基于control-panel/program.php的实现
document.addEventListener('DOMContentLoaded', function() {
    const toolName = document.getElementById('toolName');
    const toolSlug = document.getElementById('toolSlug');
    const toolCategory = document.getElementById('toolCategory');
    const toolIcon = document.getElementById('toolIcon');
    const toolDescription = document.getElementById('toolDescription');
    const toolTags = document.getElementById('toolTags');
    // toolStatus 已写死为 'coming_soon'，不需要获取元素
    const toolPrompt = document.getElementById('toolPrompt');
    const promptCount = document.getElementById('promptCount');
    const fileInput = document.getElementById('fileInput');
    const importFile = document.getElementById('importFile');
    const fileName = document.getElementById('fileName');
    const generateTool = document.getElementById('generateTool');
    const generateText = document.getElementById('generateText');
    const loadingText = document.getElementById('loadingText');
    const codeOutput = document.getElementById('codeOutput');
    const statusIndicator = document.getElementById('statusIndicator');
    const generationTime = document.getElementById('generationTime');

    let generatedCode = '';
    let startTime = 0;
    let isGenerating = false;
    let hasGeneratedCode = false;

    // 自动生成slug（智能驼峰分割）
    toolName.addEventListener('input', function() {
        toolSlug.value = generateSlug(this.value);
        updateGenerateButton();
    });

    // 字符计数和验证
    toolPrompt.addEventListener('input', function() {
        const count = this.value.length;
        promptCount.textContent = count;

        if (count > 5000) {
            this.value = this.value.substring(0, 5000);
            promptCount.textContent = 5000;
        }

        updateGenerateButton();
    });

    // 更新生成按钮状态
    function updateGenerateButton() {
        const hasName = toolName.value.trim() !== '';
        const hasSlug = toolSlug.value.trim() !== '';
        const hasPrompt = toolPrompt.value.trim().length >= 10;

        generateTool.disabled = !hasName || !hasSlug || !hasPrompt || isGenerating;
    }

    // 智能提取工具信息函数
    function extractToolInfoFromContent(content) {
        const info = {
            name: null,
            slug: null,
            category: null,
            description: null,
            tags: null
        };

        // 提取工具名称
        const nameMatch = content.match(/(?:^|\n)\s*-?\s*Tool Name:\s*(.+?)(?:\n|$)/i);
        if (nameMatch) {
            info.name = nameMatch[1].trim().replace(/^\[|\]$/g, ''); // 移除方括号
        }

        // 提取Slug
        const slugMatch = content.match(/(?:^|\n)\s*-?\s*Slug:\s*(.+?)(?:\n|$)/i);
        if (slugMatch) {
            info.slug = slugMatch[1].trim().replace(/^\[|\]$/g, '');
        }

        // 提取分类
        const categoryMatch = content.match(/(?:^|\n)\s*-?\s*Category:\s*(.+?)(?:\n|$)/i);
        if (categoryMatch) {
            info.category = categoryMatch[1].trim().replace(/^\[|\]$/g, '');
        }

        // 提取描述
        const descriptionMatch = content.match(/(?:^|\n)\s*-?\s*Description:\s*(.+?)(?:\n|$)/i);
        if (descriptionMatch) {
            info.description = descriptionMatch[1].trim().replace(/^\[|\]$/g, '');
        }

        // 如果没有找到Description，尝试从Purpose提取
        if (!info.description) {
            const purposeMatch = content.match(/(?:^|\n)\s*-?\s*Purpose:\s*(.+?)(?:\n|$)/i);
            if (purposeMatch) {
                info.description = purposeMatch[1].trim();
            }
        }

        // 提取标签
        const tagsMatch = content.match(/(?:^|\n)\s*-?\s*Tags:\s*(.+?)(?:\n|$)/i);
        if (tagsMatch) {
            info.tags = tagsMatch[1].trim().replace(/^\[|\]$/g, '');
        }

        // 如果没有找到Tags，智能生成标签
        if (!info.tags && (info.name || info.description)) {
            const tags = [];

            // 从工具名称提取标签
            if (info.name) {
                tags.push(info.name.toLowerCase().replace(/[^a-z0-9]/g, ''));
            }

            // 从描述中提取关键词
            if (info.description) {
                const keywords = extractKeywords(info.description);
                tags.push(...keywords);
            }

            // 从内容中提取功能相关关键词
            const functionalKeywords = extractFunctionalKeywords(content);
            tags.push(...functionalKeywords);

            // 去重并限制数量
            const uniqueTags = [...new Set(tags)].slice(0, 3);
            info.tags = uniqueTags.join(', ');
        }

        return info;
    }

    // 从文本中提取关键词
    function extractKeywords(text) {
        const keywords = [];
        const commonWords = ['the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'can', 'must', 'shall', 'a', 'an', 'this', 'that', 'these', 'those'];

        const words = text.toLowerCase().match(/\b[a-z]{3,}\b/g) || [];

        for (const word of words) {
            if (!commonWords.includes(word) && word.length >= 3) {
                keywords.push(word);
            }
        }

        return [...new Set(keywords)].slice(0, 3);
    }

    // 从内容中提取功能相关关键词
    function extractFunctionalKeywords(content) {
        const keywords = [];
        const functionalPatterns = [
            /shopping|budget|expense|tracker|list/gi,
            /calculator|converter|generator|analyzer/gi,
            /manager|organizer|planner|scheduler/gi,
            /tool|utility|helper|assistant/gi
        ];

        for (const pattern of functionalPatterns) {
            const matches = content.match(pattern) || [];
            for (const match of matches) {
                keywords.push(match.toLowerCase());
            }
        }

        return [...new Set(keywords)].slice(0, 3);
    }

    // 生成slug的辅助函数（智能驼峰分割）
    function generateSlug(name) {
        return name
            // 在大写字母前插入连字符（除了第一个字符）
            .replace(/([a-z])([A-Z])/g, '$1-$2')
            // 在数字和字母之间插入连字符
            .replace(/([a-z])([0-9])/g, '$1-$2')
            .replace(/([0-9])([a-z])/g, '$1-$2')
            // 转小写
            .toLowerCase()
            // 将空格转换为连字符
            .replace(/\s+/g, '-')
            // 清理特殊字符，只保留字母、数字和连字符
            .replace(/[^a-z0-9-]/g, '')
            // 合并多个连字符
            .replace(/-+/g, '-')
            // 去除首尾连字符
            .replace(/^-+|-+$/g, '');
    }

    // 文件导入功能
    importFile.addEventListener('click', function() {
        fileInput.click();
    });

    fileInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (!file) return;

        const reader = new FileReader();
        reader.onload = function(e) {
            const content = e.target.result;

            // 智能提取工具信息
            const extractedInfo = extractToolInfoFromContent(content);

            // 填充表单字段（如果提取到信息）
            let extractedCount = 0;

            if (extractedInfo.name) {
                toolName.value = extractedInfo.name;
                extractedCount++;
            }

            if (extractedInfo.slug) {
                toolSlug.value = extractedInfo.slug;
                extractedCount++;
            } else if (extractedInfo.name) {
                // 如果没有slug但有name，自动生成slug
                toolSlug.value = generateSlug(extractedInfo.name);
            }

            if (extractedInfo.category) {
                toolCategory.value = extractedInfo.category;
                extractedCount++;
            }

            if (extractedInfo.description) {
                toolDescription.value = extractedInfo.description;
                extractedCount++;
            }

            if (extractedInfo.tags) {
                toolTags.value = extractedInfo.tags;
                extractedCount++;
            }

            // 显示提取结果
            if (extractedCount > 0) {
                console.log('Extracted info:', extractedInfo);
                fileName.textContent = `✓ Extracted ${extractedCount} field(s) from ${file.name}`;
                fileName.classList.add('text-green-600');
                fileName.classList.remove('text-gray-500');
            } else {
                fileName.textContent = `⚠ No extractable info found in ${file.name}`;
                fileName.classList.add('text-orange-600');
                fileName.classList.remove('text-gray-500');
            }

            // 将内容放入prompt字段
            toolPrompt.value = content;

            // 更新字符计数
            const count = content.length;
            promptCount.textContent = count;

            if (count > 5000) {
                toolPrompt.value = content.substring(0, 5000);
                promptCount.textContent = 5000;
            }

            updateGenerateButton();

            // 显示成功消息
            const originalText = importFile.innerHTML;
            const successMessage = extractedCount > 0 ?
                `<i class="fas fa-check mr-1"></i>Imported & Extracted!` :
                `<i class="fas fa-upload mr-1"></i>Imported!`;

            importFile.innerHTML = successMessage;
            importFile.classList.add('bg-green-600');
            importFile.classList.remove('bg-gray-600');

            setTimeout(() => {
                importFile.innerHTML = originalText;
                importFile.classList.remove('bg-green-600');
                importFile.classList.add('bg-gray-600');

                // 清除文件名状态颜色
                setTimeout(() => {
                    fileName.classList.remove('text-green-600', 'text-orange-600');
                    fileName.classList.add('text-gray-500');
                }, 3000);
            }, 2000);
        };

        reader.onerror = function() {
            showCustomAlert('Error', 'Error reading file. Please try again.', 'error');
        };

        reader.readAsText(file);
    });

    // 表单验证事件监听器
    toolSlug.addEventListener('input', updateGenerateButton);

    // 状态更新函数 - 与control-panel保持一致
    function updateStatus(type, message) {
        const statusDisplay = document.getElementById('statusDisplay');
        const icons = {
            info: 'fas fa-info-circle text-blue-500',
            success: 'fas fa-check-circle text-green-500',
            error: 'fas fa-exclamation-circle text-red-500'
        };

        const colors = {
            info: 'bg-blue-50',
            success: 'bg-green-50',
            error: 'bg-red-50'
        };

        statusDisplay.className = `mb-2 p-2 rounded-md ${colors[type]} flex-shrink-0`;
        statusDisplay.innerHTML = `
            <div class="flex items-center">
                <i class="${icons[type]} mr-2"></i>
                <p class="text-gray-700">${message}</p>
            </div>
        `;
        statusDisplay.classList.remove('hidden');
    }

    // 生成工具功能 - 完全基于control-panel/program.php的实现
    generateTool.addEventListener('click', async function() {
        // 验证必填字段
        if (!toolName.value.trim()) {
            showCustomAlert('Error', 'Please enter a tool name', 'error');
            return;
        }

        if (!toolSlug.value.trim()) {
            showCustomAlert('Error', 'Please enter a URL slug', 'error');
            return;
        }

        if (!toolPrompt.value.trim()) {
            showCustomAlert('Error', 'Please describe your tool requirements', 'error');
            return;
        }

        if (toolPrompt.value.length < 10) {
            showCustomAlert('Error', 'Tool requirements must be at least 10 characters long', 'error');
            return;
        }

        if (toolPrompt.value.length > 5000) {
            showCustomAlert('Error', 'Tool requirements must be less than 5000 characters', 'error');
            return;
        }

        try {
            isGenerating = true;
            startTime = Date.now();

            // 更新UI状态
            generateTool.disabled = true;
            generateText.classList.add('hidden');
            loadingText.classList.remove('hidden');
            updateStatus('info', 'Checking quota...');

            // 检查配额是否足够（与prompt-generator保持一致）
            const quotaCheck = await fetch('../../api/check-quota.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    action: 'tool_generation'
                })
            });

            const quotaResult = await quotaCheck.json();
            if (!quotaResult.success) {
                throw new Error(quotaResult.message || 'Quota check failed');
            }

            if (!quotaResult.has_enough_quota) {
                throw new Error(quotaResult.message || 'Insufficient quota for tool generation');
            }

            // 配额检查通过，开始生成
            updateStatus('info', 'Generating your tool with AI...');

            // 清空之前的代码
            codeOutput.innerHTML = '<div class="text-center text-gray-500 py-20"><i class="fas fa-spinner fa-spin text-6xl mb-4"></i><p class="text-lg">AI is generating your tool...</p></div>';

            // 准备请求数据
            const requestData = {
                name: toolName.value,
                slug: toolSlug.value,
                category: toolCategory.value,
                icon: toolIcon.value,
                description: toolDescription.value,
                tags: toolTags.value,
                status: 'coming_soon', // 用户生成的工具状态写死为 coming_soon
                prompt: toolPrompt.value,
                model: 'claude-sonnet-4-20250514',
                appCode: appCode
            };

            // 发起流式API请求
            const response = await fetch('../../api/generate-tool.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData)
            });

            if (!response.ok) {
                throw new Error('Generation failed');
            }

            // 处理流式响应 - 与control-panel保持一致
            const reader = response.body.getReader();
            const decoder = new TextDecoder();
            generatedCode = '';
            codeOutput.innerHTML = '<pre class="text-green-400 text-sm whitespace-pre-wrap" style="word-break: break-all; overflow-wrap: break-word;"></pre>';
            const preElement = codeOutput.querySelector('pre');

            try {
                while (true) {
                    const { done, value } = await reader.read();

                    if (done) break;

                    const chunk = decoder.decode(value, { stream: true });
                    const lines = chunk.split('\n');

                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            const data = line.slice(6);
                            if (data === '[DONE]') {
                                // 流式完成
                                updateStatus('success', 'Tool generated successfully!');

                                // 代码生成完成，开始自动部署
                                hasGeneratedCode = true;
                                await autoDeploy();

                                // 显示生成信息
                                const endTime = Date.now();
                                const duration = ((endTime - startTime) / 1000).toFixed(1);
                                generationTime.textContent = duration + 's';
                                generationTime.classList.remove('hidden');

                                // 消耗配额 - 只在生成成功后扣除
                                try {
                                    await fetch('../../api/consume-quota.php', {
                                        method: 'POST',
                                        headers: {
                                            'Content-Type': 'application/json'
                                        },
                                        body: JSON.stringify({
                                            action: 'tool_generation',
                                            description: 'AI Tool Code Generation'
                                        })
                                    });
                                } catch (quotaError) {
                                    console.error('Failed to consume quota:', quotaError);
                                }

                                return;
                            }

                            try {
                                const parsed = JSON.parse(data);
                                if (parsed.choices && parsed.choices[0] && parsed.choices[0].delta && parsed.choices[0].delta.content) {
                                    const content = parsed.choices[0].delta.content;
                                    generatedCode += content;
                                    preElement.textContent = generatedCode;

                                    // 自动滚动到底部
                                    codeOutput.scrollTop = codeOutput.scrollHeight;
                                }
                            } catch (e) {
                                // 忽略部分块的JSON解析错误
                            }
                        }
                    }
                }
            } finally {
                reader.releaseLock();
            }

        } catch (error) {
            console.error('Generation error:', error);
            codeOutput.innerHTML = `<div class="text-center text-red-500 py-20"><i class="fas fa-exclamation-triangle text-6xl mb-4"></i><p class="text-lg">Generation failed</p><p class="text-sm mt-2">${error.message}</p></div>`;
            statusIndicator.textContent = 'Generation failed';
            showCustomAlert('Error', 'Generation failed: ' + error.message, 'error');
        } finally {
            isGenerating = false;
            generateTool.disabled = false;
            generateText.classList.remove('hidden');
            loadingText.classList.add('hidden');
            updateGenerateButton();
        }
    });





    // 自动部署功能
    async function autoDeploy() {
        try {
            updateStatus('info', 'Deploying your tool...');

            // 获取分类ID和分类slug
            let categoryId = null;
            let categorySlug = 'uncategorized';
            if (toolCategory.value) {
                const categoryResponse = await fetch('../../api/get-category-id.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ category: toolCategory.value })
                });
                const categoryResult = await categoryResponse.json();
                if (categoryResult.success) {
                    categoryId = categoryResult.category_id;
                    categorySlug = categoryResult.category_slug || 'uncategorized';
                }
            }

            // 部署工具
            const deployData = {
                name: toolName.value,
                slug: toolSlug.value,
                description: toolDescription.value,
                category_id: categoryId,
                icon: toolIcon.value,
                tags: toolTags.value,
                status: 'coming_soon', // 用户生成的工具状态写死为 coming_soon
                code: generatedCode
            };

            const response = await fetch('../../api/deploy-tool.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(deployData)
            });

            const result = await response.json();

            if (result.success) {
                // 使用API返回的实际slug（可能已重命名）
                const actualSlug = result.slug || toolSlug.value;
                const wasRenamed = result.renamed || false;

                // 更新状态消息
                let statusMessage = 'Tool deployed successfully!';
                if (wasRenamed) {
                    statusMessage += ` (Renamed to '${actualSlug}')`;
                }
                updateStatus('success', statusMessage);

                // 构建工具URL（使用实际的slug）
                const toolUrl = `http://localhost/tools/${categorySlug}/${actualSlug}`;

                // 显示部署成功的预览模态框
                showDeploySuccessModal(toolUrl, categorySlug, wasRenamed, result.original_slug, actualSlug);

            } else {
                throw new Error(result.error || 'Deployment failed');
            }

        } catch (error) {
            console.error('Deploy error:', error);
            updateStatus('error', 'Deployment failed: ' + error.message);
            showCustomAlert('Error', 'Deployment failed: ' + error.message, 'error');
        }
    }

    // 显示部署成功模态框
    function showDeploySuccessModal(toolUrl, categorySlug, wasRenamed = false, originalSlug = '', actualSlug = '') {
        const modal = document.getElementById('deploySuccessModal');
        const modalContent = document.getElementById('deploySuccessContent');
        const toolUrlInput = document.getElementById('toolUrlInput');
        const categoryBadge = document.getElementById('categoryBadge');
        const visitToolBtn = document.getElementById('visitToolBtn');
        const copyUrlBtn = document.getElementById('copyUrlBtn');
        const closeModalBtn = document.getElementById('closeModalBtn');

        // 设置URL和分类标签
        toolUrlInput.value = toolUrl;
        categoryBadge.textContent = categorySlug.charAt(0).toUpperCase() + categorySlug.slice(1);

        // 如果发生了重命名，显示重命名信息
        const modalTitle = document.querySelector('#deploySuccessModal h3');
        const modalDescription = document.querySelector('#deploySuccessModal p');

        if (wasRenamed) {
            modalTitle.textContent = 'Tool Deployed Successfully! (Renamed)';
            modalDescription.innerHTML = `Your tool has been deployed and is ready to use.<br><small class="text-gray-500">Note: Slug was renamed from '<strong>${originalSlug}</strong>' to '<strong>${actualSlug}</strong>' to avoid conflicts.</small>`;
        } else {
            modalTitle.textContent = 'Tool Deployed Successfully!';
            modalDescription.textContent = 'Your tool has been deployed and is ready to use.';
        }

        // 显示模态框
        modal.classList.remove('hidden');
        setTimeout(() => {
            modalContent.classList.remove('scale-95');
            modalContent.classList.add('scale-100');
        }, 10);

        // 访问工具按钮
        visitToolBtn.onclick = function() {
            window.open(toolUrl, '_blank');
        };

        // 复制URL按钮
        copyUrlBtn.onclick = async function() {
            try {
                await navigator.clipboard.writeText(toolUrl);
                const originalIcon = this.innerHTML;
                this.innerHTML = '<i class="fas fa-check"></i>';
                this.classList.add('bg-green-600');
                this.classList.remove('bg-gray-800');

                setTimeout(() => {
                    this.innerHTML = originalIcon;
                    this.classList.remove('bg-green-600');
                    this.classList.add('bg-gray-800');
                }, 2000);
            } catch (error) {
                console.error('Copy failed:', error);
                showCustomAlert('Error', 'Failed to copy URL to clipboard', 'error');
            }
        };

        // 关闭模态框按钮
        closeModalBtn.onclick = function() {
            closeDeploySuccessModal();
        };

        // 点击模态框外部关闭
        modal.onclick = function(e) {
            if (e.target === modal) {
                closeDeploySuccessModal();
            }
        };
    }

    // 关闭部署成功模态框
    function closeDeploySuccessModal() {
        const modal = document.getElementById('deploySuccessModal');
        const modalContent = document.getElementById('deploySuccessContent');

        modalContent.classList.remove('scale-100');
        modalContent.classList.add('scale-95');
        setTimeout(() => {
            modal.classList.add('hidden');
        }, 300);
    }

    // 自定义提示窗口函数
    function showCustomAlert(title, message, type = 'info') {
        return new Promise((resolve) => {
            const modal = document.getElementById('customModal');
            const modalTitle = document.getElementById('modalTitle');
            const modalMessage = document.getElementById('modalMessage');
            const modalIcon = document.getElementById('modalIcon');
            const modalConfirm = document.getElementById('modalConfirm');
            const modalContent = document.getElementById('modalContent');

            modalTitle.textContent = title;
            modalMessage.textContent = message;

            let iconHtml = '';
            let iconBg = '';
            switch (type) {
                case 'success':
                    iconHtml = '<svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg>';
                    iconBg = 'bg-green-500';
                    break;
                case 'error':
                    iconHtml = '<svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>';
                    iconBg = 'bg-red-500';
                    break;
                case 'warning':
                    iconHtml = '<svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg>';
                    iconBg = 'bg-yellow-500';
                    break;
                default:
                    iconHtml = '<svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path></svg>';
                    iconBg = 'bg-blue-500';
            }

            modalIcon.innerHTML = iconHtml;
            modalIcon.className = 'w-8 h-8 rounded-full flex items-center justify-center mr-3 ' + iconBg;

            modal.classList.remove('hidden');
            setTimeout(() => {
                modalContent.classList.remove('scale-95');
                modalContent.classList.add('scale-100');
            }, 10);

            const hideModal = () => {
                modalContent.classList.remove('scale-100');
                modalContent.classList.add('scale-95');
                setTimeout(() => {
                    modal.classList.add('hidden');
                }, 300);
            };

            const confirmHandler = () => {
                hideModal();
                modalConfirm.removeEventListener('click', confirmHandler);
                resolve(true);
            };

            modalConfirm.addEventListener('click', confirmHandler);
        });
    }

    // 工具函数
    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // 初始化
    updateGenerateButton();
});
</script>

<!-- 自定义提示窗口组件 -->
<div id="customModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center">
    <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4 transform transition-all duration-300 scale-95" id="modalContent">
        <div class="p-6">
            <div class="flex items-center mb-4">
                <div id="modalIcon" class="w-8 h-8 rounded-full flex items-center justify-center mr-3">
                    <!-- 图标将在这里插入 -->
                </div>
                <h3 id="modalTitle" class="text-lg font-semibold text-gray-900"></h3>
            </div>
            <p id="modalMessage" class="text-gray-600 mb-6"></p>
            <div class="flex justify-end space-x-3">
                <button id="modalCancel" class="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors hidden">
                    Cancel
                </button>
                <button id="modalConfirm" class="px-4 py-2 bg-black text-white rounded hover:bg-gray-800 transition-colors">
                    OK
                </button>
            </div>
        </div>
    </div>
</div>



</script>

<!-- 自定义提示窗口组件 -->
<div id="customModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center">
    <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4 transform transition-all duration-300 scale-95" id="modalContent">
        <div class="p-6">
            <div class="flex items-center mb-4">
                <div id="modalIcon" class="w-8 h-8 rounded-full flex items-center justify-center mr-3">
                    <!-- 图标将在这里插入 -->
                </div>
                <h3 id="modalTitle" class="text-lg font-semibold text-gray-900"></h3>
            </div>
            <p id="modalMessage" class="text-gray-600 mb-6"></p>
            <div class="flex justify-end space-x-3">
                <button id="modalCancel" class="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors hidden">
                    Cancel
                </button>
                <button id="modalConfirm" class="px-4 py-2 bg-black text-white rounded hover:bg-gray-800 transition-colors">
                    OK
                </button>
            </div>
        </div>
    </div>
</div>
