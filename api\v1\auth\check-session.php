<?php
/**
 * 检查会话状态API
 * 用于验证用户是否已登录
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type');

// 禁用错误输出到页面
ini_set('display_errors', 0);
error_reporting(E_ALL);

session_start();

try {
    if (isset($_SESSION['user_id'])) {
        // 用户已登录，返回用户信息
        // 使用统一的数据库连接
        require_once dirname(dirname(dirname(__DIR__))) . '/includes/database-connection.php';
        
        $stmt = $pdo->prepare("SELECT id, email, first_name, last_name, username, avatar, subscription_type FROM pt_member WHERE id = ?");
        $stmt->execute([$_SESSION['user_id']]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($user) {
            echo json_encode([
                'success' => true,
                'logged_in' => true,
                'user' => [
                    'id' => $user['id'],
                    'email' => $user['email'],
                    'name' => trim($user['first_name'] . ' ' . $user['last_name']),
                    'username' => $user['username'],
                    'avatar' => $user['avatar'],
                    'subscription_type' => $user['subscription_type']
                ],
                'session_info' => [
                    'login_time' => $_SESSION['login_time'] ?? null,
                    'login_method' => $_SESSION['login_method'] ?? 'unknown'
                ]
            ]);
        } else {
            // 用户在数据库中不存在，清除会话
            session_destroy();
            echo json_encode([
                'success' => true,
                'logged_in' => false,
                'message' => 'User not found in database'
            ]);
        }
    } else {
        // 用户未登录
        echo json_encode([
            'success' => true,
            'logged_in' => false,
            'message' => 'No active session'
        ]);
    }
    
} catch (Exception $e) {
    error_log("Check session error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'logged_in' => false,
        'message' => 'Session check failed'
    ]);
}
?>
