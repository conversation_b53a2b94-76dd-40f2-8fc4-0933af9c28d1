<?php
/**
 * XML Sitemap生成API
 * 生成网站的XML sitemap文件
 */

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// 安全检查
if (!defined('SECURE_ACCESS')) {
    define('SECURE_ACCESS', true);
}

// 定义根目录
define('ROOT_PATH', dirname(__DIR__));

// 加载应用初始化
require_once ROOT_PATH . '/app/init.php';

// 使用统一的数据库连接
require_once ROOT_PATH . '/includes/database-connection.php';

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// 获取请求数据
$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['action']) || $input['action'] !== 'generate') {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => 'Invalid request']);
    exit;
}

try {
    $startTime = microtime(true);

    // 生成sitemap
    $result = generateSitemap();

    $endTime = microtime(true);
    $generationTime = round($endTime - $startTime, 2);

    if ($result['success']) {
        echo json_encode([
            'success' => true,
            'message' => $result['message'],
            'stats' => [
                'total_urls' => $result['total_urls'],
                'file_size' => formatFileSize($result['file_size']),
                'generation_time' => $generationTime
            ]
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => $result['message'],
            'error' => $result['error'] ?? null
        ]);
    }

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Internal server error',
        'error' => $e->getMessage()
    ]);
}

/**
 * 生成XML sitemap
 */
function generateSitemap() {
    global $pdo;

    try {
        $urls = [];

        // 动态获取基础URL
        $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ? 'https://' : 'http://';
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
        $baseUrl = $protocol . $host;
        
        // 1. 静态页面
        $staticPages = [
            '/' => ['priority' => '1.0', 'changefreq' => 'daily'],
            '/pricing' => ['priority' => '0.8', 'changefreq' => 'weekly'],
            '/about' => ['priority' => '0.6', 'changefreq' => 'monthly'],
            '/contact' => ['priority' => '0.6', 'changefreq' => 'monthly'],
            '/help' => ['priority' => '0.7', 'changefreq' => 'weekly'],
            '/privacy' => ['priority' => '0.5', 'changefreq' => 'yearly'],
            '/terms' => ['priority' => '0.5', 'changefreq' => 'yearly']
        ];
        
        foreach ($staticPages as $path => $config) {
            $urls[] = [
                'loc' => $baseUrl . $path,
                'lastmod' => date('Y-m-d'),
                'changefreq' => $config['changefreq'],
                'priority' => $config['priority']
            ];
        }
        
        // 2. 列表页面
        $listPages = [
            '/tools' => ['priority' => '0.9', 'changefreq' => 'daily'],
            '/categories' => ['priority' => '0.8', 'changefreq' => 'weekly'],
            '/requests' => ['priority' => '0.7', 'changefreq' => 'daily'],
            '/launches' => ['priority' => '0.7', 'changefreq' => 'daily']
        ];
        
        foreach ($listPages as $path => $config) {
            $urls[] = [
                'loc' => $baseUrl . $path,
                'lastmod' => date('Y-m-d'),
                'changefreq' => $config['changefreq'],
                'priority' => $config['priority']
            ];
        }
        
        // 3. 工具详情页面
        $toolUrls = $pdo->query("
            SELECT t.slug as tool_slug, c.slug as category_slug, t.updated_at
            FROM pt_tool t
            LEFT JOIN pt_tool_category c ON t.category_id = c.id
            WHERE t.status = 'active' AND c.status = 'active'
            ORDER BY t.updated_at DESC
        ")->fetchAll();
        
        foreach ($toolUrls as $tool) {
            $urls[] = [
                'loc' => $baseUrl . '/tools/' . $tool['category_slug'] . '/' . $tool['tool_slug'],
                'lastmod' => date('Y-m-d', strtotime($tool['updated_at'])),
                'changefreq' => 'weekly',
                'priority' => '0.8'
            ];
        }
        
        // 4. 工具分类页面
        $categoryUrls = $pdo->query("
            SELECT slug, updated_at FROM pt_tool_category 
            WHERE status = 'active'
            ORDER BY updated_at DESC
        ")->fetchAll();
        
        foreach ($categoryUrls as $category) {
            $urls[] = [
                'loc' => $baseUrl . '/tools/' . $category['slug'],
                'lastmod' => date('Y-m-d', strtotime($category['updated_at'])),
                'changefreq' => 'weekly',
                'priority' => '0.7'
            ];
        }
        
        // 5. 请求详情页面
        $requestUrls = $pdo->query("
            SELECT slug, updated_at FROM pt_user_requests 
            WHERE status IN ('accepted', 'completed')
            ORDER BY updated_at DESC
        ")->fetchAll();
        
        foreach ($requestUrls as $request) {
            $urls[] = [
                'loc' => $baseUrl . '/ideas/' . $request['slug'],
                'lastmod' => date('Y-m-d', strtotime($request['updated_at'])),
                'changefreq' => 'monthly',
                'priority' => '0.6'
            ];
        }
        
        // 6. 产品启动页面
        $launchUrls = $pdo->query("
            SELECT slug, updated_at FROM pt_product_launches 
            WHERE launch_status = 'launched'
            ORDER BY updated_at DESC
        ")->fetchAll();
        
        foreach ($launchUrls as $launch) {
            $urls[] = [
                'loc' => $baseUrl . '/launch/' . $launch['slug'],
                'lastmod' => date('Y-m-d', strtotime($launch['updated_at'])),
                'changefreq' => 'monthly',
                'priority' => '0.6'
            ];
        }
        
        // 生成XML内容
        $xml = generateSitemapXML($urls);
        
        // 保存到文件
        $sitemapPath = ROOT_PATH . '/public/sitemap.xml';
        $bytesWritten = file_put_contents($sitemapPath, $xml);
        
        if ($bytesWritten === false) {
            return [
                'success' => false,
                'message' => 'Failed to write sitemap file',
                'error' => 'Unable to write to ' . $sitemapPath
            ];
        }
        
        return [
            'success' => true,
            'message' => 'Sitemap generated successfully with ' . count($urls) . ' URLs',
            'total_urls' => count($urls),
            'file_size' => $bytesWritten
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'Failed to generate sitemap',
            'error' => $e->getMessage()
        ];
    }
}

/**
 * 生成XML sitemap内容
 */
function generateSitemapXML($urls) {
    $xml = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
    $xml .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";
    
    foreach ($urls as $url) {
        $xml .= '  <url>' . "\n";
        $xml .= '    <loc>' . htmlspecialchars($url['loc'], ENT_XML1, 'UTF-8') . '</loc>' . "\n";
        $xml .= '    <lastmod>' . $url['lastmod'] . '</lastmod>' . "\n";
        $xml .= '    <changefreq>' . $url['changefreq'] . '</changefreq>' . "\n";
        $xml .= '    <priority>' . $url['priority'] . '</priority>' . "\n";
        $xml .= '  </url>' . "\n";
    }
    
    $xml .= '</urlset>';
    return $xml;
}

/**
 * 格式化文件大小
 */
function formatFileSize($bytes) {
    if ($bytes >= 1048576) {
        return number_format($bytes / 1048576, 2) . ' MB';
    } elseif ($bytes >= 1024) {
        return number_format($bytes / 1024, 2) . ' KB';
    } else {
        return $bytes . ' bytes';
    }
}
?>
