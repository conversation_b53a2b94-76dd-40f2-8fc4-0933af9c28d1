<?php
/**
 * 需求投票AJAX处理
 */

// 开启错误显示用于调试
ini_set('display_errors', 1);
error_reporting(E_ALL);

// 设置JSON响应头
header('Content-Type: application/json');

// 安全检查
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

session_start();

// 使用统一的数据库连接
require_once __DIR__ . '/includes/ajax-database.php';

// 检查用户登录状态
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'Please log in to vote']);
    exit;
}

$response = ['success' => false, 'message' => ''];

try {
    // 获取JSON输入
    $input = json_decode(file_get_contents('php://input'), true);
    $requestId = intval($input['request_id'] ?? 0);
    
    if ($requestId <= 0) {
        throw new Exception('Invalid request ID');
    }
    
    // 获取用户信息
    $stmt = $pdo->prepare("SELECT * FROM pt_member WHERE id = ? AND status = 'active'");
    $stmt->execute([$_SESSION['user_id']]);
    $user = $stmt->fetch();
    
    if (!$user) {
        throw new Exception('User not found or inactive');
    }
    
    // 检查需求是否存在
    $stmt = $pdo->prepare("SELECT * FROM pt_user_requests WHERE id = ?");
    $stmt->execute([$requestId]);
    $request = $stmt->fetch();
    
    if (!$request) {
        throw new Exception('Request not found');
    }
    
    // 检查用户是否已经投票
    $stmt = $pdo->prepare("
        SELECT * FROM pt_request_votes 
        WHERE request_id = ? AND user_id = ?
    ");
    $stmt->execute([$requestId, $user['id']]);
    $existingVote = $stmt->fetch();
    
    // 开始事务
    $pdo->beginTransaction();
    
    try {
        if ($existingVote) {
            // 如果已经投票，则取消投票
            $stmt = $pdo->prepare("
                DELETE FROM pt_request_votes 
                WHERE request_id = ? AND user_id = ?
            ");
            $stmt->execute([$requestId, $user['id']]);
            $voted = false;
            $action = 'removed vote from';
        } else {
            // 如果没有投票，则添加投票
            $stmt = $pdo->prepare("
                INSERT INTO pt_request_votes (request_id, user_id, vote_type, created_at)
                VALUES (?, ?, 'up', NOW())
            ");
            $stmt->execute([$requestId, $user['id']]);
            $voted = true;
            $action = 'voted for';
        }
        
        // 获取更新后的投票数
        $stmt = $pdo->prepare("
            SELECT COUNT(*) FROM pt_request_votes 
            WHERE request_id = ? AND vote_type = 'up'
        ");
        $stmt->execute([$requestId]);
        $voteCount = $stmt->fetchColumn();
        
        // 更新需求表中的投票数
        $stmt = $pdo->prepare("
            UPDATE pt_user_requests 
            SET votes = ? 
            WHERE id = ?
        ");
        $stmt->execute([$voteCount, $requestId]);
        
        // 记录用户活动（可选）
        $stmt = $pdo->prepare("
            INSERT INTO pt_member_activity_log (user_id, action, description, ip_address, created_at)
            VALUES (?, 'request_vote', ?, ?, NOW())
        ");
        $stmt->execute([
            $user['id'], 
            "User {$action} request: " . substr($request['title'], 0, 50) . (strlen($request['title']) > 50 ? '...' : ''),
            $_SERVER['REMOTE_ADDR'] ?? ''
        ]);
        
        // 提交事务
        $pdo->commit();
        
        $response['success'] = true;
        $response['voted'] = $voted;
        $response['votes'] = $voteCount;
        $response['message'] = $voted ? 'Vote added successfully' : 'Vote removed successfully';
        
    } catch (Exception $e) {
        $pdo->rollBack();
        throw $e;
    }
    
} catch (Exception $e) {
    // 清理输出缓冲区，确保干净的JSON响应
    if (ob_get_level()) {
        ob_clean();
    }
    
    $response['message'] = $e->getMessage();
    
    // 调试信息
    $response['debug'] = [
        'user_id' => $_SESSION['user_id'] ?? 'unknown',
        'request_id' => $requestId ?? 'unknown',
        'error' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ];
}

// 输出JSON响应
echo json_encode($response);
?>
