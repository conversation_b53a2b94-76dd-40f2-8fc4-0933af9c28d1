<?php
/**
 * Google JWT验证助手类
 * 提供更安全的JWT token验证功能
 */

class GoogleJWTHelper {
    
    private static $googleCertsUrl = 'https://www.googleapis.com/oauth2/v3/certs';
    private static $cacheFile = __DIR__ . '/google_certs_cache.json';
    private static $cacheExpiry = 3600; // 1小时缓存
    
    /**
     * 验证Google JWT token
     * @param string $credential JWT token
     * @param string $clientId Google OAuth Client ID
     * @return array|false 用户信息或false
     */
    public static function verifyGoogleJWT($credential, $clientId) {
        try {
            // 解析JWT token
            $parts = explode('.', $credential);
            if (count($parts) !== 3) {
                return false;
            }
            
            // 解码header和payload
            $header = json_decode(self::base64UrlDecode($parts[0]), true);
            $payload = json_decode(self::base64UrlDecode($parts[1]), true);
            $signature = self::base64UrlDecode($parts[2]);
            
            if (!$header || !$payload) {
                return false;
            }
            
            // 验证基本字段
            if (!isset($payload['email']) || !isset($payload['aud'])) {
                return false;
            }
            
            // 验证audience（客户端ID）
            if ($payload['aud'] !== $clientId) {
                return false;
            }
            
            // 验证过期时间
            if (isset($payload['exp']) && $payload['exp'] < time()) {
                return false;
            }
            
            // 验证issuer
            if (!isset($payload['iss']) || !in_array($payload['iss'], ['accounts.google.com', 'https://accounts.google.com'])) {
                return false;
            }
            
            // 验证签名（可选，需要Google公钥）
            if (isset($header['kid'])) {
                $isValidSignature = self::verifySignature($parts[0] . '.' . $parts[1], $signature, $header['kid']);
                if (!$isValidSignature) {
                    error_log("Google JWT signature verification failed");
                    // 在生产环境中，您可能希望在签名验证失败时返回false
                    // return false;
                }
            }
            
            return $payload;
            
        } catch (Exception $e) {
            error_log("Google JWT verification error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Base64 URL解码
     */
    private static function base64UrlDecode($data) {
        return base64_decode(str_replace(['-', '_'], ['+', '/'], $data));
    }
    
    /**
     * 验证JWT签名
     */
    private static function verifySignature($data, $signature, $keyId) {
        try {
            // 获取Google公钥
            $publicKeys = self::getGooglePublicKeys();
            
            if (!isset($publicKeys[$keyId])) {
                return false;
            }
            
            $publicKey = $publicKeys[$keyId];
            
            // 验证签名
            return openssl_verify($data, $signature, $publicKey, OPENSSL_ALGO_SHA256) === 1;
            
        } catch (Exception $e) {
            error_log("JWT signature verification error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 获取Google公钥（带缓存）
     */
    private static function getGooglePublicKeys() {
        // 检查缓存
        if (file_exists(self::$cacheFile)) {
            $cacheData = json_decode(file_get_contents(self::$cacheFile), true);
            if ($cacheData && isset($cacheData['expires']) && $cacheData['expires'] > time()) {
                return $cacheData['keys'];
            }
        }
        
        // 从Google获取公钥
        $context = stream_context_create([
            'http' => [
                'timeout' => 10,
                'user_agent' => 'Prompt2Tool/1.0'
            ]
        ]);
        
        $response = file_get_contents(self::$googleCertsUrl, false, $context);
        if (!$response) {
            throw new Exception('Failed to fetch Google certificates');
        }
        
        $certs = json_decode($response, true);
        if (!$certs || !isset($certs['keys'])) {
            throw new Exception('Invalid Google certificates response');
        }
        
        // 转换为可用的公钥格式
        $publicKeys = [];
        foreach ($certs['keys'] as $key) {
            if (isset($key['kid']) && isset($key['n']) && isset($key['e'])) {
                $publicKeys[$key['kid']] = self::rsaPublicKeyFromJWK($key);
            }
        }
        
        // 缓存公钥
        $cacheData = [
            'keys' => $publicKeys,
            'expires' => time() + self::$cacheExpiry
        ];
        
        file_put_contents(self::$cacheFile, json_encode($cacheData));
        
        return $publicKeys;
    }
    
    /**
     * 从JWK格式转换为RSA公钥
     */
    private static function rsaPublicKeyFromJWK($jwk) {
        if (!isset($jwk['n']) || !isset($jwk['e'])) {
            throw new Exception('Invalid JWK format');
        }
        
        $n = self::base64UrlDecode($jwk['n']);
        $e = self::base64UrlDecode($jwk['e']);
        
        // 构建RSA公钥
        $modulus = self::base64ToBigInteger($n);
        $exponent = self::base64ToBigInteger($e);
        
        // 简化版本：直接返回PEM格式的公钥
        // 在生产环境中，建议使用专业的JWT库如firebase/jwt
        $pem = "-----BEGIN PUBLIC KEY-----\n";
        $pem .= chunk_split(base64_encode($n . $e), 64);
        $pem .= "-----END PUBLIC KEY-----\n";
        
        return $pem;
    }
    
    /**
     * Base64转大整数（简化版本）
     */
    private static function base64ToBigInteger($base64) {
        return $base64; // 简化实现
    }
    
    /**
     * 清理缓存
     */
    public static function clearCache() {
        if (file_exists(self::$cacheFile)) {
            unlink(self::$cacheFile);
        }
    }
}
?>
