<?php
/**
 * AI分析产品启动的AJAX处理
 * 使用WebContentExtractor和AIProductAnalyzer
 */

// 禁用错误显示，防止HTML错误输出
ini_set('display_errors', 0);
error_reporting(0);

header('Content-Type: application/json');

// 设置错误处理函数
set_error_handler(function($severity, $message) {
    // 忽略HTML解析相关的警告
    if (strpos($message, 'DOMDocument::loadHTML') !== false ||
        strpos($message, 'Tag') !== false ||
        $severity === E_WARNING) {
        return true; // 忽略这些错误，继续执行
    }

    echo json_encode(['success' => false, 'message' => 'Server error occurred. Please try again later.']);
    exit;
});

set_exception_handler(function() {
    echo json_encode(['success' => false, 'message' => 'Server error occurred. Please try again later.']);
    exit;
});

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit;
}

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// 定义根路径和应用初始化标志
define('ROOT_PATH', dirname(__DIR__));
define('APP_INITIALIZED', true);

// 检查用户登录状态
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'Please login to use AI analysis']);
    exit;
}

// 使用统一的数据库连接
try {
    require_once ROOT_PATH . '/includes/database-connection.php';
    $pdo = getDatabaseConnection();
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Database connection failed: ' . $e->getMessage()
    ]);
    exit;
}

// 引入必要的类文件
try {
    require_once ROOT_PATH . '/classes/WebContentExtractor.php';
    require_once ROOT_PATH . '/classes/AIProductAnalyzer.php';
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Failed to load classes: ' . $e->getMessage()
    ]);
    exit;
}

try {
    $url = $_POST['url'] ?? '';

    if (empty($url)) {
        echo json_encode(['success' => false, 'message' => 'URL is required']);
        exit;
    }

    // 清理URL，移除参数
    $parsedUrl = parse_url($url);
    if ($parsedUrl === false) {
        echo json_encode(['success' => false, 'message' => 'Invalid URL format']);
        exit;
    }

    $cleanUrl = $parsedUrl['scheme'] . '://' . $parsedUrl['host'];
    if (!empty($parsedUrl['path']) && $parsedUrl['path'] !== '/') {
        $cleanUrl .= $parsedUrl['path'];
    }
    $url = $cleanUrl;

    // 验证URL格式
    if (!filter_var($url, FILTER_VALIDATE_URL)) {
        echo json_encode(['success' => false, 'message' => 'Invalid URL format']);
        exit;
    }

    // 访问性检查（最佳努力）：使用Googlebot UA，兼容更多站点。探测失败也不阻断流程。
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_TIMEOUT => 20,
        CURLOPT_USERAGENT => 'Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)',
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_MAXREDIRS => 5,
        CURLOPT_ENCODING => '' // 支持gzip/deflate
    ]);

    $htmlProbe = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);

    if ($error || $httpCode < 200 || $httpCode >= 400 || !$htmlProbe) {
        // 不再直接返回错误，记录日志后继续由提取器尝试抓取
        error_log("analyze-launch probe failed ($httpCode): $error");
    }

    // 1. 提取网站内容
    $extractor = new WebContentExtractor();
    $extractedData = $extractor->extractWebsiteData($url);

    if (!$extractedData) {
        echo json_encode([
            'success' => false,
            'message' => 'Failed to extract website content. Please check the URL and try again.'
        ]);
        exit;
    }

    // 2. AI分析（如果失败则使用基础分析）
    $analyzer = new AIProductAnalyzer();

    // 首先尝试使用AI分析，如果失败则回退到基础分析
    $analysisResult = $analyzer->analyzeProduct($extractedData);

    // 如果AI分析失败，使用基础分析作为后备
    if (!$analysisResult) {
        $analysisResult = $analyzer->getBasicAnalysis($extractedData);
    }

    if (!$analysisResult) {
        echo json_encode([
            'success' => false,
            'message' => 'Analysis failed. Please fill the form manually.'
        ]);
        exit;
    }

    // 3. 处理和清理分析结果
    $cleanedResult = [
        'product_name' => $analysisResult['product_name'] ?? '',
        'tagline' => $analysisResult['tagline'] ?? '',
        'description' => $analysisResult['description'] ?? '',
        'category' => $analysisResult['category'] ?? '',
        'tags' => $analysisResult['tags'] ?? [],
        'key_features' => $analysisResult['key_features'] ?? [],
        'target_audience' => $analysisResult['target_audience'] ?? '',
        'use_cases' => $analysisResult['use_cases'] ?? [],
        'pricing_model' => $analysisResult['pricing_model'] ?? '',
        'launch_status' => $analysisResult['launch_status'] ?? '',
        'tech_category' => $analysisResult['tech_category'] ?? '' 
    ];



    echo json_encode([
        'success' => true,
        'data' => $cleanedResult,
        'message' => 'Website analyzed successfully'
    ]);

} catch (Throwable $e) {
    error_log("AI analysis error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'An error occurred during analysis. Please try again or fill the form manually.'
    ]);
}



/**
 * 提取网页主要内容
 */
function extractMainContent($html) {
    // 创建DOM文档
    $dom = new DOMDocument();
    libxml_use_internal_errors(true);
    $dom->loadHTML('<?xml encoding="UTF-8">' . $html);
    libxml_clear_errors();

    // 移除无用元素
    $removeElements = ['script', 'style', 'nav', 'header', 'footer', 'aside', 'iframe', 'noscript'];
    foreach ($removeElements as $tag) {
        $elements = $dom->getElementsByTagName($tag);
        for ($i = $elements->length - 1; $i >= 0; $i--) {
            $element = $elements->item($i);
            if ($element && $element->parentNode) {
                $element->parentNode->removeChild($element);
            }
        }
    }

    // 提取标题
    $title = '';
    $titleElements = $dom->getElementsByTagName('title');
    if ($titleElements->length > 0) {
        $title = trim($titleElements->item(0)->textContent);
    }

    // 提取meta描述
    $description = '';
    $metaElements = $dom->getElementsByTagName('meta');
    foreach ($metaElements as $meta) {
        if ($meta->getAttribute('name') === 'description') {
            $description = trim($meta->getAttribute('content'));
            break;
        }
    }

    // 提取主要内容区域
    $contentSelectors = ['main', 'article', '[role="main"]', '.content', '#content', '.main'];
    $mainText = '';

    foreach ($contentSelectors as $selector) {
        if (strpos($selector, '.') === 0) {
            // 类选择器
            $className = substr($selector, 1);
            $xpath = new DOMXPath($dom);
            $elements = $xpath->query("//*[contains(@class, '$className')]");
        } elseif (strpos($selector, '#') === 0) {
            // ID选择器
            $id = substr($selector, 1);
            $elements = $dom->getElementById($id);
            $elements = $elements ? [$elements] : [];
        } else {
            // 标签选择器
            $elements = $dom->getElementsByTagName($selector);
        }

        if (!empty($elements)) {
            foreach ($elements as $element) {
                $text = trim($element->textContent);
                if (strlen($text) > strlen($mainText)) {
                    $mainText = $text;
                }
            }
            if (!empty($mainText)) break;
        }
    }

    // 如果没有找到主要内容，提取body内容
    if (empty($mainText)) {
        $bodyElements = $dom->getElementsByTagName('body');
        if ($bodyElements->length > 0) {
            $mainText = trim($bodyElements->item(0)->textContent);
        }
    }

    // 清理和压缩文本
    $mainText = preg_replace('/\s+/', ' ', $mainText);
    $mainText = trim($mainText);

    // 限制长度以适应模型token限制
    if (strlen($mainText) > 3000) {
        $mainText = substr($mainText, 0, 3000) . '...';
    }

    // 组合最终内容
    $finalContent = '';
    if ($title) $finalContent .= "Title: $title\n\n";
    if ($description) $finalContent .= "Description: $description\n\n";
    $finalContent .= "Content: $mainText";

    return $finalContent;
}

/**
 * 获取产品发布分类列表
 */
function getLaunchCategories($pdo) {
    $stmt = $pdo->prepare("SELECT slug, name FROM pt_launch_categories WHERE status = 'active' ORDER BY sort_order ASC, name ASC");
    $stmt->execute();
    $dbCategories = $stmt->fetchAll();

    $categories = [];
    foreach ($dbCategories as $cat) {
        $categories[] = $cat['slug'];
    }

    // 添加其他分类
    $categories[] = 'other';

    return $categories;
}

/**
 * 获取AI模型配置
 */
function getGLMConfig($pdo) {
    // 获取bigModel平台信息
    $platformStmt = $pdo->prepare("SELECT * FROM pt_service_platform WHERE code = 'bigmodel' AND is_active = 1");
    $platformStmt->execute();
    $platform = $platformStmt->fetch();

    if (!$platform) {
        return false;
    }

    // 获取免费AI模型信息
    $modelStmt = $pdo->prepare("SELECT * FROM pt_service_model WHERE platform_id = ? AND model_code = 'glm-4.5-flash' AND is_active = 1");
    $modelStmt->execute([$platform['id']]);
    $model = $modelStmt->fetch();

    if (!$model) {
        return false;
    }

    // 获取所有活跃的API密钥
    $keyStmt = $pdo->prepare("SELECT * FROM pt_service_key WHERE platform_id = ? AND is_active = 1 ORDER BY usage_count ASC, RAND()");
    $keyStmt->execute([$platform['id']]);
    $apiKeys = $keyStmt->fetchAll();

    if (empty($apiKeys)) {
        return false;
    }

    // 选择使用次数最少的Key
    $selectedKey = $apiKeys[0];

    // 更新选中Key的使用次数和最后使用时间
    try {
        $updateStmt = $pdo->prepare("UPDATE pt_service_key SET usage_count = usage_count + 1, last_used_at = NOW() WHERE id = ?");
        $updateStmt->execute([$selectedKey['id']]);
    } catch (Exception) {
        // 如果更新失败，不影响主流程
    }

    return [
        'platform' => $platform,
        'model' => $model,
        'api_key' => $selectedKey['api_key'],
        'key_id' => $selectedKey['id'],
        'key_name' => $selectedKey['name'],
        'base_url' => $platform['base_url'],
        'total_keys' => count($apiKeys)
    ];
}

/**
 * 使用AI分析产品发布内容
 */
function analyzeProductLaunch($content, $url, $pdo, $categories) {
    $modelConfig = getGLMConfig($pdo);
    if (!$modelConfig) {
        return false;
    }

    $categoryList = implode(', ', $categories);

    $prompt = "As a professional product analyst, analyze this website and generate product launch information.

\"\"\"
Website URL: {$url}
Website Content: {$content}
\"\"\"

Analysis Steps:
1. Identify the product name and core value proposition
2. Extract key features and benefits
3. Determine target audience and use cases
4. Generate compelling tagline and description

Requirements:
- Product Name: Clear, concise product name
- Tagline: Compelling one-liner describing the product
- Description: Detailed description of features and benefits
- Category: Must be exactly one from the provided list
- Tags: Relevant keywords (array of strings)
- Key Features: Main product features (array of strings)
- Target Audience: Primary user demographics
- Use Cases: How users would use this product (array of strings)
- Pricing Model: free, freemium, paid, or unknown
- Launch Status: launched, beta, coming_soon, or unknown

Available Categories: {$categoryList}

Return ONLY valid JSON format (no other text):
{
    \"product_name\": \"Product name (max 100 characters)\",
    \"tagline\": \"Compelling tagline (max 200 characters)\",
    \"description\": \"Detailed description (max 1000 characters)\",
    \"category\": \"Select exactly one from the category list\",
    \"tags\": [\"tag1\", \"tag2\", \"tag3\"],
    \"key_features\": [\"feature1\", \"feature2\", \"feature3\"],
    \"target_audience\": \"Primary target audience description\",
    \"use_cases\": [\"use case 1\", \"use case 2\"],
    \"pricing_model\": \"free|freemium|paid|unknown\",
    \"launch_status\": \"launched|beta|coming_soon|unknown\"
}";

    $data = [
        'model' => 'glm-4.5-flash',
        'messages' => [
            [
                'role' => 'system',
                'content' => 'You are a professional product analyst specializing in product launch analysis. You excel at extracting key information from website content and creating structured product information. Always respond with structured JSON output as requested.'
            ],
            [
                'role' => 'user',
                'content' => $prompt
            ]
        ],
        'thinking' => [
            'type' => 'disabled'
        ],
        'max_tokens' => 1500,
        'temperature' => 0.7
    ];

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $modelConfig['base_url'] . '/chat/completions');
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Authorization: Bearer ' . $modelConfig['api_key']
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curlError = curl_error($ch);
    curl_close($ch);

    if ($httpCode !== 200 || !$response) {
        error_log("API call failed - HTTP Code: $httpCode, cURL Error: $curlError, Response: " . substr($response, 0, 200));
        return false;
    }

    $result = json_decode($response, true);
    if (!$result) {
        error_log("Failed to parse JSON response: " . substr($response, 0, 200));
        return false;
    }

    if (isset($result['error'])) {
        error_log("API returned error: " . json_encode($result['error']));
        return false;
    }

    if (!isset($result['choices'][0]['message']['content'])) {
        error_log("Unexpected API response structure: " . json_encode($result));
        return false;
    }

    $content = trim($result['choices'][0]['message']['content']);

    // 尝试解析JSON响应
    $analysisData = json_decode($content, true);
    if (!$analysisData) {
        // 如果直接解析失败，尝试提取JSON部分
        if (preg_match('/\{.*\}/s', $content, $matches)) {
            $analysisData = json_decode($matches[0], true);
        }
    }

    if (!$analysisData || !isset($analysisData['product_name'])) {
        return false;
    }

    // 验证和清理数据
    $analysisData['product_name'] = substr(trim($analysisData['product_name']), 0, 100);
    $analysisData['tagline'] = substr(trim($analysisData['tagline'] ?? ''), 0, 200);
    $analysisData['description'] = substr(trim($analysisData['description'] ?? ''), 0, 1000);

    // 验证分类
    if (!in_array($analysisData['category'] ?? '', $categories)) {
        $analysisData['category'] = 'other';
    }

    // 确保数组字段是数组
    $analysisData['tags'] = is_array($analysisData['tags'] ?? []) ? $analysisData['tags'] : [];
    $analysisData['key_features'] = is_array($analysisData['key_features'] ?? []) ? $analysisData['key_features'] : [];
    $analysisData['use_cases'] = is_array($analysisData['use_cases'] ?? []) ? $analysisData['use_cases'] : [];

    return $analysisData;
}
?>