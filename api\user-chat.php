<?php
/**
 * User Chat API Endpoint
 * 处理用户中心的聊天请求，使用aiHubMix GPT-4o Mini模型
 */

// 安全检查
if (!defined('SECURE_ACCESS')) {
    define('SECURE_ACCESS', true);
}

// 定义根目录
define('ROOT_PATH', dirname(__DIR__));

// 加载应用初始化
require_once ROOT_PATH . '/app/init.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

// 检查用户登录状态
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode([
        'success' => false,
        'error' => 'User not logged in'
    ]);
    exit;
}

// 使用统一的数据库连接
require_once ROOT_PATH . '/includes/database-connection.php';
require_once ROOT_PATH . '/classes/QuotaManager.php';



try {

    // 获取请求数据
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);

    if (!$data) {
        throw new Exception('Invalid JSON data');
    }

    // 验证必需参数
    if (!isset($data['messages']) || !is_array($data['messages'])) {
        throw new Exception('Missing required parameter: messages');
    }



    // 智能选择aiHubMix API密钥（负载均衡）
    $apiKeyConfig = selectOptimalAPIKey($pdo);

    if (!$apiKeyConfig) {
        throw new Exception('No active API key found');
    }

    // 始终使用流式输出
        // 设置流式输出头
        header('Content-Type: text/event-stream');
        header('Cache-Control: no-cache');
        header('Connection: keep-alive');
        header('Access-Control-Allow-Origin: *');

        try {
            $success = callGPTModelWithRetry($pdo, $data['messages'], $data['max_tokens'] ?? 1000, $data['temperature'] ?? 0.7);

            if (!$success) {
                echo "data: " . json_encode(['type' => 'error', 'message' => 'Failed to get response from AI model']) . "\n\n";
                flush();
            }

            // 更新使用统计
            updateKeyUsage($pdo, $apiKeyConfig['key_id']);

        } catch (Exception $e) {
            echo "data: " . json_encode(['type' => 'error', 'message' => $e->getMessage()]) . "\n\n";
            flush();
        }

} catch (Exception $e) {

    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

/**
 * 智能选择最优的API Key（负载均衡）
 */
function selectOptimalAPIKey($pdo) {
    try {
        // 获取所有活跃的aiHubMix API Keys，按使用次数排序
        $stmt = $pdo->prepare("
            SELECT
                ak.id as key_id,
                ak.api_key,
                ak.name as key_name,
                ak.usage_count
            FROM pt_service_key ak
            JOIN pt_service_platform ap ON ak.platform_id = ap.id
            WHERE ap.code = 'aihubmix' AND ak.is_active = 1
            ORDER BY ak.usage_count ASC, RAND()
            LIMIT 1
        ");
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_ASSOC);

    } catch (PDOException) {
        return null;
    }
}

/**
 * 智能选择最优的API Key（排除指定的Key ID）
 */
function selectOptimalAPIKeyExcluding($pdo, $excludeKeyIds = []) {
    try {
        // 构建排除条件
        $excludeCondition = '';
        $params = [];
        
        if (!empty($excludeKeyIds)) {
            $placeholders = str_repeat('?,', count($excludeKeyIds) - 1) . '?';
            $excludeCondition = " AND ak.id NOT IN ($placeholders)";
            $params = $excludeKeyIds;
        }
        
        // 获取可用的API密钥（排除已使用的）
        $sql = "
            SELECT 
                ak.id as key_id,
                ak.api_key,
                ak.name as key_name,
                ak.usage_count
            FROM pt_service_key ak
            JOIN pt_service_platform ap ON ak.platform_id = ap.id
            WHERE ap.code = 'aihubmix' AND ak.is_active = 1" . $excludeCondition . "
            ORDER BY ak.usage_count ASC, RAND()
            LIMIT 1
        ";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        return $stmt->fetch(PDO::FETCH_ASSOC);

    } catch (PDOException) {
        return null;
    }
}

/**
 * 获取AiHubMix的APP-Code配置
 */
function getAppCode() {
    global $pdo;

    try {
        $stmt = $pdo->prepare("
            SELECT config_value
            FROM pt_service_config sc
            JOIN pt_service_platform sp ON sc.platform_id = sp.id
            WHERE sp.code = 'aihubmix' AND sc.config_key = 'app_code'
        ");
        $stmt->execute();
        $appCodeData = $stmt->fetch();

        // 设置APP-Code，优先使用数据库配置，备用默认值
        return $appCodeData ? $appCodeData['config_value'] : 'KXTM3281';

    } catch (PDOException) {
        // 数据库查询失败时使用默认值
        return 'KXTM3281';
    }
}

/**
 * 更新API Key使用统计
 */
function updateKeyUsage($pdo, $keyId) {
    try {
        $stmt = $pdo->prepare("
            UPDATE pt_service_key 
            SET usage_count = usage_count + 1, 
                last_used_at = NOW() 
            WHERE id = ?
        ");
        $stmt->execute([$keyId]);
    } catch (PDOException) {
        // 更新失败不影响主流程
    }
}


/**
 * 调用GPT-4o Mini模型（流式输出）
 */
function callGPTModel($apiKey, $messages, $maxTokens = 1000, $temperature = 0.7) {
    $url = 'https://api.aihubmix.com/v1/chat/completions';

    $payload = [
        'model' => 'gpt-4o-mini',
        'messages' => $messages,
        'max_tokens' => $maxTokens,
        'temperature' => $temperature,
        'stream' => true  // 启用流式输出
    ];

    // 获取APP-Code用于10%折扣
    $appCode = getAppCode();

    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => false,  // 改为false以支持流式输出
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => json_encode($payload),
        CURLOPT_HTTPHEADER => [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $apiKey,
            'APP-Code: ' . (!empty($appCode) ? $appCode : 'KXTM3281')
        ],
        CURLOPT_TIMEOUT => 30,  // 30秒超时，流式输出不需要长时间等待
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_WRITEFUNCTION => function($ch, $data) {
            // $ch parameter is required by cURL callback but not used
            // 直接转发流式数据，与chat-completion.php相同的简单方法
            echo $data;
            ob_flush();
            flush();
            return strlen($data);
        }
    ]);

    // 流式输出头已在前面设置，这里不需要重复设置

    curl_exec($ch);  // 流式输出通过WRITEFUNCTION处理，不需要返回值
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);

    if ($error) {
        throw new Exception("Network error occurred: " . $error);
    }

    if ($httpCode !== 200) {
        throw new Exception("Service temporarily unavailable (HTTP $httpCode)");
    }

    return true;
}

/**
 * 带重试机制的GPT模型调用
 */
function callGPTModelWithRetry($pdo, $messages, $maxTokens = 1000, $temperature = 0.7, $maxRetries = 3) {
    $attemptCount = 0;
    $usedKeyIds = [];

    while ($attemptCount < $maxRetries) {
        $attemptCount++;

        // 获取配置（排除已经尝试过的Key）
        $apiKeyConfig = selectOptimalAPIKeyExcluding($pdo, $usedKeyIds);
        if (!$apiKeyConfig) {
            break;
        }

        // 记录使用的Key ID
        $usedKeyIds[] = $apiKeyConfig['key_id'];

        try {
            // 尝试调用API（流式输出）
            $result = callGPTModel($apiKeyConfig['api_key'], $messages, $maxTokens, $temperature);

            if ($result) {
                // 成功，更新使用统计并返回结果
                updateKeyUsage($pdo, $apiKeyConfig['key_id']);
                return $result;
            }
        } catch (Exception) {
            // 继续尝试下一个Key
        }
    }

    return false;
}
?>
