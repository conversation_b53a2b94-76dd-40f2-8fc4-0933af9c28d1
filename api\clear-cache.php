<?php
/**
 * 清空缓存文件API
 */

// 定义根目录常量
define('ROOT_PATH', dirname(__DIR__));
define('APP_INITIALIZED', true);

// 检查登录状态
session_start();
if (!isset($_SESSION['admin_logged_in']) || !$_SESSION['admin_logged_in']) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit;
}

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit;
}

$deletedCount = 0;

try {
    // 清理temp/preview目录，保留index.php
    $previewDir = ROOT_PATH . '/temp/preview';
    if (is_dir($previewDir)) {
        $files = scandir($previewDir);
        foreach ($files as $file) {
            if ($file !== '.' && $file !== '..' && $file !== 'index.php') {
                $filePath = $previewDir . '/' . $file;
                if (is_file($filePath)) {
                    if (unlink($filePath)) {
                        $deletedCount++;
                    }
                }
            }
        }
    }

    echo json_encode([
        'success' => true,
        'deleted_count' => $deletedCount,
        'message' => "Successfully cleared {$deletedCount} cache files"
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => 'Error clearing cache: ' . $e->getMessage(),
        'deleted_count' => $deletedCount
    ]);
}
?>
