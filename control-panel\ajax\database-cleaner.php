<?php
/**
 * Database Cleanup Tool AJAX Handler
 * Used to clean logs, sessions and notification data
 */

// Set response headers
header('Content-Type: application/json; charset=utf-8');
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

// Clean output buffer
if (ob_get_level()) {
    ob_clean();
}

// Initialize response
$response = [
    'success' => false,
    'message' => '',
    'details' => ''
];

try {
    // Check request method
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Invalid request method');
    }

    // Get request data
    $input = json_decode(file_get_contents('php://input'), true);
    $action = $input['action'] ?? '';

    if (empty($action)) {
        throw new Exception('Missing action parameter');
    }

    // Include necessary files
    define('ROOT_PATH', dirname(dirname(__DIR__)));
    require_once ROOT_PATH . '/app/init.php';
    require_once ROOT_PATH . '/includes/database-connection.php';

    // Check admin permissions (session already started in init.php)
    if (!isset($_SESSION['manager_id']) && !isset($_SESSION['admin_id']) && !isset($_SESSION['admin_logged_in'])) {
        throw new Exception('Unauthorized access');
    }

    global $pdo;
    $cleanedTables = [];
    $totalDeleted = 0;

    // Start transaction
    $pdo->beginTransaction();

    switch ($action) {
        case 'logs':
            $result = cleanLogData($pdo);
            break;
        case 'sessions':
            $result = cleanSessionData($pdo);
            break;
        case 'notifications':
            $result = cleanNotificationData($pdo);
            break;
        case 'all':
            $logResult = cleanLogData($pdo);
            $sessionResult = cleanSessionData($pdo);
            $notificationResult = cleanNotificationData($pdo);

            $result = [
                'deleted' => $logResult['deleted'] + $sessionResult['deleted'] + $notificationResult['deleted'],
                'tables' => array_merge($logResult['tables'], $sessionResult['tables'], $notificationResult['tables'])
            ];
            break;
        default:
            throw new Exception('Invalid action: ' . $action);
    }

    // Commit transaction
    $pdo->commit();

    // Log admin action
    logAdminAction($pdo, $action, $result);

    $response['success'] = true;
    $response['message'] = 'Database cleanup completed successfully!';
    $response['details'] = "Deleted {$result['deleted']} records from " . count($result['tables']) . " tables:\n" . implode("\n", $result['tables']);

} catch (Exception $e) {
    // Rollback transaction
    if (isset($pdo) && $pdo->inTransaction()) {
        $pdo->rollBack();
    }

    $response['message'] = $e->getMessage();
    $response['debug'] = [
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'trace' => $e->getTraceAsString()
    ];
    error_log("Database cleaner error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine());
}

// Output JSON response
echo json_encode($response);

/**
 * Clean log data
 */
function cleanLogData($pdo) {
    $tables = [];
    $totalDeleted = 0;

    // 获取所有存在的表
    $existingTables = [];
    $stmt = $pdo->query("SHOW TABLES");
    while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
        $existingTables[] = $row[0];
    }

    // Clean admin activity logs
    if (in_array('pt_activity_log', $existingTables)) {
        $stmt = $pdo->prepare("DELETE FROM pt_activity_log");
        $stmt->execute();
        $deleted = $stmt->rowCount();
        if ($deleted > 0) {
            $tables[] = "pt_activity_log: {$deleted} records";
            $totalDeleted += $deleted;
        }
    }

    // Clean user activity logs
    if (in_array('pt_member_activity_log', $existingTables)) {
        $stmt = $pdo->prepare("DELETE FROM pt_member_activity_log");
        $stmt->execute();
        $deleted = $stmt->rowCount();
        if ($deleted > 0) {
            $tables[] = "pt_member_activity_log: {$deleted} records";
            $totalDeleted += $deleted;
        }
    }

    // Clean API access logs
    if (in_array('pt_api_access_logs', $existingTables)) {
        $stmt = $pdo->prepare("DELETE FROM pt_api_access_logs");
        $stmt->execute();
        $deleted = $stmt->rowCount();
        if ($deleted > 0) {
            $tables[] = "pt_api_access_logs: {$deleted} records";
            $totalDeleted += $deleted;
        }
    }

    // Clean analytics events
    if (in_array('pt_analytics_event', $existingTables)) {
        $stmt = $pdo->prepare("DELETE FROM pt_analytics_event");
        $stmt->execute();
        $deleted = $stmt->rowCount();
        if ($deleted > 0) {
            $tables[] = "pt_analytics_event: {$deleted} records";
            $totalDeleted += $deleted;
        }
    }

    // Clean old API usage statistics (keep current month data)
    if (in_array('pt_api_usage', $existingTables)) {
        // 只删除2个月前的数据，保留当前月和上个月的数据
        $stmt = $pdo->prepare("DELETE FROM pt_api_usage WHERE created_at < DATE_SUB(NOW(), INTERVAL 2 MONTH)");
        $stmt->execute();
        $deleted = $stmt->rowCount();
        if ($deleted > 0) {
            $tables[] = "pt_api_usage (old records): {$deleted} records";
            $totalDeleted += $deleted;
        }
    }

    // Clean tool usage records
    if (in_array('pt_tool_usage', $existingTables)) {
        $stmt = $pdo->prepare("DELETE FROM pt_tool_usage");
        $stmt->execute();
        $deleted = $stmt->rowCount();
        if ($deleted > 0) {
            $tables[] = "pt_tool_usage: {$deleted} records";
            $totalDeleted += $deleted;
        }
    }

    // Clean API rate limit records
    if (in_array('pt_api_rate_limits', $existingTables)) {
        $stmt = $pdo->prepare("DELETE FROM pt_api_rate_limits");
        $stmt->execute();
        $deleted = $stmt->rowCount();
        if ($deleted > 0) {
            $tables[] = "pt_api_rate_limits: {$deleted} records";
            $totalDeleted += $deleted;
        }
    }

    return ['deleted' => $totalDeleted, 'tables' => $tables];
}

/**
 * Clean session data
 */
function cleanSessionData($pdo) {
    $tables = [];
    $totalDeleted = 0;

    // 获取所有存在的表
    $existingTables = [];
    $stmt = $pdo->query("SHOW TABLES");
    while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
        $existingTables[] = $row[0];
    }

    // Clean admin sessions
    if (in_array('pt_manager_session', $existingTables)) {
        $stmt = $pdo->prepare("DELETE FROM pt_manager_session");
        $stmt->execute();
        $deleted = $stmt->rowCount();
        if ($deleted > 0) {
            $tables[] = "pt_manager_session: {$deleted} records";
            $totalDeleted += $deleted;
        }
    }

    // Clean user sessions
    if (in_array('pt_member_sessions', $existingTables)) {
        $stmt = $pdo->prepare("DELETE FROM pt_member_sessions");
        $stmt->execute();
        $deleted = $stmt->rowCount();
        if ($deleted > 0) {
            $tables[] = "pt_member_sessions: {$deleted} records";
            $totalDeleted += $deleted;
        }
    }

    // Clean admin remember tokens
    if (in_array('pt_manager_token', $existingTables)) {
        $stmt = $pdo->prepare("DELETE FROM pt_manager_token");
        $stmt->execute();
        $deleted = $stmt->rowCount();
        if ($deleted > 0) {
            $tables[] = "pt_manager_token: {$deleted} records";
            $totalDeleted += $deleted;
        }
    }

    // Clean user tokens
    if (in_array('pt_member_tokens', $existingTables)) {
        $stmt = $pdo->prepare("DELETE FROM pt_member_tokens");
        $stmt->execute();
        $deleted = $stmt->rowCount();
        if ($deleted > 0) {
            $tables[] = "pt_member_tokens: {$deleted} records";
            $totalDeleted += $deleted;
        }
    }

    return ['deleted' => $totalDeleted, 'tables' => $tables];
}

/**
 * Clean notification data
 */
function cleanNotificationData($pdo) {
    $tables = [];
    $totalDeleted = 0;

    // Clean user notifications
    $stmt = $pdo->prepare("DELETE FROM pt_member_notifications");
    $stmt->execute();
    $deleted = $stmt->rowCount();
    if ($deleted > 0) {
        $tables[] = "pt_member_notifications: {$deleted} records";
        $totalDeleted += $deleted;
    }

    // Clean contact messages
    $stmt = $pdo->prepare("DELETE FROM pt_contact_message");
    $stmt->execute();
    $deleted = $stmt->rowCount();
    if ($deleted > 0) {
        $tables[] = "pt_contact_message: {$deleted} records";
        $totalDeleted += $deleted;
    }

    return ['deleted' => $totalDeleted, 'tables' => $tables];
}

/**
 * Log admin action
 */
function logAdminAction($pdo, $action, $result) {
    try {
        // 检查表是否存在
        $stmt = $pdo->query("SHOW TABLES LIKE 'pt_activity_log'");
        if ($stmt->rowCount() == 0) {
            return; // 表不存在，跳过日志记录
        }

        $managerId = $_SESSION['manager_id'] ?? $_SESSION['admin_id'] ?? 1;
        $description = "Database cleanup - Action: {$action}, Deleted: {$result['deleted']} records";

        $stmt = $pdo->prepare("
            INSERT INTO pt_activity_log (manager_id, action, description, ip_address, user_agent, created_at)
            VALUES (?, ?, ?, ?, ?, NOW())
        ");

        $stmt->execute([
            $managerId,
            'database_cleanup',
            $description,
            $_SERVER['REMOTE_ADDR'] ?? null,
            $_SERVER['HTTP_USER_AGENT'] ?? null
        ]);
    } catch (Exception $e) {
        // Log failure doesn't affect main operation
        error_log("Failed to log admin action: " . $e->getMessage());
    }
}
?>
