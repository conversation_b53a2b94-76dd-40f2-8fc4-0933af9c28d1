<?php
/**
 * 产品启动提交的AJAX处理
 */

header('Content-Type: application/json');

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit;
}

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// 定义根路径
define('ROOT_PATH', dirname(__DIR__));

// 检查用户登录状态
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'Please login to submit a product']);
    exit;
}

// 使用统一的数据库连接
require_once __DIR__ . '/includes/ajax-database.php';
require_once ROOT_PATH . '/classes/QuotaManager.php';

try {

    // 验证必填字段
    $requiredFields = [
        'product_name' => 'Product Name',
        'website_url' => 'Website URL',
        'tagline' => 'Tagline',
        'description' => 'Description',
        'category' => 'Category'
        // launch_status 不再验证，强制设置为 coming-soon
    ];

    foreach ($requiredFields as $field => $label) {
        if (empty($_POST[$field])) {
            echo json_encode(['success' => false, 'message' => "$label is required"]);
            exit;
        }
    }

    // 强制设置launch_status为coming-soon，防止前端篡改
    $_POST['launch_status'] = 'coming-soon';

    // 验证URL格式
    if (!filter_var($_POST['website_url'], FILTER_VALIDATE_URL)) {
        echo json_encode(['success' => false, 'message' => 'Invalid website URL format']);
        exit;
    }

    // 检查配额是否足够
    $quotaManager = new QuotaManager($pdo);
    $userId = $_SESSION['user_id'];

    if (!$quotaManager->hasEnoughQuota($userId, 'submit_launch')) {
        $message = $quotaManager->getInsufficientQuotaMessage('submit_launch');
        echo json_encode(['success' => false, 'message' => $message]);
        exit;
    }

    // 验证分类是否存在
    $categoryStmt = $pdo->prepare("SELECT id FROM pt_launch_categories WHERE slug = ? AND is_active = 1");
    $categoryStmt->execute([$_POST['category']]);
    if (!$categoryStmt->fetch()) {
        echo json_encode(['success' => false, 'message' => 'Invalid category selected']);
        exit;
    }

    // 生成唯一的slug
    $baseSlug = generateSlug($_POST['product_name']);
    $slug = $baseSlug;
    $counter = 1;
    
    while (true) {
        $slugCheckStmt = $pdo->prepare("SELECT id FROM pt_product_launches WHERE slug = ?");
        $slugCheckStmt->execute([$slug]);
        if (!$slugCheckStmt->fetch()) {
            break;
        }
        $slug = $baseSlug . '-' . $counter;
        $counter++;
    }

    // 处理Logo上传
    $logoUrl = null;
    if (isset($_FILES['logo']) && $_FILES['logo']['error'] === UPLOAD_ERR_OK) {
        $logoUrl = handleLogoUpload($_FILES['logo'], $_POST['product_name']);
        if (!$logoUrl) {
            echo json_encode(['success' => false, 'message' => 'Failed to upload logo']);
            exit;
        }
    }

    // 处理Screenshots上传
    $screenshotsUrls = [];
    if (isset($_FILES['screenshots'])) {
        foreach ($_FILES['screenshots']['tmp_name'] as $index => $tmpName) {
            if ($_FILES['screenshots']['error'][$index] === UPLOAD_ERR_OK) {
                $screenshotFile = [
                    'name' => $_FILES['screenshots']['name'][$index],
                    'type' => $_FILES['screenshots']['type'][$index],
                    'tmp_name' => $tmpName,
                    'size' => $_FILES['screenshots']['size'][$index]
                ];

                $screenshotUrl = handleScreenshotUpload($screenshotFile, $_POST['product_name'], $index);
                if ($screenshotUrl) {
                    $screenshotsUrls[] = $screenshotUrl;
                }
            }
        }
    }

    // 处理JSON字段
    $tags = !empty($_POST['tags']) ? json_decode($_POST['tags'], true) : [];
    $keyFeatures = !empty($_POST['key_features']) ? json_decode($_POST['key_features'], true) : [];
    $useCases = !empty($_POST['use_cases']) ? json_decode($_POST['use_cases'], true) : [];

    // 处理社交链接
    $socialLinks = [];
    $socialFields = ['social_twitter', 'social_linkedin', 'social_github', 'social_website'];
    foreach ($socialFields as $field) {
        if (!empty($_POST[$field]) && filter_var($_POST[$field], FILTER_VALIDATE_URL)) {
            $socialLinks[str_replace('social_', '', $field)] = $_POST[$field];
        }
    }

    // 开始事务
    $pdo->beginTransaction();

    // 插入产品数据
    $insertStmt = $pdo->prepare("
        INSERT INTO pt_product_launches (
            user_id, name, slug, tagline, description, website_url, logo_url, screenshots,
            category, tech_category, tags, key_features, target_audience, use_cases,
            pricing_model, launch_status, social_links, video_tutorial_url, status, created_at
        ) VALUES (
            ?, ?, ?, ?, ?, ?, ?, ?,
            ?, ?, ?, ?, ?, ?,
            ?, ?, ?, ?, 'pending', NOW()
        )
    ");

    $insertStmt->execute([
        $_SESSION['user_id'],
        $_POST['product_name'],
        $slug,
        $_POST['tagline'],
        $_POST['description'],
        $_POST['website_url'],
        $logoUrl,
        json_encode($screenshotsUrls),
        $_POST['category'],
        $_POST['tech_category'] ?? null,
        json_encode($tags),
        json_encode($keyFeatures),
        $_POST['target_audience'] ?? null,
        json_encode($useCases),
        $_POST['pricing_model'] ?? 'unknown',
        $_POST['launch_status'],
        json_encode($socialLinks),
        !empty($_POST['video_tutorial_url']) ? $_POST['video_tutorial_url'] : null
    ]);

    $launchId = $pdo->lastInsertId();

    // 提交事务
    $pdo->commit();

    // 提交成功，扣除配额
    $quotaResult = $quotaManager->consumeQuota($userId, 'submit_launch', 'Product Launch Submission');
    if (!$quotaResult['success']) {
        error_log("Failed to consume quota for user $userId: " . $quotaResult['error']);
    }

    echo json_encode([
        'success' => true,
        'message' => 'Product submitted successfully! It will be reviewed by our team.',
        'launch_id' => $launchId,
        'slug' => $slug
    ]);

} catch (Exception $e) {
    if (isset($pdo)) {
        $pdo->rollBack();
    }
    
    error_log("Submit launch error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'An error occurred while submitting your product. Please try again.'
    ]);
}

/**
 * 生成URL友好的slug
 */
function generateSlug($text) {
    // 转换为小写
    $slug = strtolower($text);
    
    // 替换特殊字符
    $slug = preg_replace('/[^a-z0-9\s-]/', '', $slug);
    
    // 替换空格和多个连字符为单个连字符
    $slug = preg_replace('/[\s-]+/', '-', $slug);
    
    // 移除首尾连字符
    $slug = trim($slug, '-');
    
    // 限制长度
    $slug = substr($slug, 0, 100);
    
    return $slug ?: 'product-' . time();
}

/**
 * 生成安全的文件名
 */
function generateSafeFilename($productName, $type, $extension, $index = null) {
    // 清理产品名称，只保留字母、数字、空格和连字符
    $safeName = preg_replace('/[^a-zA-Z0-9\s\-_]/', '', $productName);
    // 将空格替换为连字符
    $safeName = preg_replace('/\s+/', '-', trim($safeName));
    // 转换为小写
    $safeName = strtolower($safeName);
    // 限制长度
    $safeName = substr($safeName, 0, 50);

    // 如果清理后为空，使用随机生成的备用方案
    if (empty($safeName)) {
        $safeName = 'product-' . uniqid();
    }

    // 生成文件名
    if ($type === 'logo') {
        return $safeName . '-logo.' . $extension;
    } else {
        $suffix = $index !== null ? '-screenshot-' . ($index + 1) : '-screenshot';
        return $safeName . $suffix . '.' . $extension;
    }
}

/**
 * 处理Logo上传
 */
function handleLogoUpload($file, $productName = '') {
    // 验证文件类型
    $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (!in_array($file['type'], $allowedTypes)) {
        return false;
    }

    // 验证文件大小 (2MB)
    if ($file['size'] > 2 * 1024 * 1024) {
        return false;
    }

    // 创建上传目录
    $uploadDir = dirname(__DIR__) . '/public/uploads/launches/logos/';
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }

    // 生成基于产品名称的文件名
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = generateSafeFilename($productName, 'logo', $extension);

    // 如果文件已存在，添加时间戳确保唯一性
    if (file_exists($uploadDir . $filename)) {
        $nameWithoutExt = pathinfo($filename, PATHINFO_FILENAME);
        $filename = $nameWithoutExt . '-' . time() . '.' . $extension;
    }

    $filepath = $uploadDir . $filename;

    // 移动文件
    if (move_uploaded_file($file['tmp_name'], $filepath)) {
        return '/uploads/launches/logos/' . $filename;
    }

    return false;
}

/**
 * 处理Screenshot上传
 */
function handleScreenshotUpload($file, $productName = '', $index = 0) {
    // 验证文件类型
    $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (!in_array($file['type'], $allowedTypes)) {
        return false;
    }

    // 验证文件大小 (5MB)
    if ($file['size'] > 5 * 1024 * 1024) {
        return false;
    }

    // 验证图片尺寸
    $imageInfo = getimagesize($file['tmp_name']);
    if ($imageInfo === false) {
        return false;
    }

    $width = $imageInfo[0];
    $height = $imageInfo[1];

    // 截图建议尺寸：最小400x300
    if ($width < 400 || $height < 300) {
        return false;
    }

    // 创建上传目录
    $uploadDir = dirname(__DIR__) . '/public/uploads/launches/screenshots/';
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }

    // 生成基于产品名称的文件名
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = generateSafeFilename($productName, 'screenshot', $extension, $index);

    // 如果文件已存在，添加时间戳确保唯一性
    if (file_exists($uploadDir . $filename)) {
        $nameWithoutExt = pathinfo($filename, PATHINFO_FILENAME);
        $filename = $nameWithoutExt . '-' . time() . '.' . $extension;
    }

    $filepath = $uploadDir . $filename;

    // 移动文件
    if (move_uploaded_file($file['tmp_name'], $filepath)) {
        return '/uploads/launches/screenshots/' . $filename;
    }

    return false;
}
?>
