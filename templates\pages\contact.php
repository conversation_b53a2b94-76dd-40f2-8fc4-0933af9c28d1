<?php
/**
 * Contact Us Page
 * 联系我们页面 - 提供多种联系方式和反馈表单
 */

// 设置当前页面
$currentPage = 'contact';

// 包含动态SEO助手
require_once ROOT_PATH . '/app/helpers/dynamic-seo.php';

// 包含社交媒体助手
require_once ROOT_PATH . '/app/helpers/social-media.php';

// 获取动态SEO数据
$seoData = getDynamicSEOData('contact');

// 获取社交媒体链接
$socialLinks = getSocialMediaLinks();

// 包含公共头部
include_once ROOT_PATH . '/templates/layout/header.php';
?>

<!-- 主要内容区域 -->
<main class="min-h-screen bg-black text-white">
    <!-- Hero Section -->
    <section class="py-12 bg-gradient-to-b from-gray-900 to-black">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <h1 class="text-4xl md:text-5xl font-bold mb-4 text-center">Contact Us</h1>
            <p class="text-xl text-gray-300 text-center leading-relaxed">
                We'd love to hear from you. Get in touch with our team for support, feedback, or business inquiries.
            </p>
        </div>
    </section>

    <!-- Contact Methods -->
    <section class="py-8 bg-black">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 class="text-3xl font-bold mb-12 text-center">Get in Touch</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
                <!-- Email Support -->
                <div class="bg-gray-900 border border-gray-800 p-8 text-center">
                    <div class="text-accent text-4xl mb-6">📧</div>
                    <h3 class="text-xl font-semibold mb-4">Email Support</h3>
                    <p class="text-gray-400 mb-6">For general inquiries and support</p>
                    <a href="mailto:<EMAIL>" class="text-accent hover:underline text-lg">
                        <EMAIL>
                    </a>
                    <p class="text-sm text-gray-500 mt-2">Response within 24 hours</p>
                </div>

                <!-- Business Inquiries -->
                <div class="bg-gray-900 border border-gray-800 p-8 text-center">
                    <div class="text-accent text-4xl mb-6">💼</div>
                    <h3 class="text-xl font-semibold mb-4">Business Inquiries</h3>
                    <p class="text-gray-400 mb-6">For partnerships and business opportunities</p>
                    <a href="mailto:<EMAIL>" class="text-accent hover:underline text-lg">
                        <EMAIL>
                    </a>
                    <p class="text-sm text-gray-500 mt-2">Response within 48 hours</p>
                </div>

                <!-- Technical Support -->
                <div class="bg-gray-900 border border-gray-800 p-8 text-center">
                    <div class="text-accent text-4xl mb-6">🔧</div>
                    <h3 class="text-xl font-semibold mb-4">Technical Support</h3>
                    <p class="text-gray-400 mb-6">For technical issues and bug reports</p>
                    <a href="mailto:<EMAIL>" class="text-accent hover:underline text-lg">
                        <EMAIL>
                    </a>
                    <p class="text-sm text-gray-500 mt-2">Response within 12 hours</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Form -->
    <section class="py-16 bg-gray-900">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold mb-4">Send us a Message</h2>
                <p class="text-gray-300">Fill out the form below and we'll get back to you as soon as possible.</p>
            </div>

            <form id="contactForm" class="space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Name -->
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-300 mb-2">
                            Full Name *
                        </label>
                        <input 
                            type="text" 
                            id="name" 
                            name="name" 
                            required
                            class="w-full px-4 py-3 bg-black border border-gray-700 text-white placeholder-gray-400 focus:border-accent focus:outline-none"
                            placeholder="Your full name"
                        >
                    </div>

                    <!-- Email -->
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-300 mb-2">
                            Email Address *
                        </label>
                        <input 
                            type="email" 
                            id="email" 
                            name="email" 
                            required
                            class="w-full px-4 py-3 bg-black border border-gray-700 text-white placeholder-gray-400 focus:border-accent focus:outline-none"
                            placeholder="<EMAIL>"
                        >
                    </div>
                </div>

                <!-- Subject -->
                <div>
                    <label for="subject" class="block text-sm font-medium text-gray-300 mb-2">
                        Subject *
                    </label>
                    <select 
                        id="subject" 
                        name="subject" 
                        required
                        class="w-full px-4 py-3 bg-black border border-gray-700 text-white focus:border-accent focus:outline-none"
                    >
                        <option value="">Select a subject</option>
                        <option value="general">General Inquiry</option>
                        <option value="support">Technical Support</option>
                        <option value="feature">Feature Request</option>
                        <option value="bug">Bug Report</option>
                        <option value="business">Business Partnership</option>
                        <option value="feedback">Feedback & Suggestions</option>
                        <option value="other">Other</option>
                    </select>
                </div>

                <!-- Message -->
                <div>
                    <label for="message" class="block text-sm font-medium text-gray-300 mb-2">
                        Message *
                    </label>
                    <textarea 
                        id="message" 
                        name="message" 
                        rows="6" 
                        required
                        class="w-full px-4 py-3 bg-black border border-gray-700 text-white placeholder-gray-400 focus:border-accent focus:outline-none resize-vertical"
                        placeholder="Please describe your inquiry in detail..."
                    ></textarea>
                </div>

                <!-- Privacy Notice -->
                <div class="flex items-start space-x-3">
                    <input 
                        type="checkbox" 
                        id="privacy" 
                        name="privacy" 
                        required
                        class="mt-1 w-4 h-4 text-accent bg-black border-gray-700 focus:ring-accent focus:ring-2"
                    >
                    <label for="privacy" class="text-sm text-gray-300">
                        I agree to the <a href="/privacy" class="text-accent hover:underline">Privacy Policy</a> 
                        and consent to the processing of my personal data for the purpose of responding to my inquiry. *
                    </label>
                </div>

                <!-- Submit Button -->
                <div class="text-center">
                    <button 
                        type="submit" 
                        class="bg-accent text-white px-8 py-3 hover:bg-blue-700 hover:text-white transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                        id="submitBtn"
                    >
                        Send Message
                    </button>
                </div>

                <!-- Success/Error Messages -->
                <div id="formMessage" class="hidden text-center p-4 border">
                    <p id="messageText"></p>
                </div>
            </form>
        </div>
    </section>

    <!-- Social Media & Other Channels -->
    <section class="py-16 bg-black">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h2 class="text-3xl font-bold mb-12 text-center">Follow Us</h2>

            <?php
            // 首先收集所有有效的社交媒体链接
            $platforms = ['github', 'twitter', 'linkedin', 'discord'];
            $validPlatforms = [];

            foreach ($platforms as $platform) {
                $url = $socialLinks[$platform . '_url'];
                $isValid = isValidSocialMediaUrl($url);

                if ($isValid) {
                    $validPlatforms[] = [
                        'platform' => $platform,
                        'url' => $url,
                        'displayName' => getSocialMediaDisplayName($platform),
                        'description' => getSocialMediaDescription($platform),
                        'icon' => getSocialMediaIcon($platform)
                    ];
                }
            }

            // 只有当有有效的社交媒体链接时才显示Follow Us部分
            if (!empty($validPlatforms)):
                // 根据平台数量调整网格布局
                $gridCols = count($validPlatforms);
                $gridClass = match($gridCols) {
                    1 => 'grid-cols-1 max-w-sm mx-auto',
                    2 => 'grid-cols-1 md:grid-cols-2 max-w-2xl mx-auto',
                    3 => 'grid-cols-1 md:grid-cols-3 max-w-4xl mx-auto',
                    default => 'grid-cols-1 md:grid-cols-4'
                };
            ?>
            <div class="grid <?= $gridClass ?> gap-8">
                <?php foreach ($validPlatforms as $platformData): ?>
                <a href="<?= htmlspecialchars($platformData['url']) ?>"
                   target="_blank" rel="noopener noreferrer"
                   class="bg-gray-900 border border-gray-800 p-6 text-center hover:border-accent transition-colors duration-200 group">
                    <div class="text-4xl mb-4 group-hover:text-accent">
                        <svg class="w-12 h-12 mx-auto" fill="currentColor" viewBox="0 0 24 24">
                            <?= $platformData['icon'] ?>
                        </svg>
                    </div>
                    <h3 class="text-lg font-semibold mb-2 group-hover:text-accent"><?= $platformData['displayName'] ?></h3>
                    <p class="text-gray-400 text-sm">
                        <?= $platformData['description'] ?>
                    </p>
                </a>
                <?php endforeach; ?>
            </div>
            <?php else: ?>
            <!-- 如果没有配置社交媒体链接，显示提示信息 -->
            <div class="text-center">
                <div class="text-6xl mb-6">🔗</div>
                <h3 class="text-xl font-semibold mb-4">Stay Connected</h3>
                <p class="text-gray-400">
                    We're working on setting up our social media presence. Check back soon!
                </p>
            </div>
            <?php endif; ?>
        </div>
    </section>
</main>

<!-- JavaScript for form handling -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Contact form handling
    const contactForm = document.getElementById('contactForm');
    if (contactForm) {
        const messageTextarea = document.getElementById('message');
        let duplicateCheckTimeout;

        // 实时检查消息重复
        if (messageTextarea) {
            messageTextarea.addEventListener('input', function() {
                clearTimeout(duplicateCheckTimeout);

                // 延迟检查，避免频繁请求
                duplicateCheckTimeout = setTimeout(() => {
                    checkMessageDuplicate(this.value);
                }, 1000);
            });
        }
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const submitBtn = document.getElementById('submitBtn');
            const formMessage = document.getElementById('formMessage');
            const messageText = document.getElementById('messageText');

            // Collect form data
            const formData = new FormData(contactForm);
            const data = {};
            for (let [key, value] of formData.entries()) {
                data[key] = value;
            }

            // Client-side validation
            const errors = [];

            // Validate name
            if (!data.name || data.name.trim().length < 2 || data.name.trim().length > 100) {
                errors.push('Name must be between 2 and 100 characters');
            }

            // Validate email
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!data.email || !emailRegex.test(data.email)) {
                errors.push('Please enter a valid email address');
            }

            // Check for temporary email domains
            if (data.email) {
                const emailDomain = data.email.toLowerCase().split('@')[1];
                const tempDomains = ['tempmail.org', '10minutemail.com', 'guerrillamail.com', 'mailinator.com', 'yopmail.com'];
                if (tempDomains.includes(emailDomain)) {
                    errors.push('Temporary email addresses are not allowed');
                }
            }

            // Validate subject
            if (!data.subject) {
                errors.push('Please select a subject');
            }

            // Validate message
            if (!data.message || data.message.trim().length < 10 || data.message.trim().length > 5000) {
                errors.push('Message must be between 10 and 5000 characters');
            }

            // Check for suspicious content
            const suspiciousPatterns = [
                /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
                /javascript:/gi,
                /<iframe/gi,
                /onclick\s*=/gi
            ];

            const allContent = (data.name + ' ' + data.message + ' ' + data.subject).toLowerCase();
            for (let pattern of suspiciousPatterns) {
                if (pattern.test(allContent)) {
                    errors.push('Message contains invalid content');
                    break;
                }
            }

            // Check for excessive links
            const linkCount = (data.message.match(/https?:\/\/[^\s]+/gi) || []).length;
            if (linkCount > 3) {
                errors.push('Too many links in message');
            }

            // Validate privacy agreement
            if (!data.privacy) {
                errors.push('You must agree to the privacy policy');
            }

            // Show validation errors
            if (errors.length > 0) {
                formMessage.classList.remove('hidden', 'bg-green-900', 'border-green-700', 'text-green-300');
                formMessage.classList.add('bg-red-900', 'border-red-700', 'text-red-300');
                messageText.textContent = 'Please fix the following errors: ' + errors.join(', ');
                formMessage.classList.remove('hidden');

                setTimeout(() => {
                    formMessage.classList.add('hidden');
                }, 8000);
                return;
            }

            // Disable submit button
            submitBtn.disabled = true;
            submitBtn.textContent = 'Checking for duplicates...';

            // 在提交前最后检查一次重复
            fetch('/api/check-duplicate.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ message: data.message.trim() })
            })
            .then(response => response.json())
            .then(duplicateResult => {
                if (duplicateResult.success && duplicateResult.is_duplicate) {
                    // 发现重复，阻止提交
                    formMessage.classList.remove('hidden', 'bg-green-900', 'border-green-700', 'text-green-300');
                    formMessage.classList.add('bg-red-900', 'border-red-700', 'text-red-300');

                    let duplicateMessage = '';
                    if (duplicateResult.duplicate_type === 'exact') {
                        duplicateMessage = 'This exact message has already been submitted. Please modify your message if you need to send it again.';
                    } else {
                        duplicateMessage = 'A very similar message has already been submitted. Please ensure your message is unique.';
                    }

                    messageText.textContent = duplicateMessage;
                    formMessage.classList.remove('hidden');

                    // Re-enable submit button
                    submitBtn.disabled = false;
                    submitBtn.textContent = 'Send Message';

                    setTimeout(() => {
                        formMessage.classList.add('hidden');
                    }, 8000);
                    return;
                }

                // 没有重复，继续提交
                submitBtn.textContent = 'Sending...';

                // Send AJAX request to API
                fetch('/api/contact.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(result => {
                // Clear any previous messages
                formMessage.classList.remove('hidden', 'bg-green-900', 'border-green-700', 'text-green-300', 'bg-red-900', 'border-red-700', 'text-red-300');

                if (result.success) {
                    // Show success message
                    formMessage.classList.add('bg-green-900', 'border-green-700', 'text-green-300');
                    messageText.textContent = result.message;

                    // Reset form
                    contactForm.reset();
                } else {
                    // Show error message
                    formMessage.classList.add('bg-red-900', 'border-red-700', 'text-red-300');
                    if (result.errors && result.errors.length > 0) {
                        messageText.textContent = 'Please fix the following errors: ' + result.errors.join(', ');
                    } else {
                        messageText.textContent = result.message || 'An error occurred. Please try again.';
                    }
                }

                formMessage.classList.remove('hidden');

                // Re-enable submit button
                submitBtn.disabled = false;
                submitBtn.textContent = 'Send Message';

                // Hide message after 8 seconds
                setTimeout(() => {
                    formMessage.classList.add('hidden');
                }, 8000);
            })
            .catch(error => {
                console.error('Error:', error);

                // Show error message
                formMessage.classList.remove('hidden', 'bg-green-900', 'border-green-700', 'text-green-300');
                formMessage.classList.add('bg-red-900', 'border-red-700', 'text-red-300');
                messageText.textContent = 'Network error. Please check your connection and try again.';

                // Re-enable submit button
                submitBtn.disabled = false;
                submitBtn.textContent = 'Send Message';

                // Hide message after 8 seconds
                setTimeout(() => {
                    formMessage.classList.add('hidden');
                }, 8000);
                })
                .catch(error => {
                    console.error('Error:', error);

                    // Show error message
                    formMessage.classList.remove('hidden', 'bg-green-900', 'border-green-700', 'text-green-300');
                    formMessage.classList.add('bg-red-900', 'border-red-700', 'text-red-300');
                    messageText.textContent = 'Network error. Please check your connection and try again.';

                    // Re-enable submit button
                    submitBtn.disabled = false;
                    submitBtn.textContent = 'Send Message';

                    // Hide message after 8 seconds
                    setTimeout(() => {
                        formMessage.classList.add('hidden');
                    }, 8000);
                });
            })
            .catch(error => {
                console.error('Duplicate check error:', error);

                // 如果重复检查失败，仍然允许提交
                submitBtn.textContent = 'Sending...';

                // Send AJAX request to API
                fetch('/api/contact.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                })
                .then(response => response.json())
                .then(result => {
                    // Clear any previous messages
                    formMessage.classList.remove('hidden', 'bg-green-900', 'border-green-700', 'text-green-300', 'bg-red-900', 'border-red-700', 'text-red-300');

                    if (result.success) {
                        // Show success message
                        formMessage.classList.add('bg-green-900', 'border-green-700', 'text-green-300');
                        messageText.textContent = result.message;

                        // Reset form
                        contactForm.reset();
                        clearDuplicateWarning();
                    } else {
                        // Show error message
                        formMessage.classList.add('bg-red-900', 'border-red-700', 'text-red-300');
                        if (result.errors && result.errors.length > 0) {
                            messageText.textContent = 'Please fix the following errors: ' + result.errors.join(', ');
                        } else {
                            messageText.textContent = result.message || 'An error occurred. Please try again.';
                        }
                    }

                    formMessage.classList.remove('hidden');

                    // Re-enable submit button
                    submitBtn.disabled = false;
                    submitBtn.textContent = 'Send Message';

                    // Hide message after 8 seconds
                    setTimeout(() => {
                        formMessage.classList.add('hidden');
                    }, 8000);
                })
                .catch(error => {
                    console.error('Error:', error);

                    // Show error message
                    formMessage.classList.remove('hidden', 'bg-green-900', 'border-green-700', 'text-green-300');
                    formMessage.classList.add('bg-red-900', 'border-red-700', 'text-red-300');
                    messageText.textContent = 'Network error. Please check your connection and try again.';

                    // Re-enable submit button
                    submitBtn.disabled = false;
                    submitBtn.textContent = 'Send Message';

                    // Hide message after 8 seconds
                    setTimeout(() => {
                        formMessage.classList.add('hidden');
                    }, 8000);
                });
            });
        });
    }

    // 检查消息重复的函数
    function checkMessageDuplicate(message) {
        if (!message || message.trim().length < 10) {
            clearDuplicateWarning();
            return;
        }

        fetch('/api/check-duplicate.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ message: message.trim() })
        })
        .then(response => response.json())
        .then(result => {
            if (result.success && result.is_duplicate) {
                showDuplicateWarning(result);
            } else {
                clearDuplicateWarning();
            }
        })
        .catch(error => {
            clearDuplicateWarning();
        });
    }

    // 显示重复警告
    function showDuplicateWarning(duplicateInfo) {
        let warningDiv = document.getElementById('duplicateWarning');

        if (!warningDiv) {
            warningDiv = document.createElement('div');
            warningDiv.id = 'duplicateWarning';
            warningDiv.className = 'mt-2 p-3 bg-yellow-900 border border-yellow-700 text-yellow-300';

            const messageTextarea = document.getElementById('message');
            messageTextarea.parentNode.appendChild(warningDiv);
        }

        let warningText = '';
        if (duplicateInfo.duplicate_type === 'exact') {
            warningText = '⚠️ ' + duplicateInfo.message + '. Please modify your message if you need to send it again.';
        } else if (duplicateInfo.duplicate_type === 'similar') {
            warningText = '⚠️ ' + duplicateInfo.message + '. Please ensure your message is unique.';
        }

        warningDiv.innerHTML = warningText;
        warningDiv.style.display = 'block';
    }

    // 清除重复警告
    function clearDuplicateWarning() {
        const warningDiv = document.getElementById('duplicateWarning');
        if (warningDiv) {
            warningDiv.style.display = 'none';
        }
    }
});
</script>

<?php include_once ROOT_PATH . '/templates/layout/footer.php'; ?>
