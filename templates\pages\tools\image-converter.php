<?php
/**
 * Image Converter Tool Page
 * 遵循01-UI设计规范.md - 零圆角设计
 */

$currentPage = 'tool-image-converter';
$breadcrumbs = [
    ['name' => 'Home', 'url' => '/'],
    ['name' => 'Tools', 'url' => '/tools'],
    ['name' => 'Design', 'url' => '/tools/design'],
    ['name' => 'Image Converter']
];

// 包含动态SEO助手
require_once ROOT_PATH . '/app/helpers/dynamic-seo.php';

// 生成SEO数据 - 优化长度
$seoData = [
    'title' => 'Free Image Converter Online - Prompt2Tool',
    'description' => 'Convert images between formats instantly. Support for JPG, PNG, WebP, GIF and more. Fast, secure, and free online image conversion.',
    'keywords' => 'image converter, image format converter, jpg to png, png to jpg, webp converter, gif converter, online image converter, free image converter',
    'og_title' => 'Free Image Converter Online - Prompt2Tool',
    'og_description' => 'Convert images between formats instantly. Support for JPG, PNG, WebP, GIF and more.',
    'canonical' => getCurrentURL()
];

// 包含公共头部
include_once ROOT_PATH . '/templates/layout/header.php';
?>

<!-- 工具页面主体 -->
<div class="bg-gray-900 text-white">

    <!-- Hero Section with SEO optimized content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-12">
        <div class="text-center mb-12">
            <div class="flex items-center justify-center mb-4">
                <div class="bg-blue-600 p-3 mr-4">
                    <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <div class="text-left">
                    <h1 class="text-4xl font-bold text-white mb-2">Image Converter</h1>
                    <p class="text-xl text-gray-400">Convert images between different formats</p>
                </div>
            </div>
        </div>

        <!-- 工具主体 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- 输入区域 -->
            <div class="bg-gray-800 border border-gray-700 p-6">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-xl font-semibold text-white">Upload Image</h2>
                    <div class="flex space-x-2">
                        <button id="clearInput" class="px-3 py-1 bg-red-600 text-white text-sm hover:bg-red-700 transition-colors">
                            Clear
                        </button>
                    </div>
                </div>
                
                <!-- 文件上传区域 -->
                <div id="dropZone" class="border-2 border-dashed border-gray-600 p-8 text-center hover:border-gray-500 transition-colors cursor-pointer">
                    <input type="file" id="imageInput" accept="image/*" class="hidden">
                    <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                    </svg>
                    <p class="text-gray-300 mb-2">Click to upload or drag and drop</p>
                    <p class="text-gray-500 text-sm">Supports: JPG, PNG, GIF, WebP, BMP</p>
                </div>
                
                <!-- 图片预览 -->
                <div id="imagePreview" class="mt-4 hidden">
                    <img id="previewImg" class="max-w-full h-auto border border-gray-600">
                    <div class="mt-2 text-sm text-gray-400">
                        <span id="imageInfo"></span>
                    </div>
                </div>
            </div>

            <!-- 输出区域 -->
            <div class="bg-gray-800 border border-gray-700 p-6">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-xl font-semibold text-white">Convert Settings</h2>
                </div>
                
                <!-- 格式选择 -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-300 mb-2">Output Format</label>
                    <select id="outputFormat" class="w-full bg-gray-900 border border-gray-600 text-gray-100 p-2 focus:outline-none focus:border-blue-500">
                        <option value="png">PNG</option>
                        <option value="jpeg">JPEG</option>
                        <option value="webp">WebP</option>
                        <option value="gif">GIF</option>
                        <option value="bmp">BMP</option>
                    </select>
                </div>
                
                <!-- 质量设置 (仅对JPEG/WebP) -->
                <div id="qualitySettings" class="mb-6 hidden">
                    <label class="block text-sm font-medium text-gray-300 mb-2">Quality: <span id="qualityValue">90</span>%</label>
                    <input type="range" id="qualitySlider" min="1" max="100" value="90" class="w-full">
                </div>
                
                <!-- 转换按钮 -->
                <button id="convertBtn" class="w-full bg-blue-600 text-white py-3 hover:bg-blue-700 transition-colors disabled:bg-gray-600 disabled:cursor-not-allowed" disabled>
                    Convert Image
                </button>
                
                <!-- 下载区域 -->
                <div id="downloadArea" class="mt-6 hidden">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-white">Converted Image</h3>
                        <div class="flex space-x-2">
                            <div class="relative">
                                <button id="downloadOutput" class="px-3 py-1 bg-green-600 text-white text-sm hover:bg-green-700 transition-colors">
                                    Download
                                </button>
                                <div id="downloadTooltip" class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-700 text-white text-xs rounded pointer-events-none whitespace-nowrap z-50" style="opacity: 0; visibility: hidden; transition: opacity 0.2s, visibility 0.2s;">
                                    Download converted image
                                </div>
                            </div>
                        </div>
                    </div>
                    <canvas id="outputCanvas" class="max-w-full border border-gray-600"></canvas>
                    <div class="mt-2 text-sm text-gray-400">
                        <span id="outputInfo"></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 工具信息 -->
        <div class="mt-12 grid grid-cols-1 md:grid-cols-3 gap-8">
            <div class="bg-gray-800 border border-gray-700 p-6">
                <h4 class="text-lg font-semibold text-white mb-3">Supported Formats</h4>
                <ul class="text-gray-300 space-y-2">
                    <li>• JPEG/JPG</li>
                    <li>• PNG</li>
                    <li>• WebP</li>
                    <li>• GIF</li>
                    <li>• BMP</li>
                </ul>
            </div>
            <div class="bg-gray-800 border border-gray-700 p-6">
                <h4 class="text-lg font-semibold text-white mb-3">Features</h4>
                <ul class="text-gray-300 space-y-2">
                    <li>• Quality control</li>
                    <li>• Batch processing</li>
                    <li>• Instant preview</li>
                    <li>• No upload limits</li>
                </ul>
            </div>
            <div class="bg-gray-800 border border-gray-700 p-6">
                <h4 class="text-lg font-semibold text-white mb-3">Benefits</h4>
                <ul class="text-gray-300 space-y-2">
                    <li>• Reduce file size</li>
                    <li>• Better compatibility</li>
                    <li>• Optimize for web</li>
                    <li>• Privacy protection</li>
                </ul>
            </div>
        </div>

        <!-- FAQ Section -->
        <div class="px-8 pt-16 pb-16">
            <h2 class="text-3xl font-bold text-white text-center mb-8">Frequently Asked Questions</h2>
            <div class="max-w-4xl mx-auto space-y-6">
                <div class="border-b border-gray-700 pb-6">
                    <h3 class="text-xl font-semibold text-white mb-3">What image formats are supported?</h3>
                    <p class="text-gray-300">Our converter supports all major image formats including JPEG, PNG, WebP, GIF, and BMP. You can convert between any of these formats.</p>
                </div>

                <div class="border-b border-gray-700 pb-6">
                    <h3 class="text-xl font-semibold text-white mb-3">Is there a file size limit?</h3>
                    <p class="text-gray-300">No, there are no file size limits. However, very large images may take longer to process depending on your device's capabilities.</p>
                </div>

                <div class="border-b border-gray-700 pb-6">
                    <h3 class="text-xl font-semibold text-white mb-3">Are my images stored on your servers?</h3>
                    <p class="text-gray-300">No, all image processing is done locally in your browser. Your images are never uploaded to our servers, ensuring complete privacy and security.</p>
                </div>

                <div class="border-b border-gray-700 pb-6">
                    <h3 class="text-xl font-semibold text-white mb-3">How do I optimize images for web?</h3>
                    <p class="text-gray-300">For web optimization, convert to WebP format for the best compression, or use JPEG with 80-90% quality for good balance between size and quality.</p>
                </div>

                <div class="mb-0">
                    <h3 class="text-xl font-semibold text-white mb-3">Can I convert multiple images at once?</h3>
                    <p class="text-gray-300 mb-0">Currently, the tool processes one image at a time. For batch processing, you can use the tool multiple times or consider our premium batch conversion service.</p>
                </div>
            </div>
        </div>
    </div>

<script>
// Image Converter functionality
document.addEventListener('DOMContentLoaded', function() {
    const imageInput = document.getElementById('imageInput');
    const dropZone = document.getElementById('dropZone');
    const imagePreview = document.getElementById('imagePreview');
    const previewImg = document.getElementById('previewImg');
    const imageInfo = document.getElementById('imageInfo');
    const outputFormat = document.getElementById('outputFormat');
    const qualitySettings = document.getElementById('qualitySettings');
    const qualitySlider = document.getElementById('qualitySlider');
    const qualityValue = document.getElementById('qualityValue');
    const convertBtn = document.getElementById('convertBtn');
    const clearInput = document.getElementById('clearInput');
    const downloadArea = document.getElementById('downloadArea');
    const downloadOutput = document.getElementById('downloadOutput');
    const downloadTooltip = document.getElementById('downloadTooltip');
    const outputCanvas = document.getElementById('outputCanvas');
    const outputInfo = document.getElementById('outputInfo');
    
    let currentImage = null;
    let convertedBlob = null;

    // 文件拖拽处理
    dropZone.addEventListener('click', () => imageInput.click());
    dropZone.addEventListener('dragover', (e) => {
        e.preventDefault();
        dropZone.classList.add('border-blue-500');
    });
    dropZone.addEventListener('dragleave', () => {
        dropZone.classList.remove('border-blue-500');
    });
    dropZone.addEventListener('drop', (e) => {
        e.preventDefault();
        dropZone.classList.remove('border-blue-500');
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            handleImageUpload(files[0]);
        }
    });

    // 文件选择处理
    imageInput.addEventListener('change', (e) => {
        if (e.target.files.length > 0) {
            handleImageUpload(e.target.files[0]);
        }
    });

    // 格式选择处理
    outputFormat.addEventListener('change', () => {
        const format = outputFormat.value;
        if (format === 'jpeg' || format === 'webp') {
            qualitySettings.classList.remove('hidden');
        } else {
            qualitySettings.classList.add('hidden');
        }
    });

    // 质量滑块处理
    qualitySlider.addEventListener('input', () => {
        qualityValue.textContent = qualitySlider.value;
    });

    // 转换按钮
    convertBtn.addEventListener('click', convertImage);

    // 清除按钮
    clearInput.addEventListener('click', () => {
        imageInput.value = '';
        imagePreview.classList.add('hidden');
        downloadArea.classList.add('hidden');
        convertBtn.disabled = true;
        currentImage = null;
        convertedBlob = null;
    });

    // 下载按钮
    downloadOutput.addEventListener('click', () => {
        if (convertedBlob) {
            const format = outputFormat.value;
            const extension = format === 'jpeg' ? 'jpg' : format;
            const filename = `converted.${extension}`;
            
            const url = URL.createObjectURL(convertedBlob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            // 显示临时提示
            if (downloadTooltip) {
                downloadTooltip.textContent = 'Downloaded!';
                downloadTooltip.style.opacity = '1';
                downloadTooltip.style.visibility = 'visible';
                
                setTimeout(() => {
                    downloadTooltip.textContent = 'Download converted image';
                    downloadTooltip.style.opacity = '0';
                    downloadTooltip.style.visibility = 'hidden';
                }, 2000);
            }
        }
    });

    function handleImageUpload(file) {
        if (!file.type.startsWith('image/')) {
            alert('Please select a valid image file.');
            return;
        }

        const reader = new FileReader();
        reader.onload = (e) => {
            currentImage = new Image();
            currentImage.onload = () => {
                previewImg.src = e.target.result;
                imagePreview.classList.remove('hidden');
                convertBtn.disabled = false;
                
                // 显示图片信息
                const sizeKB = (file.size / 1024).toFixed(2);
                imageInfo.textContent = `${currentImage.width}×${currentImage.height} • ${sizeKB} KB • ${file.type}`;
            };
            currentImage.src = e.target.result;
        };
        reader.readAsDataURL(file);
    }

    function convertImage() {
        if (!currentImage) return;

        const canvas = outputCanvas;
        const ctx = canvas.getContext('2d');
        
        canvas.width = currentImage.width;
        canvas.height = currentImage.height;
        
        ctx.drawImage(currentImage, 0, 0);
        
        const format = outputFormat.value;
        const quality = format === 'jpeg' || format === 'webp' ? qualitySlider.value / 100 : 1;
        
        canvas.toBlob((blob) => {
            convertedBlob = blob;
            downloadArea.classList.remove('hidden');
            
            // 显示输出信息
            const sizeKB = (blob.size / 1024).toFixed(2);
            outputInfo.textContent = `${canvas.width}×${canvas.height} • ${sizeKB} KB • ${format.toUpperCase()}`;
        }, `image/${format}`, quality);
    }
});
</script>

</div>

<?php include_once ROOT_PATH . '/templates/layout/footer.php'; ?>
