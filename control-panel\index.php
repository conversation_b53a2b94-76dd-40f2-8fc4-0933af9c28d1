<?php
/**
 * 管理后台入口文件
 * 使用非标准目录名 control-panel 提高安全性
 */

// 安全检查
if (!defined('SECURE_ACCESS')) {
    define('SECURE_ACCESS', true);
}

// 定义后台根目录
define('MGMT_ROOT', __DIR__);
define('ROOT_PATH', dirname(__DIR__));

// 加载应用初始化
require_once ROOT_PATH . '/app/init.php';

// 检查管理员权限
// session_start(); // 已在 app/init.php 中启动，无需重复
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: auth/login.php');
    exit;
}

// 处理导出请求（必须在任何输出之前）
if (isset($_GET['export']) && $_GET['export'] === 'csv' && ($_GET['page'] ?? '') === 'analytics') {
    handleAnalyticsExport();
    exit;
}

// 处理导出launches（必须在任何输出之前）
if (isset($_GET['export']) && $_GET['export'] === '1' && ($_GET['page'] ?? '') === 'launches') {
    try {
        // 数据库连接已在上面加载

        // 获取筛选参数
        $status = $_GET['status'] ?? 'all';
        $category = $_GET['category'] ?? 'all';
        $launchStatus = $_GET['launch_status'] ?? 'all';
        $search = $_GET['search'] ?? '';

        // 构建查询条件
        $whereConditions = [];
        $params = [];

        if ($status !== 'all') {
            $whereConditions[] = "l.status = ?";
            $params[] = $status;
        }

        if ($category !== 'all') {
            $whereConditions[] = "l.category = ?";
            $params[] = $category;
        }

        if ($launchStatus !== 'all') {
            $whereConditions[] = "l.launch_status = ?";
            $params[] = $launchStatus;
        }

        if (!empty($search)) {
            $whereConditions[] = "(l.name LIKE ? OR l.tagline LIKE ? OR l.description LIKE ? OR m.username LIKE ?)";
            $searchTerm = "%$search%";
            $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm, $searchTerm]);
        }

        $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

        // 查询所有符合条件的数据
        $sql = "
            SELECT
                l.name,
                l.website_url,
                l.category,
                l.launch_status,
                l.created_at,
                m.email as submitter_email
            FROM pt_product_launches l
            LEFT JOIN pt_member m ON l.user_id = m.id
            $whereClause
            ORDER BY l.created_at DESC
        ";

        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        $launches = $stmt->fetchAll();

        // 设置CSV下载头
        $filename = 'product_launches_' . date('Y-m-d_H-i-s') . '.csv';
        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: no-cache, must-revalidate');
        header('Expires: Sat, 26 Jul 1997 05:00:00 GMT');

        // 创建文件句柄
        $output = fopen('php://output', 'w');

        // 添加BOM以支持Excel中的UTF-8
        fwrite($output, "\xEF\xBB\xBF");

        // 写入CSV头部
        fputcsv($output, [
            'Website Name',
            'Website URL',
            'Category',
            'Status',
            'Launch Date',
            'Submitter Email'
        ]);

        // 写入数据行
        foreach ($launches as $launch) {
            // 格式化状态
            $statusLabels = [
                'coming-soon' => 'Coming Soon',
                'beta' => 'Beta',
                'launched' => 'Launched'
            ];
            $formattedStatus = $statusLabels[$launch['launch_status']] ?? ucfirst($launch['launch_status']);

            // 格式化日期
            $launchDate = date('Y-m-d H:i:s', strtotime($launch['created_at']));

            fputcsv($output, [
                $launch['name'],
                $launch['website_url'],
                ucfirst($launch['category']),
                $formattedStatus,
                $launchDate,
                $launch['submitter_email'] ?? 'N/A'
            ]);
        }

        fclose($output);
        exit;
    } catch (Exception $e) {
        // 如果导出失败，继续正常页面流程
        $_SESSION['export_error'] = "Export failed: " . $e->getMessage();
    }
}

// 处理导出邮箱（必须在任何输出之前）
if ($_SERVER['REQUEST_METHOD'] === 'POST' && ($_POST['action'] ?? '') === 'export_emails' && ($_GET['page'] ?? '') === 'messages') {
    try {
        // 数据库连接已在上面加载

        $stmt = $pdo->query("SELECT DISTINCT email FROM pt_contact_message WHERE email IS NOT NULL AND email != '' ORDER BY email");
        $emails = $stmt->fetchAll(PDO::FETCH_COLUMN);

        header('Content-Type: text/plain; charset=utf-8');
        header('Content-Disposition: attachment; filename="contact_emails_' . date('Y-m-d_H-i-s') . '.txt"');
        header('Cache-Control: no-cache, no-store, must-revalidate');
        header('Pragma: no-cache');
        header('Expires: 0');

        echo implode("\n", $emails);
        exit;
    } catch (Exception $e) {
        // 如果导出失败，继续正常页面流程
        $_SESSION['export_error'] = "Export failed: " . $e->getMessage();
    }
}

// 处理导出用户（必须在任何输出之前）
if ($_SERVER['REQUEST_METHOD'] === 'POST' && ($_POST['action'] ?? '') === 'export_users' && ($_GET['page'] ?? '') === 'users') {
    try {
        // 数据库连接已在上面加载

        // 构建查询条件
        $whereConditions = [];
        $params = [];

        if (!empty($_POST['search'])) {
            $whereConditions[] = "(username LIKE :search OR email LIKE :search OR first_name LIKE :search OR last_name LIKE :search)";
            $params['search'] = "%{$_POST['search']}%";
        }

        if (!empty($_POST['status'])) {
            $whereConditions[] = "status = :status";
            $params['status'] = $_POST['status'];
        }

        $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

        // 获取用户数据
        $exportQuery = "
            SELECT username, email, first_name, last_name, status, subscription_type,
                   email_verified, api_quota, api_used, created_at, last_login
            FROM pt_member
            {$whereClause}
            ORDER BY created_at DESC
        ";
        $stmt = $pdo->prepare($exportQuery);
        $stmt->execute($params);
        $users = $stmt->fetchAll();

        // 设置CSV头部
        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename="users_export_' . date('Y-m-d_H-i-s') . '.csv"');
        header('Cache-Control: no-cache, no-store, must-revalidate');
        header('Pragma: no-cache');
        header('Expires: 0');

        // 输出CSV
        $output = fopen('php://output', 'w');

        // 添加BOM以支持Excel中的UTF-8
        fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

        // CSV头部
        fputcsv($output, [
            'Username', 'Email', 'First Name', 'Last Name', 'Status',
            'Subscription', 'Email Verified', 'API Quota', 'API Used',
            'Created At', 'Last Login'
        ]);

        // 数据行
        foreach ($users as $user) {
            fputcsv($output, [
                $user['username'] ?? '',
                $user['email'] ?? '',
                $user['first_name'] ?? '',
                $user['last_name'] ?? '',
                $user['status'] ?? '',
                $user['subscription_type'] ?? '',
                $user['email_verified'] ? 'Yes' : 'No',
                $user['api_quota'] ?? '',
                $user['api_used'] ?? '',
                $user['created_at'] ?? '',
                $user['last_login'] ?? ''
            ]);
        }

        fclose($output);
        exit;
    } catch (Exception $e) {
        // 如果导出失败，继续正常页面流程
        $_SESSION['export_error'] = "Export failed: " . $e->getMessage();
    }
}




// 处理导出需求（必须在任何输出之前）
if ($_SERVER['REQUEST_METHOD'] === 'POST' && ($_POST['action'] ?? '') === 'export_requests' && ($_GET['page'] ?? '') === 'user-requests') {
    try {
        // 数据库连接已在上面加载

        // 构建查询条件
        $whereConditions = [];
        $params = [];

        if (!empty($_POST['search'])) {
            $whereConditions[] = "(r.title LIKE ? OR r.description LIKE ? OR m.username LIKE ?)";
            $searchTerm = "%{$_POST['search']}%";
            $params[] = $searchTerm;
            $params[] = $searchTerm;
            $params[] = $searchTerm;
        }

        if (!empty($_POST['status']) && $_POST['status'] !== 'all') {
            $whereConditions[] = "r.status = ?";
            $params[] = $_POST['status'];
        }

        if (!empty($_POST['category']) && $_POST['category'] !== 'all') {
            $whereConditions[] = "r.category = ?";
            $params[] = $_POST['category'];
        }

        $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

        // 获取需求数据
        $exportQuery = "
            SELECT r.title, r.description, r.category, r.priority, r.status, r.votes,
                   r.created_at, r.updated_at, r.admin_reply,
                   m.username, m.email, m.first_name, m.last_name
            FROM pt_user_requests r
            LEFT JOIN pt_member m ON r.user_id = m.id
            {$whereClause}
            ORDER BY r.created_at DESC
        ";
        $stmt = $pdo->prepare($exportQuery);
        $stmt->execute($params);
        $requests = $stmt->fetchAll();

        // 设置CSV头部
        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename="requests_export_' . date('Y-m-d_H-i-s') . '.csv"');
        header('Cache-Control: no-cache, no-store, must-revalidate');
        header('Pragma: no-cache');
        header('Expires: 0');

        // 输出CSV
        $output = fopen('php://output', 'w');

        // 添加BOM以支持Excel中的UTF-8
        fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

        // CSV头部
        fputcsv($output, [
            'Title', 'Description', 'Category', 'Priority', 'Status', 'Votes',
            'Username', 'Email', 'First Name', 'Last Name',
            'Admin Reply', 'Created At', 'Updated At'
        ]);

        // 数据行
        foreach ($requests as $request) {
            fputcsv($output, [
                $request['title'] ?? '',
                $request['description'] ?? '',
                $request['category'] ?? '',
                $request['priority'] ?? '',
                $request['status'] ?? '',
                $request['votes'] ?? 0,
                $request['username'] ?? '',
                $request['email'] ?? '',
                $request['first_name'] ?? '',
                $request['last_name'] ?? '',
                $request['admin_reply'] ?? '',
                $request['created_at'] ?? '',
                $request['updated_at'] ?? ''
            ]);
        }

        fclose($output);
        exit;
    } catch (Exception $e) {
        // 如果导出失败，继续正常页面流程
        $_SESSION['export_error'] = "Export failed: " . $e->getMessage();
    }
}

// 处理用户管理的AJAX请求（必须在任何输出之前）
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && ($_GET['page'] ?? '') === 'users') {
    header('Content-Type: application/json');

    try {
        // 数据库连接已在上面加载

        // 检查pt_member表是否存在
        $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
        $usersTableExists = in_array('pt_member', $tables);

        switch ($_POST['action']) {
            case 'create_user':
                if (!$usersTableExists) {
                    echo json_encode(['success' => false, 'message' => 'pt_member table does not exist']);
                    exit;
                }

                $username = trim($_POST['username']);
                $email = trim($_POST['email']);
                $password = password_hash($_POST['password'], PASSWORD_DEFAULT);
                $firstName = trim($_POST['first_name']);
                $lastName = trim($_POST['last_name']);
                $status = $_POST['status'];
                $subscriptionType = $_POST['subscription_type'];
                $apiQuota = intval($_POST['api_quota'] ?? 1000);

                // 检查用户名和邮箱是否已存在
                $stmt = $pdo->prepare("SELECT COUNT(*) FROM pt_member WHERE username = :username OR email = :email");
                $stmt->execute(['username' => $username, 'email' => $email]);
                if ($stmt->fetchColumn() > 0) {
                    echo json_encode(['success' => false, 'message' => 'Username or email already exists']);
                    exit;
                }

                $stmt = $pdo->prepare("
                    INSERT INTO pt_member (username, email, password, first_name, last_name, status, subscription_type, email_verified, api_quota, api_used)
                    VALUES (:username, :email, :password, :first_name, :last_name, :status, :subscription_type, 1, :api_quota, 0)
                ");
                $stmt->execute([
                    'username' => $username,
                    'email' => $email,
                    'password' => $password,
                    'first_name' => $firstName,
                    'last_name' => $lastName,
                    'status' => $status,
                    'subscription_type' => $subscriptionType,
                    'api_quota' => $apiQuota
                ]);

                echo json_encode(['success' => true, 'message' => 'User created successfully']);
                break;

            case 'get_user_details':
                if (!$usersTableExists) {
                    echo json_encode(['success' => false, 'message' => 'Users table does not exist']);
                    exit;
                }

                $userId = intval($_POST['user_id']);

                $stmt = $pdo->prepare("SELECT * FROM pt_member WHERE id = :id");
                $stmt->execute(['id' => $userId]);
                $user = $stmt->fetch();

                if ($user) {
                    echo json_encode(['success' => true, 'user' => $user]);
                } else {
                    echo json_encode(['success' => false, 'message' => 'User not found']);
                }
                break;

            case 'update_user':
                if (!$usersTableExists) {
                    echo json_encode(['success' => false, 'message' => 'Users table does not exist']);
                    exit;
                }

                $userId = intval($_POST['user_id']);
                $username = trim($_POST['username']);
                $email = trim($_POST['email']);
                $firstName = trim($_POST['first_name']);
                $lastName = trim($_POST['last_name']);
                $status = $_POST['status'];
                $subscriptionType = $_POST['subscription_type'];
                $apiQuota = intval($_POST['api_quota'] ?? 1000);
                $emailVerified = intval($_POST['email_verified'] ?? 0);

                // 检查用户名和邮箱是否已被其他用户使用
                $stmt = $pdo->prepare("SELECT COUNT(*) FROM pt_member WHERE (username = :username OR email = :email) AND id != :id");
                $stmt->execute(['username' => $username, 'email' => $email, 'id' => $userId]);
                if ($stmt->fetchColumn() > 0) {
                    echo json_encode(['success' => false, 'message' => 'Username or email already exists']);
                    exit;
                }

                $stmt = $pdo->prepare("
                    UPDATE pt_member SET
                        username = :username,
                        email = :email,
                        first_name = :first_name,
                        last_name = :last_name,
                        status = :status,
                        subscription_type = :subscription_type,
                        api_quota = :api_quota,
                        email_verified = :email_verified,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = :id
                ");
                $stmt->execute([
                    'username' => $username,
                    'email' => $email,
                    'first_name' => $firstName,
                    'last_name' => $lastName,
                    'status' => $status,
                    'subscription_type' => $subscriptionType,
                    'api_quota' => $apiQuota,
                    'email_verified' => $emailVerified,
                    'id' => $userId
                ]);

                echo json_encode(['success' => true, 'message' => 'User updated successfully']);
                break;

            case 'toggle_status':
                if (!$usersTableExists) {
                    echo json_encode(['success' => false, 'message' => 'Users table does not exist']);
                    exit;
                }

                $userId = intval($_POST['user_id']);
                $newStatus = $_POST['status'] === 'active' ? 'active' : 'inactive';

                $stmt = $pdo->prepare("UPDATE pt_member SET status = :status WHERE id = :id");
                $stmt->execute(['status' => $newStatus, 'id' => $userId]);

                echo json_encode(['success' => true, 'message' => 'User status updated']);
                break;

            case 'delete_user':
                if (!$usersTableExists) {
                    echo json_encode(['success' => false, 'message' => 'Users table does not exist']);
                    exit;
                }

                $userId = intval($_POST['user_id']);

                $stmt = $pdo->prepare("DELETE FROM pt_member WHERE id = :id");
                $stmt->execute(['id' => $userId]);

                echo json_encode(['success' => true, 'message' => 'User deleted']);
                break;

            default:
                echo json_encode(['success' => false, 'message' => 'Invalid action']);
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
    exit;
}

// 获取当前页面
$currentPage = $_GET['page'] ?? 'dashboard';
$allowedPages = ['dashboard', 'tools', 'api', 'users', 'user-requests', 'settings', 'analytics', 'messages', 'message-view', 'tool-categories', 'profile', 'account', 'password', 'security', 'prompt', 'program'];

if (!in_array($currentPage, $allowedPages)) {
    $currentPage = 'dashboard';
}

// 处理Analytics页面的CSV导出请求（在HTML输出之前）
if ($currentPage === 'analytics' && (isset($_GET['export']) || isset($_POST['export']))) {
    // 使用统一的数据库连接
    require_once ROOT_PATH . '/includes/database-connection.php';

    try {

        $timeRange = $_GET['range'] ?? 'today';
        $startDate = $_GET['start_date'] ?? '';
        $endDate = $_GET['end_date'] ?? '';

        // 设置时间条件
        switch ($timeRange) {
            case 'today':
                $timeLabel = "Today";
                $timeCondition = "DATE(created_at) = CURDATE()";
                break;
            case 'week':
                $timeLabel = "Last 7 Days";
                $timeCondition = "created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)";
                break;
            case 'month':
                $timeLabel = "Last 30 Days";
                $timeCondition = "created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)";
                break;
            case 'custom':
                if ($startDate && $endDate) {
                    $timeLabel = "$startDate to $endDate";
                    $timeCondition = "DATE(created_at) BETWEEN '$startDate' AND '$endDate'";
                } else {
                    $timeLabel = "All Time";
                    $timeCondition = "1=1";
                }
                break;
            default:
                $timeLabel = "All Time";
                $timeCondition = "1=1";
        }

        // 获取导出数据
        $exportData = [
            'summary' => [
                'total_tools' => $pdo->query("SELECT COUNT(*) FROM pt_tool WHERE status = 'active'")->fetchColumn(),
                'total_users' => $pdo->query("SELECT COUNT(*) FROM pt_member WHERE status = 'active'")->fetchColumn(),
                'total_views' => $pdo->query("SELECT SUM(view_count) FROM pt_tool")->fetchColumn() ?: 0,
                'time_range' => $timeLabel
            ],
            'top_tools' => $pdo->query("
                SELECT t.name, t.view_count, t.slug, c.name as category_name, t.created_at
                FROM pt_tool t
                LEFT JOIN pt_tool_category c ON t.category_id = c.id
                WHERE t.status = 'active' AND t.view_count > 0
                ORDER BY t.view_count DESC
                LIMIT 20
            ")->fetchAll(),
            'user_activity' => $pdo->query("
                SELECT DATE(created_at) as date, COUNT(*) as new_users
                FROM pt_member
                WHERE $timeCondition
                GROUP BY DATE(created_at)
                ORDER BY date DESC
            ")->fetchAll(),
            'tool_usage' => $pdo->query("
                SELECT DATE(tu.created_at) as date, COUNT(*) as usage_count
                FROM pt_tool_usage tu
                WHERE $timeCondition
                GROUP BY DATE(tu.created_at)
                ORDER BY date DESC
            ")->fetchAll()
        ];

        // 导出CSV
        $filename = 'analytics_report_' . date('Y-m-d_H-i-s') . '.csv';

        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Expires: 0');

        $output = fopen('php://output', 'w');

        // 写入BOM以支持中文
        fwrite($output, "\xEF\xBB\xBF");

        // 报表标题
        fputcsv($output, ['Analytics Report - ' . $timeLabel]);
        fputcsv($output, ['Generated on: ' . date('Y-m-d H:i:s')]);
        fputcsv($output, []);

        // 摘要数据
        fputcsv($output, ['SUMMARY']);
        fputcsv($output, ['Metric', 'Value']);
        fputcsv($output, ['Total Active Tools', $exportData['summary']['total_tools']]);
        fputcsv($output, ['Total Active Users', $exportData['summary']['total_users']]);
        fputcsv($output, ['Total Views', $exportData['summary']['total_views']]);
        fputcsv($output, []);

        // 热门工具
        fputcsv($output, ['TOP PERFORMING TOOLS']);
        fputcsv($output, ['Rank', 'Tool Name', 'Category', 'Views', 'Slug', 'Created Date']);
        foreach ($exportData['top_tools'] as $index => $tool) {
            fputcsv($output, [
                $index + 1,
                $tool['name'],
                $tool['category_name'] ?? 'Uncategorized',
                $tool['view_count'],
                $tool['slug'],
                $tool['created_at']
            ]);
        }
        fputcsv($output, []);

        // 用户活动
        if (!empty($exportData['user_activity'])) {
            fputcsv($output, ['USER ACTIVITY BY DATE']);
            fputcsv($output, ['Date', 'New Users']);
            foreach ($exportData['user_activity'] as $activity) {
                fputcsv($output, [$activity['date'], $activity['new_users']]);
            }
            fputcsv($output, []);
        }

        // 工具使用
        if (!empty($exportData['tool_usage'])) {
            fputcsv($output, ['TOOL USAGE BY DATE']);
            fputcsv($output, ['Date', 'Usage Count']);
            foreach ($exportData['tool_usage'] as $usage) {
                fputcsv($output, [$usage['date'], $usage['usage_count']]);
            }
        }

        fclose($output);
        exit;

    } catch (Exception $e) {
        error_log('Analytics export error: ' . $e->getMessage());
        // 如果导出失败，继续正常页面加载
    }
}

// 页面标题映射
$pageTitles = [
    'dashboard' => 'Prompt2Tool Admin',
    'tools' => 'Tools',
    'api' => 'API',
    'tool-categories' => 'Categories',
    'users' => 'User',
    'user-requests' => 'Requests',
    'messages' => 'Messages',
    'settings' => 'Settings',
    'analytics' => 'Analytics',
    'message-view' => 'Message Details',
    'profile' => 'Profile',
    'account' => 'Account',
    'password' => 'Password',
    'security' => 'Security',
    'prompt' => 'Prompt',
    'program' => 'Program',
    'sitemap' => 'Sitemap'
];

$pageTitle = $pageTitles[$currentPage] ?? ucfirst($currentPage);

// 调试信息 (可以删除)
// echo "<!-- Debug: currentPage = $currentPage, pageTitle = $pageTitle -->";

// 加载后台布局头部
include_once MGMT_ROOT . '/components/layout/header.php';
?>

<div class="flex min-h-screen bg-gray-100">
    <!-- 侧边栏 -->
    <div class="w-64 bg-black border-r border-gray-800 flex-shrink-0" id="sidebar">
        <?php include_once MGMT_ROOT . '/components/layout/sidebar.php'; ?>
    </div>

    <!-- 主内容区 -->
    <div class="flex-1 main-content flex flex-col">
        <!-- 顶部导航栏 -->
        <div class="bg-white shadow-sm border-b border-gray-200 flex-shrink-0">
            <div class="px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-16">
                    <div class="flex items-center">
                        <!-- 移动端菜单按钮 -->
                        <button type="button" class="lg:hidden p-2 text-gray-600 hover:text-gray-900" id="mobile-menu-btn">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                            </svg>
                        </button>

                        <!-- 页面标题 -->
                        <h1 class="ml-4 text-2xl font-semibold text-gray-900">
                            <?= htmlspecialchars($pageTitle) ?>
                        </h1>
                    </div>

                    <!-- 用户菜单 - 只在dashboard页面显示 -->
                    <?php if ($currentPage === 'dashboard'): ?>
                    <div class="flex items-center space-x-4">
                        <div class="relative">
                            <button class="flex items-center space-x-2 text-gray-700 hover:text-gray-900 focus:outline-none" id="user-menu-btn">
                                <div class="w-8 h-8 bg-black flex items-center justify-center text-white text-sm font-medium">
                                    B
                                </div>
                                <span class="text-sm font-medium text-gray-700">Boss</span>
                                <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>

                            <!-- 下拉菜单 -->
                            <div class="absolute right-0 mt-2 w-48 bg-white border border-gray-200 shadow-lg hidden z-50" id="user-menu" style="display: none;">
                                <div class="py-1">
                                    <a href="?page=profile" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <i class="fas fa-user mr-2"></i>Profile
                                    </a>
                                    <a href="?page=account" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <i class="fas fa-edit mr-2"></i>Edit Account
                                    </a>
                                    <a href="?page=password" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <i class="fas fa-key mr-2"></i>Change Password
                                    </a>
                                    <a href="?page=security" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <i class="fas fa-shield-alt mr-2"></i>Security Settings
                                    </a>
                                    <div class="border-t border-gray-200"></div>
                                    <a href="auth/logout.php" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <i class="fas fa-sign-out-alt mr-2"></i>Logout
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- 页面内容 -->
        <?php if ($currentPage === 'prompt' || $currentPage === 'program'): ?>
        <div class="flex-1 flex flex-col min-h-0">
        <?php else: ?>
        <div class="p-6">
        <?php endif; ?>
            <?php
            // 加载对应的页面内容
            $pageFile = MGMT_ROOT . '/pages/' . $currentPage . '.php';
            if (file_exists($pageFile)) {
                include $pageFile;
            } else {
                include MGMT_ROOT . '/pages/dashboard.php';
            }
            ?>
        </div>
    </div>
</div>

<!-- JavaScript -->
<script>
// 页面加载完成后绑定事件
window.addEventListener('load', function() {
    const userMenuBtn = document.getElementById('user-menu-btn');
    const userMenu = document.getElementById('user-menu');

    if (userMenuBtn && userMenu) {
        // 确保菜单初始状态是隐藏的
        userMenu.classList.add('hidden');

        userMenuBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            // 切换菜单显示状态
            if (userMenu.style.display === 'none' || userMenu.style.display === '') {
                userMenu.style.display = 'block';
                userMenu.classList.remove('hidden');
                console.log('Menu shown');
            } else {
                userMenu.style.display = 'none';
                userMenu.classList.add('hidden');
                console.log('Menu hidden');
            }
        });

        // 点击外部关闭菜单
        document.addEventListener('click', function(e) {
            if (!userMenuBtn.contains(e.target) && !userMenu.contains(e.target)) {
                userMenu.style.display = 'none';
                userMenu.classList.add('hidden');
            }
        });

        // 按ESC键关闭菜单
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                userMenu.style.display = 'none';
                userMenu.classList.add('hidden');
            }
        });
    }
});
</script>

<?php
/**
 * 处理Analytics数据导出
 */
function handleAnalyticsExport() {
    try {
        // 使用统一的数据库连接
        require_once ROOT_PATH . '/includes/database-connection.php';

        // 获取时间范围参数
        $timeRange = $_GET['range'] ?? 'today';
        $startDate = $_GET['start_date'] ?? '';
        $endDate = $_GET['end_date'] ?? '';

        // 设置时间条件
        switch ($timeRange) {
            case 'today':
                $timeCondition = "DATE(created_at) = CURDATE()";
                $timeLabel = "Today";
                break;
            case 'week':
                $timeCondition = "created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)";
                $timeLabel = "Last_7_Days";
                break;
            case 'month':
                $timeCondition = "created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)";
                $timeLabel = "Last_30_Days";
                break;
            case 'custom':
                if ($startDate && $endDate) {
                    $timeCondition = "DATE(created_at) BETWEEN '$startDate' AND '$endDate'";
                    $timeLabel = $startDate . "_to_" . $endDate;
                } else {
                    $timeCondition = "1=1";
                    $timeLabel = "All_Time";
                }
                break;
            default:
                $timeCondition = "1=1";
                $timeLabel = "All_Time";
        }

        // 设置CSV响应头
        $filename = "analytics_data_" . $timeLabel . "_" . date('Y-m-d') . ".csv";
        header('Content-Type: text/csv; charset=utf-8');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Cache-Control: no-cache, must-revalidate');

        // 创建输出流
        $output = fopen('php://output', 'w');

        // 添加BOM以支持Excel中的UTF-8
        fwrite($output, "\xEF\xBB\xBF");

        // 导出用户数据
        fputcsv($output, ['=== USER STATISTICS ===']);
        fputcsv($output, ['Metric', 'Value']);

        $totalUsers = $pdo->query("SELECT COUNT(*) FROM pt_member WHERE status = 'active'")->fetchColumn();
        $newUsers = $pdo->query("SELECT COUNT(*) FROM pt_member WHERE $timeCondition")->fetchColumn();

        fputcsv($output, ['Total Active Users', $totalUsers]);
        fputcsv($output, ['New Users (Period)', $newUsers]);
        fputcsv($output, ['']);

        // 导出工具数据
        fputcsv($output, ['=== TOOL STATISTICS ===']);
        fputcsv($output, ['Tool Name', 'Category', 'View Count', 'Status']);

        $tools = $pdo->query("
            SELECT t.name, c.name as category_name, t.view_count, t.status
            FROM pt_tool t
            LEFT JOIN pt_tool_category c ON t.category_id = c.id
            ORDER BY t.view_count DESC
        ")->fetchAll();

        foreach ($tools as $tool) {
            fputcsv($output, [
                $tool['name'],
                $tool['category_name'] ?? 'Uncategorized',
                $tool['view_count'] ?? 0,
                $tool['status']
            ]);
        }

        fputcsv($output, ['']);

        // 导出API访问日志
        fputcsv($output, ['=== API ACCESS LOGS ===']);
        fputcsv($output, ['Date', 'Endpoint', 'Method', 'Response Code', 'Response Time']);

        $apiLogs = $pdo->query("
            SELECT DATE(created_at) as date, endpoint, method, response_code, response_time
            FROM pt_api_access_logs
            WHERE $timeCondition
            ORDER BY created_at DESC
            LIMIT 1000
        ")->fetchAll();

        foreach ($apiLogs as $log) {
            fputcsv($output, [
                $log['date'],
                $log['endpoint'],
                $log['method'],
                $log['response_code'],
                $log['response_time'] . 'ms'
            ]);
        }

        fclose($output);

    } catch (Exception $e) {
        http_response_code(500);
        echo "Export failed: " . $e->getMessage();
    }
}
?>

<?php include_once MGMT_ROOT . '/components/layout/footer.php'; ?>
