<?php
/**
 * 图片上传处理
 * 支持Logo和截图上传
 */

header('Content-Type: application/json');

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit;
}

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// 检查用户登录状态
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

$userId = $_SESSION['user_id'];

try {
    // 检查是否有文件上传
    if (!isset($_FILES['image']) || $_FILES['image']['error'] !== UPLOAD_ERR_OK) {
        $errorMessages = [
            UPLOAD_ERR_INI_SIZE => 'File too large (exceeds php.ini limit)',
            UPLOAD_ERR_FORM_SIZE => 'File too large (exceeds form limit)',
            UPLOAD_ERR_PARTIAL => 'File upload incomplete',
            UPLOAD_ERR_NO_FILE => 'No file uploaded',
            UPLOAD_ERR_NO_TMP_DIR => 'No temporary directory',
            UPLOAD_ERR_CANT_WRITE => 'Cannot write to disk',
            UPLOAD_ERR_EXTENSION => 'Upload stopped by extension'
        ];
        
        $error = $_FILES['image']['error'] ?? UPLOAD_ERR_NO_FILE;
        $message = $errorMessages[$error] ?? 'Unknown upload error';
        
        echo json_encode(['success' => false, 'message' => $message]);
        exit;
    }

    $file = $_FILES['image'];
    $uploadType = $_POST['type'] ?? 'logo'; // logo 或 screenshot
    $productName = $_POST['product_name'] ?? ''; // 产品名称
    
    // 验证文件类型
    $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    $fileType = $file['type'];
    
    if (!in_array($fileType, $allowedTypes)) {
        echo json_encode(['success' => false, 'message' => 'Invalid file type. Only JPEG, PNG, GIF, and WebP are allowed.']);
        exit;
    }
    
    // 验证文件大小 (最大5MB)
    $maxSize = 5 * 1024 * 1024; // 5MB
    if ($file['size'] > $maxSize) {
        echo json_encode(['success' => false, 'message' => 'File too large. Maximum size is 5MB.']);
        exit;
    }
    
    // 验证图片尺寸
    $imageInfo = getimagesize($file['tmp_name']);
    if ($imageInfo === false) {
        echo json_encode(['success' => false, 'message' => 'Invalid image file.']);
        exit;
    }
    
    $width = $imageInfo[0];
    $height = $imageInfo[1];
    
    // 根据类型设置尺寸限制
    if ($uploadType === 'logo') {
        // Logo：前端已处理裁剪，这里只做基本验证
        if ($width < 32 || $height < 32) {
            echo json_encode(['success' => false, 'message' => 'Logo too small. Minimum size is 32x32 pixels.']);
            exit;
        }
        if ($width > 1024 || $height > 1024) {
            echo json_encode(['success' => false, 'message' => 'Logo too large. Maximum size is 1024x1024 pixels.']);
            exit;
        }
    } else {
        // 截图建议尺寸：最小400x300，最大2048x1536
        if ($width < 400 || $height < 300) {
            echo json_encode(['success' => false, 'message' => 'Screenshot too small. Minimum size is 400x300 pixels.']);
            exit;
        }
        if ($width > 2048 || $height > 1536) {
            echo json_encode(['success' => false, 'message' => 'Screenshot too large. Maximum size is 2048x1536 pixels.']);
            exit;
        }
    }
    
    // 根据上传类型确定目录
    $subDir = ($uploadType === 'logo') ? 'logos' : 'screenshots';
    $uploadDir = dirname(dirname(__DIR__)) . '/public/uploads/launches/' . $subDir . '/';

    if (!is_dir($uploadDir)) {
        if (!mkdir($uploadDir, 0755, true)) {
            echo json_encode(['success' => false, 'message' => 'Failed to create upload directory.']);
            exit;
        }
    }

    // 生成基于产品名称的文件名
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);

    // 如果有产品名称，使用基于产品名称的命名；否则使用备用方案
    if (!empty($productName)) {
        $fileName = generateSafeFilename($productName, $uploadType, $extension);
    } else {
        // 备用方案：使用原来的命名规则
        $fileName = $uploadType . '_' . $userId . '_' . time() . '_' . uniqid() . '.' . $extension;
    }

    // 如果文件已存在，添加时间戳确保唯一性
    if (file_exists($uploadDir . $fileName)) {
        $nameWithoutExt = pathinfo($fileName, PATHINFO_FILENAME);
        $fileName = $nameWithoutExt . '-' . time() . '.' . $extension;
    }

    $filePath = $uploadDir . $fileName;

    // 移动上传文件
    if (!move_uploaded_file($file['tmp_name'], $filePath)) {
        echo json_encode(['success' => false, 'message' => 'Failed to save uploaded file.']);
        exit;
    }

    // 生成访问URL
    $baseUrl = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http') .
               '://' . $_SERVER['HTTP_HOST'];
    $fileUrl = $baseUrl . '/uploads/launches/' . $subDir . '/' . $fileName;
    
    // 如果是截图，可能需要生成缩略图
    if ($uploadType === 'screenshot') {
        // 生成缩略图文件名，保持与原文件名的一致性
        $nameWithoutExt = pathinfo($fileName, PATHINFO_FILENAME);
        $extension = pathinfo($fileName, PATHINFO_EXTENSION);
        $thumbnailFileName = $nameWithoutExt . '-thumb.' . $extension;
        $thumbnailPath = $uploadDir . $thumbnailFileName;

        if (createThumbnail($filePath, $thumbnailPath, 400, 300)) {
            $thumbnailUrl = $baseUrl . '/uploads/launches/' . $subDir . '/' . $thumbnailFileName;
        }
    }
    
    echo json_encode([
        'success' => true,
        'message' => 'File uploaded successfully',
        'url' => $fileUrl,
        'filename' => $fileName,
        'size' => $file['size'],
        'dimensions' => ['width' => $width, 'height' => $height],
        'thumbnail' => $thumbnailUrl ?? null
    ]);

} catch (Exception $e) {
    error_log("Upload error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Upload failed: ' . $e->getMessage()]);
}

/**
 * 生成安全的文件名
 */
function generateSafeFilename($productName, $type, $extension, $index = null) {
    // 清理产品名称，只保留字母、数字、空格和连字符
    $safeName = preg_replace('/[^a-zA-Z0-9\s\-_]/', '', $productName);
    // 将空格替换为连字符
    $safeName = preg_replace('/\s+/', '-', trim($safeName));
    // 转换为小写
    $safeName = strtolower($safeName);
    // 限制长度
    $safeName = substr($safeName, 0, 50);

    // 如果清理后为空，使用随机生成的备用方案
    if (empty($safeName)) {
        $safeName = 'product-' . uniqid();
    }

    // 生成文件名
    if ($type === 'logo') {
        return $safeName . '-logo.' . $extension;
    } else {
        $suffix = $index !== null ? '-screenshot-' . ($index + 1) : '-screenshot';
        return $safeName . $suffix . '.' . $extension;
    }
}

/**
 * 创建缩略图
 */
function createThumbnail($sourcePath, $destPath, $maxWidth, $maxHeight) {
    try {
        $imageInfo = getimagesize($sourcePath);
        if (!$imageInfo) return false;
        
        $sourceWidth = $imageInfo[0];
        $sourceHeight = $imageInfo[1];
        $sourceType = $imageInfo[2];
        
        // 计算缩略图尺寸
        $ratio = min($maxWidth / $sourceWidth, $maxHeight / $sourceHeight);
        $thumbWidth = intval($sourceWidth * $ratio);
        $thumbHeight = intval($sourceHeight * $ratio);
        
        // 创建源图像资源
        switch ($sourceType) {
            case IMAGETYPE_JPEG:
                $sourceImage = imagecreatefromjpeg($sourcePath);
                break;
            case IMAGETYPE_PNG:
                $sourceImage = imagecreatefrompng($sourcePath);
                break;
            case IMAGETYPE_GIF:
                $sourceImage = imagecreatefromgif($sourcePath);
                break;
            case IMAGETYPE_WEBP:
                $sourceImage = imagecreatefromwebp($sourcePath);
                break;
            default:
                return false;
        }
        
        if (!$sourceImage) return false;
        
        // 创建缩略图画布
        $thumbImage = imagecreatetruecolor($thumbWidth, $thumbHeight);
        
        // 保持透明度
        if ($sourceType == IMAGETYPE_PNG || $sourceType == IMAGETYPE_GIF) {
            imagealphablending($thumbImage, false);
            imagesavealpha($thumbImage, true);
            $transparent = imagecolorallocatealpha($thumbImage, 255, 255, 255, 127);
            imagefilledrectangle($thumbImage, 0, 0, $thumbWidth, $thumbHeight, $transparent);
        }
        
        // 缩放图像
        imagecopyresampled($thumbImage, $sourceImage, 0, 0, 0, 0, 
                          $thumbWidth, $thumbHeight, $sourceWidth, $sourceHeight);
        
        // 保存缩略图
        $result = false;
        switch ($sourceType) {
            case IMAGETYPE_JPEG:
                $result = imagejpeg($thumbImage, $destPath, 85);
                break;
            case IMAGETYPE_PNG:
                $result = imagepng($thumbImage, $destPath, 6);
                break;
            case IMAGETYPE_GIF:
                $result = imagegif($thumbImage, $destPath);
                break;
            case IMAGETYPE_WEBP:
                $result = imagewebp($thumbImage, $destPath, 85);
                break;
        }
        
        // 清理资源
        imagedestroy($sourceImage);
        imagedestroy($thumbImage);
        
        return $result;
        
    } catch (Exception $e) {
        error_log("Thumbnail creation error: " . $e->getMessage());
        return false;
    }
}
?>
