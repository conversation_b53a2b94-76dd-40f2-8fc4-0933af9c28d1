<?php
/**
 * 检查slug唯一性API
 */

// 清理输出缓冲区
if (ob_get_level()) {
    ob_clean();
}

// 关闭错误显示，避免影响JSON响应
ini_set('display_errors', 0);
error_reporting(E_ALL);

// 设置JSON响应头
header('Content-Type: application/json; charset=utf-8');

// 安全检查
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

session_start();

// 使用统一的数据库连接
require_once __DIR__ . '/includes/ajax-database.php';

// 获取请求数据
$input = json_decode(file_get_contents('php://input'), true);
$slug = $input['slug'] ?? '';
$excludeId = $input['exclude_id'] ?? null;

if (empty($slug)) {
    echo json_encode([
        'success' => false,
        'available' => false,
        'message' => 'Slug is required'
    ]);
    exit;
}

// 验证slug格式
if (!preg_match('/^[a-z0-9\-]+$/', $slug)) {
    echo json_encode([
        'success' => false,
        'available' => false,
        'message' => 'Invalid slug format'
    ]);
    exit;
}

// 检查长度
if (strlen($slug) < 3 || strlen($slug) > 60) {
    echo json_encode([
        'success' => false,
        'available' => false,
        'message' => 'Slug length must be between 3 and 60 characters'
    ]);
    exit;
}

try {
    // 检查slug是否已存在
    $sql = "SELECT COUNT(*) FROM pt_user_requests WHERE slug = ?";
    $params = [$slug];
    
    if ($excludeId) {
        $sql .= " AND id != ?";
        $params[] = $excludeId;
    }
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    
    $count = $stmt->fetchColumn();
    $available = ($count == 0);
    
    echo json_encode([
        'success' => true,
        'available' => $available,
        'message' => $available ? 'Slug is available' : 'Slug is already taken'
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'available' => false,
        'message' => 'Error checking slug availability'
    ]);
}
