<?php
/**
 * 管理后台登录页面
 * 提供安全的管理员认证
 */

// 定义根目录
define('ROOT_PATH', dirname(dirname(__DIR__)));

// 加载应用初始化
require_once ROOT_PATH . '/app/init.php';

// 使用统一的数据库连接
require_once ROOT_PATH . '/includes/database-connection.php';

// 如果已经登录，重定向到仪表板
// session_start(); // 已在 app/init.php 中启动，无需重复
if (isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true) {
    header('Location: ../index.php');
    exit;
}

// 检查Remember Me Token
if (!isset($_SESSION['admin_logged_in']) && isset($_COOKIE['admin_remember_token'])) {
    $token = $_COOKIE['admin_remember_token'];
    $hashedToken = hash('sha256', $token);

    try {
        // 检查token是否有效
        $tokenSql = "SELECT a.id, a.username, a.email, a.display_name, a.role
                     FROM pt_manager_token rt
                     JOIN pt_manager a ON rt.manager_id = a.id
                     WHERE rt.token = ? AND rt.expires_at > NOW()";
        $tokenStmt = $pdo->prepare($tokenSql);
        $tokenStmt->execute([$hashedToken]);
        $admin = $tokenStmt->fetch();

        if ($admin) {
            // 自动登录
            $_SESSION['admin_logged_in'] = true;
            $_SESSION['admin_id'] = $admin['id'];
            $_SESSION['admin_username'] = $admin['username'];
            $_SESSION['admin_email'] = $admin['email'];
            $_SESSION['admin_display_name'] = $admin['display_name'];
            $_SESSION['admin_role'] = $admin['role'];
            $_SESSION['admin_login_time'] = time();

            header('Location: ../index.php');
            exit;
        } else {
            // Token无效，删除Cookie
            setcookie('admin_remember_token', '', time() - 3600, '/', '', true, true);
        }
    } catch (PDOException $e) {
        error_log("Remember token check error: " . $e->getMessage());
    }
}

$error = '';
$success = '';

// 处理登录表单提交
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = $_POST['username'] ?? '';
    $password = $_POST['password'] ?? '';
    $remember = isset($_POST['remember']);
    
    // 验证输入
    if (empty($username) || empty($password)) {
        $error = 'Please enter both username and password.';
    } else {
        // Database authentication
        try {
            // 数据库连接已在上面加载

            $sql = "SELECT id, username, email, display_name, role, password FROM pt_manager WHERE username = ? AND status = 'active'";
            $stmt = $pdo->prepare($sql);
            $stmt->execute([$username]);
            $admin = $stmt->fetch();

            if ($admin && password_verify($password, $admin['password'])) {
                // Update last login
                $updateSql = "UPDATE pt_manager SET last_login = NOW() WHERE id = ?";
                $updateStmt = $pdo->prepare($updateSql);
                $updateStmt->execute([$admin['id']]);

                // Login successful
                $_SESSION['admin_logged_in'] = true;
                $_SESSION['admin_id'] = $admin['id'];
                $_SESSION['admin_username'] = $admin['username'];
                $_SESSION['admin_email'] = $admin['email'];
                $_SESSION['admin_display_name'] = $admin['display_name'];
                $_SESSION['admin_role'] = $admin['role'];
                $_SESSION['admin_login_time'] = time();

                // Create session record
                try {
                    $sessionSql = "INSERT INTO pt_manager_session (manager_id, session_id, ip_address, user_agent, device_info, last_activity, is_active)
                                   VALUES (?, ?, ?, ?, ?, NOW(), 1)
                                   ON DUPLICATE KEY UPDATE last_activity = NOW(), is_active = 1";
                    $sessionStmt = $pdo->prepare($sessionSql);
                    $sessionStmt->execute([
                        $admin['id'],
                        session_id(),
                        $_SERVER['REMOTE_ADDR'] ?? 'localhost',
                        $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown Browser',
                        parseUserAgent($_SERVER['HTTP_USER_AGENT'] ?? '')
                    ]);
                } catch (PDOException $e) {
                    error_log("Session creation error: " . $e->getMessage());
                }

                // Log login activity
                try {
                    require_once __DIR__ . '/../includes/activity-logger.php';
                    logLogin($admin['id']);
                } catch (Exception $e) {
                    // Ignore activity log errors
                }
            } else {
                $error = 'Invalid username or password.';
            }
        } catch (PDOException $e) {
            error_log("Database error in login: " . $e->getMessage());

            // Fallback authentication for demo (remove in production)
            if ($username === 'admin' && $password === 'password') {
                $_SESSION['admin_logged_in'] = true;
                $_SESSION['admin_id'] = 1;
                $_SESSION['admin_username'] = $username;
                $_SESSION['admin_login_time'] = time();
            } else {
                $error = 'Invalid username or password.';
            }
        }

        if (isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in']) {
            
            // 记住我功能
            if ($remember) {
                $adminId = $_SESSION['admin_id'];
                $token = bin2hex(random_bytes(32));
                $expires = time() + (30 * 24 * 60 * 60); // 30天

                // 设置Cookie
                setcookie('admin_remember_token', $token, $expires, '/', '', false, true);

                // 存储到数据库
                try {
                    // 先创建表（如果不存在）
                    $createTableSql = "
                    CREATE TABLE IF NOT EXISTS `pt_manager_token` (
                        `id` int(11) NOT NULL AUTO_INCREMENT,
                        `manager_id` int(11) NOT NULL,
                        `token` varchar(255) NOT NULL,
                        `expires_at` datetime NOT NULL,
                        `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
                        PRIMARY KEY (`id`),
                        UNIQUE KEY `manager_id` (`manager_id`),
                        KEY `token` (`token`),
                        KEY `expires_at` (`expires_at`)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
                    ";
                    $pdo->exec($createTableSql);

                    // 存储token
                    $tokenSql = "INSERT INTO pt_manager_token (manager_id, token, expires_at, created_at) VALUES (?, ?, ?, NOW())
                                 ON DUPLICATE KEY UPDATE token = VALUES(token), expires_at = VALUES(expires_at)";
                    $tokenStmt = $pdo->prepare($tokenSql);
                    $tokenStmt->execute([$adminId, hash('sha256', $token), date('Y-m-d H:i:s', $expires)]);

                    error_log("Remember token created for admin ID: {$adminId}");
                } catch (PDOException $e) {
                    error_log("Remember token storage error: " . $e->getMessage());
                }
            }
            
            // 记录登录日志
            error_log("Admin login: {$username} from " . getClientIP());
            
            header('Location: ../index.php');
            exit;
        } else {
            $error = 'Invalid username or password.';
            // 记录失败的登录尝试
            error_log("Failed admin login attempt: {$username} from " . getClientIP());
        }
    }
}
// Helper function
function parseUserAgent($userAgent) {
    if (strpos($userAgent, 'Chrome') !== false) return 'Chrome Browser';
    if (strpos($userAgent, 'Firefox') !== false) return 'Firefox Browser';
    if (strpos($userAgent, 'Safari') !== false) return 'Safari Browser';
    if (strpos($userAgent, 'Edge') !== false) return 'Edge Browser';
    return 'Desktop Browser';
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Login - Prompt2Tool</title>
    <meta name="description" content="Secure admin login portal for Prompt2Tool platform management.">

    <!-- 安全头部 -->
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta http-equiv="X-Frame-Options" content="DENY">
    <meta http-equiv="X-XSS-Protection" content="1; mode=block">
    <meta name="referrer" content="strict-origin-when-cross-origin">
    <meta name="robots" content="noindex, nofollow, noarchive, nosnippet">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        accent: '#4e73df'
                    }
                }
            }
        }
    </script>
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .login-card {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        }
        
        .login-form {
            animation: slideUp 0.6s ease-out;
        }
        
        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body class="min-h-screen flex items-center justify-center p-4">
    <div class="login-card w-full max-w-md p-8 shadow-2xl">
        <div class="login-form">
            <!-- Logo和标题 -->
            <div class="text-center mb-8">
                <div class="w-16 h-16 bg-accent text-white flex items-center justify-center text-2xl font-bold mx-auto mb-4">
                    P
                </div>
                <h1 class="text-2xl font-bold text-gray-900">Control Panel</h1>
                <p class="text-gray-600 mt-2">Sign in to manage Prompt2Tool</p>
            </div>

            <!-- 错误/成功消息 -->
            <?php if ($error): ?>
            <div class="mb-6 p-4 bg-red-100 border border-red-400 text-red-700">
                <div class="flex items-center">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <?= htmlspecialchars($error) ?>
                </div>
            </div>
            <?php endif; ?>

            <?php if ($success): ?>
            <div class="mb-6 p-4 bg-green-100 border border-green-400 text-green-700">
                <div class="flex items-center">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    <?= htmlspecialchars($success) ?>
                </div>
            </div>
            <?php endif; ?>

            <!-- 登录表单 -->
            <form method="POST" class="space-y-6">
                <!-- 用户名 -->
                <div>
                    <label for="username" class="block text-sm font-medium text-gray-700 mb-2">
                        Username
                    </label>
                    <input type="text"
                           id="username"
                           name="username"
                           value="<?= htmlspecialchars($_POST['username'] ?? '') ?>"
                           required
                           autocomplete="username"
                           class="w-full px-3 py-2 border border-gray-300 focus:outline-none focus:border-accent"
                           placeholder="Enter your username">
                </div>

                <!-- 密码 -->
                <div>
                    <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                        Password
                    </label>
                    <input type="password"
                           id="password"
                           name="password"
                           required
                           autocomplete="current-password"
                           class="w-full px-3 py-2 border border-gray-300 focus:outline-none focus:border-accent"
                           placeholder="Enter your password">
                </div>

                <!-- 记住我 -->
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <input type="checkbox" 
                               id="remember" 
                               name="remember" 
                               class="w-4 h-4 text-accent border-gray-300 focus:ring-accent">
                        <label for="remember" class="ml-2 text-sm text-gray-700">
                            Remember me
                        </label>
                    </div>
                    

                </div>

                <!-- 登录按钮 -->
                <button type="submit" 
                        class="w-full bg-accent text-white py-3 px-4 hover:bg-blue-600 focus:ring-2 focus:ring-accent focus:ring-offset-2 transition-colors font-medium">
                    Sign In
                </button>
            </form>


        </div>
    </div>

    <!-- 登录页面JavaScript -->
    <script>
        // 自动聚焦到用户名输入框
        document.getElementById('username').focus();
        
        // 表单验证
        document.querySelector('form').addEventListener('submit', function(e) {
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;
            
            if (!username || !password) {
                e.preventDefault();
                alert('Please enter both username and password.');
                return false;
            }
            
            // 显示加载状态
            const submitBtn = this.querySelector('button[type="submit"]');
            submitBtn.textContent = 'Signing In...';
            submitBtn.disabled = true;
        });
        
        // 安全检查：检测开发者工具
        let devtools = {open: false, orientation: null};
        const threshold = 160;
        
        setInterval(() => {
            if (window.outerHeight - window.innerHeight > threshold || 
                window.outerWidth - window.innerWidth > threshold) {
                if (!devtools.open) {
                    devtools.open = true;
                    // Security warning for admin area
                }
            } else {
                devtools.open = false;
            }
        }, 500);
    </script>
</body>
</html>
