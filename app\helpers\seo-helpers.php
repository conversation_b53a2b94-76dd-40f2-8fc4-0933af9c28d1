<?php
/**
 * SEO辅助函数
 * 为SEO模板提供数据和功能支持
 */

/**
 * 获取页面SEO数据
 */
function getSEOData($page) {
    $seoData = [];
    
    switch ($page) {
        case 'home':
            $seoData = [
                'title' => 'Prompt2Tool - Free AI-Powered Online Tools Platform',
                'description' => 'Discover 100+ free online tools powered by AI. HTML formatter, CSS minifier, image converter, SEO analyzer, and more. Boost your productivity with Prompt2Tool.',
                'og_title' => 'Prompt2Tool - Free AI-Powered Online Tools',
                'og_description' => 'Access 100+ free online tools powered by AI. Perfect for developers, designers, and digital marketers.',
                'keywords' => 'online tools, free tools, AI tools, HTML formatter, CSS minifier, image converter, SEO tools, developer tools, productivity tools'
            ];
            break;
            
        case 'tools':
            $seoData = [
                'title' => 'All Tools - Prompt2Tool',
                'description' => 'Browse all free online tools available on Prompt2Tool. Find the perfect tool for your development, design, and productivity needs.',
                'og_title' => 'All Tools - Prompt2Tool',
                'og_description' => 'Browse 100+ free online tools for developers, designers, and digital marketers.',
                'keywords' => 'online tools, web tools, developer tools, design tools, productivity tools, free tools'
            ];
            break;
            
        case 'about':
            $seoData = [
                'title' => 'About Us - Prompt2Tool',
                'description' => 'Learn about Prompt2Tool, our mission to provide free AI-powered online tools for developers, designers, and digital professionals.',
                'og_title' => 'About Prompt2Tool',
                'og_description' => 'Learn about our mission to provide free AI-powered online tools for everyone.',
                'keywords' => 'about prompt2tool, AI tools platform, free online tools, developer tools'
            ];
            break;
            
        case 'contact':
            $seoData = [
                'title' => 'Contact Us - Prompt2Tool',
                'description' => 'Get in touch with the Prompt2Tool team. We\'d love to hear your feedback and suggestions for new tools.',
                'og_title' => 'Contact Prompt2Tool',
                'og_description' => 'Get in touch with our team. We\'d love to hear your feedback and suggestions.',
                'keywords' => 'contact prompt2tool, feedback, support, suggestions'
            ];
            break;
            
        default:
            $seoData = [
                'title' => 'Prompt2Tool - Free AI-Powered Online Tools',
                'description' => 'Free online tools powered by AI for developers, designers, and digital professionals.',
                'og_title' => 'Prompt2Tool - Free Online Tools',
                'og_description' => 'Free online tools powered by AI.',
                'keywords' => 'online tools, free tools, AI tools'
            ];
            break;
    }
    
    return $seoData;
}

/**
 * 生成结构化数据
 */
function generateStructuredData($page, $seo) {
    $baseData = [
        '@context' => 'https://schema.org',
        '@type' => 'WebSite',
        'name' => 'Prompt2Tool',
        'description' => 'Free AI-powered online tools platform',
        'url' => 'https://' . ($_SERVER['HTTP_HOST'] ?? 'localhost'),
        'potentialAction' => [
            '@type' => 'SearchAction',
            'target' => [
                '@type' => 'EntryPoint',
                'urlTemplate' => 'https://' . ($_SERVER['HTTP_HOST'] ?? 'localhost') . '/search?q={search_term_string}'
            ],
            'query-input' => 'required name=search_term_string'
        ],
        'publisher' => [
            '@type' => 'Organization',
            'name' => 'Prompt2Tool',
            'url' => 'https://' . ($_SERVER['HTTP_HOST'] ?? 'localhost'),
            'logo' => [
                '@type' => 'ImageObject',
                'url' => 'https://' . ($_SERVER['HTTP_HOST'] ?? 'localhost') . '/assets/images/logo.png'
            ]
        ]
    ];
    
    // 根据页面类型添加特定的结构化数据
    switch ($page) {
        case 'home':
            $baseData['@type'] = 'WebSite';
            $baseData['mainEntity'] = [
                '@type' => 'SoftwareApplication',
                'name' => 'Prompt2Tool',
                'applicationCategory' => 'DeveloperApplication',
                'operatingSystem' => 'Web Browser',
                'offers' => [
                    '@type' => 'Offer',
                    'price' => '0',
                    'priceCurrency' => 'USD'
                ]
            ];
            break;
            
        case 'tools':
            $baseData['@type'] = 'CollectionPage';
            $baseData['mainEntity'] = [
                '@type' => 'ItemList',
                'name' => 'Online Tools Collection',
                'description' => 'Collection of free online tools for developers and designers'
            ];
            break;
    }
    
    return $baseData;
}

/**
 * 生成面包屑导航数据
 */
function generateBreadcrumbs($currentPage, $category = null, $tool = null) {
    $breadcrumbs = [
        ['name' => 'Home', 'url' => '/']
    ];
    
    if ($currentPage === 'tools') {
        $breadcrumbs[] = ['name' => 'Tools'];
    } elseif ($category) {
        $breadcrumbs[] = ['name' => 'Tools', 'url' => '/tools'];
        $breadcrumbs[] = ['name' => ucfirst($category), 'url' => "/tools/$category"];
        
        if ($tool) {
            $breadcrumbs[] = ['name' => ucfirst(str_replace('-', ' ', $tool))];
        }
    }
    
    return $breadcrumbs;
}

// 工具分类数据已移至 tool-helpers.php 文件中
