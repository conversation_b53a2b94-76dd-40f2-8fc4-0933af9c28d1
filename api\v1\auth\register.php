<?php
/**
 * 用户注册API
 * 处理普通邮箱注册
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// 使用统一的数据库连接
require_once dirname(dirname(dirname(__DIR__))) . '/includes/database-connection.php';

try {
    // 获取请求数据
    $input = json_decode(file_get_contents('php://input'), true);
    
    // 验证必需字段
    $requiredFields = ['first_name', 'last_name', 'email', 'password', 'confirm_password'];
    foreach ($requiredFields as $field) {
        if (!isset($input[$field]) || empty(trim($input[$field]))) {
            throw new Exception("Field '{$field}' is required");
        }
    }
    
    $firstName = trim($input['first_name']);
    $lastName = trim($input['last_name']);
    $email = trim(strtolower($input['email']));
    $password = $input['password'];
    $confirmPassword = $input['confirm_password'];
    
    // 验证邮箱格式
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        throw new Exception('Invalid email format');
    }
    
    // 验证密码匹配
    if ($password !== $confirmPassword) {
        throw new Exception('Passwords do not match');
    }
    
    // 验证密码强度
    if (strlen($password) < 8) {
        throw new Exception('Password must be at least 8 characters long');
    }
    
    // 检查密码复杂度 - 与前端验证保持一致
    if (!preg_match('/(?=.*[a-z])/', $password)) {
        throw new Exception('Password must contain at least one lowercase letter');
    }
    if (!preg_match('/(?=.*[A-Z])/', $password)) {
        throw new Exception('Password must contain at least one uppercase letter');
    }
    if (!preg_match('/(?=.*\d)/', $password)) {
        throw new Exception('Password must contain at least one number');
    }
    if (!preg_match('/(?=.*[!@#$%^&*()_+\-=\[\]{}|;:,.<>?])/', $password)) {
        throw new Exception('Password must contain at least one special character');
    }
    
    // 检查邮箱是否已存在
    $stmt = $pdo->prepare("SELECT id FROM pt_member WHERE email = ?");
    $stmt->execute([$email]);
    if ($stmt->fetch()) {
        throw new Exception('Email address is already registered');
    }
    
    // 生成用户名
    $username = generateUniqueUsername($email, $pdo);
    
    // 哈希密码
    $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
    
    // 生成6位数字验证码
    $verificationCode = sprintf('%06d', mt_rand(100000, 999999));

    // 插入新用户
    $stmt = $pdo->prepare("
        INSERT INTO pt_member (
            username, email, password, first_name, last_name,
            status, email_verified, email_verification_token,
            api_quota, api_used, subscription_type,
            created_at, updated_at
        ) VALUES (?, ?, ?, ?, ?, 'pending', 0, ?, 1000, 0, 'free', NOW(), NOW())
    ");

    $stmt->execute([
        $username,
        $email,
        $hashedPassword,
        $firstName,
        $lastName,
        $verificationCode
    ]);

    $userId = $pdo->lastInsertId();
    
    // 记录活动日志
    try {
        $stmt = $pdo->prepare("
            INSERT INTO pt_member_activity_log (user_id, action, description, ip_address, user_agent, created_at)
            VALUES (?, 'register', 'User registered with email', ?, ?, NOW())
        ");
        $stmt->execute([
            $userId,
            $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        ]);
    } catch (Exception $e) {
        // 日志记录失败不影响注册流程
        error_log("Failed to log registration activity: " . $e->getMessage());
    }
    
    echo json_encode([
        'success' => true,
        'message' => 'Registration successful! Please enter the verification code to activate your account.',
        'data' => [
            'user_id' => $userId,
            'email' => $email,
            'verification_required' => true,
            'verification_code' => $verificationCode, // 前端显示给用户
            'redirect' => '/auth/verify-email?email=' . urlencode($email)
        ]
    ]);
    
} catch (Exception $e) {
    error_log("Registration error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

/**
 * 生成唯一用户名
 */
function generateUniqueUsername($email, $pdo) {
    $baseUsername = strtolower(explode('@', $email)[0]);
    $baseUsername = preg_replace('/[^a-z0-9_]/', '', $baseUsername);
    
    // 确保用户名至少3个字符
    if (strlen($baseUsername) < 3) {
        $baseUsername = 'user_' . $baseUsername;
    }
    
    $username = $baseUsername;
    $counter = 1;
    
    while (true) {
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM pt_member WHERE username = ?");
        $stmt->execute([$username]);
        
        if ($stmt->fetchColumn() == 0) {
            return $username;
        }
        
        $username = $baseUsername . '_' . $counter;
        $counter++;
        
        // 防止无限循环
        if ($counter > 1000) {
            $username = $baseUsername . '_' . uniqid();
            break;
        }
    }
    
    return $username;
}


?>
