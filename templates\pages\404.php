<?php
/**
 * 404错误页面
 */

$currentPage = '404';
$breadcrumbs = [
    ['name' => 'Home', 'url' => '/'],
    ['name' => '404 - Page Not Found']
];

// 检测是否是工具页面的404，如果是则获取工具状态信息
$toolStatusInfo = null;
$requestUri = $_SERVER['REQUEST_URI'] ?? '';
if (preg_match('/^\/tools\/([^\/]+)\/([^\/]+)/', $requestUri, $matches)) {
    $categorySlug = $matches[1];
    $toolSlug = $matches[2];

    // 尝试获取工具信息（包括非active状态的）
    try {
        require_once ROOT_PATH . '/app/helpers/tool-helpers.php';
        $toolData = getToolBySlug($toolSlug);

        if ($toolData && $toolData['status'] !== 'active') {
            $toolStatusInfo = [
                'name' => $toolData['name'],
                'status' => $toolData['status'],
                'category' => $categorySlug
            ];
        }
    } catch (Exception $e) {
        error_log("404 tool status check error: " . $e->getMessage());
    }
}

// 包含动态SEO助手
require_once ROOT_PATH . '/app/helpers/dynamic-seo.php';

// 生成404页面的SEO数据
$seoData = getDynamicSEOData('404', [
    'title' => 'Page Not Found - Prompt2Tool',
    'description' => 'The page you are looking for could not be found. Explore our free online tools and find what you need.',
    'og_title' => 'Page Not Found - Prompt2Tool',
    'og_description' => 'The page you are looking for could not be found. Explore our free online tools.'
]);

// 获取随机5个工具
$randomTools = [];
try {
    // 引入工具辅助函数
    require_once ROOT_PATH . '/app/helpers/tool-helpers.php';

    // 使用getPopularTools函数获取热门工具，然后随机选择6个
    $popularTools = getPopularTools(20); // 先获取20个热门工具

    if (!empty($popularTools)) {
        // 随机打乱数组并取前6个
        shuffle($popularTools);
        $randomTools = array_slice($popularTools, 0, 6);
    }

} catch (Exception $e) {
    // 如果获取失败，不显示工具列表
    error_log("404 page tools error: " . $e->getMessage());
    $randomTools = [];
}

// 包含公共头部
include_once ROOT_PATH . '/templates/layout/header.php';
?>

<!-- 404页面内容 -->
<section class="min-h-screen bg-black flex items-center justify-center py-20">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">


        <!-- 404图标 -->
        <div class="mb-8">
            <div class="text-9xl font-bold text-accent mb-4">404</div>
            <div class="text-6xl mb-8">🔍</div>
        </div>

        <!-- 错误信息 -->
        <h1 class="text-4xl md:text-5xl font-bold mb-6">
            <?php if ($toolStatusInfo): ?>
                Tool Not Available
            <?php else: ?>
                Page Not Found
            <?php endif; ?>
        </h1>
        <p class="text-xl text-gray-400 mb-8 max-w-2xl mx-auto">
            <?php if ($toolStatusInfo): ?>
                This tool is not currently available for public access. Please explore our other amazing tools below.
            <?php else: ?>
                Sorry, the page you are looking for doesn't exist or has been moved.
                Let's get you back on track with our amazing tools.
            <?php endif; ?>
        </p>
        
        <!-- 操作按钮 -->
        <div class="flex flex-col sm:flex-row gap-4 justify-center mb-12">
            <a href="/" 
               class="bg-accent text-white px-8 py-4 text-lg font-medium hover:bg-blue-700 hover:text-white transition-all duration-200">
                Go Home
            </a>
            <a href="/tools" 
               class="border border-gray-600 text-white px-8 py-4 text-lg font-medium hover:bg-gray-800 hover:border-accent transition-all duration-200">
                Browse Tools
            </a>
        </div>
        
        <!-- 热门工具推荐 -->
        <div class="bg-gray-900 border border-gray-800 p-8 max-w-2xl mx-auto">
            <h2 class="text-2xl font-bold mb-6">Explore Our Tools</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <?php if (!empty($randomTools)): ?>
                    <?php foreach ($randomTools as $tool): ?>
                        <a href="/tools/<?= !empty($tool['category_slug']) ? htmlspecialchars($tool['category_slug']) . '/' : '' ?><?= htmlspecialchars($tool['slug']) ?>"
                           class="flex items-center justify-center p-4 bg-black border border-gray-800 hover:border-accent transition-colors duration-200">
                            <div class="font-semibold text-center"><?= htmlspecialchars($tool['name']) ?></div>
                        </a>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="col-span-2 text-center text-gray-400">
                        <p>No tools available at the moment.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>

<?php
// 包含公共底部
include_once ROOT_PATH . '/templates/layout/footer.php';
?>
