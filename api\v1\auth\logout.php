<?php
/**
 * 用户登出API
 * 处理用户登出和会话清理
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET');
header('Access-Control-Allow-Headers: Content-Type');

// 使用统一的数据库连接
require_once dirname(dirname(dirname(__DIR__))) . '/includes/database-connection.php';

try {
    session_start();
    
    $userId = $_SESSION['user_id'] ?? null;
    
    // 记录登出活动
    if ($userId) {
        try {
            $stmt = $pdo->prepare("
                INSERT INTO pt_member_activity_log (user_id, action, description, ip_address, user_agent, created_at)
                VALUES (?, 'logout', 'User logged out', ?, ?, NOW())
            ");
            $stmt->execute([
                $userId,
                $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
            ]);
        } catch (Exception $e) {
            error_log("Failed to log logout activity: " . $e->getMessage());
        }
    }
    
    // 清理记住我令牌
    if (isset($_COOKIE['remember_token']) && $userId) {
        $token = $_COOKIE['remember_token'];
        $hashedToken = hash('sha256', $token);
        
        try {
            $stmt = $pdo->prepare("DELETE FROM pt_member_tokens WHERE user_id = ? AND token = ? AND type = 'remember_me'");
            $stmt->execute([$userId, $hashedToken]);
        } catch (Exception $e) {
            error_log("Failed to delete remember token: " . $e->getMessage());
        }
        
        // 删除cookie
        setcookie('remember_token', '', time() - 3600, '/', '', false, true);
    }
    
    // 销毁会话
    $_SESSION = array();
    
    if (ini_get("session.use_cookies")) {
        $params = session_get_cookie_params();
        setcookie(session_name(), '', time() - 42000,
            $params["path"], $params["domain"],
            $params["secure"], $params["httponly"]
        );
    }
    
    session_destroy();
    
    // 根据请求方式返回响应
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        echo json_encode([
            'success' => true,
            'message' => 'Logged out successfully',
            'redirect' => '/'
        ]);
    } else {
        // GET请求直接重定向
        header('Location: /');
        exit;
    }
    
} catch (Exception $e) {
    error_log("Logout error: " . $e->getMessage());
    
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        echo json_encode([
            'success' => false,
            'message' => 'Logout failed'
        ]);
    } else {
        header('Location: /');
        exit;
    }
}
?>
