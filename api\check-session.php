<?php
/**
 * 检查用户会话状态API
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

// 启动会话
session_start();

// 检查用户是否登录
$isLoggedIn = isset($_SESSION['user_id']);
$userId = $isLoggedIn ? $_SESSION['user_id'] : null;
$username = $isLoggedIn ? ($_SESSION['username'] ?? null) : null;

echo json_encode([
    'success' => true,
    'logged_in' => $isLoggedIn,
    'user_id' => $userId,
    'username' => $username
]);
?>
