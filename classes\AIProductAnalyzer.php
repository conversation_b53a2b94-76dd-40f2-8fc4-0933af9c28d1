<?php
/**
 * AI产品分析器
 * 使用GLM-4.5-FLASH分析网站数据并生成产品信息
 * 如果API不可用，则使用智能规则分析
 */

class AIProductAnalyzer {
    private $apiKey;
    private $apiUrl;
    private $modelCode;
    private $db;

    public function __construct() {
        // 连接数据库
        $this->connectDatabase();

        // 从数据库加载AI服务配置
        $this->loadAIConfig();
    }

    private function connectDatabase() {
        try {
            // 使用全局的数据库连接
            global $pdo;
            if ($pdo) {
                $this->db = $pdo;
            } else {
                // 如果全局 $pdo 不存在，尝试创建连接
                require_once dirname(__DIR__) . '/includes/database-connection.php';
                $this->db = getDatabaseConnection();
            }
        } catch (Exception $e) {
            error_log("Database connection failed: " . $e->getMessage());
            $this->db = null;
        }
    }

    private function loadAIConfig() {
        if (!$this->db) {
            // 如果数据库连接失败，使用默认配置
            $this->apiKey = null;
            $this->apiUrl = 'https://open.bigmodel.cn/api/paas/v4/chat/completions';
            $this->modelCode = 'glm-4.5-flash';
            return;
        }

        try {
            // 智能选择API Key（负载均衡）
            $config = $this->selectOptimalAPIKey();

            if ($config) {
                $this->apiUrl = $config['base_url'] . '/chat/completions';
                $this->apiKey = $config['api_key'];
                $this->modelCode = $config['model_code'];

                // 更新选中Key的使用统计
                $this->updateKeyUsage($config['key_id']);
            } else {
                // 如果没有找到配置，使用默认值
                $this->apiKey = null;
                $this->apiUrl = 'https://open.bigmodel.cn/api/paas/v4/chat/completions';
                $this->modelCode = 'glm-4.5-flash';
            }

        } catch (PDOException $e) {
            // 使用默认配置
            $this->apiKey = null;
            $this->apiUrl = 'https://open.bigmodel.cn/api/paas/v4/chat/completions';
            $this->modelCode = 'glm-4.5-flash';
        }
    }

    /**
     * 智能选择最优的API Key（负载均衡）
     */
    private function selectOptimalAPIKey() {
        try {
            // 获取所有活跃的BIGMODEL API Keys，按使用次数排序
            $stmt = $this->db->prepare("
                SELECT
                    sp.base_url,
                    sk.id as key_id,
                    sk.api_key,
                    sk.name as key_name,
                    sk.usage_count,
                    sm.model_code
                FROM pt_service_platform sp
                JOIN pt_service_key sk ON sp.id = sk.platform_id
                JOIN pt_service_model sm ON sp.id = sm.platform_id
                WHERE sp.code = 'bigmodel'
                AND sk.is_active = 1
                AND sm.is_free = 1
                AND sm.model_type = 'chat'
                AND sm.is_active = 1
                ORDER BY sk.usage_count ASC, RAND()
                LIMIT 1
            ");

            $stmt->execute();
            return $stmt->fetch(PDO::FETCH_ASSOC);

        } catch (PDOException $e) {
            error_log("Failed to select optimal API key: " . $e->getMessage());
            return null;
        }
    }

    /**
     * 更新API Key使用统计
     */
    private function updateKeyUsage($keyId) {
        try {
            $stmt = $this->db->prepare("
                UPDATE pt_service_key
                SET usage_count = usage_count + 1,
                    last_used_at = NOW()
                WHERE id = ?
            ");
            $stmt->execute([$keyId]);
        } catch (PDOException $e) {
            // 忽略更新失败，不影响主流程
        }
    }
    
    public function analyzeProduct($extractedData) {
        try {
            // 使用带重试机制的AI分析
            if ($this->db) {
                $aiResult = $this->analyzeWithRetry($extractedData);
                if ($aiResult) {
                    $clean = $this->validateAndCleanAnalysis($aiResult);
                    // 若AI未返回有效 tech_category，则基于提取内容进行推断
                    if (empty($clean['tech_category'])) {
                        $clean['tech_category'] = $this->inferTechCategory($extractedData['tech_stack'] ?? [], $extractedData['content'] ?? []);
                    }
                    return $clean;
                }
            }

            // 如果AI分析失败，使用智能规则分析
            return $this->getRuleBasedAnalysis($extractedData);

        } catch (Exception $e) {
            return $this->getRuleBasedAnalysis($extractedData);
        }
    }

    /**
     * 带重试机制的AI分析
     * 如果当前Key失效，会自动尝试其他可用的Key
     */
    private function analyzeWithRetry($extractedData, $maxRetries = 3) {
        $attemptCount = 0;
        $usedKeyIds = [];

        while ($attemptCount < $maxRetries) {
            $attemptCount++;

            // 获取配置（排除已经尝试过的Key）
            $config = $this->selectOptimalAPIKeyExcluding($usedKeyIds);
            if (!$config) {
                error_log("No more API keys available for retry attempt $attemptCount");
                break;
            }

            // 记录使用的Key ID
            $usedKeyIds[] = $config['key_id'];

            // 设置当前配置
            $this->apiUrl = $config['base_url'] . '/chat/completions';
            $this->apiKey = $config['api_key'];
            $this->modelCode = $config['model_code'];

            // 尝试分析
            $result = $this->callGLMAPI($extractedData);

            if ($result) {
                // 成功，更新使用统计并返回结果
                $this->updateKeyUsage($config['key_id']);
                error_log("AI analysis successful on attempt $attemptCount using key: " . $config['key_name']);
                return $result;
            }

            // 失败，记录日志并继续重试
            error_log("AI analysis failed on attempt $attemptCount using key: " . $config['key_name']);
        }

        error_log("AI analysis failed after $attemptCount attempts with " . count($usedKeyIds) . " different keys");
        return null;
    }

    /**
     * 选择最优的API Key（排除指定的Key ID）
     */
    private function selectOptimalAPIKeyExcluding($excludeKeyIds = []) {
        try {
            // 构建排除条件
            $excludeCondition = '';
            $params = [];

            if (!empty($excludeKeyIds)) {
                $placeholders = str_repeat('?,', count($excludeKeyIds) - 1) . '?';
                $excludeCondition = " AND sk.id NOT IN ($placeholders)";
                $params = $excludeKeyIds;
            }

            // 获取可用的API密钥（排除已使用的）
            $sql = "
                SELECT
                    sp.base_url,
                    sk.id as key_id,
                    sk.api_key,
                    sk.name as key_name,
                    sk.usage_count,
                    sm.model_code
                FROM pt_service_platform sp
                JOIN pt_service_key sk ON sp.id = sk.platform_id
                JOIN pt_service_model sm ON sp.id = sm.platform_id
                WHERE sp.code = 'bigmodel'
                AND sk.is_active = 1
                AND sm.is_free = 1
                AND sm.model_type = 'chat'
                AND sm.is_active = 1" . $excludeCondition . "
                ORDER BY sk.usage_count ASC, RAND()
                LIMIT 1
            ";

            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetch(PDO::FETCH_ASSOC);

        } catch (PDOException $e) {
            error_log("Failed to select optimal API key: " . $e->getMessage());
            return null;
        }
    }

    public function getBasicAnalysis($extractedData) {
        // 基础分析方法，作为最后的后备
        return $this->getRuleBasedAnalysis($extractedData);
    }
    
    private function callGLMAPI($extractedData) {
        $prompt = $this->buildAnalysisPrompt($extractedData);
        
        $data = [
            'model' => $this->modelCode,
            'messages' => [
                [
                    'role' => 'system',
                    'content' => 'You are a professional product analyst and marketing expert specializing in analyzing websites and extracting structured product information for launch platforms.

Your task is to analyze website data and generate comprehensive product launch information in JSON format. You must:

1. Extract key product information accurately
2. Generate compelling marketing content
3. Identify target categories and features
4. Provide structured data suitable for product launch platforms
5. Return ONLY valid JSON with all required fields in English

Focus on creating engaging, professional content that would attract users on a product launch platform.'
                ],
                [
                    'role' => 'user',
                    'content' => $prompt
                ]
            ],
            'thinking' => [
                'type' => 'disabled'  // 关闭思考模式，节省token用于内容生成
            ],
            'temperature' => 0.2,  // 降低温度，提高一致性
            'max_tokens' => 3000,  // 增加token限制，确保完整输出
            'stream' => false
        ];
        
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $this->apiUrl,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($data),
            CURLOPT_HTTPHEADER => [
                'Content-Type: application/json',
                'Authorization: Bearer ' . $this->apiKey
            ],
            CURLOPT_TIMEOUT => 30
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error || $httpCode !== 200) {
            error_log("GLM API error: HTTP $httpCode, $error, Response: " . substr($response, 0, 500));
            return null;
        }
        
        $result = json_decode($response, true);
        
        if (!$result || !isset($result['choices'][0]['message']['content'])) {
            error_log("GLM API response error: " . $response);
            return null;
        }
        
        return $this->parseAIResponse($result['choices'][0]['message']['content']);
    }
    
    private function buildAnalysisPrompt($data) {
        $metaData = $data['meta_data'] ?? [];
        $content = $data['content'] ?? [];
        $techStack = $data['tech_stack'] ?? [];

        $prompt = "Analyze the following website data and extract comprehensive product information for a launch platform:\n\n";

        $prompt .= "=== WEBSITE DATA ===\n";
        $prompt .= "URL: " . ($data['url'] ?? '') . "\n";
        $prompt .= "Title: " . ($metaData['title'] ?? '') . "\n";
        $prompt .= "Description: " . ($metaData['description'] ?? '') . "\n";
        $prompt .= "OG Title: " . ($metaData['og_title'] ?? '') . "\n";
        $prompt .= "OG Description: " . ($metaData['og_description'] ?? '') . "\n\n";
        
        $prompt .= "=== CONTENT ANALYSIS ===\n";
        if (!empty($content['headings']['h1'])) {
            $prompt .= "Main Headings: " . implode(', ', array_slice($content['headings']['h1'], 0, 3)) . "\n";
        }

        if (!empty($content['main_content'])) {
            $prompt .= "Content Summary: " . substr($content['main_content'], 0, 800) . "\n";
        }

        if (!empty($content['features'])) {
            $prompt .= "Detected Features: " . implode(', ', array_slice($content['features'], 0, 8)) . "\n";
        }

        if (!empty($content['cta_buttons'])) {
            $prompt .= "Call-to-Action Buttons: " . implode(', ', array_slice($content['cta_buttons'], 0, 5)) . "\n";
        }

        if (!empty($content['pricing_info'])) {
            $prompt .= "Pricing Information: " . implode(', ', array_slice($content['pricing_info'], 0, 5)) . "\n";
        }

        if (!empty($techStack)) {
            $prompt .= "Technology Stack: " . implode(', ', $techStack) . "\n";
        }
        
        $prompt .= "\n=== ANALYSIS REQUIREMENTS ===\n";
        $prompt .= "Based on the above data, extract and generate comprehensive product information. Think step by step:\n\n";
        $prompt .= "1. Identify the core product name and value proposition\n";
        $prompt .= "2. Determine the most appropriate category and target audience\n";
        $prompt .= "3. Extract or infer key features and benefits\n";
        $prompt .= "4. Generate compelling marketing content in English\n";
        $prompt .= "5. Assess pricing model and launch status\n\n";

        $prompt .= "CRITICAL REQUIREMENTS:\n";
        $prompt .= "1. Product Name: Extract ONLY the brand/product name - NO taglines, descriptions, or extra text\n";
        $prompt .= "2. Tagline: MUST create a compelling, concise tagline that captures the product's main value proposition. This is REQUIRED.\n";
        $prompt .= "3. Description: Write a clean, professional description in 2-3 complete sentences. Focus on what the product does and its main benefits. Avoid repetitive marketing copy.\n";
        $prompt .= "4. All text must be clean, complete, and properly formatted\n\n";

        $prompt .= "Return ONLY a valid JSON object with the following structure (no additional text):\n\n";
        // 从数据库获取可用的分类选项
        $availableCategories = $this->getAvailableCategories();
        $categoryOptions = implode(', ', $availableCategories);

        // 获取技术分类选项
        $techCategoryOptions = $this->getAvailableTechCategories();

        $prompt .= json_encode([
            "product_name" => "Brand name only - extract the core product/brand name without taglines or descriptions",
            "tagline" => "REQUIRED: Compelling one-line description that captures the main value proposition (max 150 characters, must be engaging and clear)",
            "description" => "Clean, professional description in 2-3 complete sentences explaining what the product does and why it's valuable. Maximum 500 characters.",
            "category" => "Primary category - choose from: $categoryOptions",
            "pricing_model" => "Pricing model - choose from: free, freemium, paid, subscription, one-time",
            "key_features" => ["List 5-6 key features based on the website content"],
            "use_cases" => ["List 3-4 main use cases for this product"],
            "target_audience" => "Primary target audience description based on the product analysis",
            "tech_category" => "Technology category slug - choose ONLY from: $techCategoryOptions",
            "tags" => ["Generate exactly 3 relevant tags based on product analysis"],
            "launch_status" => "Current status - choose from: coming_soon, beta, launched",
            "innovation_score" => "Innovation rating from 1-10 (integer) based on uniqueness and market impact"
        ], JSON_PRETTY_PRINT);
        
        return $prompt;
    }
    
    private function parseAIResponse($response) {
        // 尝试提取JSON
        $jsonStart = strpos($response, '{');
        $jsonEnd = strrpos($response, '}');
        
        if ($jsonStart !== false && $jsonEnd !== false) {
            $jsonStr = substr($response, $jsonStart, $jsonEnd - $jsonStart + 1);
            $parsed = json_decode($jsonStr, true);
            
            if ($parsed) {
                return $parsed;
            }
        }
        
        // 如果JSON解析失败，返回null
        error_log("Failed to parse AI response: " . $response);
        return null;
    }
    
    /**
     * 基于规则的智能分析（当AI不可用时使用）
     */
    private function getRuleBasedAnalysis($extractedData) {
        $metaData = $extractedData['meta_data'] ?? [];
        $content = $extractedData['content'] ?? [];
        $techStack = $extractedData['tech_stack'] ?? [];
        
        // 提取产品名称
        $productName = $this->extractProductName($metaData, $content);
        
        // 提取描述
        $tagline = $this->extractTagline($metaData);
        $description = $this->extractDescription($metaData, $content);
        
        // 推断分类
        $category = $this->inferCategory($productName . ' ' . $description . ' ' . implode(' ', $content['features'] ?? []));
        
        // 提取功能特性
        $keyFeatures = $this->extractFeatures($content);
        
        // 推断定价模式
        $pricingModel = $this->inferPricingModel($content['pricing_info'] ?? []);
        
        // 生成标签
        $tags = $this->generateTags($productName, $description, $category, $techStack);
        
        // 推断目标用户
        $targetAudience = $this->inferTargetAudience($category, $keyFeatures);
        
        // 生成使用场景
        $useCases = $this->generateUseCases($category, $keyFeatures);

        // 如果tagline为空，使用AI生成
        if (empty($tagline)) {
            $tagline = $this->generateTaglineWithAI($productName, $description, $category);
        }

        return [
            'product_name' => $productName,
            'tagline' => $tagline,
            'description' => $description,
            'category' => $category,
            'subcategory' => '',
            'tags' => $tags,
            'key_features' => $keyFeatures,
            'target_audience' => $targetAudience,
            'use_cases' => $useCases,
            'pricing_model' => $pricingModel,
            'launch_status' => '',  // 不设默认值，让用户选择
            'innovation_score' => $this->calculateInnovationScore($techStack, $keyFeatures),
            'unique_selling_points' => array_slice($keyFeatures, 0, 2),
            'tech_category' => $this->inferTechCategory($techStack, $content),
            'is_rule_based' => true
        ];
    }
    
    private function extractProductName($metaData, $content) {
        // 优先级：OG title > title > H1
        $candidates = [
            $metaData['og_title'] ?? '',
            $metaData['title'] ?? '',
            $content['headings']['h1'][0] ?? ''
        ];
        
        foreach ($candidates as $candidate) {
            $cleaned = $this->cleanProductName($candidate);
            if (strlen($cleaned) > 2 && strlen($cleaned) < 100) {
                return $cleaned;
            }
        }
        
        return 'Unknown Product';
    }
    
    private function cleanProductName($name) {
        // 移除常见的后缀和前缀
        $suffixes = [
            ' - Home', ' | Home', ' - Official Website', ' | Official Site', ' - Welcome',
            ' - Write better, faster, and have more fun.', ' - The Writing App That Feels Like a Game',
            ' | The Best', ' - Best', ' | Official', ' - Official', ' | App', ' - App',
            ' | Platform', ' - Platform', ' | Tool', ' - Tool', ' | Software', ' - Software'
        ];

        foreach ($suffixes as $suffix) {
            $name = str_ireplace($suffix, '', $name);
        }

        // 移除常见的描述性文本模式
        $patterns = [
            '/ - .+$/',  // 移除 " - 任何描述"
            '/ \| .+$/', // 移除 " | 任何描述"
            '/ – .+$/',  // 移除 " – 任何描述"
            '/ — .+$/',  // 移除 " — 任何描述"
        ];

        foreach ($patterns as $pattern) {
            $name = preg_replace($pattern, '', $name);
        }

        // 如果名称包含多个单词且第一个单词看起来像品牌名，只取第一个单词
        $words = explode(' ', trim($name));
        if (count($words) > 1) {
            $firstWord = $words[0];
            // 如果第一个单词是大写开头且长度合适，可能是品牌名
            if (ctype_upper($firstWord[0]) && strlen($firstWord) >= 3 && strlen($firstWord) <= 20) {
                // 检查是否是常见的非品牌词
                $commonWords = ['The', 'Best', 'Top', 'Free', 'Online', 'Web', 'Mobile', 'App'];
                if (!in_array($firstWord, $commonWords)) {
                    return $firstWord;
                }
            }
        }

        return trim($name);
    }
    
    private function extractTagline($metaData) {
        $candidates = [
            $metaData['og_description'] ?? '',
            $metaData['description'] ?? '',
            $metaData['twitter_description'] ?? ''
        ];

        foreach ($candidates as $candidate) {
            $candidate = trim($candidate);
            if (strlen($candidate) > 5 && strlen($candidate) <= 500) { // 放宽长度限制
                return $candidate;
            }
        }

        // 如果没有找到合适的tagline，尝试从标题中提取
        $title = $metaData['og_title'] ?? $metaData['title'] ?? '';
        if ($title) {
            // 如果标题包含分隔符，取后半部分作为tagline
            $separators = [' - ', ' | ', ' – ', ' — '];
            foreach ($separators as $sep) {
                if (strpos($title, $sep) !== false) {
                    $parts = explode($sep, $title, 2);
                    if (count($parts) > 1) {
                        $tagline = trim($parts[1]);
                        if (strlen($tagline) > 5 && strlen($tagline) <= 500) {
                            return $tagline;
                        }
                    }
                }
            }
        }

        // 如果还是没有找到tagline，返回空字符串，让AI分析生成
        return '';
    }
    
    private function extractDescription($metaData, $content) {
        // 优先使用meta描述
        $description = $metaData['og_description'] ?? $metaData['description'] ?? '';

        // 如果meta描述太短或不存在，从主要内容中提取
        if (strlen($description) < 50 && !empty($content['main_content'])) {
            $mainContent = $content['main_content'];

            // 尝试提取前几句话作为描述
            $sentences = preg_split('/[.!?]+/', $mainContent);
            $cleanSentences = [];

            foreach ($sentences as $sentence) {
                $sentence = trim($sentence);
                if (strlen($sentence) > 20 && strlen($sentence) < 200) {
                    $cleanSentences[] = $sentence;
                    if (count($cleanSentences) >= 3) break; // 最多3句话
                }
            }

            if (!empty($cleanSentences)) {
                $description = implode('. ', $cleanSentences) . '.';
            } else {
                // 如果没有找到合适的句子，截取前500个字符
                $description = substr($mainContent, 0, 500);
            }
        }

        // 清理描述
        $description = $this->cleanText($description, 1000);

        return $description;
    }
    
    private function inferCategory($text) {
        // 使用AI来推断分类，而不是硬编码规则
        try {
            $availableCategories = $this->getAvailableCategories();
            $categoryOptions = implode(', ', $availableCategories);

            $prompt = "Based on the following product information, determine the most appropriate category:\n\n";
            $prompt .= "Product Information: " . substr($text, 0, 1000) . "\n\n";
            $prompt .= "Available Categories: $categoryOptions\n\n";
            $prompt .= "Return ONLY the category name that best fits this product. Choose from the available categories listed above.";

            $data = [
                'model' => $this->modelCode,
                'messages' => [
                    [
                        'role' => 'user',
                        'content' => $prompt
                    ]
                ],
                'temperature' => 0.1, // 低温度确保一致性
                'max_tokens' => 200, // 增加token限制，避免截断
                'stream' => false
            ];

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $this->apiUrl);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                'Authorization: Bearer ' . $this->apiKey
            ]);
            curl_setopt($ch, CURLOPT_TIMEOUT, 15);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);

            if ($error || $httpCode !== 200) {
                return $availableCategories[0] ?? 'productivity'; // 返回第一个可用分类
            }

            $result = json_decode($response, true);
            if (isset($result['choices'][0]['message']['content'])) {
                $category = trim($result['choices'][0]['message']['content']);
                // 验证返回的分类是否在可用列表中
                if (in_array($category, $availableCategories)) {
                    return $category;
                }
            }

            return $availableCategories[0] ?? 'productivity';
        } catch (Exception $e) {
            error_log("Category inference error: " . $e->getMessage());
            $availableCategories = $this->getAvailableCategories();
            return $availableCategories[0] ?? 'productivity';
        }
    }
    
    private function extractFeatures($content) {
        $features = $content['features'] ?? [];

        // 如果没有提取到功能，尝试从其他地方提取
        if (empty($features)) {
            $features = [];

            // 从H2, H3标题中提取可能的功能
            $headings = array_merge($content['headings']['h2'] ?? [], $content['headings']['h3'] ?? []);
            foreach ($headings as $heading) {
                $heading = trim($heading);
                if (strlen($heading) > 5 && strlen($heading) < 150) {
                    $features[] = $heading;
                }
            }

            // 如果还是没有功能，从主要内容中提取关键句子
            if (empty($features) && !empty($content['main_content'])) {
                $sentences = preg_split('/[.!?]+/', $content['main_content']);
                foreach ($sentences as $sentence) {
                    $sentence = trim($sentence);
                    // 查找包含功能关键词的句子
                    if (strlen($sentence) > 10 && strlen($sentence) < 200) {
                        $lowerSentence = strtolower($sentence);
                        $featureKeywords = ['feature', 'capability', 'function', 'tool', 'help', 'enable', 'provide', 'support', 'allow'];
                        foreach ($featureKeywords as $keyword) {
                            if (strpos($lowerSentence, $keyword) !== false) {
                                $features[] = $sentence;
                                break;
                            }
                        }
                    }
                    if (count($features) >= 6) break;
                }
            }
        }

        // 确保至少有一些默认功能
        if (empty($features)) {
            $features = [
                'User-friendly interface',
                'Fast and reliable performance',
                'Cross-platform compatibility',
                'Secure data handling',
                'Regular updates and support'
            ];
        }

        return array_slice($features, 0, 6);
    }

    /**
     * 使用AI生成tagline
     */
    private function generateTaglineWithAI($productName, $description, $category) {
        try {
            $prompt = "Based on the following product information, create a compelling, concise tagline (maximum 100 characters):\n\n";
            $prompt .= "Product Name: $productName\n";
            $prompt .= "Description: $description\n";
            $prompt .= "Category: $category\n\n";
            $prompt .= "Requirements:\n";
            $prompt .= "- Maximum 100 characters\n";
            $prompt .= "- Compelling and memorable\n";
            $prompt .= "- Captures the main value proposition\n";
            $prompt .= "- Professional tone\n\n";
            $prompt .= "Return ONLY the tagline text, no quotes or additional text.";

            $data = [
                'model' => $this->modelCode,
                'messages' => [
                    [
                        'role' => 'user',
                        'content' => $prompt
                    ]
                ],
                'temperature' => 0.7, // 稍高的创造性
                'max_tokens' => 100,
                'stream' => false
            ];

            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $this->apiUrl);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                'Authorization: Bearer ' . $this->apiKey
            ]);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);

            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);

            if ($error || $httpCode !== 200) {
                error_log("Tagline AI generation error: HTTP $httpCode, $error");
                return $this->getFallbackTagline($category);
            }

            $result = json_decode($response, true);
            if (isset($result['choices'][0]['message']['content'])) {
                $tagline = trim($result['choices'][0]['message']['content']);
                // 移除可能的引号
                $tagline = trim($tagline, '"\'');
                return strlen($tagline) > 5 ? $tagline : $this->getFallbackTagline($category);
            }

            return $this->getFallbackTagline($category);
        } catch (Exception $e) {
            error_log("Tagline AI generation exception: " . $e->getMessage());
            return $this->getFallbackTagline($category);
        }
    }

    /**
     * 根据分类生成后备tagline
     */
    private function getFallbackTagline($category) {
        $fallbackTaglines = [
            'ai-tools' => 'Intelligent automation for smarter workflows',
            'developer-tools' => 'Streamline your development process',
            'productivity' => 'Boost your productivity and efficiency',
            'design-tools' => 'Create stunning designs with ease',
            'marketing' => 'Amplify your marketing impact',
            'business' => 'Optimize your business operations',
            'education' => 'Learn and grow with innovative tools',
            'communication' => 'Connect and collaborate seamlessly',
            'finance' => 'Manage your finances intelligently'
        ];

        return $fallbackTaglines[$category] ?? 'Innovative solution for your needs';
    }

    private function inferPricingModel($pricingInfo) {
        $pricingText = strtolower(implode(' ', $pricingInfo));
        
        if (strpos($pricingText, 'free') !== false) {
            if (strpos($pricingText, 'premium') !== false || strpos($pricingText, 'pro') !== false || strpos($pricingText, 'paid') !== false) {
                return 'freemium';
            }
            return 'free';
        }
        
        if (strpos($pricingText, '$') !== false || strpos($pricingText, 'price') !== false || strpos($pricingText, 'subscription') !== false) {
            if (strpos($pricingText, 'enterprise') !== false) {
                return 'enterprise';
            }
            return 'paid';
        }
        
        return 'unknown';
    }
    
    private function generateTags($productName, $description, $category, $techStack) {
        $tags = [];
        
        // 添加分类相关标签
        $categoryTags = [
            'ai-tools' => ['ai', 'automation', 'machine-learning'],
            'developer-tools' => ['development', 'coding', 'api'],
            'design' => ['design', 'ui-ux', 'creative'],
            'productivity' => ['productivity', 'efficiency', 'workflow'],
            'marketing' => ['marketing', 'growth', 'seo'],
            'analytics' => ['analytics', 'data', 'insights'],
            'e-commerce' => ['ecommerce', 'online-store', 'sales'],
            'communication' => ['communication', 'collaboration', 'team'],
            'finance' => ['finance', 'accounting', 'money'],
            'education' => ['education', 'learning', 'training']
        ];
        
        if (isset($categoryTags[$category])) {
            $tags = array_merge($tags, $categoryTags[$category]);
        }
        
        // 添加技术栈标签
        $tags = array_merge($tags, array_slice($techStack, 0, 3));
        
        // 从产品名称和描述中提取关键词
        $text = strtolower($productName . ' ' . $description);
        $commonKeywords = ['saas', 'web-app', 'mobile', 'cloud', 'platform', 'tool', 'service', 'solution'];
        
        foreach ($commonKeywords as $keyword) {
            if (strpos($text, $keyword) !== false) {
                $tags[] = $keyword;
            }
        }
        
        return array_unique(array_slice($tags, 0, 3)); // 限制为3个标签
    }
    
    private function inferTargetAudience($category, $features) {
        // 简化逻辑，主要依靠AI分析生成目标用户
        return 'General Users';
    }
    
    private function generateUseCases($category, $features) {
        // 简化逻辑，主要依靠AI分析生成使用场景
        return ['General productivity', 'Workflow optimization', 'Task management'];
    }
    
    private function calculateInnovationScore($techStack, $features) {
        $score = 5; // 基础分数
        
        // 基于技术栈加分
        $innovativeTech = ['ai', 'machine-learning', 'blockchain', 'ar', 'vr', 'iot'];
        foreach ($innovativeTech as $tech) {
            if (in_array($tech, $techStack)) {
                $score += 1;
            }
        }
        
        // 基于功能数量
        $score += min(2, count($features));
        
        return min(10, max(1, $score));
    }
    
    private function inferTechCategory($techStack, $content) {
        $text = strtolower(implode(' ', $techStack) . ' ' . ($content['main_content'] ?? ''));

        // 从数据库获取技术分类
        try {
            $stmt = $this->db->query("SELECT slug, name FROM pt_tech_categories WHERE is_active = 1 ORDER BY sort_order ASC");
            $techCategories = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // 动态构建关键词匹配规则，基于数据库中的分类
            $keywordRules = [];
            foreach ($techCategories as $category) {
                $slug = $category['slug'];
                $name = strtolower($category['name']);

                // 为每个分类定义基础关键词
                $keywords = [$slug, $name];

                // 根据分类添加特定关键词
                switch ($slug) {
                    case 'mobile-app':
                        $keywords = array_merge($keywords, ['mobile', 'ios', 'android', 'app store', 'play store', 'react native', 'flutter', 'swift', 'kotlin']);
                        break;
                    case 'api':
                        $keywords = array_merge($keywords, ['api', 'rest', 'graphql', 'endpoint', 'webhook', 'microservice']);
                        break;
                    case 'browser-extension':
                        $keywords = array_merge($keywords, ['extension', 'browser', 'addon']);
                        break;
                    case 'chrome-extension':
                        $keywords = array_merge($keywords, ['chrome extension', 'chrome store', 'chrome web store']);
                        break;
                    case 'desktop-app':
                        $keywords = array_merge($keywords, ['desktop', 'electron', 'windows', 'macos', 'linux', 'native app']);
                        break;
                    case 'saas':
                        $keywords = array_merge($keywords, ['saas', 'cloud', 'subscription', 'online platform']);
                        break;
                    case 'web-app':
                        $keywords = array_merge($keywords, ['web', 'website', 'webapp', 'html', 'css', 'javascript', 'react', 'vue', 'angular']);
                        break;
                    case 'cli-tool':
                        $keywords = array_merge($keywords, ['command line', 'cli', 'terminal', 'bash', 'shell', 'npm', 'pip']);
                        break;
                    case 'library-framework':
                        $keywords = array_merge($keywords, ['library', 'framework', 'sdk', 'package', 'npm package', 'pip package']);
                        break;
                    case 'plugin':
                        $keywords = array_merge($keywords, ['plugin', 'addon', 'widget']);
                        break;
                    case 'wordpress-plugin':
                        $keywords = array_merge($keywords, ['wordpress', 'wp plugin', 'wordpress plugin']);
                        break;
                    case 'figma-plugin':
                        $keywords = array_merge($keywords, ['figma', 'figma plugin', 'design tool']);
                        break;
                    case 'vscode-extension':
                        $keywords = array_merge($keywords, ['vscode', 'visual studio code', 'vs code extension']);
                        break;
                    case 'slack-bot':
                        $keywords = array_merge($keywords, ['slack', 'slack bot', 'slack app']);
                        break;
                    case 'discord-bot':
                        $keywords = array_merge($keywords, ['discord', 'discord bot', 'discord app']);
                        break;
                    case 'ai-model':
                        $keywords = array_merge($keywords, ['ai model', 'machine learning', 'neural network', 'deep learning', 'tensorflow', 'pytorch', 'artificial intelligence']);
                        break;
                    case 'no-code-platform':
                        $keywords = array_merge($keywords, ['no-code', 'low-code', 'visual builder', 'drag and drop']);
                        break;
                    case 'hardware':
                        $keywords = array_merge($keywords, ['hardware', 'iot', 'device', 'sensor', 'embedded']);
                        break;
                }

                $keywordRules[$slug] = $keywords;
            }

            // 计算每个分类的匹配分数
            $scores = [];
            foreach ($techCategories as $category) {
                $slug = $category['slug'];
                $score = 0;

                if (isset($keywordRules[$slug])) {
                    foreach ($keywordRules[$slug] as $keyword) {
                        if (strpos($text, $keyword) !== false) {
                            $score += 1;
                        }
                    }
                }

                if ($score > 0) {
                    $scores[$slug] = $score;
                }
            }

            // 返回得分最高的分类
            if (!empty($scores)) {
                arsort($scores);
                $bestMatch = array_key_first($scores);

                // 返回对应的分类slug（HTML表单需要slug值）
                return $bestMatch;
            }

            // 默认返回 web-app 的slug
            return 'web-app';

        } catch (Exception $e) {
            // 如果数据库查询失败，使用默认值
            return 'web-app';
        }
    }
    
    private function validateAndCleanAnalysis($analysis) {
        $defaults = [
            'product_name' => '',
            'tagline' => '',
            'description' => '',
            'category' => '',  // 不设默认值，让用户选择
            'subcategory' => '',
            'tags' => [],
            'key_features' => [],
            'target_audience' => '',
            'pricing_model' => '',  // 不设默认值，让用户选择
            'launch_status' => '',  // 不设默认值，让用户选择
            'innovation_score' => 5,
            'use_cases' => [],
            'unique_selling_points' => [],
            'tech_category' => ''  // 不设默认值，让用户选择
        ];
        
        // 合并默认值
        $analysis = array_merge($defaults, $analysis);
        
        // 验证和清理数据
        $analysis['product_name'] = $this->cleanText($analysis['product_name'], 100);
        $analysis['tagline'] = $this->cleanText($analysis['tagline'], 150); // 限制为150字符
        $analysis['description'] = $this->cleanText($analysis['description'], 2000); // 增加描述长度限制
        $analysis['target_audience'] = $this->cleanText($analysis['target_audience'], 300);
        
        // 保留 AI 选择的分类，不强制验证
        // 如果需要验证，可以在前端或提交时进行

        // 保留 AI 选择的定价模式，不强制验证

        // 保留 AI 选择的发布状态，不强制验证
        
        // 验证创新度评分
        $analysis['innovation_score'] = max(1, min(10, intval($analysis['innovation_score'])));
        
        // 确保数组字段是数组并限制长度
        foreach (['key_features', 'use_cases', 'unique_selling_points'] as $field) {
            if (!is_array($analysis[$field])) {
                $analysis[$field] = [];
            }
            // 限制数组长度
            $analysis[$field] = array_slice($analysis[$field], 0, 6);
        }

        // 特别处理tags字段，限制为3个
        if (!is_array($analysis['tags'])) {
            $analysis['tags'] = [];
        }
        $analysis['tags'] = array_slice($analysis['tags'], 0, 3);
        
        return $analysis;
    }
    
    private function cleanText($text, $maxLength = 500) {
        $text = strip_tags($text);
        $text = preg_replace('/\s+/', ' ', $text);
        $text = trim($text);
        
        if (strlen($text) > $maxLength) {
            $text = substr($text, 0, $maxLength - 3) . '...';
        }
        
        return $text;
    }

    /**
     * 从数据库获取可用的分类选项
     */
    private function getAvailableCategories() {
        try {
            if (!$this->db) {
                throw new Exception('DB not initialized');
            }
            // 读取产品启动分类表（仅取激活项）
            $stmt = $this->db->query("SELECT slug FROM pt_launch_categories WHERE is_active = 1 ORDER BY sort_order ASC, name ASC");
            $slugs = $stmt->fetchAll(PDO::FETCH_COLUMN);

            if (empty($slugs)) {
                // 数据库没有分类时返回空数组，由上层决定如何处理
                return [];
            }
            return $slugs;
        } catch (Exception $e) {
            error_log("Failed to get categories from database: " . $e->getMessage());
            return [];
        }
    }

    /**
     * 从数据库获取可用的技术分类选项
     */
    private function getAvailableTechCategories() {
        try {
            $stmt = $this->db->query("SELECT slug FROM pt_tech_categories WHERE is_active = 1 ORDER BY sort_order ASC");
            return implode(', ', $stmt->fetchAll(PDO::FETCH_COLUMN));
        } catch (Exception $e) {
            error_log("Failed to get tech categories from database: " . $e->getMessage());
            // 如果数据库查询失败，返回默认技术分类
            return 'saas, web-app, mobile-app, desktop-app, api, browser-extension';
        }
    }
}
