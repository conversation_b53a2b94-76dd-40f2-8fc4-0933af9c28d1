<?php
/**
 * 用户管理列表页面
 * 显示所有用户的管理界面
 */

// 定义根目录
define('ROOT_PATH', dirname(dirname(dirname(__DIR__))));

// 加载认证中间件
require_once dirname(__DIR__) . '/../auth/middleware.php';

// 权限检查
if (!hasPermission('users.view')) {
    header('HTTP/1.1 403 Forbidden');
    exit('Access denied');
}

// 获取筛选参数
$filters = [
    'search' => $_GET['search'] ?? '',
    'country' => $_GET['country'] ?? '',
    'date_from' => $_GET['date_from'] ?? '',
    'date_to' => $_GET['date_to'] ?? '',
    'page' => max(1, intval($_GET['page'] ?? 1)),
    'per_page' => 20
];

// 模拟用户数据（实际应该从数据库获取）
$allUsers = [
    [
        'id' => 1,
        'email' => '<EMAIL>',
        'name' => '<PERSON>',
        'country' => 'United States',
        'city' => 'New York',
        'first_visit' => '2024-12-01 10:30:00',
        'last_visit' => '2025-01-15 14:20:00',
        'visit_count' => 25,
        'is_active' => true,
        'ip_address' => '*************'
    ],
    [
        'id' => 2,
        'email' => '<EMAIL>',
        'name' => 'Jane Doe',
        'country' => 'Canada',
        'city' => 'Toronto',
        'first_visit' => '2024-11-15 09:15:00',
        'last_visit' => '2025-01-14 16:45:00',
        'visit_count' => 42,
        'is_active' => true,
        'ip_address' => '*************'
    ],
    [
        'id' => 3,
        'email' => '<EMAIL>',
        'name' => 'Mike Johnson',
        'country' => 'United Kingdom',
        'city' => 'London',
        'first_visit' => '2024-10-20 11:00:00',
        'last_visit' => '2025-01-13 12:30:00',
        'visit_count' => 18,
        'is_active' => true,
        'ip_address' => '*************'
    ],
    [
        'id' => 4,
        'email' => '<EMAIL>',
        'name' => 'Sarah Wilson',
        'country' => 'Australia',
        'city' => 'Sydney',
        'first_visit' => '2024-12-10 08:45:00',
        'last_visit' => '2025-01-12 10:15:00',
        'visit_count' => 33,
        'is_active' => false,
        'ip_address' => '*************'
    ],
    [
        'id' => 5,
        'email' => '<EMAIL>',
        'name' => 'David Brown',
        'country' => 'Germany',
        'city' => 'Berlin',
        'first_visit' => '2024-11-05 13:20:00',
        'last_visit' => '2025-01-11 15:50:00',
        'visit_count' => 67,
        'is_active' => true,
        'ip_address' => '*************'
    ]
];

// 应用筛选
$filteredUsers = $allUsers;

if (!empty($filters['search'])) {
    $filteredUsers = array_filter($filteredUsers, function($user) use ($filters) {
        return stripos($user['name'], $filters['search']) !== false || 
               stripos($user['email'], $filters['search']) !== false;
    });
}

if (!empty($filters['country'])) {
    $filteredUsers = array_filter($filteredUsers, function($user) use ($filters) {
        return stripos($user['country'], $filters['country']) !== false;
    });
}

// 分页处理
$totalUsers = count($filteredUsers);
$totalPages = ceil($totalUsers / $filters['per_page']);
$offset = ($filters['page'] - 1) * $filters['per_page'];
$paginatedUsers = array_slice($filteredUsers, $offset, $filters['per_page']);

// 获取统计数据
$stats = [
    'total_users' => count($allUsers),
    'active_users' => count(array_filter($allUsers, function($u) { return $u['is_active']; })),
    'new_users_today' => 3, // 模拟数据
    'avg_visits' => round(array_sum(array_column($allUsers, 'visit_count')) / count($allUsers))
];

$currentPage = 'users';
include_once dirname(__DIR__) . '/../components/layout/header.php';
?>

<div class="min-h-screen bg-gray-100">
    <!-- 侧边栏 -->
    <div class="fixed inset-y-0 left-0 w-64 bg-gray-900">
        <?php include_once dirname(__DIR__) . '/../components/layout/sidebar.php'; ?>
    </div>
    
    <!-- 主内容区 -->
    <div class="ml-64">
        <!-- 顶部导航栏 -->
        <div class="bg-white shadow-sm border-b border-gray-200">
            <div class="px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-16">
                    <div class="flex items-center">
                        <h1 class="text-2xl font-semibold text-gray-900">Users Management</h1>
                    </div>
                    
                    <!-- 用户菜单 -->
                    <div class="flex items-center space-x-4">
                        <?php if (hasPermission('users.export')): ?>
                        <button onclick="exportUsers()" class="bg-green-600 text-white px-4 py-2 hover:bg-green-700 transition-colors">
                            <i class="fas fa-download mr-2"></i>Export Data
                        </button>
                        <?php endif; ?>
                        
                        <div class="relative">
                            <button class="flex items-center space-x-2 text-gray-700 hover:text-gray-900" id="user-menu-btn">
                                <div class="w-8 h-8 bg-accent text-white flex items-center justify-center font-semibold">
                                    <?= strtoupper(substr(getCurrentAdmin()['username'], 0, 1)) ?>
                                </div>
                                <span class="hidden md:block"><?= htmlspecialchars(getCurrentAdmin()['username']) ?></span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 页面内容 -->
        <div class="p-6">
            <!-- 统计卡片 -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                <div class="bg-white border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="p-3 bg-blue-100 text-blue-600 mr-4">
                            <i class="fas fa-users text-2xl"></i>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-600">Total Users</p>
                            <p class="text-2xl font-bold text-gray-900"><?= number_format($stats['total_users']) ?></p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="p-3 bg-green-100 text-green-600 mr-4">
                            <i class="fas fa-user-check text-2xl"></i>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-600">Active Users</p>
                            <p class="text-2xl font-bold text-gray-900"><?= number_format($stats['active_users']) ?></p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="p-3 bg-yellow-100 text-yellow-600 mr-4">
                            <i class="fas fa-user-plus text-2xl"></i>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-600">New Today</p>
                            <p class="text-2xl font-bold text-gray-900"><?= number_format($stats['new_users_today']) ?></p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="p-3 bg-purple-100 text-purple-600 mr-4">
                            <i class="fas fa-chart-line text-2xl"></i>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-600">Avg. Visits</p>
                            <p class="text-2xl font-bold text-gray-900"><?= number_format($stats['avg_visits']) ?></p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 筛选器 -->
            <div class="bg-white border border-gray-200 p-4 mb-6">
                <form method="GET" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <!-- 搜索框 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Search Users</label>
                        <input type="text" 
                               name="search" 
                               value="<?= htmlspecialchars($filters['search']) ?>"
                               placeholder="Search by name or email..."
                               class="w-full px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent">
                    </div>
                    
                    <!-- 国家筛选 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Country</label>
                        <input type="text" 
                               name="country" 
                               value="<?= htmlspecialchars($filters['country']) ?>"
                               placeholder="Filter by country..."
                               class="w-full px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent">
                    </div>
                    
                    <!-- 日期范围 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Date From</label>
                        <input type="date" 
                               name="date_from" 
                               value="<?= htmlspecialchars($filters['date_from']) ?>"
                               class="w-full px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent">
                    </div>
                    
                    <!-- 搜索按钮 -->
                    <div class="flex items-end">
                        <button type="submit" class="w-full bg-accent text-white px-4 py-2 hover:bg-blue-600 transition-colors">
                            <i class="fas fa-search mr-2"></i>Filter
                        </button>
                    </div>
                </form>
            </div>
            
            <!-- 用户列表 -->
            <div class="bg-white border border-gray-200">
                <!-- 表格头部 -->
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex justify-between items-center">
                        <h3 class="text-lg font-semibold text-gray-900">Users List</h3>
                        <div class="text-sm text-gray-500">
                            Showing <?= count($paginatedUsers) ?> of <?= number_format($totalUsers) ?> users
                        </div>
                    </div>
                </div>
                
                <!-- 表格内容 -->
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Location</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Visits</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Visit</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php foreach ($paginatedUsers as $user): ?>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="w-10 h-10 bg-gray-300 text-gray-600 flex items-center justify-center font-semibold mr-3">
                                            <?= strtoupper(substr($user['name'], 0, 1)) ?>
                                        </div>
                                        <div>
                                            <div class="text-sm font-medium text-gray-900"><?= htmlspecialchars($user['name']) ?></div>
                                            <div class="text-sm text-gray-500"><?= htmlspecialchars($user['email']) ?></div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900"><?= htmlspecialchars($user['city']) ?></div>
                                    <div class="text-sm text-gray-500"><?= htmlspecialchars($user['country']) ?></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <?= number_format($user['visit_count']) ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <?= date('M j, Y H:i', strtotime($user['last_visit'])) ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <?php if ($user['is_active']): ?>
                                        <span class="inline-flex items-center px-2.5 py-0.5 text-xs font-medium bg-green-100 text-green-800">
                                            Active
                                        </span>
                                    <?php else: ?>
                                        <span class="inline-flex items-center px-2.5 py-0.5 text-xs font-medium bg-red-100 text-red-800">
                                            Inactive
                                        </span>
                                    <?php endif; ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex space-x-2">
                                        <button onclick="viewUser(<?= $user['id'] ?>)" 
                                                class="text-blue-600 hover:text-blue-900"
                                                title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        
                                        <?php if (hasPermission('users.edit')): ?>
                                        <button onclick="editUser(<?= $user['id'] ?>)" 
                                                class="text-indigo-600 hover:text-indigo-900"
                                                title="Edit User">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <?php endif; ?>
                                        
                                        <?php if (hasPermission('users.ban')): ?>
                                        <button onclick="toggleUserStatus(<?= $user['id'] ?>, <?= $user['is_active'] ? 'false' : 'true' ?>)" 
                                                class="<?= $user['is_active'] ? 'text-red-600 hover:text-red-900' : 'text-green-600 hover:text-green-900' ?>"
                                                title="<?= $user['is_active'] ? 'Deactivate' : 'Activate' ?> User">
                                            <i class="fas fa-<?= $user['is_active'] ? 'ban' : 'check' ?>"></i>
                                        </button>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                
                <!-- 分页 -->
                <?php if ($totalPages > 1): ?>
                <div class="px-6 py-4 border-t border-gray-200">
                    <div class="flex items-center justify-between">
                        <div class="text-sm text-gray-700">
                            Showing <?= $offset + 1 ?> to <?= min($offset + $filters['per_page'], $totalUsers) ?> of <?= $totalUsers ?> results
                        </div>
                        
                        <div class="flex space-x-1">
                            <?php if ($filters['page'] > 1): ?>
                            <a href="?<?= http_build_query(array_merge($filters, ['page' => $filters['page'] - 1])) ?>" 
                               class="px-3 py-2 text-sm bg-white border border-gray-300 text-gray-500 hover:bg-gray-50">
                                Previous
                            </a>
                            <?php endif; ?>
                            
                            <?php for ($i = max(1, $filters['page'] - 2); $i <= min($totalPages, $filters['page'] + 2); $i++): ?>
                            <a href="?<?= http_build_query(array_merge($filters, ['page' => $i])) ?>" 
                               class="px-3 py-2 text-sm border <?= $i === $filters['page'] ? 'bg-accent text-white border-accent' : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50' ?>">
                                <?= $i ?>
                            </a>
                            <?php endfor; ?>
                            
                            <?php if ($filters['page'] < $totalPages): ?>
                            <a href="?<?= http_build_query(array_merge($filters, ['page' => $filters['page'] + 1])) ?>" 
                               class="px-3 py-2 text-sm bg-white border border-gray-300 text-gray-500 hover:bg-gray-50">
                                Next
                            </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<script>
// 查看用户详情
function viewUser(userId) {
    alert('View user details functionality will be implemented.');
}

// 编辑用户
function editUser(userId) {
    alert('Edit user functionality will be implemented.');
}

// 切换用户状态
function toggleUserStatus(userId, newStatus) {
    const action = newStatus === 'true' ? 'activate' : 'deactivate';
    if (confirm(`Are you sure you want to ${action} this user?`)) {
        alert(`${action} user functionality will be implemented.`);
    }
}

// 导出用户数据
function exportUsers() {
    alert('Export users functionality will be implemented.');
}
</script>

<?php include_once dirname(__DIR__) . '/../components/layout/footer.php'; ?>
