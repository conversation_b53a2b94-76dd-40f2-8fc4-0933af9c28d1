<?php
/**
 * My Tools Page - 用户自己部署的工具列表
 */

// 确保用户已登录
if (!isset($_SESSION['user_id'])) {
    header('Location: /auth/login');
    exit;
}

$userId = $_SESSION['user_id'];

try {
    // 分页参数
    $page = max(1, intval($_GET['p'] ?? 1));
    $limit = 5;
    $offset = ($page - 1) * $limit;

    // 获取总数
    $countStmt = $pdo->prepare("SELECT COUNT(*) FROM pt_tool WHERE created_by = ?");
    $countStmt->execute([$userId]);
    $totalTools = $countStmt->fetchColumn();
    $totalPages = ceil($totalTools / $limit);

    // 获取用户的工具列表（分页）
    $stmt = $pdo->prepare("
        SELECT t.*, tc.name as category_name, tc.slug as category_slug
        FROM pt_tool t
        LEFT JOIN pt_tool_category tc ON t.category_id = tc.id
        WHERE t.created_by = ?
        ORDER BY t.created_at DESC
        LIMIT $limit OFFSET $offset
    ");
    $stmt->execute([$userId]);
    $tools = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // 获取统计数据
    $statsStmt = $pdo->prepare("
        SELECT
            COUNT(*) as total_tools,
            SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_tools,
            SUM(CASE WHEN status = 'coming_soon' THEN 1 ELSE 0 END) as pending_tools,
            SUM(CASE WHEN status = 'inactive' THEN 1 ELSE 0 END) as inactive_tools,
            SUM(view_count) as total_views
        FROM pt_tool
        WHERE created_by = ?
    ");
    $statsStmt->execute([$userId]);
    $stats = $statsStmt->fetch(PDO::FETCH_ASSOC);

} catch (Exception $e) {
    error_log("My Tools page error: " . $e->getMessage());
    $tools = [];
    $stats = ['total_tools' => 0, 'active_tools' => 0, 'pending_tools' => 0, 'inactive_tools' => 0, 'total_views' => 0];
    $totalPages = 0;
}

// 状态标签映射
function getStatusBadge($status) {
    $badges = [
        'active' => 'bg-green-100 text-green-800 border-green-200',
        'inactive' => 'bg-orange-100 text-orange-800 border-orange-200',
        'coming_soon' => 'bg-yellow-100 text-yellow-800 border-yellow-200'
    ];
    return $badges[$status] ?? 'bg-gray-100 text-gray-800 border-gray-200';
}

function getStatusLabel($status) {
    $labels = [
        'active' => 'Published',
        'inactive' => 'Inactive',
        'coming_soon' => 'Pending Review'
    ];
    return $labels[$status] ?? 'Unknown';
}
?>

<div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">My Tools</h1>
            <p class="text-gray-600 mt-1">Manage your created tools and track their performance</p>
        </div>
        <a href="?page=tool-generator" 
           class="inline-flex items-center px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
            </svg>
            Create New Tool
        </a>
    </div>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <div class="bg-white rounded-lg border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                        <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Tools</p>
                    <p class="text-2xl font-semibold text-gray-900"><?= $stats['total_tools'] ?? 0 ?></p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                        <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Published</p>
                    <p class="text-2xl font-semibold text-gray-900"><?= $stats['active_tools'] ?? 0 ?></p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                        <svg class="w-4 h-4 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Pending Review</p>
                    <p class="text-2xl font-semibold text-gray-900"><?= $stats['pending_tools'] ?? 0 ?></p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                        <svg class="w-4 h-4 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14H5.236a2 2 0 01-1.789-2.894l3.5-7A2 2 0 018.736 3h4.018a2 2 0 01.485.06l3.76.94m-7 10v5a2 2 0 002 2h.096c.5 0 .905-.405.905-.904 0-.715.211-1.413.608-2.008L17.294 15M10 14l4-2c.707-.293 1.207-.293 1.414 0l2.293 2.293a1 1 0 001.414-1.414L17.414 11"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Inactive</p>
                    <p class="text-2xl font-semibold text-gray-900"><?= $stats['inactive_tools'] ?? 0 ?></p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                        <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Views</p>
                    <p class="text-2xl font-semibold text-gray-900"><?= number_format($stats['total_views'] ?? 0) ?></p>
                </div>
            </div>
        </div>
    </div>

    <!-- 工具列表 -->
    <div class="bg-white rounded-lg border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-900">Your Tools</h2>
        </div>

        <?php if (empty($tools)): ?>
            <div class="px-6 py-12 text-center">
                <div class="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                    <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No tools yet</h3>
                <p class="text-gray-600 mb-6">You haven't created any tools yet. Create your first tool to get started.</p>
                <a href="?page=tool-generator" 
                   class="inline-flex items-center px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                    </svg>
                    Create Your First Tool
                </a>
            </div>
        <?php else: ?>
            <div class="divide-y divide-gray-200">
                <?php foreach ($tools as $tool): ?>
                    <div class="px-6 py-6 hover:bg-gray-50 transition-colors">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-4 flex-1">
                                <div class="flex-shrink-0">
                                    <div class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center text-xl">
                                        <?= htmlspecialchars($tool['icon'] ?: '🔧') ?>
                                    </div>
                                </div>
                                
                                <div class="flex-1 min-w-0">
                                    <div class="flex items-center space-x-3 mb-1">
                                        <h3 class="text-lg font-semibold text-gray-900 truncate">
                                            <?= htmlspecialchars($tool['name']) ?>
                                        </h3>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border <?= getStatusBadge($tool['status']) ?>">
                                            <?= getStatusLabel($tool['status']) ?>
                                        </span>
                                    </div>
                                    
                                    <p class="text-sm text-gray-600 mb-2 line-clamp-2">
                                        <?= htmlspecialchars($tool['description'] ?: 'No description available') ?>
                                    </p>
                                    
                                    <div class="flex items-center space-x-4 text-xs text-gray-500">
                                        <span class="flex items-center">
                                            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                                            </svg>
                                            <?= htmlspecialchars($tool['category_name'] ?: 'Uncategorized') ?>
                                        </span>
                                        <span class="flex items-center">
                                            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                            </svg>
                                            <?= number_format($tool['view_count']) ?> views
                                        </span>
                                        <span class="flex items-center">
                                            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a4 4 0 118 0v4m-4 8a4 4 0 11-8 0v-1a4 4 0 014-4h4a4 4 0 014 4v1a4 4 0 11-8 0z"></path>
                                            </svg>
                                            <?= date('M j, Y', strtotime($tool['created_at'])) ?>
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <div class="flex items-center space-x-3 ml-4">
                                <a href="/tools/<?= htmlspecialchars($tool['category_slug'] ?: 'uncategorized') ?>/<?= htmlspecialchars($tool['slug']) ?>"
                                   target="_blank"
                                   class="group inline-flex items-center px-4 py-2 text-sm font-medium text-blue-600 bg-white border border-blue-200 rounded-lg hover:bg-blue-50 hover:border-blue-300 hover:text-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200 shadow-sm hover:shadow-md">
                                    <svg class="w-4 h-4 mr-2 transition-transform duration-200 group-hover:scale-110" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                    </svg>
                                    <?= $tool['status'] === 'active' ? 'View' : 'Preview' ?>
                                    <svg class="w-3 h-3 ml-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                                    </svg>
                                </a>

                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- 分页导航 -->
<?php if (!empty($tools) && $totalPages > 1): ?>
    <div class="flex justify-center mt-8">
        <nav class="flex items-center space-x-2">
            <?php if ($page > 1): ?>
                <a href="?page=my-tools&p=<?= $page - 1 ?>"
                   class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 hover:text-gray-700 transition-colors">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                    </svg>
                    Previous
                </a>
            <?php endif; ?>

            <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                <a href="?page=my-tools&p=<?= $i ?>"
                   class="inline-flex items-center px-3 py-2 text-sm font-medium <?= $i === $page ? 'text-white bg-black border-black' : 'text-gray-500 bg-white border-gray-300 hover:bg-gray-50 hover:text-gray-700' ?> border rounded-lg transition-colors">
                    <?= $i ?>
                </a>
            <?php endfor; ?>

            <?php if ($page < $totalPages): ?>
                <a href="?page=my-tools&p=<?= $page + 1 ?>"
                   class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 hover:text-gray-700 transition-colors">
                    Next
                    <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                    </svg>
                </a>
            <?php endif; ?>
        </nav>
    </div>
<?php endif; ?>

<!-- 自定义提示窗口组件 -->
<div id="customModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center">
    <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4 transform transition-all duration-300 scale-95" id="modalContent">
        <div class="p-6">
            <div class="flex items-center mb-4">
                <div id="modalIcon" class="w-8 h-8 rounded-full flex items-center justify-center mr-3">
                    <!-- Icon will be inserted here -->
                </div>
                <h3 id="modalTitle" class="text-lg font-semibold text-gray-900"></h3>
            </div>
            <p id="modalMessage" class="text-gray-600 mb-6"></p>
            <div class="flex justify-end space-x-3">
                <button id="modalCancel" class="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors hidden">
                    Cancel
                </button>
                <button id="modalConfirm" class="px-4 py-2 bg-black text-white rounded hover:bg-gray-800 transition-colors">
                    OK
                </button>
            </div>
        </div>
    </div>
</div>



<script>
// 自定义提示窗口函数
function showCustomAlert(title, message, type = 'info') {
    return new Promise((resolve) => {
        const modal = document.getElementById('customModal');
        const modalTitle = document.getElementById('modalTitle');
        const modalMessage = document.getElementById('modalMessage');
        const modalIcon = document.getElementById('modalIcon');
        const modalConfirm = document.getElementById('modalConfirm');
        const modalCancel = document.getElementById('modalCancel');
        const modalContent = document.getElementById('modalContent');

        modalTitle.textContent = title;
        modalMessage.textContent = message;

        let iconHtml = '';
        let iconBg = '';
        switch (type) {
            case 'success':
                iconHtml = '<svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg>';
                iconBg = 'bg-green-500';
                break;
            case 'error':
                iconHtml = '<svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>';
                iconBg = 'bg-red-500';
                break;
            case 'warning':
                iconHtml = '<svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg>';
                iconBg = 'bg-yellow-500';
                break;
            default:
                iconHtml = '<svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path></svg>';
                iconBg = 'bg-blue-500';
        }

        modalIcon.innerHTML = iconHtml;
        modalIcon.className = 'w-8 h-8 rounded-full flex items-center justify-center mr-3 ' + iconBg;

        // Hide cancel button for alerts
        modalCancel.classList.add('hidden');

        modal.classList.remove('hidden');
        setTimeout(() => {
            modalContent.classList.remove('scale-95');
            modalContent.classList.add('scale-100');
        }, 10);

        const confirmHandler = () => {
            modalContent.classList.remove('scale-100');
            modalContent.classList.add('scale-95');
            setTimeout(() => modal.classList.add('hidden'), 300);
            modalConfirm.removeEventListener('click', confirmHandler);
            resolve(true);
        };

        modalConfirm.addEventListener('click', confirmHandler);
    });
}



// 辅助函数
function getStatusBadgeClass(status) {
    const badges = {
        'active': 'bg-green-100 text-green-800 border-green-200',
        'inactive': 'bg-orange-100 text-orange-800 border-orange-200',
        'coming_soon': 'bg-yellow-100 text-yellow-800 border-yellow-200'
    };
    return badges[status] || 'bg-gray-100 text-gray-800 border-gray-200';
}

function getStatusLabelText(status) {
    const labels = {
        'active': 'Published',
        'inactive': 'Inactive',
        'coming_soon': 'Pending Review'
    };
    return labels[status] || 'Unknown';
}


</script>