<?php
/**
 * 热门工具组件
 * 在首页和其他页面展示热门工具
 */

// 引入工具卡片组件
require_once ROOT_PATH . '/templates/components/tools/tool-card.php';

/**
 * 获取热门工具数据
 * 使用工具辅助函数获取数据
 */
function getPopularToolsData() {
    // 尝试从数据库获取数据
    try {
        $tools = getPopularTools(8);
        if (!empty($tools)) {
            return $tools;
        }
    } catch (Exception $e) {
        error_log('Error getting popular tools: ' . $e->getMessage());
    }

    // 以下是备用数据，如果工具辅助函数不可用
    return [
        [
            'name' => 'HTML Formatter',
            'slug' => 'html-formatter',
            'description' => 'Format and beautify your HTML code with proper indentation, making it more readable and organized.',
            'icon' => '🔧',
            'category' => 'Development',
            'category_slug' => 'development',
            'view_count' => 15234,
            'rating' => 4.8,
            'is_featured' => true,
            'is_new' => false,
            'tags' => ['HTML', 'Formatter', 'Code', 'Beautify'],
            'difficulty' => 'Easy',
            'estimated_time' => '< 1 min'
        ],
        [
            'name' => 'CSS Minifier',
            'slug' => 'css-minifier',
            'description' => 'Compress and minify CSS files to reduce file size and improve website loading speed.',
            'icon' => '📦',
            'category' => 'Development',
            'category_slug' => 'development',
            'view_count' => 12876,
            'rating' => 4.7,
            'is_featured' => false,
            'is_new' => false,
            'tags' => ['CSS', 'Minify', 'Optimize', 'Compress'],
            'difficulty' => 'Easy',
            'estimated_time' => '< 1 min'
        ],
        [
            'name' => 'JavaScript Formatter',
            'slug' => 'js-formatter',
            'description' => 'Format and beautify JavaScript code with proper indentation and syntax highlighting.',
            'icon' => '⚡',
            'category' => 'Development',
            'category_slug' => 'development',
            'view_count' => 11543,
            'rating' => 4.6,
            'is_featured' => false,
            'is_new' => false,
            'tags' => ['JavaScript', 'Formatter', 'Code'],
            'difficulty' => 'Easy',
            'estimated_time' => '< 1 min'
        ],
        [
            'name' => 'Image Converter',
            'slug' => 'image-converter',
            'description' => 'Convert images between different formats like JPG, PNG, WebP, GIF, and more with high quality.',
            'icon' => '🖼️',
            'category' => 'Design & Media',
            'category_slug' => 'design',
            'view_count' => 18567,
            'rating' => 4.9,
            'is_featured' => false,
            'is_new' => true,
            'tags' => ['Image', 'Convert', 'Format', 'JPG', 'PNG'],
            'difficulty' => 'Easy',
            'estimated_time' => '< 2 min'
        ],
        [
            'name' => 'QR Code Generator',
            'slug' => 'qr-generator',
            'description' => 'Generate QR codes for URLs, text, WiFi credentials, contact info, and other data types.',
            'icon' => '📱',
            'category' => 'Utilities',
            'category_slug' => 'utilities',
            'view_count' => 22145,
            'rating' => 4.8,
            'is_featured' => true,
            'is_new' => false,
            'tags' => ['QR Code', 'Generate', 'Mobile', 'URL'],
            'difficulty' => 'Easy',
            'estimated_time' => '< 1 min'
        ],
        [
            'name' => 'Text Counter',
            'slug' => 'text-counter',
            'description' => 'Count characters, words, sentences, and paragraphs in your text with detailed statistics.',
            'icon' => '📊',
            'category' => 'Productivity',
            'category_slug' => 'productivity',
            'view_count' => 9876,
            'rating' => 4.5,
            'is_featured' => false,
            'is_new' => false,
            'tags' => ['Text', 'Counter', 'Statistics', 'Words'],
            'difficulty' => 'Easy',
            'estimated_time' => '< 1 min'
        ],
        [
            'name' => 'Password Generator',
            'slug' => 'password-generator',
            'description' => 'Generate strong, secure passwords with customizable length and character sets.',
            'icon' => '🔐',
            'category' => 'Security & Privacy',
            'category_slug' => 'security',
            'view_count' => 14321,
            'rating' => 4.7,
            'is_featured' => false,
            'is_new' => false,
            'tags' => ['Password', 'Security', 'Generate', 'Strong'],
            'difficulty' => 'Easy',
            'estimated_time' => '< 1 min'
        ],
        [
            'name' => 'Color Palette Generator',
            'slug' => 'color-palette-generator',
            'description' => 'Generate beautiful color palettes for your design projects with various harmony rules.',
            'icon' => '🎨',
            'category' => 'Design & Media',
            'category_slug' => 'design',
            'view_count' => 7654,
            'rating' => 4.6,
            'is_featured' => false,
            'is_new' => true,
            'tags' => ['Color', 'Palette', 'Design', 'Harmony'],
            'difficulty' => 'Easy',
            'estimated_time' => '< 1 min'
        ]
    ];
}

/**
 * 渲染热门工具区域
 * @param int $limit 显示工具数量限制
 * @param string $style 卡片样式
 * @param bool $showHeader 是否显示区域标题
 */
function renderPopularToolsSection($limit = 8, $style = 'default', $showHeader = true) {
    // 获取热门工具数据，按view_count排序
    try {
        // 首先尝试从数据库获取所有工具
        $allTools = getAllTools();
        if (empty($allTools)) {
            // 如果数据库为空，使用备用数据
            $allTools = getPopularToolsData();
        }

        // 按view_count降序排序
        usort($allTools, function($a, $b) {
            return ($b['view_count'] ?? 0) - ($a['view_count'] ?? 0);
        });

        $tools = array_slice($allTools, 0, $limit);

        // 如果仍然没有数据，使用备用数据
        if (empty($tools)) {
            $backupTools = getPopularToolsData();
            $tools = array_slice($backupTools, 0, $limit);
        }

    } catch (Exception $e) {
        error_log('Error getting popular tools: ' . $e->getMessage());
        // 使用备用数据
        $allTools = getPopularToolsData();
        $tools = array_slice($allTools, 0, $limit);
    }

    $hasSlider = count($tools) > 3;

    if ($showHeader): ?>
    <!-- 热门工具区域标题 -->
    <div class="text-center mb-16">
        <h2 class="text-3xl md:text-4xl font-bold mb-4">Popular Tools</h2>
        <p class="text-xl text-gray-400 max-w-2xl mx-auto">
            Most used tools by our community of developers and designers
        </p>
    </div>
    <?php endif; ?>

    <!-- 热门工具滑动容器 -->
    <div class="relative">
        <?php if ($hasSlider && $showHeader): ?>
        <!-- 左箭头 -->
        <button id="popularPrev" class="absolute left-0 top-1/2 transform -translate-y-1/2 z-10 bg-accent text-white p-3 rounded-full hover:bg-blue-700 transition-colors duration-200 shadow-lg">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
        </button>

        <!-- 右箭头 -->
        <button id="popularNext" class="absolute right-0 top-1/2 transform -translate-y-1/2 z-10 bg-accent text-white p-3 rounded-full hover:bg-blue-700 transition-colors duration-200 shadow-lg">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
        </button>

        <!-- 滑动容器 -->
        <div id="popularSlider" class="overflow-hidden mx-12">
            <div class="flex transition-transform duration-300 ease-in-out">
                <?php foreach ($tools as $tool): ?>
                <div class="w-1/3 flex-shrink-0 px-4">
                    <?php renderDefaultToolCard($tool, 'popular'); ?>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php else: ?>
        <!-- 热门工具网格 -->
        <?php renderToolGridWithSection($tools, $style, 4, 'popular'); ?>
        <?php endif; ?>
    </div>

    <?php
    // Popular Tools 不显示 View All Tools 按钮
}

/**
 * 渲染最新工具区域
 * @param int $limit 显示工具数量限制
 * @param string $style 卡片样式
 * @param bool $showHeader 是否显示区域标题
 */
function renderNewToolsSection($limit = 8, $style = 'default', $showHeader = true) {
    // 获取最新工具数据，按created_at排序
    try {
        $tools = getNewTools($limit); // 直接获取按时间排序的最新工具
    } catch (Exception $e) {
        error_log('Error getting new tools: ' . $e->getMessage());
        // 备用数据：从所有工具中获取最新的几个
        $allTools = getAllTools();
        // 按创建时间排序
        usort($allTools, function($a, $b) {
            $dateA = strtotime($a['created_at'] ?? '1970-01-01');
            $dateB = strtotime($b['created_at'] ?? '1970-01-01');
            return $dateB - $dateA;
        });
        $tools = array_slice($allTools, 0, $limit);
    }

    if (empty($tools)) {
        return; // 如果没有工具，不显示这个区域
    }

    $hasSlider = count($tools) > 3;

    if ($showHeader): ?>
    <!-- 最新工具区域标题 -->
    <div class="text-center mb-16">
        <h2 class="text-3xl md:text-4xl font-bold mb-4">New Tools</h2>
        <p class="text-xl text-gray-400 max-w-2xl mx-auto">
            Latest additions to our growing collection of powerful tools
        </p>
    </div>
    <?php endif; ?>

    <!-- 最新工具滑动容器 -->
    <div class="relative">
        <?php if ($hasSlider && $showHeader): ?>
        <!-- 左箭头 -->
        <button id="newPrev" class="absolute left-0 top-1/2 transform -translate-y-1/2 z-10 bg-accent text-white p-3 rounded-full hover:bg-blue-700 transition-colors duration-200 shadow-lg">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
        </button>

        <!-- 右箭头 -->
        <button id="newNext" class="absolute right-0 top-1/2 transform -translate-y-1/2 z-10 bg-accent text-white p-3 rounded-full hover:bg-blue-700 transition-colors duration-200 shadow-lg">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
        </button>

        <!-- 滑动容器 -->
        <div id="newSlider" class="overflow-hidden mx-12">
            <div class="flex transition-transform duration-300 ease-in-out">
                <?php foreach ($tools as $tool): ?>
                <div class="w-1/3 flex-shrink-0 px-4">
                    <?php renderDefaultToolCard($tool, 'new'); ?>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php else: ?>
        <!-- 最新工具网格 -->
        <?php renderToolGridWithSection($tools, $style, 4, 'new'); ?>
        <?php endif; ?>
    </div>

    <?php if ($showHeader): ?>
    <!-- 查看更多按钮 -->
    <div class="text-center mt-12">
        <a href="/tools"
           class="inline-flex items-center px-8 py-3 border border-accent text-accent hover:bg-accent hover:text-white transition-all duration-200">
            <span class="mr-2">View All Tools</span>
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
            </svg>
        </a>
    </div>
    <?php endif;
}

/**
 * 渲染特色工具区域
 */
function renderFeaturedToolsSection() {
    // 直接从数据库获取featured工具
    try {
        $tools = getFeaturedTools();
        if (empty($tools)) {
            return; // 如果没有featured工具，不显示这个区域
        }
    } catch (Exception $e) {
        error_log('Error getting featured tools: ' . $e->getMessage());
        return;
    }

    $toolsArray = array_values($tools);
    $hasSlider = count($toolsArray) > 3;
    ?>

    <!-- 特色工具区域标题 -->
    <div class="text-center mb-16">
        <h2 class="text-3xl md:text-4xl font-bold mb-4">Featured Tools</h2>
        <p class="text-xl text-gray-400 max-w-2xl mx-auto">
            Hand-picked tools that deliver exceptional value and performance
        </p>
    </div>

    <!-- 特色工具滑动容器 -->
    <div class="relative">
        <?php if ($hasSlider): ?>
        <!-- 左箭头 -->
        <button id="featuredPrev" class="absolute left-0 top-1/2 transform -translate-y-1/2 z-10 bg-accent text-white p-3 rounded-full hover:bg-blue-700 transition-colors duration-200 shadow-lg">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
        </button>

        <!-- 右箭头 -->
        <button id="featuredNext" class="absolute right-0 top-1/2 transform -translate-y-1/2 z-10 bg-accent text-white p-3 rounded-full hover:bg-blue-700 transition-colors duration-200 shadow-lg">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
        </button>

        <!-- 滑动容器 -->
        <div id="featuredSlider" class="overflow-hidden mx-12">
            <div class="flex transition-transform duration-300 ease-in-out">
                <?php foreach ($toolsArray as $tool): ?>
                <div class="w-1/3 flex-shrink-0 px-4">
                    <?php renderDefaultToolCard($tool, 'featured'); ?>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php else: ?>
        <!-- 特色工具网格（3个或更少） -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
            <?php foreach ($toolsArray as $tool): ?>
                <?php renderDefaultToolCard($tool, 'featured'); ?>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>
    </div>

    <?php
}



/**
 * 渲染工具统计信息
 */
function renderToolStats() {
    $tools = getPopularToolsData();
    $totalViews = array_sum(array_column($tools, 'view_count'));
    $avgRating = round(array_sum(array_column($tools, 'rating')) / count($tools), 1);
    $featuredCount = count(array_filter($tools, function($tool) { return $tool['featured'] ?? false; }));
    $newCount = count(array_filter($tools, function($tool) { return $tool['is_new'] ?? false; }));
    ?>
    
    <!-- 工具统计 -->
    <div class="bg-gray-900 border border-gray-800 p-8">
        <div class="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            <div>
                <div class="text-3xl font-bold text-accent mb-2"><?= count($tools) ?>+</div>
                <div class="text-gray-400 text-sm">Total Tools</div>
            </div>
            <div>
                <div class="text-3xl font-bold text-accent mb-2"><?= number_format($totalViews) ?>+</div>
                <div class="text-gray-400 text-sm">Total Uses</div>
            </div>
            <div>
                <div class="text-3xl font-bold text-accent mb-2"><?= $avgRating ?>/5</div>
                <div class="text-gray-400 text-sm">Avg Rating</div>
            </div>
            <div>
                <div class="text-3xl font-bold text-accent mb-2"><?= $featuredCount ?></div>
                <div class="text-gray-400 text-sm">Featured</div>
            </div>
        </div>
    </div>
    
    <?php
}

/**
 * 渲染工具搜索建议
 */
function renderToolSearchSuggestions() {
    $suggestions = [
        'HTML formatter', 'CSS minifier', 'Image converter', 'QR generator',
        'Password generator', 'Color picker', 'Text counter', 'JSON formatter'
    ];
    ?>
    
    <div class="text-center">
        <p class="text-gray-400 text-sm mb-4">Popular searches:</p>
        <div class="flex flex-wrap justify-center gap-2">
            <?php foreach ($suggestions as $suggestion): ?>
                <a href="/search?q=<?= urlencode($suggestion) ?>" 
                   class="bg-gray-800 text-gray-300 text-sm px-3 py-1 hover:bg-gray-700 hover:text-white transition-colors duration-200">
                    <?= htmlspecialchars($suggestion) ?>
                </a>
            <?php endforeach; ?>
        </div>
    </div>
    
    <?php
}
?>
