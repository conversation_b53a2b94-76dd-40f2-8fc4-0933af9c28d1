<?php
/**
 * 配额检查API
 * 检查用户是否有足够配额执行指定操作
 */

header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');

// 检查用户登录状态
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

if (!isset($_SESSION['user_id'])) {
    echo json_encode([
        'success' => false,
        'message' => 'Please login first'
    ]);
    exit;
}

// 定义根路径
define('ROOT_PATH', dirname(__DIR__));
define('APP_INITIALIZED', true);

// 引入必要文件
require_once ROOT_PATH . '/includes/database-connection.php';
require_once ROOT_PATH . '/classes/QuotaManager.php';

try {
    // 获取请求数据
    $input = json_decode(file_get_contents('php://input'), true);
    $action = $input['action'] ?? '';
    
    if (empty($action)) {
        throw new Exception('Action type cannot be empty');
    }
    
    // 获取数据库连接
    $pdo = getDatabaseConnection();
    $quotaManager = new QuotaManager($pdo);
    
    // 检查配额
    $userId = $_SESSION['user_id'];
    $userQuota = $quotaManager->getUserQuota($userId);

    // 特殊处理 tool_analysis 操作（需要10配额）
    if ($action === 'tool_analysis') {
        $requiredQuota = 10;
        $hasEnoughQuota = ($userQuota['api_used'] + $requiredQuota) <= $userQuota['api_quota'];
        $message = $hasEnoughQuota ? 'Quota sufficient' : "Insufficient quota! Tool analysis requires {$requiredQuota} quota. Please upgrade your subscription plan.";
    } else {
        // 使用标准配额规则
        $hasEnoughQuota = $quotaManager->hasEnoughQuota($userId, $action);
        $requiredQuota = $quotaManager->getRequiredQuota($action);
        $message = $hasEnoughQuota ? 'Quota sufficient' : $quotaManager->getInsufficientQuotaMessage($action);
    }

    echo json_encode([
        'success' => true,
        'has_enough_quota' => $hasEnoughQuota,
        'required_quota' => $requiredQuota,
        'current_quota' => [
            'used' => $userQuota['api_used'],
            'total' => $userQuota['api_quota'],
            'remaining' => $userQuota['api_quota'] - $userQuota['api_used']
        ],
        'message' => $message
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
