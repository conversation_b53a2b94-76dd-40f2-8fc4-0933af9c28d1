<?php
/**
 * 加载需求列表AJAX处理
 */

/**
 * 根据投票数量获取对应的图标和样式
 */
function getVoteIcon($votes) {
    if ($votes >= 100) {
        return [
            'icon' => '🔥',
            'color' => 'text-red-400',
            'textColor' => 'text-red-400',
            'animation' => 'fire-animation',
            'label' => 'Super Hot Request - ' . $votes . ' votes'
        ];
    } elseif ($votes >= 50) {
        return [
            'icon' => '⭐',
            'color' => 'text-yellow-400',
            'textColor' => 'text-yellow-400',
            'animation' => 'star-twinkle',
            'label' => 'Popular Request - ' . $votes . ' votes'
        ];
    } elseif ($votes >= 20) {
        return [
            'icon' => '🚀',
            'color' => 'text-blue-400',
            'textColor' => 'text-blue-400',
            'animation' => 'rocket-rise',
            'label' => 'Rising Request - ' . $votes . ' votes'
        ];
    } elseif ($votes >= 10) {
        return [
            'icon' => '💡',
            'color' => 'text-green-400',
            'textColor' => 'text-green-400',
            'animation' => 'bulb-glow',
            'label' => 'Good Idea - ' . $votes . ' votes'
        ];
    } elseif ($votes >= 5) {
        return [
            'icon' => '👍',
            'color' => 'text-purple-400',
            'textColor' => 'text-purple-400',
            'animation' => '',
            'label' => 'Nice Suggestion - ' . $votes . ' votes'
        ];
    } elseif ($votes >= 1) {
        return [
            'icon' => '💭',
            'color' => 'text-gray-400',
            'textColor' => 'text-gray-400',
            'animation' => '',
            'label' => 'Initial Idea - ' . $votes . ' votes'
        ];
    } else {
        return [
            'icon' => '📝',
            'color' => 'text-gray-300',
            'textColor' => 'text-gray-300',
            'animation' => '',
            'label' => 'New Proposal - 0 votes'
        ];
    }
}

// 开启错误显示用于调试
ini_set('display_errors', 1);
error_reporting(E_ALL);

// 设置JSON响应头
header('Content-Type: application/json');

// 安全检查
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

session_start();

// 使用统一的数据库连接
require_once __DIR__ . '/includes/ajax-database.php';

$response = ['success' => false, 'message' => '', 'data' => []];

try {
    // 获取POST数据
    $input = json_decode(file_get_contents('php://input'), true);
    
    $status = $input['status'] ?? 'all';
    $category = $input['category'] ?? 'all';
    $sort = $input['sort'] ?? 'latest';
    $page = max(1, intval($input['page'] ?? 1));
    $limit = 10;
    $offset = ($page - 1) * $limit;

    // 构建查询条件
    $whereConditions = [];
    $params = [];

    if ($status !== 'all') {
        $whereConditions[] = "r.status = ?";
        $params[] = $status;
    }

    if ($category !== 'all') {
        $whereConditions[] = "r.category = ?";
        $params[] = $category;
    }

    $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

    // 排序逻辑
    $orderBy = match($sort) {
        'votes' => 'r.votes DESC, r.created_at DESC',
        'oldest' => 'r.created_at ASC',
        default => 'r.created_at DESC'
    };

    // 获取需求列表
    $sql = "
        SELECT r.*, m.username, m.first_name, m.last_name,
               CASE WHEN rv.user_id IS NOT NULL THEN 1 ELSE 0 END as user_voted
        FROM pt_user_requests r
        LEFT JOIN pt_member m ON r.user_id = m.id
        LEFT JOIN pt_request_votes rv ON r.id = rv.request_id AND rv.user_id = ?
        {$whereClause}
        ORDER BY {$orderBy}
        LIMIT {$limit} OFFSET {$offset}
    ";

    $userVoteParams = [isset($_SESSION['user_id']) ? $_SESSION['user_id'] : 0];
    $requests = $pdo->prepare($sql);
    $requests->execute(array_merge($userVoteParams, $params));
    $requestsList = $requests->fetchAll();

    // 获取总数
    $countSql = "SELECT COUNT(*) FROM pt_user_requests r {$whereClause}";
    $countStmt = $pdo->prepare($countSql);
    $countStmt->execute($params);
    $totalRequests = $countStmt->fetchColumn();
    $totalPages = ceil($totalRequests / $limit);

    // 获取工具分类
    $categoryStmt = $pdo->query("SELECT slug, name FROM pt_tool_category WHERE status = 'active' ORDER BY sort_order ASC, name ASC");
    $dbCategories = $categoryStmt->fetchAll();
    $categories = [];
    foreach ($dbCategories as $cat) {
        $categories[$cat['slug']] = $cat['name'];
    }
    $categories['other'] = 'Other';

    // 状态颜色配置
    $statusColors = [
        'pending' => 'bg-gray-700 text-white',
        'reviewing' => 'bg-blue-600 text-white',
        'accepted' => 'bg-green-600 text-white',
        'rejected' => 'bg-red-600 text-white',
        'completed' => 'bg-purple-600 text-white'
    ];

    // 安全过滤函数
    function sanitizeOutput($text) {
        // 确保HTML实体编码
        $text = htmlspecialchars($text, ENT_QUOTES, 'UTF-8');

        // 移除任何残留的脚本标签
        $text = preg_replace('/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/mi', '', $text);

        // 移除事件处理器属性
        $text = preg_replace('/\s*on\w+\s*=\s*["\'][^"\']*["\']/i', '', $text);

        return $text;
    }

    // 生成HTML
    $html = '';
    if (empty($requestsList)) {
        if ($page === 1) {
            $html = '<div class="bg-gray-900 border border-gray-800 p-8 text-center">
                        <p class="text-gray-300">No requests found matching your criteria.</p>
                     </div>';
        }
    } else {
        // 添加动画样式到HTML开头
        $html .= '<style>
        .fire-animation { animation: fire-flicker 2s ease-in-out infinite alternate; }
        @keyframes fire-flicker {
            0%, 100% { transform: scale(1) rotate(-1deg); filter: hue-rotate(0deg) brightness(1); }
            25% { transform: scale(1.05) rotate(1deg); filter: hue-rotate(10deg) brightness(1.1); }
            50% { transform: scale(0.98) rotate(-0.5deg); filter: hue-rotate(-5deg) brightness(0.95); }
            75% { transform: scale(1.02) rotate(0.5deg); filter: hue-rotate(5deg) brightness(1.05); }
        }
        .star-twinkle { animation: star-twinkle 1.5s ease-in-out infinite; }
        @keyframes star-twinkle {
            0%, 100% { opacity: 1; transform: scale(1); filter: brightness(1); }
            50% { opacity: 0.7; transform: scale(1.1); filter: brightness(1.3); }
        }
        .rocket-rise { animation: rocket-rise 3s ease-in-out infinite; }
        @keyframes rocket-rise {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            25% { transform: translateY(-3px) rotate(-1deg); }
            50% { transform: translateY(-5px) rotate(0deg); }
            75% { transform: translateY(-2px) rotate(1deg); }
        }
        .bulb-glow { animation: bulb-glow 2.5s ease-in-out infinite; }
        @keyframes bulb-glow {
            0%, 100% { filter: brightness(1) drop-shadow(0 0 0px rgba(34, 197, 94, 0)); transform: scale(1); }
            50% { filter: brightness(1.2) drop-shadow(0 0 8px rgba(34, 197, 94, 0.6)); transform: scale(1.05); }
        }
        </style>';

        foreach ($requestsList as $request) {
            $statusClass = $statusColors[$request['status']] ?? 'bg-gray-700 text-white';
            $categoryName = $categories[$request['category']] ?? ucfirst($request['category']);
            $userName = sanitizeOutput($request['first_name'] ?? $request['username']);
            // 获取投票图标数据
            $votes = (int)$request['votes'];
            $iconData = getVoteIcon($votes);

            $adminReply = '';
            if (!empty($request['admin_reply'])) {
                $adminReply = '<div class="mt-6 p-4 border-2 border-yellow-400 rounded-lg">
                                <p class="text-sm font-semibold text-yellow-400 mb-2">Official Response</p>
                                <p class="text-sm text-gray-300 leading-relaxed">' . sanitizeOutput($request['admin_reply']) . '</p>
                              </div>';
            }

            $voteButton = '';
            if (isset($_SESSION['user_id'])) {
                $borderClass = $request['user_voted'] ? 'border-blue-400 shadow-blue-400/20 shadow-lg' : 'border-gray-600 hover:border-blue-400';
                $voteButton = '<button onclick="voteRequest(' . $request['id'] . ')"
                                      class="group/vote relative bg-gradient-to-br from-gray-800 to-gray-700 hover:from-gray-700 hover:to-gray-600 border-2 ' . $borderClass . ' rounded-2xl p-4 transition-all duration-300 transform hover:scale-105 hover:shadow-xl cursor-pointer"
                                      data-request-id="' . $request['id'] . '" title="Click to vote">
                                    <div class="text-center">
                                        <div class="text-3xl mb-2 transition-transform duration-200 group-hover/vote:scale-110 ' . $iconData['animation'] . '"
                                             aria-label="' . $iconData['label'] . '">' . $iconData['icon'] . '</div>
                                        <div class="text-xl font-bold ' . $iconData['color'] . ' transition-colors">' . $request['votes'] . '</div>
                                        <div class="text-xs font-medium ' . $iconData['textColor'] . ' uppercase tracking-wide">votes</div>
                                    </div>
                                </button>';
            } else {
                $voteButton = '<div class="bg-gradient-to-br from-gray-800 to-gray-700 border-2 border-gray-600 rounded-2xl p-4 opacity-50">
                                    <div class="text-center">
                                        <div class="text-3xl mb-2" aria-label="' . $iconData['label'] . '">' . $iconData['icon'] . '</div>
                                        <div class="text-xl font-bold ' . $iconData['color'] . '">' . $request['votes'] . '</div>
                                        <div class="text-xs font-medium ' . $iconData['textColor'] . ' uppercase tracking-wide">votes</div>
                                    </div>
                                </div>';
            }

            $html .= '<div class="bg-gray-900 border border-gray-800 p-6 hover:border-gray-700 transition-colors">
                        <div class="flex justify-between items-start">
                            <div class="flex-1">
                                <div class="flex items-center space-x-3 mb-3">
                                    <h3 class="text-lg font-medium text-white">' . sanitizeOutput($request['title']) . '</h3>
                                    <span class="px-3 py-1 text-xs font-medium ' . $statusClass . '">' . ucfirst($request['status']) . '</span>
                                    <span class="px-3 py-1 text-xs font-medium bg-gray-700 text-white">' . sanitizeOutput($categoryName) . '</span>
                                </div>
                                <p class="text-gray-300 mb-4">' . sanitizeOutput($request['description']) . '</p>
                                <div class="flex items-center space-x-4 text-sm text-gray-400">
                                    <span>By ' . $userName . '</span>
                                    <span>' . date('M j, Y', strtotime($request['created_at'])) . '</span>
                                </div>
                                ' . $adminReply . '
                            </div>
                            <div class="ml-6 text-center">
                                ' . $voteButton . '
                            </div>
                        </div>
                      </div>';
        }
    }

    $response['success'] = true;
    $response['data'] = [
        'html' => $html,
        'current_page' => $page,
        'total_pages' => $totalPages,
        'total_requests' => $totalRequests,
        'has_more' => $page < $totalPages
    ];

} catch (Exception $e) {
    if (ob_get_level()) {
        ob_clean();
    }
    
    $response['message'] = $e->getMessage();
    $response['debug'] = [
        'error' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ];
}

echo json_encode($response);
?>
