<?php
/**
 * 用户中心仪表板页面
 */



try {
    // 获取系统默认API配额设置
    $stmt = $pdo->prepare("SELECT setting_value FROM pt_system_config WHERE setting_key = 'default_api_quota'");
    $stmt->execute();
    $defaultApiQuota = (int)$stmt->fetchColumn();

    // 从 pt_api_usage 表统计实际使用的配额（与 api-usage 页面完全一致）
    $stmt = $pdo->prepare("SELECT COALESCE(SUM(quota_consumed), 0) as total_used FROM pt_api_usage WHERE user_id = ?");
    $stmt->execute([$user['id']]);
    $apiUsed = (int)$stmt->fetchColumn();

    // 获取用户配额
    $apiQuota = $user['api_quota'] ?? $defaultApiQuota;
    $apiUsagePercent = $apiQuota > 0 ? round(($apiUsed / $apiQuota) * 100, 1) : 0;

    // 工具使用统计
    $toolUsageCount = 0;
    $favoritesCount = 0;
    $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);

    if (in_array('pt_tool_usage', $tables)) {
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM pt_tool_usage WHERE user_id = ?");
        $stmt->execute([$user['id']]);
        $toolUsageCount = $stmt->fetchColumn();
    }

    // 收藏工具统计
    if (in_array('pt_user_favorites', $tables)) {
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM pt_user_favorites WHERE user_id = ?");
        $stmt->execute([$user['id']]);
        $favoritesCount = $stmt->fetchColumn();


    }
    
    // 账户创建天数
    $accountAge = 0;
    if (!empty($user['created_at'])) {
        $createdDate = new DateTime($user['created_at']);
        $now = new DateTime();
        $accountAge = $now->diff($createdDate)->days;
    }

    // 用户统计数据
    $stats = [
        'api_used' => $apiUsed,
        'api_quota' => $apiQuota,
        'api_usage_percent' => $apiUsagePercent,
        'tools_used' => $toolUsageCount,
        'favorites_count' => $favoritesCount,
        'account_age' => $accountAge,
        'account_status' => $user['status'] ?? 'active'
    ];

    // 获取最近活动
    $recentActivity = [];
    if (in_array('pt_member_activity_log', $tables)) {
        $stmt = $pdo->prepare("
            SELECT action, description, created_at
            FROM pt_member_activity_log
            WHERE user_id = ?
            ORDER BY created_at DESC
            LIMIT 4
        ");
        $stmt->execute([$user['id']]);
        $activities = $stmt->fetchAll();



        foreach ($activities as $activity) {
            $timeAgo = timeAgo($activity['created_at']);
            $recentActivity[] = [
                'action' => $activity['description'] ?: ucfirst(str_replace('_', ' ', $activity['action'])),
                'user' => $user['email'],
                'time' => $timeAgo
            ];
        }
    }

    // 如果没有活动记录，显示账户创建记录
    if (empty($recentActivity) && !empty($user['created_at'])) {
        $recentActivity = [
            [
                'action' => 'Account created',
                'user' => $user['email'],
                'time' => timeAgo($user['created_at'])
            ]
        ];
    }

    // 获取用户收藏的工具（优先显示）
    $favoriteTools = [];

    // 首先尝试获取用户收藏的工具
    if (in_array('pt_user_favorites', $tables)) {
        $stmt = $pdo->prepare("
            SELECT t.name, t.slug, t.category_id, c.slug as category_slug, 'favorited' as type, uf.created_at as favorited_at
            FROM pt_user_favorites uf
            JOIN pt_tool t ON uf.tool_id = t.id
            LEFT JOIN pt_tool_category c ON t.category_id = c.id
            WHERE uf.user_id = ? AND t.status = 'active'
            ORDER BY uf.created_at DESC
            LIMIT 4
        ");
        $stmt->execute([$user['id']]);
        $favoriteTools = $stmt->fetchAll();
    }

    // 如果收藏工具不足4个，用最常用工具补充
    if (count($favoriteTools) < 4 && in_array('pt_tool_usage', $tables)) {
        $limit = 4 - count($favoriteTools);
        $excludeIds = [];

        // 获取已收藏工具的ID，避免重复
        if (!empty($favoriteTools)) {
            $slugs = array_column($favoriteTools, 'slug');
            $placeholders = str_repeat('?,', count($slugs) - 1) . '?';
            $stmt = $pdo->prepare("SELECT id FROM pt_tool WHERE slug IN ($placeholders)");
            $stmt->execute($slugs);
            $excludeIds = $stmt->fetchAll(PDO::FETCH_COLUMN);
        }

        $excludeClause = !empty($excludeIds) ? 'AND t.id NOT IN (' . implode(',', array_map('intval', $excludeIds)) . ')' : '';

        $stmt = $pdo->prepare("
            SELECT t.name, t.slug, t.category_id, c.slug as category_slug, 'frequently_used' as type, COUNT(*) as usage_count
            FROM pt_tool_usage tu
            JOIN pt_tool t ON tu.tool_id = t.id
            LEFT JOIN pt_tool_category c ON t.category_id = c.id
            WHERE tu.user_id = ? AND t.status = 'active' $excludeClause
            GROUP BY t.id, t.name, t.slug, t.category_id, c.slug
            ORDER BY usage_count DESC
            LIMIT $limit
        ");
        $stmt->execute([$user['id']]);
        $usedTools = $stmt->fetchAll();

        $favoriteTools = array_merge($favoriteTools, $usedTools);
    }

    // 如果仍然没有工具，保持空数组，显示空状态
    // 不再显示全站热门工具，让用户主动探索和收藏

} catch (Exception $e) {
    error_log('User dashboard data error: ' . $e->getMessage());

    $stats = [
        'api_used' => 0,
        'api_quota' => 1000,
        'api_usage_percent' => 0,
        'tools_used' => 0,
        'favorites_count' => 0,
        'account_age' => 0,
        'account_status' => 'active'
    ];
    $recentActivity = [];
    $favoriteTools = [];
}
?>

<!-- 页面描述 -->
<div class="mb-8">
    <div>
        <?php
        // 根据当前时间生成问候语
        $hour = (int)date('H');
        $greeting = '';

        if ($hour >= 5 && $hour < 12) {
            $greeting = 'Good morning';
        } elseif ($hour >= 12 && $hour < 17) {
            $greeting = 'Good afternoon';
        } elseif ($hour >= 17 && $hour < 22) {
            $greeting = 'Good evening';
        } else {
            $greeting = 'Good night';
        }
        ?>
        <p class="text-gray-600"><?= $greeting ?>, <?= htmlspecialchars($user['first_name'] ?? 'User') ?>! Here's your account overview.</p>
    </div>
</div>

<!-- 统计卡片 -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- API使用量 -->
    <div class="replicate-card stat-card p-6">
        <div class="flex items-center">
            <div class="p-3 bg-blue-100 text-blue-600 mr-4">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
                </svg>
            </div>
            <div>
                <p class="text-sm font-medium text-gray-600">API Calls Used</p>
                <p class="text-2xl font-bold text-gray-900"><?= number_format($stats['api_used']) ?></p>
            </div>
        </div>
    </div>

    <!-- 工具使用次数 -->
    <div class="replicate-card stat-card p-6">
        <div class="flex items-center">
            <div class="p-3 bg-green-100 text-green-600 mr-4">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
            </div>
            <div>
                <p class="text-sm font-medium text-gray-600">Tools Used</p>
                <p class="text-2xl font-bold text-gray-900"><?= number_format($stats['tools_used']) ?></p>
            </div>
        </div>
    </div>

    <!-- 账户状态 -->
    <div class="replicate-card stat-card p-6">
        <div class="flex items-center">
            <div class="p-3 bg-<?= $stats['account_status'] === 'active' ? 'green' : 'yellow' ?>-100 text-<?= $stats['account_status'] === 'active' ? 'green' : 'yellow' ?>-600 mr-4">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <div>
                <p class="text-sm font-medium text-gray-600">Account Status</p>
                <p class="text-2xl font-bold text-gray-900"><?= ucfirst($stats['account_status']) ?></p>
            </div>
        </div>
    </div>

    <!-- 收藏工具 -->
    <div class="replicate-card stat-card p-6">
        <div class="flex items-center">
            <div class="p-3 bg-red-100 text-red-600 mr-4">
                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd"></path>
                </svg>
            </div>
            <div>
                <p class="text-sm font-medium text-gray-600">Favorite Tools</p>
                <p class="text-2xl font-bold text-gray-900"><?= number_format($stats['favorites_count']) ?></p>
            </div>
        </div>
    </div>
</div>

<!-- 内容区域 -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
    <!-- 最近活动 -->
    <div class="replicate-card">
        <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Recent Activity</h3>
        </div>
        <div class="p-6">
            <div class="space-y-4">
                <?php if (!empty($recentActivity)): ?>
                    <?php foreach ($recentActivity as $activity): ?>
                    <div class="flex items-center space-x-3">
                        <div class="w-2 h-2 bg-black"></div>
                        <div class="flex-1">
                            <p class="text-sm text-gray-900"><?= htmlspecialchars($activity['action']) ?></p>
                            <p class="text-xs text-gray-500"><?= htmlspecialchars($activity['user']) ?> • <?= htmlspecialchars($activity['time']) ?></p>
                        </div>
                    </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="text-center py-8">
                        <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <h4 class="text-lg font-medium text-gray-900 mb-2">No recent activity</h4>
                        <p class="text-gray-500">Start using our tools to see your activity here</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- 常用工具 -->
    <div class="replicate-card">
        <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Your Tools</h3>
            <p class="text-sm text-gray-500 mt-1">Favorited and frequently used tools</p>
        </div>
        <div class="p-6">
            <div class="space-y-4">
                <?php if (!empty($favoriteTools)): ?>
                    <?php foreach ($favoriteTools as $tool): ?>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div>
                                <p class="text-sm font-medium text-gray-900"><?= htmlspecialchars($tool['name']) ?></p>
                                <p class="text-xs text-gray-500">
                                    <?php if ($tool['type'] === 'favorited'): ?>
                                        <span class="inline-flex items-center">
                                            <svg class="w-3 h-3 text-red-500 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd"></path>
                                            </svg>
                                            Favorited <?= timeAgo($tool['favorited_at']) ?>
                                        </span>
                                    <?php elseif ($tool['type'] === 'frequently_used'): ?>
                                        <?= number_format($tool['usage_count']) ?> uses
                                    <?php else: ?>
                                        <?= number_format($tool['usage_count']) ?> views
                                    <?php endif; ?>
                                </p>
                            </div>
                        </div>
                        <div class="text-right">
                            <?php
                            // 构建正确的工具URL - 强制使用分类路径
                            $toolUrl = '/tools/';
                            if (!empty($tool['category_slug'])) {
                                $toolUrl .= htmlspecialchars($tool['category_slug']) . '/';
                            } else {
                                // 如果没有分类信息，使用默认分类
                                $toolUrl .= 'utilities/';
                            }
                            $toolUrl .= htmlspecialchars($tool['slug']);
                            ?>
                            <a href="<?= $toolUrl ?>" class="text-black hover:text-gray-600 text-sm font-medium">
                                Use →
                            </a>
                        </div>
                    </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="text-center py-8">
                        <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd"></path>
                        </svg>
                        <h4 class="text-lg font-medium text-gray-900 mb-2">No favorite tools yet</h4>
                        <p class="text-gray-500 mb-4">Discover and favorite tools to see them here</p>
                        <a href="/tools" class="inline-flex items-center px-4 py-2 bg-black text-white text-sm font-medium hover:bg-gray-800 transition-colors">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                            Explore Tools
                        </a>
                    </div>
                <?php endif; ?>
            </div>
            <div class="mt-6">
                <a href="/tools" class="text-black hover:text-gray-600 text-sm font-medium">
                    Browse all tools →
                </a>
            </div>
        </div>
    </div>
</div>



<!-- API配额警告 -->
<?php if ($stats['api_usage_percent'] >= 80): ?>
<div class="mt-8">
    <div class="bg-yellow-50 border border-yellow-200 p-4">
        <div class="flex">
            <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                </svg>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-yellow-800">API Quota Warning</h3>
                <div class="mt-2 text-sm text-yellow-700">
                    <p>You've used <?= $stats['api_usage_percent'] ?>% of your monthly API quota. Consider upgrading your plan to avoid service interruption.</p>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>


