<?php
/**
 * AI Tool Generator API Endpoint
 * Handles requests to generate custom tools using Claude Sonnet 4
 */

header('Content-Type: text/plain; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Cache-Control: no-cache');
header('X-Accel-Buffering: no');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit();
}

// 使用统一的数据库连接
require_once dirname(__DIR__) . '/includes/database-connection.php';

try {
    // Get and validate input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        throw new Exception('Invalid JSON input');
    }
    
    // Validate required fields
    $requiredFields = ['name', 'slug', 'prompt'];
    foreach ($requiredFields as $field) {
        if (empty($input[$field])) {
            throw new Exception("Missing required field: {$field}");
        }
    }

    // Sanitize inputs
    $name = trim($input['name']);
    $slug = trim($input['slug']);
    $category = trim($input['category'] ?? '');
    $icon = trim($input['icon'] ?? '🔧');
    $description = trim($input['description'] ?? '');
    $tags = trim($input['tags'] ?? '');
    $status = trim($input['status'] ?? 'coming_soon');
    $prompt = trim($input['prompt']);
    $model = $input['model'] ?? 'claude-sonnet-4-20250514';
    $appCode = $input['appCode'] ?? '';

    // Validate prompt length
    if (strlen($prompt) < 10) {
        throw new Exception('Prompt must be at least 10 characters long');
    }

    if (strlen($prompt) > 5000) {
        throw new Exception('Prompt must be less than 5000 characters');
    }
    
    // Get aiHubMix platform configuration
    $stmt = $pdo->prepare("SELECT id FROM pt_service_platform WHERE code = 'aihubmix' AND is_active = 1");
    $stmt->execute();
    $aihubmixPlatform = $stmt->fetch();

    if (!$aihubmixPlatform) {
        throw new Exception('AiHubMix platform not found or inactive');
    }

    // Build the prompt for tool generation
    $generationPrompt = buildToolGenerationPrompt($name, $slug, $category, $icon, $description, $tags, $status, $prompt);

    // Prepare messages for AI
    $messages = [
        [
            'role' => 'system',
            'content' => getSystemPrompt()
        ],
        [
            'role' => 'user',
            'content' => $generationPrompt
        ]
    ];

    // Generate the tool
    $startTime = microtime(true);

    // Make request to aiHubMix API
    $requestData = [
        'model' => $model,
        'messages' => $messages,
        'max_tokens' => 12000,
        'temperature' => 0.3,
        'stream' => true
    ];

    // Handle streaming response with APP-Code in headers
    makeAiHubMixStreamRequest($requestData, $appCode);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

/**
 * Build the tool generation prompt
 */
function buildToolGenerationPrompt($name, $slug, $category, $icon, $description, $tags, $status, $userPrompt) {
    // Load the template file
    $templatePath = __DIR__ . '/../templates/tool-template.php';
    $template = file_get_contents($templatePath);

    $prompt = "Generate a complete, production-ready PHP+HTML tool using the provided template structure.\n\n";

    $prompt .= "**Tool Information:**\n";
    $prompt .= "- Name: {$name}\n";
    $prompt .= "- Slug: {$slug}\n";
    $prompt .= "- Category: {$category}\n";
    $prompt .= "- Icon: {$icon}\n";
    $prompt .= "- Description: {$description}\n";
    $prompt .= "- Tags: {$tags}\n";
    $prompt .= "- Status: {$status}\n\n";

    $prompt .= "**User Requirements & Specification:**\n";
    $prompt .= "{$userPrompt}\n\n";

    $prompt .= "**IMPORTANT: If the user prompt contains a structured specification (like from the Prompt page), follow it exactly. If it's a simple description, expand it into a full functional tool.**\n\n";

    $prompt .= "**Template Structure:**\n";
    $prompt .= "Use this exact template structure and replace the placeholders:\n\n";
    $prompt .= "```php\n";
    $prompt .= $template . "\n";
    $prompt .= "```\n\n";

    $prompt .= "**Placeholder Replacement Instructions:**\n";
    $prompt .= "Replace ALL placeholders in the template with actual content:\n";
    $prompt .= "- {{TOOL_NAME}} → {$name}\n";
    $prompt .= "- {{TOOL_SLUG}} → {$slug}\n";
    $prompt .= "- {{TOOL_CATEGORY}} → {$category}\n";
    $prompt .= "- {{TOOL_CATEGORY_SLUG}} → " . strtolower($category) . "\n";
    $prompt .= "- {{TOOL_ICON}} → {$icon}\n";
    $prompt .= "- {{TOOL_DESCRIPTION}} → {$description}\n";
    $prompt .= "- {{TOOL_TAGS}} → {$tags}\n";
    $prompt .= "- {{INPUT_SECTION}} → Complete input interface based on requirements\n";
    $prompt .= "- {{OUTPUT_SECTION}} → Complete output interface based on requirements\n";
    $prompt .= "- {{FAQ_SECTION}} → 4-5 relevant FAQ items with proper HTML structure\n";
    $prompt .= "- {{JAVASCRIPT_FUNCTIONALITY}} → Main tool logic and functionality\n";
    $prompt .= "- {{CLEAR_FUNCTION}} → Clear input logic\n";
    $prompt .= "- {{PASTE_FUNCTION}} → Paste clipboard content logic\n";
    $prompt .= "- {{COPY_FUNCTION}} → Copy output logic with success feedback\n";
    $prompt .= "- {{DOWNLOAD_FUNCTION}} → Download output as file logic\n";
    $prompt .= "- {{PROCESS_FUNCTION}} → Main processing function call\n";
    $prompt .= "- {{INITIALIZATION_CODE}} → Any initialization code needed\n\n";

    $prompt .= "**Styling Requirements:**\n";
    $prompt .= "1. Use EXACT color scheme: bg-gray-900 (main), bg-gray-800 (cards), bg-gray-700 (elements)\n";
    $prompt .= "2. Use border-gray-700 for borders\n";
    $prompt .= "3. Text colors: text-white (headings), text-gray-400 (descriptions), text-gray-300 (labels)\n";
    $prompt .= "4. Button colors: bg-blue-600, bg-green-600, bg-red-600 with hover variants\n";
    $prompt .= "5. NO rounded corners (zero border-radius design)\n";
    $prompt .= "6. Use grid grid-cols-1 lg:grid-cols-2 gap-8 for main layout\n";
    $prompt .= "7. Include proper hover effects and transitions\n\n";

    $prompt .= "**Functional Requirements:**\n";
    $prompt .= "1. Include comprehensive input validation and error handling with user-friendly messages\n";
    $prompt .= "2. Add action buttons: Copy, Download, Clear, Paste (with FontAwesome icons)\n";
    $prompt .= "3. Implement real-time processing and live preview where applicable\n";
    $prompt .= "4. Add helpful tooltips, placeholders, and user guidance text\n";
    $prompt .= "5. Include keyboard shortcuts (Ctrl+Enter to process, Ctrl+C to copy, etc.)\n";
    $prompt .= "6. Make it fully responsive with proper mobile layout (grid responsive)\n";
    $prompt .= "7. Add proper accessibility features (ARIA labels, focus management, tab order)\n";
    $prompt .= "8. Include loading states and progress indicators for async operations\n";
    $prompt .= "9. Add success/error notifications with visual feedback (green/red colors)\n";
    $prompt .= "10. Implement proper form state management and data persistence\n";
    $prompt .= "11. Add export functionality (JSON, CSV, TXT as appropriate for the tool)\n";
    $prompt .= "12. Include usage statistics, counters, or metrics where relevant\n";
    $prompt .= "13. Add copy-to-clipboard functionality with success feedback\n";
    $prompt .= "14. Implement file download with proper MIME types and filenames\n";
    $prompt .= "15. Include proper error boundaries and graceful degradation
16. NEVER use browser default alert(), confirm(), or prompt() dialogs
17. Implement custom modal components for user confirmations and notifications
18. Use modals, toast notifications, or inline alerts instead of default browser dialogs\n\n";

    $prompt .= "**Common Patterns to Follow:**\n";
    $prompt .= "1. Input areas: Use textarea or input fields with proper placeholders\n";
    $prompt .= "2. Action buttons: Use consistent styling with icons (fas fa-copy, fas fa-download, etc.)\n";
    $prompt .= "3. Output areas: Use pre tags for code, divs for formatted content\n";
    $prompt .= "4. Statistics: Show character/word counts, processing time, etc.\n";
    $prompt .= "5. Error handling: Show user-friendly error messages in red\n";
    $prompt .= "6. Success feedback: Show green success messages and visual confirmations\n";
    $prompt .= "7. Loading states: Use spinners and disabled states during processing\n";
    $prompt .= "8. Tooltips: Add helpful tooltips for complex features\n\n";

    $prompt .= "**Special Instructions for Specification Documents:**\n";
    $prompt .= "- If the user prompt contains structured specifications (Tool Overview, Core Functionality, etc.), implement ALL requirements exactly\n";
    $prompt .= "- Pay special attention to Input Requirements, Output Specifications, and Key Features\n";
    $prompt .= "- Implement the exact User Workflow described in the specification\n";
    $prompt .= "- Follow the Processing Logic requirements precisely\n";
    $prompt .= "- Ensure all Acceptance Criteria are met in the implementation\n";
    $prompt .= "- If validation rules are specified, implement them exactly\n";
    $prompt .= "- Handle all edge cases mentioned in the specification\n\n";

    $prompt .= "**Output Format:**\n";
    $prompt .= "- Provide ONLY the complete PHP+HTML code\n";
    $prompt .= "- Follow the EXACT template structure provided above\n";
    $prompt .= "- Do NOT include markdown code blocks, explanations, or comments outside the code\n";
    $prompt .= "- Start with <?php and end with the footer include\n";
    $prompt .= "- Ensure all functionality is working and complete\n";
    $prompt .= "- The file should be ready to save as {$slug}.php and deploy immediately\n";
    $prompt .= "- Include comprehensive JavaScript for all interactive features\n";
    $prompt .= "- Add 4-5 relevant FAQ items specific to the tool's functionality\n";
    $prompt .= "- Ensure the tool is production-ready, fully functional, and matches existing tool quality\n";
    $prompt .= "- Test all user interactions and edge cases in your implementation\n";

    return $prompt;
}

/**
 * Get the system prompt for tool generation
 */
function getSystemPrompt() {
    $currentTime = date('c');
    return "You are an expert PHP and web developer specializing in creating tools for the Prompt2Tool platform. Current time: {$currentTime}

CORE DEVELOPMENT REQUIREMENTS:
You MUST follow the exact structure, styling, and patterns used in existing tools.

Key requirements:
1. Follow the EXACT template structure provided in the user prompt
2. Use the precise color scheme: bg-gray-900, bg-gray-800, bg-gray-700, text-white, text-gray-400
3. NO rounded corners (zero border-radius design) - never use rounded-lg, rounded-md, etc.
4. Include proper SEO data, breadcrumbs, and layout includes
5. Create functional, interactive tools with proper JavaScript
6. Add relevant FAQ sections with tool-specific questions
7. Use consistent button styling and hover effects
8. Implement proper error handling and user feedback
9. Make tools fully responsive and accessible
10. NEVER use browser default alert(), confirm(), or prompt() dialogs
11. For user confirmations or notifications, implement custom modal components
12. Use modals, toast notifications, or inline alerts instead of default browser dialogs
13. Generate ONLY the PHP+HTML code without any markdown or explanations

You are creating tools that will be deployed directly to the platform, so they must be production-ready and follow all existing patterns exactly.";
}

/**
 * Clean up the generated code
 */
function cleanGeneratedCode($code) {
    // Remove markdown code blocks if present
    $code = preg_replace('/```(?:php|html)?\s*/', '', $code);
    $code = preg_replace('/```\s*$/', '', $code);
    
    // Remove any leading/trailing whitespace
    $code = trim($code);
    
    // Ensure it starts with <?php if it's a PHP file
    if (!str_starts_with($code, '<?php')) {
        $code = "<?php\n" . $code;
    }
    
    return $code;
}

/**
 * Make streaming request to aiHubMix API
 */
function makeAiHubMixStreamRequest($requestData, $appCode = '') {
    global $pdo;

    // Get aiHubMix platform configuration
    $stmt = $pdo->prepare("SELECT id FROM pt_service_platform WHERE code = 'aihubmix' AND is_active = 1");
    $stmt->execute();
    $aihubmixPlatform = $stmt->fetch();

    if (!$aihubmixPlatform) {
        throw new Exception('AiHubMix platform not found or inactive');
    }

    $platformId = $aihubmixPlatform['id'];

    // Get API key
    $stmt = $pdo->prepare("
        SELECT api_key
        FROM pt_service_key
        WHERE platform_id = ? AND is_active = 1
        ORDER BY id ASC
        LIMIT 1
    ");
    $stmt->execute([$platformId]);
    $apiKeyData = $stmt->fetch();

    if (!$apiKeyData) {
        throw new Exception('No active API key found for AiHubMix');
    }

    // Make streaming cURL request
    $ch = curl_init();

    curl_setopt_array($ch, [
        CURLOPT_URL => 'https://api.aihubmix.com/v1/chat/completions',
        CURLOPT_RETURNTRANSFER => false,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => json_encode($requestData),
        CURLOPT_HTTPHEADER => [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $apiKeyData['api_key'],
            'APP-Code: ' . (!empty($appCode) ? $appCode : 'KXTM3281')
        ],
        CURLOPT_TIMEOUT => 300,
        CURLOPT_CONNECTTIMEOUT => 10,
        CURLOPT_SSL_VERIFYPEER => true,
        CURLOPT_WRITEFUNCTION => function($ch, $data) {
            // $ch parameter is required by cURL callback but not used
            echo $data;
            ob_flush();
            flush();
            return strlen($data);
        }
    ]);

    curl_exec($ch); // Result not needed for streaming output
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);

    if ($error) {
        throw new Exception('cURL error: ' . $error);
    }

    if ($httpCode !== 200) {
        throw new Exception('API request failed with HTTP code: ' . $httpCode);
    }
}

/**
 * Make request to aiHubMix API (non-streaming)
 */
function makeAiHubMixRequest($requestData) {
    global $pdo;

    // Get aiHubMix platform configuration
    $stmt = $pdo->prepare("SELECT id FROM pt_service_platform WHERE code = 'aihubmix' AND is_active = 1");
    $stmt->execute();
    $aihubmixPlatform = $stmt->fetch();

    if (!$aihubmixPlatform) {
        throw new Exception('AiHubMix platform not found or inactive');
    }

    $platformId = $aihubmixPlatform['id'];

    // Get API key
    $stmt = $pdo->prepare("
        SELECT api_key
        FROM pt_service_key
        WHERE platform_id = ? AND is_active = 1
        ORDER BY id ASC
        LIMIT 1
    ");
    $stmt->execute([$platformId]);
    $apiKeyData = $stmt->fetch();

    if (!$apiKeyData) {
        throw new Exception('No active API key found for AiHubMix');
    }

    // Make cURL request
    $ch = curl_init();

    curl_setopt_array($ch, [
        CURLOPT_URL => 'https://api.aihubmix.com/v1/chat/completions',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_POST => true,
        CURLOPT_POSTFIELDS => json_encode($requestData),
        CURLOPT_HTTPHEADER => [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $apiKeyData['api_key']
        ],
        CURLOPT_TIMEOUT => 120,
        CURLOPT_CONNECTTIMEOUT => 10,
        CURLOPT_SSL_VERIFYPEER => true,
    ]);

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);

    if ($error) {
        throw new Exception('cURL error: ' . $error);
    }

    if ($httpCode !== 200) {
        throw new Exception('API request failed with HTTP code: ' . $httpCode);
    }

    $decodedResponse = json_decode($response, true);

    if (!$decodedResponse) {
        throw new Exception('Invalid JSON response from API');
    }

    if (isset($decodedResponse['error'])) {
        throw new Exception('API error: ' . $decodedResponse['error']['message']);
    }

    return $decodedResponse;
}


?>
