<?php
/**
 * 贪吃蛇游戏 Tool Page
 * 遵循01-UI设计规范.md - 零圆角设计
 */

$currentPage = 'tool-snake-game';
$breadcrumbs = [
    ['name' => 'Home', 'url' => '/'],
    ['name' => 'Tools', 'url' => '/tools'],
    ['name' => 'Games', 'url' => '/tools/games'],
    ['name' => '贪吃蛇游戏']
];

// 包含动态SEO助手
require_once ROOT_PATH . '/app/helpers/dynamic-seo.php';

// 生成SEO数据 - 优化长度
$seoData = [
    'title' => '贪吃蛇游戏 Online - 经典街机游戏 - Prompt2Tool',
    'description' => '在线玩经典贪吃蛇游戏，支持键盘控制，记录最高分，挑战你的反应速度和策略思维。',
    'keywords' => '贪吃蛇, 贪吃蛇游戏, 在线游戏, 经典游戏, 街机游戏, 蛇游戏',
    'og_title' => '贪吃蛇游戏 Online - 经典街机游戏',
    'og_description' => '在线玩经典贪吃蛇游戏，支持键盘控制，记录最高分，挑战你的反应速度和策略思维。',
    'canonical' => getCurrentURL()
];

// 包含公共头部
include_once ROOT_PATH . '/templates/layout/header.php';
?>

<!-- 工具页面主体 -->
<div class="bg-gray-900 text-white">

    <!-- Hero Section with SEO optimized content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-12">
        <div class="text-center mb-12">
            <div class="flex items-center justify-center mb-4">
                <div class="bg-green-600 p-3 mr-4">
 <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
 <path fill-rule="evenodd" d="M3 5a2 2 0 012-2h10a2 2 0 012 2v10a2 2 0 01-2 2H5a2 2 0 01-2-2V5zm11 1H6v8l4-2 4 2V6z" clip-rule="evenodd"/>
                    </svg>
                </div>
                <div class="text-left">
                    <h1 class="text-4xl font-bold text-white mb-2">贪吃蛇游戏</h1>
 <p class="text-xl text-gray-400">经典街机游戏，挑战你的反应速度和策略思维</p>
                </div>
            </div>
        </div>

        <!-- 工具主体 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- 游戏区域 -->
            <div class="bg-gray-800 border border-gray-700 p-6">
                <div class="flex items-center justify-between mb-4">
 <h2 class="text-xl font-semibold text-white">游戏区域</h2>
                    <div class="flex space-x-2">
                        <button id="startBtn" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 text-sm font-medium">开始游戏</button>
 <button id="pauseBtn" class="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 text-sm font-medium">暂停</button>
                        <button id="resetBtn" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 text-sm font-medium">重置</button>
                    </div>
                </div>
                <div class="flex justify-center">
                    <canvas id="gameCanvas" width="400" height="400" class="border-2 border-gray-600 bg-gray-900"></canvas>
                </div>
                <div class="mt-4 text-center">
                    <p class="text-gray-400 text-sm">使用方向键或 WASD 控制蛇的移动</p>
                </div>
            </div>

            <!-- 游戏信息 -->
            <div class="bg-gray-800 border border-gray-700 p-6">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-xl font-semibold text-white">游戏信息</h2>
 <div class="flex space-x-2">
                        <button id="soundBtn" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 text-sm font-medium">音效: 开</button>
                    </div>
                </div>
                <div class="space-y-4">
 <div class="bg-gray-700 p-4">
 <h3 class="text-lg font-medium text-white mb-2">当前分数</h3>
 <p id="currentScore" class="text-3xl font-bold text-green-400">0</p>
                    </div>
                    <div class="bg-gray-700 p-4">
 <h3 class="text-lg font-medium text-white mb-2">最高分数</h3>
 <p id="highScore" class="text-3xl font-bold text-yellow-400">0</p>
 </div>
                    <div class="bg-gray-700 p-4">
                        <h3 class="text-lg font-medium text-white mb-2">游戏状态</h3>
                        <p id="gameStatus" class="text-xl text-blue-400">准备开始</p>
                    </div>
                    <div class="bg-gray-700 p-4">
 <h3 class="text-lg font-medium text-white mb-2">难度设置</h3>
                        <select id="difficulty" class="bg-gray-600 text-white px-3 py-2 text-sm w-full">
                            <option value="easy">简单 (慢速)</option>
                            <option value="medium" selected>中等 (中速)</option>
                            <option value="hard">困难 (快速)</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!-- 工具信息 -->
        <div class="mt-12 grid grid-cols-1 md:grid-cols-3 gap-8">
            <div class="bg-gray-800 border border-gray-700 p-6">
                <h4 class="text-lg font-semibold text-white mb-3">游戏特色</h4>
                <ul class="text-gray-300 space-y-2">
                    <li>• 经典贪吃蛇游戏机制</li>
                    <li>• 实时分数统计</li>
                    <li>• 最高分记录保存</li>
                    <li>• 三种难度级别</li>
                    <li>• 键盘控制支持</li>
                    <li>• 音效开关控制</li>
                </ul>
            </div>
            <div class="bg-gray-800 border border-gray-700 p-6">
                <h4 class="text-lg font-semibold text-white mb-3">游戏优势</h4>
                <ul class="text-gray-300 space-y-2">
                    <li>• 提高反应速度</li>
                    <li>• 增强手眼协调能力</li>
                    <li>• 锻炼策略思维</li>
 <li>• 缓解压力放松心情</li>
                    <li>• 随时随地可玩</li>
 <li>• 无需安装即开即玩</li>
                </ul>
            </div>
            <div class="bg-gray-800 border border-gray-700 p-6">
                <h4 class="text-lg font-semibold text-white mb-3">适用场景</h4>
                <ul class="text-gray-300 space-y-2">
                    <li>• 工作间隙放松</li>
                    <li>• 学习之余娱乐</li>
                    <li>• 等待时间打发</li>
                    <li>• 儿童智力开发</li>
                    <li>• 老年人脑力锻炼</li>
                    <li>• 朋友间竞技比拼</li>
                </ul>
            </div>
        </div>

        <!-- FAQ Section -->
        <div class="px-8 pt-16 pb-16">
            <h2 class="text-3xl font-bold text-white text-center mb-8">常见问题</h2>
            <div class="max-w-4xl mx-auto space-y-6">
                <div class="bg-gray-800 border border-gray-700 p-6">
                    <h3 class="text-xl font-semibold text-white mb-3">如何开始游戏？</h3>
                    <p class="text-gray-300">点击"开始游戏"按钮即可开始，使用方向键或WASD键控制蛇的移动方向。</p>
                </div>
                <div class="bg-gray-800 border border-gray-700 p-6">
 <h3 class="text-xl font-semibold text-white mb-3">如何获得高分？</h3>
 <p class="text-gray-300">吃到食物可以增加分数和蛇的长度。避免撞到墙壁或自己的身体，存活时间越长分数越高。</p>
                </div>
                <div class="bg-gray-800 border border-gray-700 p-6">
                    <h3 class="text-xl font-semibold text-white mb-3">游戏难度有什么区别？</h3>
                    <p class="text-gray-300">简单模式蛇移动较慢，适合新手；中等模式速度适中；困难模式移动很快，挑战性高。</p>
                </div>
                <div class="bg-gray-800 border border-gray-700 p-6">
 <h3 class="text-xl font-semibold text-white mb-3">最高分如何保存？</h3>
                    <p class="text-gray-300">最高分会保存在浏览器的本地存储中，下次访问时会自动加载历史最高分。</p>
                </div>
                <div class="bg-gray-800 border border-gray-700 p-6">
                    <h3 class="text-xl font-semibold text-white mb-3">游戏支持哪些控制方式？</h3>
 <p class="text-gray-300">支持键盘方向键（↑↓←→）和WASD键控制蛇的移动方向，也可以使用屏幕按钮控制。</p>
                </div>
            </div>
        </div>
    </div>

<script>
// 贪吃蛇游戏功能
document.addEventListener('DOMContentLoaded', function() {
    const canvas = document.getElementById('gameCanvas');
    const ctx = canvas.getContext('2d');
    const startBtn = document.getElementById('startBtn');
    const pauseBtn = document.getElementById('pauseBtn');
    const resetBtn = document.getElementById('resetBtn');
    const soundBtn = document.getElementById('soundBtn');
    const currentScoreEl = document.getElementById('currentScore');
    const highScoreEl = document.getElementById('highScore');
    const gameStatusEl = document.getElementById('gameStatus');
    const difficultySelect = document.getElementById('difficulty');

    // 游戏配置
    const gridSize = 20;
    const tileCount = canvas.width / gridSize;
    
    let snake = [
        {x: 10, y: 10}
    ];
    let food = {};
    let dx = 0;
    let dy = 0;
    let score = 0;
    let highScore = localStorage.getItem('snakeHighScore') || 0;
    let gameRunning = false;
    let gamePaused = false;
    let soundEnabled = true;
    let gameSpeed = 100;

    // 初始化
    highScoreEl.textContent = highScore;
    generateFood();

    // 生成食物
    function generateFood() {
        food = {
            x: Math.floor(Math.random() * tileCount),
            y: Math.floor(Math.random() * tileCount)
        };
        
        // 确保食物不在蛇身上
        for (let segment of snake) {
            if (segment.x === food.x && segment.y === food.y) {
                generateFood();
                break;
            }
        }
    }

    // 绘制游戏
    function draw() {
        // 清空画布
        ctx.fillStyle = '#111827';
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        // 绘制蛇
        ctx.fillStyle = '#10b981';
        for (let segment of snake) {
            ctx.fillRect(segment.x * gridSize, segment.y * gridSize, gridSize - 2, gridSize - 2);
        }

        // 绘制蛇头
        ctx.fillStyle = '#059669';
        ctx.fillRect(snake[0].x * gridSize, snake[0].y * gridSize, gridSize - 2, gridSize - 2);

        // 绘制食物
        ctx.fillStyle = '#ef4444';
        ctx.fillRect(food.x * gridSize, food.y * gridSize, gridSize - 2, gridSize - 2);
    }

    // 更新游戏状态
    function update() {
        if (!gameRunning || gamePaused) return;

        // 移动蛇头
        const head = {x: snake[0].x + dx, y: snake[0].y + dy};

        // 检查碰撞
        if (head.x < 0 || head.x >= tileCount || head.y < 0 || head.y >= tileCount) {
            gameOver();
            return;
        }

        // 检查是否撞到自己
        for (let segment of snake) {
            if (head.x === segment.x && head.y === segment.y) {
                gameOver();
                return;
            }
        }

        snake.unshift(head);

        // 检查是否吃到食物
        if (head.x === food.x && head.y === food.y) {
            score += 10;
            currentScoreEl.textContent = score;
            generateFood();
            playSound('eat');
        } else {
            snake.pop();
        }

        draw();
    }

    // 游戏结束
    function gameOver() {
        gameRunning = false;
        gameStatusEl.textContent = '游戏结束';
        
        if (score > highScore) {
            highScore = score;
            highScoreEl.textContent = highScore;
            localStorage.setItem('snakeHighScore', highScore);
            playSound('win');
        } else {
            playSound('lose');
        }
    }

    // 开始游戏
    function startGame() {
        if (gameRunning) return;
        
        snake = [{x: 10, y: 10}];
        dx = 0;
        dy = 0;
        score = 0;
        currentScoreEl.textContent = score;
        gameRunning = true;
        gamePaused = false;
        gameStatusEl.textContent = '游戏进行中';
        
        // 设置游戏速度
        switch(difficultySelect.value) {
            case 'easy':
                gameSpeed = 150;
                break;
            case 'medium':
                gameSpeed = 100;
                break;
            case 'hard':
                gameSpeed = 50;
                break;
        }
        
        generateFood();
        draw();
        gameLoop();
    }

    // 暂停游戏
    function pauseGame() {
        if (!gameRunning) return;
        gamePaused = !gamePaused;
        gameStatusEl.textContent = gamePaused ? '游戏暂停' : '游戏进行中';
    }

    // 重置游戏
    function resetGame() {
        gameRunning = false;
        gamePaused = false;
        snake = [{x: 10, y: 10}];
        dx = 0;
        dy = 0;
        score = 0;
        currentScoreEl.textContent = score;
        gameStatusEl.textContent = '准备开始';
        generateFood();
        draw();
    }

    // 游戏循环
    function gameLoop() {
        if (!gameRunning) return;
        update();
        setTimeout(gameLoop, gameSpeed);
    }

    // 播放音效
    function playSound(type) {
        if (!soundEnabled) return;
        
        // 创建音效（使用Web Audio API）
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();
        
        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);
        
        switch(type) {
            case 'eat':
                oscillator.frequency.value = 800;
                gainNode.gain.value = 0.1;
                break;
            case 'win':
                oscillator.frequency.value = 1000;
                gainNode.gain.value = 0.2;
                break;
            case 'lose':
                oscillator.frequency.value = 200;
                gainNode.gain.value = 0.1;
                break;
        }
        
        oscillator.start();
        oscillator.stop(audioContext.currentTime + 0.1);
    }

    // 键盘控制
    document.addEventListener('keydown', function(e) {
        if (!gameRunning || gamePaused) return;
        
        switch(e.key) {
            case 'ArrowUp':
            case 'w':
            case 'W':
                if (dy === 0) {
                    dx = 0;
                    dy = -1;
                }
                break;
            case 'ArrowDown':
            case 's':
            case 'S':
                if (dy === 0) {
                    dx = 0;
                    dy = 1;
                }
                break;
            case 'ArrowLeft':
            case 'a':
            case 'A':
                if (dx === 0) {
                    dx = -1;
                    dy = 0;
                }
                break;
            case 'ArrowRight':
            case 'd':
            case 'D':
                if (dx === 0) {
                    dx = 1;
                    dy = 0;
                }
                break;
        }
    });

    // 按钮事件
    startBtn.addEventListener('click', startGame);
    pauseBtn('click', pauseGame);
    resetBtn.addEventListener('click', resetGame);
   Btn.addEventListener('click', function() {
        soundEnabled = !soundEnabled;
        soundBtn.textContent = `音效: ${soundEnabled ? '开' : '关'}`;
        soundBtn.className = soundEnabled ? 
            'bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 text-sm font-medium' :
            'bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 text-sm font-medium';
    });

    // 初始绘制
    draw();
});
</script>

</div>

<?php include_once ROOT_PATH . '/templates/layout/footer.php'; ?>