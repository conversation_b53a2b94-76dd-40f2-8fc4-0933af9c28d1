<?php
/**
 * 检查用户状态API
 * 用于验证页面检查用户是否已经激活
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// 使用统一的数据库连接
require_once dirname(dirname(dirname(__DIR__))) . '/includes/database-connection.php';

try {
    // 获取请求数据
    $input = json_decode(file_get_contents('php://input'), true);
    
    // 验证必需字段
    if (!isset($input['email'])) {
        throw new Exception('Email is required');
    }
    
    $email = trim(strtolower($input['email']));
    
    // 验证邮箱格式
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        throw new Exception('Invalid email format');
    }
    
    // 查找用户
    $stmt = $pdo->prepare("SELECT id, email, username, first_name, last_name, status, email_verified, email_verification_token FROM pt_member WHERE email = ?");
    $stmt->execute([$email]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        echo json_encode([
            'success' => false,
            'message' => 'User not found'
        ]);
        exit;
    }
    
    echo json_encode([
        'success' => true,
        'message' => 'User status retrieved',
        'user' => [
            'id' => $user['id'],
            'email' => $user['email'],
            'username' => $user['username'],
            'name' => trim($user['first_name'] . ' ' . $user['last_name']),
            'status' => $user['status'],
            'email_verified' => (bool)$user['email_verified'],
            'has_verification_token' => !empty($user['email_verification_token'])
        ]
    ]);
    
} catch (Exception $e) {
    error_log("Check user status error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
