<?php
/**
 * 重新发送验证码API
 * 为未验证的用户重新生成验证码
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// 使用统一的数据库连接
require_once dirname(dirname(dirname(__DIR__))) . '/includes/database-connection.php';

try {
    // 获取请求数据
    $input = json_decode(file_get_contents('php://input'), true);
    
    // 验证必需字段
    if (!isset($input['email'])) {
        throw new Exception('Email is required');
    }
    
    $email = trim(strtolower($input['email']));
    
    // 验证邮箱格式
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        throw new Exception('Invalid email format');
    }
    
    // 查找用户
    $stmt = $pdo->prepare("SELECT * FROM pt_member WHERE email = ? AND status = 'pending' AND email_verified = 0");
    $stmt->execute([$email]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        throw new Exception('User not found or already verified');
    }
    
    // 检查是否在短时间内重复请求（防止滥用）
    $stmt = $pdo->prepare("
        SELECT COUNT(*) FROM pt_member_activity_log 
        WHERE user_id = ? AND action = 'resend_verification' 
        AND created_at > DATE_SUB(NOW(), INTERVAL 1 MINUTE)
    ");
    $stmt->execute([$user['id']]);
    $recentRequests = $stmt->fetchColumn();
    
    if ($recentRequests > 0) {
        throw new Exception('Please wait at least 1 minute before requesting a new verification code');
    }
    
    // 生成新的6位验证码
    $newVerificationCode = sprintf('%06d', mt_rand(100000, 999999));
    
    // 更新用户的验证码
    $stmt = $pdo->prepare("
        UPDATE pt_member 
        SET email_verification_token = ?, updated_at = NOW()
        WHERE id = ?
    ");
    $stmt->execute([$newVerificationCode, $user['id']]);
    
    // 记录重新发送验证码的活动
    try {
        $stmt = $pdo->prepare("
            INSERT INTO pt_member_activity_log (user_id, action, description, ip_address, user_agent, created_at)
            VALUES (?, 'resend_verification', 'Requested new verification code', ?, ?, NOW())
        ");
        $stmt->execute([
            $user['id'],
            $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        ]);
    } catch (Exception $e) {
        // 日志记录失败不影响主流程
        error_log("Failed to log resend verification: " . $e->getMessage());
    }
    
    echo json_encode([
        'success' => true,
        'message' => 'New verification code generated successfully',
        'data' => [
            'verification_code' => $newVerificationCode,
            'email' => $user['email']
        ]
    ]);
    
} catch (Exception $e) {
    error_log("Resend verification error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
