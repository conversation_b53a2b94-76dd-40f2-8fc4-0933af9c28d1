<?php
/**
 * 用户需求管理页面
 */

// 检查管理员权限
if (!isset($_SESSION['admin_id'])) {
    header('Location: /control-panel/auth/login.php');
    exit;
}

// 导出处理已移至 control-panel/index.php 中，在任何HTML输出之前执行
// AJAX处理已移至独立文件 ajax/requests-handler.php

$currentPage = 'user-requests';
$pageTitle = 'Tool Requests';

// 获取筛选参数
$status = $_GET['status'] ?? 'all';
$category = $_GET['category'] ?? 'all';
$replyStatus = $_GET['reply_status'] ?? 'all';
$sort = $_GET['sort'] ?? 'latest';
$search = $_GET['search'] ?? '';
$currentPage = max(1, intval($_GET['p'] ?? 1)); // 使用 'p' 避免与控制面板的 'page' 参数冲突
$perPage = 10;
$offset = ($currentPage - 1) * $perPage;

// 构建查询条件
$whereConditions = [];
$params = [];

if ($status !== 'all') {
    $whereConditions[] = "r.status = ?";
    $params[] = $status;
}

if ($category !== 'all') {
    $whereConditions[] = "r.category = ?";
    $params[] = $category;
}

if ($replyStatus !== 'all') {
    if ($replyStatus === 'replied') {
        $whereConditions[] = "r.admin_reply IS NOT NULL AND r.admin_reply != ''";
    } elseif ($replyStatus === 'pending') {
        $whereConditions[] = "(r.admin_reply IS NULL OR r.admin_reply = '')";
    }
}

if (!empty($search)) {
    $whereConditions[] = "(r.title LIKE ? OR r.description LIKE ? OR m.username LIKE ?)";
    $searchTerm = "%{$search}%";
    $params[] = $searchTerm;
    $params[] = $searchTerm;
    $params[] = $searchTerm;
}

$whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

// 排序逻辑
$orderBy = match($sort) {
    'votes' => 'r.votes DESC, r.created_at DESC',
    'oldest' => 'r.created_at ASC',
    'priority' => 'FIELD(r.priority, "high", "medium", "low"), r.created_at DESC',
    default => 'r.created_at DESC'
};

// 获取需求列表
$sql = "
    SELECT r.*, m.username, m.first_name, m.last_name, m.email,
           admin.username as admin_username
    FROM pt_user_requests r
    LEFT JOIN pt_member m ON r.user_id = m.id
    LEFT JOIN pt_manager admin ON r.admin_id = admin.id
    {$whereClause}
    ORDER BY
        CASE WHEN r.admin_reply IS NULL OR r.admin_reply = '' THEN 0 ELSE 1 END,
        {$orderBy}
    LIMIT {$perPage} OFFSET {$offset}
";

$requests = $pdo->prepare($sql);
$requests->execute($params);
$requestsList = $requests->fetchAll();

// 获取总数
$countSql = "SELECT COUNT(*) FROM pt_user_requests r LEFT JOIN pt_member m ON r.user_id = m.id {$whereClause}";
$countStmt = $pdo->prepare($countSql);
$countStmt->execute($params);
$totalRequests = $countStmt->fetchColumn();
$totalPages = ceil($totalRequests / $perPage);

// 获取统计数据
$stats = $pdo->query("
    SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
        SUM(CASE WHEN status = 'reviewing' THEN 1 ELSE 0 END) as reviewing,
        SUM(CASE WHEN status = 'accepted' THEN 1 ELSE 0 END) as accepted,
        SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed,
        SUM(CASE WHEN status = 'rejected' THEN 1 ELSE 0 END) as rejected
    FROM pt_user_requests
")->fetch();

// 从数据库获取工具分类
$categoryStmt = $pdo->query("SELECT slug, name FROM pt_tool_category WHERE status = 'active' ORDER BY sort_order ASC, name ASC");
$dbCategories = $categoryStmt->fetchAll();

// 构建分类数组
$categories = [];
foreach ($dbCategories as $cat) {
    $categories[$cat['slug']] = $cat['name'];
}

// 添加其他分类
$categories['other'] = 'Other';

// 分类标签统一样式
function getCategoryColor() {
    return 'bg-blue-100 text-blue-800';
}

// 状态颜色配置
$statusColors = [
    'pending' => 'bg-yellow-100 text-yellow-800',
    'reviewing' => 'bg-blue-100 text-blue-800',
    'accepted' => 'bg-green-100 text-green-800',
    'rejected' => 'bg-red-100 text-red-800',
    'completed' => 'bg-purple-100 text-purple-800'
];

// 优先级颜色配置
$priorityColors = [
    'high' => 'bg-red-100 text-red-800',
    'medium' => 'bg-yellow-100 text-yellow-800',
    'low' => 'bg-gray-100 text-gray-800'
];
?>

<div class="space-y-6">
    <!-- 导出错误提示 -->
    <?php if (isset($_SESSION['export_error'])): ?>
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
            <?= htmlspecialchars($_SESSION['export_error']) ?>
        </div>
        <?php unset($_SESSION['export_error']); ?>
    <?php endif; ?>

    <!-- 页面描述和操作 -->
    <div class="flex items-center justify-between">
        <div>
            <p class="text-gray-600">Manage user feature requests and provide responses</p>
        </div>
        <div class="flex space-x-3">
            <form method="POST" style="display: inline;">
                <input type="hidden" name="action" value="export_requests">
                <input type="hidden" name="search" value="<?= htmlspecialchars($search) ?>">
                <input type="hidden" name="status" value="<?= htmlspecialchars($status) ?>">
                <input type="hidden" name="category" value="<?= htmlspecialchars($category) ?>">
                <input type="hidden" name="sort" value="<?= htmlspecialchars($sort) ?>">
                <button type="submit" class="bg-green-600 text-white px-4 py-2 hover:bg-green-700 rounded">
                    Export Requests
                </button>
            </form>
        </div>
    </div>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-2 md:grid-cols-6 gap-4">
        <div class="bg-white p-4 rounded-lg shadow">
            <div class="text-2xl font-bold text-gray-900"><?= $stats['total'] ?></div>
            <div class="text-sm text-gray-600">Total Requests</div>
        </div>
        <div class="bg-white p-4 rounded-lg shadow">
            <div class="text-2xl font-bold text-yellow-600"><?= $stats['pending'] ?></div>
            <div class="text-sm text-gray-600">Pending</div>
        </div>
        <div class="bg-white p-4 rounded-lg shadow">
            <div class="text-2xl font-bold text-blue-600"><?= $stats['reviewing'] ?></div>
            <div class="text-sm text-gray-600">Reviewing</div>
        </div>
        <div class="bg-white p-4 rounded-lg shadow">
            <div class="text-2xl font-bold text-green-600"><?= $stats['accepted'] ?></div>
            <div class="text-sm text-gray-600">Accepted</div>
        </div>
        <div class="bg-white p-4 rounded-lg shadow">
            <div class="text-2xl font-bold text-purple-600"><?= $stats['completed'] ?></div>
            <div class="text-sm text-gray-600">Completed</div>
        </div>
        <div class="bg-white p-4 rounded-lg shadow">
            <div class="text-2xl font-bold text-red-600"><?= $stats['rejected'] ?></div>
            <div class="text-sm text-gray-600">Rejected</div>
        </div>
    </div>

    <!-- 筛选器 -->
    <div class="bg-white p-6 rounded-lg shadow">
        <form method="GET" class="grid grid-cols-1 md:grid-cols-6 gap-4">
            <input type="hidden" name="page" value="user-requests">
            
            <!-- 搜索 -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Search</label>
                <input type="text" name="search" value="<?= htmlspecialchars($search) ?>" 
                       placeholder="Title, description, or username..."
                       class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
            </div>
            
            <!-- 状态筛选 -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                <select name="status" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                    <option value="all" <?= $status === 'all' ? 'selected' : '' ?>>All Status</option>
                    <option value="pending" <?= $status === 'pending' ? 'selected' : '' ?>>Pending</option>
                    <option value="reviewing" <?= $status === 'reviewing' ? 'selected' : '' ?>>Reviewing</option>
                    <option value="accepted" <?= $status === 'accepted' ? 'selected' : '' ?>>Accepted</option>
                    <option value="completed" <?= $status === 'completed' ? 'selected' : '' ?>>Completed</option>
                    <option value="rejected" <?= $status === 'rejected' ? 'selected' : '' ?>>Rejected</option>
                </select>
            </div>
            
            <!-- 分类筛选 -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Category</label>
                <select name="category" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                    <option value="all" <?= $category === 'all' ? 'selected' : '' ?>>All Categories</option>
                    <?php foreach ($categories as $slug => $name): ?>
                        <option value="<?= $slug ?>" <?= $category === $slug ? 'selected' : '' ?>><?= htmlspecialchars($name) ?></option>
                    <?php endforeach; ?>
                </select>
            </div>

            <!-- 回复状态筛选 -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Reply Status</label>
                <select name="reply_status" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                    <option value="all" <?= $replyStatus === 'all' ? 'selected' : '' ?>>All Requests</option>
                    <option value="pending" <?= $replyStatus === 'pending' ? 'selected' : '' ?>>Pending Reply</option>
                    <option value="replied" <?= $replyStatus === 'replied' ? 'selected' : '' ?>>Already Replied</option>
                </select>
            </div>

            <!-- 排序 -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Sort By</label>
                <select name="sort" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                    <option value="latest" <?= $sort === 'latest' ? 'selected' : '' ?>>Latest</option>
                    <option value="oldest" <?= $sort === 'oldest' ? 'selected' : '' ?>>Oldest</option>
                    <option value="votes" <?= $sort === 'votes' ? 'selected' : '' ?>>Most Voted</option>
                    <option value="priority" <?= $sort === 'priority' ? 'selected' : '' ?>>Priority</option>
                </select>
            </div>
            
            <!-- 操作按钮 -->
            <div class="flex items-end space-x-2">
                <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 text-sm">
                    Filter
                </button>
                <a href="?page=user-requests" class="bg-gray-500 text-white px-4 py-2 rounded-md hover:bg-gray-600 text-sm">
                    Clear
                </a>
            </div>
        </form>
    </div>

    <!-- 需求列表 -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
        <?php if (empty($requestsList)): ?>
            <div class="p-8 text-center">
                <div class="text-gray-400 mb-4">
                    <svg class="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No requests found</h3>
                <p class="text-gray-600">No user requests match your current filters.</p>
            </div>
        <?php else: ?>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Request</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Priority</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Votes</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php foreach ($requestsList as $request): ?>
                            <tr class="hover:bg-gray-50 <?= !empty($request['admin_reply']) ? 'bg-green-50 border-l-4 border-green-400' : '' ?>">
                                <td class="px-6 py-4">
                                    <div class="max-w-xs">
                                        <div class="flex items-center space-x-2 mb-1">
                                            <div class="text-sm font-medium text-gray-900 truncate"><?= htmlspecialchars($request['title']) ?></div>
                                            <?php if (!empty($request['admin_reply'])): ?>
                                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                    <i class="fas fa-check-circle mr-1"></i>
                                                    Replied
                                                </span>
                                            <?php else: ?>
                                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                                    <i class="fas fa-clock mr-1"></i>
                                                    Pending
                                                </span>
                                            <?php endif; ?>
                                        </div>
                                        <div class="text-sm text-gray-500 truncate"><?= htmlspecialchars(substr($request['description'], 0, 100)) ?>...</div>
                                        <?php if (!empty($request['admin_reply'])): ?>
                                            <div class="text-xs text-blue-600 mt-1 p-2 bg-blue-50 rounded border-l-2 border-blue-200">
                                                <strong>Reply:</strong> <?= htmlspecialchars(substr($request['admin_reply'], 0, 60)) ?><?= strlen($request['admin_reply']) > 60 ? '...' : '' ?>
                                            </div>
                                        <?php endif; ?>
                                        <div class="text-xs mt-1">
                                            <span class="<?= getCategoryColor() ?> px-2 py-1 rounded-full text-xs font-medium">
                                                <?= htmlspecialchars($categories[$request['category']] ?? ucfirst($request['category'])) ?>
                                            </span>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900"><?= htmlspecialchars($request['first_name'] ?? $request['username']) ?></div>
                                    <div class="text-sm text-gray-500"><?= htmlspecialchars($request['email']) ?></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 py-1 text-xs font-medium rounded-full <?= $statusColors[$request['status']] ?>">
                                        <?= ucfirst($request['status']) ?>
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 py-1 text-xs font-medium rounded-full <?= $priorityColors[$request['priority']] ?>">
                                        <?= ucfirst($request['priority']) ?>
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <?= $request['votes'] ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <?= date('M j, Y', strtotime($request['created_at'])) ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex items-center space-x-2">
                                        <!-- Reply -->
                                        <button onclick="replyRequest(<?= $request['id'] ?>)"
                                                class="inline-flex items-center px-2 py-1 text-blue-600 hover:text-blue-900 hover:bg-blue-50 rounded transition-colors"
                                                title="Reply to Request">
                                            <i class="fas fa-reply text-sm"></i>
                                        </button>

                                        <!-- Edit Status -->
                                        <button onclick="editRequest(<?= $request['id'] ?>)"
                                                class="inline-flex items-center px-2 py-1 text-indigo-600 hover:text-indigo-900 hover:bg-indigo-50 rounded transition-colors"
                                                title="Edit Status">
                                            <i class="fas fa-edit text-sm"></i>
                                        </button>

                                        <!-- Delete -->
                                        <button onclick="deleteRequest(<?= $request['id'] ?>)"
                                                class="inline-flex items-center px-2 py-1 text-red-600 hover:text-red-900 hover:bg-red-50 rounded transition-colors"
                                                title="Delete Request">
                                            <i class="fas fa-trash text-sm"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>

    <!-- 分页 -->
    <?php if ($totalPages > 1): ?>
    <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
        <div class="flex items-center justify-between">
            <div class="flex-1 flex justify-between sm:hidden">
                <?php if ($currentPage > 1): ?>
                <a href="?page=user-requests&<?= http_build_query(array_merge($_GET, ['p' => $currentPage - 1])) ?>"
                   class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Previous
                </a>
                <?php endif; ?>
                <?php if ($currentPage < $totalPages): ?>
                <a href="?page=user-requests&<?= http_build_query(array_merge($_GET, ['p' => $currentPage + 1])) ?>"
                   class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Next
                </a>
                <?php endif; ?>
            </div>
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                    <p class="text-sm text-gray-700">
                        Showing <span class="font-medium"><?= $offset + 1 ?></span> to
                        <span class="font-medium"><?= min($offset + $perPage, $totalRequests) ?></span> of
                        <span class="font-medium"><?= $totalRequests ?></span> results
                    </p>
                </div>
                <div>
                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                        <?php if ($currentPage > 1): ?>
                        <a href="?page=user-requests&<?= http_build_query(array_merge($_GET, ['p' => $currentPage - 1])) ?>"
                           class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            <i class="fas fa-chevron-left"></i>
                        </a>
                        <?php endif; ?>

                        <?php for ($i = max(1, $currentPage - 2); $i <= min($totalPages, $currentPage + 2); $i++): ?>
                        <a href="?page=user-requests&<?= http_build_query(array_merge($_GET, ['p' => $i])) ?>"
                           class="relative inline-flex items-center px-4 py-2 border text-sm font-medium
                                  <?= $i === $currentPage ? 'z-10 bg-blue-50 border-blue-500 text-blue-600' : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50' ?>">
                            <?= $i ?>
                        </a>
                        <?php endfor; ?>

                        <?php if ($currentPage < $totalPages): ?>
                        <a href="?page=user-requests&<?= http_build_query(array_merge($_GET, ['p' => $currentPage + 1])) ?>"
                           class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            <i class="fas fa-chevron-right"></i>
                        </a>
                        <?php endif; ?>
                    </nav>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<!-- 模态框和JavaScript将在下一部分添加 -->
<script>
// 分类数据（从PHP传递到JavaScript）
const categories = <?= json_encode($categories) ?>;

// 生成分类选项HTML
function getCategoryOptions(selectedCategory) {
    let options = '';
    for (const [slug, name] of Object.entries(categories)) {
        const selected = slug === selectedCategory ? 'selected' : '';
        options += `<option value="${slug}" ${selected}>${name}</option>`;
    }
    return options;
}



// Toast 提示函数
function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `fixed top-4 right-4 z-50 px-4 py-3 rounded-md shadow-lg transition-all duration-300 ${
        type === 'success' ? 'bg-green-100 text-green-800 border border-green-200' :
        type === 'error' ? 'bg-red-100 text-red-800 border border-red-200' :
        type === 'warning' ? 'bg-yellow-100 text-yellow-800 border border-yellow-200' :
        'bg-blue-100 text-blue-800 border border-blue-200'
    }`;
    toast.textContent = message;

    document.body.appendChild(toast);

    // 自动移除
    setTimeout(() => {
        toast.style.opacity = '0';
        setTimeout(() => toast.remove(), 300);
    }, 3000);
}



// 回复需求
function replyRequest(requestId) {
    // 获取当前需求信息
    fetch('/control-panel/ajax/requests-handler.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `action=get_request&id=${requestId}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showReplyModal(data.request);
        } else {
            showToast('Failed to load request details: ' + data.message, 'error');
        }
    })
    .catch(error => {
        showToast('Network error: ' + error.message, 'error');
    });
}

// 显示回复模态框
function showReplyModal(request) {
    const modal = `
    <div id="replyModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-10 mx-auto p-6 border w-full max-w-2xl shadow-lg rounded-lg bg-white">
            <div class="mb-4">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">Reply to Request</h3>
                    <button onclick="closeReplyModal()" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>

            <div class="mb-4 p-4 bg-gray-50 rounded">
                <h4 class="font-medium text-gray-900">${request.title}</h4>
                <p class="text-sm text-gray-600 mt-1">${request.description}</p>
                <div class="flex items-center space-x-2 mt-2">
                    <span class="px-2 py-1 text-xs bg-gray-200 text-gray-700 rounded">${request.category}</span>
                    <span class="px-2 py-1 text-xs bg-gray-200 text-gray-700 rounded">${request.status}</span>
                </div>
            </div>

            <form id="replyForm">
                <input type="hidden" name="action" value="reply_request">
                <input type="hidden" name="request_id" value="${request.id}">

                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                    <select name="status" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="pending" ${request.status === 'pending' ? 'selected' : ''}>Pending</option>
                        <option value="reviewing" ${request.status === 'reviewing' ? 'selected' : ''}>Reviewing</option>
                        <option value="accepted" ${request.status === 'accepted' ? 'selected' : ''}>Accepted</option>
                        <option value="rejected" ${request.status === 'rejected' ? 'selected' : ''}>Rejected</option>
                        <option value="completed" ${request.status === 'completed' ? 'selected' : ''}>Completed</option>
                    </select>
                </div>

                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Priority</label>
                    <select name="priority" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="low" ${request.priority === 'low' ? 'selected' : ''}>Low</option>
                        <option value="medium" ${request.priority === 'medium' ? 'selected' : ''}>Medium</option>
                        <option value="high" ${request.priority === 'high' ? 'selected' : ''}>High</option>
                    </select>
                </div>

                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Admin Reply</label>
                    <textarea name="admin_reply" rows="4" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                              placeholder="Enter your response to the user...">${request.admin_reply || ''}</textarea>
                </div>

                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="closeReplyModal()" class="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300">
                        Cancel
                    </button>
                    <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                        Update Request
                    </button>
                </div>
            </form>
        </div>
    </div>`;

    document.body.insertAdjacentHTML('beforeend', modal);

    // 绑定表单提交事件
    document.getElementById('replyForm').addEventListener('submit', function(e) {
        e.preventDefault();
        updateRequest();
    });
}

// 关闭回复模态框
function closeReplyModal() {
    const modal = document.getElementById('replyModal');
    if (modal) {
        modal.remove();
    }
}

// 显示编辑模态框
function showEditModal(request) {
    const modal = `
    <div id="editModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-10 mx-auto p-6 border w-full max-w-2xl max-h-[80vh] overflow-y-auto shadow-lg rounded-lg bg-white">
            <div class="mb-4">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">Edit Request</h3>
                    <button onclick="closeEditModal()" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>

            <form id="editForm">
                <input type="hidden" name="action" value="edit_request">
                <input type="hidden" name="request_id" value="${request.id}">

                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Title</label>
                    <input type="text" name="title" value="${request.title}"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                           required>
                </div>

                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                    <textarea name="description" rows="4"
                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                              required>${request.description}</textarea>
                </div>

                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Category</label>
                    <select name="category" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        ${getCategoryOptions(request.category)}
                    </select>
                </div>

                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Priority</label>
                    <select name="priority" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="low" ${request.priority === 'low' ? 'selected' : ''}>Low</option>
                        <option value="medium" ${request.priority === 'medium' ? 'selected' : ''}>Medium</option>
                        <option value="high" ${request.priority === 'high' ? 'selected' : ''}>High</option>
                    </select>
                </div>

                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                    <select name="status" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="pending" ${request.status === 'pending' ? 'selected' : ''}>Pending</option>
                        <option value="reviewing" ${request.status === 'reviewing' ? 'selected' : ''}>Reviewing</option>
                        <option value="accepted" ${request.status === 'accepted' ? 'selected' : ''}>Accepted</option>
                        <option value="rejected" ${request.status === 'rejected' ? 'selected' : ''}>Rejected</option>
                        <option value="completed" ${request.status === 'completed' ? 'selected' : ''}>Completed</option>
                    </select>
                </div>

                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="closeEditModal()" class="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300">
                        Cancel
                    </button>
                    <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                        Save Changes
                    </button>
                </div>
            </form>
        </div>
    </div>`;

    document.body.insertAdjacentHTML('beforeend', modal);

    // 绑定表单提交事件
    document.getElementById('editForm').addEventListener('submit', function(e) {
        e.preventDefault();
        updateRequestInfo();
    });
}

// 关闭编辑模态框
function closeEditModal() {
    const modal = document.getElementById('editModal');
    if (modal) {
        modal.remove();
    }
}

// 更新需求信息
function updateRequestInfo() {
    const form = document.getElementById('editForm');
    const formData = new FormData(form);
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;

    submitBtn.textContent = 'Saving...';
    submitBtn.disabled = true;

    fetch('/control-panel/ajax/requests-handler.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('Request updated successfully', 'success');
            closeEditModal();
            setTimeout(() => location.reload(), 1000);
        } else {
            showToast(data.message || 'Failed to update request', 'error');
        }
    })
    .catch(error => {
        showToast('Network error occurred', 'error');
    })
    .finally(() => {
        submitBtn.textContent = originalText;
        submitBtn.disabled = false;
    });
}

// 更新需求
function updateRequest() {
    const form = document.getElementById('replyForm');
    const formData = new FormData(form);
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;

    submitBtn.textContent = 'Updating...';
    submitBtn.disabled = true;

    fetch('/control-panel/ajax/requests-handler.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('Request updated successfully', 'success');
            closeReplyModal();
            setTimeout(() => location.reload(), 1000);
        } else {
            showToast(data.message || 'Failed to update request', 'error');
        }
    })
    .catch(error => {
        showToast('Network error occurred', 'error');
    })
    .finally(() => {
        submitBtn.textContent = originalText;
        submitBtn.disabled = false;
    });
}

// 编辑需求
function editRequest(requestId) {
    // 获取需求信息用于编辑
    fetch('/control-panel/ajax/requests-handler.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `action=get_request&id=${requestId}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showEditModal(data.request);
        } else {
            showToast('Failed to load request: ' + data.message, 'error');
        }
    })
    .catch(error => {
        showToast('Network error: ' + error.message, 'error');
    });
}

// 删除需求
function deleteRequest(requestId) {
    const modal = `
    <div id="deleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-20 mx-auto p-6 border w-full max-w-md shadow-lg rounded-lg bg-white">
            <div class="text-center">
                <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
                    <i class="fas fa-exclamation-triangle text-red-600"></i>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">Delete Request</h3>
                <p class="text-sm text-gray-500 mb-6">Are you sure you want to delete this request? This action cannot be undone.</p>

                <div class="flex justify-center space-x-3">
                    <button onclick="closeDeleteModal()" class="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300">
                        Cancel
                    </button>
                    <button onclick="confirmDelete(${requestId})" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700">
                        Delete
                    </button>
                </div>
            </div>
        </div>
    </div>`;

    document.body.insertAdjacentHTML('beforeend', modal);
}

// 关闭删除模态框
function closeDeleteModal() {
    const modal = document.getElementById('deleteModal');
    if (modal) {
        modal.remove();
    }
}

// 确认删除
function confirmDelete(requestId) {
    const deleteBtn = document.querySelector('#deleteModal button[onclick*="confirmDelete"]');
    deleteBtn.textContent = 'Deleting...';
    deleteBtn.disabled = true;

    fetch('/control-panel/ajax/requests-handler.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `action=delete_request&id=${requestId}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('Request deleted successfully', 'success');
            closeDeleteModal();
            setTimeout(() => location.reload(), 1000);
        } else {
            showToast(data.message || 'Failed to delete request', 'error');
        }
    })
    .catch(error => {
        showToast('Network error occurred', 'error');
    })
    .finally(() => {
        deleteBtn.textContent = 'Delete';
        deleteBtn.disabled = false;
    });
}
</script>
