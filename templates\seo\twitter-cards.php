<?php
/**
 * Twitter Cards标签模板
 * 为Twitter分享优化
 */

/**
 * 生成Twitter Cards标签
 */
function generateTwitterCardTags($data) {
    $defaults = [
        'card' => 'summary_large_image',
        'site' => '@Prompt2Tool',
        'creator' => '@Prompt2Tool',
        'title' => 'Prompt2Tool - Free AI-Powered Online Tools',
        'description' => 'Discover 100+ free online tools powered by AI. Perfect for developers, designers, and digital marketers.',
        'image' => '/assets/images/twitter-card-default.jpg'
    ];
    
    $twitter = array_merge($defaults, $data);
    
    // 确保图片URL是完整的
    if (!preg_match('/^https?:\/\//', $twitter['image'])) {
        $twitter['image'] = getFullURL($twitter['image']);
    }
    
    return $twitter;
}

/**
 * 为工具页面生成Twitter Cards数据
 */
function generateToolTwitterCard($tool) {
    return [
        'card' => 'summary_large_image',
        'title' => $tool['name'] . ' - Free Online Tool',
        'description' => $tool['description'] . ' Use this free tool on Prompt2Tool.',
        'image' => $tool['image'] ?? '/assets/images/tools/' . $tool['slug'] . '-twitter.jpg'
    ];
}

/**
 * 为分类页面生成Twitter Cards数据
 */
function generateCategoryTwitterCard($category) {
    return [
        'card' => 'summary_large_image',
        'title' => $category['name'] . ' Tools - Prompt2Tool',
        'description' => 'Explore ' . strtolower($category['name']) . ' tools on Prompt2Tool. ' . $category['description'],
        'image' => '/assets/images/categories/' . $category['slug'] . '-twitter.jpg'
    ];
}

/**
 * 输出Twitter Cards标签
 */
function outputTwitterCardTags($data) {
    $twitter = generateTwitterCardTags($data);
    
    echo '<!-- Twitter Cards标签 -->' . "\n";
    echo '<meta name="twitter:card" content="' . htmlspecialchars($twitter['card']) . '">' . "\n";
    echo '<meta name="twitter:site" content="' . htmlspecialchars($twitter['site']) . '">' . "\n";
    echo '<meta name="twitter:creator" content="' . htmlspecialchars($twitter['creator']) . '">' . "\n";
    echo '<meta name="twitter:title" content="' . htmlspecialchars($twitter['title']) . '">' . "\n";
    echo '<meta name="twitter:description" content="' . htmlspecialchars($twitter['description']) . '">' . "\n";
    echo '<meta name="twitter:image" content="' . htmlspecialchars($twitter['image']) . '">' . "\n";
    
    // 图片alt文本
    if (isset($twitter['image:alt'])) {
        echo '<meta name="twitter:image:alt" content="' . htmlspecialchars($twitter['image:alt']) . '">' . "\n";
    }
    
    // App Cards特定标签
    if ($twitter['card'] === 'app') {
        if (isset($twitter['app:name:iphone'])) {
            echo '<meta name="twitter:app:name:iphone" content="' . htmlspecialchars($twitter['app:name:iphone']) . '">' . "\n";
        }
        if (isset($twitter['app:id:iphone'])) {
            echo '<meta name="twitter:app:id:iphone" content="' . htmlspecialchars($twitter['app:id:iphone']) . '">' . "\n";
        }
        if (isset($twitter['app:url:iphone'])) {
            echo '<meta name="twitter:app:url:iphone" content="' . htmlspecialchars($twitter['app:url:iphone']) . '">' . "\n";
        }
    }
    
    // Player Cards特定标签
    if ($twitter['card'] === 'player') {
        if (isset($twitter['player'])) {
            echo '<meta name="twitter:player" content="' . htmlspecialchars($twitter['player']) . '">' . "\n";
        }
        if (isset($twitter['player:width'])) {
            echo '<meta name="twitter:player:width" content="' . htmlspecialchars($twitter['player:width']) . '">' . "\n";
        }
        if (isset($twitter['player:height'])) {
            echo '<meta name="twitter:player:height" content="' . htmlspecialchars($twitter['player:height']) . '">' . "\n";
        }
    }
}

/**
 * 生成特定页面的Twitter Cards数据
 */
function getPageTwitterCardData($page, $data = []) {
    switch ($page) {
        case 'home':
            return [
                'card' => 'summary_large_image',
                'title' => 'Prompt2Tool - Free AI-Powered Online Tools',
                'description' => 'Discover 100+ free online tools powered by AI. Boost your productivity with Prompt2Tool.',
                'image' => '/assets/images/twitter-home.jpg',
                'image:alt' => 'Prompt2Tool - Free Online Tools Platform'
            ];
            
        case 'tools':
            return [
                'card' => 'summary_large_image',
                'title' => 'All Tools - Prompt2Tool',
                'description' => 'Browse all free online tools. Find the perfect tool for your needs.',
                'image' => '/assets/images/twitter-tools.jpg',
                'image:alt' => 'Prompt2Tool Tools Collection'
            ];
            
        case 'about':
            return [
                'card' => 'summary',
                'title' => 'About Prompt2Tool',
                'description' => 'Learn about our mission to provide free AI-powered online tools.',
                'image' => '/assets/images/twitter-about.jpg',
                'image:alt' => 'About Prompt2Tool'
            ];
            
        case 'contact':
            return [
                'card' => 'summary',
                'title' => 'Contact Prompt2Tool',
                'description' => 'Get in touch with our team. We\'d love to hear from you.',
                'image' => '/assets/images/twitter-contact.jpg',
                'image:alt' => 'Contact Prompt2Tool'
            ];
            
        case 'tool':
            return generateToolTwitterCard($data);
            
        case 'category':
            return generateCategoryTwitterCard($data);
            
        default:
            return [];
    }
}

/**
 * 验证Twitter Cards图片
 */
function validateTwitterCardImage($imagePath) {
    // 检查图片是否存在
    if (!file_exists(PUBLIC_PATH . $imagePath)) {
        return '/assets/images/twitter-card-default.jpg';
    }
    
    // 检查图片尺寸
    $imageInfo = getimagesize(PUBLIC_PATH . $imagePath);
    if ($imageInfo) {
        $width = $imageInfo[0];
        $height = $imageInfo[1];
        
        // Summary Card: 最小 144x144, 最大 4096x4096
        // Summary Large Image: 最小 300x157, 最大 4096x4096
        if ($width < 144 || $height < 144) {
            return '/assets/images/twitter-card-default.jpg';
        }
    }
    
    return $imagePath;
}

/**
 * 生成动态Twitter Cards图片URL
 */
function generateDynamicTwitterImage($title, $description = '', $category = '') {
    $params = [
        'title' => urlencode($title),
        'description' => urlencode($description),
        'category' => urlencode($category),
        'type' => 'twitter'
    ];
    
    return '/api/social-image?' . http_build_query($params);
}

/**
 * 获取Twitter Cards类型建议
 */
function getRecommendedCardType($contentType) {
    switch ($contentType) {
        case 'tool':
        case 'category':
        case 'home':
            return 'summary_large_image';
            
        case 'article':
        case 'blog':
            return 'summary_large_image';
            
        case 'video':
            return 'player';
            
        case 'app':
            return 'app';
            
        default:
            return 'summary';
    }
}
?>
