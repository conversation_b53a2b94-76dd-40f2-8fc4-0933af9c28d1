<?php
/**
 * 角色管理页面
 * 管理系统中的用户角色和权限
 */

// 定义根目录
define('ROOT_PATH', dirname(dirname(dirname(__DIR__))));

// 加载依赖
require_once dirname(__DIR__) . '/../auth/middleware.php';
require_once dirname(__DIR__) . '/../middleware/PermissionMiddleware.php';
require_once dirname(__DIR__) . '/../classes/RoleManager.php';

// 权限检查
requirePermission('admins.view');

// 初始化管理器
$roleManager = new RoleManager();
$permissionManager = new PermissionManager();

// 处理表单提交
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $action = $_POST['action'] ?? '';
        $currentUser = getCurrentAdmin();
        
        switch ($action) {
            case 'create_role':
                requirePermission('admins.create');
                
                $roleData = [
                    'slug' => $_POST['role_slug'] ?? '',
                    'name' => $_POST['role_name'] ?? '',
                    'description' => $_POST['role_description'] ?? '',
                    'level' => intval($_POST['role_level'] ?? 0),
                    'permissions' => $_POST['permissions'] ?? [],
                    'color' => $_POST['role_color'] ?? '#858796',
                    'icon' => $_POST['role_icon'] ?? 'fas fa-user'
                ];
                
                $result = $roleManager->createRole($roleData, $currentUser);
                $message = $result['message'];
                $messageType = 'success';
                break;
                
            case 'update_role':
                requirePermission('admins.edit');
                
                $roleSlug = $_POST['role_slug'] ?? '';
                $roleData = [
                    'name' => $_POST['role_name'] ?? '',
                    'description' => $_POST['role_description'] ?? '',
                    'level' => intval($_POST['role_level'] ?? 0),
                    'permissions' => $_POST['permissions'] ?? [],
                    'color' => $_POST['role_color'] ?? '#858796',
                    'icon' => $_POST['role_icon'] ?? 'fas fa-user'
                ];
                
                $result = $roleManager->updateRole($roleSlug, $roleData, $currentUser);
                $message = $result['message'];
                $messageType = 'success';
                break;
                
            case 'delete_role':
                requirePermission('admins.delete');
                
                $roleSlug = $_POST['role_slug'] ?? '';
                $result = $roleManager->deleteRole($roleSlug, $currentUser);
                $message = $result['message'];
                $messageType = 'success';
                break;
        }
    } catch (Exception $e) {
        $message = $e->getMessage();
        $messageType = 'error';
    }
}

// 获取数据
$allRoles = $roleManager->getAllRoles();
$allPermissions = $permissionManager->getAllPermissions();
$permissionGroups = $permissionManager->getPermissionGroups();
$roleStatistics = $roleManager->getRoleStatistics();
$currentUser = getCurrentAdmin();

$currentPage = 'permissions';
include_once dirname(__DIR__) . '/../components/layout/header.php';
?>

<div class="min-h-screen bg-gray-100">
    <!-- 侧边栏 -->
    <div class="fixed inset-y-0 left-0 w-64 bg-gray-900">
        <?php include_once dirname(__DIR__) . '/../components/layout/sidebar.php'; ?>
    </div>
    
    <!-- 主内容区 -->
    <div class="ml-64">
        <!-- 顶部导航栏 -->
        <div class="bg-white shadow-sm border-b border-gray-200">
            <div class="px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-16">
                    <div class="flex items-center">
                        <h1 class="text-2xl font-semibold text-gray-900">Role Management</h1>
                    </div>
                    
                    <!-- 权限导航 -->
                    <div class="flex items-center space-x-4">
                        <nav class="flex space-x-4">
                            <a href="roles.php" class="text-accent border-b-2 border-accent px-3 py-2 text-sm font-medium">
                                Roles
                            </a>
                            <a href="permissions.php" class="text-gray-500 hover:text-gray-700 px-3 py-2 text-sm font-medium">
                                Permissions
                            </a>
                            <a href="audit.php" class="text-gray-500 hover:text-gray-700 px-3 py-2 text-sm font-medium">
                                Audit Log
                            </a>
                        </nav>
                        
                        <?php if (hasPermission('admins.create')): ?>
                        <button onclick="openCreateRoleModal()" class="bg-accent text-white px-4 py-2 hover:bg-blue-600 transition-colors">
                            <i class="fas fa-plus mr-2"></i>Create Role
                        </button>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 页面内容 -->
        <div class="p-6">
            <!-- 消息提示 -->
            <?php if ($message): ?>
            <div class="mb-6 p-4 border-l-4 <?= $messageType === 'success' ? 'bg-green-50 border-green-400 text-green-700' : 'bg-red-50 border-red-400 text-red-700' ?>">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <i class="fas fa-<?= $messageType === 'success' ? 'check-circle' : 'exclamation-circle' ?>"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm"><?= htmlspecialchars($message) ?></p>
                    </div>
                </div>
            </div>
            <?php endif; ?>
            
            <!-- 统计卡片 -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                <div class="bg-white border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="p-3 bg-blue-100 text-blue-600 mr-4">
                            <i class="fas fa-users-cog text-2xl"></i>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-600">Total Roles</p>
                            <p class="text-2xl font-bold text-gray-900"><?= $roleStatistics['total_roles'] ?></p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="p-3 bg-green-100 text-green-600 mr-4">
                            <i class="fas fa-shield-alt text-2xl"></i>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-600">System Roles</p>
                            <p class="text-2xl font-bold text-gray-900"><?= $roleStatistics['system_roles'] ?></p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="p-3 bg-yellow-100 text-yellow-600 mr-4">
                            <i class="fas fa-user-plus text-2xl"></i>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-600">Custom Roles</p>
                            <p class="text-2xl font-bold text-gray-900"><?= $roleStatistics['custom_roles'] ?></p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="p-3 bg-purple-100 text-purple-600 mr-4">
                            <i class="fas fa-check-circle text-2xl"></i>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-600">Active Roles</p>
                            <p class="text-2xl font-bold text-gray-900"><?= $roleStatistics['active_roles'] ?></p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 角色列表 -->
            <div class="bg-white border border-gray-200">
                <!-- 表格头部 -->
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex justify-between items-center">
                        <h3 class="text-lg font-semibold text-gray-900">System Roles</h3>
                        <div class="text-sm text-gray-500">
                            Manage user roles and their permissions
                        </div>
                    </div>
                </div>
                
                <!-- 表格内容 -->
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Level</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Users</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Permissions</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php foreach ($allRoles as $slug => $role): ?>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="w-10 h-10 flex items-center justify-center mr-3" style="background-color: <?= $role['color'] ?>; color: white;">
                                            <i class="<?= $role['icon'] ?>"></i>
                                        </div>
                                        <div>
                                            <div class="text-sm font-medium text-gray-900"><?= htmlspecialchars($role['name']) ?></div>
                                            <div class="text-sm text-gray-500"><?= htmlspecialchars($role['description']) ?></div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 text-xs font-medium bg-gray-100 text-gray-800">
                                        Level <?= $role['level'] ?>
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <?= $roleStatistics['users_by_role'][$slug]['count'] ?? 0 ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <?= count($role['permissions']) ?> permissions
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <?php if ($role['is_system']): ?>
                                        <span class="inline-flex items-center px-2.5 py-0.5 text-xs font-medium bg-blue-100 text-blue-800">
                                            System
                                        </span>
                                    <?php else: ?>
                                        <span class="inline-flex items-center px-2.5 py-0.5 text-xs font-medium bg-green-100 text-green-800">
                                            Custom
                                        </span>
                                    <?php endif; ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex space-x-2">
                                        <button onclick="viewRoleDetails('<?= $slug ?>')" 
                                                class="text-blue-600 hover:text-blue-900"
                                                title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        
                                        <?php if (hasPermission('admins.edit') && !$role['is_system']): ?>
                                        <button onclick="editRole('<?= $slug ?>')" 
                                                class="text-indigo-600 hover:text-indigo-900"
                                                title="Edit Role">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <?php endif; ?>
                                        
                                        <?php if (hasPermission('admins.delete') && !$role['is_system'] && ($roleStatistics['users_by_role'][$slug]['count'] ?? 0) === 0): ?>
                                        <button onclick="deleteRole('<?= $slug ?>')" 
                                                class="text-red-600 hover:text-red-900"
                                                title="Delete Role">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 创建角色模态框 -->
<div id="createRoleModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white max-w-2xl w-full max-h-screen overflow-y-auto">
            <div class="p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">Create New Role</h3>
                    <button onclick="closeCreateRoleModal()" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <form method="POST" id="createRoleForm">
                    <input type="hidden" name="action" value="create_role">
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Role Slug</label>
                            <input type="text" name="role_slug" required 
                                   class="w-full px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent"
                                   placeholder="e.g., content_manager">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Role Name</label>
                            <input type="text" name="role_name" required 
                                   class="w-full px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent"
                                   placeholder="e.g., Content Manager">
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                        <textarea name="role_description" rows="3" required
                                  class="w-full px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent"
                                  placeholder="Describe the role's responsibilities..."></textarea>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Permission Level</label>
                            <select name="role_level" required 
                                    class="w-full px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent">
                                <?php for ($i = 1; $i < $permissionManager->getRoleLevel($currentUser['role']); $i++): ?>
                                <option value="<?= $i ?>">Level <?= $i ?></option>
                                <?php endfor; ?>
                            </select>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Color</label>
                            <input type="color" name="role_color" value="#858796"
                                   class="w-full h-10 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Icon</label>
                            <input type="text" name="role_icon" value="fas fa-user"
                                   class="w-full px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent"
                                   placeholder="fas fa-user">
                        </div>
                    </div>
                    
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Permissions</label>
                        <div class="max-h-64 overflow-y-auto border border-gray-300 p-4">
                            <?php foreach ($permissionGroups as $groupSlug => $group): ?>
                            <div class="mb-4">
                                <h4 class="font-medium text-gray-900 mb-2"><?= htmlspecialchars($group['name']) ?></h4>
                                <div class="grid grid-cols-2 gap-2">
                                    <?php foreach ($group['permissions'] as $permission): ?>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="permissions[]" value="<?= $permission ?>"
                                               class="mr-2 text-accent focus:ring-accent">
                                        <span class="text-sm text-gray-700"><?= htmlspecialchars($permissionManager->getPermissionDescription($permission)) ?></span>
                                    </label>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    
                    <div class="flex justify-end space-x-3">
                        <button type="button" onclick="closeCreateRoleModal()" 
                                class="px-4 py-2 border border-gray-300 text-gray-700 hover:bg-gray-50">
                            Cancel
                        </button>
                        <button type="submit" 
                                class="px-4 py-2 bg-accent text-white hover:bg-blue-600">
                            Create Role
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// 打开创建角色模态框
function openCreateRoleModal() {
    document.getElementById('createRoleModal').classList.remove('hidden');
}

// 关闭创建角色模态框
function closeCreateRoleModal() {
    document.getElementById('createRoleModal').classList.add('hidden');
    document.getElementById('createRoleForm').reset();
}

// 查看角色详情
function viewRoleDetails(roleSlug) {
    // 实现角色详情查看功能
    alert('View role details functionality will be implemented.');
}

// 编辑角色
function editRole(roleSlug) {
    // 实现角色编辑功能
    alert('Edit role functionality will be implemented.');
}

// 删除角色
function deleteRole(roleSlug) {
    if (confirm('Are you sure you want to delete this role? This action cannot be undone.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.innerHTML = `
            <input type="hidden" name="action" value="delete_role">
            <input type="hidden" name="role_slug" value="${roleSlug}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

// 关闭模态框（点击外部）
document.getElementById('createRoleModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeCreateRoleModal();
    }
});
</script>

<?php include_once dirname(__DIR__) . '/../components/layout/footer.php'; ?>
