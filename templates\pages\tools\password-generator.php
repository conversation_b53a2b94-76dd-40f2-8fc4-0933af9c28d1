<?php
/**
 * Password Generator Tool Page
 * 遵循01-UI设计规范.md - 零圆角设计
 */

$currentPage = 'tool-password-generator';
$breadcrumbs = [
    ['name' => 'Home', 'url' => '/'],
    ['name' => 'Tools', 'url' => '/tools'],
    ['name' => 'Security', 'url' => '/tools/security'],
    ['name' => 'Password Generator']
];

// 包含动态SEO助手
require_once ROOT_PATH . '/app/helpers/dynamic-seo.php';

// 生成SEO数据 - 优化长度
$seoData = [
    'title' => 'Free Password Generator Online - Prompt2Tool',
    'description' => 'Generate strong, secure passwords with customizable length and character sets. Create random passwords for better online security.',
    'keywords' => 'password generator, strong password, secure password, random password, password maker, online password generator, free password generator',
    'og_title' => 'Free Password Generator Online - Prompt2Tool',
    'og_description' => 'Generate strong, secure passwords with customizable options. Free, fast, and secure password creation.',
    'canonical' => getCurrentURL()
];

// 包含公共头部
include_once ROOT_PATH . '/templates/layout/header.php';
?>

<!-- 工具页面主体 -->
<div class="bg-gray-900 text-white">

    <!-- Hero Section with SEO optimized content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-12">
        <div class="text-center mb-12">
            <div class="flex items-center justify-center mb-4">
                <div class="bg-red-600 p-3 mr-4">
                    <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                    </svg>
                </div>
                <div class="text-left">
                    <h1 class="text-4xl font-bold text-white mb-2">Password Generator</h1>
                    <p class="text-xl text-gray-400">Generate strong and secure passwords</p>
                </div>
            </div>
        </div>

        <!-- 工具主体 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- 输入区域 -->
            <div class="bg-gray-800 border border-gray-700 p-6">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-xl font-semibold text-white">Password Settings</h2>
                    <div class="flex space-x-2">
                        <button id="generatePassword" class="px-3 py-1 bg-blue-600 text-white text-sm hover:bg-blue-700 transition-colors">
                            Generate
                        </button>
                    </div>
                </div>
                
                <!-- 密码长度 -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-300 mb-2">Password Length: <span id="lengthValue">12</span></label>
                    <input type="range" id="passwordLength" min="4" max="128" value="12" class="w-full">
                    <div class="flex justify-between text-xs text-gray-500 mt-1">
                        <span>4</span>
                        <span>128</span>
                    </div>
                </div>
                
                <!-- 字符类型选择 -->
                <div class="space-y-4">
                    <h3 class="text-lg font-semibold text-white">Character Types</h3>
                    
                    <div class="space-y-3">
                        <label class="flex items-center">
                            <input type="checkbox" id="includeUppercase" checked class="mr-3 w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 focus:ring-blue-500">
                            <span class="text-gray-300">Uppercase Letters (A-Z)</span>
                        </label>
                        
                        <label class="flex items-center">
                            <input type="checkbox" id="includeLowercase" checked class="mr-3 w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 focus:ring-blue-500">
                            <span class="text-gray-300">Lowercase Letters (a-z)</span>
                        </label>
                        
                        <label class="flex items-center">
                            <input type="checkbox" id="includeNumbers" checked class="mr-3 w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 focus:ring-blue-500">
                            <span class="text-gray-300">Numbers (0-9)</span>
                        </label>
                        
                        <label class="flex items-center">
                            <input type="checkbox" id="includeSymbols" class="mr-3 w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 focus:ring-blue-500">
                            <span class="text-gray-300">Symbols (!@#$%^&*)</span>
                        </label>
                    </div>
                </div>
                
                <!-- 高级选项 -->
                <div class="mt-6 space-y-3">
                    <h3 class="text-lg font-semibold text-white">Advanced Options</h3>
                    
                    <label class="flex items-center">
                        <input type="checkbox" id="excludeSimilar" class="mr-3 w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 focus:ring-blue-500">
                        <span class="text-gray-300">Exclude Similar Characters (0, O, l, I)</span>
                    </label>
                    
                    <label class="flex items-center">
                        <input type="checkbox" id="excludeAmbiguous" class="mr-3 w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 focus:ring-blue-500">
                        <span class="text-gray-300">Exclude Ambiguous Characters ({ } [ ] ( ) / \ ' " ~ , ; . < >)</span>
                    </label>
                </div>
                
                <!-- 批量生成 -->
                <div class="mt-6">
                    <label class="block text-sm font-medium text-gray-300 mb-2">Generate Multiple: <span id="countValue">1</span></label>
                    <input type="range" id="passwordCount" min="1" max="10" value="1" class="w-full">
                    <div class="flex justify-between text-xs text-gray-500 mt-1">
                        <span>1</span>
                        <span>10</span>
                    </div>
                </div>
            </div>

            <!-- 输出区域 -->
            <div class="bg-gray-800 border border-gray-700 p-6">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-xl font-semibold text-white">Generated Passwords</h2>
                    <div class="flex space-x-2">
                        <div class="relative">
                            <button id="copyAllPasswords" class="px-3 py-1 bg-green-600 text-white text-sm hover:bg-green-700 transition-colors">
                                Copy All
                            </button>
                            <div id="copyTooltip" class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-700 text-white text-xs rounded pointer-events-none whitespace-nowrap z-50" style="opacity: 0; visibility: hidden; transition: opacity 0.2s, visibility 0.2s;">
                                Copy all passwords
                            </div>
                        </div>
                        <div class="relative">
                            <button id="exportPasswords" class="px-3 py-1 bg-purple-600 text-white text-sm hover:bg-purple-700 transition-colors">
                                Export
                            </button>
                            <div id="exportTooltip" class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-700 text-white text-xs rounded pointer-events-none whitespace-nowrap z-50" style="opacity: 0; visibility: hidden; transition: opacity 0.2s, visibility 0.2s;">
                                Export as text file
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 密码列表 -->
                <div id="passwordList" class="space-y-3 mb-6">
                    <div class="bg-gray-700 p-3 border border-gray-600 text-center text-gray-400">
                        Click "Generate" to create passwords
                    </div>
                </div>
                
                <!-- 密码强度指示器 -->
                <div id="strengthIndicator" class="hidden">
                    <h3 class="text-lg font-semibold text-white mb-3">Password Strength</h3>
                    <div class="space-y-2">
                        <div class="flex justify-between text-sm">
                            <span class="text-gray-400">Strength:</span>
                            <span id="strengthText" class="font-semibold">-</span>
                        </div>
                        <div class="w-full bg-gray-600 h-2">
                            <div id="strengthBar" class="h-2 transition-all duration-300" style="width: 0%"></div>
                        </div>
                        <div class="text-xs text-gray-500">
                            <div>✓ Length: <span id="lengthCheck">-</span></div>
                            <div>✓ Character variety: <span id="varietyCheck">-</span></div>
                            <div>✓ Entropy: <span id="entropyCheck">-</span> bits</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 工具信息 -->
        <div class="mt-12 grid grid-cols-1 md:grid-cols-3 gap-8">
            <div class="bg-gray-800 border border-gray-700 p-6">
                <h4 class="text-lg font-semibold text-white mb-3">Security Features</h4>
                <ul class="text-gray-300 space-y-2">
                    <li>• Cryptographically secure random</li>
                    <li>• No server storage</li>
                    <li>• Local generation only</li>
                    <li>• Customizable character sets</li>
                    <li>• Exclude similar characters</li>
                </ul>
            </div>
            <div class="bg-gray-800 border border-gray-700 p-6">
                <h4 class="text-lg font-semibold text-white mb-3">Password Tips</h4>
                <ul class="text-gray-300 space-y-2">
                    <li>• Use unique passwords for each account</li>
                    <li>• Minimum 12 characters recommended</li>
                    <li>• Include multiple character types</li>
                    <li>• Store in a password manager</li>
                    <li>• Enable two-factor authentication</li>
                </ul>
            </div>
            <div class="bg-gray-800 border border-gray-700 p-6">
                <h4 class="text-lg font-semibold text-white mb-3">Use Cases</h4>
                <ul class="text-gray-300 space-y-2">
                    <li>• Online accounts</li>
                    <li>• WiFi networks</li>
                    <li>• Database passwords</li>
                    <li>• API keys</li>
                    <li>• Temporary passwords</li>
                    <li>• System administration</li>
                </ul>
            </div>
        </div>

        <!-- FAQ Section -->
        <div class="px-8 pt-16 pb-16">
            <h2 class="text-3xl font-bold text-white text-center mb-8">Frequently Asked Questions</h2>
            <div class="max-w-4xl mx-auto space-y-6">
                <div class="border-b border-gray-700 pb-6">
                    <h3 class="text-xl font-semibold text-white mb-3">How secure are the generated passwords?</h3>
                    <p class="text-gray-300">Our passwords are generated using cryptographically secure random number generation. All generation happens locally in your browser - no passwords are sent to our servers.</p>
                </div>

                <div class="border-b border-gray-700 pb-6">
                    <h3 class="text-xl font-semibold text-white mb-3">What makes a password strong?</h3>
                    <p class="text-gray-300">A strong password is long (12+ characters), uses multiple character types (uppercase, lowercase, numbers, symbols), and is unique for each account.</p>
                </div>

                <div class="border-b border-gray-700 pb-6">
                    <h3 class="text-xl font-semibold text-white mb-3">Should I exclude similar characters?</h3>
                    <p class="text-gray-300">Yes, if you need to type the password manually. Excluding similar characters like 0/O and l/I prevents confusion when reading or typing passwords.</p>
                </div>

                <div class="border-b border-gray-700 pb-6">
                    <h3 class="text-xl font-semibold text-white mb-3">How do I remember strong passwords?</h3>
                    <p class="text-gray-300">Use a reputable password manager to store and auto-fill your passwords. This allows you to use unique, strong passwords for every account without memorizing them.</p>
                </div>

                <div class="mb-0">
                    <h3 class="text-xl font-semibold text-white mb-3">How often should I change my passwords?</h3>
                    <p class="text-gray-300 mb-0">Change passwords immediately if there's a security breach, or if you suspect compromise. For regular accounts, focus on using strong, unique passwords rather than frequent changes.</p>
                </div>
            </div>
        </div>
    </div>

<script>
// Password Generator functionality
document.addEventListener('DOMContentLoaded', function() {
    const passwordLength = document.getElementById('passwordLength');
    const lengthValue = document.getElementById('lengthValue');
    const passwordCount = document.getElementById('passwordCount');
    const countValue = document.getElementById('countValue');
    const generatePassword = document.getElementById('generatePassword');
    const passwordList = document.getElementById('passwordList');
    const copyAllPasswords = document.getElementById('copyAllPasswords');
    const exportPasswords = document.getElementById('exportPasswords');
    const copyTooltip = document.getElementById('copyTooltip');
    const exportTooltip = document.getElementById('exportTooltip');
    const strengthIndicator = document.getElementById('strengthIndicator');
    
    // 字符集选项
    const includeUppercase = document.getElementById('includeUppercase');
    const includeLowercase = document.getElementById('includeLowercase');
    const includeNumbers = document.getElementById('includeNumbers');
    const includeSymbols = document.getElementById('includeSymbols');
    const excludeSimilar = document.getElementById('excludeSimilar');
    const excludeAmbiguous = document.getElementById('excludeAmbiguous');
    
    let generatedPasswords = [];

    // 字符集定义
    const charSets = {
        uppercase: 'ABCDEFGHIJKLMNOPQRSTUVWXYZ',
        lowercase: 'abcdefghijklmnopqrstuvwxyz',
        numbers: '0123456789',
        symbols: '!@#$%^&*()_+-=[]{}|;:,.<>?'
    };
    
    const similarChars = '0Ol1I';
    const ambiguousChars = '{}[]()\/\\\'\"~,;.<>';

    // 滑块事件
    passwordLength.addEventListener('input', () => {
        lengthValue.textContent = passwordLength.value;
    });
    
    passwordCount.addEventListener('input', () => {
        countValue.textContent = passwordCount.value;
    });

    // 生成密码
    generatePassword.addEventListener('click', generatePasswords);
    
    // 自动生成（当设置改变时）
    [includeUppercase, includeLowercase, includeNumbers, includeSymbols, excludeSimilar, excludeAmbiguous].forEach(checkbox => {
        checkbox.addEventListener('change', () => {
            if (generatedPasswords.length > 0) {
                generatePasswords();
            }
        });
    });
    
    passwordLength.addEventListener('input', () => {
        if (generatedPasswords.length > 0) {
            generatePasswords();
        }
    });

    // 复制所有密码
    copyAllPasswords.addEventListener('click', async () => {
        if (generatedPasswords.length > 0) {
            try {
                await navigator.clipboard.writeText(generatedPasswords.join('\n'));
                
                if (copyTooltip) {
                    copyTooltip.textContent = 'Copied!';
                    copyTooltip.style.opacity = '1';
                    copyTooltip.style.visibility = 'visible';
                    
                    setTimeout(() => {
                        copyTooltip.textContent = 'Copy all passwords';
                        copyTooltip.style.opacity = '0';
                        copyTooltip.style.visibility = 'hidden';
                    }, 2000);
                }
            } catch (error) {
                console.error('Failed to copy:', error);
            }
        }
    });

    // 导出密码
    exportPasswords.addEventListener('click', () => {
        if (generatedPasswords.length > 0) {
            const content = generatedPasswords.join('\n');
            const blob = new Blob([content], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'passwords.txt';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            if (exportTooltip) {
                exportTooltip.textContent = 'Exported!';
                exportTooltip.style.opacity = '1';
                exportTooltip.style.visibility = 'visible';
                
                setTimeout(() => {
                    exportTooltip.textContent = 'Export as text file';
                    exportTooltip.style.opacity = '0';
                    exportTooltip.style.visibility = 'hidden';
                }, 2000);
            }
        }
    });

    function generatePasswords() {
        const length = parseInt(passwordLength.value);
        const count = parseInt(passwordCount.value);
        
        // 构建字符集
        let charset = '';
        if (includeUppercase.checked) charset += charSets.uppercase;
        if (includeLowercase.checked) charset += charSets.lowercase;
        if (includeNumbers.checked) charset += charSets.numbers;
        if (includeSymbols.checked) charset += charSets.symbols;
        
        if (!charset) {
            passwordList.innerHTML = '<div class="bg-red-700 p-3 border border-red-600 text-center text-white">Please select at least one character type</div>';
            return;
        }
        
        // 排除相似和模糊字符
        if (excludeSimilar.checked) {
            charset = charset.split('').filter(char => !similarChars.includes(char)).join('');
        }
        if (excludeAmbiguous.checked) {
            charset = charset.split('').filter(char => !ambiguousChars.includes(char)).join('');
        }
        
        generatedPasswords = [];
        
        // 生成多个密码
        for (let i = 0; i < count; i++) {
            let password = '';
            for (let j = 0; j < length; j++) {
                const randomIndex = Math.floor(Math.random() * charset.length);
                password += charset[randomIndex];
            }
            generatedPasswords.push(password);
        }
        
        displayPasswords();
        if (generatedPasswords.length === 1) {
            calculateStrength(generatedPasswords[0]);
        } else {
            strengthIndicator.classList.add('hidden');
        }
    }

    function displayPasswords() {
        passwordList.innerHTML = '';
        
        generatedPasswords.forEach((password, index) => {
            const passwordDiv = document.createElement('div');
            passwordDiv.className = 'bg-gray-700 p-3 border border-gray-600 flex items-center justify-between';
            passwordDiv.innerHTML = `
                <span class="font-mono text-sm break-all flex-1 mr-3">${password}</span>
                <button onclick="copyPassword('${password}', ${index})" class="px-2 py-1 bg-blue-600 text-white text-xs hover:bg-blue-700 transition-colors">
                    Copy
                </button>
            `;
            passwordList.appendChild(passwordDiv);
        });
    }

    function calculateStrength(password) {
        const length = password.length;
        let charsetSize = 0;
        let variety = 0;
        
        if (/[a-z]/.test(password)) { charsetSize += 26; variety++; }
        if (/[A-Z]/.test(password)) { charsetSize += 26; variety++; }
        if (/[0-9]/.test(password)) { charsetSize += 10; variety++; }
        if (/[^a-zA-Z0-9]/.test(password)) { charsetSize += 32; variety++; }
        
        const entropy = Math.log2(Math.pow(charsetSize, length));
        
        let strength = 'Very Weak';
        let strengthColor = '#ef4444';
        let strengthWidth = 20;
        
        if (entropy >= 60) {
            strength = 'Very Strong';
            strengthColor = '#10b981';
            strengthWidth = 100;
        } else if (entropy >= 50) {
            strength = 'Strong';
            strengthColor = '#059669';
            strengthWidth = 80;
        } else if (entropy >= 40) {
            strength = 'Good';
            strengthColor = '#f59e0b';
            strengthWidth = 60;
        } else if (entropy >= 30) {
            strength = 'Fair';
            strengthColor = '#f97316';
            strengthWidth = 40;
        }
        
        document.getElementById('strengthText').textContent = strength;
        document.getElementById('strengthText').style.color = strengthColor;
        document.getElementById('strengthBar').style.backgroundColor = strengthColor;
        document.getElementById('strengthBar').style.width = strengthWidth + '%';
        document.getElementById('lengthCheck').textContent = length + ' characters';
        document.getElementById('varietyCheck').textContent = variety + '/4 types';
        document.getElementById('entropyCheck').textContent = Math.round(entropy);
        
        strengthIndicator.classList.remove('hidden');
    }

    // 全局函数用于复制单个密码
    window.copyPassword = async function(password, index) {
        try {
            await navigator.clipboard.writeText(password);
        } catch (error) {
            console.error('Failed to copy password:', error);
        }
    };

    // 初始生成一个密码
    generatePasswords();
});
</script>

</div>

<?php include_once ROOT_PATH . '/templates/layout/footer.php'; ?>
