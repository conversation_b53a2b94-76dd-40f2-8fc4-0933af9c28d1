<?php
/**
 * 提交用户需求AJAX处理
 */

// 清理输出缓冲区
if (ob_get_level()) {
    ob_clean();
}

// 开启错误显示用于调试
ini_set('display_errors', 0); // 关闭错误显示，避免影响JSON响应
error_reporting(E_ALL);

// 设置JSON响应头
header('Content-Type: application/json; charset=utf-8');

// 安全检查
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

session_start();

// 使用统一的数据库连接
require_once __DIR__ . '/includes/ajax-database.php';

// 检查用户登录状态
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'Please log in to submit a request']);
    exit;
}

$response = ['success' => false, 'message' => ''];

try {
    // 获取用户信息
    $stmt = $pdo->prepare("SELECT * FROM pt_member WHERE id = ? AND status = 'active'");
    $stmt->execute([$_SESSION['user_id']]);
    $user = $stmt->fetch();
    
    if (!$user) {
        throw new Exception('User not found or inactive');
    }
    
    // 获取表单数据并进行安全过滤
    $title = trim($_POST['title'] ?? '');
    $slug = trim($_POST['slug'] ?? '');
    $description = trim($_POST['description'] ?? '');
    $category = trim($_POST['category'] ?? 'other');

    // 安全过滤函数
    function sanitizeInput($input) {
        // 移除HTML标签
        $input = strip_tags($input);

        // 移除潜在的脚本代码
        $input = preg_replace('/javascript:/i', '', $input);
        $input = preg_replace('/vbscript:/i', '', $input);
        $input = preg_replace('/onload/i', '', $input);
        $input = preg_replace('/onerror/i', '', $input);
        $input = preg_replace('/onclick/i', '', $input);
        $input = preg_replace('/onmouseover/i', '', $input);

        // 移除SQL注入相关字符
        $input = preg_replace('/[\'";]/', '', $input);

        // 移除潜在的命令注入
        $input = preg_replace('/[`${}]/', '', $input);

        // 移除换行符和制表符
        $input = preg_replace('/[\r\n\t]/', ' ', $input);

        // 移除多余空格
        $input = preg_replace('/\s+/', ' ', $input);

        return trim($input);
    }

    // 检测恶意内容
    function containsMaliciousContent($input) {
        $maliciousPatterns = [
            // XSS攻击模式
            '/<script/i',
            '/<iframe/i',
            '/<object/i',
            '/<embed/i',
            '/javascript:/i',
            '/vbscript:/i',
            '/data:text\/html/i',

            // SQL注入模式
            '/union\s+select/i',
            '/drop\s+table/i',
            '/delete\s+from/i',
            '/insert\s+into/i',
            '/update\s+set/i',

            // 命令注入模式
            '/\|\s*nc\s/i',
            '/\|\s*netcat/i',
            '/\|\s*wget/i',
            '/\|\s*curl/i',
            '/\$\(/i',
            '/`[^`]*`/i',

            // 文件包含攻击
            '/\.\.\/\.\.\//i',
            '/\/etc\/passwd/i',
            '/\/proc\/self/i',

            // 网址和链接模式
            '/https?:\/\//i',
            '/ftp:\/\//i',
            '/www\./i',
            '/\.com/i',
            '/\.org/i',
            '/\.net/i',
            '/\.edu/i',
            '/\.gov/i',
        ];

        foreach ($maliciousPatterns as $pattern) {
            if (preg_match($pattern, $input)) {
                return true;
            }
        }

        return false;
    }

    // 应用安全过滤
    $title = sanitizeInput($title);
    $description = sanitizeInput($description);

    // slug不需要sanitizeInput，因为它只包含特定字符
    $slug = strtolower(trim($slug));

    // 检测恶意内容
    if (containsMaliciousContent($title)) {
        throw new Exception('Title contains prohibited content. Please use plain text only.');
    }

    if (containsMaliciousContent($description)) {
        throw new Exception('Description contains prohibited content. Please use plain text only.');
    }

    // 基本验证
    if (empty($title)) {
        throw new Exception('Title is required');
    }

    if (strlen($title) > 200) {
        throw new Exception('Title is too long (max 200 characters)');
    }

    if (empty($slug)) {
        throw new Exception('URL slug is required');
    }

    if (strlen($slug) < 3 || strlen($slug) > 60) {
        throw new Exception('URL slug must be between 3 and 60 characters');
    }

    if (!preg_match('/^[a-z0-9\-]+$/', $slug)) {
        throw new Exception('URL slug can only contain lowercase letters, numbers, and hyphens');
    }

    if (empty($description)) {
        throw new Exception('Description is required');
    }

    if (strlen($description) > 1000) {
        throw new Exception('Description is too long (max 1000 characters)');
    }

    // 内容质量检查
    if (strlen($title) < 10) {
        throw new Exception('Title is too short (min 10 characters)');
    }

    if (strlen($description) < 20) {
        throw new Exception('Description is too short (min 20 characters)');
    }

    // 检查是否只包含特殊字符
    if (preg_match('/^[^a-zA-Z0-9\s]+$/', $title)) {
        throw new Exception('Title must contain alphanumeric characters');
    }

    if (preg_match('/^[^a-zA-Z0-9\s]+$/', $description)) {
        throw new Exception('Description must contain alphanumeric characters');
    }
    
    // 验证分类（从数据库获取有效分类）
    $categoryStmt = $pdo->query("SELECT slug FROM pt_tool_category WHERE status = 'active'");
    $validCategories = $categoryStmt->fetchAll(PDO::FETCH_COLUMN);
    $validCategories[] = 'other'; // 添加other作为默认选项

    if (!in_array($category, $validCategories)) {
        $category = 'other';
    }
    
    // 检查提交频率限制（每用户每天最多5个需求）
    $today = date('Y-m-d');
    $stmt = $pdo->prepare("
        SELECT COUNT(*) FROM pt_user_requests 
        WHERE user_id = ? AND DATE(created_at) = ?
    ");
    $stmt->execute([$user['id'], $today]);
    $todayCount = $stmt->fetchColumn();
    
    if ($todayCount >= 5) {
        throw new Exception('You have reached the daily limit of 5 requests. Please try again tomorrow.');
    }
    
    // 检查重复内容
    $titleHash = md5(strtolower($title));
    $descriptionHash = md5(strtolower($description));
    
    $stmt = $pdo->prepare("
        SELECT r.title FROM pt_user_requests r
        JOIN pt_request_duplicates d ON r.id = d.request_id
        WHERE d.user_id = ? AND d.title_hash = ? AND d.description_hash = ?
    ");
    $stmt->execute([$user['id'], $titleHash, $descriptionHash]);
    $duplicate = $stmt->fetch();
    
    if ($duplicate) {
        throw new Exception('You have already submitted a similar request: "' . htmlspecialchars($duplicate['title']) . '"');
    }
    
    // 检查相似标题（防止用户提交几乎相同的需求）
    $stmt = $pdo->prepare("
        SELECT title FROM pt_user_requests 
        WHERE user_id = ? AND LOWER(title) LIKE ? 
        ORDER BY created_at DESC LIMIT 1
    ");
    $stmt->execute([$user['id'], '%' . strtolower($title) . '%']);
    $similarTitle = $stmt->fetch();
    
    if ($similarTitle && strlen($title) > 10) {
        // 计算相似度
        $similarity = 0;
        similar_text(strtolower($title), strtolower($similarTitle['title']), $similarity);
        if ($similarity > 80) {
            throw new Exception('You have already submitted a similar request: "' . htmlspecialchars($similarTitle['title']) . '"');
        }
    }

    // 检查slug唯一性
    require_once dirname(__DIR__) . '/classes/SlugGenerator.php';
    if (!SlugGenerator::isUnique($slug, $pdo)) {
        // 如果slug不唯一，自动生成一个唯一的
        $slug = SlugGenerator::generateUnique($title, $pdo);
    }

    // 获取用户IP地址
    $ipAddress = $_SERVER['HTTP_X_FORWARDED_FOR'] ?? $_SERVER['HTTP_X_REAL_IP'] ?? $_SERVER['REMOTE_ADDR'] ?? '';
    if (strpos($ipAddress, ',') !== false) {
        $ipAddress = trim(explode(',', $ipAddress)[0]);
    }
    
    // 开始事务
    $pdo->beginTransaction();
    
    try {
        // 插入需求
        $stmt = $pdo->prepare("
            INSERT INTO pt_user_requests (
                user_id, title, slug, description, category, priority, status, ip_address, created_at
            ) VALUES (?, ?, ?, ?, ?, 'medium', 'pending', ?, NOW())
        ");
        $stmt->execute([$user['id'], $title, $slug, $description, $category, $ipAddress]);
        $requestId = $pdo->lastInsertId();
        
        // 插入防重复记录
        $stmt = $pdo->prepare("
            INSERT INTO pt_request_duplicates (user_id, title_hash, description_hash, request_id)
            VALUES (?, ?, ?, ?)
        ");
        $stmt->execute([$user['id'], $titleHash, $descriptionHash, $requestId]);
        
        // 记录用户活动
        $stmt = $pdo->prepare("
            INSERT INTO pt_member_activity_log (user_id, action, description, ip_address, created_at)
            VALUES (?, 'request_submit', ?, ?, NOW())
        ");
        $stmt->execute([
            $user['id'], 
            "Submitted feature request: " . substr($title, 0, 50) . (strlen($title) > 50 ? '...' : ''),
            $ipAddress
        ]);
        
        // 提交事务
        $pdo->commit();

        // 注意：提交请求不再奖励配额

        $response['success'] = true;
        $response['message'] = 'Your request has been submitted successfully! We will review it soon.';
        $response['request_id'] = $requestId;
        
    } catch (Exception $e) {
        $pdo->rollBack();
        throw $e;
    }
    
} catch (Exception $e) {
    // 清理输出缓冲区，确保干净的JSON响应
    if (ob_get_level()) {
        ob_clean();
    }
    
    $response['message'] = $e->getMessage();
    
    // 调试信息（开发环境）
    $response['debug'] = [
        'user_id' => $_SESSION['user_id'] ?? 'unknown',
        'error' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ];
}

// 清理输出缓冲区，确保干净的JSON响应
if (ob_get_level()) {
    ob_clean();
}

// 输出JSON响应
echo json_encode($response);
