<?php
/**
 * 加载更多需求的AJAX处理
 */

/**
 * 根据投票数量获取对应的图标和样式
 */
function getVoteIcon($votes) {
    if ($votes >= 100) {
        return [
            'icon' => '🔥',
            'color' => 'text-red-400',
            'textColor' => 'text-red-400',
            'animation' => 'fire-animation',
            'label' => 'Super Hot Request - ' . $votes . ' votes'
        ];
    } elseif ($votes >= 50) {
        return [
            'icon' => '⭐',
            'color' => 'text-yellow-400',
            'textColor' => 'text-yellow-400',
            'animation' => 'star-twinkle',
            'label' => 'Popular Request - ' . $votes . ' votes'
        ];
    } elseif ($votes >= 20) {
        return [
            'icon' => '🚀',
            'color' => 'text-blue-400',
            'textColor' => 'text-blue-400',
            'animation' => 'rocket-rise',
            'label' => 'Rising Request - ' . $votes . ' votes'
        ];
    } elseif ($votes >= 10) {
        return [
            'icon' => '💡',
            'color' => 'text-green-400',
            'textColor' => 'text-green-400',
            'animation' => 'bulb-glow',
            'label' => 'Good Idea - ' . $votes . ' votes'
        ];
    } elseif ($votes >= 5) {
        return [
            'icon' => '👍',
            'color' => 'text-purple-400',
            'textColor' => 'text-purple-400',
            'animation' => '',
            'label' => 'Nice Suggestion - ' . $votes . ' votes'
        ];
    } elseif ($votes >= 1) {
        return [
            'icon' => '💭',
            'color' => 'text-gray-400',
            'textColor' => 'text-gray-400',
            'animation' => '',
            'label' => 'Initial Idea - ' . $votes . ' votes'
        ];
    } else {
        return [
            'icon' => '📝',
            'color' => 'text-gray-300',
            'textColor' => 'text-gray-300',
            'animation' => '',
            'label' => 'New Proposal - 0 votes'
        ];
    }
}

header('Content-Type: application/json');

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit;
}

session_start();

// 使用统一的数据库连接
require_once __DIR__ . '/includes/ajax-database.php';

try {

    // 获取参数
    $status = $_POST['status'] ?? 'all';
    $category = $_POST['category'] ?? 'all';
    $sort = $_POST['sort'] ?? 'latest';
    $page = max(1, intval($_POST['page'] ?? 1));
    $limit = 5;
    $offset = ($page - 1) * $limit;

    // 构建查询条件
    $whereConditions = [];
    $params = [];

    if ($status !== 'all') {
        $whereConditions[] = "r.status = ?";
        $params[] = $status;
    }

    if ($category !== 'all') {
        $whereConditions[] = "r.category = ?";
        $params[] = $category;
    }

    $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

    // 排序逻辑
    $orderBy = match($sort) {
        'votes' => 'r.votes DESC, r.created_at DESC',
        'oldest' => 'r.created_at ASC',
        default => 'r.created_at DESC'
    };

    // 获取需求列表
    $sql = "
        SELECT r.*, m.username, m.first_name, m.last_name,
               CASE WHEN rv.user_id IS NOT NULL THEN 1 ELSE 0 END as user_voted
        FROM pt_user_requests r
        LEFT JOIN pt_member m ON r.user_id = m.id
        LEFT JOIN pt_request_votes rv ON r.id = rv.request_id AND rv.user_id = ?
        {$whereClause}
        ORDER BY {$orderBy}
        LIMIT {$limit} OFFSET {$offset}
    ";

    $userVoteParams = [isset($_SESSION['user_id']) ? $_SESSION['user_id'] : 0];
    $requests = $pdo->prepare($sql);
    $allParams = array_merge($userVoteParams, $params);
    $requests->execute($allParams);
    $requestsList = $requests->fetchAll();

    // 调试信息已删除

    // 获取分类数据
    $categoryStmt = $pdo->query("SELECT slug, name FROM pt_tool_category WHERE status = 'active' ORDER BY sort_order ASC, name ASC");
    $dbCategories = $categoryStmt->fetchAll();
    $categories = [];
    foreach ($dbCategories as $cat) {
        $categories[$cat['slug']] = $cat['name'];
    }
    $categories['other'] = 'Other';

    // 状态颜色函数
    function getStatusColor($status) {
        $colors = [
            'pending' => 'bg-yellow-100 text-yellow-800',
            'reviewing' => 'bg-blue-100 text-blue-800',
            'accepted' => 'bg-green-100 text-green-800',
            'rejected' => 'bg-red-100 text-red-800',
            'completed' => 'bg-purple-100 text-purple-800'
        ];
        
        return $colors[$status] ?? 'bg-gray-100 text-gray-800';
    }

    // 生成HTML
    ob_start();

    // 调试信息已移除，避免影响布局

    if (!empty($requestsList)) {
        // 添加动画样式
        echo '<style>
        .fire-animation { animation: fire-flicker 2s ease-in-out infinite alternate; }
        @keyframes fire-flicker {
            0%, 100% { transform: scale(1) rotate(-1deg); filter: hue-rotate(0deg) brightness(1); }
            25% { transform: scale(1.05) rotate(1deg); filter: hue-rotate(10deg) brightness(1.1); }
            50% { transform: scale(0.98) rotate(-0.5deg); filter: hue-rotate(-5deg) brightness(0.95); }
            75% { transform: scale(1.02) rotate(0.5deg); filter: hue-rotate(5deg) brightness(1.05); }
        }
        .star-twinkle { animation: star-twinkle 1.5s ease-in-out infinite; }
        @keyframes star-twinkle {
            0%, 100% { opacity: 1; transform: scale(1); filter: brightness(1); }
            50% { opacity: 0.7; transform: scale(1.1); filter: brightness(1.3); }
        }
        .rocket-rise { animation: rocket-rise 3s ease-in-out infinite; }
        @keyframes rocket-rise {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            25% { transform: translateY(-3px) rotate(-1deg); }
            50% { transform: translateY(-5px) rotate(0deg); }
            75% { transform: translateY(-2px) rotate(1deg); }
        }
        .bulb-glow { animation: bulb-glow 2.5s ease-in-out infinite; }
        @keyframes bulb-glow {
            0%, 100% { filter: brightness(1) drop-shadow(0 0 0px rgba(34, 197, 94, 0)); transform: scale(1); }
            50% { filter: brightness(1.2) drop-shadow(0 0 8px rgba(34, 197, 94, 0.6)); transform: scale(1.05); }
        }
        </style>';

        foreach ($requestsList as $request):
    ?>
        <div class="bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl p-6 hover:border-gray-600 hover:shadow-lg transition-all duration-300 group">
            <div class="flex items-start space-x-6">
                <!-- 投票区域 -->
                <div class="flex flex-col items-center min-w-[90px]">
                    <button onclick="voteRequest(<?= $request['id'] ?>)"
                            class="group/vote relative bg-gradient-to-br from-gray-800 to-gray-700 hover:from-gray-700 hover:to-gray-600 border-2 <?= $request['user_voted'] ? 'border-blue-400 shadow-blue-400/20 shadow-lg' : 'border-gray-600 hover:border-blue-400' ?> rounded-2xl p-4 transition-all duration-300 transform hover:scale-105 hover:shadow-xl <?= !isset($_SESSION['user_id']) ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer' ?>"
                            <?= !isset($_SESSION['user_id']) ? 'disabled title="Please login to vote"' : 'title="Click to vote"' ?>>
                        <div class="text-center">
                            <?php
                            $votes = (int)$request['votes'];
                            $iconData = getVoteIcon($votes);
                            ?>
                            <div class="text-3xl mb-2 transition-transform duration-200 group-hover/vote:scale-110 <?= $iconData['animation'] ?>"
                                 aria-label="<?= $iconData['label'] ?>"><?= $iconData['icon'] ?></div>
                            <div class="text-xl font-bold <?= $iconData['color'] ?> transition-colors"><?= $request['votes'] ?></div>
                            <div class="text-xs font-medium <?= $iconData['textColor'] ?> uppercase tracking-wide">votes</div>
                        </div>
                    </button>
                </div>
                
                <!-- 需求内容 -->
                <div class="flex-1 min-w-0">
                    <!-- 标题和标签区域 -->
                    <div class="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-3 mb-4">
                        <h3 class="text-xl font-bold text-white leading-tight group-hover:text-blue-300 transition-colors duration-200">
                            <a href="/ideas/<?= htmlspecialchars($request['slug']) ?>"
                               class="hover:text-blue-300 transition-colors duration-200"
                               target="_blank"
                               rel="noopener noreferrer">
                                <?= htmlspecialchars($request['title']) ?>
                            </a>
                        </h3>
                        <div class="flex items-center gap-2 flex-shrink-0">
                            <span class="px-3 py-1.5 text-xs font-semibold rounded-full <?= getStatusColor($request['status']) ?> shadow-sm">
                                <?= ucfirst($request['status']) ?>
                            </span>
                            <span class="px-3 py-1.5 text-xs font-semibold rounded-full bg-gradient-to-r from-gray-700 to-gray-600 text-gray-200 shadow-sm">
                                <?= htmlspecialchars($categories[$request['category']] ?? ucfirst($request['category'])) ?>
                            </span>
                        </div>
                    </div>

                    <!-- 描述内容 -->
                    <div class="mb-5">
                        <p class="text-gray-300 leading-relaxed line-clamp-4 hover:line-clamp-none transition-all duration-300"><?= htmlspecialchars($request['description']) ?></p>
                    </div>

                    <!-- 元信息 -->
                    <div class="flex items-center justify-between text-sm">
                        <div class="flex items-center space-x-4 text-gray-400">
                            <span class="flex items-center">
                                <svg class="w-4 h-4 mr-1.5" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                                </svg>
                                <?= htmlspecialchars($request['username'] ?? 'Anonymous') ?>
                            </span>
                            <span class="flex items-center">
                                <svg class="w-4 h-4 mr-1.5" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                                </svg>
                                <?= date('M j, Y', strtotime($request['created_at'])) ?>
                            </span>
                        </div>
                    </div>
                    
                    <?php if (!empty($request['admin_reply'])): ?>
                        <div class="mt-6 p-4 border-2 border-yellow-400 rounded-lg">
                            <p class="text-sm font-semibold text-yellow-400 mb-2">Official Response</p>
                            <p class="text-sm text-gray-300 leading-relaxed"><?= htmlspecialchars($request['admin_reply']) ?></p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    <?php
        endforeach;
    }
    $html = ob_get_clean();

    echo json_encode([
        'success' => true,
        'html' => $html,
        'hasMore' => count($requestsList) === $limit
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage()
    ]);
}
?>
