<?php
/**
 * System Settings Page
 * Manage website SEO, social media, and general settings
 */

// 使用统一的数据库连接
require_once dirname(dirname(__DIR__)) . '/includes/database-connection.php';

$message = '';
$messageType = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $settings = $_POST['settings'] ?? [];
        
        // Validate required fields
        if (empty($settings['site_title']) || empty($settings['site_description'])) {
            $message = 'Site title and description are required.';
            $messageType = 'error';
        } else {
            // Update settings in database
            foreach ($settings as $key => $value) {
                // 确定是否为私有配置
                $privateSettings = ['jwt_secret', 'google_oauth_client_secret'];
                $isPublic = in_array($key, $privateSettings) ? 0 : 1;

                $sql = "INSERT INTO pt_system_config (setting_key, setting_value, setting_type, description, is_public)
                        VALUES (?, ?, 'string', ?, ?)
                        ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)";
                $stmt = $pdo->prepare($sql);

                // Set description based on setting key
                $description = getSettingDescription($key);
                $stmt->execute([$key, $value, $description, $isPublic]);
            }
            
            $message = 'Settings updated successfully!';
            $messageType = 'success';
        }
    } catch (PDOException $e) {
        error_log("Settings update error: " . $e->getMessage());
        $message = 'Failed to update settings. Please try again.';
        $messageType = 'error';
    }
}

// Get current settings
$currentSettings = [];
try {
    // 获取公开配置
    $sql = "SELECT setting_key, setting_value FROM pt_system_config WHERE is_public = 1";
    $stmt = $pdo->query($sql);
    while ($row = $stmt->fetch()) {
        $currentSettings[$row['setting_key']] = $row['setting_value'];
    }

    // 获取管理后台需要的私有配置
    $privateSettings = ['jwt_secret', 'google_oauth_client_secret'];
    foreach ($privateSettings as $key) {
        $stmt = $pdo->prepare("SELECT setting_value FROM pt_system_config WHERE setting_key = ?");
        $stmt->execute([$key]);
        $result = $stmt->fetch();
        if ($result) {
            $currentSettings[$key] = $result['setting_value'];
        }
    }
} catch (PDOException $e) {
    error_log("Settings fetch error: " . $e->getMessage());
}

// Default values
$defaults = [
    'site_title' => 'Free AI-Powered Online Tools Platform - Prompt2Tool',
    'site_description' => 'Discover 100+ free online tools powered by AI. HTML formatter, CSS minifier, image converter, SEO analyzer, and more. Boost your productivity.',
    'og_title' => 'Free AI-Powered Online Tools - Prompt2Tool',
    'og_description' => 'Access 100+ free online tools powered by AI. Perfect for developers, designers, and digital marketers.',
    'og_image' => '/assets/images/og-default.jpg',
    'twitter_handle' => '@Prompt2Tool',
    'twitter_card' => 'summary_large_image',
    'company_description' => 'AI-powered free online tools platform providing development, design, SEO, and various practical tools to boost your productivity.',
    'copyright_text' => 'Prompt2Tool. All rights reserved.',
    'github_url' => 'https://github.com/prompt2tool',
    'twitter_url' => 'https://twitter.com/prompt2tool',
    'linkedin_url' => 'https://linkedin.com/company/prompt2tool',
    'contact_email' => '<EMAIL>',
    'google_analytics_id' => '',
    'google_adsense_id' => '',
    'google_oauth_client_id' => '',
    'google_oauth_client_secret' => '',
    'google_oauth_enabled' => false,
    'jwt_secret' => 'your-secret-key-change-this-in-production'
];

// Merge with current settings
foreach ($defaults as $key => $defaultValue) {
    if (!isset($currentSettings[$key])) {
        $currentSettings[$key] = $defaultValue;
    }
}

function getSettingDescription($key) {
    $descriptions = [
        'site_title' => 'Main website title for SEO',
        'site_description' => 'Meta description for homepage',
        'site_keywords' => 'Meta keywords for SEO',
        'og_title' => 'Open Graph title for social sharing',
        'og_description' => 'Open Graph description for social sharing',
        'og_image' => 'Open Graph image URL',
        'twitter_handle' => 'Twitter handle (with @)',
        'twitter_card' => 'Twitter card type',
        'company_description' => 'Company description for footer',
        'copyright_text' => 'Copyright text for footer (without year and ©, will be added automatically)',
        'github_url' => 'GitHub profile URL',
        'twitter_url' => 'Twitter profile URL',
        'linkedin_url' => 'LinkedIn profile URL',
        'contact_email' => 'Contact email address',
        'google_analytics_id' => 'Google Analytics tracking ID',
        'google_adsense_id' => 'Google AdSense publisher ID',
        'google_oauth_client_id' => 'Google OAuth Client ID for login integration',
        'google_oauth_client_secret' => 'Google OAuth Client Secret (keep secure)',
        'google_oauth_enabled' => 'Enable Google OAuth login functionality',
        'jwt_secret' => 'JWT secret key for API token signing (keep secure)'
    ];
    
    return $descriptions[$key] ?? 'System setting';
}
?>

<div class="p-6">
    <div class="mb-6">
        <p class="text-gray-600">Configure website SEO, social media, and general settings</p>
    </div>

    <!-- Success/Error Messages -->
    <?php if ($message): ?>
    <div class="mb-6 p-4 border-l-4 <?= $messageType === 'success' ? 'border-green-500 bg-green-50' : 'border-red-500 bg-red-50' ?>">
        <div class="flex">
            <div class="flex-shrink-0">
                <i class="fas <?= $messageType === 'success' ? 'fa-check-circle text-green-400' : 'fa-exclamation-circle text-red-400' ?>"></i>
            </div>
            <div class="ml-3">
                <p class="text-sm <?= $messageType === 'success' ? 'text-green-700' : 'text-red-700' ?>">
                    <?= htmlspecialchars($message) ?>
                </p>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <form method="POST" class="space-y-8">
        <!-- SEO Settings -->
        <div class="bg-white shadow border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">SEO Settings</h3>
                <p class="mt-1 text-sm text-gray-600">Configure meta tags and SEO information</p>
            </div>
            <div class="p-6 space-y-6">
                <!-- Site Title -->
                <div>
                    <label for="site_title" class="block text-sm font-medium text-gray-700 mb-2">Site Title</label>
                    <input type="text" id="site_title" name="settings[site_title]"
                           value="<?= htmlspecialchars($currentSettings['site_title']) ?>"
                           class="w-full px-3 py-2 border border-gray-300 focus:outline-none focus:border-accent"
                           maxlength="60" required>
                    <div class="mt-1 flex justify-between items-center">
                        <p class="text-sm text-gray-500">Main title for your website (50-60 characters optimal)</p>
                        <span id="title-counter" class="text-sm font-medium">0/60</span>
                    </div>
                    <div id="title-warning" class="mt-1 text-sm text-red-600 hidden">
                        ⚠️ Title too long! Reduce to 50-60 characters for optimal SEO.
                    </div>
                </div>

                <!-- Site Description -->
                <div>
                    <label for="site_description" class="block text-sm font-medium text-gray-700 mb-2">Site Description</label>
                    <textarea id="site_description" name="settings[site_description]" rows="3"
                              class="w-full px-3 py-2 border border-gray-300 focus:outline-none focus:border-accent"
                              maxlength="160" required><?= htmlspecialchars($currentSettings['site_description']) ?></textarea>
                    <div class="mt-1 flex justify-between items-center">
                        <p class="text-sm text-gray-500">Meta description for search engines (150-160 characters optimal)</p>
                        <span id="description-counter" class="text-sm font-medium">0/160</span>
                    </div>
                    <div id="description-warning" class="mt-1 text-sm text-red-600 hidden">
                        ⚠️ Description too long! Reduce to 150-160 characters for optimal SEO.
                    </div>
                </div>
            </div>
        </div>

        <!-- Open Graph Settings -->
        <div class="bg-white shadow border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Social Media (Open Graph)</h3>
                <p class="mt-1 text-sm text-gray-600">Configure how your site appears when shared on social media</p>
            </div>
            <div class="p-6 space-y-6">
                <!-- OG Title -->
                <div>
                    <label for="og_title" class="block text-sm font-medium text-gray-700 mb-2">Open Graph Title</label>
                    <input type="text" id="og_title" name="settings[og_title]" 
                           value="<?= htmlspecialchars($currentSettings['og_title']) ?>"
                           class="w-full px-3 py-2 border border-gray-300 focus:outline-none focus:border-accent">
                    <p class="mt-1 text-sm text-gray-500">Title for social media sharing</p>
                </div>

                <!-- OG Description -->
                <div>
                    <label for="og_description" class="block text-sm font-medium text-gray-700 mb-2">Open Graph Description</label>
                    <textarea id="og_description" name="settings[og_description]" rows="2"
                              class="w-full px-3 py-2 border border-gray-300 focus:outline-none focus:border-accent"><?= htmlspecialchars($currentSettings['og_description']) ?></textarea>
                    <p class="mt-1 text-sm text-gray-500">Description for social media sharing</p>
                </div>

                <!-- OG Image -->
                <div>
                    <label for="og_image" class="block text-sm font-medium text-gray-700 mb-2">Open Graph Image</label>

                    <?php if (!empty($currentSettings['og_image']) && $currentSettings['og_image'] !== '/assets/images/og-default.jpg'): ?>
                        <!-- Show current image -->
                        <div class="mb-3 p-3 border border-gray-200 rounded bg-gray-50">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    <img src="<?= htmlspecialchars($currentSettings['og_image']) ?>"
                                         alt="Current OG Image"
                                         class="w-16 h-8 object-cover rounded border"
                                         onerror="this.src='/assets/images/placeholder.jpg'">
                                    <div>
                                        <p class="text-sm font-medium text-gray-900">Current Image</p>
                                        <p class="text-xs text-gray-500"><?= htmlspecialchars($currentSettings['og_image']) ?></p>
                                    </div>
                                </div>
                                <button type="button" onclick="removeOGImage()"
                                        class="text-red-600 hover:text-red-800 text-sm">
                                    <i class="fas fa-trash mr-1"></i>Remove
                                </button>
                            </div>
                        </div>

                        <!-- Hidden input for current image -->
                        <input type="hidden" id="og_image" name="settings[og_image]"
                               value="<?= htmlspecialchars($currentSettings['og_image']) ?>">
                    <?php else: ?>
                        <!-- Upload button when no image -->
                        <div class="mb-3">
                            <div class="flex items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100" onclick="document.getElementById('og_image_upload').click()">
                                <div class="flex flex-col items-center justify-center pt-5 pb-6">
                                    <i class="fas fa-cloud-upload-alt text-gray-400 text-2xl mb-2"></i>
                                    <p class="mb-2 text-sm text-gray-500">
                                        <span class="font-semibold">Click to upload</span> or drag and drop
                                    </p>
                                    <p class="text-xs text-gray-500">PNG, JPG or JPEG (1200x630px recommended)</p>
                                </div>
                                <input type="file" id="og_image_upload" class="hidden" accept="image/*" onchange="handleImageUpload(this)">
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Always show manual URL input -->
                    <div class="mt-3">
                        <label for="og_image_url" class="block text-sm font-medium text-gray-700 mb-1">Or enter image path/URL manually:</label>
                        <input type="text" id="og_image_url"
                               value="<?= htmlspecialchars($currentSettings['og_image']) ?>"
                               class="w-full px-3 py-2 border border-gray-300 focus:outline-none focus:border-accent"
                               placeholder="/assets/images/og-image.jpg or https://example.com/image.jpg"
                               onchange="updateOGImageFromURL(this.value)">
                    </div>

                    <!-- Hidden input for form submission -->
                    <input type="hidden" id="og_image" name="settings[og_image]" value="<?= htmlspecialchars($currentSettings['og_image']) ?>">

                    <p class="mt-2 text-sm text-gray-500">
                        Image for social media sharing. Recommended size: 1200x630px. Each upload will replace the previous image.
                    </p>
                </div>
            </div>
        </div>

        <!-- Twitter Settings -->
        <div class="bg-white shadow border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Twitter Settings</h3>
                <p class="mt-1 text-sm text-gray-600">Configure Twitter Cards and social links</p>
            </div>
            <div class="p-6 space-y-6">
                <!-- Twitter Handle -->
                <div>
                    <label for="twitter_handle" class="block text-sm font-medium text-gray-700 mb-2">Twitter Handle</label>
                    <input type="text" id="twitter_handle" name="settings[twitter_handle]"
                           value="<?= htmlspecialchars($currentSettings['twitter_handle']) ?>"
                           class="w-full px-3 py-2 border border-gray-300 focus:outline-none focus:border-accent"
                           placeholder="@Prompt2Tool">
                    <p class="mt-1 text-sm text-gray-500">Your Twitter handle (include @)</p>
                </div>

                <!-- Twitter Card Type -->
                <div>
                    <label for="twitter_card" class="block text-sm font-medium text-gray-700 mb-2">Twitter Card Type</label>
                    <select id="twitter_card" name="settings[twitter_card]"
                            class="w-full px-3 py-2 border border-gray-300 focus:outline-none focus:border-accent">
                        <option value="summary" <?= $currentSettings['twitter_card'] === 'summary' ? 'selected' : '' ?>>Summary</option>
                        <option value="summary_large_image" <?= $currentSettings['twitter_card'] === 'summary_large_image' ? 'selected' : '' ?>>Summary Large Image</option>
                    </select>
                    <p class="mt-1 text-sm text-gray-500">Type of Twitter card to display</p>
                </div>
            </div>
        </div>

        <!-- Company Information -->
        <div class="bg-white shadow border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Company Information</h3>
                <p class="mt-1 text-sm text-gray-600">Configure company details and footer information</p>
            </div>
            <div class="p-6 space-y-6">
                <!-- Company Description -->
                <div>
                    <label for="company_description" class="block text-sm font-medium text-gray-700 mb-2">Company Description</label>
                    <textarea id="company_description" name="settings[company_description]" rows="3"
                              class="w-full px-3 py-2 border border-gray-300 focus:outline-none focus:border-accent"><?= htmlspecialchars($currentSettings['company_description']) ?></textarea>
                    <p class="mt-1 text-sm text-gray-500">Description shown in footer and about sections</p>
                </div>

                <!-- Copyright Text -->
                <div>
                    <label for="copyright_text" class="block text-sm font-medium text-gray-700 mb-2">Copyright Text</label>
                    <input type="text" id="copyright_text" name="settings[copyright_text]"
                           value="<?= htmlspecialchars($currentSettings['copyright_text']) ?>"
                           class="w-full px-3 py-2 border border-gray-300 focus:outline-none focus:border-accent"
                           placeholder="Prompt2Tool. All rights reserved.">
                    <p class="mt-1 text-sm text-gray-500">
                        Copyright notice for footer. Year and © symbol will be added automatically.
                        <br><strong>Example:</strong> "Prompt2Tool. All rights reserved." becomes "© <?= date('Y') ?> Prompt2Tool. All rights reserved."
                    </p>
                </div>

                <!-- Contact Email -->
                <div>
                    <label for="contact_email" class="block text-sm font-medium text-gray-700 mb-2">Contact Email</label>
                    <input type="email" id="contact_email" name="settings[contact_email]"
                           value="<?= htmlspecialchars($currentSettings['contact_email']) ?>"
                           class="w-full px-3 py-2 border border-gray-300 focus:outline-none focus:border-accent">
                    <p class="mt-1 text-sm text-gray-500">Main contact email address</p>
                </div>
            </div>
        </div>

        <!-- Social Media Links -->
        <div class="bg-white shadow border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Social Media Links</h3>
                <p class="mt-1 text-sm text-gray-600">Configure social media profile URLs</p>
            </div>
            <div class="p-6 space-y-6">
                <!-- GitHub URL -->
                <div>
                    <label for="github_url" class="block text-sm font-medium text-gray-700 mb-2">GitHub URL</label>
                    <input type="url" id="github_url" name="settings[github_url]"
                           value="<?= htmlspecialchars($currentSettings['github_url'] ?? '') ?>"
                           class="w-full px-3 py-2 border border-gray-300 focus:outline-none focus:border-accent"
                           placeholder="https://github.com/prompt2tool">
                    <p class="mt-1 text-sm text-gray-500">GitHub profile or organization URL</p>
                </div>

                <!-- Twitter URL -->
                <div>
                    <label for="twitter_url" class="block text-sm font-medium text-gray-700 mb-2">Twitter URL</label>
                    <input type="url" id="twitter_url" name="settings[twitter_url]"
                           value="<?= htmlspecialchars($currentSettings['twitter_url'] ?? '') ?>"
                           class="w-full px-3 py-2 border border-gray-300 focus:outline-none focus:border-accent"
                           placeholder="https://twitter.com/prompt2tool">
                    <p class="mt-1 text-sm text-gray-500">Twitter profile URL</p>
                </div>

                <!-- LinkedIn URL -->
                <div>
                    <label for="linkedin_url" class="block text-sm font-medium text-gray-700 mb-2">LinkedIn URL</label>
                    <input type="url" id="linkedin_url" name="settings[linkedin_url]"
                           value="<?= htmlspecialchars($currentSettings['linkedin_url'] ?? '') ?>"
                           class="w-full px-3 py-2 border border-gray-300 focus:outline-none focus:border-accent"
                           placeholder="https://linkedin.com/company/prompt2tool">
                    <p class="mt-1 text-sm text-gray-500">LinkedIn company page URL</p>
                </div>

                <!-- Discord URL -->
                <div>
                    <label for="discord_url" class="block text-sm font-medium text-gray-700 mb-2">Discord URL</label>
                    <input type="url" id="discord_url" name="settings[discord_url]"
                           value="<?= htmlspecialchars($currentSettings['discord_url'] ?? '') ?>"
                           class="w-full px-3 py-2 border border-gray-300 focus:outline-none focus:border-accent"
                           placeholder="https://discord.gg/prompt2tool">
                    <p class="mt-1 text-sm text-gray-500">Discord server invite URL</p>
                </div>
            </div>
        </div>

        <!-- Analytics & Tracking -->
        <div class="bg-white shadow border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Analytics & Tracking</h3>
                <p class="mt-1 text-sm text-gray-600">Configure tracking codes and analytics</p>
            </div>
            <div class="p-6 space-y-6">
                <!-- Google Analytics -->
                <div>
                    <label for="google_analytics_id" class="block text-sm font-medium text-gray-700 mb-2">Google Analytics ID</label>
                    <input type="text" id="google_analytics_id" name="settings[google_analytics_id]"
                           value="<?= htmlspecialchars($currentSettings['google_analytics_id']) ?>"
                           class="w-full px-3 py-2 border border-gray-300 focus:outline-none focus:border-accent"
                           placeholder="GA_TRACKING_ID">
                    <p class="mt-1 text-sm text-gray-500">Google Analytics tracking ID</p>
                </div>

                <!-- Google AdSense -->
                <div>
                    <label for="google_adsense_id" class="block text-sm font-medium text-gray-700 mb-2">Google AdSense ID</label>
                    <input type="text" id="google_adsense_id" name="settings[google_adsense_id]"
                           value="<?= htmlspecialchars($currentSettings['google_adsense_id']) ?>"
                           class="w-full px-3 py-2 border border-gray-300 focus:outline-none focus:border-accent"
                           placeholder="ca-pub-xxxxxxxxxxxxxxxxx">
                    <p class="mt-1 text-sm text-gray-500">Google AdSense publisher ID (ca-pub-xxxxxxxxxxxxxxxxx)</p>
                </div>
            </div>
        </div>

        <!-- Google OAuth Configuration -->
        <div class="bg-white shadow border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Google OAuth Login</h3>
                <p class="mt-1 text-sm text-gray-600">Configure Google Sign-In for user authentication</p>
            </div>
            <div class="p-6 space-y-6">
                <!-- Enable Google OAuth -->
                <div>
                    <label class="flex items-center">
                        <input type="checkbox" name="settings[google_oauth_enabled]" value="1"
                               <?= !empty($currentSettings['google_oauth_enabled']) ? 'checked' : '' ?>
                               class="mr-3 w-4 h-4 text-accent bg-gray-100 border-gray-300 focus:ring-accent">
                        <span class="text-sm font-medium text-gray-700">Enable Google OAuth Login</span>
                    </label>
                    <p class="mt-1 text-sm text-gray-500">Allow users to sign in with their Google accounts</p>
                </div>

                <!-- Google OAuth Client ID -->
                <div>
                    <label for="google_oauth_client_id" class="block text-sm font-medium text-gray-700 mb-2">Google OAuth Client ID</label>
                    <input type="text" id="google_oauth_client_id" name="settings[google_oauth_client_id]"
                           value="<?= htmlspecialchars($currentSettings['google_oauth_client_id']) ?>"
                           class="w-full px-3 py-2 border border-gray-300 focus:outline-none focus:border-accent"
                           placeholder="************-abcdefghijklmnopqrstuvwxyz.apps.googleusercontent.com">
                    <p class="mt-1 text-sm text-gray-500">OAuth 2.0 Client ID from Google Cloud Console</p>
                </div>

                <!-- Google OAuth Client Secret -->
                <div>
                    <label for="google_oauth_client_secret" class="block text-sm font-medium text-gray-700 mb-2">Google OAuth Client Secret</label>
                    <input type="password" id="google_oauth_client_secret" name="settings[google_oauth_client_secret]"
                           value="<?= htmlspecialchars($currentSettings['google_oauth_client_secret']) ?>"
                           class="w-full px-3 py-2 border border-gray-300 focus:outline-none focus:border-accent"
                           placeholder="GOCSPX-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx">
                    <p class="mt-1 text-sm text-gray-500">OAuth 2.0 Client Secret (keep this secure)</p>
                </div>

                <!-- Setup Instructions -->
                <div class="bg-blue-50 border border-blue-200 p-4">
                    <h4 class="text-sm font-medium text-blue-900 mb-2">Setup Instructions:</h4>
                    <ol class="text-sm text-blue-800 space-y-1 list-decimal list-inside">
                        <li>Go to <a href="https://console.cloud.google.com/" target="_blank" class="underline">Google Cloud Console</a></li>
                        <li>Create a new project or select existing one</li>
                        <li>Enable the Google+ API</li>
                        <li>Create OAuth 2.0 credentials</li>
                        <li>Add your domain to authorized origins</li>
                        <li>Add redirect URI: <code class="bg-blue-100 px-1"><?= htmlspecialchars($_SERVER['HTTP_HOST'] ?? 'your-domain.com') ?>/auth/google-callback</code></li>
                    </ol>
                </div>
            </div>
        </div>

        <!-- JWT Configuration -->
        <div class="bg-white shadow border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">JWT Configuration</h3>
                <p class="mt-1 text-sm text-gray-600">Configure JWT secret key for API authentication</p>
            </div>
            <div class="p-6 space-y-6">
                <!-- JWT Secret Key -->
                <div>
                    <label for="jwt_secret" class="block text-sm font-medium text-gray-700 mb-2">JWT Secret Key</label>
                    <input type="password" id="jwt_secret" name="settings[jwt_secret]"
                           value="<?= htmlspecialchars($currentSettings['jwt_secret']) ?>"
                           class="w-full px-3 py-2 border border-gray-300 focus:outline-none focus:border-accent"
                           placeholder="Enter a secure random string (minimum 32 characters)">
                    <p class="mt-1 text-sm text-gray-500">Secret key used to sign JWT tokens for API authentication. Keep this secure and change from default value.</p>
                </div>

                <!-- JWT Info -->
                <div class="bg-yellow-50 border border-yellow-200 p-4">
                    <h4 class="text-sm font-medium text-yellow-900 mb-2">Important:</h4>
                    <ul class="text-sm text-yellow-800 space-y-1 list-disc list-inside">
                        <li>Use a strong, random string of at least 32 characters</li>
                        <li>Changing this key will invalidate all existing JWT tokens</li>
                        <li>Users will need to log in again after changing this key</li>
                        <li>Keep this key secret and secure</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Save Button -->
        <div class="flex justify-end">
            <button type="submit" class="px-6 py-2 bg-accent text-white hover:bg-blue-600 transition-colors">
                Save Settings
            </button>
        </div>
    </form>
</div>

<script>
// Character counters and validation
document.addEventListener('DOMContentLoaded', function() {
    const titleInput = document.getElementById('site_title');
    const descInput = document.getElementById('site_description');
    const titleCounter = document.getElementById('title-counter');
    const descCounter = document.getElementById('description-counter');
    const titleWarning = document.getElementById('title-warning');
    const descWarning = document.getElementById('description-warning');

    // Title counter and validation
    function updateTitleCounter() {
        const length = titleInput.value.length;
        titleCounter.textContent = length + '/60';

        if (length > 60) {
            titleCounter.className = 'text-sm font-medium text-red-600';
            titleWarning.classList.remove('hidden');
            titleInput.classList.add('border-red-500');
        } else if (length > 50) {
            titleCounter.className = 'text-sm font-medium text-orange-600';
            titleWarning.classList.add('hidden');
            titleInput.classList.remove('border-red-500');
        } else {
            titleCounter.className = 'text-sm font-medium text-green-600';
            titleWarning.classList.add('hidden');
            titleInput.classList.remove('border-red-500');
        }
    }

    // Description counter and validation
    function updateDescCounter() {
        const length = descInput.value.length;
        descCounter.textContent = length + '/160';

        if (length > 160) {
            descCounter.className = 'text-sm font-medium text-red-600';
            descWarning.classList.remove('hidden');
            descInput.classList.add('border-red-500');
        } else if (length > 150) {
            descCounter.className = 'text-sm font-medium text-orange-600';
            descWarning.classList.add('hidden');
            descInput.classList.remove('border-red-500');
        } else {
            descCounter.className = 'text-sm font-medium text-green-600';
            descWarning.classList.add('hidden');
            descInput.classList.remove('border-red-500');
        }
    }

    // Initialize counters
    updateTitleCounter();
    updateDescCounter();

    // Add event listeners
    titleInput.addEventListener('input', updateTitleCounter);
    descInput.addEventListener('input', updateDescCounter);
});

// Image upload handler
function handleImageUpload(input) {
    if (input.files && input.files[0]) {
        const file = input.files[0];

        // Validate file type
        if (!file.type.match('image.*')) {
            alert('Please select an image file (PNG, JPG, or JPEG).');
            return;
        }

        // Validate file size (max 5MB)
        if (file.size > 5 * 1024 * 1024) {
            alert('File size must be less than 5MB.');
            return;
        }

        // Create FormData for upload
        const formData = new FormData();
        formData.append('og_image', file);

        // Show loading state
        const uploadArea = input.closest('.flex');
        uploadArea.innerHTML = '<div class="flex items-center justify-center"><i class="fas fa-spinner fa-spin text-gray-400 text-2xl"></i><span class="ml-2 text-gray-500">Uploading...</span></div>';

        // Upload file
        fetch('ajax/upload-og-image.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update hidden input with uploaded image path
                document.getElementById('og_image').value = data.path;

                // Update manual URL input if it exists
                const urlInput = document.getElementById('og_image_url');
                if (urlInput) {
                    urlInput.value = data.path;
                }

                // Show success message and reload after delay
                const uploadArea = document.querySelector('.cursor-pointer').closest('div');
                uploadArea.innerHTML = '<div class="flex items-center justify-center text-green-600"><i class="fas fa-check-circle text-2xl mr-2"></i><span>Upload successful! Reloading...</span></div>';

                setTimeout(() => {
                    location.reload();
                }, 1500);
            } else {
                alert('Upload failed: ' + (data.message || 'Unknown error'));
                location.reload();
            }
        })
        .catch(error => {
            console.error('Upload error:', error);
            alert('Upload failed. Please try again.');
            location.reload();
        });
    }
}

// Remove OG Image
function removeOGImage() {
    if (confirm('Are you sure you want to remove the current Open Graph image?')) {
        document.getElementById('og_image').value = '';
        location.reload();
    }
}

// Update OG Image from URL
function updateOGImageFromURL(url) {
    // Update hidden input for form submission
    document.getElementById('og_image').value = url;

    // If URL is cleared, also clear the visible input
    if (!url) {
        document.getElementById('og_image_url').value = '';
    }
}

// Sync URL input with hidden field on page load
document.addEventListener('DOMContentLoaded', function() {
    const urlInput = document.getElementById('og_image_url');
    const hiddenInput = document.getElementById('og_image');

    if (urlInput && hiddenInput) {
        // Sync on input change
        urlInput.addEventListener('input', function() {
            hiddenInput.value = this.value;
        });

        // Ensure they start in sync
        if (hiddenInput.value && !urlInput.value) {
            urlInput.value = hiddenInput.value;
        }
    }
});
</script>
