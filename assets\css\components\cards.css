/* 卡片组件样式 - 零圆角设计 */

/* 基础卡片样式 */
.card {
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    padding: 1.5rem;
    transition: all 0.3s ease;
    border-radius: 0 !important; /* 零圆角强制 */
    position: relative;
    overflow: hidden;
}

.card:hover {
    border-color: var(--accent-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(78, 115, 223, 0.15);
}

/* 卡片头部 */
.card-header {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
    background-color: var(--secondary-color);
    margin: -1.5rem -1.5rem 1.5rem -1.5rem;
}

.card-header h3 {
    margin: 0;
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-color);
}

/* 卡片内容 */
.card-body {
    padding: 0;
}

.card-content {
    color: var(--text-color);
    line-height: 1.6;
}

/* 卡片底部 */
.card-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid var(--border-color);
    background-color: var(--secondary-color);
    margin: 1.5rem -1.5rem -1.5rem -1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* 工具卡片特殊样式 */
.tool-card {
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.tool-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, transparent, var(--accent-color), transparent);
    transition: left 0.5s ease;
}

.tool-card:hover::before {
    left: 100%;
}

.tool-card .tool-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    transition: transform 0.3s ease;
    display: block;
}

.tool-card:hover .tool-icon {
    transform: scale(1.1) rotate(5deg);
}

.tool-card .tool-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-color);
    transition: color 0.3s ease;
}

.tool-card:hover .tool-title {
    color: var(--accent-color);
}

.tool-card .tool-description {
    color: var(--text-color);
    opacity: 0.8;
    font-size: 0.875rem;
    line-height: 1.5;
    margin-bottom: 1rem;
}

.tool-card .tool-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.75rem;
    color: var(--text-color);
    opacity: 0.6;
}

.tool-card .tool-action {
    display: flex;
    align-items: center;
    color: var(--accent-color);
    font-weight: 500;
    font-size: 0.875rem;
    transition: transform 0.3s ease;
}

.tool-card:hover .tool-action {
    transform: translateX(4px);
}

/* 特色卡片 */
.card-featured {
    background: linear-gradient(135deg, var(--accent-color), #6c8cff);
    border-color: var(--accent-color);
    color: white;
}

.card-featured .tool-title,
.card-featured .tool-description,
.card-featured .tool-meta {
    color: white;
}

.card-featured:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 8px 25px rgba(78, 115, 223, 0.3);
}

/* 紧凑卡片 */
.card-compact {
    padding: 1rem;
}

.card-compact .tool-icon {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.card-compact .tool-title {
    font-size: 1rem;
    margin-bottom: 0.25rem;
}

.card-compact .tool-description {
    font-size: 0.75rem;
    margin-bottom: 0.5rem;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* 列表卡片 */
.card-list {
    padding: 1rem 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.card-list:hover {
    transform: translateX(4px);
    background-color: var(--hover-bg);
}

.card-list .tool-icon {
    font-size: 2rem;
    margin: 0;
    flex-shrink: 0;
}

.card-list .tool-content {
    flex: 1;
    min-width: 0;
}

.card-list .tool-title {
    font-size: 1.125rem;
    margin-bottom: 0.25rem;
}

.card-list .tool-description {
    font-size: 0.875rem;
    margin: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.card-list .tool-stats {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 0.25rem;
    flex-shrink: 0;
}

.card-list .tool-action {
    margin: 0;
}

/* 卡片标签 */
.card-badge {
    position: absolute;
    top: 0.75rem;
    right: 0.75rem;
    background-color: var(--accent-color);
    color: white;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.card-badge.badge-new {
    background-color: var(--success-color);
}

.card-badge.badge-featured {
    background-color: var(--warning-color);
    color: var(--primary-color);
}

.card-badge.badge-popular {
    background-color: var(--danger-color);
}

/* 卡片标签组 */
.card-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 1rem;
}

.card-tag {
    background-color: var(--secondary-color);
    color: var(--text-color);
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    border: 1px solid var(--border-color);
    transition: all 0.2s ease;
}

.card-tag:hover {
    background-color: var(--hover-bg);
    border-color: var(--accent-color);
    color: var(--accent-color);
}

/* 卡片加载状态 */
.card-loading {
    opacity: 0.6;
    pointer-events: none;
    position: relative;
}

.card-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 2rem;
    height: 2rem;
    margin: -1rem 0 0 -1rem;
    border: 2px solid var(--border-color);
    border-top: 2px solid var(--accent-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 卡片网格布局 */
.cards-grid {
    display: grid;
    gap: 1.5rem;
}

.cards-grid-1 {
    grid-template-columns: 1fr;
}

.cards-grid-2 {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.cards-grid-3 {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.cards-grid-4 {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}

/* 响应式设计 */
@media (max-width: 768px) {
    .card {
        padding: 1rem;
    }
    
    .card-header,
    .card-footer {
        padding: 0.75rem 1rem;
        margin: -1rem -1rem 1rem -1rem;
    }
    
    .card-footer {
        margin: 1rem -1rem -1rem -1rem;
        flex-direction: column;
        gap: 0.5rem;
        align-items: stretch;
    }
    
    .tool-card .tool-icon {
        font-size: 2rem;
    }
    
    .tool-card .tool-title {
        font-size: 1.125rem;
    }
    
    .cards-grid {
        gap: 1rem;
    }
    
    .cards-grid-2,
    .cards-grid-3,
    .cards-grid-4 {
        grid-template-columns: 1fr;
    }
    
    .card-list {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
    }
    
    .card-list .tool-stats {
        align-items: flex-start;
        flex-direction: row;
        justify-content: space-between;
        width: 100%;
    }
}
