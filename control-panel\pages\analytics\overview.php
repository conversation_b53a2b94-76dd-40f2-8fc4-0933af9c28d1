<?php
/**
 * 数据分析概览页面
 * 显示平台的关键统计数据和图表
 */

// 定义根目录
define('ROOT_PATH', dirname(dirname(dirname(__DIR__))));

// 加载认证中间件
require_once dirname(__DIR__) . '/../auth/middleware.php';

// 权限检查
if (!hasPermission('analytics.view')) {
    header('HTTP/1.1 403 Forbidden');
    exit('Access denied');
}

// 获取时间范围参数
$timeRange = $_GET['range'] ?? '7d';
$validRanges = ['1d', '7d', '30d', '90d'];
if (!in_array($timeRange, $validRanges)) {
    $timeRange = '7d';
}

// 模拟统计数据（实际应该从数据库获取）
$stats = [
    'total_visits' => 15680,
    'visits_change' => 12.5,
    'tool_usage' => 8420,
    'usage_change' => 8.3,
    'unique_users' => 3240,
    'users_change' => 15.7,
    'avg_session' => '4m 32s',
    'session_change' => -2.1,
    'bounce_rate' => 32.4,
    'bounce_change' => -5.2
];

// 模拟图表数据
$chartData = [
    'visits' => [
        'labels' => ['Jan 8', 'Jan 9', 'Jan 10', 'Jan 11', 'Jan 12', 'Jan 13', 'Jan 14', 'Jan 15'],
        'data' => [1200, 1350, 1180, 1420, 1680, 1520, 1750, 1890]
    ],
    'tools' => [
        'labels' => ['HTML Formatter', 'CSS Minifier', 'Image Converter', 'QR Generator', 'Others'],
        'data' => [2340, 1890, 1650, 1420, 1120]
    ]
];

// 热门工具数据 - 从数据库获取真实数据
try {
    $popularTools = $pdo->query("
        SELECT name,
               CASE
                   WHEN category_id = 1 THEN 'Development'
                   WHEN category_id = 2 THEN 'Design'
                   WHEN category_id = 3 THEN 'Utilities'
                   WHEN category_id = 4 THEN 'Security'
                   ELSE 'Other'
               END as category,
               view_count,
               '+0%' as trend
        FROM pt_tool
        WHERE status = 'active' AND view_count > 0
        ORDER BY view_count DESC
        LIMIT 5
    ")->fetchAll();

    // 如果没有数据，使用示例数据
    if (empty($popularTools)) {
        $popularTools = [
            ['name' => 'HTML Formatter', 'category' => 'Development', 'view_count' => 0, 'trend' => '+0%'],
            ['name' => 'CSS Minifier', 'category' => 'Development', 'view_count' => 0, 'trend' => '+0%'],
            ['name' => 'Image Converter', 'category' => 'Design', 'view_count' => 0, 'trend' => '+0%'],
            ['name' => 'QR Generator', 'category' => 'Utilities', 'view_count' => 0, 'trend' => '+0%'],
            ['name' => 'Password Generator', 'category' => 'Security', 'view_count' => 0, 'trend' => '+0%']
        ];
    }
} catch (Exception $e) {
    // 如果查询失败，使用示例数据
    $popularTools = [
        ['name' => 'HTML Formatter', 'category' => 'Development', 'view_count' => 0, 'trend' => '+0%'],
        ['name' => 'CSS Minifier', 'category' => 'Development', 'view_count' => 0, 'trend' => '+0%'],
        ['name' => 'Image Converter', 'category' => 'Design', 'view_count' => 0, 'trend' => '+0%'],
        ['name' => 'QR Generator', 'category' => 'Utilities', 'view_count' => 0, 'trend' => '+0%'],
        ['name' => 'Password Generator', 'category' => 'Security', 'view_count' => 0, 'trend' => '+0%']
    ];
}

// 流量来源数据
$trafficSources = [
    ['source' => 'Direct', 'visits' => 6720, 'percentage' => 42.8],
    ['source' => 'Google Search', 'visits' => 4704, 'percentage' => 30.0],
    ['source' => 'Social Media', 'visits' => 2352, 'percentage' => 15.0],
    ['source' => 'Referral', 'visits' => 1568, 'percentage' => 10.0],
    ['source' => 'Email', 'visits' => 336, 'percentage' => 2.2]
];

$currentPage = 'analytics';
include_once dirname(__DIR__) . '/../components/layout/header.php';
?>

<div class="min-h-screen bg-gray-100">
    <!-- 侧边栏 -->
    <div class="fixed inset-y-0 left-0 w-64 bg-gray-900">
        <?php include_once dirname(__DIR__) . '/../components/layout/sidebar.php'; ?>
    </div>
    
    <!-- 主内容区 -->
    <div class="ml-64">
        <!-- 顶部导航栏 -->
        <div class="bg-white shadow-sm border-b border-gray-200">
            <div class="px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-16">
                    <div class="flex items-center">
                        <h1 class="text-2xl font-semibold text-gray-900">Analytics Overview</h1>
                    </div>
                    
                    <!-- 时间范围选择器 -->
                    <div class="flex items-center space-x-2">
                        <?php foreach ($validRanges as $range): ?>
                            <?php
                            $labels = ['1d' => '24 Hours', '7d' => '7 Days', '30d' => '30 Days', '90d' => '90 Days'];
                            $isActive = $timeRange === $range;
                            ?>
                            <a href="?range=<?= $range ?>" 
                               class="px-3 py-2 text-sm transition-colors <?= $isActive ? 'bg-accent text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300' ?>">
                                <?= $labels[$range] ?>
                            </a>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 页面内容 -->
        <div class="p-6">
            <!-- 统计卡片 -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
                <!-- 总访问量 -->
                <div class="bg-white border border-gray-200 p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-600">Total Visits</p>
                            <p class="text-2xl font-bold text-gray-900"><?= number_format($stats['total_visits']) ?></p>
                            <p class="text-sm <?= $stats['visits_change'] >= 0 ? 'text-green-600' : 'text-red-600' ?>">
                                <?= $stats['visits_change'] >= 0 ? '+' : '' ?><?= number_format($stats['visits_change'], 1) ?>%
                            </p>
                        </div>
                        <div class="text-3xl text-blue-500">
                            <i class="fas fa-eye"></i>
                        </div>
                    </div>
                </div>
                
                <!-- 工具使用量 -->
                <div class="bg-white border border-gray-200 p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-600">Tool Usage</p>
                            <p class="text-2xl font-bold text-gray-900"><?= number_format($stats['tool_usage']) ?></p>
                            <p class="text-sm <?= $stats['usage_change'] >= 0 ? 'text-green-600' : 'text-red-600' ?>">
                                <?= $stats['usage_change'] >= 0 ? '+' : '' ?><?= number_format($stats['usage_change'], 1) ?>%
                            </p>
                        </div>
                        <div class="text-3xl text-green-500">
                            <i class="fas fa-tools"></i>
                        </div>
                    </div>
                </div>
                
                <!-- 独立用户 -->
                <div class="bg-white border border-gray-200 p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-600">Unique Users</p>
                            <p class="text-2xl font-bold text-gray-900"><?= number_format($stats['unique_users']) ?></p>
                            <p class="text-sm <?= $stats['users_change'] >= 0 ? 'text-green-600' : 'text-red-600' ?>">
                                <?= $stats['users_change'] >= 0 ? '+' : '' ?><?= number_format($stats['users_change'], 1) ?>%
                            </p>
                        </div>
                        <div class="text-3xl text-yellow-500">
                            <i class="fas fa-users"></i>
                        </div>
                    </div>
                </div>
                
                <!-- 平均会话时长 -->
                <div class="bg-white border border-gray-200 p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-600">Avg. Session</p>
                            <p class="text-2xl font-bold text-gray-900"><?= $stats['avg_session'] ?></p>
                            <p class="text-sm <?= $stats['session_change'] >= 0 ? 'text-green-600' : 'text-red-600' ?>">
                                <?= $stats['session_change'] >= 0 ? '+' : '' ?><?= number_format($stats['session_change'], 1) ?>%
                            </p>
                        </div>
                        <div class="text-3xl text-purple-500">
                            <i class="fas fa-clock"></i>
                        </div>
                    </div>
                </div>
                
                <!-- 跳出率 -->
                <div class="bg-white border border-gray-200 p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-600">Bounce Rate</p>
                            <p class="text-2xl font-bold text-gray-900"><?= number_format($stats['bounce_rate'], 1) ?>%</p>
                            <p class="text-sm <?= $stats['bounce_change'] <= 0 ? 'text-green-600' : 'text-red-600' ?>">
                                <?= $stats['bounce_change'] >= 0 ? '+' : '' ?><?= number_format($stats['bounce_change'], 1) ?>%
                            </p>
                        </div>
                        <div class="text-3xl text-red-500">
                            <i class="fas fa-chart-line"></i>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 图表区域 -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                <!-- 访问趋势图 -->
                <div class="bg-white border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Visit Trends</h3>
                    <canvas id="visitsChart" width="400" height="200"></canvas>
                </div>
                
                <!-- 工具使用排行 -->
                <div class="bg-white border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Top Tools Usage</h3>
                    <canvas id="toolsChart" width="400" height="200"></canvas>
                </div>
            </div>
            
            <!-- 详细数据表格 -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- 热门工具 -->
                <div class="bg-white border border-gray-200">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900">Popular Tools</h3>
                    </div>
                    <div class="p-6">
                        <div class="space-y-4">
                            <?php foreach ($popularTools as $index => $tool): ?>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-accent text-white text-sm flex items-center justify-center mr-3 font-semibold">
                                        <?= $index + 1 ?>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900"><?= htmlspecialchars($tool['name']) ?></div>
                                        <div class="text-xs text-gray-500"><?= htmlspecialchars($tool['category']) ?></div>
                                    </div>
                                </div>
                                <div class="text-right">
                                    <div class="text-sm font-medium text-gray-900"><?= number_format($tool['view_count']) ?></div>
                                    <div class="text-xs text-green-600"><?= htmlspecialchars($tool['trend']) ?></div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
                
                <!-- 流量来源 -->
                <div class="bg-white border border-gray-200">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900">Traffic Sources</h3>
                    </div>
                    <div class="p-6">
                        <div class="space-y-4">
                            <?php foreach ($trafficSources as $source): ?>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <div class="w-3 h-3 bg-accent mr-3"></div>
                                    <div class="text-sm font-medium text-gray-900"><?= htmlspecialchars($source['source']) ?></div>
                                </div>
                                <div class="text-right">
                                    <div class="text-sm font-medium text-gray-900"><?= number_format($source['visits']) ?></div>
                                    <div class="text-xs text-gray-500"><?= number_format($source['percentage'], 1) ?>%</div>
                                </div>
                            </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// 访问趋势图
const visitsCtx = document.getElementById('visitsChart').getContext('2d');
new Chart(visitsCtx, {
    type: 'line',
    data: {
        labels: <?= json_encode($chartData['visits']['labels']) ?>,
        datasets: [{
            label: 'Visits',
            data: <?= json_encode($chartData['visits']['data']) ?>,
            borderColor: '#4e73df',
            backgroundColor: 'rgba(78, 115, 223, 0.1)',
            tension: 0.4,
            fill: true
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                grid: {
                    color: '#e5e7eb'
                }
            },
            x: {
                grid: {
                    color: '#e5e7eb'
                }
            }
        }
    }
});

// 工具使用排行图
const toolsCtx = document.getElementById('toolsChart').getContext('2d');
new Chart(toolsCtx, {
    type: 'doughnut',
    data: {
        labels: <?= json_encode($chartData['tools']['labels']) ?>,
        datasets: [{
            data: <?= json_encode($chartData['tools']['data']) ?>,
            backgroundColor: [
                '#4e73df',
                '#1cc88a',
                '#36b9cc',
                '#f6c23e',
                '#e74a3b'
            ]
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom',
                labels: {
                    padding: 20,
                    usePointStyle: true
                }
            }
        }
    }
});
</script>

<?php include_once dirname(__DIR__) . '/../components/layout/footer.php'; ?>
