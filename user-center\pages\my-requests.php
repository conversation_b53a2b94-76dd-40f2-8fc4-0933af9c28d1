<?php
/**
 * 我的需求页面
 */

// 检查用户登录状态
if (!isset($_SESSION['user_id'])) {
    header('Location: /auth/login');
    exit;
}

$currentPage = 'my-requests';
$pageTitle = 'My Tool Requests';

// 获取工具分类
$categoryStmt = $pdo->query("SELECT slug, name FROM pt_tool_category WHERE status = 'active' ORDER BY sort_order ASC, name ASC");
$dbCategories = $categoryStmt->fetchAll();

$categories = [];
foreach ($dbCategories as $cat) {
    $categories[$cat['slug']] = $cat['name'];
}
$categories['other'] = 'Other';

// 获取筛选参数
$status = $_GET['status'] ?? 'all';
$currentPage = max(1, intval($_GET['p'] ?? 1));
$perPage = 5;
$offset = ($currentPage - 1) * $perPage;

// 构建查询条件
$whereConditions = ["r.user_id = ?"];
$params = [$_SESSION['user_id']];

if ($status !== 'all') {
    $whereConditions[] = "r.status = ?";
    $params[] = $status;
}

$whereClause = 'WHERE ' . implode(' AND ', $whereConditions);

// 获取用户的需求列表
$sql = "
    SELECT r.*, 
           COUNT(rv.id) as vote_count
    FROM pt_user_requests r
    LEFT JOIN pt_request_votes rv ON r.id = rv.request_id AND rv.vote_type = 'up'
    {$whereClause}
    GROUP BY r.id
    ORDER BY r.created_at DESC
    LIMIT {$perPage} OFFSET {$offset}
";

$requests = $pdo->prepare($sql);
$requests->execute($params);
$requestsList = $requests->fetchAll();

// 获取总数用于分页
$countSql = "SELECT COUNT(*) FROM pt_user_requests r {$whereClause}";
$countStmt = $pdo->prepare($countSql);
$countStmt->execute($params);
$totalRequests = $countStmt->fetchColumn();
$totalPages = ceil($totalRequests / $perPage);

// 获取统计数据
$stats = $pdo->prepare("
    SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
        SUM(CASE WHEN status = 'reviewing' THEN 1 ELSE 0 END) as reviewing,
        SUM(CASE WHEN status = 'accepted' THEN 1 ELSE 0 END) as accepted,
        SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed,
        SUM(CASE WHEN status = 'rejected' THEN 1 ELSE 0 END) as rejected
    FROM pt_user_requests 
    WHERE user_id = ?
");
$stats->execute([$_SESSION['user_id']]);
$userStats = $stats->fetch();

// 状态颜色配置
$statusColors = [
    'pending' => 'bg-yellow-100 text-yellow-800',
    'reviewing' => 'bg-blue-100 text-blue-800',
    'accepted' => 'bg-green-100 text-green-800',
    'rejected' => 'bg-red-100 text-red-800',
    'completed' => 'bg-purple-100 text-purple-800'
];

// 状态中文名称
$statusNames = [
    'pending' => 'Pending',
    'reviewing' => 'Under Review',
    'accepted' => 'Accepted',
    'rejected' => 'Rejected',
    'completed' => 'Completed'
];
?>

<div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex justify-between items-center">
        <h1 class="text-2xl font-bold text-gray-900">My Tool Requests</h1>
        <button onclick="openSubmitModal()" class="inline-flex items-center px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
            </svg>
            Submit New Request
        </button>
    </div>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-2 md:grid-cols-6 gap-4">
        <div class="bg-white p-4 rounded-lg shadow">
            <div class="text-2xl font-bold text-gray-900"><?= $userStats['total'] ?></div>
            <div class="text-sm text-gray-600">Total</div>
        </div>
        <div class="bg-white p-4 rounded-lg shadow">
            <div class="text-2xl font-bold text-yellow-600"><?= $userStats['pending'] ?></div>
            <div class="text-sm text-gray-600">Pending</div>
        </div>
        <div class="bg-white p-4 rounded-lg shadow">
            <div class="text-2xl font-bold text-blue-600"><?= $userStats['reviewing'] ?></div>
            <div class="text-sm text-gray-600">Reviewing</div>
        </div>
        <div class="bg-white p-4 rounded-lg shadow">
            <div class="text-2xl font-bold text-green-600"><?= $userStats['accepted'] ?></div>
            <div class="text-sm text-gray-600">Accepted</div>
        </div>
        <div class="bg-white p-4 rounded-lg shadow">
            <div class="text-2xl font-bold text-purple-600"><?= $userStats['completed'] ?></div>
            <div class="text-sm text-gray-600">Completed</div>
        </div>
        <div class="bg-white p-4 rounded-lg shadow">
            <div class="text-2xl font-bold text-red-600"><?= $userStats['rejected'] ?></div>
            <div class="text-sm text-gray-600">Rejected</div>
        </div>
    </div>

    <!-- 筛选器 -->
    <div class="bg-white p-4 rounded-lg shadow">
        <div class="flex items-center space-x-4">
            <label class="text-sm font-medium text-gray-700">Filter by Status:</label>
            <select id="status-filter" class="border border-gray-300 rounded-md px-3 py-2 text-sm" onchange="filterRequests()">
                <option value="all" <?= $status === 'all' ? 'selected' : '' ?>>All Status</option>
                <option value="pending" <?= $status === 'pending' ? 'selected' : '' ?>>Pending</option>
                <option value="reviewing" <?= $status === 'reviewing' ? 'selected' : '' ?>>Under Review</option>
                <option value="accepted" <?= $status === 'accepted' ? 'selected' : '' ?>>Accepted</option>
                <option value="completed" <?= $status === 'completed' ? 'selected' : '' ?>>Completed</option>
                <option value="rejected" <?= $status === 'rejected' ? 'selected' : '' ?>>Rejected</option>
            </select>
        </div>
    </div>

    <!-- 需求列表 -->
    <div class="bg-white rounded-lg shadow">
        <?php if (empty($requestsList)): ?>
            <div class="bg-white border border-gray-200 rounded-lg shadow-sm">
                <div class="p-12 text-center">
                    <div class="text-gray-400 mb-6">
                        <svg class="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-3">No requests found</h3>
                    <p class="text-gray-600 mb-6 max-w-md mx-auto">You haven't submitted any tool requests yet. Start by submitting your first request to help improve our platform.</p>
                    <button onclick="openSubmitModal()" class="inline-flex items-center px-6 py-3 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors font-medium">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                        </svg>
                        Submit Your First Request
                    </button>
                </div>
            </div>
        <?php else: ?>
            <div class="space-y-4">
                <?php foreach ($requestsList as $request): ?>
                    <div class="bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200">
                        <div class="p-6">
                            <div class="flex justify-between items-start">
                                <div class="flex-1">
                                    <div class="flex items-center flex-wrap gap-3 mb-3">
                                        <h3 class="text-lg font-semibold text-gray-900">
                                            <a href="/request/<?= htmlspecialchars($request['slug']) ?>"
                                               class="hover:text-blue-600 transition-colors duration-200"
                                               target="_blank"
                                               rel="noopener noreferrer">
                                                <?= htmlspecialchars($request['title']) ?>
                                            </a>
                                        </h3>
                                        <span class="px-3 py-1 text-xs font-medium rounded-full <?= $statusColors[$request['status']] ?>">
                                            <?= $statusNames[$request['status']] ?>
                                        </span>
                                        <span class="px-3 py-1 text-xs font-medium bg-blue-50 text-blue-700 rounded-full border border-blue-200">
                                            <?= ucfirst($request['category']) ?>
                                        </span>
                                    </div>

                                    <p class="text-gray-700 mb-4 leading-relaxed"><?= htmlspecialchars($request['description']) ?></p>

                                    <div class="flex items-center flex-wrap gap-4 text-sm text-gray-500">
                                        <span class="flex items-center">
                                            <i class="fas fa-calendar-alt mr-1"></i>
                                            Submitted: <?= date('M j, Y', strtotime($request['created_at'])) ?>
                                        </span>
                                        <span class="flex items-center">
                                            <i class="fas fa-thumbs-up mr-1"></i>
                                            Votes: <?= $request['vote_count'] ?>
                                        </span>
                                        <?php if ($request['processed_at']): ?>
                                            <span class="flex items-center">
                                                <i class="fas fa-clock mr-1"></i>
                                                Updated: <?= date('M j, Y', strtotime($request['processed_at'])) ?>
                                            </span>
                                        <?php endif; ?>
                                    </div>

                                    <?php if (!empty($request['admin_reply'])): ?>
                                        <div class="mt-4 p-4 bg-amber-50 border border-amber-200 rounded-lg">
                                            <div class="flex items-center mb-2">
                                                <i class="fas fa-reply text-amber-600 mr-2"></i>
                                                <p class="text-sm font-semibold text-amber-800">Official Response</p>
                                            </div>
                                            <p class="text-sm text-gray-700 leading-relaxed"><?= htmlspecialchars($request['admin_reply']) ?></p>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <div class="ml-6 flex flex-col items-end justify-start">
                                    <?php if ($request['status'] === 'pending'): ?>
                                        <div class="flex flex-col space-y-2">
                                            <button onclick="editRequest(<?= $request['id'] ?>)"
                                                    class="px-3 py-1.5 text-sm text-blue-600 hover:text-white hover:bg-blue-600 border border-blue-600 rounded-md transition-colors duration-200">
                                                <i class="fas fa-edit mr-1"></i>Edit
                                            </button>
                                            <button onclick="deleteRequest(<?= $request['id'] ?>)"
                                                    class="px-3 py-1.5 text-sm text-red-600 hover:text-white hover:bg-red-600 border border-red-600 rounded-md transition-colors duration-200">
                                                <i class="fas fa-trash mr-1"></i>Delete
                                            </button>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>

    <!-- 分页 -->
    <?php if ($totalPages > 1): ?>
        <div class="flex justify-center">
            <nav class="flex space-x-2">
                <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                    <a href="?page=my-requests&status=<?= $status ?>&p=<?= $i ?>"
                       class="px-3 py-2 text-sm <?= $i === $currentPage ? 'bg-blue-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-50' ?> border border-gray-300 rounded-md">
                        <?= $i ?>
                    </a>
                <?php endfor; ?>
            </nav>
        </div>
    <?php endif; ?>
</div>

<!-- 提交需求模态框 -->
<div id="submitModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 hidden">
    <div class="relative top-20 mx-auto p-6 border w-full max-w-md shadow-lg rounded-lg bg-white">
        <div class="mb-4">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-gray-900">Submit New Request</h3>
                <button onclick="closeSubmitModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>

        <form id="submitForm">
            <!-- URL分析区域 -->
            <div class="mb-6 p-4 bg-gray-50 border border-gray-200 rounded-md">
                <h4 class="text-md font-medium text-gray-900 mb-3">🔍 Analyze Existing Tool (Optional)</h4>
                <p class="text-sm text-gray-600 mb-3">Enter a tool website URL to automatically generate request details</p>
                <div class="flex gap-3">
                    <input type="url" id="tool-url" placeholder="Enter tool website URL..."
                           class="flex-1 px-3 py-2 bg-white border border-gray-300 text-gray-900 rounded-md focus:border-blue-500 focus:outline-none">
                    <button type="button" id="analyze-btn"
                            class="inline-flex items-center px-6 py-2 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                        </svg>
                        Analyze
                    </button>
                </div>
                <div id="analysis-status" class="mt-2 text-sm text-gray-500"></div>
            </div>

            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Title</label>
                <input type="text" name="title" minlength="5" maxlength="200" required
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                <div class="text-xs text-gray-500 mt-1">
                    <span id="title-count">0</span>/200 characters (min 5)
                </div>
            </div>

            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Category</label>
                <select name="category" required
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <?php foreach ($categories as $value => $label): ?>
                        <option value="<?= $value ?>"><?= htmlspecialchars($label) ?></option>
                    <?php endforeach; ?>
                </select>
            </div>

            <div class="mb-6">
                <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                <textarea name="description" rows="4" minlength="20" maxlength="1000" required
                          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          placeholder="Describe your feature request in detail..."></textarea>
                <div class="text-xs text-gray-500 mt-1">
                    <span id="description-count">0</span>/1000 characters (min 20)
                </div>
            </div>

            <div class="flex justify-end space-x-3">
                <button type="button" onclick="closeSubmitModal()" class="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300">
                    Cancel
                </button>
                <button type="submit" class="inline-flex items-center px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                    </svg>
                    Submit Request
                </button>
            </div>
        </form>
    </div>
</div>

<!-- 编辑需求模态框 -->
<div id="editModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 hidden">
    <div class="relative top-20 mx-auto p-6 border w-full max-w-md shadow-lg rounded-lg bg-white">
        <div class="mb-4">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-gray-900">Edit Request</h3>
                <button onclick="closeEditModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>

        <form id="editForm">
            <input type="hidden" name="request_id" id="edit-request-id">

            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Title</label>
                <input type="text" name="title" id="edit-title" minlength="5" maxlength="200" required
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                <div class="text-xs text-gray-500 mt-1">
                    <span id="edit-title-count">0</span>/200 characters (min 5)
                </div>
            </div>

            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Category</label>
                <select name="category" id="edit-category" required
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <?php foreach ($categories as $value => $label): ?>
                        <option value="<?= $value ?>"><?= htmlspecialchars($label) ?></option>
                    <?php endforeach; ?>
                </select>
            </div>

            <div class="mb-6">
                <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                <textarea name="description" id="edit-description" rows="4" minlength="20" maxlength="1000" required
                          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          placeholder="Describe your feature request in detail..."></textarea>
                <div class="text-xs text-gray-500 mt-1">
                    <span id="edit-description-count">0</span>/1000 characters (min 20)
                </div>
            </div>

            <div class="flex justify-end space-x-3">
                <button type="button" onclick="closeEditModal()" class="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300">
                    Cancel
                </button>
                <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                    Update Request
                </button>
            </div>
        </form>
    </div>
</div>

<!-- 删除确认模态框 -->
<div id="deleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 hidden">
    <div class="relative top-20 mx-auto p-6 border w-full max-w-md shadow-lg rounded-lg bg-white">
        <div class="text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
                <i class="fas fa-exclamation-triangle text-red-600"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">Delete Request</h3>
            <p class="text-sm text-gray-500 mb-6">Are you sure you want to delete this request? This action cannot be undone.</p>

            <div class="flex justify-center space-x-3">
                <button onclick="closeDeleteModal()" class="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300">
                    Cancel
                </button>
                <button onclick="confirmDelete()" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700">
                    Delete
                </button>
            </div>
        </div>
    </div>
</div>

<!-- AI分析进度模态框 -->
<div id="ai-analysis-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50 hidden">
    <div class="relative top-20 mx-auto p-6 border w-full max-w-lg shadow-lg rounded-lg bg-white">
        <div class="text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 mb-4">
                <i class="fas fa-robot text-blue-600"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">AI Tool Analysis</h3>
            <p class="text-sm text-gray-500 mb-6">Analyzing the tool website to generate request details...</p>

            <!-- 进度步骤 -->
            <div class="space-y-4 mb-6">
                <!-- 步骤1：抓取网站内容 -->
                <div class="flex items-center space-x-3">
                    <div id="step-1-icon">
                        <div class="w-5 h-5 border-2 border-gray-600 rounded-full"></div>
                    </div>
                    <span id="step-1-text" class="text-gray-500">Crawling website content...</span>
                </div>

                <!-- 步骤2：提取主要内容 -->
                <div class="flex items-center space-x-3">
                    <div id="step-2-icon">
                        <div class="w-5 h-5 border-2 border-gray-600 rounded-full"></div>
                    </div>
                    <span id="step-2-text" class="text-gray-500">Extracting main content...</span>
                </div>

                <!-- 步骤3：AI分析 -->
                <div class="flex items-center space-x-3">
                    <div id="step-3-icon">
                        <div class="w-5 h-5 border-2 border-gray-600 rounded-full"></div>
                    </div>
                    <span id="step-3-text" class="text-gray-500">AI analyzing content...</span>
                </div>

                <!-- 步骤4：生成请求详情 -->
                <div class="flex items-center space-x-3">
                    <div id="step-4-icon">
                        <div class="w-5 h-5 border-2 border-gray-600 rounded-full"></div>
                    </div>
                    <span id="step-4-text" class="text-gray-500">Generating request details...</span>
                </div>
            </div>

            <div class="flex justify-center">
                <button onclick="cancelAnalysis()" class="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300">
                    Cancel
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Toast 提示系统
function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `fixed top-4 right-4 z-50 px-4 py-3 rounded-md shadow-lg transition-all duration-300 ${
        type === 'success' ? 'bg-green-100 text-green-800 border border-green-200' :
        type === 'error' ? 'bg-red-100 text-red-800 border border-red-200' :
        type === 'warning' ? 'bg-yellow-100 text-yellow-800 border border-yellow-200' :
        'bg-blue-100 text-blue-800 border border-blue-200'
    }`;
    toast.textContent = message;

    document.body.appendChild(toast);

    // 自动移除
    setTimeout(() => {
        toast.style.opacity = '0';
        setTimeout(() => toast.remove(), 300);
    }, 4000);
}

function filterRequests() {
    const status = document.getElementById('status-filter').value;
    window.location.href = '?page=my-requests&status=' + status;
}

function editRequest(requestId) {
    // 获取需求数据
    fetch(`/ajax/get-request.php?id=${requestId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 填充编辑表单
                document.getElementById('edit-request-id').value = data.request.id;
                document.getElementById('edit-title').value = data.request.title;
                document.getElementById('edit-category').value = data.request.category;
                document.getElementById('edit-description').value = data.request.description;

                // 更新字符计数
                updateEditCharacterCounts();

                // 显示模态框
                document.getElementById('editModal').classList.remove('hidden');

                // 聚焦到标题输入框
                setTimeout(() => {
                    document.getElementById('edit-title').focus();
                }, 100);
            } else {
                showToast('Failed to load request data: ' + data.message, 'error');
            }
        })
        .catch(error => {
            showToast('Network error occurred', 'error');
        });
}

let deleteRequestId = null;

function deleteRequest(requestId) {
    deleteRequestId = requestId;
    document.getElementById('deleteModal').classList.remove('hidden');
}

// 模态框相关函数
function openSubmitModal() {
    document.getElementById('submitModal').classList.remove('hidden');
    // 聚焦到第一个输入框
    setTimeout(() => {
        document.querySelector('input[name="title"]').focus();
    }, 100);
}

function closeSubmitModal() {
    document.getElementById('submitModal').classList.add('hidden');
    document.getElementById('submitForm').reset();
    updateCharacterCounts();
}

// ESC键关闭模态框
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        const submitModal = document.getElementById('submitModal');
        const editModal = document.getElementById('editModal');
        const deleteModal = document.getElementById('deleteModal');

        if (!submitModal.classList.contains('hidden')) {
            closeSubmitModal();
        } else if (!editModal.classList.contains('hidden')) {
            closeEditModal();
        } else if (!deleteModal.classList.contains('hidden')) {
            closeDeleteModal();
        }
    }
});

// 编辑模态框相关函数
function closeEditModal() {
    document.getElementById('editModal').classList.add('hidden');
    document.getElementById('editForm').reset();
}

// 删除模态框相关函数
function closeDeleteModal() {
    document.getElementById('deleteModal').classList.add('hidden');
    deleteRequestId = null;
}

function confirmDelete() {
    if (!deleteRequestId) return;

    const deleteBtn = document.querySelector('#deleteModal button[onclick="confirmDelete()"]');
    const originalText = deleteBtn.textContent;

    deleteBtn.textContent = 'Deleting...';
    deleteBtn.disabled = true;

    fetch('/ajax/delete-request.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `id=${deleteRequestId}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('Request deleted successfully', 'success');
            closeDeleteModal();
            setTimeout(() => window.location.reload(), 1000);
        } else {
            showToast('Failed to delete request: ' + data.message, 'error');
        }
    })
    .catch(error => {
        showToast('Network error occurred', 'error');
    })
    .finally(() => {
        deleteBtn.textContent = originalText;
        deleteBtn.disabled = false;
    });
}

// 点击模态框外部关闭
document.getElementById('submitModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeSubmitModal();
    }
});

document.getElementById('editModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeEditModal();
    }
});

document.getElementById('deleteModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeDeleteModal();
    }
});

// 字符计数功能
function updateCharacterCounts() {
    const titleInput = document.querySelector('input[name="title"]');
    const descriptionInput = document.querySelector('textarea[name="description"]');

    if (titleInput) {
        document.getElementById('title-count').textContent = titleInput.value.length;
    }

    if (descriptionInput) {
        document.getElementById('description-count').textContent = descriptionInput.value.length;
    }
}

function updateEditCharacterCounts() {
    const titleInput = document.getElementById('edit-title');
    const descriptionInput = document.getElementById('edit-description');

    if (titleInput) {
        document.getElementById('edit-title-count').textContent = titleInput.value.length;
    }

    if (descriptionInput) {
        document.getElementById('edit-description-count').textContent = descriptionInput.value.length;
    }
}

// 绑定字符计数事件
document.addEventListener('DOMContentLoaded', function() {
    const titleInput = document.querySelector('input[name="title"]');
    const descriptionInput = document.querySelector('textarea[name="description"]');

    if (titleInput) {
        titleInput.addEventListener('input', updateCharacterCounts);
    }

    if (descriptionInput) {
        descriptionInput.addEventListener('input', updateCharacterCounts);
    }

    // 编辑表单字符计数事件
    const editTitleInput = document.getElementById('edit-title');
    const editDescriptionInput = document.getElementById('edit-description');

    if (editTitleInput) {
        editTitleInput.addEventListener('input', updateEditCharacterCounts);
    }

    if (editDescriptionInput) {
        editDescriptionInput.addEventListener('input', updateEditCharacterCounts);
    }

    // 编辑表单提交处理
    document.getElementById('editForm').addEventListener('submit', async function(e) {
        e.preventDefault();

        const formData = new FormData(this);
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;

        submitBtn.textContent = 'Updating...';
        submitBtn.disabled = true;

        try {
            const response = await fetch('/ajax/update-request.php', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                showToast('Request updated successfully!', 'success');
                closeEditModal();
                setTimeout(() => window.location.reload(), 1000);
            } else {
                showToast('Error: ' + result.message, 'error');
            }
        } catch (error) {
            showToast('Network error occurred. Please try again.', 'error');
        } finally {
            submitBtn.textContent = originalText;
            submitBtn.disabled = false;
        }
    });

    // 提交表单处理
    document.getElementById('submitForm').addEventListener('submit', async function(e) {
        e.preventDefault();

        const formData = new FormData(this);
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;

        submitBtn.textContent = 'Submitting...';
        submitBtn.disabled = true;

        try {
            const response = await fetch('/ajax/submit-request.php', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                showToast('Request submitted successfully!', 'success');
                closeSubmitModal();
                // 刷新页面显示新提交的需求
                setTimeout(() => window.location.reload(), 1000);
            } else {
                showToast('Error: ' + result.message, 'error');
            }
        } catch (error) {
            showToast('Network error occurred. Please try again.', 'error');
        } finally {
            submitBtn.textContent = originalText;
            submitBtn.disabled = false;
        }
    });
});

// URL分析功能
let analysisAbortController = null;

document.getElementById('analyze-btn').addEventListener('click', async function() {
    const url = document.getElementById('tool-url').value.trim();
    const statusDiv = document.getElementById('analysis-status');
    const btn = this;

    if (!url) {
        statusDiv.textContent = '❌ Please enter a valid URL';
        statusDiv.className = 'mt-2 text-sm text-red-400';
        return;
    }

    // 验证URL格式
    try {
        new URL(url);
    } catch (e) {
        statusDiv.textContent = '❌ Please enter a valid URL format';
        statusDiv.className = 'mt-2 text-sm text-red-400';
        return;
    }

    // 先检查配额是否足够，避免显示进度模态框
    btn.disabled = true;
    btn.textContent = 'Checking quota...';
    statusDiv.textContent = '🔍 Checking quota availability...';
    statusDiv.className = 'mt-2 text-sm text-blue-400';

    try {
        // 检查配额
        const quotaResponse = await fetch('../../api/check-quota.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                action: 'tool_analysis' // 自定义检查10配额
            })
        });

        const quotaResult = await quotaResponse.json();

        if (!quotaResult.success) {
            statusDiv.textContent = '❌ Failed to check quota. Please try again.';
            statusDiv.className = 'mt-2 text-sm text-red-400';
            btn.disabled = false;
            btn.textContent = 'Analyze';
            return;
        }

        // 检查是否有足够的10配额
        const userQuota = quotaResult.current_quota;
        if (userQuota.used + 10 > userQuota.total) {
            statusDiv.textContent = '❌ Insufficient quota! Tool analysis requires 10 quota. Please upgrade your subscription plan.';
            statusDiv.className = 'mt-2 text-sm text-red-400';
            btn.disabled = false;
            btn.textContent = 'Analyze';
            return;
        }

    } catch (error) {
        statusDiv.textContent = '❌ Failed to check quota. Please try again.';
        statusDiv.className = 'mt-2 text-sm text-red-400';
        btn.disabled = false;
        btn.textContent = 'Analyze';
        return;
    }

    // 配额检查通过，显示模态框并开始分析
    showAnalysisModal();
    btn.textContent = 'Analyzing...';
    statusDiv.textContent = '';

    // 创建AbortController用于取消请求
    analysisAbortController = new AbortController();

    try {
        // 模拟前3步进度
        await simulateInitialProgress();

        // 开始第4步：发送真正的API请求
        updateProgressStep(4, 'active', 'Generating request details...');

        const response = await fetch('/ajax/analyze-tool.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ url: url }),
            signal: analysisAbortController.signal
        });

        const result = await response.json();

        if (result.success) {
            // 第4步完成
            updateProgressStep(4, 'completed');

            // 稍等一下让用户看到完成状态
            await new Promise(resolve => setTimeout(resolve, 500));

            hideAnalysisModal();

            // 自动填充表单
            if (result.data.title) {
                document.querySelector('input[name="title"]').value = result.data.title;
            }
            if (result.data.category) {
                document.querySelector('select[name="category"]').value = result.data.category;
            }
            if (result.data.description) {
                document.querySelector('textarea[name="description"]').value = result.data.description;
            }

            // 更新字符计数
            updateCharacterCounts();

            statusDiv.textContent = '✅ Analysis completed and form filled automatically';
            statusDiv.className = 'mt-2 text-sm text-green-600';

            // 清空URL输入框
            document.getElementById('tool-url').value = '';
        } else {
            hideAnalysisModal();
            statusDiv.textContent = '❌ ' + (result.message || 'Analysis failed');
            statusDiv.className = 'mt-2 text-sm text-red-400';
        }
    } catch (error) {
        if (error.name === 'AbortError') {
            statusDiv.textContent = '⏹️ Analysis cancelled';
            statusDiv.className = 'mt-2 text-sm text-yellow-400';
        } else {
            hideAnalysisModal();
            statusDiv.textContent = '❌ Network error occurred';
            statusDiv.className = 'mt-2 text-sm text-red-400';
        }
    } finally {
        btn.disabled = false;
        btn.textContent = 'Analyze';
        analysisAbortController = null;
    }
});

// 模态框控制函数
function showAnalysisModal() {
    document.getElementById('ai-analysis-modal').classList.remove('hidden');
    // 重置所有步骤状态
    resetProgressSteps();
    // 开始第一步
    updateProgressStep(1, 'active', 'Crawling website content...');
}

function hideAnalysisModal() {
    document.getElementById('ai-analysis-modal').classList.add('hidden');
}

function resetProgressSteps() {
    for (let i = 1; i <= 4; i++) {
        const icon = document.getElementById(`step-${i}-icon`);
        const text = document.getElementById(`step-${i}-text`);

        icon.innerHTML = '<div class="w-5 h-5 border-2 border-gray-600 rounded-full"></div>';
        text.className = 'text-gray-500';
    }
}

function updateProgressStep(step, status, message) {
    const icon = document.getElementById(`step-${step}-icon`);
    const text = document.getElementById(`step-${step}-text`);

    if (status === 'active') {
        icon.innerHTML = '<div class="w-5 h-5 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>';
        text.className = 'text-blue-600';
        text.textContent = message;
    } else if (status === 'completed') {
        icon.innerHTML = '<div class="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center"><i class="fas fa-check text-white text-xs"></i></div>';
        text.className = 'text-green-600';
    }
}

async function simulateInitialProgress() {
    // 第一步：抓取网站内容
    await new Promise(resolve => setTimeout(resolve, 1000));
    updateProgressStep(1, 'completed');
    updateProgressStep(2, 'active', 'Extracting main content...');

    // 第二步：提取主要内容
    await new Promise(resolve => setTimeout(resolve, 800));
    updateProgressStep(2, 'completed');
    updateProgressStep(3, 'active', 'AI analyzing content...');

    // 第三步：AI分析
    await new Promise(resolve => setTimeout(resolve, 1500));
    updateProgressStep(3, 'completed');

    // 注意：第4步不在这里完成，而是在真正的API响应完成后
}

// 取消分析
function cancelAnalysis() {
    if (analysisAbortController) {
        analysisAbortController.abort();
    }
    hideAnalysisModal();

    // 重置按钮状态
    const btn = document.getElementById('analyze-btn');
    btn.disabled = false;
    btn.textContent = 'Analyze';

    // 显示取消状态
    const statusDiv = document.getElementById('analysis-status');
    statusDiv.textContent = '⏹️ Analysis cancelled';
    statusDiv.className = 'mt-2 text-sm text-yellow-400';
}
</script>
