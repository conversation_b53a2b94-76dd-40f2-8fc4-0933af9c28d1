<?php
/**
 * 添加收藏API
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

session_start();

// 检查用户是否已登录
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

// 使用统一的数据库连接
require_once dirname(dirname(dirname(__DIR__))) . '/includes/database-connection.php';

try {
    // 获取请求数据
    $input = json_decode(file_get_contents('php://input'), true);
    
    $userId = $_SESSION['user_id'];
    $toolId = intval($input['tool_id'] ?? 0);
    
    if ($toolId <= 0) {
        throw new Exception('Invalid tool ID');
    }
    
    // 检查工具是否存在
    $stmt = $pdo->prepare("SELECT id FROM pt_tool WHERE id = ?");
    $stmt->execute([$toolId]);
    if (!$stmt->fetch()) {
        throw new Exception('Tool not found');
    }
    
    // 检查收藏表是否存在
    $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    if (!in_array('pt_user_favorites', $tables)) {
        // 如果表不存在，创建它
        $pdo->exec("
            CREATE TABLE pt_user_favorites (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                tool_id INT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE KEY unique_user_tool (user_id, tool_id),
                FOREIGN KEY (user_id) REFERENCES pt_member(id) ON DELETE CASCADE,
                FOREIGN KEY (tool_id) REFERENCES pt_tool(id) ON DELETE CASCADE
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        ");
    }
    
    // 添加收藏（使用INSERT IGNORE避免重复）
    $stmt = $pdo->prepare("INSERT IGNORE INTO pt_user_favorites (user_id, tool_id) VALUES (?, ?)");
    $stmt->execute([$userId, $toolId]);
    
    $affectedRows = $stmt->rowCount();
    
    if ($affectedRows > 0) {
        echo json_encode([
            'success' => true,
            'message' => 'Tool added to favorites'
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Tool is already in favorites'
        ]);
    }
    
} catch (Exception $e) {
    error_log("Add favorite error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
