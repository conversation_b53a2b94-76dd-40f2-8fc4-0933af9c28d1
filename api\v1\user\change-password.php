<?php
/**
 * 修改密码API
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

session_start();

// 检查用户是否已登录
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

// 使用统一的数据库连接
require_once dirname(dirname(dirname(__DIR__))) . '/includes/database-connection.php';

try {
    // 获取请求数据
    $input = json_decode(file_get_contents('php://input'), true);
    
    $userId = $_SESSION['user_id'];
    $currentPassword = $input['current_password'] ?? '';
    $newPassword = $input['new_password'] ?? '';
    $confirmPassword = $input['confirm_password'] ?? '';
    
    // 验证必需字段
    if (empty($currentPassword) || empty($newPassword) || empty($confirmPassword)) {
        throw new Exception('All password fields are required');
    }
    
    // 验证新密码匹配
    if ($newPassword !== $confirmPassword) {
        throw new Exception('New passwords do not match');
    }
    
    // 验证新密码强度
    if (strlen($newPassword) < 8) {
        throw new Exception('New password must be at least 8 characters long');
    }
    
    if (!preg_match('/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/', $newPassword)) {
        throw new Exception('New password must contain at least one uppercase letter, one lowercase letter, one number, and one special character');
    }
    
    // 获取用户当前密码
    $stmt = $pdo->prepare("SELECT password FROM pt_member WHERE id = ?");
    $stmt->execute([$userId]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        throw new Exception('User not found');
    }
    
    // 检查是否是Google用户
    if (empty($user['password'])) {
        throw new Exception('Cannot change password for Google accounts');
    }
    
    // 验证当前密码
    if (!password_verify($currentPassword, $user['password'])) {
        throw new Exception('Current password is incorrect');
    }
    
    // 哈希新密码
    $hashedNewPassword = password_hash($newPassword, PASSWORD_DEFAULT);
    
    // 更新密码
    $stmt = $pdo->prepare("
        UPDATE pt_member 
        SET password = ?, updated_at = NOW()
        WHERE id = ?
    ");
    $stmt->execute([$hashedNewPassword, $userId]);
    
    // 记录活动
    try {
        $stmt = $pdo->prepare("
            INSERT INTO pt_member_activity_log (user_id, action, description, ip_address, user_agent, created_at)
            VALUES (?, 'password_changed', 'User changed password', ?, ?, NOW())
        ");
        $stmt->execute([
            $userId,
            $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        ]);
    } catch (Exception $e) {
        // 日志记录失败不影响主流程
    }
    
    echo json_encode([
        'success' => true,
        'message' => 'Password changed successfully'
    ]);
    
} catch (Exception $e) {
    error_log("Change password error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
