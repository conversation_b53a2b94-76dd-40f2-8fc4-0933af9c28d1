# 允许访问图片文件
<FilesMatch "\.(jpg|jpeg|png|gif|webp)$">
    Require all granted
</FilesMatch>

# 禁止访问PHP和其他危险文件
<FilesMatch "\.(php|phtml|php3|php4|php5|pl|py|jsp|asp|sh|cgi)$">
    Require all denied
</FilesMatch>

# 禁止访问配置文件
<FilesMatch "\.(htaccess|htpasswd|ini|log|sh|inc|bak)$">
    Require all denied
</FilesMatch>

# 禁止执行PHP脚本
php_flag engine off

# 禁止目录浏览
Options -Indexes

# 设置缓存头
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/webp "access plus 1 month"
</IfModule>

# 压缩图片
<IfModule mod_deflate.c>
    <FilesMatch "\.(jpg|jpeg|png|gif|webp)$">
        SetOutputFilter DEFLATE
    </FilesMatch>
</IfModule>
