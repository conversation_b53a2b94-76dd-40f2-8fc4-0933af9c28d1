<?php
/**
 * AI Tool File Analysis API
 * Analyzes uploaded tool files and generated files to extract metadata
 */

// 安全检查 - 定义必要的常量
if (!defined('SECURE_ACCESS')) {
    define('SECURE_ACCESS', true);
}

// 禁用输出缓冲
if (ob_get_level()) {
    ob_end_clean();
}

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// 检查用户权限
session_start();
if (!isset($_SESSION['admin_logged_in']) && !isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode([
        'success' => false,
        'message' => 'Unauthorized access. Please login first.',
        'debug' => [
            'admin_logged_in' => isset($_SESSION['admin_logged_in']) ? $_SESSION['admin_logged_in'] : 'not set',
            'user_id' => isset($_SESSION['user_id']) ? $_SESSION['user_id'] : 'not set',
            'session_id' => session_id()
        ]
    ]);
    exit;
}

// 定义根路径
define('MGMT_ROOT', __DIR__);
define('ROOT_PATH', dirname(__DIR__));

// 使用统一的数据库连接
require_once ROOT_PATH . '/includes/database-connection.php';

try {
    $fileContent = '';
    $fileName = '';
    
    // 检查是否是生成的文件路径
    $input = json_decode(file_get_contents('php://input'), true);
    if ($input && isset($input['generated_file_path'])) {
        // 处理生成的文件
        $filePath = $input['generated_file_path'];
        $fullPath = MGMT_ROOT . '/' . $filePath;
        
        if (!file_exists($fullPath)) {
            throw new Exception('Generated file not found: ' . $filePath);
        }
        
        $fileContent = file_get_contents($fullPath);
        $fileName = basename($filePath);
    } else {
        // 处理上传的文件
        if (!isset($_FILES['file']) || $_FILES['file']['error'] !== UPLOAD_ERR_OK) {
            throw new Exception('No file uploaded or upload error');
        }
        
        $uploadedFile = $_FILES['file'];
        $fileName = $uploadedFile['name'];
        $fileContent = file_get_contents($uploadedFile['tmp_name']);
    }
    
    if (empty($fileContent)) {
        throw new Exception('File is empty or could not be read');
    }
    
    // 分析文件内容
    $analysis = analyzeToolFile($fileContent, $fileName);

    echo json_encode([
        'success' => true,
        'data' => $analysis
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Analysis failed. Please try again.'
    ]);
}

/**
 * 分析工具文件内容
 */
function analyzeToolFile($content, $fileName) {
    // 基本信息提取
    $analysis = [
        'name' => '',
        'slug' => '',
        'category' => 'Utilities',
        'icon' => '🔧',
        'description' => '',
        'tags' => [],
        'confidence' => 0.9
    ];

    // 从文件名推断工具名
    $baseName = pathinfo($fileName, PATHINFO_FILENAME);
    $analysis['name'] = ucwords(str_replace(['-', '_'], ' ', $baseName));
    $analysis['slug'] = strtolower(str_replace([' ', '_'], '-', $baseName));

    // 分析文件内容
    $lines = explode("\n", $content);

    foreach ($lines as $line) {
        $line = trim($line);

        // 查找工具名称 - 更多模式
        if (preg_match('/\*\s*(.+?)\s*Tool\s*Page/i', $line, $matches) ||
            preg_match('/<title[^>]*>(.+?)<\/title>/i', $line, $matches) ||
            preg_match('/<h1[^>]*>(.+?)<\/h1>/i', $line, $matches)) {
            $analysis['name'] = trim(strip_tags($matches[1]));
            $analysis['slug'] = strtolower(str_replace([' ', '_'], '-', $analysis['name']));
        }

        // 查找描述 - 更多模式
        if (empty($analysis['description'])) {
            if (preg_match('/description.*?[\'"]([^\'\"]{10,})[\'"]/', $line, $matches) ||
                preg_match('/<meta[^>]*name=[\'"]description[\'"][^>]*content=[\'"]([^\'\"]{10,})[\'"]/', $line, $matches) ||
                preg_match('/<p[^>]*class=[\'"][^\'\"]*description[^\'\"]*[\'"][^>]*>([^<]{10,})<\/p>/i', $line, $matches)) {
                $desc = trim(strip_tags($matches[1]));
                if (strlen($desc) > 10 && $desc !== '=>') {
                    $analysis['description'] = $desc;
                }
            }
        }

        // 查找关键词 - 更多模式
        if (empty($analysis['tags'])) {
            if (preg_match('/keywords.*?[\'"]([^\'\"]{5,})[\'"]/', $line, $matches) ||
                preg_match('/<meta[^>]*name=[\'"]keywords[\'"][^>]*content=[\'"]([^\'\"]{5,})[\'"]/', $line, $matches)) {
                $keywords = trim($matches[1]);
                if (strlen($keywords) > 5 && $keywords !== '=>') {
                    $analysis['tags'] = array_map('trim', explode(',', $keywords));
                }
            }
        }
    }

    // 智能内容分析
    $contentLower = strtolower($content);

    // 更详细的分类检测
    if (strpos($contentLower, 'game') !== false || strpos($contentLower, 'snake') !== false ||
        strpos($contentLower, 'puzzle') !== false || strpos($contentLower, 'play') !== false) {
        $analysis['category'] = 'Games';
        $analysis['icon'] = '🎮';
        if (empty($analysis['description'])) {
            $analysis['description'] = 'An interactive game tool for entertainment and fun';
        }
        if (empty($analysis['tags'])) {
            $analysis['tags'] = ['game', 'entertainment', 'interactive', 'fun'];
        }
    } elseif (strpos($contentLower, 'password') !== false || strpos($contentLower, 'security') !== false ||
              strpos($contentLower, 'encrypt') !== false || strpos($contentLower, 'hash') !== false) {
        $analysis['category'] = 'Security';
        $analysis['icon'] = '🔐';
        if (empty($analysis['description']) || $analysis['description'] === '=>') {
            $analysis['description'] = 'A security tool for password generation and protection';
        }
        if (empty($analysis['tags']) || (is_array($analysis['tags']) && in_array('=>', $analysis['tags']))) {
            $analysis['tags'] = ['security', 'password', 'encryption', 'protection'];
        }
    } elseif (strpos($contentLower, 'qr') !== false || strpos($contentLower, 'barcode') !== false) {
        $analysis['category'] = 'Utilities';
        $analysis['icon'] = '📱';
        if (empty($analysis['description'])) {
            $analysis['description'] = 'A QR code generator and scanner tool';
        }
        if (empty($analysis['tags'])) {
            $analysis['tags'] = ['qr-code', 'generator', 'scanner', 'mobile'];
        }
    } elseif (strpos($contentLower, 'calculator') !== false || strpos($contentLower, 'math') !== false ||
              strpos($contentLower, 'calculate') !== false || strpos($contentLower, 'number') !== false) {
        $analysis['category'] = 'Utilities';
        $analysis['icon'] = '🧮';
        if (empty($analysis['description'])) {
            $analysis['description'] = 'A mathematical calculator tool for various calculations';
        }
        if (empty($analysis['tags'])) {
            $analysis['tags'] = ['calculator', 'math', 'calculation', 'numbers'];
        }
    } elseif (strpos($contentLower, 'text') !== false || strpos($contentLower, 'string') !== false ||
              strpos($contentLower, 'format') !== false || strpos($contentLower, 'convert') !== false) {
        $analysis['category'] = 'Text Tools';
        $analysis['icon'] = '📝';
        if (empty($analysis['description'])) {
            $analysis['description'] = 'A text processing and formatting tool';
        }
        if (empty($analysis['tags'])) {
            $analysis['tags'] = ['text', 'formatting', 'processing', 'converter'];
        }
    } elseif (strpos($contentLower, 'color') !== false || strpos($contentLower, 'css') !== false ||
              strpos($contentLower, 'design') !== false || strpos($contentLower, 'style') !== false) {
        $analysis['category'] = 'Design Tools';
        $analysis['icon'] = '🎨';
        if (empty($analysis['description'])) {
            $analysis['description'] = 'A design and styling tool for creative projects';
        }
        if (empty($analysis['tags'])) {
            $analysis['tags'] = ['design', 'color', 'css', 'styling'];
        }
    }

    // 如果仍然没有描述，生成一个更好的默认描述
    if (empty($analysis['description']) || $analysis['description'] === '=>') {
        $analysis['description'] = 'A useful ' . strtolower($analysis['name']) . ' tool for productivity and efficiency';
    }

    // 如果仍然没有标签，生成更相关的标签
    if (empty($analysis['tags']) || (is_array($analysis['tags']) && in_array('=>', $analysis['tags']))) {
        $nameWords = explode(' ', strtolower($analysis['name']));
        $analysis['tags'] = array_merge($nameWords, ['tool', 'utility', 'productivity']);
        $analysis['tags'] = array_unique(array_filter($analysis['tags']));
    }

    // 确保标签是数组格式
    if (is_string($analysis['tags'])) {
        $analysis['tags'] = explode(',', $analysis['tags']);
    }
    $analysis['tags'] = array_map('trim', $analysis['tags']);

    return $analysis;
}
?>
