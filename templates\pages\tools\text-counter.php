<?php
/**
 * Text Counter Tool Page
 * 遵循01-UI设计规范.md - 零圆角设计
 */

$currentPage = 'tool-text-counter';
$breadcrumbs = [
    ['name' => 'Home', 'url' => '/'],
    ['name' => 'Tools', 'url' => '/tools'],
    ['name' => 'Productivity', 'url' => '/tools/productivity'],
    ['name' => 'Text Counter']
];

// 包含动态SEO助手
require_once ROOT_PATH . '/app/helpers/dynamic-seo.php';

// 生成SEO数据 - 优化长度
$seoData = [
    'title' => 'Free Text Counter Online - Prompt2Tool',
    'description' => 'Count characters, words, sentences, and paragraphs instantly. Perfect for writers, students, and content creators.',
    'keywords' => 'text counter, word counter, character counter, sentence counter, paragraph counter, writing tool, text analysis, word count tool',
    'og_title' => 'Free Text Counter Online - Prompt2Tool',
    'og_description' => 'Count characters, words, sentences, and paragraphs instantly. Perfect for writers and content creators.',
    'canonical' => getCurrentURL()
];

// 包含公共头部
include_once ROOT_PATH . '/templates/layout/header.php';
?>

<!-- 工具页面主体 -->
<div class="bg-gray-900 text-white">

    <!-- Hero Section with SEO optimized content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-12">
        <div class="text-center mb-12">
            <div class="flex items-center justify-center mb-4">
                <div class="bg-green-600 p-3 mr-4">
                    <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                </div>
                <div class="text-left">
                    <h1 class="text-4xl font-bold text-white mb-2">Text Counter</h1>
                    <p class="text-xl text-gray-400">Count characters, words, sentences, and paragraphs</p>
                </div>
            </div>
        </div>

        <!-- 工具主体 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- 输入区域 -->
            <div class="bg-gray-800 border border-gray-700 p-6">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-xl font-semibold text-white">Text Input</h2>
                    <div class="flex space-x-2">
                        <button id="clearInput" class="px-3 py-1 bg-red-600 text-white text-sm hover:bg-red-700 transition-colors">
                            Clear
                        </button>
                        <button id="pasteInput" class="px-3 py-1 bg-blue-600 text-white text-sm hover:bg-blue-700 transition-colors">
                            Paste
                        </button>
                    </div>
                </div>
                <textarea 
                    id="textInput" 
                    class="w-full h-96 bg-gray-900 border border-gray-600 text-gray-100 p-4 focus:outline-none focus:border-blue-500 resize-none" 
                    placeholder="Type or paste your text here to start counting..."
                ></textarea>
                
                <!-- 实时统计显示 -->
                <div class="mt-4 grid grid-cols-2 gap-4">
                    <div class="bg-gray-700 p-3 text-center">
                        <div class="text-2xl font-bold text-white" id="charCount">0</div>
                        <div class="text-sm text-gray-300">Characters</div>
                    </div>
                    <div class="bg-gray-700 p-3 text-center">
                        <div class="text-2xl font-bold text-white" id="charCountNoSpaces">0</div>
                        <div class="text-sm text-gray-300">Characters (no spaces)</div>
                    </div>
                </div>
            </div>

            <!-- 输出区域 -->
            <div class="bg-gray-800 border border-gray-700 p-6">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-xl font-semibold text-white">Text Statistics</h2>
                    <div class="flex space-x-2">
                        <div class="relative">
                            <button id="copyStats" class="px-3 py-1 bg-green-600 text-white text-sm hover:bg-green-700 transition-colors">
                                Copy Stats
                            </button>
                            <div id="copyTooltip" class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-700 text-white text-xs rounded pointer-events-none whitespace-nowrap z-50" style="opacity: 0; visibility: hidden; transition: opacity 0.2s, visibility 0.2s;">
                                Copy statistics
                            </div>
                        </div>
                        <div class="relative">
                            <button id="exportStats" class="px-3 py-1 bg-purple-600 text-white text-sm hover:bg-purple-700 transition-colors">
                                Export
                            </button>
                            <div id="exportTooltip" class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-700 text-white text-xs rounded pointer-events-none whitespace-nowrap z-50" style="opacity: 0; visibility: hidden; transition: opacity 0.2s, visibility 0.2s;">
                                Export as JSON
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 详细统计 -->
                <div class="space-y-4">
                    <div class="grid grid-cols-2 gap-4">
                        <div class="bg-gray-700 p-4 text-center">
                            <div class="text-3xl font-bold text-blue-400" id="wordCount">0</div>
                            <div class="text-sm text-gray-300">Words</div>
                        </div>
                        <div class="bg-gray-700 p-4 text-center">
                            <div class="text-3xl font-bold text-green-400" id="sentenceCount">0</div>
                            <div class="text-sm text-gray-300">Sentences</div>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-2 gap-4">
                        <div class="bg-gray-700 p-4 text-center">
                            <div class="text-3xl font-bold text-purple-400" id="paragraphCount">0</div>
                            <div class="text-sm text-gray-300">Paragraphs</div>
                        </div>
                        <div class="bg-gray-700 p-4 text-center">
                            <div class="text-3xl font-bold text-yellow-400" id="lineCount">0</div>
                            <div class="text-sm text-gray-300">Lines</div>
                        </div>
                    </div>
                    
                    <!-- 阅读时间估算 -->
                    <div class="bg-gray-700 p-4">
                        <h4 class="text-lg font-semibold text-white mb-3">Reading Time</h4>
                        <div class="grid grid-cols-2 gap-4 text-center">
                            <div>
                                <div class="text-xl font-bold text-orange-400" id="readingTimeSlow">0m</div>
                                <div class="text-xs text-gray-300">Slow (150 WPM)</div>
                            </div>
                            <div>
                                <div class="text-xl font-bold text-orange-400" id="readingTimeFast">0m</div>
                                <div class="text-xs text-gray-300">Fast (250 WPM)</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 最常用词 -->
                    <div class="bg-gray-700 p-4">
                        <h4 class="text-lg font-semibold text-white mb-3">Most Common Words</h4>
                        <div id="commonWords" class="text-sm text-gray-300">
                            <div class="text-gray-500">Enter text to see common words...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 工具信息 -->
        <div class="mt-12 grid grid-cols-1 md:grid-cols-3 gap-8">
            <div class="bg-gray-800 border border-gray-700 p-6">
                <h4 class="text-lg font-semibold text-white mb-3">Features</h4>
                <ul class="text-gray-300 space-y-2">
                    <li>• Real-time counting</li>
                    <li>• Character count (with/without spaces)</li>
                    <li>• Word count</li>
                    <li>• Sentence count</li>
                    <li>• Paragraph count</li>
                    <li>• Reading time estimation</li>
                </ul>
            </div>
            <div class="bg-gray-800 border border-gray-700 p-6">
                <h4 class="text-lg font-semibold text-white mb-3">Benefits</h4>
                <ul class="text-gray-300 space-y-2">
                    <li>• Meet word limits</li>
                    <li>• Improve writing efficiency</li>
                    <li>• Track progress</li>
                    <li>• Optimize content length</li>
                    <li>• Better time management</li>
                </ul>
            </div>
            <div class="bg-gray-800 border border-gray-700 p-6">
                <h4 class="text-lg font-semibold text-white mb-3">Use Cases</h4>
                <ul class="text-gray-300 space-y-2">
                    <li>• Academic writing</li>
                    <li>• Blog posts</li>
                    <li>• Social media content</li>
                    <li>• Essays and reports</li>
                    <li>• Marketing copy</li>
                    <li>• SEO content</li>
                </ul>
            </div>
        </div>

        <!-- FAQ Section -->
        <div class="px-8 pt-16 pb-16">
            <h2 class="text-3xl font-bold text-white text-center mb-8">Frequently Asked Questions</h2>
            <div class="max-w-4xl mx-auto space-y-6">
                <div class="border-b border-gray-700 pb-6">
                    <h3 class="text-xl font-semibold text-white mb-3">How accurate is the word count?</h3>
                    <p class="text-gray-300">Our word counter uses standard algorithms that count words separated by spaces, punctuation, and line breaks. It's highly accurate and matches most word processors.</p>
                </div>

                <div class="border-b border-gray-700 pb-6">
                    <h3 class="text-xl font-semibold text-white mb-3">Does it count special characters and numbers?</h3>
                    <p class="text-gray-300">Yes, all characters including letters, numbers, punctuation, and special characters are counted. You can see both total characters and characters without spaces.</p>
                </div>

                <div class="border-b border-gray-700 pb-6">
                    <h3 class="text-xl font-semibold text-white mb-3">How is reading time calculated?</h3>
                    <p class="text-gray-300">Reading time is estimated based on average reading speeds: 150 words per minute for slow readers and 250 words per minute for fast readers.</p>
                </div>

                <div class="border-b border-gray-700 pb-6">
                    <h3 class="text-xl font-semibold text-white mb-3">Is my text stored or saved anywhere?</h3>
                    <p class="text-gray-300">No, all text processing is done locally in your browser. Your text is never sent to our servers or stored anywhere, ensuring complete privacy.</p>
                </div>

                <div class="mb-0">
                    <h3 class="text-xl font-semibold text-white mb-3">Can I export the statistics?</h3>
                    <p class="text-gray-300 mb-0">Yes, you can copy the statistics to your clipboard or export them as a JSON file for use in other applications or for record keeping.</p>
                </div>
            </div>
        </div>
    </div>

<script>
// Text Counter functionality
document.addEventListener('DOMContentLoaded', function() {
    const textInput = document.getElementById('textInput');
    const clearInput = document.getElementById('clearInput');
    const pasteInput = document.getElementById('pasteInput');
    const copyStats = document.getElementById('copyStats');
    const exportStats = document.getElementById('exportStats');
    const copyTooltip = document.getElementById('copyTooltip');
    const exportTooltip = document.getElementById('exportTooltip');
    
    // 统计元素
    const charCount = document.getElementById('charCount');
    const charCountNoSpaces = document.getElementById('charCountNoSpaces');
    const wordCount = document.getElementById('wordCount');
    const sentenceCount = document.getElementById('sentenceCount');
    const paragraphCount = document.getElementById('paragraphCount');
    const lineCount = document.getElementById('lineCount');
    const readingTimeSlow = document.getElementById('readingTimeSlow');
    const readingTimeFast = document.getElementById('readingTimeFast');
    const commonWords = document.getElementById('commonWords');
    
    let currentStats = {};

    // 实时统计
    textInput.addEventListener('input', updateStats);

    // 清除按钮
    clearInput.addEventListener('click', () => {
        textInput.value = '';
        updateStats();
    });

    // 粘贴按钮
    pasteInput.addEventListener('click', async () => {
        try {
            const text = await navigator.clipboard.readText();
            textInput.value = text;
            updateStats();
        } catch (error) {
            // Failed to paste
        }
    });

    // 复制统计
    copyStats.addEventListener('click', async () => {
        const statsText = `Text Statistics:
Characters: ${currentStats.characters}
Characters (no spaces): ${currentStats.charactersNoSpaces}
Words: ${currentStats.words}
Sentences: ${currentStats.sentences}
Paragraphs: ${currentStats.paragraphs}
Lines: ${currentStats.lines}
Reading Time (slow): ${currentStats.readingTimeSlow}
Reading Time (fast): ${currentStats.readingTimeFast}`;

        try {
            await navigator.clipboard.writeText(statsText);
            
            if (copyTooltip) {
                copyTooltip.textContent = 'Copied!';
                copyTooltip.style.opacity = '1';
                copyTooltip.style.visibility = 'visible';
                
                setTimeout(() => {
                    copyTooltip.textContent = 'Copy statistics';
                    copyTooltip.style.opacity = '0';
                    copyTooltip.style.visibility = 'hidden';
                }, 2000);
            }
        } catch (error) {
            // Failed to copy
        }
    });

    // 导出统计
    exportStats.addEventListener('click', () => {
        const statsData = {
            text: textInput.value,
            statistics: currentStats,
            timestamp: new Date().toISOString()
        };
        
        const blob = new Blob([JSON.stringify(statsData, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'text-statistics.json';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        if (exportTooltip) {
            exportTooltip.textContent = 'Exported!';
            exportTooltip.style.opacity = '1';
            exportTooltip.style.visibility = 'visible';
            
            setTimeout(() => {
                exportTooltip.textContent = 'Export as JSON';
                exportTooltip.style.opacity = '0';
                exportTooltip.style.visibility = 'hidden';
            }, 2000);
        }
    });

    function updateStats() {
        const text = textInput.value;
        
        // 基本统计
        const characters = text.length;
        const charactersNoSpaces = text.replace(/\s/g, '').length;
        const words = text.trim() === '' ? 0 : text.trim().split(/\s+/).length;
        const sentences = text.trim() === '' ? 0 : text.split(/[.!?]+/).filter(s => s.trim().length > 0).length;
        const paragraphs = text.trim() === '' ? 0 : text.split(/\n\s*\n/).filter(p => p.trim().length > 0).length;
        const lines = text === '' ? 0 : text.split('\n').length;
        
        // 阅读时间计算 (分钟)
        const readingTimeSlowMinutes = Math.ceil(words / 150);
        const readingTimeFastMinutes = Math.ceil(words / 250);
        
        // 更新显示
        charCount.textContent = characters.toLocaleString();
        charCountNoSpaces.textContent = charactersNoSpaces.toLocaleString();
        wordCount.textContent = words.toLocaleString();
        sentenceCount.textContent = sentences.toLocaleString();
        paragraphCount.textContent = paragraphs.toLocaleString();
        lineCount.textContent = lines.toLocaleString();
        readingTimeSlow.textContent = readingTimeSlowMinutes + 'm';
        readingTimeFast.textContent = readingTimeFastMinutes + 'm';
        
        // 更新最常用词
        updateCommonWords(text);
        
        // 保存当前统计
        currentStats = {
            characters,
            charactersNoSpaces,
            words,
            sentences,
            paragraphs,
            lines,
            readingTimeSlow: readingTimeSlowMinutes + 'm',
            readingTimeFast: readingTimeFastMinutes + 'm'
        };
    }

    function updateCommonWords(text) {
        if (text.trim() === '') {
            commonWords.innerHTML = '<div class="text-gray-500">Enter text to see common words...</div>';
            return;
        }
        
        // 提取单词并统计频率
        const words = text.toLowerCase()
            .replace(/[^\w\s]/g, '')
            .split(/\s+/)
            .filter(word => word.length > 2); // 过滤短词
        
        const wordFreq = {};
        words.forEach(word => {
            wordFreq[word] = (wordFreq[word] || 0) + 1;
        });
        
        // 排序并取前5个
        const sortedWords = Object.entries(wordFreq)
            .sort((a, b) => b[1] - a[1])
            .slice(0, 5);
        
        if (sortedWords.length === 0) {
            commonWords.innerHTML = '<div class="text-gray-500">No common words found...</div>';
            return;
        }
        
        const wordsHtml = sortedWords
            .map(([word, count]) => `<span class="inline-block bg-gray-600 px-2 py-1 mr-2 mb-2 text-xs">${word} (${count})</span>`)
            .join('');
        
        commonWords.innerHTML = wordsHtml;
    }

    // 初始化
    updateStats();
});
</script>

</div>

<?php include_once ROOT_PATH . '/templates/layout/footer.php'; ?>
