<?php
/**
 * 工具源文件编辑器
 */

// 定义根目录
define('MGMT_ROOT', __DIR__);
define('ROOT_PATH', dirname(__DIR__));

// 加载认证中间件
require_once __DIR__ . '/auth/middleware.php';

// 权限检查
if (!hasPermission('tools.edit')) {
    header('HTTP/1.1 403 Forbidden');
    exit('Access denied');
}

$toolSlug = $_GET['tool'] ?? '';
if (empty($toolSlug)) {
    exit('Tool slug is required');
}

// 构建源文件路径
$sourceFilePath = ROOT_PATH . '/templates/pages/tools/' . $toolSlug . '.php';
$fileExists = file_exists($sourceFilePath);
$fileContent = $fileExists ? file_get_contents($sourceFilePath) : '';

// 处理保存操作
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['content'])) {
    try {
        // 确保目录存在
        $dir = dirname($sourceFilePath);
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
        }
        
        // 保存文件
        if (file_put_contents($sourceFilePath, $_POST['content']) !== false) {
            $success_message = "File saved successfully";
            $fileContent = $_POST['content'];
            $fileExists = true;
        } else {
            $error_message = "Failed to save file";
        }
    } catch (Exception $e) {
        $error_message = "Error: " . $e->getMessage();
    }
}

// 使用统一的数据库连接
require_once ROOT_PATH . '/includes/database-connection.php';
$stmt = $pdo->prepare("SELECT * FROM pt_tool WHERE slug = ?");
$stmt->execute([$toolSlug]);
$tool = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$tool) {
    exit('Tool not found');
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Source Editor - <?= htmlspecialchars($tool['name']) ?></title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'accent': '#3b82f6',
                    }
                }
            }
        }
    </script>
    <style>
        .editor-container {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="min-h-screen">
        <!-- 头部 -->
        <div class="bg-white shadow-sm border-b border-gray-200 p-4">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-xl font-semibold text-gray-900">
                        Source Editor: <?= htmlspecialchars($tool['name']) ?>
                    </h1>
                    <p class="text-sm text-gray-600">
                        File: /templates/pages/tools/<?= htmlspecialchars($toolSlug) ?>.php
                        <?php if (!$fileExists): ?>
                        <span class="text-red-600">(New File)</span>
                        <?php endif; ?>
                    </p>
                </div>
                
                <div class="flex space-x-2">
                    <button onclick="saveFile()" class="bg-accent text-white px-4 py-2 hover:bg-blue-700">
                        Save File
                    </button>
                    <button onclick="window.close()" class="bg-gray-300 text-gray-700 px-4 py-2 hover:bg-gray-400">
                        Close
                    </button>
                </div>
            </div>
        </div>

        <!-- 消息 -->
        <?php if (isset($success_message)): ?>
        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 m-4">
            <?= htmlspecialchars($success_message) ?>
        </div>
        <?php endif; ?>

        <?php if (isset($error_message)): ?>
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 m-4">
            <?= htmlspecialchars($error_message) ?>
        </div>
        <?php endif; ?>

        <!-- 编辑器 -->
        <div class="p-4">
            <form method="POST" id="editorForm">
                <div class="bg-white border border-gray-300">
                    <div class="bg-gray-50 px-4 py-2 border-b border-gray-300">
                        <div class="flex justify-between items-center">
                            <span class="text-sm font-medium text-gray-700">PHP Source Code</span>
                            <div class="flex space-x-2">
                                <button type="button" onclick="insertTemplate()" class="text-sm text-blue-600 hover:text-blue-800">
                                    Insert Template
                                </button>
                                <button type="button" onclick="formatCode()" class="text-sm text-blue-600 hover:text-blue-800">
                                    Format Code
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <textarea name="content" id="codeEditor" 
                              class="w-full h-96 p-4 font-mono text-sm border-0 focus:outline-none resize-none editor-container"
                              placeholder="Enter PHP code here..."><?= htmlspecialchars($fileContent) ?></textarea>
                </div>
                
                <div class="mt-4 flex justify-between items-center">
                    <div class="text-sm text-gray-600">
                        Lines: <span id="lineCount">1</span> | 
                        Characters: <span id="charCount">0</span>
                    </div>
                    
                    <div class="flex space-x-2">
                        <button type="submit" class="bg-accent text-white px-6 py-2 hover:bg-blue-700">
                            Save Changes
                        </button>
                        <a href="/tools/<?= htmlspecialchars($tool['category_slug'] ?? 'uncategorized') ?>/<?= htmlspecialchars($toolSlug) ?>" 
                           target="_blank" 
                           class="bg-green-600 text-white px-6 py-2 hover:bg-green-700">
                            Preview Tool
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <script>
        const editor = document.getElementById('codeEditor');
        const lineCount = document.getElementById('lineCount');
        const charCount = document.getElementById('charCount');

        // 更新统计信息
        function updateStats() {
            const content = editor.value;
            const lines = content.split('\n').length;
            const chars = content.length;
            
            lineCount.textContent = lines;
            charCount.textContent = chars;
        }

        // 保存文件
        function saveFile() {
            document.getElementById('editorForm').submit();
        }

        // 插入模板
        function insertTemplate() {
            const toolName = '<?= addslashes($tool['name']) ?>';
            const toolSlug = '<?= addslashes($toolSlug) ?>';
            const toolDescription = '<?= addslashes($tool['description'] ?? '') ?>';

            const template = `<?php
/**
 * ${toolName} Tool Page
 */

$currentPage = 'tool-${toolSlug}';
$breadcrumbs = [
    ['name' => 'Home', 'url' => '/'],
    ['name' => 'Tools', 'url' => '/tools'],
    ['name' => '${toolName}']
];

// 包含公共头部
include_once ROOT_PATH . '/templates/layout/header.php';
?>

<section class="min-h-screen bg-black text-white py-20">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- 工具标题 -->
        <div class="text-center mb-12">
            <h1 class="text-4xl md:text-5xl font-bold mb-6">
                ${toolName}
            </h1>
            <p class="text-xl text-gray-400 max-w-2xl mx-auto">
                ${toolDescription}
            </p>
        </div>

        <!-- 工具界面 -->
        <div class="bg-gray-900 border border-gray-800 p-8">
            <div class="space-y-6">
                <!-- 输入区域 -->
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">
                        Input
                    </label>
                    <textarea id="input" 
                              class="w-full h-40 px-4 py-3 bg-black border border-gray-700 text-white focus:border-accent focus:outline-none"
                              placeholder="Enter your content here..."></textarea>
                </div>

                <!-- 操作按钮 -->
                <div class="flex justify-center">
                    <button onclick="processInput()" 
                            class="bg-accent text-white px-8 py-3 hover:bg-blue-700 transition-colors">
                        Process
                    </button>
                </div>

                <!-- 输出区域 -->
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">
                        Output
                    </label>
                    <textarea id="output" 
                              class="w-full h-40 px-4 py-3 bg-black border border-gray-700 text-white focus:border-accent focus:outline-none"
                              readonly></textarea>
                </div>

                <!-- 复制按钮 -->
                <div class="flex justify-center">
                    <button onclick="copyOutput()" 
                            class="bg-green-600 text-white px-6 py-2 hover:bg-green-700 transition-colors">
                        Copy Output
                    </button>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
function processInput() {
    const input = document.getElementById('input').value;
    const output = document.getElementById('output');
    
    // 在这里添加你的处理逻辑
    output.value = input; // 示例：直接复制输入
}

function copyOutput() {
    const output = document.getElementById('output');
    output.select();
    document.execCommand('copy');
    
    // 显示复制成功提示
    const button = event.target;
    const originalText = button.textContent;
    button.textContent = 'Copied!';
    setTimeout(() => {
        button.textContent = originalText;
    }, 2000);
}
</script>

<?php
// 包含公共底部
include_once ROOT_PATH . '/templates/layout/footer.php';
?>\`;

            if (editor.value.trim() === '' || confirm('This will replace the current content. Continue?')) {
                editor.value = template;
                updateStats();
            }
        }

        // 简单的代码格式化
        function formatCode() {
            // 这里可以添加更复杂的格式化逻辑
            alert('Code formatting feature coming soon!');
        }

        // 监听输入变化
        editor.addEventListener('input', updateStats);

        // 初始化统计
        updateStats();

        // 键盘快捷键
        editor.addEventListener('keydown', function(e) {
            // Ctrl+S 保存
            if (e.ctrlKey && e.key === 's') {
                e.preventDefault();
                saveFile();
            }
            
            // Tab键插入4个空格
            if (e.key === 'Tab') {
                e.preventDefault();
                const start = this.selectionStart;
                const end = this.selectionEnd;
                this.value = this.value.substring(0, start) + '    ' + this.value.substring(end);
                this.selectionStart = this.selectionEnd = start + 4;
            }
        });
    </script>
</body>
</html>
