/**
 * 导航组件
 * 处理导航栏交互和移动端菜单
 */
class Navigation {
    constructor() {
        this.init();
    }

    init() {
        this.setupMobileMenu();
        this.setupDropdowns();
        this.setupScrollEffects();
        this.setupSearchModal();
    }

    setupMobileMenu() {
        const mobileMenuBtn = document.getElementById('mobile-menu-btn');
        const mobileMenu = document.getElementById('mobile-menu');

        if (mobileMenuBtn && mobileMenu) {
            mobileMenuBtn.addEventListener('click', () => {
                mobileMenu.classList.toggle('hidden');
                
                // 切换图标
                const icon = mobileMenuBtn.querySelector('svg');
                if (mobileMenu.classList.contains('hidden')) {
                    icon.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>';
                } else {
                    icon.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>';
                }
            });

            // 点击外部关闭菜单
            document.addEventListener('click', (e) => {
                if (!mobileMenuBtn.contains(e.target) && !mobileMenu.contains(e.target)) {
                    mobileMenu.classList.add('hidden');
                }
            });
        }
    }

    setupDropdowns() {
        const dropdowns = document.querySelectorAll('.group');
        
        dropdowns.forEach(dropdown => {
            const menu = dropdown.querySelector('.absolute');
            if (!menu) return;

            let timeout;

            dropdown.addEventListener('mouseenter', () => {
                clearTimeout(timeout);
                menu.classList.remove('opacity-0', 'invisible');
                menu.classList.add('opacity-100', 'visible');
            });

            dropdown.addEventListener('mouseleave', () => {
                timeout = setTimeout(() => {
                    menu.classList.add('opacity-0', 'invisible');
                    menu.classList.remove('opacity-100', 'visible');
                }, 150);
            });
        });
    }

    setupScrollEffects() {
        const navbar = document.querySelector('.navbar');
        if (!navbar) return;

        let lastScrollY = window.scrollY;

        window.addEventListener('scroll', () => {
            const currentScrollY = window.scrollY;

            // 添加滚动阴影
            if (currentScrollY > 10) {
                navbar.classList.add('shadow-lg');
            } else {
                navbar.classList.remove('shadow-lg');
            }

            // 隐藏/显示导航栏
            if (currentScrollY > lastScrollY && currentScrollY > 100) {
                navbar.style.transform = 'translateY(-100%)';
            } else {
                navbar.style.transform = 'translateY(0)';
            }

            lastScrollY = currentScrollY;
        });
    }

    setupSearchModal() {
        const searchTriggers = document.querySelectorAll('[data-modal="search-modal"]');
        const searchModal = document.getElementById('search-modal');
        const searchInput = searchModal?.querySelector('input');

        searchTriggers.forEach(trigger => {
            trigger.addEventListener('click', (e) => {
                e.preventDefault();
                this.openSearchModal();
            });
        });

        // ESC键关闭
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && searchModal && !searchModal.classList.contains('hidden')) {
                this.closeSearchModal();
            }
        });

        // 点击外部关闭
        searchModal?.addEventListener('click', (e) => {
            if (e.target === searchModal) {
                this.closeSearchModal();
            }
        });

        // 搜索功能
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.handleSearch(e.target.value);
            });
        }
    }

    openSearchModal() {
        const searchModal = document.getElementById('search-modal');
        const searchInput = searchModal?.querySelector('input');
        
        if (searchModal) {
            searchModal.classList.remove('hidden');
            searchInput?.focus();
        }
    }

    closeSearchModal() {
        const searchModal = document.getElementById('search-modal');
        if (searchModal) {
            searchModal.classList.add('hidden');
        }
    }

    handleSearch(query) {
        if (query.length < 2) return;

        // 这里可以添加实时搜索功能
        console.log('Searching for:', query);
        
        // 示例：显示搜索建议
        // this.showSearchSuggestions(query);
    }
}

// 自动初始化
if (typeof window !== 'undefined') {
    window.Navigation = Navigation;
}
