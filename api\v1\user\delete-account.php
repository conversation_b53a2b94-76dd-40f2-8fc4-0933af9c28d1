<?php
/**
 * 删除账户API
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

session_start();

// 检查用户是否已登录
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

// 使用统一的数据库连接
require_once dirname(dirname(dirname(__DIR__))) . '/includes/database-connection.php';

try {
    // 获取请求数据
    $input = json_decode(file_get_contents('php://input'), true);
    
    $userId = $_SESSION['user_id'];
    $confirmText = trim($input['confirm_text'] ?? '');
    $passwordConfirm = $input['password_confirm'] ?? '';
    $deletionReason = trim($input['deletion_reason'] ?? '');
    $feedback = trim($input['feedback'] ?? '');
    
    // 验证确认文本
    if ($confirmText !== 'DELETE MY ACCOUNT') {
        throw new Exception('Please type "DELETE MY ACCOUNT" to confirm');
    }
    
    // 获取用户信息
    $stmt = $pdo->prepare("SELECT * FROM pt_member WHERE id = ?");
    $stmt->execute([$userId]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        throw new Exception('User not found');
    }
    
    // 如果不是Google用户，验证密码
    if (!empty($user['password'])) {
        if (empty($passwordConfirm)) {
            throw new Exception('Password confirmation is required');
        }
        
        if (!password_verify($passwordConfirm, $user['password'])) {
            throw new Exception('Password confirmation is incorrect');
        }
    }
    
    // 开始事务
    $pdo->beginTransaction();
    
    try {
        // 记录删除原因（如果提供）
        if (!empty($deletionReason) || !empty($feedback)) {
            $stmt = $pdo->prepare("
                INSERT INTO pt_member_activity_log (user_id, action, description, metadata, ip_address, user_agent, created_at)
                VALUES (?, 'account_deletion_request', 'User requested account deletion', ?, ?, ?, NOW())
            ");
            $metadata = json_encode([
                'reason' => $deletionReason,
                'feedback' => $feedback
            ]);
            $stmt->execute([
                $userId,
                $metadata,
                $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
            ]);
        }
        
        // 删除相关数据（按外键约束顺序）
        
        // 删除用户令牌
        $stmt = $pdo->prepare("DELETE FROM pt_member_tokens WHERE user_id = ?");
        $stmt->execute([$userId]);
        
        // 删除用户通知
        $stmt = $pdo->prepare("DELETE FROM pt_member_notifications WHERE user_id = ?");
        $stmt->execute([$userId]);
        
        // 删除用户会话
        $stmt = $pdo->prepare("DELETE FROM pt_member_sessions WHERE user_id = ?");
        $stmt->execute([$userId]);
        
        // 删除工具使用记录
        $stmt = $pdo->prepare("DELETE FROM pt_tool_usage WHERE user_id = ?");
        $stmt->execute([$userId]);
        
        // 删除活动日志
        $stmt = $pdo->prepare("DELETE FROM pt_member_activity_log WHERE user_id = ?");
        $stmt->execute([$userId]);
        
        // 最后删除用户主记录
        $stmt = $pdo->prepare("DELETE FROM pt_member WHERE id = ?");
        $stmt->execute([$userId]);
        
        // 提交事务
        $pdo->commit();
        
        // 销毁会话
        $_SESSION = array();
        if (ini_get("session.use_cookies")) {
            $params = session_get_cookie_params();
            setcookie(session_name(), '', time() - 42000,
                $params["path"], $params["domain"],
                $params["secure"], $params["httponly"]
            );
        }
        session_destroy();
        
        // 删除记住我cookie
        if (isset($_COOKIE['remember_token'])) {
            setcookie('remember_token', '', time() - 3600, '/', '', false, true);
        }
        
        echo json_encode([
            'success' => true,
            'message' => 'Account deleted successfully'
        ]);
        
    } catch (Exception $e) {
        // 回滚事务
        $pdo->rollBack();
        throw $e;
    }
    
} catch (Exception $e) {
    error_log("Delete account error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
