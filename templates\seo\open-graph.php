<?php
/**
 * Open Graph标签模板
 * 为社交媒体分享优化
 */

/**
 * 生成Open Graph标签
 */
function generateOpenGraphTags($data) {
    $defaults = [
        'title' => 'Prompt2Tool - Free AI-Powered Online Tools',
        'description' => 'Discover 100+ free online tools powered by AI. Perfect for developers, designers, and digital marketers.',
        'image' => '/assets/images/og-default.jpg',
        'url' => getCurrentURL(),
        'type' => 'website',
        'site_name' => 'Prompt2Tool',
        'locale' => 'en_US'
    ];
    
    $og = array_merge($defaults, $data);
    
    // 确保图片URL是完整的
    if (!preg_match('/^https?:\/\//', $og['image'])) {
        $og['image'] = getFullURL($og['image']);
    }
    
    return $og;
}

/**
 * 为工具页面生成Open Graph数据
 */
function generateToolOpenGraph($tool) {
    return [
        'title' => $tool['name'] . ' - Free Online Tool | Prompt2Tool',
        'description' => $tool['description'] . ' Use this free online tool on Prompt2Tool.',
        'image' => $tool['image'] ?? '/assets/images/tools/' . $tool['slug'] . '.jpg',
        'type' => 'article',
        'article:author' => 'Prompt2Tool Team',
        'article:section' => $tool['category'],
        'article:tag' => implode(',', $tool['tags'] ?? [])
    ];
}

/**
 * 为分类页面生成Open Graph数据
 */
function generateCategoryOpenGraph($category) {
    return [
        'title' => $category['name'] . ' Tools - Prompt2Tool',
        'description' => 'Explore ' . strtolower($category['name']) . ' tools on Prompt2Tool. ' . $category['description'],
        'image' => '/assets/images/categories/' . $category['slug'] . '.jpg',
        'type' => 'website'
    ];
}

/**
 * 输出Open Graph标签
 */
function outputOpenGraphTags($data) {
    $og = generateOpenGraphTags($data);
    
    echo '<!-- Open Graph标签 -->' . "\n";
    echo '<meta property="og:title" content="' . htmlspecialchars($og['title']) . '">' . "\n";
    echo '<meta property="og:description" content="' . htmlspecialchars($og['description']) . '">' . "\n";
    echo '<meta property="og:image" content="' . htmlspecialchars($og['image']) . '">' . "\n";
    echo '<meta property="og:url" content="' . htmlspecialchars($og['url']) . '">' . "\n";
    echo '<meta property="og:type" content="' . htmlspecialchars($og['type']) . '">' . "\n";
    echo '<meta property="og:site_name" content="' . htmlspecialchars($og['site_name']) . '">' . "\n";
    echo '<meta property="og:locale" content="' . htmlspecialchars($og['locale']) . '">' . "\n";
    
    // 图片尺寸
    echo '<meta property="og:image:width" content="1200">' . "\n";
    echo '<meta property="og:image:height" content="630">' . "\n";
    echo '<meta property="og:image:type" content="image/jpeg">' . "\n";
    
    // 文章特定标签
    if (isset($og['article:author'])) {
        echo '<meta property="article:author" content="' . htmlspecialchars($og['article:author']) . '">' . "\n";
    }
    if (isset($og['article:section'])) {
        echo '<meta property="article:section" content="' . htmlspecialchars($og['article:section']) . '">' . "\n";
    }
    if (isset($og['article:tag'])) {
        echo '<meta property="article:tag" content="' . htmlspecialchars($og['article:tag']) . '">' . "\n";
    }
    if (isset($og['article:published_time'])) {
        echo '<meta property="article:published_time" content="' . htmlspecialchars($og['article:published_time']) . '">' . "\n";
    }
    if (isset($og['article:modified_time'])) {
        echo '<meta property="article:modified_time" content="' . htmlspecialchars($og['article:modified_time']) . '">' . "\n";
    }
}

/**
 * 生成特定页面的Open Graph数据
 */
function getPageOpenGraphData($page, $data = []) {
    switch ($page) {
        case 'home':
            return [
                'title' => 'Prompt2Tool - Free AI-Powered Online Tools Platform',
                'description' => 'Discover 100+ free online tools powered by AI. HTML formatter, CSS minifier, image converter, SEO analyzer, and more. Boost your productivity with Prompt2Tool.',
                'image' => '/assets/images/og-home.jpg'
            ];
            
        case 'tools':
            return [
                'title' => 'All Tools - Prompt2Tool',
                'description' => 'Browse all free online tools available on Prompt2Tool. Find the perfect tool for your development, design, and productivity needs.',
                'image' => '/assets/images/og-tools.jpg'
            ];
            
        case 'about':
            return [
                'title' => 'About Prompt2Tool - Free Online Tools Platform',
                'description' => 'Learn about Prompt2Tool, our mission to provide free AI-powered online tools for developers, designers, and digital professionals.',
                'image' => '/assets/images/og-about.jpg'
            ];
            
        case 'contact':
            return [
                'title' => 'Contact Prompt2Tool - Get in Touch',
                'description' => 'Get in touch with the Prompt2Tool team. We\'d love to hear your feedback and suggestions for new tools.',
                'image' => '/assets/images/og-contact.jpg'
            ];
            
        case 'tool':
            return generateToolOpenGraph($data);
            
        case 'category':
            return generateCategoryOpenGraph($data);
            
        default:
            return [];
    }
}

/**
 * 验证Open Graph图片
 */
function validateOpenGraphImage($imagePath) {
    // 检查图片是否存在
    if (!file_exists(PUBLIC_PATH . $imagePath)) {
        return '/assets/images/og-default.jpg';
    }
    
    // 检查图片尺寸（推荐1200x630）
    $imageInfo = getimagesize(PUBLIC_PATH . $imagePath);
    if ($imageInfo) {
        $width = $imageInfo[0];
        $height = $imageInfo[1];
        
        // 如果尺寸不合适，返回默认图片
        if ($width < 600 || $height < 315) {
            return '/assets/images/og-default.jpg';
        }
    }
    
    return $imagePath;
}

/**
 * 生成动态Open Graph图片URL
 */
function generateDynamicOGImage($title, $description = '', $category = '') {
    $params = [
        'title' => urlencode($title),
        'description' => urlencode($description),
        'category' => urlencode($category)
    ];
    
    return '/api/og-image?' . http_build_query($params);
}
?>
