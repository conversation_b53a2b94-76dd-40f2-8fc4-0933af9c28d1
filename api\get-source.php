<?php
/**
 * 获取工具源文件内容API
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

// 检查登录状态
session_start();
if (!isset($_SESSION['admin_logged_in']) || !$_SESSION['admin_logged_in']) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

// 只允许GET请求
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

$toolSlug = $_GET['tool'] ?? '';

if (empty($toolSlug)) {
    http_response_code(400);
    echo json_encode(['error' => 'Tool slug is required']);
    exit;
}

// 验证slug格式（安全检查）
if (!preg_match('/^[a-z0-9-]+$/', $toolSlug)) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid tool slug format']);
    exit;
}

try {
    // 构建源文件路径
    $sourceFilePath = dirname(__DIR__) . '/templates/pages/tools/' . $toolSlug . '.php';
    
    // 检查文件是否存在
    $fileExists = file_exists($sourceFilePath);
    $content = '';
    
    if ($fileExists) {
        $content = file_get_contents($sourceFilePath);
        if ($content === false) {
            throw new Exception('Failed to read source file');
        }
    }
    
    echo json_encode([
        'success' => true,
        'content' => $content,
        'exists' => $fileExists,
        'path' => '/templates/pages/tools/' . $toolSlug . '.php'
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Error reading source file: ' . $e->getMessage()]);
}
?>
