<?php
/**
 * 用户创建页面
 * 提供用户创建表单和处理功能
 */

// 定义根目录
define('ROOT_PATH', dirname(dirname(dirname(__DIR__))));

// 加载依赖
require_once dirname(__DIR__) . '/../auth/middleware.php';
require_once dirname(__DIR__) . '/../middleware/PermissionMiddleware.php';
require_once dirname(__DIR__) . '/../models/UserModel.php';
require_once dirname(__DIR__) . '/../classes/SecurityManager.php';

// 权限检查
requirePermission('users.create');

// 初始化模型
$userModel = new UserModel();
$securityManager = new SecurityManager();

// 处理表单提交
$message = '';
$messageType = '';
$formData = [];

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // 获取表单数据
        $formData = [
            'username' => trim($_POST['username'] ?? ''),
            'email' => trim($_POST['email'] ?? ''),
            'password' => $_POST['password'] ?? '',
            'first_name' => trim($_POST['first_name'] ?? ''),
            'last_name' => trim($_POST['last_name'] ?? ''),
            'phone' => trim($_POST['phone'] ?? ''),
            'country' => trim($_POST['country'] ?? ''),
            'city' => trim($_POST['city'] ?? ''),
            'timezone' => $_POST['timezone'] ?? 'UTC',
            'language' => $_POST['language'] ?? 'en',
            'status' => $_POST['status'] ?? 'active',
            'subscription_type' => $_POST['subscription_type'] ?? 'free',
            'email_verified_at' => isset($_POST['email_verified']) ? date('Y-m-d H:i:s') : null,
            'phone_verified_at' => isset($_POST['phone_verified']) ? date('Y-m-d H:i:s') : null
        ];
        
        // 处理订阅到期时间
        if ($formData['subscription_type'] !== 'free' && !empty($_POST['subscription_expires_at'])) {
            $formData['subscription_expires_at'] = $_POST['subscription_expires_at'];
        }
        
        // 验证必填字段
        $errors = [];
        if (empty($formData['email'])) {
            $errors[] = 'Email is required';
        } elseif (!filter_var($formData['email'], FILTER_VALIDATE_EMAIL)) {
            $errors[] = 'Invalid email format';
        }
        
        if (empty($formData['password'])) {
            $errors[] = 'Password is required';
        }
        
        if (!empty($errors)) {
            throw new Exception(implode(', ', $errors));
        }
        
        // 检查邮箱唯一性
        if ($userModel->findByEmail($formData['email'])) {
            throw new Exception('Email already exists');
        }
        
        // 检查用户名唯一性（如果提供）
        if (!empty($formData['username']) && $userModel->findByUsername($formData['username'])) {
            throw new Exception('Username already exists');
        }
        
        // 验证密码强度
        $passwordValidation = $securityManager->validatePasswordStrength($formData['password']);
        if (!$passwordValidation['valid']) {
            throw new Exception('Password does not meet security requirements: ' . implode(', ', $passwordValidation['errors']));
        }
        
        // 哈希密码
        $formData['password'] = $securityManager->hashPassword($formData['password']);
        
        // 生成推荐码
        $formData['referral_code'] = strtoupper(substr(md5(uniqid()), 0, 8));

        // 处理偏好设置
        $preferences = [];
        if (!empty($_POST['preferences'])) {
            $preferences = json_decode($_POST['preferences'], true) ?: [];
        }
        $formData['preferences'] = !empty($preferences) ? json_encode($preferences) : null;
        
        // 创建用户
        $user = $userModel->create($formData);
        
        if ($user) {
            $message = 'User created successfully!';
            $messageType = 'success';
            
            // 记录操作
            $currentUser = getCurrentAdmin();
            $logData = [
                'admin_id' => $currentUser['id'],
                'activity_type' => 'user_create',
                'description' => "Created user: {$formData['email']} (ID: {$user['id']})",
                'target_type' => 'user',
                'target_id' => $user['id'],
                'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
            ];
            $db = Database::getInstance();
            $db->insert('pt_activity_log', $logData);
            
            // 重定向
            if (isset($_POST['save_and_continue'])) {
                header("Location: edit.php?id={$user['id']}");
                exit;
            } else {
                header('Location: index.php?message=' . urlencode($message));
                exit;
            }
        } else {
            throw new Exception('Failed to create user');
        }
        
    } catch (Exception $e) {
        $message = $e->getMessage();
        $messageType = 'error';
    }
}

// 获取国家列表
$countries = [
    'US' => 'United States', 'CN' => 'China', 'JP' => 'Japan', 'DE' => 'Germany',
    'UK' => 'United Kingdom', 'FR' => 'France', 'CA' => 'Canada', 'AU' => 'Australia',
    'IN' => 'India', 'BR' => 'Brazil', 'RU' => 'Russia', 'KR' => 'South Korea'
];

// 获取时区列表
$timezones = [
    'UTC' => 'UTC', 'America/New_York' => 'Eastern Time', 'America/Chicago' => 'Central Time',
    'America/Denver' => 'Mountain Time', 'America/Los_Angeles' => 'Pacific Time',
    'Europe/London' => 'London', 'Europe/Paris' => 'Paris', 'Europe/Berlin' => 'Berlin',
    'Asia/Tokyo' => 'Tokyo', 'Asia/Shanghai' => 'Shanghai', 'Asia/Kolkata' => 'Kolkata'
];

$currentPage = 'users';
include_once dirname(__DIR__) . '/../components/layout/header.php';
?>

<div class="min-h-screen bg-gray-100">
    <!-- 侧边栏 -->
    <div class="fixed inset-y-0 left-0 w-64 bg-gray-900">
        <?php include_once dirname(__DIR__) . '/../components/layout/sidebar.php'; ?>
    </div>
    
    <!-- 主内容区 -->
    <div class="ml-64">
        <!-- 顶部导航栏 -->
        <div class="bg-white shadow-sm border-b border-gray-200">
            <div class="px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-16">
                    <div class="flex items-center">
                        <nav class="flex space-x-4">
                            <a href="index.php" class="text-gray-500 hover:text-gray-700 px-3 py-2 text-sm font-medium">
                                <i class="fas fa-arrow-left mr-2"></i>Back to Users
                            </a>
                        </nav>
                        <h1 class="ml-4 text-2xl font-semibold text-gray-900">Create New User</h1>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 页面内容 -->
        <div class="p-6">
            <!-- 消息提示 -->
            <?php if ($message): ?>
            <div class="mb-6 p-4 border-l-4 <?= $messageType === 'success' ? 'bg-green-50 border-green-400 text-green-700' : 'bg-red-50 border-red-400 text-red-700' ?>">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <i class="fas fa-<?= $messageType === 'success' ? 'check-circle' : 'exclamation-circle' ?>"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm"><?= htmlspecialchars($message) ?></p>
                    </div>
                </div>
            </div>
            <?php endif; ?>
            
            <!-- 创建表单 -->
            <div class="bg-white border border-gray-200">
                <form method="POST" id="createUserForm" class="space-y-6">
                    <!-- 基本信息 -->
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Basic Information</h3>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Email *</label>
                                <input type="email" name="email" required 
                                       value="<?= htmlspecialchars($formData['email'] ?? '') ?>"
                                       class="w-full px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent"
                                       placeholder="<EMAIL>">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Username</label>
                                <input type="text" name="username" 
                                       value="<?= htmlspecialchars($formData['username'] ?? '') ?>"
                                       class="w-full px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent"
                                       placeholder="Optional username">
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">First Name</label>
                                <input type="text" name="first_name" 
                                       value="<?= htmlspecialchars($formData['first_name'] ?? '') ?>"
                                       class="w-full px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent"
                                       placeholder="First name">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Last Name</label>
                                <input type="text" name="last_name" 
                                       value="<?= htmlspecialchars($formData['last_name'] ?? '') ?>"
                                       class="w-full px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent"
                                       placeholder="Last name">
                            </div>
                        </div>
                        
                        <div class="mt-6">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Password *</label>
                            <input type="password" name="password" required 
                                   class="w-full px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent"
                                   placeholder="Enter password">
                            <p class="text-sm text-gray-500 mt-1">Password must be at least 8 characters with uppercase, lowercase, number and special character</p>
                        </div>
                    </div>
                    
                    <!-- 联系信息 -->
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Contact Information</h3>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Phone</label>
                                <input type="tel" name="phone" 
                                       value="<?= htmlspecialchars($formData['phone'] ?? '') ?>"
                                       class="w-full px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent"
                                       placeholder="+1234567890">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Country</label>
                                <select name="country" 
                                        class="w-full px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent">
                                    <option value="">Select Country</option>
                                    <?php foreach ($countries as $code => $name): ?>
                                    <option value="<?= $code ?>" 
                                            <?= ($formData['country'] ?? '') === $code ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($name) ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        
                        <div class="mt-6">
                            <label class="block text-sm font-medium text-gray-700 mb-2">City</label>
                            <input type="text" name="city" 
                                   value="<?= htmlspecialchars($formData['city'] ?? '') ?>"
                                   class="w-full px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent"
                                   placeholder="City name">
                        </div>
                    </div>
                    
                    <!-- 偏好设置 -->
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Preferences</h3>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Timezone</label>
                                <select name="timezone" 
                                        class="w-full px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent">
                                    <?php foreach ($timezones as $value => $label): ?>
                                    <option value="<?= $value ?>" 
                                            <?= ($formData['timezone'] ?? 'UTC') === $value ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($label) ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Language</label>
                                <select name="language" 
                                        class="w-full px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent">
                                    <option value="en" <?= ($formData['language'] ?? 'en') === 'en' ? 'selected' : '' ?>>English</option>
                                    <option value="zh" <?= ($formData['language'] ?? '') === 'zh' ? 'selected' : '' ?>>中文</option>
                                    <option value="es" <?= ($formData['language'] ?? '') === 'es' ? 'selected' : '' ?>>Español</option>
                                    <option value="fr" <?= ($formData['language'] ?? '') === 'fr' ? 'selected' : '' ?>>Français</option>
                                    <option value="de" <?= ($formData['language'] ?? '') === 'de' ? 'selected' : '' ?>>Deutsch</option>
                                    <option value="ja" <?= ($formData['language'] ?? '') === 'ja' ? 'selected' : '' ?>>日本語</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 订阅信息 -->
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Subscription</h3>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Subscription Type</label>
                                <select name="subscription_type" id="subscriptionType"
                                        class="w-full px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent">
                                    <option value="free" <?= ($formData['subscription_type'] ?? 'free') === 'free' ? 'selected' : '' ?>>Free</option>
                                    <option value="basic" <?= ($formData['subscription_type'] ?? '') === 'basic' ? 'selected' : '' ?>>Basic</option>
                                    <option value="premium" <?= ($formData['subscription_type'] ?? '') === 'premium' ? 'selected' : '' ?>>Premium</option>
                                    <option value="enterprise" <?= ($formData['subscription_type'] ?? '') === 'enterprise' ? 'selected' : '' ?>>Enterprise</option>
                                </select>
                            </div>
                            
                            <div id="subscriptionExpiresContainer" style="display: none;">
                                <label class="block text-sm font-medium text-gray-700 mb-2">Subscription Expires</label>
                                <input type="datetime-local" name="subscription_expires_at" 
                                       value="<?= htmlspecialchars($formData['subscription_expires_at'] ?? '') ?>"
                                       class="w-full px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent">
                            </div>
                        </div>
                    </div>
                    
                    <!-- 状态和验证 -->
                    <div class="p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Status & Verification</h3>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                                <select name="status" 
                                        class="w-full px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent">
                                    <option value="active" <?= ($formData['status'] ?? 'active') === 'active' ? 'selected' : '' ?>>Active</option>
                                    <option value="inactive" <?= ($formData['status'] ?? '') === 'inactive' ? 'selected' : '' ?>>Inactive</option>
                                    <option value="suspended" <?= ($formData['status'] ?? '') === 'suspended' ? 'selected' : '' ?>>Suspended</option>
                                </select>
                            </div>
                            
                            <div class="space-y-3">
                                <label class="flex items-center">
                                    <input type="checkbox" name="email_verified" value="1" 
                                           <?= ($formData['email_verified_at'] ?? false) ? 'checked' : '' ?>
                                           class="mr-2 text-accent focus:ring-accent">
                                    <span class="text-sm font-medium text-gray-700">Email Verified</span>
                                </label>
                                
                                <label class="flex items-center">
                                    <input type="checkbox" name="phone_verified" value="1" 
                                           <?= ($formData['phone_verified_at'] ?? false) ? 'checked' : '' ?>
                                           class="mr-2 text-accent focus:ring-accent">
                                    <span class="text-sm font-medium text-gray-700">Phone Verified</span>
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 提交按钮 -->
                    <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-end space-x-3">
                        <a href="index.php" 
                           class="px-4 py-2 border border-gray-300 text-gray-700 hover:bg-gray-50">
                            Cancel
                        </a>
                        <button type="submit" name="save_and_continue" 
                                class="px-4 py-2 bg-gray-600 text-white hover:bg-gray-700">
                            Save & Continue Editing
                        </button>
                        <button type="submit" 
                                class="px-4 py-2 bg-accent text-white hover:bg-blue-600">
                            Create User
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// 订阅类型变化处理
document.getElementById('subscriptionType').addEventListener('change', function() {
    const expiresContainer = document.getElementById('subscriptionExpiresContainer');
    if (this.value === 'free') {
        expiresContainer.style.display = 'none';
    } else {
        expiresContainer.style.display = 'block';
        // 设置默认到期时间（1年后）
        if (!document.querySelector('input[name="subscription_expires_at"]').value) {
            const nextYear = new Date();
            nextYear.setFullYear(nextYear.getFullYear() + 1);
            document.querySelector('input[name="subscription_expires_at"]').value = 
                nextYear.toISOString().slice(0, 16);
        }
    }
});

// 页面加载时检查订阅类型
document.addEventListener('DOMContentLoaded', function() {
    const subscriptionType = document.getElementById('subscriptionType');
    if (subscriptionType.value !== 'free') {
        document.getElementById('subscriptionExpiresContainer').style.display = 'block';
    }
});

// 表单验证
document.getElementById('createUserForm').addEventListener('submit', function(e) {
    const email = document.querySelector('input[name="email"]').value.trim();
    const password = document.querySelector('input[name="password"]').value;
    
    if (!email) {
        alert('Please enter an email address');
        e.preventDefault();
        return;
    }
    
    if (!password) {
        alert('Please enter a password');
        e.preventDefault();
        return;
    }
    
    if (password.length < 8) {
        alert('Password must be at least 8 characters long');
        e.preventDefault();
        return;
    }
});
</script>

<?php include_once dirname(__DIR__) . '/../components/layout/footer.php'; ?>
