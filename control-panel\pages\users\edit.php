<?php
/**
 * 用户编辑页面
 * 提供用户编辑表单和处理功能
 */

// 定义根目录
define('ROOT_PATH', dirname(dirname(dirname(__DIR__))));

// 加载依赖
require_once dirname(__DIR__) . '/../auth/middleware.php';
require_once dirname(__DIR__) . '/../middleware/PermissionMiddleware.php';
require_once dirname(__DIR__) . '/../models/UserModel.php';
require_once dirname(__DIR__) . '/../classes/SecurityManager.php';
require_once dirname(__DIR__) . '/../classes/Database.php';

// 权限检查
requirePermission('users.edit');

// 初始化模型
$userModel = new UserModel();
$securityManager = new SecurityManager();

// 获取用户ID
$userId = intval($_GET['id'] ?? 0);
if (!$userId) {
    header('Location: index.php?error=' . urlencode('User not found'));
    exit;
}

// 获取用户信息
$user = $userModel->find($userId);
if (!$user) {
    header('Location: index.php?error=' . urlencode('User not found'));
    exit;
}

// 处理表单提交
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // 获取表单数据
        $formData = [
            'username' => trim($_POST['username'] ?? ''),
            'email' => trim($_POST['email'] ?? ''),
            'first_name' => trim($_POST['first_name'] ?? ''),
            'last_name' => trim($_POST['last_name'] ?? ''),
            'phone' => trim($_POST['phone'] ?? ''),
            'country' => trim($_POST['country'] ?? ''),
            'city' => trim($_POST['city'] ?? ''),
            'timezone' => $_POST['timezone'] ?? 'UTC',
            'language' => $_POST['language'] ?? 'en',
            'status' => $_POST['status'] ?? 'active',
            'subscription_type' => $_POST['subscription_type'] ?? 'free',
            'email_verified_at' => isset($_POST['email_verified']) ? 
                ($user['email_verified_at'] ?: date('Y-m-d H:i:s')) : null,
            'phone_verified_at' => isset($_POST['phone_verified']) ? 
                ($user['phone_verified_at'] ?: date('Y-m-d H:i:s')) : null
        ];
        
        // 处理密码更新
        if (!empty($_POST['password'])) {
            // 验证密码强度
            $passwordValidation = $securityManager->validatePasswordStrength($_POST['password']);
            if (!$passwordValidation['valid']) {
                throw new Exception('Password does not meet security requirements: ' . implode(', ', $passwordValidation['errors']));
            }
            $formData['password'] = $securityManager->hashPassword($_POST['password']);
        }
        
        // 处理订阅到期时间
        if ($formData['subscription_type'] !== 'free' && !empty($_POST['subscription_expires_at'])) {
            $formData['subscription_expires_at'] = $_POST['subscription_expires_at'];
        } elseif ($formData['subscription_type'] === 'free') {
            $formData['subscription_expires_at'] = null;
        }
        
        // 验证必填字段
        $errors = [];
        if (empty($formData['email'])) {
            $errors[] = 'Email is required';
        } elseif (!filter_var($formData['email'], FILTER_VALIDATE_EMAIL)) {
            $errors[] = 'Invalid email format';
        }
        
        if (!empty($errors)) {
            throw new Exception(implode(', ', $errors));
        }
        
        // 检查邮箱唯一性
        $existingUser = $userModel->findByEmail($formData['email']);
        if ($existingUser && $existingUser['id'] != $userId) {
            throw new Exception('Email already exists');
        }
        
        // 检查用户名唯一性（如果提供）
        if (!empty($formData['username'])) {
            $existingUser = $userModel->findByUsername($formData['username']);
            if ($existingUser && $existingUser['id'] != $userId) {
                throw new Exception('Username already exists');
            }
        }
        
        // 处理偏好设置
        $preferences = [];
        if (!empty($_POST['preferences'])) {
            $preferences = json_decode($_POST['preferences'], true) ?: [];
        }
        $formData['preferences'] = !empty($preferences) ? json_encode($preferences) : null;
        
        // 更新用户
        $updatedUser = $userModel->update($userId, $formData);
        
        if ($updatedUser) {
            $message = 'User updated successfully!';
            $messageType = 'success';
            
            // 更新本地用户数据
            $user = $updatedUser;
            
            // 记录操作
            $currentUser = getCurrentAdmin();
            $logData = [
                'admin_id' => $currentUser['id'],
                'activity_type' => 'user_update',
                'description' => "Updated user: {$formData['email']} (ID: {$userId})",
                'target_type' => 'user',
                'target_id' => $userId,
                'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
            ];
            $db = Database::getInstance();
            $db->insert('pt_activity_log', $logData);
            
            // 如果点击了保存并继续，重定向到同一页面
            if (isset($_POST['save_and_continue'])) {
                header("Location: edit.php?id={$userId}&message=" . urlencode($message));
                exit;
            }
        } else {
            throw new Exception('Failed to update user');
        }
        
    } catch (Exception $e) {
        $message = $e->getMessage();
        $messageType = 'error';
    }
}

// 获取用户统计信息
$db = Database::getInstance();
$userStats = [
    'tool_usage_count' => $db->fetchColumn(
        "SELECT COUNT(*) FROM pt_tool_usage WHERE user_id = :user_id",
        ['user_id' => $userId]
    ),
    'last_activity' => $db->fetchColumn(
        "SELECT MAX(created_at) FROM pt_member_activity_log WHERE user_id = :user_id",
        ['user_id' => $userId]
    ),
    'api_requests_today' => $db->fetchColumn(
        "SELECT COUNT(*) FROM pt_analytics_event WHERE user_id = :user_id AND DATE(created_at) = CURDATE()",
        ['user_id' => $userId]
    )
];

// 获取国家列表
$countries = [
    'US' => 'United States', 'CN' => 'China', 'JP' => 'Japan', 'DE' => 'Germany',
    'UK' => 'United Kingdom', 'FR' => 'France', 'CA' => 'Canada', 'AU' => 'Australia',
    'IN' => 'India', 'BR' => 'Brazil', 'RU' => 'Russia', 'KR' => 'South Korea'
];

// 获取时区列表
$timezones = [
    'UTC' => 'UTC', 'America/New_York' => 'Eastern Time', 'America/Chicago' => 'Central Time',
    'America/Denver' => 'Mountain Time', 'America/Los_Angeles' => 'Pacific Time',
    'Europe/London' => 'London', 'Europe/Paris' => 'Paris', 'Europe/Berlin' => 'Berlin',
    'Asia/Tokyo' => 'Tokyo', 'Asia/Shanghai' => 'Shanghai', 'Asia/Kolkata' => 'Kolkata'
];

$currentPage = 'users';
include_once dirname(__DIR__) . '/../components/layout/header.php';
?>

<div class="min-h-screen bg-gray-100">
    <!-- 侧边栏 -->
    <div class="fixed inset-y-0 left-0 w-64 bg-gray-900">
        <?php include_once dirname(__DIR__) . '/../components/layout/sidebar.php'; ?>
    </div>
    
    <!-- 主内容区 -->
    <div class="ml-64">
        <!-- 顶部导航栏 -->
        <div class="bg-white shadow-sm border-b border-gray-200">
            <div class="px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-16">
                    <div class="flex items-center">
                        <nav class="flex space-x-4">
                            <a href="index.php" class="text-gray-500 hover:text-gray-700 px-3 py-2 text-sm font-medium">
                                <i class="fas fa-arrow-left mr-2"></i>Back to Users
                            </a>
                        </nav>
                        <h1 class="ml-4 text-2xl font-semibold text-gray-900">
                            Edit User: <?= htmlspecialchars($user['email']) ?>
                        </h1>
                    </div>
                    
                    <div class="flex items-center space-x-4">
                        <!-- 用户状态 -->
                        <span class="inline-flex items-center px-2.5 py-0.5 text-xs font-medium 
                                     <?= $user['status'] === 'active' ? 'bg-green-100 text-green-800' : 
                                         ($user['status'] === 'inactive' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800') ?>">
                            <?= ucfirst($user['status']) ?>
                        </span>
                        
                        <!-- 订阅类型 -->
                        <span class="inline-flex items-center px-2.5 py-0.5 text-xs font-medium 
                                     <?= $user['subscription_type'] === 'free' ? 'bg-gray-100 text-gray-800' : 'bg-blue-100 text-blue-800' ?>">
                            <?= ucfirst($user['subscription_type']) ?>
                        </span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 页面内容 -->
        <div class="p-6">
            <!-- 消息提示 -->
            <?php if ($message): ?>
            <div class="mb-6 p-4 border-l-4 <?= $messageType === 'success' ? 'bg-green-50 border-green-400 text-green-700' : 'bg-red-50 border-red-400 text-red-700' ?>">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <i class="fas fa-<?= $messageType === 'success' ? 'check-circle' : 'exclamation-circle' ?>"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm"><?= htmlspecialchars($message) ?></p>
                    </div>
                </div>
            </div>
            <?php endif; ?>
            
            <!-- 用户统计信息 -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                <div class="bg-white border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="p-3 bg-blue-100 text-blue-600 mr-4">
                            <i class="fas fa-tools text-2xl"></i>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-600">Tool Usage</p>
                            <p class="text-2xl font-bold text-gray-900"><?= number_format($userStats['tool_usage_count']) ?></p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="p-3 bg-green-100 text-green-600 mr-4">
                            <i class="fas fa-calendar text-2xl"></i>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-600">Member Since</p>
                            <p class="text-sm font-bold text-gray-900"><?= date('M j, Y', strtotime($user['created_at'])) ?></p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="p-3 bg-yellow-100 text-yellow-600 mr-4">
                            <i class="fas fa-clock text-2xl"></i>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-600">Last Login</p>
                            <p class="text-sm font-bold text-gray-900">
                                <?= $user['last_login_at'] ? date('M j, Y', strtotime($user['last_login_at'])) : 'Never' ?>
                            </p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="p-3 bg-purple-100 text-purple-600 mr-4">
                            <i class="fas fa-chart-line text-2xl"></i>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-600">API Requests Today</p>
                            <p class="text-2xl font-bold text-gray-900"><?= number_format($userStats['api_requests_today']) ?></p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 编辑表单 -->
            <div class="bg-white border border-gray-200">
                <form method="POST" id="editUserForm" class="space-y-6">
                    <!-- 基本信息 -->
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Basic Information</h3>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Email *</label>
                                <input type="email" name="email" required 
                                       value="<?= htmlspecialchars($user['email']) ?>"
                                       class="w-full px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent"
                                       placeholder="<EMAIL>">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Username</label>
                                <input type="text" name="username" 
                                       value="<?= htmlspecialchars($user['username']) ?>"
                                       class="w-full px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent"
                                       placeholder="Optional username">
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">First Name</label>
                                <input type="text" name="first_name" 
                                       value="<?= htmlspecialchars($user['first_name']) ?>"
                                       class="w-full px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent"
                                       placeholder="First name">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Last Name</label>
                                <input type="text" name="last_name" 
                                       value="<?= htmlspecialchars($user['last_name']) ?>"
                                       class="w-full px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent"
                                       placeholder="Last name">
                            </div>
                        </div>
                        
                        <div class="mt-6">
                            <label class="block text-sm font-medium text-gray-700 mb-2">New Password</label>
                            <input type="password" name="password" 
                                   class="w-full px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent"
                                   placeholder="Leave blank to keep current password">
                            <p class="text-sm text-gray-500 mt-1">Password must be at least 8 characters with uppercase, lowercase, number and special character</p>
                        </div>
                    </div>
                    
                    <!-- 联系信息 -->
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Contact Information</h3>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Phone</label>
                                <input type="tel" name="phone" 
                                       value="<?= htmlspecialchars($user['phone']) ?>"
                                       class="w-full px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent"
                                       placeholder="+1234567890">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Country</label>
                                <select name="country" 
                                        class="w-full px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent">
                                    <option value="">Select Country</option>
                                    <?php foreach ($countries as $code => $name): ?>
                                    <option value="<?= $code ?>" 
                                            <?= $user['country'] === $code ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($name) ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        
                        <div class="mt-6">
                            <label class="block text-sm font-medium text-gray-700 mb-2">City</label>
                            <input type="text" name="city" 
                                   value="<?= htmlspecialchars($user['city']) ?>"
                                   class="w-full px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent"
                                   placeholder="City name">
                        </div>
                    </div>
                    
                    <!-- 偏好设置 -->
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Preferences</h3>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Timezone</label>
                                <select name="timezone" 
                                        class="w-full px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent">
                                    <?php foreach ($timezones as $value => $label): ?>
                                    <option value="<?= $value ?>" 
                                            <?= $user['timezone'] === $value ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($label) ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Language</label>
                                <select name="language" 
                                        class="w-full px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent">
                                    <option value="en" <?= $user['language'] === 'en' ? 'selected' : '' ?>>English</option>
                                    <option value="zh" <?= $user['language'] === 'zh' ? 'selected' : '' ?>>中文</option>
                                    <option value="es" <?= $user['language'] === 'es' ? 'selected' : '' ?>>Español</option>
                                    <option value="fr" <?= $user['language'] === 'fr' ? 'selected' : '' ?>>Français</option>
                                    <option value="de" <?= $user['language'] === 'de' ? 'selected' : '' ?>>Deutsch</option>
                                    <option value="ja" <?= $user['language'] === 'ja' ? 'selected' : '' ?>>日本語</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 订阅信息 -->
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Subscription</h3>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Subscription Type</label>
                                <select name="subscription_type" id="subscriptionType"
                                        class="w-full px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent">
                                    <option value="free" <?= $user['subscription_type'] === 'free' ? 'selected' : '' ?>>Free</option>
                                    <option value="basic" <?= $user['subscription_type'] === 'basic' ? 'selected' : '' ?>>Basic</option>
                                    <option value="premium" <?= $user['subscription_type'] === 'premium' ? 'selected' : '' ?>>Premium</option>
                                    <option value="enterprise" <?= $user['subscription_type'] === 'enterprise' ? 'selected' : '' ?>>Enterprise</option>
                                </select>
                            </div>
                            
                            <div id="subscriptionExpiresContainer" 
                                 style="display: <?= $user['subscription_type'] === 'free' ? 'none' : 'block' ?>;">
                                <label class="block text-sm font-medium text-gray-700 mb-2">Subscription Expires</label>
                                <input type="datetime-local" name="subscription_expires_at" 
                                       value="<?= $user['subscription_expires_at'] ? date('Y-m-d\TH:i', strtotime($user['subscription_expires_at'])) : '' ?>"
                                       class="w-full px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent">
                            </div>
                        </div>
                        
                        <div class="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">API Rate Limit (per day)</label>
                                <input type="number" name="api_rate_limit" 
                                       value="<?= htmlspecialchars($user['api_rate_limit']) ?>"
                                       class="w-full px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent"
                                       min="0" max="100000">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Referral Code</label>
                                <input type="text" name="referral_code" readonly
                                       value="<?= htmlspecialchars($user['referral_code']) ?>"
                                       class="w-full px-3 py-2 border border-gray-300 bg-gray-50">
                            </div>
                        </div>
                    </div>
                    
                    <!-- 状态和验证 -->
                    <div class="p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Status & Verification</h3>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                                <select name="status" 
                                        class="w-full px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent">
                                    <option value="active" <?= $user['status'] === 'active' ? 'selected' : '' ?>>Active</option>
                                    <option value="inactive" <?= $user['status'] === 'inactive' ? 'selected' : '' ?>>Inactive</option>
                                    <option value="suspended" <?= $user['status'] === 'suspended' ? 'selected' : '' ?>>Suspended</option>
                                </select>
                            </div>
                            
                            <div class="space-y-3">
                                <label class="flex items-center">
                                    <input type="checkbox" name="email_verified" value="1" 
                                           <?= $user['email_verified_at'] ? 'checked' : '' ?>
                                           class="mr-2 text-accent focus:ring-accent">
                                    <span class="text-sm font-medium text-gray-700">Email Verified</span>
                                    <?php if ($user['email_verified_at']): ?>
                                    <span class="ml-2 text-xs text-gray-500">
                                        (<?= date('M j, Y', strtotime($user['email_verified_at'])) ?>)
                                    </span>
                                    <?php endif; ?>
                                </label>
                                
                                <label class="flex items-center">
                                    <input type="checkbox" name="phone_verified" value="1" 
                                           <?= $user['phone_verified_at'] ? 'checked' : '' ?>
                                           class="mr-2 text-accent focus:ring-accent">
                                    <span class="text-sm font-medium text-gray-700">Phone Verified</span>
                                    <?php if ($user['phone_verified_at']): ?>
                                    <span class="ml-2 text-xs text-gray-500">
                                        (<?= date('M j, Y', strtotime($user['phone_verified_at'])) ?>)
                                    </span>
                                    <?php endif; ?>
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 提交按钮 -->
                    <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-between">
                        <div class="flex space-x-3">
                            <a href="index.php" 
                               class="px-4 py-2 border border-gray-300 text-gray-700 hover:bg-gray-50">
                                Back to List
                            </a>
                            
                            <?php if (hasPermission('users.delete')): ?>
                            <button type="button" onclick="deleteUser(<?= $userId ?>)" 
                                    class="px-4 py-2 bg-red-600 text-white hover:bg-red-700">
                                <i class="fas fa-trash mr-2"></i>Delete User
                            </button>
                            <?php endif; ?>
                        </div>
                        
                        <div class="flex space-x-3">
                            <button type="submit" name="save_and_continue" 
                                    class="px-4 py-2 bg-gray-600 text-white hover:bg-gray-700">
                                Save & Continue
                            </button>
                            <button type="submit" 
                                    class="px-4 py-2 bg-accent text-white hover:bg-blue-600">
                                Update User
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// 订阅类型变化处理
document.getElementById('subscriptionType').addEventListener('change', function() {
    const expiresContainer = document.getElementById('subscriptionExpiresContainer');
    if (this.value === 'free') {
        expiresContainer.style.display = 'none';
    } else {
        expiresContainer.style.display = 'block';
        // 设置默认到期时间（1年后）
        if (!document.querySelector('input[name="subscription_expires_at"]').value) {
            const nextYear = new Date();
            nextYear.setFullYear(nextYear.getFullYear() + 1);
            document.querySelector('input[name="subscription_expires_at"]').value = 
                nextYear.toISOString().slice(0, 16);
        }
    }
});

// 删除用户
function deleteUser(userId) {
    if (confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = 'delete.php';
        form.innerHTML = `<input type="hidden" name="id" value="${userId}">`;
        document.body.appendChild(form);
        form.submit();
    }
}

// 表单验证
document.getElementById('editUserForm').addEventListener('submit', function(e) {
    const email = document.querySelector('input[name="email"]').value.trim();
    const password = document.querySelector('input[name="password"]').value;
    
    if (!email) {
        alert('Please enter an email address');
        e.preventDefault();
        return;
    }
    
    if (password && password.length < 8) {
        alert('Password must be at least 8 characters long');
        e.preventDefault();
        return;
    }
});
</script>

<?php include_once dirname(__DIR__) . '/../components/layout/footer.php'; ?>
