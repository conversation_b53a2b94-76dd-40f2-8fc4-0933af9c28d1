<?php
/**
 * 安全管理器类
 * 负责处理系统的各种安全功能
 */

// 防止直接访问
if (!defined('ROOT_PATH')) {
    exit('Direct access not allowed');
}

class SecurityManager {
    
    private $config;
    private $redis;
    private $logger;
    
    /**
     * 构造函数
     */
    public function __construct() {
        $this->config = include ROOT_PATH . '/control-panel/config/security.php';
        $this->initializeRedis();
        $this->initializeLogger();
        $this->setupSecurityHeaders();
    }
    
    /**
     * 初始化Redis连接
     */
    private function initializeRedis() {
        try {
            $this->redis = new Redis();
            $this->redis->connect('127.0.0.1', 6379);
            $this->redis->select(2); // 使用数据库2存储安全数据
        } catch (Exception $e) {
            $this->redis = null;
        }
    }
    
    /**
     * 初始化日志记录器
     */
    private function initializeLogger() {
        $this->logger = new SecurityLogger($this->config['audit_logging']);
    }
    
    /**
     * 设置安全头部
     */
    private function setupSecurityHeaders() {
        if (!$this->config['security_headers']['enabled']) {
            return;
        }
        
        foreach ($this->config['security_headers']['headers'] as $header => $value) {
            header("{$header}: {$value}");
        }
        
        // 设置内容安全策略
        if ($this->config['xss']['content_security_policy']['enabled']) {
            $csp = $this->buildCSPHeader();
            header("Content-Security-Policy: {$csp}");
        }
    }
    
    /**
     * 构建CSP头部
     */
    private function buildCSPHeader() {
        $csp = $this->config['xss']['content_security_policy'];
        $directives = [];
        
        foreach ($csp as $directive => $value) {
            if ($directive !== 'enabled' && !empty($value)) {
                $directives[] = str_replace('_', '-', $directive) . ' ' . $value;
            }
        }
        
        return implode('; ', $directives);
    }
    
    /**
     * 验证登录尝试
     */
    public function validateLoginAttempt($username, $ip) {
        $key = "login_attempts:{$ip}:{$username}";
        $attempts = $this->redis ? $this->redis->get($key) : 0;
        
        if ($attempts >= $this->config['login']['max_attempts']) {
            $this->logSecurityEvent('login_blocked', [
                'username' => $username,
                'ip' => $ip,
                'attempts' => $attempts
            ]);
            
            return [
                'allowed' => false,
                'message' => 'Too many failed attempts. Account temporarily locked.',
                'retry_after' => $this->config['login']['lockout_duration']
            ];
        }
        
        return ['allowed' => true];
    }
    
    /**
     * 记录登录失败
     */
    public function recordLoginFailure($username, $ip) {
        $key = "login_attempts:{$ip}:{$username}";
        $attempts = $this->redis ? $this->redis->incr($key) : 1;
        
        if ($this->redis) {
            $this->redis->expire($key, $this->config['login']['lockout_duration']);
        }
        
        $this->logSecurityEvent('login_failure', [
            'username' => $username,
            'ip' => $ip,
            'attempts' => $attempts
        ]);
        
        // 检查是否需要自动封禁IP
        if ($attempts >= $this->config['ip_control']['auto_ban_threshold']) {
            $this->banIP($ip, 'Excessive login failures');
        }
        
        return $attempts;
    }
    
    /**
     * 清除登录尝试记录
     */
    public function clearLoginAttempts($username, $ip) {
        $key = "login_attempts:{$ip}:{$username}";
        if ($this->redis) {
            $this->redis->del($key);
        }
    }
    
    /**
     * 生成CSRF Token
     */
    public function generateCSRFToken() {
        if (!$this->config['csrf']['enabled']) {
            return null;
        }
        
        $token = bin2hex(random_bytes($this->config['csrf']['token_length']));
        $tokenName = $this->config['csrf']['token_name'];
        
        $_SESSION[$tokenName] = $token;
        $_SESSION["{$tokenName}_time"] = time();
        
        return $token;
    }
    
    /**
     * 验证CSRF Token
     */
    public function validateCSRFToken($token) {
        if (!$this->config['csrf']['enabled']) {
            return true;
        }
        
        $tokenName = $this->config['csrf']['token_name'];
        $sessionToken = $_SESSION[$tokenName] ?? '';
        $tokenTime = $_SESSION["{$tokenName}_time"] ?? 0;
        
        // 检查Token是否过期
        if (time() - $tokenTime > $this->config['csrf']['expire_time']) {
            unset($_SESSION[$tokenName], $_SESSION["{$tokenName}_time"]);
            return false;
        }
        
        // 验证Token
        $isValid = hash_equals($sessionToken, $token);
        
        if (!$isValid) {
            $this->logSecurityEvent('csrf_violation', [
                'ip' => $this->getClientIP(),
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
                'expected_token' => substr($sessionToken, 0, 8) . '...',
                'received_token' => substr($token, 0, 8) . '...'
            ]);
        }
        
        // 如果配置为使用后重新生成
        if ($isValid && $this->config['csrf']['regenerate_on_use']) {
            unset($_SESSION[$tokenName], $_SESSION["{$tokenName}_time"]);
        }
        
        return $isValid;
    }
    
    /**
     * 检查访问频率限制
     */
    public function checkRateLimit($identifier = null) {
        if (!$this->config['rate_limiting']['enabled']) {
            return ['allowed' => true];
        }
        
        $ip = $this->getClientIP();
        $identifier = $identifier ?: $ip;
        
        // 检查IP是否在白名单中
        if (in_array($ip, $this->config['rate_limiting']['whitelist_ips'])) {
            return ['allowed' => true];
        }
        
        $windows = [
            'minute' => ['limit' => $this->config['rate_limiting']['requests_per_minute'], 'window' => 60],
            'hour' => ['limit' => $this->config['rate_limiting']['requests_per_hour'], 'window' => 3600],
            'day' => ['limit' => $this->config['rate_limiting']['requests_per_day'], 'window' => 86400]
        ];
        
        foreach ($windows as $period => $config) {
            $key = "rate_limit:{$period}:{$identifier}";
            $current = $this->redis ? $this->redis->incr($key) : 1;
            
            if ($current === 1 && $this->redis) {
                $this->redis->expire($key, $config['window']);
            }
            
            if ($current > $config['limit']) {
                $this->logSecurityEvent('rate_limit_exceeded', [
                    'identifier' => $identifier,
                    'period' => $period,
                    'limit' => $config['limit'],
                    'current' => $current
                ]);
                
                return [
                    'allowed' => false,
                    'message' => 'Rate limit exceeded',
                    'retry_after' => $config['window']
                ];
            }
        }
        
        return ['allowed' => true];
    }
    
    /**
     * 封禁IP地址
     */
    public function banIP($ip, $reason = '', $duration = null) {
        $duration = $duration ?: $this->config['ip_control']['auto_ban_duration'];
        $key = "banned_ip:{$ip}";
        
        if ($this->redis) {
            $this->redis->setex($key, $duration, json_encode([
                'reason' => $reason,
                'banned_at' => time(),
                'expires_at' => time() + $duration
            ]));
        }
        
        $this->logSecurityEvent('ip_banned', [
            'ip' => $ip,
            'reason' => $reason,
            'duration' => $duration
        ]);
    }
    
    /**
     * 检查IP是否被封禁
     */
    public function isIPBanned($ip) {
        if (!$this->config['ip_control']['blacklist_enabled']) {
            return false;
        }
        
        // 检查静态黑名单
        if (in_array($ip, $this->config['ip_control']['blacklist'])) {
            return true;
        }
        
        // 检查动态封禁
        $key = "banned_ip:{$ip}";
        return $this->redis ? $this->redis->exists($key) : false;
    }
    
    /**
     * 验证密码强度
     */
    public function validatePasswordStrength($password) {
        $config = $this->config['password'];
        $errors = [];
        
        // 检查长度
        if (strlen($password) < $config['min_length']) {
            $errors[] = "Password must be at least {$config['min_length']} characters long";
        }
        
        if (strlen($password) > $config['max_length']) {
            $errors[] = "Password must not exceed {$config['max_length']} characters";
        }
        
        // 检查字符要求
        if ($config['require_uppercase'] && !preg_match('/[A-Z]/', $password)) {
            $errors[] = 'Password must contain at least one uppercase letter';
        }
        
        if ($config['require_lowercase'] && !preg_match('/[a-z]/', $password)) {
            $errors[] = 'Password must contain at least one lowercase letter';
        }
        
        if ($config['require_numbers'] && !preg_match('/[0-9]/', $password)) {
            $errors[] = 'Password must contain at least one number';
        }
        
        if ($config['require_symbols'] && !preg_match('/[^A-Za-z0-9]/', $password)) {
            $errors[] = 'Password must contain at least one special character';
        }
        
        // 检查禁止的模式
        foreach ($config['forbidden_patterns'] as $pattern) {
            if (stripos($password, $pattern) !== false) {
                $errors[] = 'Password contains forbidden pattern';
                break;
            }
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors,
            'strength' => $this->calculatePasswordStrength($password)
        ];
    }
    
    /**
     * 计算密码强度
     */
    private function calculatePasswordStrength($password) {
        $score = 0;
        $length = strlen($password);
        
        // 长度评分
        $score += min($length * 4, 25);
        
        // 字符类型评分
        if (preg_match('/[a-z]/', $password)) $score += 5;
        if (preg_match('/[A-Z]/', $password)) $score += 5;
        if (preg_match('/[0-9]/', $password)) $score += 5;
        if (preg_match('/[^A-Za-z0-9]/', $password)) $score += 10;
        
        // 复杂性评分
        if ($length >= 8) $score += 5;
        if ($length >= 12) $score += 5;
        if (preg_match('/[A-Z].*[a-z]|[a-z].*[A-Z]/', $password)) $score += 5;
        if (preg_match('/[0-9].*[^A-Za-z0-9]|[^A-Za-z0-9].*[0-9]/', $password)) $score += 5;
        
        // 返回强度等级
        if ($score >= 80) return 'very_strong';
        if ($score >= 60) return 'strong';
        if ($score >= 40) return 'medium';
        if ($score >= 20) return 'weak';
        return 'very_weak';
    }
    
    /**
     * 哈希密码
     */
    public function hashPassword($password) {
        return password_hash(
            $password,
            $this->config['password']['algorithm'],
            $this->config['password']['options']
        );
    }
    
    /**
     * 验证密码
     */
    public function verifyPassword($password, $hash) {
        return password_verify($password, $hash);
    }
    
    /**
     * 过滤用户输入
     */
    public function filterInput($input, $type = 'string') {
        if (!$this->config['xss']['enabled']) {
            return $input;
        }
        
        switch ($type) {
            case 'email':
                return filter_var($input, FILTER_SANITIZE_EMAIL);
            case 'url':
                return filter_var($input, FILTER_SANITIZE_URL);
            case 'int':
                return filter_var($input, FILTER_SANITIZE_NUMBER_INT);
            case 'float':
                return filter_var($input, FILTER_SANITIZE_NUMBER_FLOAT, FILTER_FLAG_ALLOW_FRACTION);
            case 'html':
                return htmlspecialchars($input, ENT_QUOTES, 'UTF-8');
            default:
                return trim(strip_tags($input));
        }
    }
    
    /**
     * 转义输出
     */
    public function escapeOutput($output) {
        if (!$this->config['xss']['escape_output']) {
            return $output;
        }
        
        return htmlspecialchars($output, ENT_QUOTES, 'UTF-8');
    }
    
    /**
     * 获取客户端IP地址
     */
    public function getClientIP() {
        $ipKeys = [
            'HTTP_CF_CONNECTING_IP',
            'HTTP_CLIENT_IP',
            'HTTP_X_FORWARDED_FOR',
            'HTTP_X_FORWARDED',
            'HTTP_X_CLUSTER_CLIENT_IP',
            'HTTP_FORWARDED_FOR',
            'HTTP_FORWARDED',
            'REMOTE_ADDR'
        ];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }
    
    /**
     * 记录安全事件
     */
    public function logSecurityEvent($event, $data = []) {
        if ($this->logger) {
            $this->logger->log($event, array_merge($data, [
                'timestamp' => date('Y-m-d H:i:s'),
                'ip' => $this->getClientIP(),
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
                'request_uri' => $_SERVER['REQUEST_URI'] ?? ''
            ]));
        }
    }
    
    /**
     * 检测可疑活动
     */
    public function detectSuspiciousActivity($activity, $context = []) {
        $suspiciousPatterns = [
            'sql_injection' => '/(\bunion\b|\bselect\b|\binsert\b|\bupdate\b|\bdelete\b|\bdrop\b)/i',
            'xss_attempt' => '/<script|javascript:|vbscript:|onload=|onerror=/i',
            'path_traversal' => '/\.\.[\/\\\\]/',
            'command_injection' => '/(\||;|&|\$\(|\`)/i'
        ];
        
        foreach ($suspiciousPatterns as $type => $pattern) {
            if (preg_match($pattern, $activity)) {
                $this->logSecurityEvent('suspicious_activity_detected', [
                    'type' => $type,
                    'activity' => $activity,
                    'context' => $context
                ]);
                
                return [
                    'suspicious' => true,
                    'type' => $type,
                    'risk_level' => $this->calculateRiskLevel($type)
                ];
            }
        }
        
        return ['suspicious' => false];
    }
    
    /**
     * 计算风险等级
     */
    private function calculateRiskLevel($type) {
        $riskLevels = [
            'sql_injection' => 'high',
            'xss_attempt' => 'medium',
            'path_traversal' => 'high',
            'command_injection' => 'critical'
        ];
        
        return $riskLevels[$type] ?? 'low';
    }
}

/**
 * 安全日志记录器类
 */
class SecurityLogger {
    
    private $config;
    private $logFile;
    
    public function __construct($config) {
        $this->config = $config;
        $this->logFile = $config['log_file'];
        $this->ensureLogDirectory();
    }
    
    /**
     * 确保日志目录存在
     */
    private function ensureLogDirectory() {
        $logDir = dirname($this->logFile);
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
    }
    
    /**
     * 记录日志
     */
    public function log($event, $data) {
        if (!$this->config['enabled']) {
            return;
        }
        
        // 过滤敏感字段
        $filteredData = $this->filterSensitiveData($data);
        
        $logEntry = [
            'timestamp' => date('c'),
            'event' => $event,
            'data' => $filteredData,
            'severity' => $this->getSeverity($event)
        ];
        
        $logLine = json_encode($logEntry, JSON_UNESCAPED_UNICODE) . "\n";
        
        file_put_contents($this->logFile, $logLine, FILE_APPEND | LOCK_EX);
        
        // 检查文件大小并轮换
        $this->rotateLogIfNeeded();
    }
    
    /**
     * 过滤敏感数据
     */
    private function filterSensitiveData($data) {
        $sensitiveFields = $this->config['sensitive_fields'];
        
        foreach ($sensitiveFields as $field) {
            if (isset($data[$field])) {
                $data[$field] = '[REDACTED]';
            }
        }
        
        return $data;
    }
    
    /**
     * 获取事件严重性
     */
    private function getSeverity($event) {
        $severityMap = [
            'login_failure' => 'warning',
            'login_blocked' => 'error',
            'csrf_violation' => 'error',
            'rate_limit_exceeded' => 'warning',
            'ip_banned' => 'error',
            'suspicious_activity_detected' => 'critical',
            'security_violation' => 'critical'
        ];
        
        return $severityMap[$event] ?? 'info';
    }
    
    /**
     * 轮换日志文件
     */
    private function rotateLogIfNeeded() {
        if (!file_exists($this->logFile)) {
            return;
        }
        
        $fileSize = filesize($this->logFile);
        if ($fileSize >= $this->config['max_file_size']) {
            $timestamp = date('Y-m-d-H-i-s');
            $rotatedFile = $this->logFile . '.' . $timestamp;
            rename($this->logFile, $rotatedFile);
            
            // 压缩旧文件
            if (function_exists('gzopen')) {
                $this->compressLogFile($rotatedFile);
            }
            
            // 清理旧文件
            $this->cleanupOldLogs();
        }
    }
    
    /**
     * 压缩日志文件
     */
    private function compressLogFile($file) {
        $compressedFile = $file . '.gz';
        $input = fopen($file, 'rb');
        $output = gzopen($compressedFile, 'wb9');
        
        while (!feof($input)) {
            gzwrite($output, fread($input, 8192));
        }
        
        fclose($input);
        gzclose($output);
        unlink($file);
    }
    
    /**
     * 清理旧日志
     */
    private function cleanupOldLogs() {
        $logDir = dirname($this->logFile);
        $logBasename = basename($this->logFile);
        $files = glob($logDir . '/' . $logBasename . '.*');
        
        if (count($files) > $this->config['max_files']) {
            // 按修改时间排序
            usort($files, function($a, $b) {
                return filemtime($a) - filemtime($b);
            });
            
            // 删除最旧的文件
            $filesToDelete = array_slice($files, 0, count($files) - $this->config['max_files']);
            foreach ($filesToDelete as $file) {
                unlink($file);
            }
        }
    }
}
?>
