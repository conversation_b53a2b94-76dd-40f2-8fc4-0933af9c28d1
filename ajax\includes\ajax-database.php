<?php
/**
 * AJAX 专用数据库连接文件
 * 简化版本，专门为 ajax 目录下的文件提供数据库连接
 */

// 防止重复包含
if (defined('AJAX_DATABASE_LOADED')) {
    return $pdo;
}
define('AJAX_DATABASE_LOADED', true);

// 定义必要的常量
if (!defined('ROOT_PATH')) {
    define('ROOT_PATH', dirname(dirname(__DIR__)));
}

if (!defined('APP_INITIALIZED')) {
    define('APP_INITIALIZED', true);
}

// 设置 JSON 响应头（AJAX 文件通用）
if (!headers_sent()) {
    header('Content-Type: application/json; charset=utf-8');
}

try {
    // 加载数据库配置
    $configPath = ROOT_PATH . '/config/database.php';
    if (!file_exists($configPath)) {
        throw new Exception('Database configuration file not found');
    }
    
    $config = require $configPath;
    $dbConfig = $config['connections']['mysql'];
    
    // 创建数据库连接
    $dsn = "mysql:host={$dbConfig['host']};port={$dbConfig['port']};dbname={$dbConfig['database']};charset={$dbConfig['charset']}";
    $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password'], $dbConfig['options']);
    
} catch (Exception $e) {
    // AJAX 错误响应
    echo json_encode([
        'success' => false,
        'message' => 'Database connection failed',
        'error' => $e->getMessage()
    ]);
    exit;
}

/**
 * AJAX 响应辅助函数
 */
function sendAjaxSuccess($data = [], $message = 'Success') {
    echo json_encode([
        'success' => true,
        'message' => $message,
        'data' => $data
    ]);
    exit;
}

function sendAjaxError($message = 'An error occurred', $code = 400) {
    http_response_code($code);
    echo json_encode([
        'success' => false,
        'message' => $message
    ]);
    exit;
}

/**
 * 使用方法：
 * 
 * 在任何 ajax/*.php 文件的开头添加：
 * require_once __DIR__ . '/includes/ajax-database.php';
 * 
 * 然后直接使用 $pdo 变量和辅助函数：
 * $stmt = $pdo->prepare("SELECT * FROM table WHERE id = ?");
 * $stmt->execute([$id]);
 * $result = $stmt->fetch();
 * 
 * sendAjaxSuccess($result);
 */
?>
