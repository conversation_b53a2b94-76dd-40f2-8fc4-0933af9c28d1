<?php
/**
 * 需求详情页面
 */

// 获取slug参数
$slug = $_GET['slug'] ?? '';

if (empty($slug)) {
    header('Location: /requests');
    exit;
}

$currentPage = 'request-detail';

// 使用统一的数据库连接
require_once dirname(dirname(__DIR__)) . '/includes/database-connection.php';

// 数据库连接
try {
    // 获取需求详情
    $stmt = $pdo->prepare("
        SELECT r.*, m.username, m.avatar
        FROM pt_user_requests r
        LEFT JOIN pt_member m ON r.user_id = m.id
        WHERE r.slug = ?
    ");
    $stmt->execute([$slug]);
    $request = $stmt->fetch();

    if (!$request) {
        header('Location: /requests');
        exit;
    }



    // 获取相关需求（同分类的其他需求）
    $relatedStmt = $pdo->prepare("
        SELECT id, title, slug, status, created_at
        FROM pt_user_requests
        WHERE category = ? AND slug != ? AND status != 'rejected'
        ORDER BY created_at DESC
        LIMIT 5
    ");
    $relatedStmt->execute([$request['category'], $slug]);
    $relatedRequests = $relatedStmt->fetchAll();

} catch (Exception $e) {
    error_log("Request detail error: " . $e->getMessage());
    header('Location: /requests');
    exit;
}

// 状态样式映射 - 深色主题
$statusColors = [
    'pending' => 'bg-yellow-600 text-white',
    'reviewing' => 'bg-blue-600 text-white',
    'accepted' => 'bg-green-600 text-white',
    'rejected' => 'bg-red-600 text-white',
    'completed' => 'bg-purple-600 text-white'
];

$statusNames = [
    'pending' => 'Pending',
    'reviewing' => 'Under Review',
    'accepted' => 'Accepted', 
    'rejected' => 'Rejected',
    'completed' => 'Completed'
];

// 分类映射
$categoryNames = [
    'development' => 'Development Tools',
    'design' => 'Design Tools',
    'productivity' => 'Productivity',
    'marketing' => 'Marketing',
    'utilities' => 'Utilities',
    'other' => 'Other'
];

// SEO meta标签 - 使用标准格式，控制字符长度
$requestTitle = htmlspecialchars($request['title']);
// 标题控制在60字符以内，格式：请求标题 - Prompt2Tool
$pageTitle = (strlen($requestTitle) > 45) ? substr($requestTitle, 0, 42) . '...' : $requestTitle;
$pageTitle .= ' - Prompt2Tool';

// 描述控制在160字符以内
$requestDesc = htmlspecialchars($request['description']);
$pageDescription = (strlen($requestDesc) > 157) ? substr($requestDesc, 0, 154) . '...' : $requestDesc;

// 设置SEO数据 - 遵循项目标准
$seoData = [
    'title' => $pageTitle,
    'description' => $pageDescription,
    'og_url' => 'https://' . $_SERVER['HTTP_HOST'] . '/ideas/' . $slug,
    'canonical' => 'https://' . $_SERVER['HTTP_HOST'] . '/ideas/' . $slug
];

// 包含公共头部
include_once ROOT_PATH . '/templates/layout/header.php';
?>

<!-- JSON-LD Structured Data -->
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "Question",
    "name": "<?= addslashes(htmlspecialchars($request['title'])) ?>",
    "text": "<?= addslashes(htmlspecialchars($request['description'])) ?>",
    "dateCreated": "<?= date('c', strtotime($request['created_at'])) ?>",
    "author": {
        "@type": "Person",
        "name": "<?= addslashes(htmlspecialchars($request['username'] ?? 'Anonymous')) ?>"
    },
    "upvoteCount": 0,
    "url": "<?= 'https://' . $_SERVER['HTTP_HOST'] . '/ideas/' . $slug ?>",
    "about": {
        "@type": "Thing",
        "name": "<?= addslashes($categoryNames[$request['category']]) ?>"
    }
    <?php if (!empty($request['admin_reply'])): ?>
    ,
    "acceptedAnswer": {
        "@type": "Answer",
        "text": "<?= addslashes(htmlspecialchars($request['admin_reply'])) ?>",
        "dateCreated": "<?= $request['processed_at'] ? date('c', strtotime($request['processed_at'])) : '' ?>",
        "author": {
            "@type": "Organization",
            "name": "AI Tools Platform"
        }
    }
    <?php endif; ?>
}
</script>

<!-- 自定义样式 -->
<style>
    .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }


</style>

<!-- Main Content -->
<main class="bg-black min-h-screen">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Breadcrumb -->
        <nav class="mb-6">
            <ol class="flex items-center space-x-2 text-sm text-gray-400">
                <li><a href="/" class="hover:text-white transition-colors">Home</a></li>
                <li><i class="fas fa-chevron-right text-xs"></i></li>
                <li><a href="/requests" class="hover:text-white transition-colors">Tool Requests</a></li>
                <li><i class="fas fa-chevron-right text-xs"></i></li>
                <li class="text-gray-500 truncate max-w-xs" title="<?= htmlspecialchars($request['title']) ?>">
                    <?= htmlspecialchars(strlen($request['title']) > 50 ? substr($request['title'], 0, 47) . '...' : $request['title']) ?>
                </li>
            </ol>
        </nav>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Content -->
            <div class="lg:col-span-2">
                <!-- Request Header -->
                <div class="bg-secondary border border-gray-800 p-6 mb-6">
                    <div class="flex flex-wrap items-start justify-between mb-4">
                        <div class="flex-1 min-w-0">
                            <h1 class="text-2xl lg:text-3xl font-bold text-white mb-3 break-words">
                                <?= htmlspecialchars($request['title']) ?>
                            </h1>
                            
                            <div class="flex flex-wrap items-center gap-3 mb-4">
                                <span class="text-sm text-gray-400">
                                    <i class="fas fa-calendar-alt mr-1"></i>
                                    <?= date('M j, Y', strtotime($request['created_at'])) ?>
                                </span>
                            </div>
                        </div>
                        

                    </div>
                    
                    <!-- Author Info -->
                    <div class="flex items-center text-sm text-gray-300 mb-4">
                        <div class="w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center mr-3 text-white font-medium">
                            <?php if ($request['avatar']): ?>
                                <img src="<?= htmlspecialchars($request['avatar']) ?>" alt="Avatar" class="w-8 h-8 rounded-full">
                            <?php else: ?>
                                <?php
                                // 获取用户名的第一个字符（字母或数字）
                                $username = $request['username'] ?? 'Anonymous';
                                $firstChar = strtoupper(substr($username, 0, 1));
                                // 确保是字母或数字，否则使用默认图标
                                if (ctype_alnum($firstChar)) {
                                    echo $firstChar;
                                } else {
                                    echo '<i class="fas fa-user"></i>';
                                }
                                ?>
                            <?php endif; ?>
                        </div>
                        <span>Requested by <strong><?= htmlspecialchars($request['username'] ?? 'Anonymous') ?></strong></span>
                    </div>
                </div>

                <!-- Request Description -->
                <div class="bg-secondary border border-gray-800 p-6 mb-6">

                    <div class="prose prose-invert max-w-none">
                        <article class="text-gray-300 leading-relaxed">
                            <?php
                            $description = htmlspecialchars($request['description']);

                            // 智能分段处理 - 创建文章式布局
                            $paragraphs = [];

                            // 首先按双换行符分割（如果有的话）
                            $sections = preg_split('/\n\s*\n/', $description);

                            foreach ($sections as $section) {
                                $section = trim($section);
                                if (empty($section)) continue;

                                // 如果段落很长（超过300字符），尝试按句号分割
                                if (strlen($section) > 300) {
                                    $sentences = preg_split('/(?<=[.!?])\s+/', $section);
                                    $currentParagraph = '';

                                    foreach ($sentences as $sentence) {
                                        $sentence = trim($sentence);
                                        if (empty($sentence)) continue;

                                        // 如果当前段落加上新句子超过280字符，就开始新段落
                                        if (strlen($currentParagraph . ' ' . $sentence) > 280 && !empty($currentParagraph)) {
                                            $paragraphs[] = trim($currentParagraph);
                                            $currentParagraph = $sentence;
                                        } else {
                                            $currentParagraph .= (empty($currentParagraph) ? '' : ' ') . $sentence;
                                        }
                                    }

                                    if (!empty($currentParagraph)) {
                                        $paragraphs[] = trim($currentParagraph);
                                    }
                                } else {
                                    $paragraphs[] = $section;
                                }
                            }

                            // 如果没有分段，使用原始内容
                            if (empty($paragraphs)) {
                                $paragraphs = [$description];
                            }

                            // 输出格式化的段落 - 文章式样式
                            foreach ($paragraphs as $index => $paragraph) {
                                $paragraph = trim($paragraph);
                                if (empty($paragraph)) continue;

                                // 第一段使用引言样式
                                if ($index === 0) {
                                    echo '<p class="text-lg text-gray-200 leading-8 mb-6 font-medium">' . $paragraph . '</p>';
                                }
                                // 检查是否包含特征词汇，如果是功能描述段落，使用特殊样式
                                elseif (preg_match('/features|should include|target users|capabilities/i', $paragraph)) {
                                    echo '<div class="bg-gray-800 bg-opacity-50 p-4 rounded-lg mb-6">';
                                    echo '<p class="text-gray-200 leading-7">' . $paragraph . '</p>';
                                    echo '</div>';
                                }
                                // 普通段落
                                else {
                                    echo '<p class="text-gray-300 leading-7 mb-5 text-justify">' . $paragraph . '</p>';
                                }
                            }
                            ?>
                        </article>
                    </div>
                </div>

                <!-- Admin Reply -->
                <?php if (!empty($request['admin_reply'])): ?>
                <div class="p-4 border-2 border-yellow-400 rounded-lg mb-6">
                    <p class="text-sm font-semibold text-yellow-400 mb-2">Official Response</p>
                    <p class="text-sm text-gray-300 leading-relaxed mb-3"><?= htmlspecialchars($request['admin_reply']) ?></p>
                    <?php if ($request['processed_at']): ?>
                    <p class="text-xs text-gray-400">
                        Replied on <?= date('M j, Y \a\t g:i A', strtotime($request['processed_at'])) ?>
                    </p>
                    <?php endif; ?>
                </div>
                <?php endif; ?>

                <!-- Share Section -->
                <div class="bg-secondary border border-gray-800 p-6">
                    <h2 class="text-xl font-semibold mb-4 text-white">
                        <i class="fas fa-share-alt mr-2"></i>Share This Request
                    </h2>
                    <div class="grid grid-cols-2 gap-3">
                        <button onclick="shareOnTwitter()" class="flex items-center justify-center px-3 py-2 bg-sky-500 hover:bg-sky-600 transition-colors text-sm text-white font-medium">
                            <i class="fab fa-twitter mr-2 text-base"></i>Twitter
                        </button>
                        <button onclick="shareOnLinkedIn()" class="flex items-center justify-center px-3 py-2 bg-blue-600 hover:bg-blue-700 transition-colors text-sm text-white font-medium">
                            <i class="fab fa-linkedin mr-2 text-base"></i>LinkedIn
                        </button>
                        <button onclick="shareOnFacebook()" class="flex items-center justify-center px-3 py-2 bg-blue-500 hover:bg-blue-600 transition-colors text-sm text-white font-medium">
                            <i class="fab fa-facebook mr-2 text-base"></i>Facebook
                        </button>
                        <button onclick="shareOnWhatsApp()" class="flex items-center justify-center px-3 py-2 bg-green-500 hover:bg-green-600 transition-colors text-sm text-white font-medium">
                            <i class="fab fa-whatsapp mr-2 text-base"></i>WhatsApp
                        </button>
                        <button onclick="shareOnTelegram()" class="flex items-center justify-center px-3 py-2 bg-cyan-500 hover:bg-cyan-600 transition-colors text-sm text-white font-medium">
                            <i class="fab fa-telegram mr-2 text-base"></i>Telegram
                        </button>
                        <button onclick="shareOnReddit()" class="flex items-center justify-center px-3 py-2 bg-orange-500 hover:bg-orange-600 transition-colors text-sm text-white font-medium">
                            <i class="fab fa-reddit mr-2 text-base"></i>Reddit
                        </button>
                        <button onclick="shareViaEmail()" class="flex items-center justify-center px-3 py-2 bg-red-500 hover:bg-red-600 transition-colors text-sm text-white font-medium">
                            <i class="fas fa-envelope mr-2 text-base"></i>Email
                        </button>
                        <button onclick="copyToClipboard()" class="flex items-center justify-center px-3 py-2 bg-purple-500 hover:bg-purple-600 transition-colors text-sm text-white font-medium">
                            <i class="fas fa-copy mr-2 text-base"></i>Copy Link
                        </button>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1">
                <!-- Request Stats -->
                <div class="bg-secondary border border-gray-800 p-6 mb-6">
                    <h3 class="text-lg font-semibold mb-4 text-white">Request Stats</h3>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-400">Votes:</span>
                            <span class="font-medium text-white"><?= (int)($request['votes'] ?? 0) ?></span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Status:</span>
                            <span class="font-medium text-white"><?= $statusNames[$request['status']] ?></span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Category:</span>
                            <span class="font-medium text-white"><?= $categoryNames[$request['category']] ?></span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">Priority:</span>
                            <span class="font-medium capitalize text-white"><?= $request['priority'] ?></span>
                        </div>
                    </div>
                </div>

                <!-- Related Requests -->
                <?php if (!empty($relatedRequests)): ?>
                <div class="bg-secondary border border-gray-800 p-6">
                    <h3 class="text-lg font-semibold mb-4 text-white">Related Requests</h3>
                    <div class="space-y-3">
                        <?php foreach ($relatedRequests as $related): ?>
                        <a href="/ideas/<?= $related['slug'] ?>" class="block p-3 hover:bg-gray-800 transition-colors">
                            <h4 class="font-medium text-sm mb-1 line-clamp-2 text-white"><?= htmlspecialchars($related['title']) ?></h4>
                            <div class="flex items-center justify-between text-xs text-gray-400">
                                <span class="capitalize"><?= $related['status'] ?></span>
                                <span><?= date('M j', strtotime($related['created_at'] ?? 'now')) ?></span>
                            </div>
                        </a>
                        <?php endforeach; ?>
                    </div>

                    <div class="mt-4 pt-4 border-t border-gray-700">
                        <form method="POST" action="/requests" class="inline">
                            <input type="hidden" name="category" value="<?= $request['category'] ?>">
                            <button type="submit" class="text-accent hover:text-blue-300 text-sm transition-colors bg-transparent border-none cursor-pointer">
                                View all <?= $categoryNames[$request['category']] ?> requests →
                            </button>
                        </form>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</main>

<?php include_once ROOT_PATH . '/templates/layout/footer.php'; ?>

    <!-- Scripts -->
    <script>
        // Toast 提示系统
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `fixed top-4 right-4 z-50 px-6 py-4 shadow-lg transition-all duration-300 transform ${
                type === 'success' ? 'bg-green-600 text-white' :
                type === 'error' ? 'bg-red-600 text-white' :
                type === 'warning' ? 'bg-yellow-600 text-white' :
                'bg-blue-600 text-white'
            }`;

            toast.innerHTML = `
                <div class="flex items-center justify-between">
                    <span>${message}</span>
                    <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-current opacity-70 hover:opacity-100">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            `;

            document.body.appendChild(toast);

            // 自动移除
            setTimeout(() => {
                if (toast.parentElement) {
                    toast.style.opacity = '0';
                    setTimeout(() => toast.remove(), 300);
                }
            }, 4000);
        }



        // 分享功能
        function shareOnTwitter() {
            const url = encodeURIComponent(window.location.href);
            const text = encodeURIComponent('Check out this tool request: <?= addslashes($request['title']) ?>');
            window.open(`https://twitter.com/intent/tweet?url=${url}&text=${text}`, '_blank');
        }

        function shareOnLinkedIn() {
            const url = encodeURIComponent(window.location.href);
            window.open(`https://www.linkedin.com/sharing/share-offsite/?url=${url}`, '_blank');
        }

        function shareOnFacebook() {
            const url = encodeURIComponent(window.location.href);
            window.open(`https://www.facebook.com/sharer/sharer.php?u=${url}`, '_blank');
        }

        function shareOnWhatsApp() {
            const url = encodeURIComponent(window.location.href);
            const text = encodeURIComponent('Check out this tool request: <?= addslashes($request['title']) ?>');
            window.open(`https://wa.me/?text=${text}%20${url}`, '_blank');
        }

        function shareOnTelegram() {
            const url = encodeURIComponent(window.location.href);
            const text = encodeURIComponent('Check out this tool request: <?= addslashes($request['title']) ?>');
            window.open(`https://t.me/share/url?url=${url}&text=${text}`, '_blank');
        }

        function shareOnReddit() {
            const url = encodeURIComponent(window.location.href);
            const title = encodeURIComponent('<?= addslashes($request['title']) ?>');
            window.open(`https://www.reddit.com/submit?url=${url}&title=${title}`, '_blank');
        }

        function shareViaEmail() {
            const url = window.location.href;
            const title = '<?= addslashes($request['title']) ?>';
            const subject = encodeURIComponent('Tool Request: ' + title);
            const body = encodeURIComponent('Check out this tool request:\n\n' + title + '\n\n' + url);

            // 创建 mailto 链接
            const mailtoLink = `mailto:?subject=${subject}&body=${body}`;

            // 尝试打开邮件客户端
            try {
                window.location.href = mailtoLink;
                showToast('Opening email client...', 'info');
            } catch (error) {
                // 如果失败，复制到剪贴板作为备选方案
                navigator.clipboard.writeText(`Subject: Tool Request: ${title}\n\nCheck out this tool request:\n\n${title}\n\n${url}`).then(() => {
                    showToast('Email content copied to clipboard!', 'success');
                }).catch(() => {
                    showToast('Failed to open email client', 'error');
                });
            }
        }

        function copyToClipboard() {
            navigator.clipboard.writeText(window.location.href).then(() => {
                showToast('Link copied to clipboard!', 'success');
            }).catch(() => {
                showToast('Failed to copy link', 'error');
            });
        }


    </script>

    <style>
        .prose p {
            margin-bottom: 1rem;
        }
    </style>
</body>
</html>
