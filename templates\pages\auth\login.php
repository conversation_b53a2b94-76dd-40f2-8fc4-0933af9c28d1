<?php
/**
 * User Login Page
 * 支持传统登录和Google一键登录
 */

$currentPage = 'login';
$breadcrumbs = [
    ['name' => 'Home', 'url' => '/'],
    ['name' => 'Login']
];

// 包含动态SEO助手
require_once ROOT_PATH . '/app/helpers/dynamic-seo.php';

// 生成SEO数据
$seoData = [
    'title' => 'Login - Prompt2Tool',
    'description' => 'Sign in to your Prompt2Tool account to access premium features and save your preferences.',
    'keywords' => 'login, sign in, user account, prompt2tool',
    'og_title' => 'Login - Prompt2Tool',
    'og_description' => 'Sign in to your Prompt2Tool account to access premium features.',
    'canonical' => getCurrentURL()
];

// 获取Google OAuth配置
$googleClientId = '';
try {
    // 使用统一的数据库连接
    require_once ROOT_PATH . '/includes/database-connection.php';
    $stmt = $pdo->prepare("SELECT setting_value FROM pt_system_config WHERE setting_key = 'google_oauth_client_id'");
    $stmt->execute();
    $result = $stmt->fetch();
    if ($result) {
        $googleClientId = $result['setting_value'];
    }
} catch (Exception $e) {
    // 静默处理错误
}

// 包含公共头部
include_once ROOT_PATH . '/templates/layout/header.php';
?>

<!-- 登录页面主体 -->
<div class="bg-gray-900 text-white min-h-screen">
    <div class="max-w-md mx-auto pt-20 pb-12 px-4">
        <div class="bg-gray-800 border border-gray-700 p-8">
            <!-- Logo和标题 -->
            <div class="text-center mb-8">
                <div class="w-16 h-16 bg-blue-600 text-white flex items-center justify-center text-2xl font-bold mx-auto mb-4">
                    P
                </div>
                <h1 class="text-2xl font-bold text-white mb-2">Welcome Back</h1>
                <p class="text-gray-400">Sign in to your account</p>
            </div>

            <!-- 错误/成功消息 -->
            <div id="messageArea" class="hidden mb-6">
                <div id="messageContent" class="p-4 text-sm"></div>
            </div>

            <!-- Google登录按钮 -->
            <?php if (!empty($googleClientId)): ?>
            <div class="mb-6">
                <button id="googleSignIn" class="w-full bg-white text-gray-900 py-3 px-4 hover:bg-gray-100 transition-colors flex items-center justify-center space-x-3 font-medium">
                    <svg class="w-5 h-5" viewBox="0 0 24 24">
                        <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                        <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                        <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                        <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                    </svg>
                    <span>Continue with Google</span>
                </button>
            </div>

            <!-- 分隔线 -->
            <div class="relative mb-6">
                <div class="absolute inset-0 flex items-center">
                    <div class="w-full border-t border-gray-600"></div>
                </div>
                <div class="relative flex justify-center text-sm">
                    <span class="px-2 bg-gray-800 text-gray-400">Or continue with email</span>
                </div>
            </div>
            <?php endif; ?>

            <!-- 传统登录表单 -->
            <form id="loginForm" class="space-y-6">
                <!-- 邮箱 -->
                <div>
                    <label for="email" class="block text-sm font-medium text-gray-300 mb-2">
                        Email Address
                    </label>
                    <input type="email"
                           id="email"
                           name="email"
                           required
                           autocomplete="email"
                           class="w-full bg-gray-900 border border-gray-600 text-gray-100 p-3 focus:outline-none focus:border-blue-500"
                           placeholder="Enter your email">
                </div>

                <!-- 密码 -->
                <div>
                    <label for="password" class="block text-sm font-medium text-gray-300 mb-2">
                        Password
                    </label>
                    <input type="password"
                           id="password"
                           name="password"
                           required
                           autocomplete="current-password"
                           class="w-full bg-gray-900 border border-gray-600 text-gray-100 p-3 focus:outline-none focus:border-blue-500"
                           placeholder="Enter your password">
                </div>

                <!-- 记住我 -->
                <div class="flex items-center">
                    <label class="flex items-center">
                        <input type="checkbox" id="remember" name="remember" class="mr-2 w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 focus:ring-blue-500">
                        <span class="text-sm text-gray-300">Remember me</span>
                    </label>
                </div>

                <!-- 登录按钮 -->
                <button type="submit" class="w-full bg-blue-600 text-white py-3 hover:bg-blue-700 transition-colors font-medium">
                    Sign In
                </button>
            </form>

            <!-- 注册链接 -->
            <div class="mt-6 text-center">
                <p class="text-gray-400">
                    Don't have an account?
                    <a href="/auth/register" class="text-blue-400 hover:text-blue-300 font-medium">
                        Sign up
                    </a>
                </p>
            </div>
        </div>
    </div>
</div>

<!-- Google Sign-In API -->
<?php if (!empty($googleClientId)): ?>
<script src="https://accounts.google.com/gsi/client" async defer></script>
<?php endif; ?>

<!-- 自定义Google按钮样式 -->
<style>
/* 覆盖Google按钮的默认样式 */
#googleSignIn {
    background: #1f2937 !important;
    border: 1px solid #4b5563 !important;
    border-radius: 0 !important;
    transition: all 0.2s ease !important;
}

#googleSignIn:hover {
    background: #374151 !important;
    border-color: #6b7280 !important;
}

/* 移除iframe的白色背景 */
#googleSignIn iframe {
    background: transparent !important;
    border-radius: 0 !important;
    filter: invert(1) hue-rotate(180deg) !important;
}

/* 确保Google按钮容器样式 */
.S9gUrf-YoZ4jf {
    background: transparent !important;
    border: none !important;
}

/* 移除Google按钮的白色背景和边框 */
#googleSignIn .L5Fo6c-PQbLGe {
    background: transparent !important;
    border: none !important;
    border-radius: 0 !important;
}

/* 强制覆盖Google按钮样式 */
#googleSignIn div[role="button"] {
    background: #1f2937 !important;
    border: 1px solid #4b5563 !important;
    color: white !important;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const loginForm = document.getElementById('loginForm');
    const messageArea = document.getElementById('messageArea');
    const messageContent = document.getElementById('messageContent');

    // 显示消息
    function showMessage(message, type = 'error') {
        messageContent.textContent = message;
        messageContent.className = `p-4 text-sm ${type === 'error' ? 'bg-red-600 text-white' : 'bg-green-600 text-white'}`;
        messageArea.classList.remove('hidden');

        // 自动隐藏成功消息
        if (type === 'success') {
            setTimeout(() => {
                messageArea.classList.add('hidden');
            }, 3000);
        }
    }

    // 检查URL参数，显示验证成功消息
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('verified') === '1') {
        showMessage('Email verified successfully! You can now log in to your account.', 'success');
        // 清理URL参数
        window.history.replaceState({}, document.title, window.location.pathname);
    }

    // 传统登录表单提交
    loginForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const formData = new FormData(loginForm);
        const submitBtn = loginForm.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;
        
        submitBtn.textContent = 'Signing In...';
        submitBtn.disabled = true;
        
        try {
            const response = await fetch('/api/v1/auth/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    email: formData.get('email'),
                    password: formData.get('password'),
                    remember: formData.get('remember') === 'on'
                })
            });

            // 检查HTTP状态码
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            // 检查响应是否为JSON
            const contentType = response.headers.get('content-type');
            if (!contentType || !contentType.includes('application/json')) {
                // 如果不是JSON响应，可能是重定向或其他问题
                console.log('Non-JSON response received, checking if login was successful...');
                // 直接重定向到用户中心，因为登录可能已经成功
                window.location.replace('/user-center/');
                return;
            }

            const data = await response.json();

            if (data.success) {
                showMessage('Login successful! Redirecting...', 'success');
                setTimeout(() => {
                    window.location.replace(data.data?.redirect || '/user-center/');
                }, 1000);
            } else {
                showMessage(data.message || 'Login failed. Please try again.');
            }
        } catch (error) {
            console.error('Login error:', error);
            // 如果是网络错误但登录可能成功了，尝试重定向
            if (error.message.includes('Failed to fetch') || error.message.includes('JSON')) {
                console.log('Possible successful login with response error, redirecting...');
                showMessage('Login may have succeeded. Redirecting...', 'success');
                setTimeout(() => {
                    window.location.replace('/user-center/');
                }, 1500);
            } else {
                showMessage('Network error. Please try again.');
            }
        } finally {
            submitBtn.textContent = originalText;
            submitBtn.disabled = false;
        }
    });

    <?php if (!empty($googleClientId)): ?>
    // Google登录初始化
    window.onload = function() {
        try {
            // 等待Google API加载完成
            if (typeof google === 'undefined' || !google.accounts) {
                console.error('Google Identity Services not loaded');
                return;
            }

            google.accounts.id.initialize({
                client_id: '<?= htmlspecialchars($googleClientId) ?>',
                callback: handleGoogleSignIn,
                auto_select: false,
                cancel_on_tap_outside: true
            });

            // 渲染Google登录按钮
            google.accounts.id.renderButton(
                document.getElementById('googleSignIn'),
                {
                    theme: 'filled_black',
                    size: 'large',
                    width: '100%',
                    text: 'continue_with',
                    shape: 'rectangular',
                    logo_alignment: 'left'
                }
            );

            console.log('Google Sign-In initialized successfully');
        } catch (error) {
            console.error('Google Sign-In initialization error:', error);
            // 隐藏Google登录按钮如果初始化失败
            const googleContainer = document.getElementById('googleSignIn');
            if (googleContainer) {
                googleContainer.style.display = 'none';
            }
        }
    };

    // 处理Google登录回调
    function handleGoogleSignIn(response) {
        console.log('Google Sign-In response received');

        // 显示加载状态
        showMessage('Signing in with Google...', 'success');

        // 创建一个隐藏的表单来提交Google凭证
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '/api/v1/auth/google-login';
        form.style.display = 'none';

        const credentialInput = document.createElement('input');
        credentialInput.type = 'hidden';
        credentialInput.name = 'credential';
        credentialInput.value = response.credential;

        const redirectInput = document.createElement('input');
        redirectInput.type = 'hidden';
        redirectInput.name = 'redirect';
        redirectInput.value = '/dashboard';

        form.appendChild(credentialInput);
        form.appendChild(redirectInput);
        document.body.appendChild(form);

        // 提交表单，这将导致页面重定向，避免跨域问题
        form.submit();
    }
    <?php endif; ?>
});
</script>

<?php include_once ROOT_PATH . '/templates/layout/footer.php'; ?>
