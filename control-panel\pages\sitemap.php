<?php
/**
 * XML Sitemap管理页面
 * 生成和管理网站的XML sitemap文件
 */

// 使用统一的数据库连接
require_once ROOT_PATH . '/includes/database-connection.php';

// 获取sitemap统计数据
try {
    // 静态页面数量
    $staticPagesCount = 7; // 首页、pricing、about、contact、help、privacy、terms
    
    // 工具页面数量
    $toolsCount = $pdo->query("
        SELECT COUNT(*) FROM pt_tool t
        LEFT JOIN pt_tool_category c ON t.category_id = c.id
        WHERE t.status = 'active' AND c.status = 'active'
    ")->fetchColumn();
    
    // 工具分类页面数量
    $categoriesCount = $pdo->query("
        SELECT COUNT(*) FROM pt_tool_category WHERE status = 'active'
    ")->fetchColumn();
    
    // 请求详情页面数量
    $requestsCount = $pdo->query("
        SELECT COUNT(*) FROM pt_user_requests 
        WHERE status IN ('accepted', 'completed')
    ")->fetchColumn();
    
    // 产品启动页面数量
    $launchesCount = $pdo->query("
        SELECT COUNT(*) FROM pt_product_launches 
        WHERE launch_status = 'launched'
    ")->fetchColumn();
    
    // 列表页面数量
    $listPagesCount = 4; // tools、categories、requests、launches
    
    // 总URL数量
    $totalUrls = $staticPagesCount + $toolsCount + $categoriesCount + $requestsCount + $launchesCount + $listPagesCount;
    
    // 检查sitemap文件状态
    $sitemapPath = ROOT_PATH . '/public/sitemap.xml';
    $sitemapExists = file_exists($sitemapPath);
    $sitemapSize = $sitemapExists ? filesize($sitemapPath) : 0;
    $sitemapModified = $sitemapExists ? filemtime($sitemapPath) : null;
    
} catch (Exception $e) {
    $totalUrls = 0;
    $sitemapExists = false;
    $sitemapSize = 0;
    $sitemapModified = null;
}

/**
 * 格式化文件大小
 */
function formatFileSize($bytes) {
    if ($bytes >= 1048576) {
        return number_format($bytes / 1048576, 2) . ' MB';
    } elseif ($bytes >= 1024) {
        return number_format($bytes / 1024, 2) . ' KB';
    } else {
        return $bytes . ' bytes';
    }
}

// 使用全局的timeAgo函数（在functions.php中定义）
?>

<!-- 页面描述和操作 -->
<div class="mb-8">
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 mb-2">Sitemap Management</h1>
            <p class="text-gray-600">Generate and manage XML sitemap for search engines</p>
        </div>
        <div class="flex space-x-3">
            <?php if ($sitemapExists): ?>
            <a href="/sitemap.xml" target="_blank" class="bg-gray-600 text-white px-4 py-2 hover:bg-gray-700 transition-colors">
                View Sitemap
            </a>
            <?php endif; ?>
            <button onclick="generateSitemap()" class="bg-accent text-white px-4 py-2 hover:bg-blue-600 transition-colors">
                Generate Sitemap
            </button>
        </div>
    </div>
</div>

<!-- 统计卡片 -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- 总URL数量 -->
    <div class="admin-card stat-card p-6">
        <div class="flex items-center">
            <div class="p-3 bg-blue-100 text-blue-600 mr-4">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
                </svg>
            </div>
            <div>
                <p class="text-sm font-medium text-gray-600">Total URLs</p>
                <p class="text-2xl font-bold text-gray-900"><?= number_format($totalUrls) ?></p>
            </div>
        </div>
    </div>

    <!-- 工具页面 -->
    <div class="admin-card stat-card p-6">
        <div class="flex items-center">
            <div class="p-3 bg-green-100 text-green-600 mr-4">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
            </div>
            <div>
                <p class="text-sm font-medium text-gray-600">Tool Pages</p>
                <p class="text-2xl font-bold text-gray-900"><?= number_format($toolsCount) ?></p>
            </div>
        </div>
    </div>

    <!-- 分类页面 -->
    <div class="admin-card stat-card p-6">
        <div class="flex items-center">
            <div class="p-3 bg-yellow-100 text-yellow-600 mr-4">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                </svg>
            </div>
            <div>
                <p class="text-sm font-medium text-gray-600">Category Pages</p>
                <p class="text-2xl font-bold text-gray-900"><?= number_format($categoriesCount) ?></p>
            </div>
        </div>
    </div>

    <!-- 文件状态 -->
    <div class="admin-card stat-card p-6">
        <div class="flex items-center">
            <div class="p-3 <?= $sitemapExists ? 'bg-green-100 text-green-600' : 'bg-red-100 text-red-600' ?> mr-4">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <?php if ($sitemapExists): ?>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    <?php else: ?>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    <?php endif; ?>
                </svg>
            </div>
            <div>
                <p class="text-sm font-medium text-gray-600">Sitemap Status</p>
                <p class="text-2xl font-bold <?= $sitemapExists ? 'text-green-600' : 'text-red-600' ?>">
                    <?= $sitemapExists ? 'Generated' : 'Not Found' ?>
                </p>
            </div>
        </div>
    </div>
</div>

<!-- 内容区域 -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
    <!-- URL分类统计 -->
    <div class="admin-card">
        <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">URL Categories</h3>
        </div>
        <div class="p-6">
            <div class="space-y-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-3 h-3 bg-blue-500 rounded-full mr-3"></div>
                        <span class="text-sm font-medium text-gray-900">Static Pages</span>
                    </div>
                    <span class="text-sm font-bold text-gray-900"><?= $staticPagesCount ?></span>
                </div>
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-3 h-3 bg-green-500 rounded-full mr-3"></div>
                        <span class="text-sm font-medium text-gray-900">Tool Detail Pages</span>
                    </div>
                    <span class="text-sm font-bold text-gray-900"><?= $toolsCount ?></span>
                </div>
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-3 h-3 bg-yellow-500 rounded-full mr-3"></div>
                        <span class="text-sm font-medium text-gray-900">Category Pages</span>
                    </div>
                    <span class="text-sm font-bold text-gray-900"><?= $categoriesCount ?></span>
                </div>
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-3 h-3 bg-purple-500 rounded-full mr-3"></div>
                        <span class="text-sm font-medium text-gray-900">Request Pages</span>
                    </div>
                    <span class="text-sm font-bold text-gray-900"><?= $requestsCount ?></span>
                </div>
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-3 h-3 bg-red-500 rounded-full mr-3"></div>
                        <span class="text-sm font-medium text-gray-900">Launch Pages</span>
                    </div>
                    <span class="text-sm font-bold text-gray-900"><?= $launchesCount ?></span>
                </div>
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="w-3 h-3 bg-gray-500 rounded-full mr-3"></div>
                        <span class="text-sm font-medium text-gray-900">List Pages</span>
                    </div>
                    <span class="text-sm font-bold text-gray-900"><?= $listPagesCount ?></span>
                </div>
            </div>
        </div>
    </div>

    <!-- 文件信息 -->
    <div class="admin-card">
        <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Sitemap File Information</h3>
        </div>
        <div class="p-6">
            <?php if ($sitemapExists): ?>
            <div class="space-y-4">
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-gray-600">File Path</span>
                    <span class="text-sm text-gray-900 font-mono">/public/sitemap.xml</span>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-gray-600">File Size</span>
                    <span class="text-sm text-gray-900"><?= formatFileSize($sitemapSize) ?></span>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-gray-600">Last Modified</span>
                    <span class="text-sm text-gray-900"><?= timeAgo(date('Y-m-d H:i:s', $sitemapModified)) ?></span>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-gray-600">Status</span>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        Active
                    </span>
                </div>
            </div>
            <?php else: ?>
            <div class="text-center py-8">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">No sitemap file found</h3>
                <p class="mt-1 text-sm text-gray-500">Generate a sitemap to get started</p>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- 生成状态显示 -->
<div id="generationStatus" class="mt-8 hidden">
    <div class="admin-card p-6">
        <div class="flex items-center">
            <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mr-4"></div>
            <div>
                <h3 class="text-lg font-semibold text-gray-900">Generating Sitemap...</h3>
                <p class="text-sm text-gray-600">Please wait while we collect all URLs and generate the XML sitemap.</p>
            </div>
        </div>
    </div>
</div>

<!-- 生成结果显示 -->
<div id="generationResult" class="mt-8 hidden">
    <div class="admin-card p-6">
        <div id="resultContent">
            <!-- 动态内容将在这里显示 -->
        </div>
    </div>
</div>

<script>
// 生成sitemap功能
function generateSitemap() {
    // 显示生成状态
    document.getElementById('generationStatus').classList.remove('hidden');
    document.getElementById('generationResult').classList.add('hidden');

    // 禁用生成按钮
    const generateBtn = document.querySelector('button[onclick="generateSitemap()"]');
    generateBtn.disabled = true;
    generateBtn.innerHTML = 'Generating...';

    // 发送AJAX请求
    fetch('../api/generate-sitemap.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'generate'
        })
    })
    .then(response => response.json())
    .then(data => {
        // 隐藏生成状态
        document.getElementById('generationStatus').classList.add('hidden');

        // 显示结果
        showGenerationResult(data);

        // 恢复按钮状态
        generateBtn.disabled = false;
        generateBtn.innerHTML = 'Generate Sitemap';

        // 如果成功，更新页面数据而不是刷新整个页面
        if (data.success) {
            updatePageData();
        }
    })
    .catch(error => {
        console.error('Error:', error);

        // 隐藏生成状态
        document.getElementById('generationStatus').classList.add('hidden');

        // 显示错误结果
        showGenerationResult({
            success: false,
            message: 'Failed to generate sitemap',
            error: error.message
        });

        // 恢复按钮状态
        generateBtn.disabled = false;
        generateBtn.innerHTML = 'Generate Sitemap';
    });
}

// 显示生成结果
function showGenerationResult(data) {
    const resultDiv = document.getElementById('generationResult');
    const contentDiv = document.getElementById('resultContent');

    if (data.success) {
        contentDiv.innerHTML = `
            <div class="flex items-center mb-4">
                <div class="p-2 bg-green-100 text-green-600 rounded-full mr-4">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-green-800">Sitemap Generated Successfully!</h3>
                    <p class="text-sm text-green-600">${data.message}</p>
                </div>
            </div>
            ${data.stats ? `
            <div class="bg-green-50 border border-green-200 rounded p-4">
                <h4 class="font-medium text-green-800 mb-2">Generation Statistics:</h4>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>
                        <span class="text-green-600">Total URLs:</span>
                        <span class="font-bold text-green-800">${data.stats.total_urls}</span>
                    </div>
                    <div>
                        <span class="text-green-600">File Size:</span>
                        <span class="font-bold text-green-800">${data.stats.file_size}</span>
                    </div>
                    <div>
                        <span class="text-green-600">Generation Time:</span>
                        <span class="font-bold text-green-800">${data.stats.generation_time}s</span>
                    </div>
                    <div>
                        <span class="text-green-600">Status:</span>
                        <span class="font-bold text-green-800">Active</span>
                    </div>
                </div>
            </div>
            ` : ''}
        `;
    } else {
        contentDiv.innerHTML = `
            <div class="flex items-center mb-4">
                <div class="p-2 bg-red-100 text-red-600 rounded-full mr-4">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </div>
                <div>
                    <h3 class="text-lg font-semibold text-red-800">Generation Failed</h3>
                    <p class="text-sm text-red-600">${data.message}</p>
                </div>
            </div>
            ${data.error ? `
            <div class="bg-red-50 border border-red-200 rounded p-4">
                <h4 class="font-medium text-red-800 mb-2">Error Details:</h4>
                <p class="text-sm text-red-600 font-mono">${data.error}</p>
            </div>
            ` : ''}
        `;
    }

    resultDiv.classList.remove('hidden');
}

// 更新页面数据而不刷新整个页面
function updatePageData() {
    // 延迟2秒后更新文件信息
    setTimeout(() => {
        // 更新View Sitemap按钮显示
        const viewBtn = document.querySelector('a[href="/sitemap.xml"]');
        if (!viewBtn) {
            // 如果按钮不存在，添加它
            const generateBtn = document.querySelector('button[onclick="generateSitemap()"]');
            const viewBtnHtml = `
                <a href="/sitemap.xml" target="_blank" class="bg-gray-600 text-white px-4 py-2 hover:bg-gray-700 transition-colors">
                    View Sitemap
                </a>
            `;
            generateBtn.insertAdjacentHTML('beforebegin', viewBtnHtml);
        }

        // 可以在这里添加更多的页面数据更新逻辑
        // 比如更新文件大小、修改时间等
    }, 2000);
}
</script>
