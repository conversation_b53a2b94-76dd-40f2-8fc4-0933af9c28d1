/* AI Tools Platform - 主样式文件 */
/* 基于 Tailwind CSS v4.1 + Replicate极简设计风格 */

/* Tailwind CSS CDN导入 */
@import url('https://cdn.tailwindcss.com');

/* 自定义CSS变量 */
:root {
  --primary-color: #000000;
  --secondary-color: #1a1a1a;
  --accent-color: #4e73df;
  --success-color: #1cc88a;
  --warning-color: #f6c23e;
  --danger-color: #e74a3b;
  --text-color: #ffffff;
  --bg-color: #000000;
  --border-color: #333333;
  --card-bg: #111111;
  --hover-bg: #222222;
}

/* 全局样式重置 - 零圆角设计 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  border-radius: 0 !important; /* 零圆角设计核心 */
}

/* 基础字体和布局 */
body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  background-color: var(--bg-color);
  color: var(--text-color);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 链接样式 */
a {
  color: var(--accent-color);
  text-decoration: none;
  transition: color 0.2s ease;
}

a:hover {
  color: #6c8cff;
}

/* 标题样式 */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: 1rem;
}

h1 { font-size: 2rem; }    /* 32px */
h2 { font-size: 1.5rem; }  /* 24px */
h3 { font-size: 1.25rem; } /* 20px */

/* 按钮基础样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  border-radius: 0 !important; /* 零圆角 */
}

.btn-primary {
  background-color: var(--accent-color);
  color: white;
}

.btn-primary:hover {
  background-color: #3d5fd8;
  transform: translateY(-1px);
}

.btn-secondary {
  background-color: transparent;
  color: var(--text-color);
  border: 1px solid var(--border-color);
}

.btn-secondary:hover {
  background-color: var(--hover-bg);
  border-color: var(--accent-color);
}

/* 卡片样式 */
.card {
  background-color: var(--card-bg);
  border: 1px solid var(--border-color);
  padding: 1.5rem;
  transition: all 0.3s ease;
  border-radius: 0 !important; /* 零圆角 */
}

.card:hover {
  border-color: var(--accent-color);
  transform: translateY(-2px);
}

/* 输入框样式 */
.input {
  width: 100%;
  padding: 0.75rem 1rem;
  background-color: var(--card-bg);
  border: 1px solid var(--border-color);
  color: var(--text-color);
  transition: border-color 0.2s ease;
  border-radius: 0 !important; /* 零圆角 */
}

.input:focus {
  outline: none;
  border-color: var(--accent-color);
}

.input::placeholder {
  color: #666;
}

/* 工具类 */
.text-accent { color: var(--accent-color); }
.text-success { color: var(--success-color); }
.text-warning { color: var(--warning-color); }
.text-danger { color: var(--danger-color); }

.bg-card { background-color: var(--card-bg); }
.bg-hover { background-color: var(--hover-bg); }

.border-accent { border-color: var(--accent-color); }

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 0 1rem;
  }
  
  h1 { font-size: 1.75rem; }
  h2 { font-size: 1.25rem; }
  h3 { font-size: 1.125rem; }
  
  .btn {
    padding: 0.625rem 1.25rem;
    font-size: 0.875rem;
  }
}

/* 导入组件样式 */
@import './components/buttons.css';
@import './components/cards.css';
@import './components/forms.css';
@import './components/navigation.css';
@import './components/modals.css';

/* 加载动画 */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

.fade-in {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}
