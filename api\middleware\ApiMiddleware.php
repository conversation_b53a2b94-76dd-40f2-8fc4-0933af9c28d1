<?php
/**
 * API中间件
 * 处理API请求的通用逻辑
 */

class ApiMiddleware {
    
    private $rateLimit;
    
    public function __construct() {
        $this->rateLimit = new ApiRateLimit();
    }
    
    /**
     * 处理请求
     */
    public function handle() {
        // 记录请求开始时间
        if (!defined('API_START_TIME')) {
            define('API_START_TIME', microtime(true));
        }
        
        // 设置调试模式
        if (!defined('API_DEBUG')) {
            define('API_DEBUG', false);
        }
        
        // 检查维护模式
        if ($this->isMaintenanceMode()) {
            ApiResponse::error([
                'message' => 'API is currently under maintenance',
                'error_code' => 'MAINTENANCE_MODE'
            ], 503);
            return false;
        }
        
        // 获取客户端IP
        $clientIp = $this->getClientIp();
        
        // 检查IP黑名单
        if ($this->isBlacklisted($clientIp)) {
            ApiResponse::error([
                'message' => 'Access denied',
                'error_code' => 'IP_BLACKLISTED'
            ], 403);
            return false;
        }
        
        // 获取请求路径
        $requestPath = $this->getRequestPath();
        
        // 检查IP频率限制
        if (!$this->rateLimit->isWhitelisted($clientIp, 'ip')) {
            $ipLimitResult = $this->rateLimit->checkIpLimit($clientIp, $requestPath);
            if (!$ipLimitResult['allowed']) {
                $this->setRateLimitHeaders($ipLimitResult);
                ApiResponse::rateLimited(
                    'IP rate limit exceeded',
                    $ipLimitResult['retry_after']
                );
                return false;
            }
            $this->setRateLimitHeaders($ipLimitResult);
        }
        
        // 获取认证Token
        $token = ApiAuth::getTokenFromRequest();
        
        // 检查是否需要认证
        if ($this->requiresAuth($requestPath)) {
            if (!$token) {
                ApiResponse::unauthorized('Authentication required');
                return false;
            }
            
            // 验证Token
            if (!ApiAuth::validateToken($token)) {
                ApiResponse::unauthorized('Invalid or expired token');
                return false;
            }
            
            // 检查用户频率限制
            $userId = ApiAuth::getUserId();
            if ($userId) {
                $userLimitResult = $this->rateLimit->checkUserLimit($userId, $requestPath);
                if (!$userLimitResult['allowed']) {
                    $this->setRateLimitHeaders($userLimitResult);
                    ApiResponse::rateLimited(
                        'User rate limit exceeded',
                        $userLimitResult['retry_after']
                    );
                    return false;
                }
                $this->setRateLimitHeaders($userLimitResult);
            }
            
            // 记录认证日志
            ApiAuth::logAuth('api_access', [
                'endpoint' => $requestPath,
                'method' => $_SERVER['REQUEST_METHOD'],
                'ip' => $clientIp
            ]);
        }
        
        // 检查管理员权限
        if ($this->requiresAdminAuth($requestPath)) {
            if (!ApiAuth::isAdmin()) {
                ApiResponse::forbidden('Admin access required');
                return false;
            }
        }
        
        // 检查特定权限
        $requiredPermission = $this->getRequiredPermission($requestPath);
        if ($requiredPermission && !ApiAuth::hasPermission($requiredPermission)) {
            ApiResponse::forbidden('Insufficient permissions');
            return false;
        }
        
        // 验证请求内容
        if (!$this->validateRequest()) {
            return false;
        }
        
        // 记录API访问日志
        $this->logApiAccess($requestPath, $clientIp);
        
        return true;
    }
    
    /**
     * 检查是否为维护模式
     */
    private function isMaintenanceMode() {
        $db = Database::getInstance();
        $result = $db->fetch(
            "SELECT setting_value FROM pt_system_config WHERE setting_key = 'maintenance_mode'",
            []
        );
        
        return $result && $result['setting_value'] === '1';
    }
    
    /**
     * 获取客户端IP
     */
    private function getClientIp() {
        $ipKeys = [
            'HTTP_CF_CONNECTING_IP',
            'HTTP_CLIENT_IP',
            'HTTP_X_FORWARDED_FOR',
            'HTTP_X_FORWARDED',
            'HTTP_X_CLUSTER_CLIENT_IP',
            'HTTP_FORWARDED_FOR',
            'HTTP_FORWARDED',
            'REMOTE_ADDR'
        ];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }
    
    /**
     * 检查IP是否被封禁
     */
    private function isBlacklisted($ip) {
        $db = Database::getInstance();
        $result = $db->fetch(
            "SELECT setting_value FROM pt_system_config WHERE setting_key = 'ip_blacklist_enabled'",
            []
        );

        if (!$result || $result['setting_value'] !== '1') {
            return false;
        }

        $blacklistResult = $db->fetch(
            "SELECT setting_value FROM pt_system_config WHERE setting_key = 'ip_blacklist'",
            []
        );

        if (!$blacklistResult || empty($blacklistResult['setting_value'])) {
            return false;
        }

        $blacklist = explode("\n", $blacklistResult['setting_value']);
        foreach ($blacklist as $blockedIp) {
            $blockedIp = trim($blockedIp);
            if ($blockedIp === $ip || $this->ipInRange($ip, $blockedIp)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 检查IP是否在范围内
     */
    private function ipInRange($ip, $range) {
        if (strpos($range, '/') === false) {
            return $ip === $range;
        }
        
        list($subnet, $mask) = explode('/', $range);
        return (ip2long($ip) & ~((1 << (32 - $mask)) - 1)) === ip2long($subnet);
    }
    
    /**
     * 获取请求路径
     */
    private function getRequestPath() {
        $path = $_SERVER['REQUEST_URI'];
        
        if (($pos = strpos($path, '?')) !== false) {
            $path = substr($path, 0, $pos);
        }
        
        $apiPrefix = '/api';
        if (strpos($path, $apiPrefix) === 0) {
            $path = substr($path, strlen($apiPrefix));
        }
        
        return $path ?: '/';
    }
    
    /**
     * 检查是否需要认证
     */
    private function requiresAuth($path) {
        $publicPaths = [
            '/system/info',
            '/system/health',
            '/system/version',
            '/auth/login',
            '/auth/register',
            '/tools', // 公开的工具列表
            '/categories',
            '/search'
        ];
        
        foreach ($publicPaths as $publicPath) {
            if ($path === $publicPath || strpos($path, $publicPath . '/') === 0) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 检查是否需要管理员认证
     */
    private function requiresAdminAuth($path) {
        return strpos($path, '/admin/') === 0;
    }
    
    /**
     * 获取所需权限
     */
    private function getRequiredPermission($path) {
        $permissions = [
            '/admin/admins' => 'admins.view',
            '/admin/settings' => 'settings.view',
            '/admin/logs' => 'logs.view',
            '/admin/permissions' => 'permissions.view',
            '/admin/roles' => 'roles.view',
            '/users' => 'users.view',
            '/tools' => 'tools.view'
        ];
        
        foreach ($permissions as $pathPattern => $permission) {
            if (strpos($path, $pathPattern) === 0) {
                return $permission;
            }
        }
        
        return null;
    }
    
    /**
     * 验证请求
     */
    private function validateRequest() {
        // 检查Content-Type
        if (in_array($_SERVER['REQUEST_METHOD'], ['POST', 'PUT', 'PATCH'])) {
            $contentType = $_SERVER['CONTENT_TYPE'] ?? '';
            if (strpos($contentType, 'application/json') === false && 
                strpos($contentType, 'multipart/form-data') === false &&
                strpos($contentType, 'application/x-www-form-urlencoded') === false) {
                ApiResponse::error([
                    'message' => 'Invalid Content-Type',
                    'error_code' => 'INVALID_CONTENT_TYPE'
                ], 400);
                return false;
            }
        }
        
        // 检查请求大小
        $maxSize = 10 * 1024 * 1024; // 10MB
        if (isset($_SERVER['CONTENT_LENGTH']) && $_SERVER['CONTENT_LENGTH'] > $maxSize) {
            ApiResponse::error([
                'message' => 'Request too large',
                'error_code' => 'REQUEST_TOO_LARGE'
            ], 413);
            return false;
        }
        
        return true;
    }
    
    /**
     * 设置频率限制头部
     */
    private function setRateLimitHeaders($limitResult) {
        header("X-RateLimit-Limit: {$limitResult['limit']}");
        header("X-RateLimit-Remaining: {$limitResult['remaining']}");
        header("X-RateLimit-Reset: {$limitResult['reset_time']}");
        
        if ($limitResult['retry_after']) {
            header("Retry-After: {$limitResult['retry_after']}");
        }
    }
    
    /**
     * 记录API访问日志
     */
    private function logApiAccess($path, $ip) {
        $db = Database::getInstance();
        
        $logData = [
            'user_id' => ApiAuth::getUserId(),
            'endpoint' => $path,
            'method' => $_SERVER['REQUEST_METHOD'],
            'ip_address' => $ip,
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        try {
            $db->insert('pt_api_access_logs', $logData);
        } catch (Exception $e) {
            error_log('Failed to log API access: ' . $e->getMessage());
        }
    }
}
?>
