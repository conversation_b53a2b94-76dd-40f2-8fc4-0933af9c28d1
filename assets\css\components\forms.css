/* 表单组件样式 - 零圆角设计 */

/* 基础表单样式 */
.form {
    width: 100%;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group:last-child {
    margin-bottom: 0;
}

/* 标签样式 */
.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-color);
    font-size: 0.875rem;
}

.form-label.required::after {
    content: ' *';
    color: var(--danger-color);
}

/* 输入框基础样式 */
.form-input,
.form-textarea,
.form-select {
    width: 100%;
    padding: 0.75rem 1rem;
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    color: var(--text-color);
    font-size: 1rem;
    line-height: 1.5;
    transition: all 0.2s ease;
    border-radius: 0 !important; /* 零圆角强制 */
    font-family: inherit;
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px rgba(78, 115, 223, 0.1);
}

.form-input::placeholder,
.form-textarea::placeholder {
    color: var(--text-color);
    opacity: 0.5;
}

/* 文本域特殊样式 */
.form-textarea {
    min-height: 120px;
    resize: vertical;
}

/* 选择框样式 */
.form-select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.75rem center;
    background-repeat: no-repeat;
    background-size: 1rem;
    padding-right: 2.5rem;
    appearance: none;
}

/* 复选框和单选框样式 */
.form-checkbox,
.form-radio {
    width: 1.125rem;
    height: 1.125rem;
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    margin-right: 0.5rem;
    cursor: pointer;
    transition: all 0.2s ease;
    border-radius: 0 !important; /* 零圆角强制 */
}

.form-radio {
    border-radius: 50% !important; /* 单选框保持圆形 */
}

.form-checkbox:checked,
.form-radio:checked {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
}

.form-checkbox:checked {
    background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='m13.854 3.646-7.5 7.5a.5.5 0 0 1-.708 0l-3.5-3.5a.5.5 0 1 1 .708-.708L6 10.293l7.146-7.147a.5.5 0 0 1 .708.708z'/%3e%3c/svg%3e");
}

.form-radio:checked {
    background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3ccircle cx='8' cy='8' r='3'/%3e%3c/svg%3e");
}

/* 复选框和单选框标签 */
.form-check {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
    cursor: pointer;
}

.form-check:last-child {
    margin-bottom: 0;
}

.form-check-label {
    color: var(--text-color);
    font-size: 0.875rem;
    cursor: pointer;
    user-select: none;
}

/* 文件上传样式 */
.form-file {
    position: relative;
    display: inline-block;
    width: 100%;
}

.form-file-input {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.form-file-label {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    border: 2px dashed var(--border-color);
    background-color: var(--card-bg);
    color: var(--text-color);
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease;
    border-radius: 0 !important; /* 零圆角强制 */
}

.form-file-label:hover,
.form-file:hover .form-file-label {
    border-color: var(--accent-color);
    background-color: var(--hover-bg);
}

.form-file-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    opacity: 0.6;
}

.form-file-text {
    font-size: 0.875rem;
}

.form-file-hint {
    font-size: 0.75rem;
    opacity: 0.6;
    margin-top: 0.25rem;
}

/* 输入框尺寸变体 */
.form-input-sm,
.form-textarea-sm,
.form-select-sm {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
}

.form-input-lg,
.form-textarea-lg,
.form-select-lg {
    padding: 1rem 1.25rem;
    font-size: 1.125rem;
}

/* 输入框状态 */
.form-input.error,
.form-textarea.error,
.form-select.error {
    border-color: var(--danger-color);
    box-shadow: 0 0 0 2px rgba(231, 74, 59, 0.1);
}

.form-input.success,
.form-textarea.success,
.form-select.success {
    border-color: var(--success-color);
    box-shadow: 0 0 0 2px rgba(28, 200, 138, 0.1);
}

.form-input:disabled,
.form-textarea:disabled,
.form-select:disabled {
    background-color: var(--secondary-color);
    color: var(--text-color);
    opacity: 0.6;
    cursor: not-allowed;
}

/* 错误和帮助文本 */
.form-error {
    display: block;
    margin-top: 0.25rem;
    font-size: 0.75rem;
    color: var(--danger-color);
}

.form-help {
    display: block;
    margin-top: 0.25rem;
    font-size: 0.75rem;
    color: var(--text-color);
    opacity: 0.6;
}

.form-success {
    display: block;
    margin-top: 0.25rem;
    font-size: 0.75rem;
    color: var(--success-color);
}

/* 输入框组 */
.form-input-group {
    display: flex;
    align-items: stretch;
}

.form-input-group .form-input {
    flex: 1;
    border-radius: 0 !important;
}

.form-input-group .form-input:not(:first-child) {
    border-left: none;
}

.form-input-group .form-input:focus {
    z-index: 1;
    position: relative;
}

.form-input-addon {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    background-color: var(--secondary-color);
    border: 1px solid var(--border-color);
    color: var(--text-color);
    font-size: 0.875rem;
    white-space: nowrap;
}

.form-input-addon:first-child {
    border-right: none;
}

.form-input-addon:last-child {
    border-left: none;
}

/* 搜索框样式 */
.form-search {
    position: relative;
}

.form-search .form-input {
    padding-right: 3rem;
}

.form-search-icon {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-color);
    opacity: 0.5;
    pointer-events: none;
}

.form-search-clear {
    position: absolute;
    right: 0.5rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-color);
    opacity: 0.5;
    cursor: pointer;
    padding: 0.25rem;
    transition: opacity 0.2s ease;
}

.form-search-clear:hover {
    opacity: 1;
}

/* 表单布局 */
.form-row {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.form-col {
    flex: 1;
}

.form-col-auto {
    flex: none;
}

/* 表单操作区域 */
.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid var(--border-color);
}

.form-actions.centered {
    justify-content: center;
}

.form-actions.left {
    justify-content: flex-start;
}

.form-actions.space-between {
    justify-content: space-between;
}

/* 表单消息 */
.form-message {
    padding: 1rem;
    margin-bottom: 1.5rem;
    border: 1px solid;
    font-size: 0.875rem;
}

.form-message.success {
    background-color: rgba(28, 200, 138, 0.1);
    border-color: var(--success-color);
    color: var(--success-color);
}

.form-message.error {
    background-color: rgba(231, 74, 59, 0.1);
    border-color: var(--danger-color);
    color: var(--danger-color);
}

.form-message.info {
    background-color: rgba(78, 115, 223, 0.1);
    border-color: var(--accent-color);
    color: var(--accent-color);
}

.form-message.warning {
    background-color: rgba(246, 194, 62, 0.1);
    border-color: var(--warning-color);
    color: var(--warning-color);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .form-row {
        flex-direction: column;
        gap: 0;
    }
    
    .form-col {
        margin-bottom: 1.5rem;
    }
    
    .form-col:last-child {
        margin-bottom: 0;
    }
    
    .form-actions {
        flex-direction: column;
        align-items: stretch;
    }
    
    .form-actions.space-between {
        flex-direction: column-reverse;
    }
    
    .form-input-group {
        flex-direction: column;
    }
    
    .form-input-group .form-input:not(:first-child) {
        border-left: 1px solid var(--border-color);
        border-top: none;
    }
    
    .form-input-addon:first-child {
        border-right: 1px solid var(--border-color);
        border-bottom: none;
    }
    
    .form-input-addon:last-child {
        border-left: 1px solid var(--border-color);
        border-top: none;
    }
}
