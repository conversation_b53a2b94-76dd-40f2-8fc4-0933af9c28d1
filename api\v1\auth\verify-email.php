<?php
/**
 * 邮箱验证API
 * 验证用户输入的验证码并激活账户
 */

// 禁用错误输出到页面，避免影响JSON响应
ini_set('display_errors', 0);
error_reporting(E_ALL);

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// 使用统一的数据库连接
require_once dirname(dirname(dirname(__DIR__))) . '/includes/database-connection.php';

try {
    // 获取请求数据
    $input = json_decode(file_get_contents('php://input'), true);
    
    // 验证必需字段
    if (!isset($input['email']) || !isset($input['verification_code'])) {
        throw new Exception('Email and verification code are required');
    }
    
    $email = trim(strtolower($input['email']));
    $verificationCode = trim($input['verification_code']);
    
    // 验证邮箱格式
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        throw new Exception('Invalid email format');
    }
    
    // 验证验证码格式
    if (!preg_match('/^\d{6}$/', $verificationCode)) {
        throw new Exception('Verification code must be 6 digits');
    }
    
    // 查找待验证的用户
    $stmt = $pdo->prepare("SELECT * FROM pt_member WHERE email = ? AND status = 'pending'");
    $stmt->execute([$email]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$user) {
        // 检查用户是否存在但已激活
        $stmt = $pdo->prepare("SELECT status, email_verified FROM pt_member WHERE email = ?");
        $stmt->execute([$email]);
        $existingUser = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($existingUser) {
            if ($existingUser['status'] === 'active' && $existingUser['email_verified'] == 1) {
                throw new Exception('Email already verified. You can login directly.');
            } else {
                throw new Exception('Account exists but cannot be verified. Status: ' . $existingUser['status']);
            }
        } else {
            throw new Exception('User not found. Please register first.');
        }
    }
    
    // 验证验证码
    if ($user['email_verification_token'] !== $verificationCode) {
        // 记录失败的验证尝试
        try {
            $stmt = $pdo->prepare("
                INSERT INTO pt_member_activity_log (user_id, action, description, ip_address, user_agent, created_at)
                VALUES (?, 'email_verification_failed', 'Failed email verification attempt', ?, ?, NOW())
            ");
            $stmt->execute([
                $user['id'],
                $_SERVER['REMOTE_ADDR'] ?? 'unknown',
                $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
            ]);
        } catch (Exception $e) {
            // 日志记录失败不影响主流程
        }

        throw new Exception('Invalid verification code. Please check the code and try again.');
    }
    
    // 激活用户账户
    $stmt = $pdo->prepare("
        UPDATE pt_member 
        SET status = 'active', 
            email_verified = 1, 
            email_verification_token = NULL,
            updated_at = NOW()
        WHERE id = ?
    ");
    $stmt->execute([$user['id']]);
    
    // 记录成功的验证
    try {
        $stmt = $pdo->prepare("
            INSERT INTO pt_member_activity_log (user_id, action, description, ip_address, user_agent, created_at)
            VALUES (?, 'email_verified', 'Email address verified successfully', ?, ?, NOW())
        ");
        $stmt->execute([
            $user['id'],
            $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        ]);
    } catch (Exception $e) {
        // 日志记录失败不影响主流程
        error_log("Failed to log email verification: " . $e->getMessage());
    }
    
    // 创建登录会话
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    session_regenerate_id(true); // 重新生成会话ID以提高安全性
    $_SESSION['user_id'] = $user['id'];
    $_SESSION['user_email'] = $user['email'];
    $_SESSION['user_name'] = trim($user['first_name'] . ' ' . $user['last_name']);
    $_SESSION['username'] = $user['username'];
    $_SESSION['login_time'] = time();
    $_SESSION['login_method'] = 'email_verification';

    // 记录登录活动
    try {
        $stmt = $pdo->prepare("
            INSERT INTO pt_member_activity_log (user_id, action, description, ip_address, user_agent, created_at)
            VALUES (?, 'login', 'Auto-login after email verification', ?, ?, NOW())
        ");
        $stmt->execute([
            $user['id'],
            $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        ]);
    } catch (Exception $e) {
        error_log("Failed to log auto-login: " . $e->getMessage());
    }

    // 创建欢迎通知
    try {
        $stmt = $pdo->prepare("
            INSERT INTO pt_member_notifications (user_id, type, title, message, is_important, created_at)
            VALUES (?, 'welcome', 'Welcome to Prompt2Tool!', 'Your account has been successfully activated. Start exploring our AI tools and features.', 1, NOW())
        ");
        $stmt->execute([$user['id']]);
    } catch (Exception $e) {
        // 通知创建失败不影响主流程
        error_log("Failed to create welcome notification: " . $e->getMessage());
    }

    // 检查是否是AJAX请求
    $isAjax = !empty($_SERVER['HTTP_X_REQUESTED_WITH']) &&
              strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest';
    $isJsonRequest = strpos($_SERVER['CONTENT_TYPE'] ?? '', 'application/json') !== false;

    if ($isAjax || $isJsonRequest) {
        // AJAX请求，返回JSON
        echo json_encode([
            'success' => true,
            'message' => 'Email verified successfully! Welcome to Prompt2Tool!',
            'data' => [
                'user_id' => $user['id'],
                'email' => $user['email'],
                'username' => $user['username'],
                'name' => trim($user['first_name'] . ' ' . $user['last_name']),
                'status' => 'active',
                'redirect' => '/dashboard',
                'auto_login' => true
            ]
        ]);
    } else {
        // 表单提交，直接重定向
        header('Location: /dashboard');
        exit;
    }
    
} catch (Exception $e) {
    error_log("Email verification error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
