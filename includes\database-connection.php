<?php
/**
 * 统一数据库连接文件
 * 为所有 AJAX 和页面文件提供标准化的数据库连接
 */

// 防止重复包含
if (defined('DATABASE_CONNECTION_LOADED')) {
    return $pdo;
}
define('DATABASE_CONNECTION_LOADED', true);

// 定义必要的常量（如果还没有定义）
if (!defined('ROOT_PATH')) {
    define('ROOT_PATH', dirname(__DIR__));
}

if (!defined('APP_INITIALIZED')) {
    define('APP_INITIALIZED', true);
}

/**
 * 获取数据库连接
 * @param bool $useConfigFile 是否使用配置文件（默认true）
 * @return PDO 数据库连接对象
 * @throws Exception 连接失败时抛出异常
 */
function getDatabaseConnection($useConfigFile = true) {
    static $pdo = null;
    
    // 如果已经有连接，直接返回
    if ($pdo !== null) {
        return $pdo;
    }
    
    try {
        if ($useConfigFile) {
            // 方式1：使用配置文件（推荐）
            $configPath = ROOT_PATH . '/config/database.php';
            if (file_exists($configPath)) {
                $config = require $configPath;
                $dbConfig = $config['connections']['mysql'];
                
                $dsn = "mysql:host={$dbConfig['host']};port={$dbConfig['port']};dbname={$dbConfig['database']};charset={$dbConfig['charset']}";
                $pdo = new PDO($dsn, $dbConfig['username'], $dbConfig['password'], $dbConfig['options']);
            } else {
                throw new Exception('Database configuration file not found');
            }
        } else {
            // 方式2：使用默认配置（备用）
            $host = 'localhost';
            $port = '3306';
            $database = 'prompt2tool';
            $username = 'root';
            $password = '';
            $charset = 'utf8mb4';

            $dsn = "mysql:host={$host};port={$port};dbname={$database};charset={$charset}";
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES {$charset}"
            ];

            $pdo = new PDO($dsn, $username, $password, $options);
        }
        
        return $pdo;
        
    } catch (PDOException $e) {
        error_log("Database connection failed: " . $e->getMessage());
        throw new Exception('Database connection failed: ' . $e->getMessage());
    }
}

/**
 * 为 AJAX 文件提供的快速连接函数
 * 包含错误处理和 JSON 响应
 */
function getAjaxDatabaseConnection() {
    try {
        return getDatabaseConnection();
    } catch (Exception $e) {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => 'Database connection failed',
            'error' => $e->getMessage()
        ]);
        exit;
    }
}

/**
 * 数据库连接健康检查
 * @return bool 连接是否正常
 */
function checkDatabaseHealth() {
    try {
        $pdo = getDatabaseConnection();
        $stmt = $pdo->query('SELECT 1');
        return $stmt !== false;
    } catch (Exception $e) {
        return false;
    }
}

// 自动创建全局 $pdo 变量（向后兼容）
try {
    $pdo = getDatabaseConnection();
} catch (Exception $e) {
    // 在非 AJAX 环境中，记录错误但不中断执行
    error_log("Database connection warning: " . $e->getMessage());
    $pdo = null;
}

/**
 * 使用示例：
 * 
 * // 方式1：直接包含文件（推荐）
 * require_once ROOT_PATH . '/includes/database-connection.php';
 * // $pdo 变量自动可用
 * 
 * // 方式2：使用函数获取连接
 * $pdo = getDatabaseConnection();
 * 
 * // 方式3：AJAX 文件专用（包含错误处理）
 * $pdo = getAjaxDatabaseConnection();
 */
?>
