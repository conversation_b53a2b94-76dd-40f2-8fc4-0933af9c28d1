<?php
/**
 * 根据工具slug获取工具ID的API
 * 用于收藏功能
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

// 使用统一的数据库连接
require_once dirname(__DIR__) . '/includes/database-connection.php';

try {

    $toolSlug = $_GET['slug'] ?? '';
    $fullPath = $_GET['full_path'] ?? '';

    // 验证工具slug
    if (empty($toolSlug)) {
        echo json_encode([
            'success' => false,
            'message' => 'Tool slug is required',
            'error_code' => 'MISSING_SLUG'
        ]);
        exit;
    }

    // 查询工具信息，包括分类信息和状态（不限制状态，用于状态警告功能）
    $stmt = $pdo->prepare("
        SELECT t.id, t.name, t.slug, t.status, t.category_id, c.slug as category_slug, c.name as category_name
        FROM pt_tool t
        LEFT JOIN pt_tool_category c ON t.category_id = c.id
        WHERE t.slug = ?
    ");
    $stmt->execute([$toolSlug]);
    $tool = $stmt->fetch();

    if ($tool) {
        // 构建正确的工具URL - 强制使用分类路径
        $toolUrl = '/tools/';
        if (!empty($tool['category_slug'])) {
            $toolUrl .= $tool['category_slug'] . '/';
        } else {
            // 如果工具没有分类，使用默认分类
            $toolUrl .= 'utilities/';
        }
        $toolUrl .= $tool['slug'];

        echo json_encode([
            'success' => true,
            'tool_id' => $tool['id'],
            'tool_name' => $tool['name'],
            'tool_slug' => $tool['slug'],
            'tool_status' => $tool['status'],
            'category_id' => $tool['category_id'],
            'category_slug' => $tool['category_slug'] ?: 'utilities',
            'category_name' => $tool['category_name'] ?: 'Utilities',
            'tool_url' => $toolUrl,
            'full_path' => $fullPath,
            'canonical_url' => $toolUrl // 规范URL
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Tool not found or inactive',
            'error_code' => 'TOOL_NOT_FOUND'
        ]);
    }

} catch (PDOException $e) {
    error_log('Database error in get-tool-id API: ' . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => 'Database connection failed',
        'error_code' => 'DATABASE_ERROR'
    ]);
} catch (Exception $e) {
    error_log('General error in get-tool-id API: ' . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => 'An unexpected error occurred',
        'error_code' => 'GENERAL_ERROR'
    ]);
}
?>
