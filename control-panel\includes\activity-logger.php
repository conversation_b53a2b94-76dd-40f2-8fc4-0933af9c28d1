<?php
/**
 * Activity Logger Functions
 */

/**
 * Log admin activity
 */
function logActivity($action, $description, $icon = 'fas fa-info-circle', $color = 'blue', $adminId = null) {
    global $pdo;
    
    if (!$adminId) {
        $adminId = $_SESSION['admin_id'] ?? 1;
    }
    
    try {
        $sql = "INSERT INTO pt_activity_log (manager_id, action, description, icon, color, ip_address, user_agent, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, NOW())";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([
            $adminId,
            $action,
            $description,
            $icon,
            $color,
            $_SERVER['REMOTE_ADDR'] ?? null,
            $_SERVER['HTTP_USER_AGENT'] ?? null
        ]);
        return true;
    } catch (PDOException $e) {
        return false;
    }
}

/**
 * Get recent activities for admin
 */
function getRecentActivities($adminId = null, $limit = 10) {
    global $pdo;
    
    if (!$adminId) {
        $adminId = $_SESSION['admin_id'] ?? 1;
    }
    
    try {
        $sql = "SELECT action, description, icon, color, created_at
                FROM pt_activity_log
                WHERE manager_id = ?
                ORDER BY created_at DESC
                LIMIT ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$adminId, $limit]);
        return $stmt->fetchAll();
    } catch (PDOException $e) {
        return [];
    }
}

/**
 * Predefined activity types
 */
class ActivityType {
    const LOGIN = [
        'action' => 'login',
        'description' => 'Logged in to admin panel',
        'icon' => 'fas fa-sign-in-alt',
        'color' => 'green'
    ];
    
    const LOGOUT = [
        'action' => 'logout',
        'description' => 'Logged out from admin panel',
        'icon' => 'fas fa-sign-out-alt',
        'color' => 'gray'
    ];
    
    const UPDATE_PROFILE = [
        'action' => 'update_profile',
        'description' => 'Updated profile information',
        'icon' => 'fas fa-user-edit',
        'color' => 'blue'
    ];
    
    const CHANGE_PASSWORD = [
        'action' => 'change_password',
        'description' => 'Changed account password',
        'icon' => 'fas fa-key',
        'color' => 'orange'
    ];
    
    const UPDATE_SETTINGS = [
        'action' => 'update_settings',
        'description' => 'Updated system settings',
        'icon' => 'fas fa-cog',
        'color' => 'blue'
    ];
    
    const VIEW_USERS = [
        'action' => 'view_users',
        'description' => 'Reviewed user management',
        'icon' => 'fas fa-users',
        'color' => 'purple'
    ];
    
    const ADD_TOOL = [
        'action' => 'add_tool',
        'description' => 'Added new AI tool',
        'icon' => 'fas fa-plus-circle',
        'color' => 'green'
    ];
    
    const EDIT_TOOL = [
        'action' => 'edit_tool',
        'description' => 'Modified AI tool settings',
        'icon' => 'fas fa-edit',
        'color' => 'blue'
    ];
    
    const DELETE_TOOL = [
        'action' => 'delete_tool',
        'description' => 'Removed AI tool',
        'icon' => 'fas fa-trash',
        'color' => 'red'
    ];
    
    const ENABLE_2FA = [
        'action' => 'enable_2fa',
        'description' => 'Enabled two-factor authentication',
        'icon' => 'fas fa-shield-alt',
        'color' => 'green'
    ];
    
    const DISABLE_2FA = [
        'action' => 'disable_2fa',
        'description' => 'Disabled two-factor authentication',
        'icon' => 'fas fa-shield-alt',
        'color' => 'orange'
    ];
    
    const REVOKE_SESSION = [
        'action' => 'revoke_session',
        'description' => 'Revoked user session',
        'icon' => 'fas fa-ban',
        'color' => 'red'
    ];
}

/**
 * Quick log functions
 */
function logLogin($adminId = null) {
    $activity = ActivityType::LOGIN;
    return logActivity($activity['action'], $activity['description'], $activity['icon'], $activity['color'], $adminId);
}

function logLogout($adminId = null) {
    $activity = ActivityType::LOGOUT;
    return logActivity($activity['action'], $activity['description'], $activity['icon'], $activity['color'], $adminId);
}

function logProfileUpdate($adminId = null) {
    $activity = ActivityType::UPDATE_PROFILE;
    return logActivity($activity['action'], $activity['description'], $activity['icon'], $activity['color'], $adminId);
}

function logPasswordChange($adminId = null) {
    $activity = ActivityType::CHANGE_PASSWORD;
    return logActivity($activity['action'], $activity['description'], $activity['icon'], $activity['color'], $adminId);
}

function log2FAEnable($adminId = null) {
    $activity = ActivityType::ENABLE_2FA;
    return logActivity($activity['action'], $activity['description'], $activity['icon'], $activity['color'], $adminId);
}

function log2FADisable($adminId = null) {
    $activity = ActivityType::DISABLE_2FA;
    return logActivity($activity['action'], $activity['description'], $activity['icon'], $activity['color'], $adminId);
}
?>
