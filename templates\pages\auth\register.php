<?php
/**
 * User Registration Page
 * 支持传统注册和Google一键注册
 */

$currentPage = 'register';
$breadcrumbs = [
    ['name' => 'Home', 'url' => '/'],
    ['name' => 'Register']
];

// 包含动态SEO助手
require_once ROOT_PATH . '/app/helpers/dynamic-seo.php';

// 生成SEO数据
$seoData = [
    'title' => 'Register - Prompt2Tool',
    'description' => 'Create your free Prompt2Tool account to access premium features and save your preferences.',
    'keywords' => 'register, sign up, create account, prompt2tool',
    'og_title' => 'Register - Prompt2Tool',
    'og_description' => 'Create your free Prompt2Tool account to access premium features.',
    'canonical' => getCurrentURL()
];

// 获取Google OAuth配置
$googleClientId = '';
try {
    // 使用统一的数据库连接
    require_once ROOT_PATH . '/includes/database-connection.php';
    $stmt = $pdo->prepare("SELECT setting_value FROM pt_system_config WHERE setting_key = 'google_oauth_client_id'");
    $stmt->execute();
    $result = $stmt->fetch();
    if ($result) {
        $googleClientId = $result['setting_value'];
    }
} catch (Exception $e) {
    // 静默处理错误
}

// 包含公共头部
include_once ROOT_PATH . '/templates/layout/header.php';
?>

<!-- 注册页面主体 -->
<div class="bg-gray-900 text-white min-h-screen">
    <div class="max-w-md mx-auto pt-20 pb-12 px-4">
        <div class="bg-gray-800 border border-gray-700 p-8">
            <!-- Logo和标题 -->
            <div class="text-center mb-8">
                <div class="w-16 h-16 bg-blue-600 text-white flex items-center justify-center text-2xl font-bold mx-auto mb-4">
                    P
                </div>
                <h1 class="text-2xl font-bold text-white mb-2">Create Account</h1>
                <p class="text-gray-400">Join Prompt2Tool today</p>
            </div>

            <!-- 错误/成功消息 -->
            <div id="messageArea" class="hidden mb-6">
                <div id="messageContent" class="p-4 text-sm"></div>
            </div>

            <!-- Google注册按钮 -->
            <?php if (!empty($googleClientId)): ?>
            <div class="mb-6">
                <button id="googleSignUp" class="w-full bg-white text-gray-900 py-3 px-4 hover:bg-gray-100 transition-colors flex items-center justify-center space-x-3 font-medium">
                    <svg class="w-5 h-5" viewBox="0 0 24 24">
                        <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                        <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                        <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                        <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                    </svg>
                    <span>Sign up with Google</span>
                </button>
            </div>

            <!-- 分隔线 -->
            <div class="relative mb-6">
                <div class="absolute inset-0 flex items-center">
                    <div class="w-full border-t border-gray-600"></div>
                </div>
                <div class="relative flex justify-center text-sm">
                    <span class="px-2 bg-gray-800 text-gray-400">Or sign up with email</span>
                </div>
            </div>
            <?php endif; ?>

            <!-- 传统注册表单 -->
            <form id="registerForm" class="space-y-6">
                <!-- 姓名 -->
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-300 mb-2">
                        Full Name
                    </label>
                    <input type="text"
                           id="name"
                           name="name"
                           required
                           autocomplete="name"
                           class="w-full bg-gray-900 border border-gray-600 text-gray-100 p-3 focus:outline-none focus:border-blue-500"
                           placeholder="Enter your full name">
                </div>

                <!-- 邮箱 -->
                <div>
                    <label for="email" class="block text-sm font-medium text-gray-300 mb-2">
                        Email Address
                    </label>
                    <input type="email"
                           id="email"
                           name="email"
                           required
                           autocomplete="email"
                           class="w-full bg-gray-900 border border-gray-600 text-gray-100 p-3 focus:outline-none focus:border-blue-500"
                           placeholder="Enter your email">
                </div>

                <!-- 密码 -->
                <div>
                    <label for="password" class="block text-sm font-medium text-gray-300 mb-2">
                        Password
                    </label>
                    <div class="relative">
                        <input type="password"
                               id="password"
                               name="password"
                               required
                               autocomplete="new-password"
                               class="w-full bg-gray-900 border border-gray-600 text-gray-100 p-3 pr-24 focus:outline-none focus:border-blue-500"
                               placeholder="Create a password">
                        <div class="absolute inset-y-0 right-0 flex items-center space-x-1 pr-3">
                            <button type="button"
                                    id="generatePasswordBtn"
                                    class="text-blue-400 hover:text-blue-300 text-xs font-medium px-2 py-1 rounded border border-blue-400 hover:bg-blue-400 hover:text-white transition-colors"
                                    title="Generate secure password">
                                <i class="fas fa-magic mr-1"></i>Generate
                            </button>
                            <button type="button"
                                    id="togglePasswordBtn"
                                    class="text-gray-400 hover:text-gray-300 ml-2"
                                    title="Show/hide password">
                                <i class="fas fa-eye" id="passwordToggleIcon"></i>
                            </button>
                        </div>
                    </div>


                    <div class="mt-2">
                        <!-- 密码强度指示器 -->
                        <div class="flex space-x-1 mb-2">
                            <div class="h-1 w-1/4 bg-gray-600 rounded" id="strength-1"></div>
                            <div class="h-1 w-1/4 bg-gray-600 rounded" id="strength-2"></div>
                            <div class="h-1 w-1/4 bg-gray-600 rounded" id="strength-3"></div>
                            <div class="h-1 w-1/4 bg-gray-600 rounded" id="strength-4"></div>
                        </div>
                        <div class="text-xs text-gray-500" id="strength-text">
                            At least 8 characters with uppercase, lowercase, numbers and special characters
                        </div>
                    </div>
                </div>

                <!-- 确认密码 -->
                <div>
                    <label for="confirmPassword" class="block text-sm font-medium text-gray-300 mb-2">
                        Confirm Password
                    </label>
                    <div class="relative">
                        <input type="password"
                               id="confirmPassword"
                               name="confirmPassword"
                               required
                               autocomplete="new-password"
                               class="w-full bg-gray-900 border border-gray-600 text-gray-100 p-3 pr-10 focus:outline-none focus:border-blue-500"
                               placeholder="Confirm your password">
                        <button type="button"
                                id="toggleConfirmPasswordBtn"
                                class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-300"
                                title="Show/hide password">
                            <i class="fas fa-eye" id="confirmPasswordToggleIcon"></i>
                        </button>
                    </div>
                    <div class="mt-1 text-xs text-red-400 hidden" id="password-mismatch">
                        Passwords do not match
                    </div>
                </div>

                <!-- 服务条款 -->
                <div>
                    <label class="flex items-start">
                        <input type="checkbox" id="terms" name="terms" required class="mr-3 w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 focus:ring-blue-500 mt-1">
                        <span class="text-sm text-gray-300">
                            I agree to the <a href="/terms" class="text-blue-400 hover:text-blue-300">Terms of Service</a> 
                            and <a href="/privacy" class="text-blue-400 hover:text-blue-300">Privacy Policy</a>
                        </span>
                    </label>
                </div>

                <!-- 注册按钮 -->
                <button type="submit" class="w-full bg-blue-600 text-white py-3 hover:bg-blue-700 transition-colors font-medium">
                    Create Account
                </button>
            </form>

            <!-- 登录链接 -->
            <div class="mt-6 text-center">
                <p class="text-gray-400">
                    Already have an account?
                    <a href="/auth/login" class="text-blue-400 hover:text-blue-300 font-medium">
                        Sign in
                    </a>
                </p>
            </div>
        </div>


    </div>
</div>

<!-- Google Sign-In API -->
<?php if (!empty($googleClientId)): ?>
<script src="https://accounts.google.com/gsi/client" async defer></script>
<?php endif; ?>

<!-- 自定义Google按钮样式 -->
<style>
/* 覆盖Google按钮的默认样式 */
#googleSignUp {
    background: #1f2937 !important;
    border: 1px solid #4b5563 !important;
    border-radius: 0 !important;
    transition: all 0.2s ease !important;
}

#googleSignUp:hover {
    background: #374151 !important;
    border-color: #6b7280 !important;
}

/* 移除iframe的白色背景 */
#googleSignUp iframe {
    background: transparent !important;
    border-radius: 0 !important;
    filter: invert(1) hue-rotate(180deg) !important;
}

/* 确保Google按钮容器样式 */
.S9gUrf-YoZ4jf {
    background: transparent !important;
    border: none !important;
}

/* 移除Google按钮的白色背景和边框 */
#googleSignUp .L5Fo6c-PQbLGe {
    background: transparent !important;
    border: none !important;
    border-radius: 0 !important;
}

/* 强制覆盖Google按钮样式 */
#googleSignUp div[role="button"] {
    background: #1f2937 !important;
    border: 1px solid #4b5563 !important;
    color: white !important;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const registerForm = document.getElementById('registerForm');
    const messageArea = document.getElementById('messageArea');
    const messageContent = document.getElementById('messageContent');

    // 显示消息
    function showMessage(message, type = 'error') {
        messageContent.textContent = message;
        messageContent.className = `p-4 text-sm ${type === 'error' ? 'bg-red-600 text-white' : 'bg-green-600 text-white'}`;
        messageArea.classList.remove('hidden');
        
        // 自动隐藏成功消息
        if (type === 'success') {
            setTimeout(() => {
                messageArea.classList.add('hidden');
            }, 3000);
        }
    }

    // 生成安全密码
    function generateSecurePassword() {
        // 定义字符集 - 确保与验证函数完全匹配
        const lowercase = 'abcdefghijklmnopqrstuvwxyz';
        const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        const numbers = '0123456789';
        // 使用与验证函数完全匹配的特殊字符集
        const symbols = '!@#$%^&*()_+-=[]{}|;:,.<>?';

        // 构建密码，确保包含所有必需的字符类型
        let password = '';

        // 必须包含的字符（各2个确保足够）
        password += getRandomChars(lowercase, 2);
        password += getRandomChars(uppercase, 2);
        password += getRandomChars(numbers, 2);
        password += getRandomChars(symbols, 2);

        // 填充到12位总长度
        const allChars = lowercase + uppercase + numbers + symbols;
        while (password.length < 12) {
            password += allChars[Math.floor(Math.random() * allChars.length)];
        }

        // 随机打乱密码字符
        let shuffledPassword = password.split('').sort(() => Math.random() - 0.5).join('');

        // 验证生成的密码是否符合所有要求
        const validation = validatePassword(shuffledPassword);
        if (validation !== null) {
            // 如果不符合要求，递归重新生成（最多尝试10次避免无限循环）
            console.log('Generated password failed validation, regenerating...', validation);
            return generateSecurePassword();
        }

        console.log('Generated valid password:', shuffledPassword);
        return shuffledPassword;
    }

    // 从字符集中获取指定数量的随机字符
    function getRandomChars(charset, count) {
        let result = '';
        for (let i = 0; i < count; i++) {
            result += charset[Math.floor(Math.random() * charset.length)];
        }
        return result;
    }

    // 检查密码强度
    function checkPasswordStrength(password) {
        let strength = 0;
        const requirements = {
            length: password.length >= 8,
            lowercase: /[a-z]/.test(password),
            uppercase: /[A-Z]/.test(password),
            number: /[0-9]/.test(password),
            special: /[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]/.test(password)
        };

        Object.values(requirements).forEach(req => {
            if (req) strength++;
        });

        // 更新强度指示器
        const strengthBars = ['strength-1', 'strength-2', 'strength-3', 'strength-4'];
        const strengthColors = ['bg-red-500', 'bg-orange-500', 'bg-yellow-500', 'bg-green-500'];
        const strengthTexts = ['Very Weak', 'Weak', 'Fair', 'Strong'];

        strengthBars.forEach((barId, index) => {
            const bar = document.getElementById(barId);
            if (bar) {
                bar.className = 'h-1 w-1/4 rounded';
                if (index < strength - 1) {
                    bar.classList.add(strengthColors[Math.min(strength - 2, 3)]);
                } else {
                    bar.classList.add('bg-gray-600');
                }
            }
        });

        const strengthText = document.getElementById('strength-text');
        if (strengthText) {
            if (password.length === 0) {
                strengthText.textContent = 'At least 8 characters with uppercase, lowercase, numbers and special characters';
                strengthText.className = 'text-xs text-gray-500';
            } else {
                strengthText.textContent = `Password strength: ${strengthTexts[Math.min(strength - 1, 3)] || 'Very Weak'}`;
                strengthText.className = `text-xs ${strength >= 4 ? 'text-green-400' : strength >= 3 ? 'text-yellow-400' : 'text-red-400'}`;
            }
        }

        return strength;
    }

    // 密码验证
    function validatePassword(password) {
        if (password.length < 8) {
            return 'Password must be at least 8 characters long';
        }
        if (!/(?=.*[a-z])/.test(password)) {
            return 'Password must contain at least one lowercase letter';
        }
        if (!/(?=.*[A-Z])/.test(password)) {
            return 'Password must contain at least one uppercase letter';
        }
        if (!/(?=.*\d)/.test(password)) {
            return 'Password must contain at least one number';
        }
        if (!/(?=.*[!@#$%^&*()_+\-=\[\]{}|;:,.<>?])/.test(password)) {
            return 'Password must contain at least one special character';
        }
        return null;
    }

    // 检查密码匹配
    function checkPasswordMatch() {
        const password = document.getElementById('password').value;
        const confirmPassword = document.getElementById('confirmPassword').value;
        const mismatchDiv = document.getElementById('password-mismatch');

        if (confirmPassword && password !== confirmPassword) {
            mismatchDiv.classList.remove('hidden');
            return false;
        } else {
            mismatchDiv.classList.add('hidden');
            return true;
        }
    }

    // 切换密码可见性
    function togglePasswordVisibility(inputId, iconId) {
        const input = document.getElementById(inputId);
        const icon = document.getElementById(iconId);

        if (input.type === 'password') {
            input.type = 'text';
            icon.classList.remove('fa-eye');
            icon.classList.add('fa-eye-slash');
        } else {
            input.type = 'password';
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        }
    }

    // 传统注册表单提交
    registerForm.addEventListener('submit', async function(e) {
        e.preventDefault();

        const formData = new FormData(registerForm);
        const password = formData.get('password');
        const confirmPassword = formData.get('confirmPassword');

        // 客户端验证
        const passwordError = validatePassword(password);
        if (passwordError) {
            showMessage(passwordError);
            return;
        }

        if (password !== confirmPassword) {
            showMessage('Passwords do not match');
            return;
        }
        
        const submitBtn = registerForm.querySelector('button[type="submit"]');
        const originalText = submitBtn.textContent;
        
        submitBtn.textContent = 'Creating Account...';
        submitBtn.disabled = true;
        
        try {
            const response = await fetch('/api/v1/auth/register', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    first_name: formData.get('name').split(' ')[0] || '',
                    last_name: formData.get('name').split(' ').slice(1).join(' ') || '',
                    username: formData.get('email').split('@')[0], // 使用邮箱前缀作为用户名
                    email: formData.get('email'),
                    password: password,
                    confirm_password: confirmPassword,
                    terms: formData.get('terms') === 'on'
                })
            });

            const data = await response.json();
            
            if (data.success) {
                if (data.data && data.data.verification_required) {
                    // 保存验证码到localStorage
                    localStorage.setItem('verification_code', data.data.verification_code);
                    showMessage('Account created successfully! Redirecting to verification...', 'success');

                    setTimeout(() => {
                        window.location.href = data.data.redirect || '/auth/verify-email?email=' + encodeURIComponent(data.data.email);
                    }, 1500);
                } else {
                    showMessage('Account created successfully! Redirecting to login...', 'success');
                    setTimeout(() => {
                        window.location.href = '/auth/login';
                    }, 1000);
                }
            } else {
                showMessage(data.message || 'Registration failed. Please try again.');
            }
        } catch (error) {
            showMessage('Network error. Please try again.');
        } finally {
            submitBtn.textContent = originalText;
            submitBtn.disabled = false;
        }
    });

    <?php if (!empty($googleClientId)): ?>
    // Google注册初始化
    window.onload = function() {
        google.accounts.id.initialize({
            client_id: '<?= htmlspecialchars($googleClientId) ?>',
            callback: handleGoogleSignUp
        });
        
        // 渲染Google注册按钮
        google.accounts.id.renderButton(
            document.getElementById('googleSignUp'),
            {
                theme: 'filled_black',
                size: 'large',
                width: '100%',
                text: 'signup_with',
                shape: 'rectangular'
            }
        );
    };

    // 处理Google注册回调
    async function handleGoogleSignUp(response) {
        try {
            const result = await fetch('/api/v1/auth/google-register', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    credential: response.credential
                })
            });
            
            const data = await result.json();
            
            if (data.success) {
                showMessage('Google registration successful! Redirecting...', 'success');
                setTimeout(() => {
                    window.location.href = data.redirect || '/';
                }, 1000);
            } else {
                showMessage(data.message || 'Google registration failed. Please try again.');
            }
        } catch (error) {
            showMessage('Google registration error. Please try again.');
        }
    }
    <?php endif; ?>

    // 事件监听器

    // 密码生成按钮
    const generatePasswordBtn = document.getElementById('generatePasswordBtn');

    if (generatePasswordBtn) {
        generatePasswordBtn.addEventListener('click', function() {
            const password = generateSecurePassword();
            const passwordInput = document.getElementById('password');
            const confirmPasswordInput = document.getElementById('confirmPassword');

            // 填充密码字段
            passwordInput.value = password;
            confirmPasswordInput.value = password;

            // 自动显示密码为明文（方便查看和复制）
            passwordInput.type = 'text';
            confirmPasswordInput.type = 'text';
            document.getElementById('passwordToggleIcon').classList.remove('fa-eye');
            document.getElementById('passwordToggleIcon').classList.add('fa-eye-slash');
            document.getElementById('confirmPasswordToggleIcon').classList.remove('fa-eye');
            document.getElementById('confirmPasswordToggleIcon').classList.add('fa-eye-slash');

            // 更新强度指示器
            checkPasswordStrength(password);
            checkPasswordMatch();

            // 显示生成成功消息
            showMessage('Secure password generated! Password is now visible for easy copying.', 'success');

            // 临时改变按钮样式表示生成成功
            const originalHTML = this.innerHTML;
            this.innerHTML = '<i class="fas fa-check mr-1"></i>Generated!';
            this.classList.add('bg-green-500', 'text-white');

            setTimeout(() => {
                this.innerHTML = originalHTML;
                this.classList.remove('bg-green-500', 'text-white');
            }, 2000);
        });
    }

    // 密码可见性切换
    const togglePasswordBtn = document.getElementById('togglePasswordBtn');
    if (togglePasswordBtn) {
        togglePasswordBtn.addEventListener('click', function() {
            togglePasswordVisibility('password', 'passwordToggleIcon');
        });
    }

    const toggleConfirmPasswordBtn = document.getElementById('toggleConfirmPasswordBtn');
    if (toggleConfirmPasswordBtn) {
        toggleConfirmPasswordBtn.addEventListener('click', function() {
            togglePasswordVisibility('confirmPassword', 'confirmPasswordToggleIcon');
        });
    }

    // 密码输入事件
    const passwordInput = document.getElementById('password');
    if (passwordInput) {
        passwordInput.addEventListener('input', function() {
            checkPasswordStrength(this.value);
            checkPasswordMatch();
        });
    }

    const confirmPasswordInput = document.getElementById('confirmPassword');
    if (confirmPasswordInput) {
        confirmPasswordInput.addEventListener('input', function() {
            checkPasswordMatch();
        });
    }
});
</script>

<?php include_once ROOT_PATH . '/templates/layout/footer.php'; ?>
