<?php
/**
 * UTM Builder Tool Page
 * 遵循01-UI设计规范.md - 零圆角设计
 */

$currentPage = 'tool-utm-builder';
$breadcrumbs = [
    ['name' => 'Home', 'url' => '/'],
    ['name' => 'Tools', 'url' => '/tools'],
    ['name' => 'Marketing', 'url' => '/tools/marketing'],
    ['name' => 'UTM Builder']
];

// 包含动态SEO助手
require_once ROOT_PATH . '/app/helpers/dynamic-seo.php';

// 生成SEO数据 - 优化长度
$seoData = [
    'title' => 'Free UTM Builder Online - Prompt2Tool',
    'description' => 'Generate UTM tracking parameters for your marketing campaigns. Track traffic sources, mediums, and campaigns with Google Analytics.',
    'keywords' => 'utm builder, utm generator, utm parameters, utm tracking, google analytics, campaign tracking, marketing analytics, traffic tracking',
    'og_title' => 'Free UTM Builder Online - Prompt2Tool',
    'og_description' => 'Generate UTM tracking parameters for your marketing campaigns. Track traffic sources and campaign performance.',
    'canonical' => getCurrentURL()
];

// 包含公共头部
include_once ROOT_PATH . '/templates/layout/header.php';
?>

<!-- 工具页面主体 -->
<div class="bg-gray-900 text-white">

    <!-- Hero Section with SEO optimized content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-12">
        <div class="text-center mb-12">
            <div class="flex items-center justify-center mb-4">
                <div class="bg-orange-600 p-3 mr-4">
                    <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                </div>
                <div class="text-left">
                    <h1 class="text-4xl font-bold text-white mb-2">UTM Builder</h1>
                    <p class="text-xl text-gray-400">Generate UTM tracking parameters for your campaigns</p>
                </div>
            </div>
        </div>

        <!-- 工具主体 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- 输入区域 -->
            <div class="bg-gray-800 border border-gray-700 p-6">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-xl font-semibold text-white">Campaign Details</h2>
                    <div class="flex space-x-2">
                        <button id="clearForm" class="px-3 py-1 bg-red-600 text-white text-sm hover:bg-red-700 transition-colors">
                            Clear
                        </button>
                        <button id="fillExample" class="px-3 py-1 bg-blue-600 text-white text-sm hover:bg-blue-700 transition-colors">
                            Example
                        </button>
                    </div>
                </div>
                
                <form id="utmForm" class="space-y-4">
                    <!-- Website URL -->
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">Website URL *</label>
                        <input type="url" id="websiteUrl" class="w-full bg-gray-900 border border-gray-600 text-gray-100 p-2 focus:outline-none focus:border-blue-500" placeholder="https://example.com" required>
                    </div>
                    
                    <!-- Campaign Source -->
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">Campaign Source *</label>
                        <input type="text" id="utmSource" class="w-full bg-gray-900 border border-gray-600 text-gray-100 p-2 focus:outline-none focus:border-blue-500" placeholder="google, facebook, newsletter" required>
                        <div class="text-xs text-gray-500 mt-1">The referrer (e.g., google, facebook, newsletter)</div>
                    </div>
                    
                    <!-- Campaign Medium -->
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">Campaign Medium *</label>
                        <input type="text" id="utmMedium" class="w-full bg-gray-900 border border-gray-600 text-gray-100 p-2 focus:outline-none focus:border-blue-500" placeholder="cpc, email, social" required>
                        <div class="text-xs text-gray-500 mt-1">Marketing medium (e.g., cpc, email, social)</div>
                    </div>
                    
                    <!-- Campaign Name -->
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">Campaign Name</label>
                        <input type="text" id="utmCampaign" class="w-full bg-gray-900 border border-gray-600 text-gray-100 p-2 focus:outline-none focus:border-blue-500" placeholder="spring_sale, product_launch">
                        <div class="text-xs text-gray-500 mt-1">Product, promo code, or slogan</div>
                    </div>
                    
                    <!-- Campaign Term -->
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">Campaign Term</label>
                        <input type="text" id="utmTerm" class="w-full bg-gray-900 border border-gray-600 text-gray-100 p-2 focus:outline-none focus:border-blue-500" placeholder="running+shoes, marketing+tools">
                        <div class="text-xs text-gray-500 mt-1">Identify paid search keywords</div>
                    </div>
                    
                    <!-- Campaign Content -->
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">Campaign Content</label>
                        <input type="text" id="utmContent" class="w-full bg-gray-900 border border-gray-600 text-gray-100 p-2 focus:outline-none focus:border-blue-500" placeholder="logolink, textlink">
                        <div class="text-xs text-gray-500 mt-1">Differentiate ads or links</div>
                    </div>
                </form>
            </div>

            <!-- 输出区域 -->
            <div class="bg-gray-800 border border-gray-700 p-6">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-xl font-semibold text-white">Generated UTM URL</h2>
                    <div class="flex space-x-2">
                        <div class="relative">
                            <button id="copyUrl" class="px-3 py-1 bg-green-600 text-white text-sm hover:bg-green-700 transition-colors">
                                Copy URL
                            </button>
                            <div id="copyTooltip" class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-700 text-white text-xs rounded pointer-events-none whitespace-nowrap z-50" style="opacity: 0; visibility: hidden; transition: opacity 0.2s, visibility 0.2s;">
                                Copy UTM URL
                            </div>
                        </div>
                        <div class="relative">
                            <button id="generateQR" class="px-3 py-1 bg-purple-600 text-white text-sm hover:bg-purple-700 transition-colors">
                                QR Code
                            </button>
                            <div id="qrTooltip" class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-700 text-white text-xs rounded pointer-events-none whitespace-nowrap z-50" style="opacity: 0; visibility: hidden; transition: opacity 0.2s, visibility 0.2s;">
                                Generate QR code
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- UTM URL 显示 -->
                <div class="mb-6">
                    <textarea id="utmUrl" class="w-full h-32 bg-gray-900 border border-gray-600 text-gray-100 p-3 font-mono text-sm focus:outline-none focus:border-blue-500" readonly placeholder="Your UTM URL will appear here..."></textarea>
                </div>
                
                <!-- UTM 参数预览 -->
                <div class="mb-6">
                    <h3 class="text-lg font-semibold text-white mb-3">UTM Parameters</h3>
                    <div class="space-y-2 text-sm">
                        <div class="flex justify-between">
                            <span class="text-gray-400">utm_source:</span>
                            <span id="previewSource" class="text-gray-300">-</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">utm_medium:</span>
                            <span id="previewMedium" class="text-gray-300">-</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">utm_campaign:</span>
                            <span id="previewCampaign" class="text-gray-300">-</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">utm_term:</span>
                            <span id="previewTerm" class="text-gray-300">-</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-400">utm_content:</span>
                            <span id="previewContent" class="text-gray-300">-</span>
                        </div>
                    </div>
                </div>
                
                <!-- QR Code 显示 -->
                <div id="qrCodeSection" class="hidden">
                    <h3 class="text-lg font-semibold text-white mb-3">QR Code</h3>
                    <div class="text-center">
                        <canvas id="qrCanvas" class="border border-gray-600 bg-white"></canvas>
                        <div class="mt-2">
                            <button id="downloadQR" class="px-3 py-1 bg-blue-600 text-white text-sm hover:bg-blue-700 transition-colors">
                                Download QR
                            </button>
                        </div>
                    </div>
                </div>


            </div>
        </div>

        <!-- 工具信息 -->
        <div class="mt-12 grid grid-cols-1 md:grid-cols-3 gap-8">
            <div class="bg-gray-800 border border-gray-700 p-6">
                <h4 class="text-lg font-semibold text-white mb-3">UTM Parameters</h4>
                <ul class="text-gray-300 space-y-2">
                    <li>• <strong>Source:</strong> Traffic origin</li>
                    <li>• <strong>Medium:</strong> Marketing channel</li>
                    <li>• <strong>Campaign:</strong> Specific campaign</li>
                    <li>• <strong>Term:</strong> Paid keywords</li>
                    <li>• <strong>Content:</strong> Ad variation</li>
                </ul>
            </div>
            <div class="bg-gray-800 border border-gray-700 p-6">
                <h4 class="text-lg font-semibold text-white mb-3">Benefits</h4>
                <ul class="text-gray-300 space-y-2">
                    <li>• Track campaign performance</li>
                    <li>• Measure ROI accurately</li>
                    <li>• Optimize marketing spend</li>
                    <li>• Identify best channels</li>
                    <li>• Data-driven decisions</li>
                </ul>
            </div>
            <div class="bg-gray-800 border border-gray-700 p-6">
                <h4 class="text-lg font-semibold text-white mb-3">Use Cases</h4>
                <ul class="text-gray-300 space-y-2">
                    <li>• Email campaigns</li>
                    <li>• Social media ads</li>
                    <li>• Google Ads</li>
                    <li>• Affiliate marketing</li>
                    <li>• Content marketing</li>
                </ul>
            </div>
        </div>

        <!-- FAQ Section -->
        <div class="px-8 pt-16 pb-16">
            <h2 class="text-3xl font-bold text-white text-center mb-8">Frequently Asked Questions</h2>
            <div class="max-w-4xl mx-auto space-y-6">
                <div class="border-b border-gray-700 pb-6">
                    <h3 class="text-xl font-semibold text-white mb-3">What are UTM parameters?</h3>
                    <p class="text-gray-300">UTM parameters are tags added to URLs that help track the effectiveness of marketing campaigns in Google Analytics and other analytics tools.</p>
                </div>

                <div class="border-b border-gray-700 pb-6">
                    <h3 class="text-xl font-semibold text-white mb-3">Which UTM parameters are required?</h3>
                    <p class="text-gray-300">Only utm_source and utm_medium are required. utm_campaign, utm_term, and utm_content are optional but recommended for detailed tracking.</p>
                </div>

                <div class="border-b border-gray-700 pb-6">
                    <h3 class="text-xl font-semibold text-white mb-3">How do I track UTM campaigns in Google Analytics?</h3>
                    <p class="text-gray-300">In Google Analytics, go to Acquisition > Campaigns > All Campaigns to see your UTM-tagged traffic and campaign performance data.</p>
                </div>

                <div class="border-b border-gray-700 pb-6">
                    <h3 class="text-xl font-semibold text-white mb-3">Can I use UTM parameters for internal links?</h3>
                    <p class="text-gray-300">It's not recommended to use UTM parameters for internal links as they can interfere with your analytics data and create new sessions.</p>
                </div>

                <div class="mb-0">
                    <h3 class="text-xl font-semibold text-white mb-3">Are UTM parameters case-sensitive?</h3>
                    <p class="text-gray-300 mb-0">Yes, UTM parameters are case-sensitive. "Facebook" and "facebook" will be treated as different sources in your analytics.</p>
                </div>
            </div>
        </div>
    </div>

<!-- 引入QR码生成库 -->
<script src="https://cdn.jsdelivr.net/npm/qrious@4.0.2/dist/qrious.min.js"></script>

<script>
// UTM Builder functionality
document.addEventListener('DOMContentLoaded', function() {
    const websiteUrl = document.getElementById('websiteUrl');
    const utmSource = document.getElementById('utmSource');
    const utmMedium = document.getElementById('utmMedium');
    const utmCampaign = document.getElementById('utmCampaign');
    const utmTerm = document.getElementById('utmTerm');
    const utmContent = document.getElementById('utmContent');
    const utmUrl = document.getElementById('utmUrl');
    const clearForm = document.getElementById('clearForm');
    const fillExample = document.getElementById('fillExample');
    const copyUrl = document.getElementById('copyUrl');
    const generateQR = document.getElementById('generateQR');
    const copyTooltip = document.getElementById('copyTooltip');
    const qrTooltip = document.getElementById('qrTooltip');
    const qrCodeSection = document.getElementById('qrCodeSection');
    const qrCanvas = document.getElementById('qrCanvas');
    const downloadQR = document.getElementById('downloadQR');
    
    // 预览元素
    const previewSource = document.getElementById('previewSource');
    const previewMedium = document.getElementById('previewMedium');
    const previewCampaign = document.getElementById('previewCampaign');
    const previewTerm = document.getElementById('previewTerm');
    const previewContent = document.getElementById('previewContent');
    
    // 实时更新UTM URL
    const inputs = [websiteUrl, utmSource, utmMedium, utmCampaign, utmTerm, utmContent];
    inputs.forEach(input => {
        input.addEventListener('input', updateUTMUrl);
    });

    // 清除表单
    clearForm.addEventListener('click', () => {
        inputs.forEach(input => input.value = '');
        updateUTMUrl();
        qrCodeSection.classList.add('hidden');
    });

    // 填充示例
    fillExample.addEventListener('click', () => {
        websiteUrl.value = 'https://example.com';
        utmSource.value = 'facebook';
        utmMedium.value = 'social';
        utmCampaign.value = 'spring_sale';
        utmTerm.value = 'running_shoes';
        utmContent.value = 'ad_variant_a';
        updateUTMUrl();
    });

    // 复制URL
    copyUrl.addEventListener('click', async () => {
        if (utmUrl.value) {
            try {
                await navigator.clipboard.writeText(utmUrl.value);
                
                if (copyTooltip) {
                    copyTooltip.textContent = 'Copied!';
                    copyTooltip.style.opacity = '1';
                    copyTooltip.style.visibility = 'visible';
                    
                    setTimeout(() => {
                        copyTooltip.textContent = 'Copy UTM URL';
                        copyTooltip.style.opacity = '0';
                        copyTooltip.style.visibility = 'hidden';
                    }, 2000);
                }
            } catch (error) {
                console.error('Failed to copy:', error);
            }
        }
    });

    // 生成QR码
    generateQR.addEventListener('click', () => {
        if (utmUrl.value) {
            generateQRCode(utmUrl.value);
            qrCodeSection.classList.remove('hidden');
            
            if (qrTooltip) {
                qrTooltip.textContent = 'Generated!';
                qrTooltip.style.opacity = '1';
                qrTooltip.style.visibility = 'visible';
                
                setTimeout(() => {
                    qrTooltip.textContent = 'Generate QR code';
                    qrTooltip.style.opacity = '0';
                    qrTooltip.style.visibility = 'hidden';
                }, 2000);
            }
        }
    });

    // 下载QR码
    downloadQR.addEventListener('click', () => {
        const link = document.createElement('a');
        link.download = 'utm-qr-code.png';
        link.href = qrCanvas.toDataURL();
        link.click();
    });

    function updateUTMUrl() {
        const baseUrl = websiteUrl.value.trim();
        if (!baseUrl) {
            utmUrl.value = '';
            updatePreview();
            return;
        }

        const params = new URLSearchParams();
        
        if (utmSource.value.trim()) params.set('utm_source', utmSource.value.trim());
        if (utmMedium.value.trim()) params.set('utm_medium', utmMedium.value.trim());
        if (utmCampaign.value.trim()) params.set('utm_campaign', utmCampaign.value.trim());
        if (utmTerm.value.trim()) params.set('utm_term', utmTerm.value.trim());
        if (utmContent.value.trim()) params.set('utm_content', utmContent.value.trim());
        
        const separator = baseUrl.includes('?') ? '&' : '?';
        const finalUrl = params.toString() ? `${baseUrl}${separator}${params.toString()}` : baseUrl;
        
        utmUrl.value = finalUrl;
        updatePreview();
    }

    function updatePreview() {
        previewSource.textContent = utmSource.value.trim() || '-';
        previewMedium.textContent = utmMedium.value.trim() || '-';
        previewCampaign.textContent = utmCampaign.value.trim() || '-';
        previewTerm.textContent = utmTerm.value.trim() || '-';
        previewContent.textContent = utmContent.value.trim() || '-';
    }

    function generateQRCode(text) {
        if (!text.trim()) {
            return;
        }

        // 检查QRious库是否可用
        if (typeof QRious !== 'undefined') {
            try {
                // 使用QRious生成真实的QR码
                const canvas = qrCanvas;

                // 创建QRious实例
                const qr = new QRious({
                    element: canvas,
                    value: text,
                    size: 200,
                    foreground: '#000000',
                    background: '#ffffff',
                    level: 'M'
                });

            } catch (error) {
                console.error('QRious generation failed, using fallback:', error);
                generateQRCodeFallback(text);
            }
        } else {
            // 使用备用方案
            console.log('QRious not loaded, using fallback API');
            generateQRCodeFallback(text);
        }
    }

    // 备用QR码生成方法（使用在线API）
    function generateQRCodeFallback(text) {
        const canvas = qrCanvas;
        const ctx = canvas.getContext('2d');

        // 使用QR Server API作为备用方案
        const qrUrl = `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(text)}`;

        const img = new Image();
        img.crossOrigin = 'anonymous';
        img.onload = function() {
            canvas.width = 200;
            canvas.height = 200;
            ctx.drawImage(img, 0, 0, 200, 200);
        };
        img.onerror = function() {
            // 如果API也失败，显示错误信息
            canvas.width = 200;
            canvas.height = 200;
            ctx.fillStyle = '#f3f4f6';
            ctx.fillRect(0, 0, 200, 200);
            ctx.fillStyle = '#ef4444';
            ctx.font = '14px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('QR Code', 100, 90);
            ctx.fillText('Generation Failed', 100, 110);
        };
        img.src = qrUrl;
    }

    // 初始化
    updateUTMUrl();
});
</script>

</div>

<?php include_once ROOT_PATH . '/templates/layout/footer.php'; ?>
