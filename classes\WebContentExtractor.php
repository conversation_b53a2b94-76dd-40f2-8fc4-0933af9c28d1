<?php
/**
 * 网站内容提取器
 * 从网站URL提取元数据、内容和技术栈信息
 */

class WebContentExtractor {
    // 使用Googlebot UA以提高站点兼容性
    private $userAgent = 'Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)';
    private $timeout = 15;
    
    public function extractWebsiteData($url) {
        try {
            // 获取网页内容
            $html = $this->fetchWebpage($url);
            if (!$html) {
                return null;
            }
            
            // 解析HTML - 使用libxml选项来抑制HTML5标签警告
            $dom = new DOMDocument();
            libxml_use_internal_errors(true); // 抑制libxml错误
            $dom->loadHTML('<?xml encoding="UTF-8">' . $html, LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);
            libxml_clear_errors(); // 清除错误缓冲区

            // 移除无用元素 - 优化内容提取质量
            $this->removeUnwantedElements($dom);

            $xpath = new DOMXPath($dom);
            
            // 提取各种数据
            $data = [
                'url' => $url,
                'meta_data' => $this->extractMetaData($dom, $xpath),
                'content' => $this->extractContent($dom, $xpath, $html),
                'tech_stack' => $this->detectTechStack($html),
                'structured_data' => $this->extractStructuredData($html)
            ];
            
            return $data;
            
        } catch (Exception $e) {
            error_log("WebContentExtractor error: " . $e->getMessage());
            return null;
        }
    }
    
    private function fetchWebpage($url) {
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_MAXREDIRS => 5,
            CURLOPT_TIMEOUT => $this->timeout,
            CURLOPT_USERAGENT => $this->userAgent,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_SSL_VERIFYHOST => false,
            CURLOPT_ENCODING => '', // 自动解压gzip/deflate
            CURLOPT_HTTPHEADER => [
                'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language: en-US,en;q=0.5',
                'Connection: keep-alive',
            ]
        ]);
        
        $html = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error || $httpCode < 200 || $httpCode >= 400) {
            return null;
        }
        
        return $html;
    }
    
    private function extractMetaData($dom, $xpath) {
        $meta = [];
        
        // 基础meta标签
        $title = $xpath->query('//title')->item(0);
        $meta['title'] = $title ? trim($title->textContent) : '';
        
        $description = $xpath->query('//meta[@name="description"]/@content')->item(0);
        $meta['description'] = $description ? trim($description->value) : '';
        
        $keywords = $xpath->query('//meta[@name="keywords"]/@content')->item(0);
        $meta['keywords'] = $keywords ? trim($keywords->value) : '';
        
        // Open Graph标签
        $ogTitle = $xpath->query('//meta[@property="og:title"]/@content')->item(0);
        $meta['og_title'] = $ogTitle ? trim($ogTitle->value) : '';
        
        $ogDescription = $xpath->query('//meta[@property="og:description"]/@content')->item(0);
        $meta['og_description'] = $ogDescription ? trim($ogDescription->value) : '';
        
        $ogImage = $xpath->query('//meta[@property="og:image"]/@content')->item(0);
        $meta['og_image'] = $ogImage ? trim($ogImage->value) : '';
        
        $ogUrl = $xpath->query('//meta[@property="og:url"]/@content')->item(0);
        $meta['og_url'] = $ogUrl ? trim($ogUrl->value) : '';
        
        // Twitter Card标签
        $twitterTitle = $xpath->query('//meta[@name="twitter:title"]/@content')->item(0);
        $meta['twitter_title'] = $twitterTitle ? trim($twitterTitle->value) : '';
        
        $twitterDescription = $xpath->query('//meta[@name="twitter:description"]/@content')->item(0);
        $meta['twitter_description'] = $twitterDescription ? trim($twitterDescription->value) : '';
        
        // Favicon
        $favicon = $xpath->query('//link[@rel="icon"]/@href | //link[@rel="shortcut icon"]/@href')->item(0);
        $meta['favicon'] = $favicon ? trim($favicon->value) : '';
        
        return $meta;
    }
    
    private function extractContent($dom, $xpath, $html) {
        $content = [];
        
        // 提取标题
        $content['headings'] = [
            'h1' => $this->extractTextFromNodes($xpath->query('//h1')),
            'h2' => $this->extractTextFromNodes($xpath->query('//h2')),
            'h3' => $this->extractTextFromNodes($xpath->query('//h3'))
        ];
        
        // 提取主要内容
        $mainContent = '';
        $contentSelectors = [
            '//main',
            '//article',
            '//div[@class*="content"]',
            '//div[@id*="content"]',
            '//section',
            '//div[@class*="main"]'
        ];
        
        foreach ($contentSelectors as $selector) {
            $nodes = $xpath->query($selector);
            if ($nodes->length > 0) {
                $mainContent = trim($nodes->item(0)->textContent);
                if (strlen($mainContent) > 100) {
                    break;
                }
            }
        }
        
        $content['main_content'] = $this->cleanText($mainContent);
        
        // 提取功能特性（通过常见的模式）
        $content['features'] = $this->extractFeatures($xpath, $html);
        
        // 提取CTA按钮
        $content['cta_buttons'] = $this->extractCTAButtons($xpath);
        
        // 提取价格信息
        $content['pricing_info'] = $this->extractPricingInfo($html);
        
        return $content;
    }
    
    private function extractFeatures($xpath, $html) {
        $features = [];

        // 查找包含功能的常见模式 - 扩展选择器
        $featureSelectors = [
            // 明确的功能列表
            '//ul/li[contains(text(), "feature") or contains(text(), "Feature")]',
            '//ol/li[contains(text(), "feature") or contains(text(), "Feature")]',

            // 功能相关的容器
            '//div[contains(@class, "feature")]//h3 | //div[contains(@class, "feature")]//h4',
            '//section[contains(@class, "feature")]//h3 | //section[contains(@class, "feature")]//h4',
            '//div[contains(@class, "benefit")]//h3 | //div[contains(@class, "benefit")]//h4',

            // 通用列表项（长度适中的）
            '//ul/li[string-length(text()) > 10 and string-length(text()) < 150]',
            '//ol/li[string-length(text()) > 10 and string-length(text()) < 150]',

            // 标题中的功能描述
            '//h3[string-length(text()) > 10 and string-length(text()) < 100]',
            '//h4[string-length(text()) > 10 and string-length(text()) < 100]',

            // 卡片或项目容器
            '//div[contains(@class, "card")]//h3 | //div[contains(@class, "card")]//h4',
            '//div[contains(@class, "item")]//h3 | //div[contains(@class, "item")]//h4'
        ];

        foreach ($featureSelectors as $selector) {
            $nodes = $xpath->query($selector);
            foreach ($nodes as $node) {
                $text = trim($node->textContent);
                // 过滤掉明显不是功能的文本
                if (strlen($text) > 5 && strlen($text) < 200 &&
                    !$this->isNavigationText($text) &&
                    !in_array(strtolower($text), $features)) {
                    $features[] = $text;
                }
            }

            if (count($features) >= 10) {
                break;
            }
        }

        return array_unique(array_slice($features, 0, 8));
    }

    /**
     * 检查文本是否是导航或非功能性文本
     */
    private function isNavigationText($text) {
        $navigationKeywords = [
            'home', 'about', 'contact', 'login', 'signup', 'register', 'menu',
            'navigation', 'footer', 'header', 'sidebar', 'search', 'subscribe',
            'follow us', 'social media', 'copyright', 'privacy', 'terms',
            'click here', 'read more', 'learn more', 'get started', 'try now'
        ];

        $lowerText = strtolower($text);
        foreach ($navigationKeywords as $keyword) {
            if (strpos($lowerText, $keyword) !== false) {
                return true;
            }
        }

        return false;
    }
    
    private function extractCTAButtons($xpath) {
        $buttons = [];
        
        $buttonSelectors = [
            '//button[contains(@class, "cta") or contains(@class, "btn")]',
            '//a[contains(@class, "button") or contains(@class, "btn")]',
            '//input[@type="submit"]'
        ];
        
        foreach ($buttonSelectors as $selector) {
            $nodes = $xpath->query($selector);
            foreach ($nodes as $node) {
                $text = trim($node->textContent);
                if (strlen($text) > 2 && strlen($text) < 50) {
                    $buttons[] = $text;
                }
            }
        }
        
        return array_unique(array_slice($buttons, 0, 10));
    }
    
    private function extractPricingInfo($html) {
        $pricing = [];
        
        // 查找价格相关的文本
        $pricePatterns = [
            '/\$\d+(?:\.\d{2})?(?:\s*\/\s*(?:month|year|mo|yr))?/i',
            '/€\d+(?:\.\d{2})?(?:\s*\/\s*(?:month|year|mo|yr))?/i',
            '/£\d+(?:\.\d{2})?(?:\s*\/\s*(?:month|year|mo|yr))?/i',
            '/free|trial|premium|pro|basic|starter/i'
        ];
        
        foreach ($pricePatterns as $pattern) {
            if (preg_match_all($pattern, $html, $matches)) {
                $pricing = array_merge($pricing, $matches[0]);
            }
        }
        
        return array_unique(array_slice($pricing, 0, 10));
    }
    
    private function detectTechStack($html) {
        $techStack = [];
        
        // 检测常见的技术栈
        $technologies = [
            'React' => '/react/i',
            'Vue.js' => '/vue\.js|vuejs/i',
            'Angular' => '/angular/i',
            'jQuery' => '/jquery/i',
            'Bootstrap' => '/bootstrap/i',
            'Tailwind CSS' => '/tailwind/i',
            'WordPress' => '/wp-content|wordpress/i',
            'Shopify' => '/shopify/i',
            'Stripe' => '/stripe/i',
            'Google Analytics' => '/google-analytics|gtag/i',
            'Font Awesome' => '/font-awesome|fontawesome/i',
            'Next.js' => '/next\.js|nextjs/i',
            'Nuxt.js' => '/nuxt\.js|nuxtjs/i'
        ];
        
        foreach ($technologies as $tech => $pattern) {
            if (preg_match($pattern, $html)) {
                $techStack[] = $tech;
            }
        }
        
        return $techStack;
    }
    
    private function extractStructuredData($html) {
        $structuredData = [];
        
        // 提取JSON-LD
        if (preg_match_all('/<script[^>]*type=["\']application\/ld\+json["\'][^>]*>(.*?)<\/script>/is', $html, $matches)) {
            foreach ($matches[1] as $jsonLd) {
                $data = json_decode(trim($jsonLd), true);
                if ($data) {
                    $structuredData[] = $data;
                }
            }
        }
        
        return $structuredData;
    }
    
    private function extractTextFromNodes($nodes) {
        $texts = [];
        foreach ($nodes as $node) {
            $text = trim($node->textContent);
            if (strlen($text) > 2) {
                $texts[] = $text;
            }
        }
        return $texts;
    }
    
    private function cleanText($text) {
        // 清理文本
        $text = preg_replace('/\s+/', ' ', $text);
        $text = trim($text);
        return $text;
    }

    /**
     * 移除无用的HTML元素，提高内容提取质量
     */
    private function removeUnwantedElements($dom) {
        // 需要移除的元素标签 - 基于最佳实践和内容提取优化
        $removeElements = [
            // 脚本和样式
            'script', 'style', 'noscript',
            // 导航和结构元素
            'nav', 'header', 'footer', 'aside',
            // 广告和跟踪
            'iframe', 'embed', 'object',
            // 表单元素（通常不是主要内容）
            'form', 'input', 'button', 'select', 'textarea',
            // 其他非内容元素
            'canvas', 'svg', 'audio', 'video',
            // 注释和元数据
            'meta', 'link', 'base'
        ];

        foreach ($removeElements as $tag) {
            $elements = $dom->getElementsByTagName($tag);
            // 从后往前删除，避免索引问题
            for ($i = $elements->length - 1; $i >= 0; $i--) {
                $element = $elements->item($i);
                if ($element && $element->parentNode) {
                    $element->parentNode->removeChild($element);
                }
            }
        }

        // 移除包含特定class或id的广告/导航元素
        $xpath = new DOMXPath($dom);
        $unwantedSelectors = [
            // 广告相关
            "//*[contains(@class, 'ad') or contains(@class, 'advertisement') or contains(@class, 'banner')]",
            "//*[contains(@id, 'ad') or contains(@id, 'advertisement') or contains(@id, 'banner')]",
            // 导航相关
            "//*[contains(@class, 'nav') or contains(@class, 'menu') or contains(@class, 'sidebar')]",
            "//*[contains(@id, 'nav') or contains(@id, 'menu') or contains(@id, 'sidebar')]",
            // 社交媒体和分享
            "//*[contains(@class, 'social') or contains(@class, 'share') or contains(@class, 'follow')]",
            // 评论和互动
            "//*[contains(@class, 'comment') or contains(@class, 'discussion')]",
            // Cookie和隐私通知
            "//*[contains(@class, 'cookie') or contains(@class, 'privacy') or contains(@class, 'gdpr')]"
        ];

        foreach ($unwantedSelectors as $selector) {
            $elements = $xpath->query($selector);
            for ($i = $elements->length - 1; $i >= 0; $i--) {
                $element = $elements->item($i);
                if ($element && $element->parentNode) {
                    $element->parentNode->removeChild($element);
                }
            }
        }
    }
}
?>
