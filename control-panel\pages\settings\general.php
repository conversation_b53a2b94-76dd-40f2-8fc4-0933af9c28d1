<?php
/**
 * 系统基础设置页面
 * 管理网站的基本配置信息
 */

// 定义根目录
define('ROOT_PATH', dirname(dirname(dirname(__DIR__))));

// 加载认证中间件
require_once dirname(__DIR__) . '/../auth/middleware.php';

// 权限检查
if (!hasPermission('settings.view')) {
    header('HTTP/1.1 403 Forbidden');
    exit('Access denied');
}

// 处理表单提交
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && hasPermission('settings.edit')) {
    // 验证CSRF Token（简化版）
    if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== ($_SESSION['csrf_token'] ?? '')) {
        $message = 'Invalid security token.';
        $messageType = 'error';
    } else {
        // 处理设置更新
        $settings = [
            'site_name' => $_POST['site_name'] ?? '',
            'site_description' => $_POST['site_description'] ?? '',
            'site_keywords' => $_POST['site_keywords'] ?? '',
            'admin_email' => $_POST['admin_email'] ?? '',
            'timezone' => $_POST['timezone'] ?? '',
            'maintenance_mode' => isset($_POST['maintenance_mode']),
            'user_registration' => isset($_POST['user_registration']),
            'google_analytics' => $_POST['google_analytics'] ?? '',
            'items_per_page' => intval($_POST['items_per_page'] ?? 20)
        ];
        
        // 这里应该保存到数据库
        // saveSettings($settings);
        
        $message = 'Settings updated successfully.';
        $messageType = 'success';
    }
}

// 生成CSRF Token
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

// 获取当前设置（模拟数据）
$currentSettings = [
    'site_name' => 'Prompt2Tool',
    'site_description' => 'Free AI-Powered Online Tools Platform',
    'site_keywords' => 'online tools, free tools, AI tools, developer tools, productivity tools',
    'admin_email' => '<EMAIL>',
    'timezone' => 'UTC',
    'maintenance_mode' => false,
    'user_registration' => true,
    'google_analytics' => '',
    'items_per_page' => 20
];

$currentPage = 'settings';
include_once dirname(__DIR__) . '/../components/layout/header.php';
?>

<div class="min-h-screen bg-gray-100">
    <!-- 侧边栏 -->
    <div class="fixed inset-y-0 left-0 w-64 bg-gray-900">
        <?php include_once dirname(__DIR__) . '/../components/layout/sidebar.php'; ?>
    </div>
    
    <!-- 主内容区 -->
    <div class="ml-64">
        <!-- 顶部导航栏 -->
        <div class="bg-white shadow-sm border-b border-gray-200">
            <div class="px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-16">
                    <div class="flex items-center">
                        <h1 class="text-2xl font-semibold text-gray-900">General Settings</h1>
                    </div>
                    
                    <!-- 设置导航 -->
                    <div class="flex items-center space-x-4">
                        <nav class="flex space-x-4">
                            <a href="general.php" class="text-accent border-b-2 border-accent px-3 py-2 text-sm font-medium">
                                General
                            </a>
                            <a href="security.php" class="text-gray-500 hover:text-gray-700 px-3 py-2 text-sm font-medium">
                                Security
                            </a>
                            <a href="backup.php" class="text-gray-500 hover:text-gray-700 px-3 py-2 text-sm font-medium">
                                Backup
                            </a>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 页面内容 -->
        <div class="p-6">
            <!-- 消息提示 -->
            <?php if ($message): ?>
            <div class="mb-6 p-4 border-l-4 <?= $messageType === 'success' ? 'bg-green-50 border-green-400 text-green-700' : 'bg-red-50 border-red-400 text-red-700' ?>">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <i class="fas fa-<?= $messageType === 'success' ? 'check-circle' : 'exclamation-circle' ?>"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm"><?= htmlspecialchars($message) ?></p>
                    </div>
                </div>
            </div>
            <?php endif; ?>
            
            <!-- 设置表单 -->
            <form method="POST" class="space-y-6">
                <input type="hidden" name="csrf_token" value="<?= $_SESSION['csrf_token'] ?>">
                
                <!-- 网站基本信息 -->
                <div class="bg-white border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Site Information</h3>
                    
                    <div class="grid grid-cols-1 gap-6">
                        <!-- 网站名称 -->
                        <div>
                            <label for="site_name" class="block text-sm font-medium text-gray-700 mb-2">
                                Site Name
                            </label>
                            <input type="text" 
                                   id="site_name" 
                                   name="site_name" 
                                   value="<?= htmlspecialchars($currentSettings['site_name']) ?>"
                                   class="w-full px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent"
                                   required>
                        </div>
                        
                        <!-- 网站描述 -->
                        <div>
                            <label for="site_description" class="block text-sm font-medium text-gray-700 mb-2">
                                Site Description
                            </label>
                            <textarea id="site_description" 
                                      name="site_description" 
                                      rows="3"
                                      class="w-full px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent"
                                      required><?= htmlspecialchars($currentSettings['site_description']) ?></textarea>
                        </div>
                        
                        <!-- 关键词 -->
                        <div>
                            <label for="site_keywords" class="block text-sm font-medium text-gray-700 mb-2">
                                Site Keywords
                            </label>
                            <input type="text" 
                                   id="site_keywords" 
                                   name="site_keywords" 
                                   value="<?= htmlspecialchars($currentSettings['site_keywords']) ?>"
                                   class="w-full px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent"
                                   placeholder="Separate keywords with commas">
                            <p class="mt-1 text-sm text-gray-500">Separate multiple keywords with commas</p>
                        </div>
                        
                        <!-- 管理员邮箱 -->
                        <div>
                            <label for="admin_email" class="block text-sm font-medium text-gray-700 mb-2">
                                Administrator Email
                            </label>
                            <input type="email" 
                                   id="admin_email" 
                                   name="admin_email" 
                                   value="<?= htmlspecialchars($currentSettings['admin_email']) ?>"
                                   class="w-full px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent"
                                   required>
                        </div>
                    </div>
                </div>
                
                <!-- 系统配置 -->
                <div class="bg-white border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">System Configuration</h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- 时区设置 -->
                        <div>
                            <label for="timezone" class="block text-sm font-medium text-gray-700 mb-2">
                                Timezone
                            </label>
                            <select id="timezone" 
                                    name="timezone" 
                                    class="w-full px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent">
                                <option value="UTC" <?= $currentSettings['timezone'] === 'UTC' ? 'selected' : '' ?>>UTC</option>
                                <option value="America/New_York" <?= $currentSettings['timezone'] === 'America/New_York' ? 'selected' : '' ?>>Eastern Time</option>
                                <option value="America/Chicago" <?= $currentSettings['timezone'] === 'America/Chicago' ? 'selected' : '' ?>>Central Time</option>
                                <option value="America/Denver" <?= $currentSettings['timezone'] === 'America/Denver' ? 'selected' : '' ?>>Mountain Time</option>
                                <option value="America/Los_Angeles" <?= $currentSettings['timezone'] === 'America/Los_Angeles' ? 'selected' : '' ?>>Pacific Time</option>
                                <option value="Europe/London" <?= $currentSettings['timezone'] === 'Europe/London' ? 'selected' : '' ?>>London</option>
                                <option value="Europe/Paris" <?= $currentSettings['timezone'] === 'Europe/Paris' ? 'selected' : '' ?>>Paris</option>
                                <option value="Asia/Tokyo" <?= $currentSettings['timezone'] === 'Asia/Tokyo' ? 'selected' : '' ?>>Tokyo</option>
                                <option value="Asia/Shanghai" <?= $currentSettings['timezone'] === 'Asia/Shanghai' ? 'selected' : '' ?>>Shanghai</option>
                            </select>
                        </div>
                        
                        <!-- 每页显示项目数 -->
                        <div>
                            <label for="items_per_page" class="block text-sm font-medium text-gray-700 mb-2">
                                Items Per Page
                            </label>
                            <select id="items_per_page" 
                                    name="items_per_page" 
                                    class="w-full px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent">
                                <option value="10" <?= $currentSettings['items_per_page'] === 10 ? 'selected' : '' ?>>10</option>
                                <option value="20" <?= $currentSettings['items_per_page'] === 20 ? 'selected' : '' ?>>20</option>
                                <option value="50" <?= $currentSettings['items_per_page'] === 50 ? 'selected' : '' ?>>50</option>
                                <option value="100" <?= $currentSettings['items_per_page'] === 100 ? 'selected' : '' ?>>100</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <!-- 功能开关 -->
                <div class="bg-white border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Feature Toggles</h3>
                    
                    <div class="space-y-4">
                        <!-- 维护模式 -->
                        <div class="flex items-center justify-between">
                            <div>
                                <label for="maintenance_mode" class="text-sm font-medium text-gray-700">
                                    Maintenance Mode
                                </label>
                                <p class="text-sm text-gray-500">Enable maintenance mode to temporarily disable the site</p>
                            </div>
                            <div>
                                <input type="checkbox" 
                                       id="maintenance_mode" 
                                       name="maintenance_mode" 
                                       <?= $currentSettings['maintenance_mode'] ? 'checked' : '' ?>
                                       class="w-4 h-4 text-accent border-gray-300 focus:ring-accent">
                            </div>
                        </div>
                        
                        <!-- 用户注册 -->
                        <div class="flex items-center justify-between">
                            <div>
                                <label for="user_registration" class="text-sm font-medium text-gray-700">
                                    User Registration
                                </label>
                                <p class="text-sm text-gray-500">Allow new users to register accounts</p>
                            </div>
                            <div>
                                <input type="checkbox" 
                                       id="user_registration" 
                                       name="user_registration" 
                                       <?= $currentSettings['user_registration'] ? 'checked' : '' ?>
                                       class="w-4 h-4 text-accent border-gray-300 focus:ring-accent">
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 第三方集成 -->
                <div class="bg-white border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Third-party Integrations</h3>
                    
                    <div class="grid grid-cols-1 gap-6">
                        <!-- Google Analytics -->
                        <div>
                            <label for="google_analytics" class="block text-sm font-medium text-gray-700 mb-2">
                                Google Analytics Tracking ID
                            </label>
                            <input type="text" 
                                   id="google_analytics" 
                                   name="google_analytics" 
                                   value="<?= htmlspecialchars($currentSettings['google_analytics']) ?>"
                                   class="w-full px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent"
                                   placeholder="G-XXXXXXXXXX">
                            <p class="mt-1 text-sm text-gray-500">Enter your Google Analytics tracking ID to enable analytics</p>
                        </div>
                    </div>
                </div>
                
                <!-- 保存按钮 -->
                <?php if (hasPermission('settings.edit')): ?>
                <div class="flex justify-end">
                    <button type="submit" 
                            class="bg-accent text-white px-6 py-2 hover:bg-blue-600 transition-colors">
                        <i class="fas fa-save mr-2"></i>Save Settings
                    </button>
                </div>
                <?php endif; ?>
            </form>
        </div>
    </div>
</div>

<?php include_once dirname(__DIR__) . '/../components/layout/footer.php'; ?>
