<?php
/**
 * 工具管理页面入口
 * 包含工具管理的主要功能
 */

// 检查用户权限
if (!isset($_SESSION['admin_logged_in']) || !$_SESSION['admin_logged_in']) {
    header('Location: auth/login.php');
    exit;
}

// 处理操作
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $tool_id = $_POST['tool_id'] ?? '';
    
    try {
        switch ($action) {
            case 'create':
                // 检查slug是否重复
                $stmt = $pdo->prepare("SELECT COUNT(*) FROM pt_tool WHERE slug = ?");
                $stmt->execute([$_POST['slug']]);
                if ($stmt->fetchColumn() > 0) {
                    throw new Exception("Slug already exists. Please choose a different slug.");
                }

                // 处理生成的文件部署
                if (isset($_POST['generated_file_path']) && !empty($_POST['generated_file_path'])) {
                    $generatedFilePath = $_POST['generated_file_path'];
                    $sourceFile = ROOT_PATH . '/' . $generatedFilePath;

                    if (file_exists($sourceFile)) {
                        // 目标文件路径
                        $targetDir = ROOT_PATH . '/templates/pages/tools/';
                        $targetFile = $targetDir . $_POST['slug'] . '.php';

                        // 确保目标目录存在
                        if (!is_dir($targetDir)) {
                            mkdir($targetDir, 0755, true);
                        }

                        // 复制文件到正式目录
                        if (!copy($sourceFile, $targetFile)) {
                            throw new Exception("Failed to deploy generated file.");
                        }

                        // 删除临时文件
                        unlink($sourceFile);
                    }
                }

                // 处理文件上传或生成的文件
                $fileType = 'php'; // 默认文件类型

                // 检查是否有生成的文件路径
                if (isset($_POST['generated_file_path']) && !empty($_POST['generated_file_path'])) {
                    // 处理生成的文件
                    $generatedFilePath = MGMT_ROOT . '/' . $_POST['generated_file_path'];

                    if (!file_exists($generatedFilePath)) {
                        throw new Exception("Generated file not found.");
                    }

                    $fileExtension = strtolower(pathinfo($generatedFilePath, PATHINFO_EXTENSION));
                    $fileType = $fileExtension;

                    // 复制生成的文件到工具目录
                    $targetPath = ROOT_PATH . '/templates/pages/tools/' . $_POST['slug'] . '.' . $fileExtension;

                    if (!copy($generatedFilePath, $targetPath)) {
                        throw new Exception("Failed to copy generated file.");
                    }

                } elseif (isset($_FILES['tool_file']) && $_FILES['tool_file']['error'] === UPLOAD_ERR_OK) {
                    // 处理上传的文件
                    $uploadedFile = $_FILES['tool_file'];
                    $fileExtension = strtolower(pathinfo($uploadedFile['name'], PATHINFO_EXTENSION));

                    // 验证文件类型
                    $allowedExtensions = ['php', 'html', 'htm'];
                    if (in_array($fileExtension, $allowedExtensions)) {
                        $fileType = $fileExtension;

                        // 保存文件到工具目录
                        $targetPath = ROOT_PATH . '/templates/pages/tools/' . $_POST['slug'] . '.' . $fileExtension;

                        if (!move_uploaded_file($uploadedFile['tmp_name'], $targetPath)) {
                            throw new Exception("Failed to save uploaded file.");
                        }
                    } else {
                        throw new Exception("Invalid file type. Only PHP, HTML, and HTM files are allowed.");
                    }
                }

                // 处理category_id
                $categoryId = null;
                if (isset($_POST['category']) && !empty($_POST['category'])) {
                    // 如果传入的是category名称，查找对应的ID
                    $stmt = $pdo->prepare("SELECT id FROM pt_tool_category WHERE name = ? OR slug = ?");
                    $stmt->execute([$_POST['category'], $_POST['category']]);
                    $category = $stmt->fetch(PDO::FETCH_ASSOC);
                    if ($category) {
                        $categoryId = $category['id'];
                    }
                } elseif (isset($_POST['category_id']) && !empty($_POST['category_id'])) {
                    $categoryId = $_POST['category_id'];
                }

                $stmt = $pdo->prepare("
                    INSERT INTO pt_tool (name, slug, description, category_id, icon, tags, sort_order, status, is_featured, file_type)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ");
                $stmt->execute([
                    $_POST['name'],
                    $_POST['slug'],
                    $_POST['description'],
                    $categoryId,
                    $_POST['icon'],
                    $_POST['tags'],
                    intval($_POST['sort_order'] ?? 1),
                    $_POST['status'] ?? 'active',
                    isset($_POST['is_featured']) ? 1 : 0,
                    $fileType
                ]);
                $success_message = "Tool created successfully";

                // 如果是AJAX请求，返回JSON响应
                if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
                    header('Content-Type: application/json');
                    echo json_encode(['success' => true, 'message' => $success_message]);
                    exit;
                }
                break;
                
            case 'update':
                // 获取当前工具信息
                $stmt = $pdo->prepare("SELECT slug FROM pt_tool WHERE id = ?");
                $stmt->execute([intval($_POST['tool_id'])]);
                $currentTool = $stmt->fetch(PDO::FETCH_ASSOC);

                if (!$currentTool) {
                    throw new Exception("Tool not found.");
                }

                $oldSlug = $currentTool['slug'];
                $newSlug = $_POST['slug'];

                // 检查slug是否重复（排除当前工具）
                $stmt = $pdo->prepare("SELECT COUNT(*) FROM pt_tool WHERE slug = ? AND id != ?");
                $stmt->execute([$newSlug, intval($_POST['tool_id'])]);
                if ($stmt->fetchColumn() > 0) {
                    throw new Exception("Slug already exists. Please choose a different slug.");
                }

                // 如果slug发生变化，需要重命名物理文件
                if ($oldSlug !== $newSlug) {
                    $supportedExtensions = ['php', 'html', 'htm'];
                    $fileRenamed = false;

                    foreach ($supportedExtensions as $ext) {
                        $oldFilePath = ROOT_PATH . '/templates/pages/tools/' . $oldSlug . '.' . $ext;
                        if (file_exists($oldFilePath)) {
                            $newFilePath = ROOT_PATH . '/templates/pages/tools/' . $newSlug . '.' . $ext;
                            if (!rename($oldFilePath, $newFilePath)) {
                                throw new Exception("Failed to rename tool file from {$oldSlug}.{$ext} to {$newSlug}.{$ext}");
                            }
                            $fileRenamed = true;
                            break;
                        }
                    }
                }

                $stmt = $pdo->prepare("
                    UPDATE pt_tool
                    SET name = ?, slug = ?, description = ?, category_id = ?, icon = ?, tags = ?, sort_order = ?, status = ?, is_featured = ?
                    WHERE id = ?
                ");
                $stmt->execute([
                    $_POST['name'],
                    $newSlug,
                    $_POST['description'],
                    $_POST['category_id'] ?: null,
                    $_POST['icon'],
                    $_POST['tags'],
                    intval($_POST['sort_order']),
                    $_POST['status'],
                    isset($_POST['is_featured']) ? 1 : 0,
                    intval($_POST['tool_id'])
                ]);
                $success_message = "Tool updated successfully";
                break;
                
            case 'delete':
                // 获取工具信息以获取slug
                $stmt = $pdo->prepare("SELECT slug FROM pt_tool WHERE id = ?");
                $stmt->execute([intval($_POST['tool_id'])]);
                $tool = $stmt->fetch(PDO::FETCH_ASSOC);

                if ($tool) {
                    // 删除对应的物理文件（支持多种文件类型）
                    $supportedExtensions = ['php', 'html', 'htm'];
                    foreach ($supportedExtensions as $ext) {
                        $toolFilePath = ROOT_PATH . '/templates/pages/tools/' . $tool['slug'] . '.' . $ext;
                        if (file_exists($toolFilePath)) {
                            unlink($toolFilePath);
                            break; // 找到并删除后退出循环
                        }
                    }
                }

                // 删除数据库记录
                $stmt = $pdo->prepare("DELETE FROM pt_tool WHERE id = ?");
                $stmt->execute([intval($_POST['tool_id'])]);
                $success_message = "Tool deleted successfully";
                break;
                
            case 'toggle_status':
                $stmt = $pdo->prepare("UPDATE pt_tool SET status = ? WHERE id = ?");
                $new_status = $_POST['current_status'] === 'active' ? 'inactive' : 'active';
                $stmt->execute([$new_status, intval($_POST['tool_id'])]);
                $success_message = "Tool status updated successfully";
                break;
        }
    } catch (Exception $e) {
        $error_message = "Error: " . $e->getMessage();

        // 如果是AJAX请求，返回JSON错误响应
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => $error_message]);
            exit;
        }
    }
}

// 获取筛选参数
$filters = [
    'category' => $_GET['category'] ?? '',
    'status' => $_GET['status'] ?? '',
    'search' => $_GET['search'] ?? '',
    'sort' => $_GET['sort'] ?? 'newest',
    'page' => max(1, intval($_GET['p'] ?? 1)),
    'per_page' => 10
];

// 获取工具分类
$stmt = $pdo->query("SELECT * FROM pt_tool_category ORDER BY sort_order, name");
$categories = $stmt->fetchAll(PDO::FETCH_ASSOC);

// 构建排序条件
$orderBy = match($filters['sort']) {
    'name_asc' => 't.name ASC',
    'name_desc' => 't.name DESC',
    'category' => 'tc.name ASC, t.name ASC',
    'status' => 't.status ASC, t.name ASC',
    'oldest' => 't.created_at ASC, t.id ASC',
    'sort_order' => 't.sort_order ASC, t.name ASC',
    default => 't.created_at DESC, t.id DESC' // newest
};

// 获取所有工具
$stmt = $pdo->query("
    SELECT t.*, tc.name as category_name, tc.slug as category_slug
    FROM pt_tool t
    LEFT JOIN pt_tool_category tc ON t.category_id = tc.id
    ORDER BY {$orderBy}
");
$allTools = $stmt->fetchAll(PDO::FETCH_ASSOC);

// 应用筛选
$filteredTools = $allTools;

if (!empty($filters['search'])) {
    $filteredTools = array_filter($filteredTools, function($tool) use ($filters) {
        return stripos($tool['name'], $filters['search']) !== false || 
               stripos($tool['description'], $filters['search']) !== false;
    });
}

if (!empty($filters['category'])) {
    $filteredTools = array_filter($filteredTools, function($tool) use ($filters) {
        return $tool['category_name'] === $filters['category'];
    });
}

if ($filters['status'] !== '') {
    $filteredTools = array_filter($filteredTools, function($tool) use ($filters) {
        return $tool['status'] === $filters['status'];
    });
}

// 分页处理
$totalTools = count($filteredTools);
$totalPages = ceil($totalTools / $filters['per_page']);
$offset = ($filters['page'] - 1) * $filters['per_page'];
$paginatedTools = array_slice($filteredTools, $offset, $filters['per_page']);
?>

<div class="space-y-6">
    <!-- 页面描述 -->
    <div class="flex justify-between items-center">
        <div>
            <p class="text-gray-600">Manage and configure your AI tools</p>
        </div>
        
        <button onclick="openToolModal()" class="bg-accent text-white px-4 py-2 hover:bg-blue-700 hover:text-white transition-all duration-200">
            Add Tool
        </button>
    </div>

    <!-- 成功/错误消息 -->
    <?php if (isset($success_message)): ?>
    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3">
        <?= htmlspecialchars($success_message) ?>
    </div>
    <?php endif; ?>

    <?php if (isset($error_message)): ?>
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3">
        <?= htmlspecialchars($error_message) ?>
    </div>
    <?php endif; ?>

    <!-- 筛选和搜索 -->
    <div class="bg-white p-4 border border-gray-200">
        <form method="GET" class="flex flex-wrap gap-4 items-end">
            <input type="hidden" name="page" value="tools">
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Search</label>
                <input type="text" name="search" value="<?= htmlspecialchars($filters['search']) ?>" 
                       placeholder="Search tools..." 
                       class="px-3 py-2 border border-gray-300 focus:outline-none focus:border-accent">
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Category</label>
                <select name="category" class="px-3 py-2 border border-gray-300 focus:outline-none focus:border-accent">
                    <option value="">All Categories</option>
                    <?php foreach ($categories as $category): ?>
                    <option value="<?= htmlspecialchars($category['name']) ?>" <?= $filters['category'] === $category['name'] ? 'selected' : '' ?>>
                        <?= htmlspecialchars($category['name']) ?>
                    </option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                <select name="status" class="px-3 py-2 border border-gray-300 focus:outline-none focus:border-accent">
                    <option value="">All Status</option>
                    <option value="active" <?= $filters['status'] === 'active' ? 'selected' : '' ?>>Active</option>
                    <option value="inactive" <?= $filters['status'] === 'inactive' ? 'selected' : '' ?>>Inactive</option>
                    <option value="coming_soon" <?= $filters['status'] === 'coming_soon' ? 'selected' : '' ?>>Coming Soon</option>
                </select>
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Sort By</label>
                <select name="sort" class="px-3 py-2 border border-gray-300 focus:outline-none focus:border-accent">
                    <option value="newest" <?= $filters['sort'] === 'newest' ? 'selected' : '' ?>>Newest First</option>
                    <option value="oldest" <?= $filters['sort'] === 'oldest' ? 'selected' : '' ?>>Oldest First</option>
                    <option value="name_asc" <?= $filters['sort'] === 'name_asc' ? 'selected' : '' ?>>Name A-Z</option>
                    <option value="name_desc" <?= $filters['sort'] === 'name_desc' ? 'selected' : '' ?>>Name Z-A</option>
                    <option value="category" <?= $filters['sort'] === 'category' ? 'selected' : '' ?>>Category</option>
                    <option value="status" <?= $filters['sort'] === 'status' ? 'selected' : '' ?>>Status</option>
                    <option value="sort_order" <?= $filters['sort'] === 'sort_order' ? 'selected' : '' ?>>Sort Order</option>
                </select>
            </div>
            
            <button type="submit" class="px-4 py-2 bg-accent text-white hover:bg-blue-700 hover:text-white">
                Filter
            </button>
            
            <a href="?page=tools" class="px-4 py-2 bg-gray-300 text-gray-700 hover:bg-gray-400">
                Clear
            </a>

            <button type="button" onclick="clearCacheFiles()"
                    class="px-4 py-2 bg-red-600 text-white hover:bg-red-700 transition-colors">
                Clear Cache
            </button>
        </form>
    </div>

    <!-- 工具列表 -->
    <div class="bg-white border border-gray-200">
        <div class="overflow-x-auto">
            <table class="w-full">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tool</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Views</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php foreach ($paginatedTools as $tool): ?>
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-10 w-10">
                                    <div class="h-10 w-10 flex items-center justify-center text-2xl">
                                        <?= htmlspecialchars($tool['icon'] ?: '🔧') ?>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <div class="text-sm font-medium text-gray-900"><?= htmlspecialchars($tool['name']) ?></div>
                                    <div class="text-sm text-gray-500"><?= htmlspecialchars(substr($tool['description'] ?: '', 0, 60)) ?>...</div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 text-sm text-gray-900">
                            <span class="inline-flex px-2 py-1 text-xs font-semibold bg-gray-100 text-gray-800">
                                <?= htmlspecialchars($tool['category_name'] ?: 'No Category') ?>
                            </span>
                        </td>
                        <td class="px-6 py-4 text-sm text-gray-900">
                            <?= number_format($tool['view_count'] ?: 0) ?>
                        </td>
                        <td class="px-6 py-4">
                            <span class="inline-flex px-2 py-1 text-xs font-semibold leading-5 
                                <?php
                                switch($tool['status']) {
                                    case 'active': echo 'bg-green-100 text-green-800'; break;
                                    case 'inactive': echo 'bg-red-100 text-red-800'; break;
                                    case 'coming_soon': echo 'bg-yellow-100 text-yellow-800'; break;
                                    default: echo 'bg-gray-100 text-gray-800';
                                }
                                ?>">
                                <?= ucfirst(str_replace('_', ' ', $tool['status'])) ?>
                            </span>
                        </td>
                        <td class="px-6 py-4 text-sm font-medium">
                            <div class="flex space-x-2">
                                <?php
                                // 始终基于slug生成URL
                                if ($tool['category_slug']) {
                                    $viewUrl = '/tools/' . $tool['category_slug'] . '/' . $tool['slug'];
                                } else {
                                    $viewUrl = '/tools/' . $tool['slug'];
                                }
                                ?>
                                <a href="<?= htmlspecialchars($viewUrl) ?>"
                                   target="_blank"
                                   class="text-blue-600 hover:text-blue-900">
                                    View
                                </a>
                                
                                <button onclick="editTool(<?= htmlspecialchars(json_encode($tool)) ?>)"
                                        class="text-indigo-600 hover:text-indigo-900">
                                    Edit
                                </button>

                                <button onclick="editSourceFile('<?= htmlspecialchars($tool['slug']) ?>')"
                                        class="text-purple-600 hover:text-purple-900"
                                        title="Edit Source File">
                                    Source
                                </button>
                                
                                <form method="POST" class="inline">
                                    <input type="hidden" name="action" value="toggle_status">
                                    <input type="hidden" name="tool_id" value="<?= $tool['id'] ?>">
                                    <input type="hidden" name="current_status" value="<?= $tool['status'] ?>">
                                    <button type="submit" class="text-yellow-600 hover:text-yellow-900">
                                        <?= $tool['status'] === 'active' ? 'Deactivate' : 'Activate' ?>
                                    </button>
                                </form>
                                
                                <button onclick="deleteTool(<?= $tool['id'] ?>, '<?= htmlspecialchars($tool['name']) ?>')" 
                                        class="text-red-600 hover:text-red-900">
                                    Delete
                                </button>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>

        <!-- 分页导航 -->
        <?php if ($totalPages > 1): ?>
        <div class="px-6 py-4 border-t border-gray-200">
            <div class="flex items-center justify-between">
                <div class="text-sm text-gray-700">
                    显示 <?= ($offset + 1) ?> 到 <?= min($offset + $filters['per_page'], $totalTools) ?> 条，共 <?= $totalTools ?> 条记录
                </div>

                <div class="flex space-x-2">
                    <?php if ($filters['page'] > 1): ?>
                    <a href="?page=tools&p=<?= $filters['page'] - 1 ?>&category=<?= urlencode($filters['category']) ?>&status=<?= urlencode($filters['status']) ?>&search=<?= urlencode($filters['search']) ?>&sort=<?= urlencode($filters['sort']) ?>"
                       class="px-3 py-2 bg-gray-300 text-gray-700 hover:bg-gray-400">
                        上一页
                    </a>
                    <?php endif; ?>

                    <?php for ($i = max(1, $filters['page'] - 2); $i <= min($totalPages, $filters['page'] + 2); $i++): ?>
                    <a href="?page=tools&p=<?= $i ?>&category=<?= urlencode($filters['category']) ?>&status=<?= urlencode($filters['status']) ?>&search=<?= urlencode($filters['search']) ?>&sort=<?= urlencode($filters['sort']) ?>"
                       class="px-3 py-2 <?= $i === $filters['page'] ? 'bg-accent text-white' : 'bg-gray-300 text-gray-700 hover:bg-gray-400' ?>">
                        <?= $i ?>
                    </a>
                    <?php endfor; ?>

                    <?php if ($filters['page'] < $totalPages): ?>
                    <a href="?page=tools&p=<?= $filters['page'] + 1 ?>&category=<?= urlencode($filters['category']) ?>&status=<?= urlencode($filters['status']) ?>&search=<?= urlencode($filters['search']) ?>&sort=<?= urlencode($filters['sort']) ?>"
                       class="px-3 py-2 bg-gray-300 text-gray-700 hover:bg-gray-400">
                        下一页
                    </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>

<!-- 工具编辑模态框 -->
<div id="toolModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-10 mx-auto p-5 border w-4/5 max-w-4xl shadow-lg bg-white">
        <div class="mt-3">
            <h3 id="modalTitle" class="text-lg font-medium text-gray-900 mb-4">Add Tool</h3>

            <form id="toolForm" method="POST" enctype="multipart/form-data">
                <input type="hidden" name="action" id="formAction" value="create">
                <input type="hidden" name="tool_id" id="toolId">

                <!-- 文件上传区域 -->
                <div class="mb-6 p-4 border-2 border-dashed border-gray-300 rounded-lg bg-gray-50">
                    <div class="text-center">
                        <div class="mb-4">
                            <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                            </svg>
                        </div>
                        <div class="mb-4">
                            <label for="toolFile" class="cursor-pointer">
                                <span class="mt-2 block text-sm font-medium text-gray-900">
                                    Upload Tool File & AI Analyze
                                </span>
                                <span class="mt-1 block text-xs text-gray-500">
                                    Upload .php, .html, or .htm file for automatic analysis
                                </span>
                            </label>
                            <input type="file" id="toolFile" name="tool_file" accept=".php,.html,.htm" class="hidden" onchange="handleFileUpload(this)">
                        </div>
                        <div class="flex justify-center space-x-4">
                            <button type="button" id="chooseFileBtn" onclick="document.getElementById('toolFile').click()"
                                    class="bg-blue-600 text-white px-4 py-2 text-sm hover:bg-blue-700 transition-colors">
                                Choose File
                            </button>
                            <button type="button" onclick="clearFileUpload()"
                                    class="bg-gray-500 text-white px-4 py-2 text-sm hover:bg-gray-600 transition-colors">
                                Clear
                            </button>
                        </div>
                        <div id="fileUploadStatus" class="mt-3 text-sm hidden">
                            <div id="uploadProgress" class="w-full bg-gray-200 rounded-full h-2 mb-2 hidden">
                                <div id="progressBar" class="bg-blue-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                            </div>
                            <div id="uploadMessage" class="text-gray-600"></div>
                        </div>
                    </div>
                </div>

                <!-- 两列布局 -->
                <div class="grid grid-cols-2 gap-6">
                    <!-- 左列 -->
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Name *</label>
                            <div class="relative">
                                <input type="text" name="name" id="toolName" required
                                       class="w-full px-3 py-2 border border-gray-300 focus:outline-none focus:border-accent pr-10">
                                <button type="button" onclick="getAISuggestion('name')"
                                        class="absolute right-2 top-1/2 transform -translate-y-1/2 text-blue-500 hover:text-blue-700 transition-colors"
                                        title="AI Suggestions">
                                    <i class="fas fa-robot text-sm"></i>
                                </button>
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Slug *</label>
                            <div class="relative">
                                <input type="text" name="slug" id="toolSlug" required
                                       class="w-full px-3 py-2 border border-gray-300 focus:outline-none focus:border-accent pr-16"
                                       oninput="checkSlugAvailability(this.value)">
                                <div class="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center space-x-1">
                                    <button type="button" onclick="getAISuggestion('slug')"
                                            class="text-blue-500 hover:text-blue-700 transition-colors"
                                            title="AI Suggestions">
                                        <i class="fas fa-robot text-sm"></i>
                                    </button>
                                    <div id="slugStatus">
                                        <!-- Status icon will appear here -->
                                    </div>
                                </div>
                            </div>
                            <div id="slugMessage" class="text-xs mt-1 hidden">
                                <!-- Status message will appear here -->
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Category</label>
                            <div class="relative">
                                <select name="category_id" id="toolCategoryId"
                                        class="w-full px-3 py-2 border border-gray-300 focus:outline-none focus:border-accent pr-12">
                                    <option value="">Select Category</option>
                                    <?php foreach ($categories as $category): ?>
                                    <option value="<?= $category['id'] ?>"><?= htmlspecialchars($category['name']) ?></option>
                                    <?php endforeach; ?>
                                </select>
                                <button type="button" onclick="getAISuggestion('category')"
                                        class="absolute right-8 top-1/2 transform -translate-y-1/2 text-blue-500 hover:text-blue-700 transition-colors"
                                        title="AI Suggestions">
                                    <i class="fas fa-robot text-sm"></i>
                                </button>
                            </div>
                        </div>



                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Icon (Emoji)</label>
                            <div class="relative">
                                <input type="text" name="icon" id="toolIcon" placeholder="🔧"
                                       class="w-full px-3 py-2 border border-gray-300 focus:outline-none focus:border-accent pr-10">
                                <button type="button" onclick="getAISuggestion('icon')"
                                        class="absolute right-2 top-1/2 transform -translate-y-1/2 text-blue-500 hover:text-blue-700 transition-colors"
                                        title="AI Suggestions">
                                    <i class="fas fa-robot text-sm"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 右列 -->
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2 flex items-center justify-between">
                                Description
                                <button type="button" onclick="getAISuggestion('description')"
                                        class="text-blue-500 hover:text-blue-700 transition-colors text-sm"
                                        title="AI Suggestions">
                                    <i class="fas fa-robot mr-1"></i>AI Generate
                                </button>
                            </label>
                            <textarea name="description" id="toolDescription" rows="3"
                                      class="w-full px-3 py-2 border border-gray-300 focus:outline-none focus:border-accent"
                                      placeholder="Brief description of the tool"></textarea>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Tags (comma separated)</label>
                            <div class="relative">
                                <input type="text" name="tags" id="toolTags" placeholder="html, css, formatter"
                                       class="w-full px-3 py-2 border border-gray-300 focus:outline-none focus:border-accent pr-10">
                                <button type="button" onclick="getAISuggestion('tags')"
                                        class="absolute right-2 top-1/2 transform -translate-y-1/2 text-blue-500 hover:text-blue-700 transition-colors"
                                        title="AI Suggestions">
                                    <i class="fas fa-robot text-sm"></i>
                                </button>
                            </div>
                        </div>

                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Sort Order</label>
                                <input type="number" name="sort_order" id="toolSortOrder" value="1" min="1"
                                       class="w-full px-3 py-2 border border-gray-300 focus:outline-none focus:border-accent">
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                                <select name="status" id="toolStatus"
                                        class="w-full px-3 py-2 border border-gray-300 focus:outline-none focus:border-accent">
                                    <option value="active">Active</option>
                                    <option value="inactive">Inactive</option>
                                    <option value="coming_soon">Coming Soon</option>
                                </select>
                            </div>
                        </div>

                        <div>
                            <label class="flex items-center">
                                <input type="checkbox" name="is_featured" id="toolFeatured" class="mr-2">
                                <span class="text-sm font-medium text-gray-700">Featured Tool</span>
                            </label>
                        </div>
                    </div>
                </div>

                <div class="flex justify-end space-x-4 mt-6 pt-4 border-t">
                    <button type="button" onclick="closeToolModal()"
                            class="px-4 py-2 bg-gray-300 text-gray-700 hover:bg-gray-400">
                        Cancel
                    </button>
                    <button type="submit"
                            class="px-4 py-2 bg-accent text-white hover:bg-blue-700 hover:text-white">
                        Save Tool
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div id="deleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg bg-white">
        <div class="mt-3 text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 bg-red-100 mb-4">
                <svg class="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">Delete Tool</h3>
            <p class="text-sm text-gray-500 mb-4">
                Are you sure you want to delete "<span id="deleteToolName"></span>"? This action cannot be undone.
            </p>

            <form method="POST" class="inline">
                <input type="hidden" name="action" value="delete">
                <input type="hidden" name="tool_id" id="deleteToolId">

                <div class="flex justify-center space-x-4">
                    <button type="button" onclick="closeDeleteModal()"
                            class="px-4 py-2 bg-gray-300 text-gray-700 hover:bg-gray-400">
                        Cancel
                    </button>
                    <button type="submit"
                            class="px-4 py-2 bg-red-600 text-white hover:bg-red-700">
                        Delete Tool
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- AI建议模态框 -->
<div id="aiSuggestionsModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex justify-between items-center mb-4">
                <h3 id="aiModalTitle" class="text-lg font-medium text-gray-900 flex items-center">
                    <i class="fas fa-robot mr-2 text-blue-500"></i>
                    AI Suggestions
                </h3>
                <button onclick="closeAIModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <!-- 加载状态 -->
            <div id="aiLoading" class="text-center py-8">
                <div class="inline-flex items-center">
                    <div class="w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mr-3"></div>
                    <span class="text-gray-600">Generating AI suggestions...</span>
                </div>
            </div>

            <!-- 建议内容 -->
            <div id="aiSuggestions" class="hidden">
                <div id="aiSuggestionsContent"></div>

                <div class="flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-200">
                    <button onclick="retryAISuggestion()"
                            class="px-4 py-2 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 flex items-center">
                        <i class="fas fa-redo mr-2"></i>Retry
                    </button>
                    <button onclick="closeAIModal()"
                            class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">
                        Cancel
                    </button>
                </div>
            </div>

            <!-- 错误状态 -->
            <div id="aiError" class="hidden">
                <div class="bg-red-50 border border-red-200 rounded-md p-4">
                    <div class="flex">
                        <i class="fas fa-exclamation-triangle text-red-400 mr-3 mt-0.5"></i>
                        <div>
                            <h3 class="text-sm font-medium text-red-800">Error</h3>
                            <p id="aiErrorMessage" class="text-sm text-red-700 mt-1"></p>
                        </div>
                    </div>
                </div>
                <div class="flex justify-end space-x-3 mt-4">
                    <button onclick="retryAISuggestion()"
                            class="px-4 py-2 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 flex items-center">
                        <i class="fas fa-redo mr-2"></i>Retry
                    </button>
                    <button onclick="closeAIModal()"
                            class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">
                        Close
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>

// 显示通知消息
function showNotification(message, type = 'info') {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 px-6 py-4 rounded-lg shadow-lg z-50 ${
        type === 'success' ? 'bg-green-600 text-white' :
        type === 'error' ? 'bg-red-600 text-white' :
        type === 'warning' ? 'bg-yellow-600 text-white' :
        'bg-blue-600 text-white'
    }`;
    notification.textContent = message;

    // 添加到页面
    document.body.appendChild(notification);

    // 3秒后自动移除
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 3000);
}

// 清空缓存文件
function clearCacheFiles() {
    // 显示处理中的通知
    showNotification('Clearing cache files...', 'info');

    fetch('/api/clear-cache.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const count = data.deleted_count || 0;
            if (count > 0) {
                showNotification(`Successfully cleared ${count} cache files`, 'success');
            } else {
                showNotification('No cache files found to clear', 'info');
            }
        } else {
            showNotification('Error clearing cache: ' + (data.error || 'Unknown error'), 'error');
        }
    })
    .catch(error => {
        showNotification('Error clearing cache files', 'error');
    });
}

// 打开工具模态框
function openToolModal(tool = null) {
    const modal = document.getElementById('toolModal');
    const form = document.getElementById('toolForm');
    const title = document.getElementById('modalTitle');

    if (tool) {
        // 编辑模式
        title.textContent = 'Edit Tool';
        document.getElementById('formAction').value = 'update';
        document.getElementById('toolId').value = tool.id;
        document.getElementById('toolName').value = tool.name;
        document.getElementById('toolSlug').value = tool.slug;
        document.getElementById('toolDescription').value = tool.description || '';
        document.getElementById('toolCategoryId').value = tool.category_id || '';

        document.getElementById('toolIcon').value = tool.icon || '';
        document.getElementById('toolTags').value = tool.tags || '';
        document.getElementById('toolSortOrder').value = tool.sort_order || 1;
        document.getElementById('toolStatus').value = tool.status || 'active';
        document.getElementById('toolFeatured').checked = tool.is_featured == 1;

        // 触发slug检查
        setTimeout(() => {
            checkSlugAvailability(tool.slug);
        }, 100);
    } else {
        // 创建模式
        title.textContent = 'Add Tool';
        document.getElementById('formAction').value = 'create';
        form.reset();
        currentToolId = null; // 清除工具ID

        // 清除slug状态
        document.getElementById('slugStatus').innerHTML = '';
        document.getElementById('slugMessage').classList.add('hidden');
    }

    modal.classList.remove('hidden');
}

// 关闭模态框
function closeToolModal() {
    document.getElementById('toolModal').classList.add('hidden');

    // 清理slug检查状态
    currentToolId = null;
    if (slugCheckTimeout) {
        clearTimeout(slugCheckTimeout);
        slugCheckTimeout = null;
    }
    document.getElementById('slugStatus').innerHTML = '';
    document.getElementById('slugMessage').classList.add('hidden');
}

// 删除工具
function deleteTool(toolId, toolName) {
    document.getElementById('deleteToolId').value = toolId;
    document.getElementById('deleteToolName').textContent = toolName;
    document.getElementById('deleteModal').classList.remove('hidden');
}

function closeDeleteModal() {
    document.getElementById('deleteModal').classList.add('hidden');
}

// 编辑工具
function editTool(tool) {
    currentToolId = tool.id; // 设置当前工具ID用于slug检查
    openToolModal(tool);
}

// 编辑源文件
function editSourceFile(toolSlug) {
    // 强制清理所有缓存状态
    if (window.codeEditor && typeof window.codeEditor.toTextArea === 'function') {
        try {
            window.codeEditor.toTextArea();
            window.codeEditor = null;
        } catch (e) {
            // 忽略清理错误
        }
    }

    // 清理缓存数据
    if (window.lastLoadedContent) {
        delete window.lastLoadedContent;
    }
    if (window.currentToolSlug) {
        delete window.currentToolSlug;
    }

    // 确保之前的模态框完全关闭
    if (document.getElementById('sourceModal').classList.contains('hidden') === false) {
        closeSourceModal();
        window.forceCloseModal();
    }

    // 延迟确保清理完成，然后重新从服务器获取文件内容
    setTimeout(() => {
        // 加载源文件内容
        fetch(`/api/get-source.php?tool=${encodeURIComponent(toolSlug)}&t=${Date.now()}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    openSourceModal(toolSlug, data.content, data.exists);
                } else {
                    showNotification('Error loading source file: ' + (data.error || 'Unknown error'), 'error');
                }
            })
            .catch(error => {
                showNotification('Error loading source file: ' + error.message, 'error');
            });
    }, 100);
}

// CodeMirror实例
let codeEditor = null;
let previewVisible = true;

// 打开源文件编辑模态框
function openSourceModal(toolSlug, content, exists) {
    // 强制清理之前的状态 - 确保完全重置
    if (codeEditor) {
        try {
            codeEditor.toTextArea();
        } catch (e) {
            // 清理失败，继续执行
        }
        codeEditor = null;
    }

    // 清理全局变量
    window.codeEditor = null;
    if (window.lastLoadedContent) {
        delete window.lastLoadedContent;
    }
    if (window.currentToolSlug) {
        delete window.currentToolSlug;
    }

    // 清理编辑器容器
    const editorContainer = document.getElementById('codeEditor');
    if (editorContainer) {
        editorContainer.innerHTML = '';
    }

    // 确保模态框完全重置
    const modal = document.getElementById('sourceModal');
    modal.style.display = '';
    modal.classList.remove('hidden');

    // 设置表单数据 - 强制使用传入的内容
    document.getElementById('sourceToolSlug').value = toolSlug;
    document.getElementById('sourceContent').value = content || '';
    document.getElementById('sourceModalTitle').textContent = `Edit Source: ${toolSlug}.php`;
    document.getElementById('sourceFileStatus').textContent = exists ? 'Existing File' : 'New File';
    document.getElementById('sourceFileStatus').className = exists ? 'text-green-600' : 'text-orange-600';

    // 存储当前数据 - 使用服务器返回的真实内容
    window.currentToolSlug = toolSlug;
    window.lastLoadedContent = content || '';

    // 重置预览状态
    previewVisible = true;
    const previewPanel = document.getElementById('previewPanel');
    if (previewPanel) {
        previewPanel.style.display = 'flex';
    }

    // 重置预览iframe
    const iframe = document.getElementById('previewFrame');
    if (iframe) {
        iframe.src = 'about:blank';
        iframe.srcdoc = '';
    }

    // 延迟初始化CodeMirror，确保DOM完全准备好
    setTimeout(() => {
        // 再次确认使用正确的内容
        const actualContent = content || '';
        initCodeEditor(actualContent);
        updateSourceStats();

        // 初始预览
        setTimeout(() => {
            refreshPreview();
        }, 100);
    }, 100); // 增加延迟确保完全清理
}

// 初始化CodeMirror编辑器
function initCodeEditor(content) {
    // 强制销毁所有可能的CodeMirror实例
    if (window.codeEditor) {
        try {
            window.codeEditor.toTextArea();
        } catch (e) {
            // 销毁失败，继续执行
        }
        window.codeEditor = null;
    }

    if (codeEditor) {
        try {
            codeEditor.toTextArea();
        } catch (e) {
            // 销毁失败，继续执行
        }
        codeEditor = null;
    }

    // 完全清理编辑器容器
    const editorContainer = document.getElementById('codeEditor');
    if (editorContainer) {
        editorContainer.innerHTML = '';
        // 强制重置容器样式
        editorContainer.style.height = '';
        editorContainer.style.width = '';
    }

    // 确保使用传入的内容
    const actualContent = content || '';

    // 延迟创建，确保DOM完全清理
    setTimeout(() => {
        // 再次检查容器
        const container = document.getElementById('codeEditor');
        if (!container) {
            return;
        }

        // 创建全新的CodeMirror实例
        const newEditor = CodeMirror(container, {
            value: actualContent,
            mode: 'htmlmixed',
            theme: 'monokai',
            lineNumbers: true,
            autoCloseBrackets: true,
            matchBrackets: true,
            indentUnit: 4,
            indentWithTabs: false,
            lineWrapping: true,
            viewportMargin: Infinity,
            extraKeys: {
                'Ctrl-S': function() {
                    saveSourceFile();
                },
                'Tab': function(cm) {
                    cm.replaceSelection('    ');
                }
            }
        });

        // 设置全局变量
        window.codeEditor = newEditor;
        codeEditor = newEditor;

        // 设置编辑器尺寸
        setTimeout(() => {
            if (newEditor) {
                newEditor.setSize('100%', '100%');
                newEditor.refresh();

            }
        }, 50);

        // 监听内容变化
        newEditor.on('change', function() {
            updateSourceStats();
            clearTimeout(window.previewTimeout);
            window.previewTimeout = setTimeout(refreshPreview, 1000);
        });

    }, 100);
}

// 关闭源文件模态框
function closeSourceModal() {
    try {
        // 完全销毁CodeMirror实例
        if (codeEditor && typeof codeEditor.toTextArea === 'function') {
            codeEditor.toTextArea();
            codeEditor = null;
        }

        if (window.codeEditor && typeof window.codeEditor.toTextArea === 'function') {
            window.codeEditor.toTextArea();
            window.codeEditor = null;
        }

        // 清理编辑器容器
        const editorContainer = document.getElementById('codeEditor');
        if (editorContainer) {
            editorContainer.innerHTML = '';
        }

        // 重置全局变量
        previewVisible = true;

        // 完全清理表单数据
        document.getElementById('sourceToolSlug').value = '';
        document.getElementById('sourceContent').value = '';
        document.getElementById('sourceModalTitle').textContent = 'Edit Source File';
        document.getElementById('sourceFileStatus').textContent = 'File Status';

        // 重置统计信息
        document.getElementById('sourceLineCount').textContent = '0';
        document.getElementById('sourceCharCount').textContent = '0';

        // 重置预览iframe
        const iframe = document.getElementById('previewFrame');
        if (iframe) {
            iframe.src = 'about:blank';
            iframe.srcdoc = '';
        }

        // 强制隐藏模态框
        const modal = document.getElementById('sourceModal');
        if (modal) {
            modal.classList.add('hidden');
            modal.style.display = 'none';
        }

        // 清理预览超时
        if (window.previewTimeout) {
            clearTimeout(window.previewTimeout);
            window.previewTimeout = null;
        }

        // 清理缓存数据
        if (window.pendingTemplate) {
            delete window.pendingTemplate;
        }
        if (window.lastLoadedContent) {
            delete window.lastLoadedContent;
        }
        if (window.currentToolSlug) {
            delete window.currentToolSlug;
        }

    } catch (error) {
        // 即使出错也要尝试隐藏
        const modal = document.getElementById('sourceModal');
        if (modal) {
            modal.style.display = 'none';
        }
    }
}

// 强制关闭模态框（备用方法）
window.forceCloseModal = function() {
    // 强制隐藏模态框
    const modal = document.getElementById('sourceModal');
    if (modal) {
        modal.classList.add('hidden');
        modal.style.display = 'none';
    }

    // 完全销毁所有CodeMirror实例
    if (window.codeEditor) {
        try {
            window.codeEditor.toTextArea();
        } catch (e) {
            // 清理失败，继续执行
        }
        window.codeEditor = null;
    }

    if (typeof codeEditor !== 'undefined' && codeEditor) {
        try {
            codeEditor.toTextArea();
        } catch (e) {
            // 清理失败，继续执行
        }
        codeEditor = null;
    }

    // 完全清理编辑器容器
    const editorContainer = document.getElementById('codeEditor');
    if (editorContainer) {
        editorContainer.innerHTML = '';
        editorContainer.style.height = '';
        editorContainer.style.width = '';
    }

    // 重置所有相关变量和状态
    if (typeof previewVisible !== 'undefined') {
        previewVisible = true;
    }

    // 完全清理表单和状态
    const slugInput = document.getElementById('sourceToolSlug');
    const contentInput = document.getElementById('sourceContent');
    if (slugInput) slugInput.value = '';
    if (contentInput) contentInput.value = '';

    // 重置标题和状态
    const titleElement = document.getElementById('sourceModalTitle');
    const statusElement = document.getElementById('sourceFileStatus');
    if (titleElement) titleElement.textContent = 'Edit Source File';
    if (statusElement) statusElement.textContent = 'File Status';

    // 清理预览
    const iframe = document.getElementById('previewFrame');
    if (iframe) {
        iframe.src = 'about:blank';
        iframe.srcdoc = '';
    }

    // 清理所有缓存数据
    if (window.lastLoadedContent) {
        delete window.lastLoadedContent;
    }
    if (window.currentToolSlug) {
        delete window.currentToolSlug;
    }
    if (window.pendingTemplate) {
        delete window.pendingTemplate;
    }

    // 清理预览超时
    if (window.previewTimeout) {
        clearTimeout(window.previewTimeout);
        window.previewTimeout = null;
    }
};

// 切换预览面板
function togglePreview() {
    const previewPanel = document.getElementById('previewPanel');
    const editorContainer = previewPanel.previousElementSibling;

    if (previewVisible) {
        previewPanel.style.display = 'none';
        editorContainer.classList.remove('w-1/2');
        editorContainer.classList.add('w-full');
        previewVisible = false;
    } else {
        previewPanel.style.display = 'flex';
        editorContainer.classList.remove('w-full');
        editorContainer.classList.add('w-1/2');
        previewVisible = true;
        refreshPreview();
    }

    // 刷新CodeMirror布局
    if (codeEditor) {
        setTimeout(() => codeEditor.refresh(), 100);
    }
}

// 刷新预览
function refreshPreview() {
    if (!previewVisible) return;

    const content = codeEditor ? codeEditor.getValue() : '';
    const iframe = document.getElementById('previewFrame');

    if (!content.trim()) {
        iframe.srcdoc = '<div style="padding: 20px; text-align: center; color: #666; background: white; font-family: Arial;">No content to preview</div>';
        return;
    }

    // 检查是否包含PHP代码
    if (content.includes('<' + '?php') || content.includes('<' + '?=') || content.includes('include') || content.includes('require')) {
        iframe.srcdoc = '<div style="padding: 20px; background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; font-family: Arial; text-align: center;">' +
                       '<h3 style="margin: 0 0 10px 0; color: #856404;">⚠️ PHP Code Detected</h3>' +
                       '<p style="margin: 0;">This preview only supports pure HTML content.<br>' +
                       'PHP code cannot be previewed in this environment.</p>' +
                       '</div>';
        return;
    }

    // 检查是否是有效的HTML
    const trimmedContent = content.trim();
    if (!trimmedContent.toLowerCase().includes('<html') && !trimmedContent.toLowerCase().includes('<!doctype')) {
        // 如果不是完整的HTML文档，包装一下
        const wrappedContent = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Preview</title>
</head>
<body>
${content}
</body>
</html>`;
        iframe.srcdoc = wrappedContent;
    } else {
        // 直接显示HTML内容
        iframe.srcdoc = content;
    }
}

// 保存源文件
function saveSourceFile() {
    const toolSlug = document.getElementById('sourceToolSlug').value;
    const content = codeEditor ? codeEditor.getValue() : document.getElementById('sourceContent').value;

    fetch('/api/save-source.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            tool_slug: toolSlug,
            content: content
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Source file saved successfully!', 'success');
            closeSourceModal();
        } else {
            showNotification('Error saving source file: ' + data.error, 'error');
        }
    })
    .catch(error => {

        showNotification('Error saving source file', 'error');
    });
}

// 更新源文件统计
function updateSourceStats() {
    const content = codeEditor ? codeEditor.getValue() : document.getElementById('sourceContent').value;
    const lines = content.split('\n').length;
    const chars = content.length;

    document.getElementById('sourceLineCount').textContent = lines;
    document.getElementById('sourceCharCount').textContent = chars;
}

// 插入源文件模板
function insertSourceTemplate() {
    const toolSlug = document.getElementById('sourceToolSlug').value;
    const toolName = toolSlug.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());

    const template = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tool Template - Prompt2Tool</title>
    <script src="https://cdn.tailwindcss.com"><\/script>
</head>
<body class="bg-black text-white min-h-screen">
    <!-- Header -->
    <header class="bg-gray-900 border-b border-gray-800">
        <div class="max-w-6xl mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <div class="w-8 h-8 bg-blue-600 rounded flex items-center justify-center">
                        <span class="text-white font-bold">P2T</span>
                    </div>
                    <h1 class="text-xl font-bold">Your Tool Name</h1>
                </div>
                <div class="text-sm text-gray-400">
                    Free Online Tool
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-6xl mx-auto px-4 py-8">
        <!-- Tool Description -->
        <div class="text-center mb-8">
            <h2 class="text-2xl font-bold mb-4">Your Tool Name</h2>
            <p class="text-gray-400 max-w-2xl mx-auto">
                Replace this with your tool description. Explain what your tool does and how it helps users.
            </p>
        </div>

        <!-- Tool Interface -->
        <div class="bg-gray-900 border border-gray-800 p-6 mb-8">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Input Section -->
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">
                        📝 Input
                    </label>
                    <textarea
                        id="input"
                        class="w-full h-64 px-4 py-3 bg-black border border-gray-700 text-white focus:border-blue-500 focus:outline-none resize-none"
                        placeholder="Enter your content here..."
                    ></textarea>
                    <div class="flex justify-between items-center mt-2 text-xs text-gray-500">
                        <span id="inputStats">0 characters, 1 lines</span>
                        <button onclick="clearInput()" class="text-red-400 hover:text-red-300">Clear</button>
                    </div>
                </div>

                <!-- Output Section -->
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">
                        ✨ Output
                    </label>
                    <textarea
                        id="output"
                        class="w-full h-64 px-4 py-3 bg-black border border-gray-700 text-white focus:border-blue-500 focus:outline-none resize-none"
                        placeholder="Processed content will appear here..."
                        readonly
                    ></textarea>
                    <div class="flex justify-between items-center mt-2 text-xs text-gray-500">
                        <span id="outputStats">0 characters, 1 lines</span>
                        <button onclick="copyOutput()" class="text-green-400 hover:text-green-300">Copy</button>
                    </div>
                </div>
            </div>

            <!-- Process Button -->
            <div class="flex justify-center mt-6">
                <button
                    onclick="processInput()"
                    class="bg-blue-600 text-white px-8 py-3 hover:bg-blue-700 transition-colors font-medium"
                >
                    🚀 Process Content
                </button>
            </div>
        </div>

        <!-- Instructions -->
        <div class="bg-gray-900 border border-gray-800 p-6 mb-8">
            <h3 class="text-lg font-bold mb-4">📋 How to Use This Tool</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h4 class="font-medium mb-2">Step 1: Input</h4>
                    <p class="text-sm text-gray-400">Enter or paste your content in the input area on the left.</p>
                </div>
                <div>
                    <h4 class="font-medium mb-2">Step 2: Process</h4>
                    <p class="text-sm text-gray-400">Click the "Process Content" button to transform your input.</p>
                </div>
                <div>
                    <h4 class="font-medium mb-2">Step 3: Copy</h4>
                    <p class="text-sm text-gray-400">Copy the processed result from the output area.</p>
                </div>
                <div>
                    <h4 class="font-medium mb-2">Step 4: Use</h4>
                    <p class="text-sm text-gray-400">Use the processed content in your projects or applications.</p>
                </div>
            </div>
        </div>

        <!-- Features -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div class="bg-gray-900 border border-gray-800 p-4 text-center">
                <div class="text-2xl mb-2">⚡</div>
                <h3 class="font-medium mb-2">Fast Processing</h3>
                <p class="text-sm text-gray-400">Instant results with optimized algorithms</p>
            </div>
            <div class="bg-gray-900 border border-gray-800 p-4 text-center">
                <div class="text-2xl mb-2">🔒</div>
                <h3 class="font-medium mb-2">Secure & Private</h3>
                <p class="text-sm text-gray-400">All processing happens locally in your browser</p>
            </div>
            <div class="bg-gray-900 border border-gray-800 p-4 text-center">
                <div class="text-2xl mb-2">📱</div>
                <h3 class="font-medium mb-2">Mobile Friendly</h3>
                <p class="text-sm text-gray-400">Works perfectly on all devices</p>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-900 border-t border-gray-800 mt-auto">
        <div class="max-w-6xl mx-auto px-4 py-6 text-center">
            <p class="text-gray-400 text-sm">
                Powered by <strong>Prompt2Tool</strong> - Free Online Tools Platform
            </p>
        </div>
    </footer>

    <script>
    // Update input/output statistics
    function updateStats() {
        const input = document.getElementById('input');
        const output = document.getElementById('output');

        const inputText = input.value;
        const outputText = output.value;

        document.getElementById('inputStats').textContent =
            inputText.length + ' characters, ' + (inputText.split('\\n').length) + ' lines';

        document.getElementById('outputStats').textContent =
            outputText.length + ' characters, ' + (outputText.split('\\n').length) + ' lines';
    }

    // Process input content
    function processInput() {
        const input = document.getElementById('input').value;
        const output = document.getElementById('output');

        if (!input.trim()) {
            showMessage('Please enter some content to process.', 'warning');
            return;
        }

        // Example processing: Convert to uppercase and add line numbers
        // Replace this with your actual processing logic
        const lines = input.split('\\n');
        const processed = lines.map((line, index) => {
            return (index + 1) + '. ' + line.toUpperCase();
        }).join('\\n');

        output.value = processed;
        updateStats();
        showMessage('Content processed successfully!', 'success');
    }

    // Copy output to clipboard
    function copyOutput() {
        const output = document.getElementById('output');

        if (!output.value.trim()) {
            showMessage('No content to copy.', 'warning');
            return;
        }

        // 使用现代的Clipboard API，如果不支持则回退到execCommand
        if (navigator.clipboard && window.isSecureContext) {
            navigator.clipboard.writeText(output.value).then(() => {
                showMessage('Content copied to clipboard!', 'success');
            }).catch(() => {
                // 回退到传统方法
                output.select();
                try {
                    document.execCommand('copy');
                    showMessage('Content copied to clipboard!', 'success');
                } catch (err) {
                    showMessage('Copy failed. Please copy manually.', 'error');
                }
            });
        } else {
            // 回退到传统方法
            output.select();
            try {
                document.execCommand('copy');
                showMessage('Content copied to clipboard!', 'success');
            } catch (err) {
                showMessage('Copy failed. Please copy manually.', 'error');
            }
        }
    }

    // Clear input
    function clearInput() {
        document.getElementById('input').value = '';
        document.getElementById('output').value = '';
        updateStats();
        showMessage('Content cleared.', 'info');
    }

    // Show notification message
    function showMessage(message, type = 'info') {
        const colors = {
            success: 'bg-green-600',
            warning: 'bg-yellow-600',
            error: 'bg-red-600',
            info: 'bg-blue-600'
        };

        const notification = document.createElement('div');
        notification.className = 'fixed top-4 right-4 ' + colors[type] + ' text-white px-4 py-2 rounded shadow-lg z-50';
        notification.textContent = message;

        document.body.appendChild(notification);

        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 3000);
    }

    // Initialize
    document.getElementById('input').addEventListener('input', updateStats);
    document.getElementById('output').addEventListener('input', updateStats);
    updateStats();
    <\/script>
</body>
</html>`;

    const currentContent = codeEditor ? codeEditor.getValue() : '';
    if (currentContent.trim() === '') {
        // 编辑器为空，直接插入模板
        if (codeEditor) {
            codeEditor.setValue(template);
        } else {
            document.getElementById('sourceContent').value = template;
        }
        updateSourceStats();
        refreshPreview();
        showNotification('Template inserted successfully!', 'success');
    } else {
        // 编辑器有内容，显示替换选项
        showReplaceConfirmation(template);
    }
}

// 显示替换确认
function showReplaceConfirmation(template) {
    // 存储模板到全局变量
    window.pendingTemplate = template;

    // 创建确认通知
    const notification = document.createElement('div');
    notification.className = 'fixed top-4 right-4 bg-yellow-600 text-white px-6 py-4 rounded-lg shadow-lg z-50 max-w-sm';
    notification.innerHTML = `
        <div class="mb-3">
            <strong>Replace Content?</strong>
            <p class="text-sm mt-1">Editor has content. Replace with template?</p>
        </div>
        <div class="flex space-x-2">
            <button onclick="confirmReplace(this.closest('div').parentElement)"
                    class="bg-white text-yellow-600 px-3 py-1 text-sm hover:bg-gray-100">
                Replace
            </button>
            <button onclick="this.closest('div').parentElement.remove()"
                    class="bg-yellow-700 text-white px-3 py-1 text-sm hover:bg-yellow-800">
                Cancel
            </button>
        </div>
    `;

    document.body.appendChild(notification);

    // 10秒后自动移除
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 10000);
}

// 确认替换
function confirmReplace(notification) {
    const template = window.pendingTemplate;
    if (codeEditor) {
        codeEditor.setValue(template);
    } else {
        document.getElementById('sourceContent').value = template;
    }
    updateSourceStats();
    refreshPreview();
    showNotification('Template inserted successfully!', 'success');
    notification.remove();
    delete window.pendingTemplate;
}

// Slug检测相关变量
let slugCheckTimeout = null;
let currentToolId = null;

// 检查slug可用性
function checkSlugAvailability(slug) {
    const statusIcon = document.getElementById('slugStatus');
    const statusMessage = document.getElementById('slugMessage');

    // 清除之前的超时
    if (slugCheckTimeout) {
        clearTimeout(slugCheckTimeout);
    }

    // 如果slug为空，清除状态
    if (!slug.trim()) {
        statusIcon.innerHTML = '';
        statusMessage.classList.add('hidden');
        return;
    }

    // 显示检查中状态
    statusIcon.innerHTML = '<div class="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>';
    statusMessage.textContent = 'Checking availability...';
    statusMessage.className = 'text-xs mt-1 text-blue-600';

    // 延迟检查，避免频繁请求
    slugCheckTimeout = setTimeout(() => {
        const excludeId = currentToolId || '';

        fetch(`/api/check-slug.php?slug=${encodeURIComponent(slug)}&exclude_id=${excludeId}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.text().then(text => {
                    try {
                        return JSON.parse(text);
                    } catch (e) {
                        throw new Error('Invalid response from server');
                    }
                });
            })
            .then(data => {
                if (data.success) {
                    if (data.available) {
                        // 可用 - 绿色
                        statusIcon.innerHTML = '<svg class="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg>';
                        statusMessage.textContent = data.message;
                        statusMessage.className = 'text-xs mt-1 text-green-600';
                    } else {
                        // 不可用 - 红色
                        statusIcon.innerHTML = '<svg class="w-4 h-4 text-red-500" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>';
                        statusMessage.textContent = data.message;
                        statusMessage.className = 'text-xs mt-1 text-red-600';
                    }
                } else {
                    // 错误状态
                    statusIcon.innerHTML = '<svg class="w-4 h-4 text-yellow-500" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg>';
                    statusMessage.textContent = 'Error checking slug';
                    statusMessage.className = 'text-xs mt-1 text-yellow-600';
                }
            })
            .catch(error => {
                statusIcon.innerHTML = '<svg class="w-4 h-4 text-yellow-500" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg>';
                statusMessage.textContent = 'Network error';
                statusMessage.className = 'text-xs mt-1 text-yellow-600';
            });
    }, 500); // 500ms延迟
}

// 自动生成slug
document.addEventListener('DOMContentLoaded', function() {
    // 检查是否有自动上传参数
    const urlParams = new URLSearchParams(window.location.search);
    const autoUploadFile = urlParams.get('auto_upload');

    if (autoUploadFile) {
        // 自动打开Add Tool模态框
        document.getElementById('addToolModal').classList.remove('hidden');

        // 自动设置文件路径并触发AI分析
        setTimeout(() => {
            autoSelectGeneratedFile(autoUploadFile);
        }, 500);
    }

    const nameInput = document.getElementById('toolName');
    if (nameInput) {
        nameInput.addEventListener('input', function() {
            const name = this.value;
            const slug = name.toLowerCase()
                .replace(/[^a-z0-9\s-]/g, '')
                .replace(/\s+/g, '-')
                .replace(/-+/g, '-')
                .trim('-');

            const slugInput = document.getElementById('toolSlug');
            slugInput.value = slug;

            // 触发slug检查
            checkSlugAvailability(slug);
        });
    }
});

// 自动选择生成的文件并触发AI分析
function autoSelectGeneratedFile(filePath) {
    // 显示文件路径信息
    const fileInfo = document.querySelector('.file-upload-info');
    if (fileInfo) {
        fileInfo.innerHTML = `
            <div class="flex items-center space-x-2 text-green-600">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <span>Generated file: ${filePath}</span>
            </div>
        `;
    }

    // 设置隐藏字段存储文件路径
    let hiddenInput = document.getElementById('generated_file_path');
    if (!hiddenInput) {
        hiddenInput = document.createElement('input');
        hiddenInput.type = 'hidden';
        hiddenInput.id = 'generated_file_path';
        hiddenInput.name = 'generated_file_path';
        document.getElementById('addToolForm').appendChild(hiddenInput);
    }
    hiddenInput.value = filePath;

    // 自动触发AI分析
    setTimeout(() => {
        analyzeGeneratedFile(filePath);
    }, 1000);
}

// 分析生成的文件
function analyzeGeneratedFile(filePath) {
    // 显示分析状态
    const statusDiv = document.getElementById('fileUploadStatus');
    const progressDiv = document.getElementById('uploadProgress');
    const progressBar = document.getElementById('progressBar');
    const messageDiv = document.getElementById('uploadMessage');

    if (statusDiv) {
        statusDiv.classList.remove('hidden');
        progressDiv.classList.remove('hidden');
        messageDiv.textContent = 'Analyzing generated file...';
        progressBar.style.width = '50%';
    }

    // 发送文件路径到AI分析API
    fetch('analyze-tool-file-final.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            generated_file_path: filePath
        })
    })
    .then(response => {
        return response.text();
    })
    .then(text => {
        try {
            const data = JSON.parse(text);
            return data;
        } catch (e) {
            throw new Error('Server returned invalid JSON: ' + text.substring(0, 100));
        }
    })
    .then(data => {
        if (progressBar) progressBar.style.width = '100%';

        if (data.success) {
            if (messageDiv) {
                messageDiv.innerHTML = `
                    <span class="text-green-600">✅ Analysis complete!</span>
                    <span class="text-xs text-gray-500 ml-2">Confidence: ${Math.round((data.data.confidence || 0.9) * 100)}%</span>
                `;
            }

            // 自动填充表单
            fillFormFromAI(data.data);
        } else {
            if (messageDiv) {
                messageDiv.innerHTML = `<span class="text-red-600">❌ Analysis failed: ${data.message}</span>`;
            }
        }
    })
    .catch(error => {
        if (messageDiv) {
            messageDiv.innerHTML = `<span class="text-red-600">❌ Analysis failed: ${error.message}</span>`;
        }
    });
}

// 点击模态框外部关闭
document.getElementById('toolModal')?.addEventListener('click', function(e) {
    if (e.target === this) closeToolModal();
});

document.getElementById('deleteModal')?.addEventListener('click', function(e) {
    if (e.target === this) closeDeleteModal();
});

document.getElementById('sourceModal')?.addEventListener('click', function(e) {
    if (e.target === this) closeSourceModal();
});

// 监听源文件内容变化
document.getElementById('sourceContent')?.addEventListener('input', updateSourceStats);

// 源文件编辑器键盘快捷键
document.getElementById('sourceContent')?.addEventListener('keydown', function(e) {
    // Ctrl+S 保存
    if (e.ctrlKey && e.key === 's') {
        e.preventDefault();
        saveSourceFile();
    }

    // Tab键插入4个空格
    if (e.key === 'Tab') {
        e.preventDefault();
        const start = this.selectionStart;
        const end = this.selectionEnd;
        this.value = this.value.substring(0, start) + '    ' + this.value.substring(end);
        this.selectionStart = this.selectionEnd = start + 4;
    }
});
</script>

<!-- 源文件编辑模态框 -->
<div id="sourceModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-5 mx-auto p-5 border w-11/12 max-w-7xl shadow-lg bg-white" style="height: 90vh;">
        <div class="h-full flex flex-col">
            <!-- 头部 -->
            <div class="flex justify-between items-center mb-4">
                <div>
                    <h3 id="sourceModalTitle" class="text-lg font-medium text-gray-900">Edit Source File</h3>
                    <p class="text-sm text-gray-600">
                        <span id="sourceFileStatus">File Status</span> |
                        Lines: <span id="sourceLineCount">0</span> |
                        Characters: <span id="sourceCharCount">0</span>
                    </p>
                </div>
                <div class="flex space-x-2">
                    <button onclick="insertSourceTemplate()"
                            class="px-3 py-1 bg-blue-600 text-white text-sm hover:bg-blue-700">
                        Insert Template
                    </button>
                    <button onclick="togglePreview()"
                            class="px-3 py-1 bg-green-600 text-white text-sm hover:bg-green-700">
                        Toggle Preview
                    </button>
                    <button type="button" onclick="closeSourceModal(); window.forceCloseModal();"
                            class="px-3 py-1 bg-gray-300 text-gray-700 text-sm hover:bg-gray-400">
                        Close
                    </button>
                </div>
            </div>

            <!-- 分屏内容区域 -->
            <div class="flex-1 flex gap-4" style="height: 600px;">
                <!-- 左侧编辑器 -->
                <div class="w-1/2 flex flex-col h-full">
                    <div class="bg-gray-100 px-3 py-2 text-sm font-medium text-gray-700 border-b">
                        Code Editor
                    </div>
                    <div class="flex-1 border border-gray-300 overflow-hidden">
                        <textarea id="sourceContent" class="hidden"></textarea>
                        <div id="codeEditor" class="w-full h-full"></div>
                    </div>
                </div>

                <!-- 右侧预览 -->
                <div id="previewPanel" class="w-1/2 flex flex-col h-full">
                    <div class="bg-gray-100 px-3 py-2 text-sm font-medium text-gray-700 border-b flex justify-between items-center">
                        <span>Live Preview</span>
                        <button onclick="refreshPreview()" class="text-xs bg-blue-500 text-white px-2 py-1 hover:bg-blue-600">
                            Refresh
                        </button>
                    </div>
                    <div class="flex-1 border border-gray-300 bg-white overflow-auto">
                        <iframe id="previewFrame" class="w-full h-full border-0" src="about:blank"></iframe>
                    </div>
                </div>
            </div>

            <!-- 底部按钮 -->
            <div class="flex justify-end space-x-4 mt-4 pt-4 border-t">
                <button type="button" onclick="event.preventDefault(); closeSourceModal(); window.forceCloseModal();"
                        class="px-4 py-2 bg-gray-300 text-gray-700 hover:bg-gray-400">
                    Cancel
                </button>
                <button onclick="saveSourceFile()"
                        class="px-4 py-2 bg-accent text-white hover:bg-blue-700">
                    Save Source File
                </button>
            </div>

            <input type="hidden" id="sourceToolSlug">
        </div>
    </div>
</div>

<script>
// AI建议功能
let currentAIType = '';

// 获取AI建议
async function getAISuggestion(type) {
    currentAIType = type;

    // 显示模态框
    document.getElementById('aiSuggestionsModal').classList.remove('hidden');
    document.getElementById('aiLoading').classList.remove('hidden');
    document.getElementById('aiSuggestions').classList.add('hidden');
    document.getElementById('aiError').classList.add('hidden');

    // 更新标题
    const titles = {
        'name': 'Name Suggestions',
        'slug': 'Slug Suggestions',
        'category': 'Category Recommendation',
        'description': 'Description Suggestions',
        'tags': 'Tags Suggestions',
        'icon': 'Icon Suggestions'
    };
    document.getElementById('aiModalTitle').innerHTML = `<i class="fas fa-robot mr-2 text-blue-500"></i>${titles[type]}`;

    try {
        // 收集当前表单数据
        const data = collectFormData(type);

        // 调用AI API
        const response = await fetch('/api/ai-suggestions.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                type: type,
                data: data
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();

        if (result.success) {
            displayAISuggestions(type, result.suggestions);
        } else {
            throw new Error(result.error || 'Failed to generate suggestions');
        }

    } catch (error) {
        showAIError(error.message);
    } finally {
        document.getElementById('aiLoading').classList.add('hidden');
    }
}

// 收集表单数据
function collectFormData(type) {
    const toolName = document.getElementById('toolName').value;
    const description = document.getElementById('toolDescription').value;
    const categorySelect = document.getElementById('toolCategoryId');
    const selectedCategory = categorySelect.options[categorySelect.selectedIndex]?.text || '';

    const data = {
        tool_name: toolName,
        description: description,
        category: selectedCategory
    };

    // 根据类型添加特定数据
    switch (type) {
        case 'slug':
            // 获取现有slugs用于避免重复
            data.existing_slugs = []; // 这里可以从页面获取现有slugs
            break;
        case 'category':
            // 获取所有可用分类
            const categories = [];
            const options = document.getElementById('toolCategoryId').options;
            for (let i = 1; i < options.length; i++) {
                categories.push({
                    id: options[i].value,
                    name: options[i].text
                });
            }
            data.categories = categories;
            break;
        case 'name':
            data.current_name = toolName;
            break;
        case 'tags':
            data.current_tags = document.getElementById('toolTags').value;
            break;
    }

    return data;
}

// 显示AI建议
function displayAISuggestions(type, suggestions) {
    const content = document.getElementById('aiSuggestionsContent');

    switch (type) {
        case 'slug':
            if (Array.isArray(suggestions)) {
                content.innerHTML = `
                    <div class="space-y-3">
                        <p class="text-sm text-gray-600 mb-4">Choose a slug from the AI suggestions:</p>
                        ${suggestions.map((slug, index) => `
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer"
                                 onclick="selectSuggestion('slug', '${slug}')">
                                <div class="flex items-center space-x-3">
                                    <span class="inline-flex items-center justify-center w-6 h-6 bg-blue-100 text-blue-600 text-xs font-medium rounded-full">
                                        ${index + 1}
                                    </span>
                                    <span class="font-mono text-gray-900">${slug}</span>
                                </div>
                                <button class="text-blue-600 hover:text-blue-800">
                                    <i class="fas fa-check"></i>
                                </button>
                            </div>
                        `).join('')}
                    </div>
                `;
            }
            break;

        case 'category':
            if (suggestions.category) {
                content.innerHTML = `
                    <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <h4 class="font-medium text-green-800">Recommended Category</h4>
                                <p class="text-green-700 mt-1">${suggestions.category}</p>
                                <p class="text-sm text-green-600 mt-2">${suggestions.reason || ''}</p>
                                <p class="text-xs text-green-500 mt-1">Confidence: ${Math.round((suggestions.confidence || 0.9) * 100)}%</p>
                            </div>
                            <button onclick="selectSuggestion('category', '${suggestions.category}')"
                                    class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700">
                                Apply
                            </button>
                        </div>
                    </div>
                `;
            }
            break;

        case 'description':
            if (suggestions.concise || suggestions.detailed || suggestions.marketing) {
                content.innerHTML = `
                    <div class="space-y-4">
                        <p class="text-sm text-gray-600 mb-4">Choose a description style:</p>
                        ${suggestions.concise ? `
                            <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 cursor-pointer"
                                 onclick="selectSuggestion('description', \`${suggestions.concise.replace(/`/g, '\\`')}\`)" >
                                <h4 class="font-medium text-gray-800 mb-2">Concise</h4>
                                <p class="text-gray-600">${suggestions.concise}</p>
                            </div>
                        ` : ''}
                        ${suggestions.detailed ? `
                            <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 cursor-pointer"
                                 onclick="selectSuggestion('description', \`${suggestions.detailed.replace(/`/g, '\\`')}\`)" >
                                <h4 class="font-medium text-gray-800 mb-2">Detailed</h4>
                                <p class="text-gray-600">${suggestions.detailed}</p>
                            </div>
                        ` : ''}
                        ${suggestions.marketing ? `
                            <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 cursor-pointer"
                                 onclick="selectSuggestion('description', \`${suggestions.marketing.replace(/`/g, '\\`')}\`)" >
                                <h4 class="font-medium text-gray-800 mb-2">Marketing</h4>
                                <p class="text-gray-600">${suggestions.marketing}</p>
                            </div>
                        ` : ''}
                    </div>
                `;
            }
            break;

        case 'tags':
            if (Array.isArray(suggestions)) {
                content.innerHTML = `
                    <div>
                        <p class="text-sm text-gray-600 mb-4">Suggested tags:</p>
                        <div class="flex flex-wrap gap-2 mb-4">
                            ${suggestions.map(tag => `
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800 cursor-pointer hover:bg-blue-200"
                                      onclick="toggleTag(this)" data-tag="${tag.replace(/"/g, '&quot;')}">
                                    ${tag}
                                    <i class="fas fa-plus ml-2 text-xs"></i>
                                </span>
                            `).join('')}
                        </div>
                        <button onclick="applyAllTags()"
                                class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                                data-tags='${JSON.stringify(suggestions)}'>
                            Apply All Tags
                        </button>
                    </div>
                `;
            }
            break;

        case 'name':
            if (Array.isArray(suggestions)) {
                content.innerHTML = `
                    <div class="space-y-3">
                        <p class="text-sm text-gray-600 mb-4">Suggested names:</p>
                        ${suggestions.map((name, index) => `
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer"
                                 onclick="selectSuggestion('name', '${name}')">
                                <div class="flex items-center space-x-3">
                                    <span class="inline-flex items-center justify-center w-6 h-6 bg-purple-100 text-purple-600 text-xs font-medium rounded-full">
                                        ${index + 1}
                                    </span>
                                    <span class="font-medium text-gray-900">${name}</span>
                                </div>
                                <button class="text-purple-600 hover:text-purple-800">
                                    <i class="fas fa-check"></i>
                                </button>
                            </div>
                        `).join('')}
                    </div>
                `;
            }
            break;

        case 'icon':
            if (Array.isArray(suggestions)) {
                content.innerHTML = `
                    <div class="space-y-3">
                        <p class="text-sm text-gray-600 mb-4">Suggested icons:</p>
                        <div class="grid grid-cols-3 gap-4">
                            ${suggestions.map(icon => `
                                <div class="flex flex-col items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 cursor-pointer"
                                     onclick="selectSuggestion('icon', '${icon}')">
                                    <span class="text-3xl mb-2">${icon}</span>
                                    <button class="text-sm text-blue-600 hover:text-blue-800">
                                        Select
                                    </button>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                `;
            }
            break;
    }

    document.getElementById('aiSuggestions').classList.remove('hidden');
}

// 选择建议
function selectSuggestion(type, value) {
    switch (type) {
        case 'name':
            document.getElementById('toolName').value = value;
            // 自动生成slug
            const slug = value.toLowerCase()
                .replace(/[^a-z0-9\s-]/g, '')
                .replace(/\s+/g, '-')
                .replace(/-+/g, '-')
                .trim('-');
            document.getElementById('toolSlug').value = slug;
            checkSlugAvailability(slug);
            break;
        case 'slug':
            document.getElementById('toolSlug').value = value;
            checkSlugAvailability(value);
            break;
        case 'category':
            const categorySelect = document.getElementById('toolCategoryId');
            for (let option of categorySelect.options) {
                if (option.text === value) {
                    categorySelect.value = option.value;
                    break;
                }
            }
            break;
        case 'description':
            document.getElementById('toolDescription').value = value;
            break;
        case 'icon':
            document.getElementById('toolIcon').value = value;
            break;
    }

    closeAIModal();
}

// 切换标签
function toggleTag(element) {
    const tag = element.getAttribute('data-tag');
    const tagsInput = document.getElementById('toolTags');
    const currentTags = tagsInput.value.split(',').map(t => t.trim()).filter(t => t);

    if (currentTags.includes(tag)) {
        // 移除标签
        const newTags = currentTags.filter(t => t !== tag);
        tagsInput.value = newTags.join(', ');
        element.querySelector('i').className = 'fas fa-plus ml-2 text-xs';
    } else {
        // 添加标签
        currentTags.push(tag);
        tagsInput.value = currentTags.join(', ');
        element.querySelector('i').className = 'fas fa-check ml-2 text-xs';
    }
}

// 应用所有标签
function applyAllTags() {
    // 从事件源获取标签数据
    const button = event.target;
    const tagsData = button.getAttribute('data-tags');

    try {
        const tags = JSON.parse(tagsData);
        document.getElementById('toolTags').value = tags.join(', ');
        closeAIModal();
    } catch (e) {
        showNotification('Error applying tags', 'error');
    }
}

// 显示AI错误
function showAIError(message) {
    document.getElementById('aiErrorMessage').textContent = message;
    document.getElementById('aiError').classList.remove('hidden');
}

// 重试AI建议
function retryAISuggestion() {
    if (currentAIType) {
        // 重新调用当前类型的AI建议
        getAISuggestion(currentAIType);
    }
}

// 关闭AI模态框
function closeAIModal() {
    document.getElementById('aiSuggestionsModal').classList.add('hidden');
}

// 文件上传处理函数
function handleFileUpload(input) {
    const file = input.files[0];
    if (!file) {
        // 重置按钮文本
        document.getElementById('chooseFileBtn').textContent = 'Choose File';
        return;
    }

    // 验证文件类型
    const allowedTypes = ['php', 'html', 'htm'];
    const fileExtension = file.name.split('.').pop().toLowerCase();

    if (!allowedTypes.includes(fileExtension)) {
        showNotification('Invalid file type. Only PHP, HTML, and HTM files are allowed.', 'error');
        input.value = '';
        document.getElementById('chooseFileBtn').textContent = 'Choose File';
        return;
    }

    // 验证文件大小 (2MB)
    const maxSize = 2 * 1024 * 1024;
    if (file.size > maxSize) {
        showNotification('File too large. Maximum size is 2MB.', 'error');
        input.value = '';
        document.getElementById('chooseFileBtn').textContent = 'Choose File';
        return;
    }

    // 更新按钮文本显示选中的文件名
    const fileName = file.name.length > 20 ? file.name.substring(0, 17) + '...' : file.name;
    document.getElementById('chooseFileBtn').textContent = fileName;

    // 显示上传状态
    const statusDiv = document.getElementById('fileUploadStatus');
    const progressDiv = document.getElementById('uploadProgress');
    const progressBar = document.getElementById('progressBar');
    const messageDiv = document.getElementById('uploadMessage');

    statusDiv.classList.remove('hidden');
    progressDiv.classList.remove('hidden');
    messageDiv.textContent = `Uploading ${file.name}...`;

    // 创建FormData
    const formData = new FormData();
    formData.append('file', file);

    // 发送文件到AI分析API
    fetch('analyze-tool-file-final.php', {
        method: 'POST',
        body: formData
    })
    .then(response => {
        return response.text(); // 先获取文本内容
    })
    .then(text => {
        try {
            const data = JSON.parse(text); // 尝试解析JSON
            return data;
        } catch (e) {
            throw new Error('Server returned invalid response');
        }
    })
    .then(data => {
        progressBar.style.width = '100%';

        if (data.success) {
            messageDiv.innerHTML = `
                <span class="text-green-600">✅ Analysis complete!</span>
                <span class="text-xs text-gray-500 ml-2">Confidence: ${Math.round((data.data.confidence || 0.8) * 100)}%</span>
            `;

            // 自动填充表单
            fillFormFromAI(data.data);

            setTimeout(() => {
                progressDiv.classList.add('hidden');
                statusDiv.classList.add('hidden');
            }, 2000);

        } else {
            messageDiv.innerHTML = `<span class="text-red-600">❌ ${data.error}</span>`;
            setTimeout(() => {
                statusDiv.classList.add('hidden');
            }, 3000);
        }
    })
    .catch(error => {
        messageDiv.innerHTML = `<span class="text-red-600">❌ Upload failed</span>`;
        setTimeout(() => {
            statusDiv.classList.add('hidden');
        }, 3000);
    });

    // 模拟进度
    let progress = 0;
    const progressInterval = setInterval(() => {
        progress += Math.random() * 30;
        if (progress > 90) progress = 90;
        progressBar.style.width = progress + '%';

        if (progress >= 90) {
            clearInterval(progressInterval);
        }
    }, 200);
}

// 清除文件上传
function clearFileUpload() {
    document.getElementById('toolFile').value = '';
    document.getElementById('fileUploadStatus').classList.add('hidden');
    document.getElementById('uploadProgress').classList.add('hidden');
    document.getElementById('progressBar').style.width = '0%';

    // 重置按钮文本
    document.getElementById('chooseFileBtn').textContent = 'Choose File';

    // 清除表单数据
    document.getElementById('toolName').value = '';
    document.getElementById('toolSlug').value = '';
    document.getElementById('toolDescription').value = '';
    document.getElementById('toolTags').value = '';
    document.getElementById('toolIcon').value = '🔧';
    document.getElementById('toolCategory').value = '';
}

// 从AI分析结果填充表单
function fillFormFromAI(data) {
    if (data.name) {
        document.getElementById('toolName').value = data.name;
    }

    if (data.slug) {
        document.getElementById('toolSlug').value = data.slug;
        // 检查slug可用性
        checkSlugAvailability(data.slug);
    }

    if (data.description) {
        document.getElementById('toolDescription').value = data.description;
    }

    if (data.icon) {
        document.getElementById('toolIcon').value = data.icon;
    }

    if (data.tags && Array.isArray(data.tags)) {
        document.getElementById('toolTags').value = data.tags.join(', ');
    }

    // 尝试匹配分类
    if (data.category) {
        const categorySelect = document.getElementById('toolCategoryId');
        for (let option of categorySelect.options) {
            if (option.text.toLowerCase().includes(data.category.toLowerCase()) ||
                option.value.toLowerCase().includes(data.category.toLowerCase())) {
                categorySelect.value = option.value;
                break;
            }
        }
    }

    // 显示成功消息
    const confidence = Math.round((data.confidence || 0.8) * 100);
    const fallbackText = data.fallback ? ' (Fallback analysis)' : '';
    showNotification(`Form auto-filled with ${confidence}% confidence${fallbackText}`, 'success');
}
</script>
