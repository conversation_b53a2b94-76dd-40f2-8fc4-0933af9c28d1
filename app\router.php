<?php
/**
 * 简单的路由处理器
 * 处理URL路由和页面分发
 */

// 防止直接访问
if (!defined('ROOT_PATH')) {
    exit('Direct access not allowed');
}

/**
 * 简单路由类
 */
class SimpleRouter {
    private $routes = [];
    private $currentUri;
    private $currentMethod;
    
    public function __construct() {
        $this->currentUri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
        $this->currentMethod = $_SERVER['REQUEST_METHOD'];
        
        // 移除查询参数
        $this->currentUri = strtok($this->currentUri, '?');
        
        // 确保以/开头
        if (!str_starts_with($this->currentUri, '/')) {
            $this->currentUri = '/' . $this->currentUri;
        }
        
        // 移除末尾的/（除了根路径）
        if ($this->currentUri !== '/' && str_ends_with($this->currentUri, '/')) {
            $this->currentUri = rtrim($this->currentUri, '/');
        }
    }
    
    /**
     * 添加路由
     */
    public function addRoute($pattern, $handler, $method = 'GET') {
        $this->routes[] = [
            'pattern' => $pattern,
            'handler' => $handler,
            'method' => strtoupper($method)
        ];
    }
    
    /**
     * 处理路由
     */
    public function dispatch() {
        foreach ($this->routes as $route) {
            if ($route['method'] !== $this->currentMethod) {
                continue;
            }
            
            $pattern = $route['pattern'];
            $handler = $route['handler'];
            
            // 将路由模式转换为正则表达式
            $regex = $this->patternToRegex($pattern);
            
            if (preg_match($regex, $this->currentUri, $matches)) {
                // 移除完整匹配
                array_shift($matches);
                
                // 调用处理器
                if (is_callable($handler)) {
                    return call_user_func_array($handler, $matches);
                } elseif (is_string($handler)) {
                    return $this->includeTemplate($handler, $matches);
                }
            }
        }
        
        // 没有匹配的路由，返回404
        return $this->handle404();
    }
    
    /**
     * 将路由模式转换为正则表达式
     */
    private function patternToRegex($pattern) {
        // 转义特殊字符
        $pattern = preg_quote($pattern, '/');
        
        // 替换参数占位符
        $pattern = preg_replace('/\\\\{([^}]+)\\\\}/', '([^/]+)', $pattern);
        
        return '/^' . $pattern . '$/';
    }
    
    /**
     * 包含模板文件
     */
    private function includeTemplate($template, $params = []) {
        $templatePath = ROOT_PATH . '/templates/pages/' . $template . '.php';
        
        if (file_exists($templatePath)) {
            // 将参数设置为全局变量
            foreach ($params as $key => $value) {
                $GLOBALS['route_param_' . $key] = $value;
            }
            
            include $templatePath;
            return true;
        }
        
        return false;
    }
    
    /**
     * 处理404错误
     */
    private function handle404() {
        http_response_code(404);
        include ROOT_PATH . '/templates/pages/404.php';
        return false;
    }
    
    /**
     * 获取当前URI
     */
    public function getCurrentUri() {
        return $this->currentUri;
    }
}

/**
 * 初始化路由
 */
function initializeRoutes() {
    $router = new SimpleRouter();
    
    // 首页
    $router->addRoute('/', 'index');

    // 工具列表页
    $router->addRoute('/tools', 'tools');

    // 帮助和支持页面
    $router->addRoute('/help', 'help');
    $router->addRoute('/contact', 'contact');
    $router->addRoute('/privacy', 'privacy');
    $router->addRoute('/terms', 'terms');
    
    // 工具分类页
    $router->addRoute('/tools/{category}', function($category) {
        $GLOBALS['route_category'] = $category;
        include ROOT_PATH . '/templates/pages/tools/category.php';
    });
    
    // 工具详情页
    $router->addRoute('/tools/{category}/{tool}', function($category, $tool) {
        $GLOBALS['route_category'] = $category;
        $GLOBALS['route_tool'] = $tool;
        include ROOT_PATH . '/templates/pages/tool-detail.php';
    });

    // API路由
    $router->addRoute('/api/{endpoint}', function($endpoint) {
        include ROOT_PATH . '/api/' . $endpoint . '.php';
    }, 'POST');
    
    // 处理路由
    return $router->dispatch();
}

/**
 * 获取路由参数
 */
function getRouteParam($key, $default = null) {
    return $GLOBALS['route_param_' . $key] ?? $GLOBALS['route_' . $key] ?? $default;
}

/**
 * 生成URL
 */
function url($path = '') {
    $baseUrl = config('app.url', 'http://localhost');
    return rtrim($baseUrl, '/') . '/' . ltrim($path, '/');
}

/**
 * 重定向到指定URL
 */
function redirectTo($url, $statusCode = 302) {
    if (!str_starts_with($url, 'http')) {
        $url = url($url);
    }
    
    header('Location: ' . $url, true, $statusCode);
    exit;
}

/**
 * 检查当前路由是否匹配
 */
function isCurrentRoute($pattern) {
    $currentUri = $_SERVER['REQUEST_URI'] ?? '/';
    $currentUri = parse_url($currentUri, PHP_URL_PATH);
    
    // 简单的模式匹配
    if ($pattern === $currentUri) {
        return true;
    }
    
    // 支持通配符
    if (str_contains($pattern, '*')) {
        $regex = '/^' . str_replace('*', '.*', preg_quote($pattern, '/')) . '$/';
        return preg_match($regex, $currentUri);
    }
    
    return false;
}

/**
 * 获取当前页面名称
 */
function getCurrentPageName() {
    $uri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
    $parts = explode('/', trim($uri, '/'));
    
    if (empty($parts[0])) {
        return 'home';
    }
    
    return $parts[0];
}
