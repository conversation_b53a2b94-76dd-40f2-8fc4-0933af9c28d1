<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- SEO Meta标签 -->
    <?php
    // 包含动态SEO助手（如果还没有包含）
    if (!function_exists('getDynamicSEOData')) {
        require_once ROOT_PATH . '/app/helpers/dynamic-seo.php';
    }

    // 使用页面定义的SEO数据，如果没有则使用默认
    if (isset($seoData) && is_array($seoData)) {
        // 使用页面自定义的SEO数据
        echo generateMetaTags($currentPage ?? 'home', $seoData);
    } else {
        // 使用默认SEO数据
        echo generateMetaTags($currentPage ?? 'home');
    }

    // 添加规范URL（用于SEO优化）
    $currentUrl = $_SERVER['REQUEST_URI'] ?? '';
    if (preg_match('/^\/tools\/([^\/]+)\/([^\/]+)$/', $currentUrl)) {
        // 工具页面 - 确保使用HTTPS和完整域名
        $canonicalUrl = 'https://' . ($_SERVER['HTTP_HOST'] ?? 'localhost') . $currentUrl;
        echo '<link rel="canonical" href="' . htmlspecialchars($canonicalUrl) . '">' . "\n";
    }
    ?>
    
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    borderRadius: {
                        'none': '0',
                        DEFAULT: '0',
                    },
                    colors: {
                        primary: '#000000',
                        secondary: '#1a1a1a',
                        accent: '#4e73df',
                        success: '#1cc88a',
                        warning: '#f6c23e',
                        danger: '#e74a3b',
                    },
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                    }
                }
            },
            darkMode: 'class'
        }
    </script>

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- 自定义样式 -->
    <link rel="stylesheet" href="/assets/css/app.css">
    
    <!-- 字体预加载 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="apple-touch-icon" href="/assets/images/apple-touch-icon.png">

    <?php
    // 获取追踪代码设置
    $trackingCodes = getTrackingCodes();

    // Google Analytics
    if (!empty($trackingCodes['google_analytics'])):
    ?>
    <!-- Google Analytics -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=<?= htmlspecialchars($trackingCodes['google_analytics']) ?>"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', '<?= htmlspecialchars($trackingCodes['google_analytics']) ?>');
    </script>
    <?php endif; ?>

    <?php
    // Google AdSense
    if (!empty($trackingCodes['google_adsense'])):
    ?>
    <!-- Google AdSense -->
    <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=<?= htmlspecialchars($trackingCodes['google_adsense']) ?>" crossorigin="anonymous"></script>
    <?php endif; ?>
</head>
<body class="bg-black text-white min-h-screen">
    <!-- 导航栏 -->
    <nav class="navbar bg-black border-b border-gray-800 fixed w-full top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo区域 -->
                <div class="flex items-center">
                    <a href="/" class="flex items-center space-x-2">
                        <div class="w-8 h-8 bg-accent flex items-center justify-center">
                            <span class="text-white font-bold text-lg">AI</span>
                        </div>
                        <span class="text-xl font-bold text-white hidden sm:block">
                            Prompt2Tool
                        </span>
                    </a>
                </div>
                
                <!-- 桌面端导航菜单 -->
                <div class="hidden md:flex items-center space-x-8">
                    <a href="/" class="text-gray-300 hover:text-white transition-colors duration-200 <?= $currentPage === 'home' ? 'text-accent' : '' ?>">
                        Home
                    </a>
                    <div class="relative group">
                        <a href="/tools" class="text-gray-300 hover:text-white transition-colors duration-200 flex items-center <?= strpos($currentPage, 'tools') === 0 ? 'text-accent' : '' ?>">
                            Tools
                            <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                            </svg>
                        </a>
                        <!-- 工具分类下拉菜单 -->
                        <div class="absolute top-full left-0 mt-2 w-56 bg-gray-900 border border-gray-700 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                            <a href="/tools" class="block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800">
                                🛠️ All Tools
                            </a>
                            <div class="border-t border-gray-700 my-1"></div>
                            <?php
                            // 只显示工具数量最多的6个分类
                            $topCategories = getTopToolCategories(6);
                            foreach ($topCategories as $slug => $category): ?>
                            <a href="/tools/<?= $slug ?>" class="block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800">
                                <?= htmlspecialchars($category['icon']) ?> <?= htmlspecialchars($category['name']) ?>
                            </a>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    <a href="/requests" class="text-gray-300 hover:text-white transition-colors duration-200 <?= $currentPage === 'requests' ? 'text-accent' : '' ?>">
                        Requests
                    </a>
                    <a href="/launches" class="text-gray-300 hover:text-white transition-colors duration-200 <?= $currentPage === 'launches' ? 'text-accent' : '' ?>">
                        Launches
                    </a>

                </div>
                
                <!-- 用户菜单区域 -->
                <div class="flex items-center space-x-4">
                    <!-- 搜索按钮 -->
                    <button class="text-gray-300 hover:text-white p-2" data-modal="search-modal">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </button>
                    
                    <?php if (isset($_SESSION['user_id'])): ?>
                        <!-- 已登录用户菜单 -->
                        <div class="relative group">
                            <button class="flex items-center space-x-2 text-gray-300 hover:text-white">
                                <div class="w-8 h-8 bg-accent flex items-center justify-center">
                                    <span class="text-white text-sm font-medium">
                                        <?= strtoupper(substr($_SESSION['username'] ?? 'U', 0, 1)) ?>
                                    </span>
                                </div>
                                <span class="hidden sm:block"><?= htmlspecialchars($_SESSION['username'] ?? 'User') ?></span>
                            </button>
                            <!-- 用户下拉菜单 -->
                            <div class="absolute top-full right-0 mt-2 w-48 bg-gray-900 border border-gray-700 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                                <a href="/dashboard" class="block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800">📊 Dashboard</a>
                                <a href="/profile" class="block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800">👤 Profile</a>
                                <div class="border-t border-gray-700 my-1"></div>
                                <a href="/auth/logout" class="block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800">🚪 Logout</a>
                            </div>
                        </div>
                    <?php else: ?>
                        <!-- 未登录用户按钮 -->
                        <a href="/auth/login" class="text-gray-300 hover:text-white transition-colors duration-200">
                            Login
                        </a>
                        <a href="/auth/register" class="bg-accent text-white px-4 py-2 hover:bg-blue-700 hover:text-white transition-colors duration-200">
                            Sign Up
                        </a>
                    <?php endif; ?>
                    
                    <!-- 移动端菜单按钮 -->
                    <button class="md:hidden text-gray-300 hover:text-white p-2" id="mobile-menu-btn">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- 移动端菜单 -->
        <div class="md:hidden bg-gray-900 border-t border-gray-800 hidden" id="mobile-menu">
            <div class="px-4 py-2 space-y-1">
                <a href="/" class="block py-2 text-gray-300 hover:text-white">Home</a>
                <a href="/tools" class="block py-2 text-gray-300 hover:text-white">Tools</a>

                <?php if (!isset($_SESSION['user_id'])): ?>
                    <div class="border-t border-gray-700 pt-2 mt-2">
                        <a href="/auth/login" class="block py-2 text-gray-300 hover:text-white">Login</a>
                        <a href="/auth/register" class="block py-2 text-accent hover:text-blue-400">Sign Up</a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </nav>
    
    <!-- 主内容区域 -->
    <main class="pt-16 min-h-screen">
        <!-- 面包屑导航已删除 -->
