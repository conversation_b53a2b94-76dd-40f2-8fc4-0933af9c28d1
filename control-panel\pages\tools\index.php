<?php
/**
 * 工具管理列表页面
 * 显示所有工具的管理界面
 */

// 定义根目录
define('ROOT_PATH', dirname(dirname(dirname(__DIR__))));

// 加载认证中间件
require_once dirname(__DIR__) . '/../auth/middleware.php';

// 权限检查
if (!hasPermission('tools.view')) {
    header('HTTP/1.1 403 Forbidden');
    exit('Access denied');
}

// 获取筛选参数
$filters = [
    'category' => $_GET['category'] ?? '',
    'status' => $_GET['status'] ?? '',
    'search' => $_GET['search'] ?? '',
    'page' => max(1, intval($_GET['page'] ?? 1)),
    'per_page' => 20
];

// 使用统一的数据库连接
require_once ROOT_PATH . '/includes/database-connection.php';

// 处理操作
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $tool_id = $_POST['tool_id'] ?? '';

    try {
        switch ($action) {
            case 'create':
                $stmt = $pdo->prepare("
                    INSERT INTO tools (name, slug, description, category_id, url, icon, tags, sort_order, status, is_featured)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ");
                $stmt->execute([
                    $_POST['name'],
                    $_POST['slug'],
                    $_POST['description'],
                    $_POST['category_id'] ?: null,
                    $_POST['url'],
                    $_POST['icon'],
                    $_POST['tags'],
                    intval($_POST['sort_order']),
                    $_POST['status'],
                    isset($_POST['is_featured']) ? 1 : 0
                ]);
                $success_message = "Tool created successfully";
                break;

            case 'update':
                $stmt = $pdo->prepare("
                    UPDATE pt_tool
                    SET name = ?, slug = ?, description = ?, category_id = ?, url = ?, icon = ?, tags = ?, sort_order = ?, status = ?, is_featured = ?
                    WHERE id = ?
                ");
                $stmt->execute([
                    $_POST['name'],
                    $_POST['slug'],
                    $_POST['description'],
                    $_POST['category_id'] ?: null,
                    $_POST['url'],
                    $_POST['icon'],
                    $_POST['tags'],
                    intval($_POST['sort_order']),
                    $_POST['status'],
                    isset($_POST['is_featured']) ? 1 : 0,
                    intval($_POST['tool_id'])
                ]);
                $success_message = "Tool updated successfully";
                break;

            case 'delete':
                $stmt = $pdo->prepare("DELETE FROM pt_tool WHERE id = ?");
                $stmt->execute([intval($_POST['tool_id'])]);
                $success_message = "Tool deleted successfully";
                break;

            case 'toggle_status':
                $stmt = $pdo->prepare("UPDATE tools SET status = ? WHERE id = ?");
                $new_status = $_POST['current_status'] === 'active' ? 'inactive' : 'active';
                $stmt->execute([$new_status, intval($_POST['tool_id'])]);
                $success_message = "Tool status updated successfully";
                break;
        }
    } catch (Exception $e) {
        $error_message = "Error: " . $e->getMessage();
    }
}

// 获取工具分类
$stmt = $pdo->query("SELECT * FROM pt_tool_category WHERE status = 'active' ORDER BY sort_order, name");
$categories = $stmt->fetchAll(PDO::FETCH_ASSOC);

// 获取所有工具
$stmt = $pdo->query("
    SELECT t.*, tc.name as category_name
    FROM pt_tool t
    LEFT JOIN pt_tool_category tc ON t.category_id = tc.id
    ORDER BY t.sort_order, t.name
");
$allTools = $stmt->fetchAll(PDO::FETCH_ASSOC);

// 应用筛选
$filteredTools = $allTools;

if (!empty($filters['search'])) {
    $filteredTools = array_filter($filteredTools, function($tool) use ($filters) {
        return stripos($tool['name'], $filters['search']) !== false ||
               stripos($tool['description'], $filters['search']) !== false;
    });
}

if (!empty($filters['category'])) {
    $filteredTools = array_filter($filteredTools, function($tool) use ($filters) {
        return $tool['category_name'] === $filters['category'];
    });
}

if ($filters['status'] !== '') {
    $isActive = $filters['status'] === 'active';
    $filteredTools = array_filter($filteredTools, function($tool) use ($isActive) {
        return $tool['is_active'] === $isActive;
    });
}

// 分页处理
$totalTools = count($filteredTools);
$totalPages = ceil($totalTools / $filters['per_page']);
$offset = ($filters['page'] - 1) * $filters['per_page'];
$paginatedTools = array_slice($filteredTools, $offset, $filters['per_page']);

$currentPage = 'tools';
include_once dirname(__DIR__) . '/../components/layout/header.php';
?>

<div class="min-h-screen bg-gray-100">
    <!-- 侧边栏 -->
    <div class="fixed inset-y-0 left-0 w-64 bg-gray-900">
        <?php include_once dirname(__DIR__) . '/../components/layout/sidebar.php'; ?>
    </div>
    
    <!-- 主内容区 -->
    <div class="ml-64">
        <!-- 操作按钮区域 -->
        <div class="mb-6">
            <div class="flex justify-end">
                <button onclick="openToolModal()" class="bg-accent text-white px-4 py-2 hover:bg-blue-700 hover:text-white transition-all duration-200">
                    Add Tool
                </button>
            </div>
        </div>

        <!-- 页面内容 -->
        <div class="p-6">
            <!-- 成功/错误消息 -->
            <?php if (isset($success_message)): ?>
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 mb-6">
                <?= htmlspecialchars($success_message) ?>
            </div>
            <?php endif; ?>

            <?php if (isset($error_message)): ?>
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 mb-6">
                <?= htmlspecialchars($error_message) ?>
            </div>
            <?php endif; ?>
            <!-- 筛选器 -->
            <div class="bg-white border border-gray-200 p-4 mb-6">
                <form method="GET" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <!-- 搜索框 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Search Tools</label>
                        <input type="text" 
                               name="search" 
                               value="<?= htmlspecialchars($filters['search']) ?>"
                               placeholder="Search by name or description..."
                               class="w-full px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent">
                    </div>
                    
                    <!-- 分类筛选 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Category</label>
                        <select name="category" class="w-full px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent">
                            <option value="">All Categories</option>
                            <?php foreach ($categories as $category): ?>
                            <option value="<?= htmlspecialchars($category['name']) ?>" <?= $filters['category'] === $category['name'] ? 'selected' : '' ?>>
                                <?= htmlspecialchars($category['name']) ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <!-- 状态筛选 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                        <select name="status" class="w-full px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent">
                            <option value="">All Status</option>
                            <option value="active" <?= $filters['status'] === 'active' ? 'selected' : '' ?>>Active</option>
                            <option value="inactive" <?= $filters['status'] === 'inactive' ? 'selected' : '' ?>>Inactive</option>
                        </select>
                    </div>
                    
                    <!-- 搜索按钮 -->
                    <div class="flex items-end">
                        <button type="submit" class="w-full bg-accent text-white px-4 py-2 hover:bg-blue-600 transition-colors">
                            <i class="fas fa-search mr-2"></i>Filter
                        </button>
                    </div>
                </form>
            </div>
            
            <!-- 工具列表 -->
            <div class="bg-white border border-gray-200">
                <!-- 表格头部 -->
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex justify-between items-center">
                        <h3 class="text-lg font-semibold text-gray-900">Tools List</h3>
                        <div class="text-sm text-gray-500">
                            Showing <?= count($paginatedTools) ?> of <?= number_format($totalTools) ?> tools
                        </div>
                    </div>
                </div>
                
                <!-- 表格内容 -->
                <div class="overflow-x-auto">
                    <table class="w-full">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tool</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Views</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php foreach ($paginatedTools as $tool): ?>
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="text-2xl mr-3"><?= htmlspecialchars($tool['icon']) ?></div>
                                        <div>
                                            <div class="text-sm font-medium text-gray-900"><?= htmlspecialchars($tool['name']) ?></div>
                                            <div class="text-sm text-gray-500"><?= htmlspecialchars(substr($tool['description'], 0, 60)) ?>...</div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex items-center px-2.5 py-0.5 text-xs font-medium bg-gray-100 text-gray-800">
                                        <?= htmlspecialchars($tool['category_name'] ?? 'Uncategorized') ?>
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <?= number_format($tool['view_count'] ?? 0) ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <?php if ($tool['status'] === 'active'): ?>
                                        <span class="inline-flex items-center px-2.5 py-0.5 text-xs font-medium bg-green-100 text-green-800">
                                            Active
                                        </span>
                                    <?php else: ?>
                                        <span class="inline-flex items-center px-2.5 py-0.5 text-xs font-medium bg-gray-100 text-gray-800">
                                            <?= ucfirst($tool['status']) ?>
                                        </span>
                                    <?php endif; ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex space-x-2">
                                        <a href="<?= htmlspecialchars($tool['url'] ?? '/tools/' . $tool['slug']) ?>"
                                           target="_blank"
                                           class="text-blue-600 hover:text-blue-900"
                                           title="View Tool">
                                            View
                                        </a>

                                        <button onclick="editTool(<?= htmlspecialchars(json_encode($tool)) ?>)"
                                                class="text-indigo-600 hover:text-indigo-900"
                                                title="Edit Tool">
                                            Edit
                                        </button>

                                        <form method="POST" class="inline">
                                            <input type="hidden" name="action" value="toggle_status">
                                            <input type="hidden" name="tool_id" value="<?= $tool['id'] ?>">
                                            <input type="hidden" name="current_status" value="<?= $tool['status'] ?>">
                                            <button type="submit" class="text-yellow-600 hover:text-yellow-900" title="Toggle Status">
                                                <?= $tool['status'] === 'active' ? 'Deactivate' : 'Activate' ?>
                                            </button>
                                        </form>

                                        <button onclick="deleteTool(<?= $tool['id'] ?>, '<?= htmlspecialchars($tool['name']) ?>')"
                                                class="text-red-600 hover:text-red-900"
                                                title="Delete Tool">
                                            Delete
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                
                <!-- 分页 -->
                <?php if ($totalPages > 1): ?>
                <div class="px-6 py-4 border-t border-gray-200">
                    <div class="flex items-center justify-between">
                        <div class="text-sm text-gray-700">
                            Showing <?= $offset + 1 ?> to <?= min($offset + $filters['per_page'], $totalTools) ?> of <?= $totalTools ?> results
                        </div>
                        
                        <div class="flex space-x-1">
                            <?php if ($filters['page'] > 1): ?>
                            <a href="?<?= http_build_query(array_merge($filters, ['page' => $filters['page'] - 1])) ?>" 
                               class="px-3 py-2 text-sm bg-white border border-gray-300 text-gray-500 hover:bg-gray-50">
                                Previous
                            </a>
                            <?php endif; ?>
                            
                            <?php for ($i = max(1, $filters['page'] - 2); $i <= min($totalPages, $filters['page'] + 2); $i++): ?>
                            <a href="?<?= http_build_query(array_merge($filters, ['page' => $i])) ?>" 
                               class="px-3 py-2 text-sm border <?= $i === $filters['page'] ? 'bg-accent text-white border-accent' : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50' ?>">
                                <?= $i ?>
                            </a>
                            <?php endfor; ?>
                            
                            <?php if ($filters['page'] < $totalPages): ?>
                            <a href="?<?= http_build_query(array_merge($filters, ['page' => $filters['page'] + 1])) ?>" 
                               class="px-3 py-2 text-sm bg-white border border-gray-300 text-gray-500 hover:bg-gray-50">
                                Next
                            </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- 工具编辑模态框 -->
<div id="toolModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg bg-white">
        <div class="mt-3">
            <h3 id="modalTitle" class="text-lg font-medium text-gray-900 mb-4">Add Tool</h3>

            <form id="toolForm" method="POST">
                <input type="hidden" name="action" id="formAction" value="create">
                <input type="hidden" name="tool_id" id="toolId">

                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Name</label>
                        <input type="text" name="name" id="toolName" required
                               class="w-full px-3 py-2 border border-gray-300 focus:outline-none focus:border-accent">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Slug</label>
                        <input type="text" name="slug" id="toolSlug" required
                               class="w-full px-3 py-2 border border-gray-300 focus:outline-none focus:border-accent">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                        <textarea name="description" id="toolDescription" rows="3"
                                  class="w-full px-3 py-2 border border-gray-300 focus:outline-none focus:border-accent"></textarea>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Category</label>
                        <select name="category_id" id="toolCategoryId"
                                class="w-full px-3 py-2 border border-gray-300 focus:outline-none focus:border-accent">
                            <option value="">Select Category</option>
                            <?php foreach ($categories as $category): ?>
                            <option value="<?= $category['id'] ?>"><?= htmlspecialchars($category['name']) ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">URL</label>
                        <input type="text" name="url" id="toolUrl"
                               class="w-full px-3 py-2 border border-gray-300 focus:outline-none focus:border-accent">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Icon (Emoji)</label>
                        <input type="text" name="icon" id="toolIcon" placeholder="🔧"
                               class="w-full px-3 py-2 border border-gray-300 focus:outline-none focus:border-accent">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Tags (comma separated)</label>
                        <input type="text" name="tags" id="toolTags"
                               class="w-full px-3 py-2 border border-gray-300 focus:outline-none focus:border-accent">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Sort Order</label>
                        <input type="number" name="sort_order" id="toolSortOrder" value="1" min="1"
                               class="w-full px-3 py-2 border border-gray-300 focus:outline-none focus:border-accent">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                        <select name="status" id="toolStatus"
                                class="w-full px-3 py-2 border border-gray-300 focus:outline-none focus:border-accent">
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                            <option value="coming_soon">Coming Soon</option>
                        </select>
                    </div>

                    <div>
                        <label class="flex items-center">
                            <input type="checkbox" name="is_featured" id="toolFeatured" class="mr-2">
                            <span class="text-sm font-medium text-gray-700">Featured Tool</span>
                        </label>
                    </div>
                </div>

                <div class="flex justify-end space-x-4 mt-6">
                    <button type="button" onclick="closeToolModal()"
                            class="px-4 py-2 bg-gray-300 text-gray-700 hover:bg-gray-400">
                        Cancel
                    </button>
                    <button type="submit"
                            class="px-4 py-2 bg-accent text-white hover:bg-blue-700 hover:text-white">
                        Save Tool
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div id="deleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg bg-white">
        <div class="mt-3 text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 bg-red-100 mb-4">
                <svg class="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">Delete Tool</h3>
            <p class="text-sm text-gray-500 mb-4">
                Are you sure you want to delete "<span id="deleteToolName"></span>"? This action cannot be undone.
            </p>

            <form method="POST" class="inline">
                <input type="hidden" name="action" value="delete">
                <input type="hidden" name="tool_id" id="deleteToolId">

                <div class="flex justify-center space-x-4">
                    <button type="button" onclick="closeDeleteModal()"
                            class="px-4 py-2 bg-gray-300 text-gray-700 hover:bg-gray-400">
                        Cancel
                    </button>
                    <button type="submit"
                            class="px-4 py-2 bg-red-600 text-white hover:bg-red-700">
                        Delete Tool
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// 打开工具模态框
function openToolModal(tool = null) {
    const modal = document.getElementById('toolModal');
    const form = document.getElementById('toolForm');
    const title = document.getElementById('modalTitle');

    if (tool) {
        // 编辑模式
        title.textContent = 'Edit Tool';
        document.getElementById('formAction').value = 'update';
        document.getElementById('toolId').value = tool.id;
        document.getElementById('toolName').value = tool.name;
        document.getElementById('toolSlug').value = tool.slug;
        document.getElementById('toolDescription').value = tool.description || '';
        document.getElementById('toolCategoryId').value = tool.category_id || '';
        document.getElementById('toolUrl').value = tool.url || '';
        document.getElementById('toolIcon').value = tool.icon || '';
        document.getElementById('toolTags').value = tool.tags || '';
        document.getElementById('toolSortOrder').value = tool.sort_order || 1;
        document.getElementById('toolStatus').value = tool.status || 'active';
        document.getElementById('toolFeatured').checked = tool.is_featured == 1;
    } else {
        // 创建模式
        title.textContent = 'Add Tool';
        document.getElementById('formAction').value = 'create';
        form.reset();
    }

    modal.classList.remove('hidden');
}

// 关闭模态框
function closeToolModal() {
    document.getElementById('toolModal').classList.add('hidden');
}

// 删除工具
function deleteTool(toolId, toolName) {
    openDeleteModal(toolId, toolName);
}

function openDeleteModal(toolId, toolName) {
    document.getElementById('deleteToolId').value = toolId;
    document.getElementById('deleteToolName').textContent = toolName;
    document.getElementById('deleteModal').classList.remove('hidden');
}

function closeDeleteModal() {
    document.getElementById('deleteModal').classList.add('hidden');
}

// 编辑工具
function editTool(tool) {
    openToolModal(tool);
}

// 自动生成slug
document.getElementById('toolName').addEventListener('input', function() {
    const name = this.value;
    const slug = name.toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim('-');
    document.getElementById('toolSlug').value = slug;
});

// 点击模态框外部关闭
document.getElementById('toolModal')?.addEventListener('click', function(e) {
    if (e.target === this) closeToolModal();
});

document.getElementById('deleteModal')?.addEventListener('click', function(e) {
    if (e.target === this) closeDeleteModal();
});
</script>

<?php include_once dirname(__DIR__) . '/../components/layout/footer.php'; ?>
