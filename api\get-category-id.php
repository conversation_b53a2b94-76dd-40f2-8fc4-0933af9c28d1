<?php
/**
 * Get Category ID API Endpoint
 * Returns the category ID for a given category name
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit();
}

// 使用统一的数据库连接
require_once dirname(__DIR__) . '/includes/database-connection.php';

try {
    // Get and validate input
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input || empty($input['category'])) {
        throw new Exception('Category name is required');
    }
    
    $categoryName = trim($input['category']);
    
    // Find category by name
    $stmt = $pdo->prepare("SELECT id, slug FROM pt_tool_category WHERE name = ? AND status = 'active'");
    $stmt->execute([$categoryName]);
    $category = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($category) {
        echo json_encode([
            'success' => true,
            'category_id' => $category['id'],
            'category_slug' => $category['slug']
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'error' => 'Category not found'
        ]);
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
