/**
 * 表单处理组件
 * 处理表单验证、提交和反馈
 */
class FormHandler {
    constructor() {
        this.forms = new Map();
        this.init();
    }

    init() {
        this.setupForms();
        this.setupValidation();
        this.setupSubmission();
    }

    /**
     * 设置表单
     */
    setupForms() {
        const forms = document.querySelectorAll('form[data-form]');
        
        forms.forEach(form => {
            const formType = form.dataset.form;
            this.forms.set(form, {
                type: formType,
                validators: this.getValidators(formType),
                isSubmitting: false
            });

            // 添加实时验证
            const inputs = form.querySelectorAll('input, textarea, select');
            inputs.forEach(input => {
                input.addEventListener('blur', () => this.validateField(input));
                input.addEventListener('input', () => this.clearFieldError(input));
            });
        });
    }

    /**
     * 获取表单验证器
     */
    getValidators(formType) {
        const validators = {
            contact: {
                name: { required: true, minLength: 2 },
                email: { required: true, email: true },
                message: { required: true, minLength: 10 }
            },
            newsletter: {
                email: { required: true, email: true }
            },
            feedback: {
                rating: { required: true },
                comment: { minLength: 5 }
            },
            tool: {
                input: { required: true }
            }
        };

        return validators[formType] || {};
    }

    /**
     * 设置表单验证
     */
    setupValidation() {
        // 实时验证已在setupForms中设置
    }

    /**
     * 验证单个字段
     */
    validateField(field) {
        const form = field.closest('form');
        const formData = this.forms.get(form);
        if (!formData) return true;

        const fieldName = field.name;
        const fieldValue = field.value.trim();
        const rules = formData.validators[fieldName];

        if (!rules) return true;

        const errors = [];

        // 必填验证
        if (rules.required && !fieldValue) {
            errors.push('This field is required');
        }

        // 最小长度验证
        if (rules.minLength && fieldValue.length < rules.minLength) {
            errors.push(`Minimum ${rules.minLength} characters required`);
        }

        // 最大长度验证
        if (rules.maxLength && fieldValue.length > rules.maxLength) {
            errors.push(`Maximum ${rules.maxLength} characters allowed`);
        }

        // 邮箱验证
        if (rules.email && fieldValue && !this.isValidEmail(fieldValue)) {
            errors.push('Please enter a valid email address');
        }

        // URL验证
        if (rules.url && fieldValue && !this.isValidURL(fieldValue)) {
            errors.push('Please enter a valid URL');
        }

        // 数字验证
        if (rules.numeric && fieldValue && !this.isNumeric(fieldValue)) {
            errors.push('Please enter a valid number');
        }

        // 显示或清除错误
        if (errors.length > 0) {
            this.showFieldError(field, errors[0]);
            return false;
        } else {
            this.clearFieldError(field);
            return true;
        }
    }

    /**
     * 显示字段错误
     */
    showFieldError(field, message) {
        this.clearFieldError(field);

        const errorElement = document.createElement('div');
        errorElement.className = 'field-error text-danger text-sm mt-1';
        errorElement.textContent = message;

        field.classList.add('error');
        field.parentNode.appendChild(errorElement);
    }

    /**
     * 清除字段错误
     */
    clearFieldError(field) {
        field.classList.remove('error');
        const errorElement = field.parentNode.querySelector('.field-error');
        if (errorElement) {
            errorElement.remove();
        }
    }

    /**
     * 设置表单提交
     */
    setupSubmission() {
        document.addEventListener('submit', (e) => {
            const form = e.target;
            if (this.forms.has(form)) {
                e.preventDefault();
                this.handleSubmit(form);
            }
        });
    }

    /**
     * 处理表单提交
     */
    async handleSubmit(form) {
        const formData = this.forms.get(form);
        if (!formData || formData.isSubmitting) return;

        // 验证整个表单
        if (!this.validateForm(form)) {
            this.showFormMessage(form, 'Please fix the errors above', 'error');
            return;
        }

        // 设置提交状态
        formData.isSubmitting = true;
        this.setFormLoading(form, true);

        try {
            const result = await this.submitForm(form);
            
            if (result.success) {
                this.showFormMessage(form, result.message || 'Form submitted successfully!', 'success');
                this.resetForm(form);
            } else {
                this.showFormMessage(form, result.message || 'An error occurred. Please try again.', 'error');
            }
        } catch (error) {
            console.error('Form submission error:', error);
            this.showFormMessage(form, 'Network error. Please check your connection and try again.', 'error');
        } finally {
            formData.isSubmitting = false;
            this.setFormLoading(form, false);
        }
    }

    /**
     * 验证整个表单
     */
    validateForm(form) {
        const inputs = form.querySelectorAll('input, textarea, select');
        let isValid = true;

        inputs.forEach(input => {
            if (!this.validateField(input)) {
                isValid = false;
            }
        });

        return isValid;
    }

    /**
     * 提交表单到服务器
     */
    async submitForm(form) {
        const formData = new FormData(form);
        const action = form.action || '/api/form-submit';
        const method = form.method || 'POST';

        const response = await fetch(action, {
            method: method,
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return await response.json();
    }

    /**
     * 设置表单加载状态
     */
    setFormLoading(form, loading) {
        const submitButton = form.querySelector('button[type="submit"], input[type="submit"]');
        
        if (loading) {
            submitButton?.classList.add('loading');
            submitButton?.setAttribute('disabled', 'disabled');
        } else {
            submitButton?.classList.remove('loading');
            submitButton?.removeAttribute('disabled');
        }
    }

    /**
     * 显示表单消息
     */
    showFormMessage(form, message, type = 'info') {
        // 移除现有消息
        const existingMessage = form.querySelector('.form-message');
        if (existingMessage) {
            existingMessage.remove();
        }

        // 创建新消息
        const messageElement = document.createElement('div');
        messageElement.className = `form-message p-4 mb-4 border ${
            type === 'success' ? 'bg-success border-green-600 text-white' :
            type === 'error' ? 'bg-danger border-red-600 text-white' :
            'bg-gray-900 border-gray-700 text-gray-300'
        }`;
        messageElement.textContent = message;

        // 插入到表单顶部
        form.insertBefore(messageElement, form.firstChild);

        // 自动移除成功消息
        if (type === 'success') {
            setTimeout(() => {
                if (messageElement.parentElement) {
                    messageElement.remove();
                }
            }, 5000);
        }
    }

    /**
     * 重置表单
     */
    resetForm(form) {
        form.reset();
        
        // 清除所有错误
        const inputs = form.querySelectorAll('input, textarea, select');
        inputs.forEach(input => this.clearFieldError(input));
    }

    /**
     * 验证邮箱
     */
    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    /**
     * 验证URL
     */
    isValidURL(url) {
        try {
            new URL(url);
            return true;
        } catch {
            return false;
        }
    }

    /**
     * 验证数字
     */
    isNumeric(value) {
        return !isNaN(value) && !isNaN(parseFloat(value));
    }
}

// 自动初始化
if (typeof window !== 'undefined') {
    window.FormHandler = FormHandler;
    
    document.addEventListener('DOMContentLoaded', () => {
        window.formHandler = new FormHandler();
    });
}
