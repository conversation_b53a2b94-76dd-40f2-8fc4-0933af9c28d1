<?php
/**
 * 检查分类slug是否重复API
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

// 检查登录状态
session_start();
if (!isset($_SESSION['admin_logged_in']) || !$_SESSION['admin_logged_in']) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit;
}

// 只允许GET请求
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit;
}

$slug = $_GET['slug'] ?? '';
$excludeId = $_GET['exclude_id'] ?? null;

if (empty($slug)) {
    echo json_encode(['success' => false, 'error' => 'Slug is required']);
    exit;
}

// 使用统一的数据库连接
require_once dirname(__DIR__) . '/includes/database-connection.php';

try {

    // 检查slug是否已存在
    if ($excludeId) {
        // 编辑模式：排除当前分类ID
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM pt_tool_category WHERE slug = ? AND id != ?");
        $stmt->execute([$slug, $excludeId]);
    } else {
        // 创建模式：检查所有分类
        $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM pt_tool_category WHERE slug = ?");
        $stmt->execute([$slug]);
    }

    $result = $stmt->fetch();
    $exists = $result['count'] > 0;

    if ($exists) {
        echo json_encode([
            'success' => true,
            'available' => false,
            'message' => 'Slug is already taken'
        ]);
    } else {
        echo json_encode([
            'success' => true,
            'available' => true,
            'message' => 'Slug is available'
        ]);
    }

} catch (PDOException $e) {
    error_log("Database error in check-category-slug.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Database error occurred'
    ]);
} catch (Exception $e) {
    error_log("General error in check-category-slug.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'An error occurred while checking slug availability'
    ]);
}
?>
