<?php
/**
 * 管理后台仪表板页面
 * 显示系统概览和关键统计数据
 */

// 使用统一的数据库连接
require_once ROOT_PATH . '/includes/database-connection.php';

// 获取数据库连接
try {

// 获取统计数据
    // 总工具数
    $totalTools = $pdo->query("SELECT COUNT(*) FROM pt_tool WHERE status != 'inactive'")->fetchColumn();

    // 总用户数 (如果pt_member表存在)
    $totalUsers = 0;
    $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    if (in_array('pt_member', $tables)) {
        $totalUsers = $pdo->query("SELECT COUNT(*) FROM pt_member WHERE status = 'active'")->fetchColumn();
    }

    // 总浏览量
    $totalViews = $pdo->query("SELECT SUM(view_count) FROM pt_tool")->fetchColumn() ?: 0;

    // 活跃工具数 (作为活跃会话的替代)
    $activeTools = $pdo->query("SELECT COUNT(*) FROM pt_tool WHERE status = 'active'")->fetchColumn();

    $stats = [
        'total_tools' => $totalTools ?: 0,
        'total_users' => $totalUsers,
        'total_views' => $totalViews,
        'active_sessions' => $activeTools ?: 0
    ];

    // 获取最近活动 (基于工具创建时间和联系消息)
    $recentActivity = [];

    // 最近添加的工具
    $recentTools = $pdo->query("
        SELECT name, created_at
        FROM pt_tool
        ORDER BY created_at DESC
        LIMIT 3
    ")->fetchAll();

    foreach ($recentTools as $tool) {
        $timeAgo = timeAgo($tool['created_at']);
        $recentActivity[] = [
            'action' => 'New tool added: ' . $tool['name'],
            'user' => 'admin',
            'time' => $timeAgo
        ];
    }

    // 最近的联系消息 (如果表存在)
    if (in_array('pt_contact_message', $tables)) {
        $recentMessages = $pdo->query("
            SELECT name, email, created_at
            FROM pt_contact_message
            ORDER BY created_at DESC
            LIMIT 2
        ")->fetchAll();

        foreach ($recentMessages as $message) {
            $timeAgo = timeAgo($message['created_at']);
            $recentActivity[] = [
                'action' => 'New contact message received',
                'user' => $message['email'],
                'time' => $timeAgo
            ];
        }
    }

    // 按时间排序
    usort($recentActivity, function($a, $b) {
        return strcmp($b['time'], $a['time']);
    });

    // 只保留最新的4条
    $recentActivity = array_slice($recentActivity, 0, 4);

    // 获取热门工具 (按浏览量排序)
    $popularTools = $pdo->query("
        SELECT
            name,
            view_count as uses,
            slug,
            CASE
                WHEN view_count > 1000 THEN '+15%'
                WHEN view_count > 500 THEN '+10%'
                WHEN view_count > 100 THEN '+5%'
                ELSE '+2%'
            END as trend
        FROM pt_tool
        WHERE status = 'active' AND view_count > 0
        ORDER BY view_count DESC
        LIMIT 4
    ")->fetchAll();

    // 如果没有浏览量数据，显示最新的工具
    if (empty($popularTools)) {
        $popularTools = $pdo->query("
            SELECT
                name,
                0 as uses,
                slug,
                'New' as trend
            FROM pt_tool
            WHERE status = 'active'
            ORDER BY created_at DESC
            LIMIT 4
        ")->fetchAll();
    }

} catch (Exception $e) {
    error_log('Dashboard data error: ' . $e->getMessage());

    // 降级到默认数据
    $stats = [
        'total_tools' => 0,
        'total_users' => 0,
        'total_views' => 0,
        'active_sessions' => 0
    ];
    $recentActivity = [];
    $popularTools = [];
}
?>

<!-- 页面描述和操作 -->
<div class="mb-8">
    <div class="flex justify-between items-center">
        <div>
            <p class="text-gray-600">Welcome back! Here's what's happening with Prompt2Tool.</p>
        </div>
        <div>
            <a href="?page=tools" class="bg-accent text-white px-4 py-2 hover:bg-blue-600 inline-block">
                Add New Tool
            </a>
        </div>
    </div>
</div>

<!-- 统计卡片 -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- 总工具数 -->
    <div class="admin-card stat-card p-6">
        <div class="flex items-center">
            <div class="p-3 bg-blue-100 text-blue-600 mr-4">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
            </div>
            <div>
                <p class="text-sm font-medium text-gray-600">Total Tools</p>
                <p class="text-2xl font-bold text-gray-900"><?= number_format($stats['total_tools']) ?></p>
            </div>
        </div>
    </div>

    <!-- 总用户数 -->
    <div class="admin-card stat-card p-6">
        <div class="flex items-center">
            <div class="p-3 bg-green-100 text-green-600 mr-4">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                </svg>
            </div>
            <div>
                <p class="text-sm font-medium text-gray-600">Total Users</p>
                <p class="text-2xl font-bold text-gray-900"><?= number_format($stats['total_users']) ?></p>
            </div>
        </div>
    </div>

    <!-- 总浏览量 -->
    <div class="admin-card stat-card p-6">
        <div class="flex items-center">
            <div class="p-3 bg-yellow-100 text-yellow-600 mr-4">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                </svg>
            </div>
            <div>
                <p class="text-sm font-medium text-gray-600">Total Views</p>
                <p class="text-2xl font-bold text-gray-900"><?= number_format($stats['total_views']) ?></p>
            </div>
        </div>
    </div>

    <!-- 活跃会话 -->
    <div class="admin-card stat-card p-6">
        <div class="flex items-center">
            <div class="p-3 bg-red-100 text-red-600 mr-4">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                </svg>
            </div>
            <div>
                <p class="text-sm font-medium text-gray-600">Active Sessions</p>
                <p class="text-2xl font-bold text-gray-900"><?= number_format($stats['active_sessions']) ?></p>
            </div>
        </div>
    </div>
</div>

<!-- 内容区域 -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
    <!-- 最近活动 -->
    <div class="admin-card">
        <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Recent Activity</h3>
        </div>
        <div class="p-6">
            <div class="space-y-4">
                <?php foreach ($recentActivity as $activity): ?>
                <div class="flex items-center space-x-3">
                    <div class="w-2 h-2 bg-accent rounded-full"></div>
                    <div class="flex-1">
                        <p class="text-sm text-gray-900"><?= htmlspecialchars($activity['action']) ?></p>
                        <p class="text-xs text-gray-500"><?= htmlspecialchars($activity['user']) ?> • <?= htmlspecialchars($activity['time']) ?></p>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
            <div class="mt-6">
                <a href="?page=messages" class="text-accent hover:text-blue-600 text-sm font-medium">
                    View all messages →
                </a>
            </div>
        </div>
    </div>

    <!-- 热门工具 -->
    <div class="admin-card">
        <div class="p-6 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Popular Tools</h3>
        </div>
        <div class="p-6">
            <div class="space-y-4">
                <?php foreach ($popularTools as $tool): ?>
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-900"><?= htmlspecialchars($tool['name']) ?></p>
                        <p class="text-xs text-gray-500"><?= number_format($tool['uses']) ?> uses</p>
                    </div>
                    <div class="text-right">
                        <span class="text-sm font-medium text-green-600"><?= htmlspecialchars($tool['trend']) ?></span>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
            <div class="mt-6">
                <a href="?page=tools" class="text-accent hover:text-blue-600 text-sm font-medium">
                    Manage tools →
                </a>
            </div>
        </div>
    </div>
</div>

<!-- 数据库清理工具 -->
<div class="mt-8">
    <div class="admin-card p-6">
        <div class="flex items-center justify-between mb-6">
            <h3 class="text-lg font-semibold text-gray-900">Database Cleanup Tools</h3>
            <div class="text-sm text-gray-500">
                <i class="fas fa-info-circle mr-1"></i>
                Core business data will not be deleted
            </div>
        </div>

        <!-- 一键清空所有 -->
        <div class="mb-6 p-4 bg-red-50 border border-red-200 rounded">
            <div class="flex items-center justify-between">
                <div>
                    <h4 class="text-red-800 font-medium">🚨 Clean All Temporary Data</h4>
                    <p class="text-red-600 text-sm mt-1">Clear all logs, sessions and notifications (core business data will not be affected)</p>
                </div>
                <button onclick="cleanAllData()" class="bg-red-600 text-white px-4 py-2 hover:bg-red-700 transition-colors rounded">
                    <i class="fas fa-trash-alt mr-2"></i>Clean All
                </button>
            </div>
        </div>

        <!-- 分类清理 -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <!-- 日志类表 (高优先级) -->
            <div class="border border-red-300 rounded p-4 bg-red-50">
                <div class="flex items-center mb-3">
                    <i class="fas fa-file-alt text-red-600 mr-2"></i>
                    <h4 class="font-medium text-red-800">Log Data</h4>
                    <span class="ml-2 px-2 py-1 bg-red-200 text-red-800 text-xs rounded">High Priority</span>
                </div>
                <div class="space-y-2 mb-4">
                    <div class="text-sm text-red-700">• Admin activity logs</div>
                    <div class="text-sm text-red-700">• User activity logs</div>
                    <div class="text-sm text-red-700">• API access logs</div>
                    <div class="text-sm text-red-700">• Analytics events</div>
                    <div class="text-sm text-red-700">• API usage statistics</div>
                    <div class="text-sm text-red-700">• Tool usage records</div>
                    <div class="text-sm text-red-700">• API rate limit records</div>
                </div>
                <button onclick="cleanLogData()" class="w-full bg-red-600 text-white py-2 hover:bg-red-700 transition-colors rounded">
                    <i class="fas fa-broom mr-2"></i>Clean Log Data
                </button>
            </div>

            <!-- 会话类表 (中优先级) -->
            <div class="border border-orange-300 rounded p-4 bg-orange-50">
                <div class="flex items-center mb-3">
                    <i class="fas fa-user-clock text-orange-600 mr-2"></i>
                    <h4 class="font-medium text-orange-800">Session Data</h4>
                    <span class="ml-2 px-2 py-1 bg-orange-200 text-orange-800 text-xs rounded">Medium Priority</span>
                </div>
                <div class="space-y-2 mb-4">
                    <div class="text-sm text-orange-700">• All admin sessions</div>
                    <div class="text-sm text-orange-700">• All user sessions</div>
                    <div class="text-sm text-orange-700">• All remember tokens</div>
                    <div class="text-sm text-orange-700">• All user tokens</div>
                </div>
                <button onclick="cleanSessionData()" class="w-full bg-orange-600 text-white py-2 hover:bg-orange-700 transition-colors rounded">
                    <i class="fas fa-clock mr-2"></i>Clean Session Data
                </button>
            </div>

            <!-- 通知类表 (低优先级) -->
            <div class="border border-yellow-300 rounded p-4 bg-yellow-50">
                <div class="flex items-center mb-3">
                    <i class="fas fa-bell text-yellow-600 mr-2"></i>
                    <h4 class="font-medium text-yellow-800">Notification Data</h4>
                    <span class="ml-2 px-2 py-1 bg-yellow-200 text-yellow-800 text-xs rounded">Low Priority</span>
                </div>
                <div class="space-y-2 mb-4">
                    <div class="text-sm text-yellow-700">• All user notifications</div>
                    <div class="text-sm text-yellow-700">• All contact messages</div>
                </div>
                <button onclick="cleanNotificationData()" class="w-full bg-yellow-600 text-white py-2 hover:bg-yellow-700 transition-colors rounded">
                    <i class="fas fa-bell-slash mr-2"></i>Clean Notification Data
                </button>
            </div>
        </div>

        <!-- 清理状态显示 -->
        <div id="cleaningStatus" class="mt-6 hidden">
            <div class="bg-blue-50 border border-blue-200 rounded p-4">
                <div class="flex items-center">
                    <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-3"></div>
                    <span class="text-blue-800">Cleaning data, please wait...</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 数据库清理 JavaScript -->
<script>
// 显示自定义确认对话框
function showCustomConfirm(title, message, onConfirm) {
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    modal.innerHTML = `
        <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <div class="flex items-center mb-4">
                <i class="fas fa-exclamation-triangle text-red-500 text-xl mr-3"></i>
                <h3 class="text-lg font-semibold text-gray-900">${title}</h3>
            </div>
            <p class="text-gray-600 mb-6 whitespace-pre-line">${message}</p>
            <div class="flex justify-end space-x-3">
                <button onclick="this.closest('.fixed').remove()" class="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400 transition-colors">
                    Cancel
                </button>
                <button onclick="this.closest('.fixed').remove(); (${onConfirm.toString()})()" class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors">
                    Confirm Clean
                </button>
            </div>
        </div>
    `;
    document.body.appendChild(modal);
}

// 显示清理状态
function showCleaningStatus() {
    document.getElementById('cleaningStatus').classList.remove('hidden');
}

// 隐藏清理状态
function hideCleaningStatus() {
    document.getElementById('cleaningStatus').classList.add('hidden');
}

// 显示清理结果
function showCleanResult(success, message, details = '') {
    hideCleaningStatus();

    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    modal.innerHTML = `
        <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <div class="flex items-center mb-4">
                <i class="fas ${success ? 'fa-check-circle text-green-500' : 'fa-times-circle text-red-500'} text-xl mr-3"></i>
                <h3 class="text-lg font-semibold text-gray-900">${success ? 'Clean Completed' : 'Clean Failed'}</h3>
            </div>
            <p class="text-gray-600 mb-4">${message}</p>
            ${details ? `<div class="bg-gray-50 p-3 rounded text-sm text-gray-600 mb-4 whitespace-pre-line">${details}</div>` : ''}
            <div class="flex justify-end">
                <button onclick="this.closest('.fixed').remove()" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors">
                    OK
                </button>
            </div>
        </div>
    `;
    document.body.appendChild(modal);
}

// 执行清理操作
async function performClean(type) {
    showCleaningStatus();

    try {
        const response = await fetch('/control-panel/ajax/database-cleaner.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ action: type })
        });

        const result = await response.json();

        if (result.success) {
            showCleanResult(true, result.message, result.details);
        } else {
            showCleanResult(false, result.message || 'Clean operation failed');
        }
    } catch (error) {
        console.error('Clean error:', error);
        showCleanResult(false, 'Network error, please try again later');
    }
}

// 清理日志数据
function cleanLogData() {
    showCustomConfirm(
        'Clean Log Data',
        'Are you sure you want to clean ALL log data? This will delete:\n• All admin activity logs\n• All user activity logs\n• All API access logs\n• All analytics events\n• All API usage statistics\n• All tool usage records\n• All API rate limit records',
        () => performClean('logs')
    );
}

// 清理会话数据
function cleanSessionData() {
    showCustomConfirm(
        'Clean Session Data',
        'Are you sure you want to clean ALL session data? This will delete:\n• All admin sessions\n• All user sessions\n• All remember tokens\n• All user tokens',
        () => performClean('sessions')
    );
}

// 清理通知数据
function cleanNotificationData() {
    showCustomConfirm(
        'Clean Notification Data',
        'Are you sure you want to clean ALL notification data? This will delete:\n• All user notifications\n• All contact messages',
        () => performClean('notifications')
    );
}

// 一键清空所有数据
function cleanAllData() {
    showCustomConfirm(
        '⚠️ Clean All Data',
        'Are you sure you want to clean ALL temporary data?\n\nThis will execute:\n• Clean all log data\n• Clean all session data\n• Clean all notification data\n\nCore business data will not be affected.',
        () => performClean('all')
    );
}
</script>
