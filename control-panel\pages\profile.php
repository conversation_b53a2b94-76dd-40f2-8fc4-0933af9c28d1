<?php
/**
 * Admin Profile Page
 */

// Security check
if (!defined('SECURE_ACCESS')) {
    exit('Direct access not allowed');
}

// 使用统一的数据库连接
require_once dirname(dirname(__DIR__)) . '/includes/database-connection.php';

// Get current admin data from database
$adminData = [
    'id' => 1,
    'username' => 'admin',
    'email' => '<EMAIL>',
    'display_name' => 'Administrator',
    'role' => 'admin',
    'created_at' => date('Y-m-d H:i:s'),
    'last_login' => date('Y-m-d H:i:s'),
    'status' => 'active'
];

try {
    $adminId = $_SESSION['admin_id'] ?? 1;
    $sql = "SELECT * FROM pt_manager WHERE id = ?";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$adminId]);
    $result = $stmt->fetch();

    if ($result) {
        $adminData = $result;
    }
} catch (PDOException $e) {
    error_log("Database error in profile page: " . $e->getMessage());
}

// Get statistics
$stats = [
    'total_tools' => 0,
    'total_users' => 0,
    'total_sessions' => 0
];

try {
    $stats['total_tools'] = $pdo->query("SELECT COUNT(*) FROM pt_tool")->fetchColumn();
    $stats['total_users'] = $pdo->query("SELECT COUNT(*) FROM pt_member")->fetchColumn();
    $stats['total_sessions'] = $pdo->query("SELECT COUNT(*) FROM pt_manager_session WHERE is_active = 1")->fetchColumn();
} catch (PDOException $e) {
    error_log("Database error getting stats: " . $e->getMessage());
}

// Get recent activities
$recentActivities = [];
try {
    $adminId = $_SESSION['admin_id'] ?? 1;
    $activitySql = "SELECT action, description, icon, color, created_at
                    FROM pt_activity_log
                    WHERE manager_id = ?
                    ORDER BY created_at DESC
                    LIMIT 10";
    $activityStmt = $pdo->prepare($activitySql);
    $activityStmt->execute([$adminId]);
    $recentActivities = $activityStmt->fetchAll();
} catch (PDOException $e) {
    error_log("Database error getting activities: " . $e->getMessage());
    // Fallback to empty array if table doesn't exist
    $recentActivities = [];
}

// Time formatting function
function formatTimeAgo($datetime) {
    if (empty($datetime)) {
        return 'Unknown time';
    }

    $timestamp = strtotime($datetime);
    if ($timestamp === false) {
        return 'Invalid time';
    }

    $diff = time() - $timestamp;

    // Handle future dates (shouldn't happen but just in case)
    if ($diff < 0) {
        return 'Just now';
    }

    if ($diff < 60) {
        return 'Just now';
    } elseif ($diff < 3600) {
        $minutes = floor($diff / 60);
        return $minutes . ' minute' . ($minutes > 1 ? 's' : '') . ' ago';
    } elseif ($diff < 86400) {
        $hours = floor($diff / 3600);
        return $hours . ' hour' . ($hours > 1 ? 's' : '') . ' ago';
    } elseif ($diff < 2592000) { // 30 days
        $days = floor($diff / 86400);
        return $days . ' day' . ($days > 1 ? 's' : '') . ' ago';
    } else {
        // For older dates, show the actual date
        return date('M j, Y', $timestamp);
    }
}
?>

<div class="max-w-4xl mx-auto">
    <div class="mb-6">
        <h1 class="text-2xl font-semibold text-gray-900">Profile</h1>
        <p class="text-gray-600 mt-2">View and manage your profile information</p>
    </div>

    <!-- Profile Overview -->
    <div class="bg-white shadow border border-gray-200 mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-medium text-gray-900">Profile Overview</h2>
        </div>
        <div class="p-6">
            <div class="flex items-center space-x-6">
                <!-- Avatar -->
                <div class="flex-shrink-0">
                    <div class="w-20 h-20 bg-black flex items-center justify-center text-white text-2xl font-bold">
                        <?= strtoupper(substr($adminData['display_name'] ?? $adminData['username'], 0, 1)) ?>
                    </div>
                </div>

                <!-- Basic Info -->
                <div class="flex-1">
                    <h3 class="text-xl font-semibold text-gray-900"><?= htmlspecialchars($adminData['display_name'] ?? $adminData['username'] ?? 'Administrator') ?></h3>
                    <p class="text-gray-600"><?= htmlspecialchars($adminData['email'] ?? '<EMAIL>') ?></p>
                    <p class="text-sm text-gray-500 mt-1"><?= ucfirst($adminData['role'] ?? 'admin') ?> Administrator</p>

                    <div class="mt-4">
                        <span class="inline-flex px-2 py-1 text-xs font-medium <?= $adminData['status'] === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' ?>">
                            <?= ucfirst($adminData['status']) ?>
                        </span>
                    </div>
                </div>
                
                <!-- Actions -->
                <div class="flex-shrink-0">
                    <a href="?page=account" class="bg-accent text-white px-4 py-2 text-sm font-medium hover:bg-blue-700">
                        Edit Profile
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Account Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <div class="bg-white shadow border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-calendar-alt text-2xl text-accent"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Member Since</p>
                    <p class="text-lg font-semibold text-gray-900"><?= (!empty($adminData['created_at'])) ? date('M j, Y', strtotime($adminData['created_at'])) : 'N/A' ?></p>
                </div>
            </div>
        </div>

        <div class="bg-white shadow border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-clock text-2xl text-green-500"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Last Login</p>
                    <p class="text-lg font-semibold text-gray-900"><?= (!empty($adminData['last_login'])) ? date('M j, Y H:i', strtotime($adminData['last_login'])) : 'Never' ?></p>
                </div>
            </div>
        </div>

        <div class="bg-white shadow border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <i class="fas fa-tools text-2xl text-blue-500"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">AI Tools</p>
                    <p class="text-lg font-semibold text-gray-900"><?= $stats['total_tools'] ?></p>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="bg-white shadow border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-medium text-gray-900">Recent Activity</h2>
        </div>
        <div class="p-6">
            <div class="space-y-4">
                <?php if (!empty($recentActivities)): ?>
                    <?php foreach ($recentActivities as $activity): ?>
                        <div class="flex items-center space-x-3">
                            <div class="flex-shrink-0">
                                <i class="<?= htmlspecialchars($activity['icon'] ?? 'fas fa-info-circle') ?> text-<?= htmlspecialchars($activity['color'] ?? 'blue') ?>-500"></i>
                            </div>
                            <div class="flex-1">
                                <p class="text-sm text-gray-900"><?= htmlspecialchars($activity['description'] ?? 'Unknown activity') ?></p>
                                <p class="text-xs text-gray-500"><?= formatTimeAgo($activity['created_at']) ?></p>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="text-center py-8">
                        <i class="fas fa-history text-gray-400 text-3xl mb-3"></i>
                        <p class="text-gray-500">No recent activity found</p>
                        <p class="text-xs text-gray-400 mt-1">Activity will appear here as you use the system</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
