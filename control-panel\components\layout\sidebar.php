<?php
/**
 * 管理后台侧边栏组件
 * 包含导航菜单和品牌信息
 */

$currentPage = $_GET['page'] ?? 'dashboard';

// 获取工具分类数据
try {
    // 使用统一的数据库连接
    require_once dirname(dirname(dirname(__DIR__))) . '/includes/database-connection.php';
    $stmt = $pdo->query("SELECT * FROM pt_tool_category WHERE status = 'active' ORDER BY sort_order, name");
    $toolCategories = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $toolCategories = [];
}
?>

<!-- 侧边栏头部 -->
<div class="flex items-center h-16 px-4 bg-secondary border-b border-gray-800">
    <div class="flex items-center space-x-3">
        <div class="w-8 h-8 bg-white text-black flex items-center justify-center font-bold text-lg">
            P
        </div>
        <span class="text-white font-semibold text-lg">Control Panel</span>
    </div>
</div>

<!-- 导航菜单 -->
<nav class="mt-8">
    <div class="px-4 space-y-2">
        <!-- 仪表板 -->
        <a href="?page=dashboard" 
           class="sidebar-link flex items-center px-4 py-3 text-gray-300 hover:text-white group <?= $currentPage === 'dashboard' ? 'active' : '' ?>">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z"></path>
            </svg>
            Dashboard
        </a>

        <!-- 工具管理 -->
        <a href="?page=tools"
           class="sidebar-link flex items-center px-4 py-3 text-gray-300 hover:text-white group <?= $currentPage === 'tools' ? 'active' : '' ?>">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
            </svg>
            Tools
        </a>



        <!-- API -->
        <a href="?page=api"
           class="sidebar-link flex items-center px-4 py-3 text-gray-300 hover:text-white group <?= $currentPage === 'api' ? 'active' : '' ?>">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1117 9z"></path>
            </svg>
            API
        </a>

        <!-- Prompt -->
        <a href="?page=prompt"
           class="sidebar-link flex items-center px-4 py-3 text-gray-300 hover:text-white group <?= $currentPage === 'prompt' ? 'active' : '' ?>">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
            </svg>
            Prompt
        </a>

        <!-- Program Generator -->
        <a href="?page=program"
           class="sidebar-link flex items-center px-4 py-3 text-gray-300 hover:text-white group <?= $currentPage === 'program' ? 'active' : '' ?>">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
            </svg>
            Program
        </a>


        <!-- 工具分类管理 -->
        <a href="?page=tool-categories"
           class="sidebar-link flex items-center px-4 py-3 text-gray-300 hover:text-white group <?= $currentPage === 'tool-categories' ? 'active' : '' ?>">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14-7H5a2 2 0 00-2 2v14a2 2 0 002 2h14a2 2 0 002-2V6a2 2 0 00-2-2zM9 7h6m-6 4h6m-7 4h8"></path>
            </svg>
            Categories
        </a>

        <!-- 用户管理 -->
        <a href="?page=users"
           class="sidebar-link flex items-center px-4 py-3 text-gray-300 hover:text-white group <?= $currentPage === 'users' ? 'active' : '' ?>">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
            </svg>
            User
        </a>

        <!-- 需求管理 -->
        <a href="?page=user-requests"
           class="sidebar-link flex items-center px-4 py-3 text-gray-300 hover:text-white group <?= $currentPage === 'user-requests' ? 'active' : '' ?>">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
            </svg>
            Requests
        </a>

        <!-- 产品发布管理 -->
        <a href="?page=launches"
           class="sidebar-link flex items-center px-4 py-3 text-gray-300 hover:text-white group <?= $currentPage === 'launches' ? 'active' : '' ?>">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
            </svg>
            Launches
        </a>

        <!-- 数据分析 -->
        <a href="?page=analytics"
           class="sidebar-link flex items-center px-4 py-3 text-gray-300 hover:text-white group <?= $currentPage === 'analytics' ? 'active' : '' ?>">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
            </svg>
            Analytics
        </a>

        <!-- 联系表单 -->
        <a href="?page=messages"
           class="sidebar-link flex items-center px-4 py-3 text-gray-300 hover:text-white group <?= $currentPage === 'messages' ? 'active' : '' ?>">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
            </svg>
            Messages
        </a>

        <!-- Sitemap -->
        <a href="?page=sitemap"
           class="sidebar-link flex items-center px-4 py-3 text-gray-300 hover:text-white group <?= $currentPage === 'sitemap' ? 'active' : '' ?>">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            Sitemap
        </a>

        <!-- 系统设置 -->
        <a href="?page=settings" 
           class="sidebar-link flex items-center px-4 py-3 text-gray-300 hover:text-white group <?= $currentPage === 'settings' ? 'active' : '' ?>">
            <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
            </svg>
            Settings
        </a>
    </div>


</nav>


