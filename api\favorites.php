<?php
/**
 * 用户收藏管理API
 * 处理添加/删除收藏、检查收藏状态等操作
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET');
header('Access-Control-Allow-Headers: Content-Type');

// 使用统一的数据库连接
require_once dirname(__DIR__) . '/includes/database-connection.php';

try {

    // 启动会话
    session_start();

    // 检查用户是否登录
    if (!isset($_SESSION['user_id'])) {
        echo json_encode([
            'success' => false,
            'message' => 'Please login to use favorites feature',
            'error_code' => 'NOT_LOGGED_IN'
        ]);
        exit;
    }

    $userId = $_SESSION['user_id'];
    $action = $_POST['action'] ?? $_GET['action'] ?? '';
    $toolId = intval($_POST['tool_id'] ?? $_GET['tool_id'] ?? 0);

    // 验证工具ID
    if ($toolId <= 0) {
        echo json_encode([
            'success' => false,
            'message' => 'Invalid tool ID',
            'error_code' => 'INVALID_TOOL_ID'
        ]);
        exit;
    }

    // 验证工具是否存在且状态为active
    $stmt = $pdo->prepare("SELECT id, name FROM pt_tool WHERE id = ? AND status = 'active'");
    $stmt->execute([$toolId]);
    $tool = $stmt->fetch();

    if (!$tool) {
        echo json_encode([
            'success' => false,
            'message' => 'Tool not found or inactive',
            'error_code' => 'TOOL_NOT_FOUND'
        ]);
        exit;
    }

    switch ($action) {
        case 'add':
            // 添加收藏
            try {
                // 检查是否已经收藏
                $stmt = $pdo->prepare("SELECT COUNT(*) FROM pt_user_favorites WHERE user_id = ? AND tool_id = ?");
                $stmt->execute([$userId, $toolId]);
                $exists = $stmt->fetchColumn();

                if ($exists) {
                    echo json_encode([
                        'success' => false,
                        'message' => 'Tool is already in your favorites',
                        'error_code' => 'ALREADY_FAVORITED'
                    ]);
                    exit;
                }

                // 添加收藏
                $stmt = $pdo->prepare("INSERT INTO pt_user_favorites (user_id, tool_id) VALUES (?, ?)");
                $stmt->execute([$userId, $toolId]);

                // 记录活动日志
                $stmt = $pdo->prepare("
                    INSERT INTO pt_member_activity_log (user_id, action, description, ip_address, created_at) 
                    VALUES (?, 'tool_favorite', ?, ?, NOW())
                ");
                $stmt->execute([
                    $userId, 
                    "Added '{$tool['name']}' to favorites", 
                    $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1'
                ]);

                echo json_encode([
                    'success' => true,
                    'message' => 'Tool added to favorites successfully',
                    'action' => 'added',
                    'tool_name' => $tool['name']
                ]);

            } catch (PDOException $e) {
                error_log('Add favorite error: ' . $e->getMessage());
                echo json_encode([
                    'success' => false,
                    'message' => 'Failed to add tool to favorites',
                    'error_code' => 'DATABASE_ERROR'
                ]);
            }
            break;

        case 'remove':
            // 删除收藏
            try {
                // 检查是否已收藏
                $stmt = $pdo->prepare("SELECT COUNT(*) FROM pt_user_favorites WHERE user_id = ? AND tool_id = ?");
                $stmt->execute([$userId, $toolId]);
                $exists = $stmt->fetchColumn();

                if (!$exists) {
                    echo json_encode([
                        'success' => false,
                        'message' => 'Tool is not in your favorites',
                        'error_code' => 'NOT_FAVORITED'
                    ]);
                    exit;
                }

                // 删除收藏
                $stmt = $pdo->prepare("DELETE FROM pt_user_favorites WHERE user_id = ? AND tool_id = ?");
                $stmt->execute([$userId, $toolId]);

                // 记录活动日志
                $stmt = $pdo->prepare("
                    INSERT INTO pt_member_activity_log (user_id, action, description, ip_address, created_at) 
                    VALUES (?, 'tool_unfavorite', ?, ?, NOW())
                ");
                $stmt->execute([
                    $userId, 
                    "Removed '{$tool['name']}' from favorites", 
                    $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1'
                ]);

                echo json_encode([
                    'success' => true,
                    'message' => 'Tool removed from favorites successfully',
                    'action' => 'removed',
                    'tool_name' => $tool['name']
                ]);

            } catch (PDOException $e) {
                error_log('Remove favorite error: ' . $e->getMessage());
                echo json_encode([
                    'success' => false,
                    'message' => 'Failed to remove tool from favorites',
                    'error_code' => 'DATABASE_ERROR'
                ]);
            }
            break;

        case 'check':
            // 检查收藏状态
            try {
                $stmt = $pdo->prepare("SELECT COUNT(*) FROM pt_user_favorites WHERE user_id = ? AND tool_id = ?");
                $stmt->execute([$userId, $toolId]);
                $isFavorited = $stmt->fetchColumn() > 0;

                echo json_encode([
                    'success' => true,
                    'is_favorited' => $isFavorited,
                    'tool_id' => $toolId,
                    'tool_name' => $tool['name']
                ]);

            } catch (PDOException $e) {
                error_log('Check favorite error: ' . $e->getMessage());
                echo json_encode([
                    'success' => false,
                    'message' => 'Failed to check favorite status',
                    'error_code' => 'DATABASE_ERROR'
                ]);
            }
            break;

        case 'toggle':
            // 切换收藏状态
            try {
                // 检查当前状态
                $stmt = $pdo->prepare("SELECT COUNT(*) FROM pt_user_favorites WHERE user_id = ? AND tool_id = ?");
                $stmt->execute([$userId, $toolId]);
                $isFavorited = $stmt->fetchColumn() > 0;

                if ($isFavorited) {
                    // 删除收藏
                    $stmt = $pdo->prepare("DELETE FROM pt_user_favorites WHERE user_id = ? AND tool_id = ?");
                    $stmt->execute([$userId, $toolId]);

                    // 记录活动日志
                    $stmt = $pdo->prepare("
                        INSERT INTO pt_member_activity_log (user_id, action, description, ip_address, created_at) 
                        VALUES (?, 'tool_unfavorite', ?, ?, NOW())
                    ");
                    $stmt->execute([
                        $userId, 
                        "Removed '{$tool['name']}' from favorites", 
                        $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1'
                    ]);

                    echo json_encode([
                        'success' => true,
                        'message' => 'Tool removed from favorites',
                        'action' => 'removed',
                        'is_favorited' => false,
                        'tool_name' => $tool['name']
                    ]);
                } else {
                    // 添加收藏
                    $stmt = $pdo->prepare("INSERT INTO pt_user_favorites (user_id, tool_id) VALUES (?, ?)");
                    $stmt->execute([$userId, $toolId]);

                    // 记录活动日志
                    $stmt = $pdo->prepare("
                        INSERT INTO pt_member_activity_log (user_id, action, description, ip_address, created_at) 
                        VALUES (?, 'tool_favorite', ?, ?, NOW())
                    ");
                    $stmt->execute([
                        $userId, 
                        "Added '{$tool['name']}' to favorites", 
                        $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1'
                    ]);

                    echo json_encode([
                        'success' => true,
                        'message' => 'Tool added to favorites',
                        'action' => 'added',
                        'is_favorited' => true,
                        'tool_name' => $tool['name']
                    ]);
                }

            } catch (PDOException $e) {
                error_log('Toggle favorite error: ' . $e->getMessage());
                echo json_encode([
                    'success' => false,
                    'message' => 'Failed to update favorite status',
                    'error_code' => 'DATABASE_ERROR'
                ]);
            }
            break;

        default:
            echo json_encode([
                'success' => false,
                'message' => 'Invalid action. Supported actions: add, remove, check, toggle',
                'error_code' => 'INVALID_ACTION'
            ]);
            break;
    }

} catch (PDOException $e) {
    error_log('Database connection error in favorites API: ' . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => 'Database connection failed',
        'error_code' => 'DATABASE_CONNECTION_ERROR'
    ]);
} catch (Exception $e) {
    error_log('General error in favorites API: ' . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => 'An unexpected error occurred',
        'error_code' => 'GENERAL_ERROR'
    ]);
}
?>
