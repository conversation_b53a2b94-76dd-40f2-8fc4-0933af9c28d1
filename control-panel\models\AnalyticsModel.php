<?php
/**
 * 分析模型类
 * 处理数据分析和统计相关的数据库操作
 */

// 防止直接访问
if (!defined('ROOT_PATH')) {
    exit('Direct access not allowed');
}

// 加载基础模型
require_once ROOT_PATH . '/control-panel/models/BaseModel.php';

class AnalyticsModel extends BaseModel {
    
    protected $table = 'pt_analytics_event';
    protected $primaryKey = 'id';
    protected $fillable = [
        'event_type', 'event_name', 'user_id', 'session_id',
        'ip_address', 'user_agent', 'referer', 'url',
        'properties', 'created_at'
    ];
    
    /**
     * 记录分析事件
     */
    public function recordEvent($eventType, $eventName, $properties = [], $userId = null) {
        $data = [
            'event_type' => $eventType,
            'event_name' => $eventName,
            'user_id' => $userId,
            'session_id' => session_id(),
            'ip_address' => $this->getClientIP(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'referer' => $_SERVER['HTTP_REFERER'] ?? '',
            'url' => $_SERVER['REQUEST_URI'] ?? '',
            'properties' => json_encode($properties),
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        return $this->create($data);
    }
    
    /**
     * 获取网站概览统计
     */
    public function getOverviewStats($days = 30) {
        $stats = [
            'page_views' => 0,
            'unique_visitors' => 0,
            'tool_usage' => 0,
            'user_registrations' => 0,
            'bounce_rate' => 0,
            'avg_session_duration' => 0
        ];
        
        $dateFrom = date('Y-m-d', strtotime("-{$days} days"));
        
        // 页面浏览量
        $sql = "
            SELECT COUNT(*) 
            FROM {$this->table} 
            WHERE event_type = 'page_view' 
            AND DATE(created_at) >= :date_from
        ";
        $stats['page_views'] = $this->db->fetchColumn($sql, ['date_from' => $dateFrom]);
        
        // 独立访客
        $sql = "
            SELECT COUNT(DISTINCT ip_address) 
            FROM {$this->table} 
            WHERE event_type = 'page_view' 
            AND DATE(created_at) >= :date_from
        ";
        $stats['unique_visitors'] = $this->db->fetchColumn($sql, ['date_from' => $dateFrom]);
        
        // 工具使用次数
        $sql = "
            SELECT COUNT(*)
            FROM pt_tool_usage
            WHERE DATE(created_at) >= :date_from
        ";
        $stats['tool_usage'] = $this->db->fetchColumn($sql, ['date_from' => $dateFrom]);

        // 用户注册数
        $sql = "
            SELECT COUNT(*)
            FROM pt_member
            WHERE DATE(created_at) >= :date_from
        ";
        $stats['user_registrations'] = $this->db->fetchColumn($sql, ['date_from' => $dateFrom]);
        
        // 跳出率（简化计算）
        $sql = "
            SELECT 
                COUNT(CASE WHEN page_count = 1 THEN 1 END) * 100.0 / COUNT(*) as bounce_rate
            FROM (
                SELECT session_id, COUNT(*) as page_count
                FROM {$this->table}
                WHERE event_type = 'page_view'
                AND DATE(created_at) >= :date_from
                GROUP BY session_id
            ) sessions
        ";
        $bounceResult = $this->db->fetch($sql, ['date_from' => $dateFrom]);
        $stats['bounce_rate'] = round($bounceResult['bounce_rate'] ?? 0, 2);
        
        return $stats;
    }
    
    /**
     * 获取访问趋势数据
     */
    public function getVisitTrend($days = 30) {
        $sql = "
            SELECT 
                DATE(created_at) as date,
                COUNT(*) as page_views,
                COUNT(DISTINCT ip_address) as unique_visitors
            FROM {$this->table}
            WHERE event_type = 'page_view'
            AND DATE(created_at) >= DATE_SUB(NOW(), INTERVAL :days DAY)
            GROUP BY DATE(created_at)
            ORDER BY date ASC
        ";
        
        return $this->db->fetchAll($sql, ['days' => $days]);
    }
    
    /**
     * 获取热门页面
     */
    public function getTopPages($days = 30, $limit = 20) {
        $sql = "
            SELECT 
                url,
                COUNT(*) as page_views,
                COUNT(DISTINCT ip_address) as unique_visitors
            FROM {$this->table}
            WHERE event_type = 'page_view'
            AND DATE(created_at) >= DATE_SUB(NOW(), INTERVAL :days DAY)
            GROUP BY url
            ORDER BY page_views DESC
            LIMIT :limit
        ";
        
        return $this->db->fetchAll($sql, ['days' => $days, 'limit' => $limit]);
    }
    
    /**
     * 获取流量来源
     */
    public function getTrafficSources($days = 30) {
        $sql = "
            SELECT 
                CASE 
                    WHEN referer = '' OR referer IS NULL THEN 'Direct'
                    WHEN referer LIKE '%google%' THEN 'Google'
                    WHEN referer LIKE '%bing%' THEN 'Bing'
                    WHEN referer LIKE '%yahoo%' THEN 'Yahoo'
                    WHEN referer LIKE '%facebook%' THEN 'Facebook'
                    WHEN referer LIKE '%twitter%' THEN 'Twitter'
                    WHEN referer LIKE '%linkedin%' THEN 'LinkedIn'
                    ELSE 'Other'
                END as source,
                COUNT(*) as visits,
                COUNT(DISTINCT ip_address) as unique_visitors
            FROM {$this->table}
            WHERE event_type = 'page_view'
            AND DATE(created_at) >= DATE_SUB(NOW(), INTERVAL :days DAY)
            GROUP BY source
            ORDER BY visits DESC
        ";
        
        return $this->db->fetchAll($sql, ['days' => $days]);
    }
    
    /**
     * 获取设备统计
     */
    public function getDeviceStats($days = 30) {
        $sql = "
            SELECT 
                CASE 
                    WHEN user_agent LIKE '%Mobile%' OR user_agent LIKE '%Android%' OR user_agent LIKE '%iPhone%' THEN 'Mobile'
                    WHEN user_agent LIKE '%Tablet%' OR user_agent LIKE '%iPad%' THEN 'Tablet'
                    ELSE 'Desktop'
                END as device_type,
                COUNT(*) as visits,
                COUNT(DISTINCT ip_address) as unique_visitors
            FROM {$this->table}
            WHERE event_type = 'page_view'
            AND DATE(created_at) >= DATE_SUB(NOW(), INTERVAL :days DAY)
            GROUP BY device_type
            ORDER BY visits DESC
        ";
        
        return $this->db->fetchAll($sql, ['days' => $days]);
    }
    
    /**
     * 获取浏览器统计
     */
    public function getBrowserStats($days = 30) {
        $sql = "
            SELECT 
                CASE 
                    WHEN user_agent LIKE '%Chrome%' AND user_agent NOT LIKE '%Edge%' THEN 'Chrome'
                    WHEN user_agent LIKE '%Firefox%' THEN 'Firefox'
                    WHEN user_agent LIKE '%Safari%' AND user_agent NOT LIKE '%Chrome%' THEN 'Safari'
                    WHEN user_agent LIKE '%Edge%' THEN 'Edge'
                    WHEN user_agent LIKE '%Opera%' THEN 'Opera'
                    ELSE 'Other'
                END as browser,
                COUNT(*) as visits,
                COUNT(DISTINCT ip_address) as unique_visitors
            FROM {$this->table}
            WHERE event_type = 'page_view'
            AND DATE(created_at) >= DATE_SUB(NOW(), INTERVAL :days DAY)
            GROUP BY browser
            ORDER BY visits DESC
        ";
        
        return $this->db->fetchAll($sql, ['days' => $days]);
    }
    
    /**
     * 获取地理位置统计
     */
    public function getGeographicStats($days = 30) {
        // 这里需要IP地理位置数据库，暂时返回模拟数据
        $sql = "
            SELECT 
                'Unknown' as country,
                COUNT(*) as visits,
                COUNT(DISTINCT ip_address) as unique_visitors
            FROM {$this->table}
            WHERE event_type = 'page_view'
            AND DATE(created_at) >= DATE_SUB(NOW(), INTERVAL :days DAY)
            GROUP BY ip_address
            LIMIT 1
        ";
        
        return $this->db->fetchAll($sql, ['days' => $days]);
    }
    
    /**
     * 获取实时访客
     */
    public function getRealTimeVisitors($minutes = 30) {
        $sql = "
            SELECT COUNT(DISTINCT ip_address) as active_visitors
            FROM {$this->table}
            WHERE event_type = 'page_view'
            AND created_at >= DATE_SUB(NOW(), INTERVAL :minutes MINUTE)
        ";
        
        return $this->db->fetchColumn($sql, ['minutes' => $minutes]);
    }
    
    /**
     * 获取用户行为流
     */
    public function getUserFlow($days = 7) {
        $sql = "
            SELECT 
                session_id,
                url,
                created_at,
                ROW_NUMBER() OVER (PARTITION BY session_id ORDER BY created_at) as step_number
            FROM {$this->table}
            WHERE event_type = 'page_view'
            AND DATE(created_at) >= DATE_SUB(NOW(), INTERVAL :days DAY)
            ORDER BY session_id, created_at
        ";
        
        $results = $this->db->fetchAll($sql, ['days' => $days]);
        
        // 分析页面流转
        $flows = [];
        $currentSession = null;
        $currentPath = [];
        
        foreach ($results as $row) {
            if ($currentSession !== $row['session_id']) {
                if (!empty($currentPath)) {
                    $flowKey = implode(' -> ', $currentPath);
                    $flows[$flowKey] = ($flows[$flowKey] ?? 0) + 1;
                }
                $currentSession = $row['session_id'];
                $currentPath = [$row['url']];
            } else {
                $currentPath[] = $row['url'];
                if (count($currentPath) > 5) { // 限制路径长度
                    array_shift($currentPath);
                }
            }
        }
        
        // 处理最后一个会话
        if (!empty($currentPath)) {
            $flowKey = implode(' -> ', $currentPath);
            $flows[$flowKey] = ($flows[$flowKey] ?? 0) + 1;
        }
        
        // 排序并返回前20个流程
        arsort($flows);
        return array_slice($flows, 0, 20, true);
    }
    
    /**
     * 获取转化漏斗
     */
    public function getConversionFunnel($steps, $days = 30) {
        $results = [];
        
        foreach ($steps as $index => $step) {
            $sql = "
                SELECT COUNT(DISTINCT session_id) as count
                FROM {$this->table}
                WHERE event_name = :event_name
                AND DATE(created_at) >= DATE_SUB(NOW(), INTERVAL :days DAY)
            ";
            
            $count = $this->db->fetchColumn($sql, [
                'event_name' => $step,
                'days' => $days
            ]);
            
            $results[] = [
                'step' => $step,
                'count' => $count,
                'conversion_rate' => $index === 0 ? 100 : ($count / $results[0]['count'] * 100)
            ];
        }
        
        return $results;
    }
    
    /**
     * 获取事件统计
     */
    public function getEventStats($eventType = null, $days = 30) {
        $sql = "
            SELECT 
                event_name,
                COUNT(*) as count,
                COUNT(DISTINCT user_id) as unique_users
            FROM {$this->table}
            WHERE DATE(created_at) >= DATE_SUB(NOW(), INTERVAL :days DAY)
        ";
        
        $params = ['days' => $days];
        
        if ($eventType) {
            $sql .= " AND event_type = :event_type";
            $params['event_type'] = $eventType;
        }
        
        $sql .= " GROUP BY event_name ORDER BY count DESC";
        
        return $this->db->fetchAll($sql, $params);
    }
    
    /**
     * 获取用户留存率
     */
    public function getRetentionRate($days = 30) {
        $sql = "
            SELECT 
                DATE(first_visit) as cohort_date,
                COUNT(*) as users,
                COUNT(CASE WHEN return_visit IS NOT NULL THEN 1 END) as retained_users
            FROM (
                SELECT 
                    ip_address,
                    MIN(DATE(created_at)) as first_visit,
                    CASE 
                        WHEN COUNT(DISTINCT DATE(created_at)) > 1 THEN MIN(DATE(created_at))
                        ELSE NULL 
                    END as return_visit
                FROM {$this->table}
                WHERE event_type = 'page_view'
                AND DATE(created_at) >= DATE_SUB(NOW(), INTERVAL :days DAY)
                GROUP BY ip_address
            ) cohorts
            GROUP BY DATE(first_visit)
            ORDER BY cohort_date DESC
        ";
        
        return $this->db->fetchAll($sql, ['days' => $days]);
    }
    
    /**
     * 清理旧数据
     */
    public function cleanupOldData($days = 90) {
        $sql = "DELETE FROM {$this->table} WHERE created_at < DATE_SUB(NOW(), INTERVAL :days DAY)";
        $stmt = $this->db->query($sql, ['days' => $days]);
        return $stmt->rowCount();
    }
    
    /**
     * 获取客户端IP地址
     */
    private function getClientIP() {
        $ipKeys = [
            'HTTP_CF_CONNECTING_IP',
            'HTTP_CLIENT_IP',
            'HTTP_X_FORWARDED_FOR',
            'HTTP_X_FORWARDED',
            'HTTP_X_CLUSTER_CLIENT_IP',
            'HTTP_FORWARDED_FOR',
            'HTTP_FORWARDED',
            'REMOTE_ADDR'
        ];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }
    
    /**
     * 生成分析报告
     */
    public function generateReport($startDate, $endDate) {
        $report = [
            'period' => [
                'start' => $startDate,
                'end' => $endDate
            ],
            'overview' => $this->getOverviewStatsForPeriod($startDate, $endDate),
            'traffic_sources' => $this->getTrafficSourcesForPeriod($startDate, $endDate),
            'top_pages' => $this->getTopPagesForPeriod($startDate, $endDate),
            'device_stats' => $this->getDeviceStatsForPeriod($startDate, $endDate),
            'browser_stats' => $this->getBrowserStatsForPeriod($startDate, $endDate)
        ];
        
        return $report;
    }
    
    /**
     * 获取指定时期的概览统计
     */
    private function getOverviewStatsForPeriod($startDate, $endDate) {
        // 实现指定时期的统计逻辑
        return $this->getOverviewStats(30); // 简化实现
    }
    
    /**
     * 获取指定时期的流量来源
     */
    private function getTrafficSourcesForPeriod($startDate, $endDate) {
        // 实现指定时期的流量来源逻辑
        return $this->getTrafficSources(30); // 简化实现
    }
    
    /**
     * 获取指定时期的热门页面
     */
    private function getTopPagesForPeriod($startDate, $endDate) {
        // 实现指定时期的热门页面逻辑
        return $this->getTopPages(30); // 简化实现
    }
    
    /**
     * 获取指定时期的设备统计
     */
    private function getDeviceStatsForPeriod($startDate, $endDate) {
        // 实现指定时期的设备统计逻辑
        return $this->getDeviceStats(30); // 简化实现
    }
    
    /**
     * 获取指定时期的浏览器统计
     */
    private function getBrowserStatsForPeriod($startDate, $endDate) {
        // 实现指定时期的浏览器统计逻辑
        return $this->getBrowserStats(30); // 简化实现
    }
}
?>
