<?php
/**
 * 保存工具源文件API
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// 检查登录状态
session_start();
if (!isset($_SESSION['admin_logged_in']) || !$_SESSION['admin_logged_in']) {
    http_response_code(401);
    echo json_encode(['error' => 'Unauthorized']);
    exit;
}

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

// 获取POST数据
$input = json_decode(file_get_contents('php://input'), true);
$toolSlug = $input['tool_slug'] ?? '';
$content = $input['content'] ?? '';

if (empty($toolSlug)) {
    http_response_code(400);
    echo json_encode(['error' => 'Tool slug is required']);
    exit;
}

// 验证slug格式（安全检查）
if (!preg_match('/^[a-z0-9-]+$/', $toolSlug)) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid tool slug format']);
    exit;
}

try {
    // 构建源文件路径
    $sourceFilePath = dirname(__DIR__) . '/templates/pages/tools/' . $toolSlug . '.php';
    $sourceDir = dirname($sourceFilePath);
    
    // 确保目录存在
    if (!is_dir($sourceDir)) {
        if (!mkdir($sourceDir, 0755, true)) {
            throw new Exception('Failed to create directory');
        }
    }
    
    // 保存文件
    if (file_put_contents($sourceFilePath, $content) === false) {
        throw new Exception('Failed to save source file');
    }
    
    echo json_encode([
        'success' => true,
        'message' => 'Source file saved successfully',
        'path' => '/templates/pages/tools/' . $toolSlug . '.php'
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Error saving source file: ' . $e->getMessage()]);
}
?>
