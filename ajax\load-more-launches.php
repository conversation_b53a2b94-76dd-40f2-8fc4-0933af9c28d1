<?php
/**
 * 加载更多产品启动的AJAX处理
 */

// 投票相关函数已移除

/**
 * 获取发布状态颜色样式
 */
function getLaunchStatusBadge($launchStatus) {
    $badges = [
        'coming-soon' => 'bg-blue-100 text-blue-800',
        'beta' => 'bg-purple-100 text-purple-800',
        'launched' => 'bg-green-100 text-green-800'
    ];

    return $badges[$launchStatus] ?? 'bg-gray-100 text-gray-800';
}

/**
 * 获取投票图标和样式
 */
function getLaunchVoteIcon($votes) {
    if ($votes >= 100) {
        return [
            'icon' => '🔥',
            'color' => 'text-orange-400',
            'textColor' => 'text-orange-300',
            'animation' => 'animate-pulse',
            'label' => 'Hot product'
        ];
    } elseif ($votes >= 50) {
        return [
            'icon' => '⭐',
            'color' => 'text-yellow-400',
            'textColor' => 'text-yellow-300',
            'animation' => '',
            'label' => 'Popular product'
        ];
    } elseif ($votes >= 20) {
        return [
            'icon' => '👍',
            'color' => 'text-green-400',
            'textColor' => 'text-green-300',
            'animation' => '',
            'label' => 'Good product'
        ];
    } else {
        return [
            'icon' => '▲',
            'color' => 'text-blue-400',
            'textColor' => 'text-blue-300',
            'animation' => '',
            'label' => 'Vote for this product'
        ];
    }
}

header('Content-Type: application/json');

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit;
}

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// 使用统一的数据库连接
require_once __DIR__ . '/includes/ajax-database.php';

try {

    // 获取参数 - 支持POST和GET
    $categoryFilter = $_POST['category'] ?? $_GET['category'] ?? 'all';
    $launchStatusFilter = $_POST['launch_status'] ?? $_GET['launch_status'] ?? 'all';
    $sort = $_POST['sort'] ?? $_GET['sort'] ?? 'latest';
    $page = max(1, intval($_POST['page'] ?? $_GET['page'] ?? 1));
    $limit = 5;
    $offset = ($page - 1) * $limit;

    // 构建查询条件
    $whereConditions = ["l.status = 'approved'"];
    $params = [];

    if ($categoryFilter !== 'all') {
        $whereConditions[] = "l.category = ?";
        $params[] = $categoryFilter;
    }

    if ($launchStatusFilter !== 'all') {
        $whereConditions[] = "l.launch_status = ?";
        $params[] = $launchStatusFilter;
    }

    $whereClause = 'WHERE ' . implode(' AND ', $whereConditions);

    // 排序逻辑
    $orderBy = match($sort) {
        'votes' => 'l.votes DESC, l.created_at DESC',
        'views' => 'l.views DESC, l.created_at DESC',
        'oldest' => 'l.created_at ASC',
        default => 'l.created_at DESC'
    };

    // 获取产品列表
    $sql = "
        SELECT l.*, m.username, m.first_name, m.last_name
        FROM pt_product_launches l
        LEFT JOIN pt_member m ON l.user_id = m.id
        {$whereClause}
        ORDER BY {$orderBy}
        LIMIT {$limit} OFFSET {$offset}
    ";

    $launches = $pdo->prepare($sql);
    $launches->execute($params);
    $launchesList = $launches->fetchAll();

    // 获取分类数据
    $categoryStmt = $pdo->query("SELECT slug, name FROM pt_launch_categories WHERE is_active = 1 ORDER BY sort_order ASC, name ASC");
    $dbCategories = $categoryStmt->fetchAll();
    $categories = [];
    foreach ($dbCategories as $cat) {
        $categories[$cat['slug']] = $cat['name'];
    }

    // 生成HTML
    ob_start();

    if (!empty($launchesList)) {
        foreach ($launchesList as $launch):
    ?>
        <a href="/launch/<?= htmlspecialchars($launch['slug']) ?>" class="block">
            <div class="bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl p-6 hover:border-gray-600 hover:shadow-lg transition-all duration-300 group">
                <div class="flex items-start space-x-6">
                    <!-- Logo区域 -->
                    <div class="flex-shrink-0">
                        <?php if (!empty($launch['logo_url'])): ?>
                            <img src="<?= htmlspecialchars($launch['logo_url']) ?>"
                                 alt="<?= htmlspecialchars($launch['name']) ?> logo"
                                 class="w-16 h-16 rounded-xl object-cover border border-gray-600">
                        <?php else: ?>
                            <div class="w-16 h-16 bg-gradient-to-br from-gray-700 to-gray-600 rounded-xl flex items-center justify-center border border-gray-600">
                                <i class="fas fa-rocket text-gray-400 text-xl"></i>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- 产品内容 -->
                    <div class="flex-1 min-w-0">
                        <!-- 标题和标签区域 -->
                        <div class="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-3 mb-4">
                            <div>
                                <h3 class="text-xl font-bold text-white leading-tight group-hover:text-blue-300 transition-colors duration-200">
                                    <?= htmlspecialchars($launch['name']) ?>
                                </h3>
                                <?php if (!empty($launch['tagline'])): ?>
                                    <p class="text-gray-400 text-sm mt-1"><?= htmlspecialchars($launch['tagline']) ?></p>
                                <?php endif; ?>
                            </div>

                            <div class="flex items-center gap-2 flex-shrink-0">
                                <span class="px-3 py-1.5 text-xs font-semibold rounded-full <?= getLaunchStatusBadge($launch['launch_status']) ?> shadow-sm">
                                    <?= ucfirst(str_replace('-', ' ', $launch['launch_status'])) ?>
                                </span>
                                <span class="px-3 py-1.5 text-xs font-semibold rounded-full bg-gradient-to-r from-gray-700 to-gray-600 text-gray-200 shadow-sm">
                                    <?= htmlspecialchars($categories[$launch['category']] ?? ucfirst($launch['category'])) ?>
                                </span>
                            </div>
                        </div>

                        <!-- 描述内容 -->
                        <div class="mb-5">
                            <p class="text-gray-300 leading-relaxed line-clamp-3"><?= htmlspecialchars($launch['description']) ?></p>
                        </div>

                        <!-- 元信息 -->
                        <div class="flex items-center space-x-4 text-sm text-gray-400">
                            <span class="flex items-center">
                                <svg class="w-4 h-4 mr-1.5" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                                </svg>
                                <?= htmlspecialchars($launch['username'] ?? 'Anonymous') ?>
                            </span>
                            <span class="flex items-center">
                                <svg class="w-4 h-4 mr-1.5" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                                </svg>
                                <?= date('M j, Y', strtotime($launch['created_at'])) ?>
                            </span>
                            <span class="flex items-center">
                                <svg class="w-4 h-4 mr-1.5" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"/>
                                    <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"/>
                                </svg>
                                <?= number_format($launch['views']) ?> views
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </a>
    <?php
        endforeach;
    }
    $html = ob_get_clean();

    echo json_encode([
        'success' => true,
        'html' => $html,
        'hasMore' => count($launchesList) === $limit
    ]);

} catch (Exception $e) {
    error_log("Load more launches error: " . $e->getMessage());
    error_log("Stack trace: " . $e->getTraceAsString());

    echo json_encode([
        'success' => false,
        'message' => 'Error: ' . $e->getMessage(),
        'debug' => [
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString()
        ]
    ]);
}
?>
