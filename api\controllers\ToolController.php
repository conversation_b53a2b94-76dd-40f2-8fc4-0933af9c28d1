<?php
/**
 * 工具控制器
 * 处理工具相关的API请求
 */

require_once ROOT_PATH . '/control-panel/models/ToolModel.php';

class ToolController {
    
    private $toolModel;
    
    public function __construct() {
        $this->toolModel = new ToolModel();
    }
    
    /**
     * 获取工具列表
     */
    public function index() {
        try {
            $page = intval($_GET['page'] ?? 1);
            $perPage = min(intval($_GET['per_page'] ?? 20), 100);
            $category = $_GET['category'] ?? null;
            $featured = isset($_GET['featured']) ? (bool)$_GET['featured'] : null;
            $premium = isset($_GET['premium']) ? (bool)$_GET['premium'] : null;
            $difficulty = $_GET['difficulty'] ?? null;
            $sort = $_GET['sort'] ?? 'created_at';
            $order = $_GET['order'] ?? 'desc';
            
            // 构建查询条件
            $conditions = ['status' => 'published'];
            
            if ($category) {
                $conditions['category_id'] = $category;
            }
            
            if ($featured !== null) {
                $conditions['featured'] = $featured ? 1 : 0;
            }
            
            if ($premium !== null) {
                $conditions['premium'] = $premium ? 1 : 0;
            }
            
            if ($difficulty) {
                $conditions['difficulty_level'] = $difficulty;
            }
            
            // 获取分页数据
            $result = $this->toolModel->paginate($page, $perPage, $conditions, "{$sort} {$order}");
            
            // 格式化数据
            $tools = array_map([$this, 'formatToolData'], $result['data']);
            
            // 创建分页信息
            $pagination = ApiResponse::createPagination(
                $result['total'],
                $page,
                $perPage,
                '/api/tools'
            );
            
            ApiResponse::paginated($tools, $pagination);
            
        } catch (Exception $e) {
            ApiResponse::handleException($e);
        }
    }
    
    /**
     * 获取单个工具
     */
    public function show($id) {
        try {
            $tool = $this->toolModel->find($id);
            
            if (!$tool || $tool['status'] !== 'published') {
                ApiResponse::notFound('Tool not found');
                return;
            }
            
            // 增加查看次数
            $this->toolModel->incrementUsageCount($id);
            
            // 获取相关工具
            $relatedTools = $this->toolModel->getRelatedTools($id, 5);
            $relatedTools = array_map([$this, 'formatToolData'], $relatedTools);
            
            $toolData = $this->formatToolData($tool, true);
            $toolData['related_tools'] = $relatedTools;
            
            ApiResponse::success($toolData);
            
        } catch (Exception $e) {
            ApiResponse::handleException($e);
        }
    }
    
    /**
     * 创建工具（需要管理员权限）
     */
    public function store() {
        try {
            if (!ApiAuth::isAdmin() || !ApiAuth::hasPermission('tools.create')) {
                ApiResponse::forbidden('Permission denied');
                return;
            }
            
            $input = $this->getJsonInput();
            
            // 验证输入
            $errors = $this->validateToolInput($input);
            if (!empty($errors)) {
                ApiResponse::validationError($errors);
                return;
            }
            
            // 生成唯一slug
            $input['slug'] = $this->toolModel->generateUniqueSlug($input['name']);
            $input['created_by'] = ApiAuth::getUserId();
            
            $tool = $this->toolModel->create($input);
            
            if (!$tool) {
                ApiResponse::serverError('Failed to create tool');
                return;
            }
            
            ApiResponse::success($this->formatToolData($tool), 'Tool created successfully', 201);
            
        } catch (Exception $e) {
            ApiResponse::handleException($e);
        }
    }
    
    /**
     * 更新工具（需要管理员权限）
     */
    public function update($id) {
        try {
            if (!ApiAuth::isAdmin() || !ApiAuth::hasPermission('tools.edit')) {
                ApiResponse::forbidden('Permission denied');
                return;
            }
            
            $tool = $this->toolModel->find($id);
            if (!$tool) {
                ApiResponse::notFound('Tool not found');
                return;
            }
            
            $input = $this->getJsonInput();
            
            // 验证输入
            $errors = $this->validateToolInput($input, $id);
            if (!empty($errors)) {
                ApiResponse::validationError($errors);
                return;
            }
            
            $input['updated_by'] = ApiAuth::getUserId();
            
            $updatedTool = $this->toolModel->update($id, $input);
            
            if (!$updatedTool) {
                ApiResponse::serverError('Failed to update tool');
                return;
            }
            
            ApiResponse::success($this->formatToolData($updatedTool), 'Tool updated successfully');
            
        } catch (Exception $e) {
            ApiResponse::handleException($e);
        }
    }
    
    /**
     * 删除工具（需要管理员权限）
     */
    public function destroy($id) {
        try {
            if (!ApiAuth::isAdmin() || !ApiAuth::hasPermission('tools.delete')) {
                ApiResponse::forbidden('Permission denied');
                return;
            }
            
            $tool = $this->toolModel->find($id);
            if (!$tool) {
                ApiResponse::notFound('Tool not found');
                return;
            }
            
            // 检查是否有使用记录
            $usageCount = $this->toolModel->db->fetchColumn(
                "SELECT COUNT(*) FROM pt_tool_usage WHERE tool_id = :tool_id",
                ['tool_id' => $id]
            );
            
            if ($usageCount > 0) {
                // 软删除
                $result = $this->toolModel->softDelete($id);
                $message = 'Tool archived due to existing usage records';
            } else {
                // 硬删除
                $result = $this->toolModel->delete($id);
                $message = 'Tool deleted successfully';
            }
            
            if (!$result) {
                ApiResponse::serverError('Failed to delete tool');
                return;
            }
            
            ApiResponse::success(null, $message);
            
        } catch (Exception $e) {
            ApiResponse::handleException($e);
        }
    }
    
    /**
     * 获取工具使用统计
     */
    public function usage($id) {
        try {
            $tool = $this->toolModel->find($id);
            if (!$tool) {
                ApiResponse::notFound('Tool not found');
                return;
            }
            
            $days = intval($_GET['days'] ?? 30);
            
            // 获取使用趋势
            $sql = "
                SELECT DATE(created_at) as date, COUNT(*) as count
                FROM pt_tool_usage
                WHERE tool_id = :tool_id
                AND created_at >= DATE_SUB(NOW(), INTERVAL :days DAY)
                GROUP BY DATE(created_at)
                ORDER BY date ASC
            ";
            
            $usage = $this->toolModel->db->fetchAll($sql, [
                'tool_id' => $id,
                'days' => $days
            ]);
            
            // 获取总统计
            $totalUsage = $this->toolModel->db->fetchColumn(
                "SELECT COUNT(*) FROM pt_tool_usage WHERE tool_id = :tool_id",
                ['tool_id' => $id]
            );

            $uniqueUsers = $this->toolModel->db->fetchColumn(
                "SELECT COUNT(DISTINCT user_id) FROM pt_tool_usage WHERE tool_id = :tool_id AND user_id IS NOT NULL",
                ['tool_id' => $id]
            );
            
            ApiResponse::success([
                'tool_id' => $id,
                'total_usage' => $totalUsage,
                'unique_users' => $uniqueUsers,
                'usage_trend' => $usage,
                'period_days' => $days
            ]);
            
        } catch (Exception $e) {
            ApiResponse::handleException($e);
        }
    }
    
    /**
     * 执行工具
     */
    public function execute($id) {
        try {
            $tool = $this->toolModel->find($id);
            if (!$tool || $tool['status'] !== 'published') {
                ApiResponse::notFound('Tool not found');
                return;
            }
            
            // 检查是否为付费工具
            if ($tool['premium'] && (!ApiAuth::isAuthenticated() || !$this->hasValidSubscription())) {
                ApiResponse::error([
                    'message' => 'Premium subscription required',
                    'error_code' => 'PREMIUM_REQUIRED'
                ], 402);
                return;
            }
            
            $input = $this->getJsonInput();
            
            // 记录使用
            $this->toolModel->recordUsage($id, ApiAuth::getUserId(), $this->getClientIp());
            
            // 执行工具逻辑
            $result = [
                'tool_id' => $id,
                'input' => $input,
                'output' => 'Tool execution result would be here',
                'execution_time' => 0.123,
                'timestamp' => time()
            ];
            
            ApiResponse::success($result, 'Tool executed successfully');
            
        } catch (Exception $e) {
            ApiResponse::handleException($e);
        }
    }
    
    /**
     * 评分工具
     */
    public function rate($id) {
        try {
            if (!ApiAuth::isAuthenticated()) {
                ApiResponse::unauthorized('Authentication required');
                return;
            }
            
            $tool = $this->toolModel->find($id);
            if (!$tool) {
                ApiResponse::notFound('Tool not found');
                return;
            }
            
            $input = $this->getJsonInput();
            
            if (!isset($input['rating']) || $input['rating'] < 1 || $input['rating'] > 5) {
                ApiResponse::validationError(['rating' => 'Rating must be between 1 and 5']);
                return;
            }
            
            // 更新评分
            $this->toolModel->updateRating($id, $input['rating']);
            
            ApiResponse::success(null, 'Rating submitted successfully');
            
        } catch (Exception $e) {
            ApiResponse::handleException($e);
        }
    }
    
    /**
     * 验证工具输入
     */
    private function validateToolInput($input, $excludeId = null) {
        $errors = [];
        
        if (empty($input['name'])) {
            $errors['name'] = 'Tool name is required';
        }
        
        if (empty($input['description'])) {
            $errors['description'] = 'Tool description is required';
        }
        
        if (empty($input['category_id'])) {
            $errors['category_id'] = 'Category is required';
        }
        
        return $errors;
    }
    
    /**
     * 格式化工具数据
     */
    private function formatToolData($tool, $detailed = false) {
        $data = [
            'id' => $tool['id'],
            'name' => $tool['name'],
            'slug' => $tool['slug'],
            'description' => $tool['description'],
            'category_id' => $tool['category_id'],
            'icon' => $tool['icon'],
            'color' => $tool['color'],
            'difficulty_level' => $tool['difficulty_level'],
            'featured' => (bool)$tool['featured'],
            'premium' => (bool)$tool['premium'],
            'usage_count' => $tool['usage_count'],
            'rating' => $tool['rating'] ? floatval($tool['rating']) : null,
            'rating_count' => $tool['rating_count'],
            'created_at' => $tool['created_at'],
            'updated_at' => $tool['updated_at']
        ];
        
        if ($detailed) {
            $data['tags'] = $tool['tags'] ? explode(',', $tool['tags']) : [];
            $data['version'] = $tool['version'];
            $data['author'] = $tool['author'];
            $data['documentation_url'] = $tool['documentation_url'];
            $data['demo_url'] = $tool['demo_url'];
            $data['github_url'] = $tool['github_url'];
            $data['config'] = $tool['config'] ? json_decode($tool['config'], true) : null;
            $data['dependencies'] = $tool['dependencies'] ? json_decode($tool['dependencies'], true) : null;
        }
        
        return $data;
    }
    
    /**
     * 检查是否有有效订阅
     */
    private function hasValidSubscription() {
        $user = ApiAuth::getCurrentUser();
        if (!$user) {
            return false;
        }
        
        if ($user['subscription_type'] === 'free') {
            return false;
        }
        
        if ($user['subscription_expires_at'] && strtotime($user['subscription_expires_at']) < time()) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 获取JSON输入
     */
    private function getJsonInput() {
        $input = json_decode(file_get_contents('php://input'), true);
        return $input ?: [];
    }
    
    /**
     * 获取客户端IP
     */
    private function getClientIp() {
        return $_SERVER['HTTP_X_FORWARDED_FOR'] ?? $_SERVER['REMOTE_ADDR'] ?? '';
    }
}
?>
