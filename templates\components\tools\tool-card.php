<?php
/**
 * 工具卡片组件
 * 用于在各个页面展示工具信息的可复用组件
 */

/**
 * 渲染工具卡片
 * @param array $tool 工具数据
 * @param string $style 卡片样式 (default, compact, featured)
 * @param string $section 区域类型 (popular, featured, new)
 */
function renderToolCard($tool, $style = 'default', $section = '') {
    // 验证必需的工具数据
    if (!isset($tool['name']) || !isset($tool['slug'])) {
        return;
    }
    
    // 设置默认值
    $tool = array_merge([
        'description' => '',
        'icon' => '🔧',
        'category' => 'Tools',
        'category_slug' => 'tools',
        'view_count' => 0,
        'rating' => 0,
        'is_featured' => false,
        'is_new' => false,
        'tags' => [],
        'difficulty' => 'Easy',
        'estimated_time' => '< 1 min'
    ], $tool);
    
    // 根据样式选择渲染函数
    switch ($style) {
        case 'compact':
            renderCompactToolCard($tool);
            break;
        case 'featured':
            renderFeaturedToolCard($tool);
            break;
        case 'list':
            renderListToolCard($tool);
            break;
        default:
            renderDefaultToolCard($tool, $section);
            break;
    }
}

/**
 * 默认工具卡片样式
 */
function renderDefaultToolCard($tool, $section = '') {
    $toolUrl = !empty($tool['category_slug']) ? "/tools/{$tool['category_slug']}/{$tool['slug']}" : "/tools/{$tool['slug']}";
?>
<a href="<?= htmlspecialchars($toolUrl) ?>"
   class="bg-gray-900 border border-blue-600 p-6 hover:border-blue-400 transition-all duration-300 group cursor-pointer block">
    
    <!-- 卡片头部 -->
    <div class="flex items-start justify-between mb-4">
        <div class="flex items-center">
            <span class="text-3xl mr-3 group-hover:scale-110 transition-transform duration-300">
                <?= htmlspecialchars($tool['icon']) ?>
            </span>
            <div>
                <h3 class="text-lg font-semibold group-hover:text-accent transition-colors duration-300">
                    <?= htmlspecialchars($tool['name']) ?>
                </h3>
                <span class="text-xs text-gray-500 uppercase tracking-wide">
                    <?= htmlspecialchars($tool['category'] ?? $tool['category_name'] ?? 'Tool') ?>
                </span>
            </div>
        </div>
        
        <!-- 标签区域 -->
        <div class="flex flex-col items-end space-y-1">
            <?php if ($section === 'popular'): ?>
                <span class="bg-blue-600 text-white text-xs px-2 py-1 font-medium">POPULAR</span>
            <?php elseif ($section === 'featured'): ?>
                <span class="bg-orange-500 text-white text-xs px-2 py-1 font-medium">FEATURED</span>
            <?php elseif ($section === 'new'): ?>
                <span class="bg-green-500 text-white text-xs px-2 py-1 font-medium">NEW</span>
            <?php else: ?>
                <?php if ($tool['featured'] ?? false): ?>
                    <span class="bg-orange-500 text-white text-xs px-2 py-1 font-medium">FEATURED</span>
                <?php endif; ?>
                <?php if ($tool['is_new'] ?? false): ?>
                    <span class="bg-green-500 text-white text-xs px-2 py-1 font-medium">NEW</span>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>
    
    <!-- 工具描述 -->
    <p class="text-gray-400 text-sm mb-4 leading-relaxed line-clamp-2">
        <?= htmlspecialchars($tool['description']) ?>
    </p>
    
    <!-- 工具标签 -->
    <?php if (!empty($tool['tags'])): ?>
    <div class="flex flex-wrap gap-2 mb-4">
        <?php foreach (array_slice($tool['tags'], 0, 3) as $tag): ?>
            <span class="bg-gray-800 text-gray-300 text-xs px-2 py-1 hover:bg-gray-700 transition-colors duration-200">
                <?= htmlspecialchars($tag) ?>
            </span>
        <?php endforeach; ?>
    </div>
    <?php endif; ?>
    
    <!-- 卡片底部 -->
    <div class="flex justify-between items-center">
        <div class="flex items-center space-x-4">
            <!-- 使用次数 -->
            <div class="flex items-center text-xs text-gray-500">
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                </svg>
                <?= number_format($tool['view_count']) ?>
            </div>
            
            <!-- 难度等级 - 在热门工具区域隐藏 -->
            <?php if ($section !== 'popular'): ?>
            <div class="flex items-center text-xs text-gray-500">
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                </svg>
                <?= htmlspecialchars($tool['difficulty'] ?? 'Easy') ?>
            </div>
            <?php endif; ?>
        </div>
        
        <!-- 操作按钮 -->
        <div class="flex items-center text-accent group-hover:translate-x-1 transition-transform duration-300">
            <span class="text-sm font-medium mr-1">Try Tool</span>
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
            </svg>
        </div>
    </div>
</a>
<?php
}

/**
 * 紧凑型工具卡片样式
 */
function renderCompactToolCard($tool) {
    $toolUrl = !empty($tool['category_slug']) ? "/tools/{$tool['category_slug']}/{$tool['slug']}" : "/tools/{$tool['slug']}";
?>
<a href="<?= htmlspecialchars($toolUrl) ?>"
   class="bg-gray-900 border border-gray-800 p-4 hover:border-accent transition-all duration-300 group cursor-pointer block">
    
    <div class="flex items-center justify-between">
        <!-- 左侧信息 -->
        <div class="flex items-center">
            <span class="text-2xl mr-3 group-hover:scale-110 transition-transform duration-300">
                <?= htmlspecialchars($tool['icon']) ?>
            </span>
            <div>
                <h3 class="text-base font-semibold group-hover:text-accent transition-colors duration-300">
                    <?= htmlspecialchars($tool['name']) ?>
                </h3>
                <p class="text-xs text-gray-500 line-clamp-1">
                    <?= htmlspecialchars($tool['description']) ?>
                </p>
            </div>
        </div>
        
        <!-- 右侧操作 -->
        <div class="flex items-center space-x-2">
            <span class="text-xs text-gray-500"><?= number_format($tool['view_count']) ?></span>
            <svg class="w-4 h-4 text-accent group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
            </svg>
        </div>
    </div>
</a>
<?php
}

/**
 * 特色工具卡片样式
 */
function renderFeaturedToolCard($tool) {
    $toolUrl = !empty($tool['category_slug']) ? "/tools/{$tool['category_slug']}/{$tool['slug']}" : "/tools/{$tool['slug']}";
?>
<a href="<?= htmlspecialchars($toolUrl) ?>"
   class="bg-gray-900 border border-blue-600 p-6 hover:border-blue-400 transition-all duration-300 group cursor-pointer block relative">

    <div class="h-full">
        <!-- 特色标签 -->
        <div class="flex justify-between items-start mb-4">
            <span class="bg-orange-500 text-white text-xs px-3 py-1 font-bold">FEATURED</span>
            <span class="text-4xl group-hover:scale-110 transition-transform duration-300">
                <?= htmlspecialchars($tool['icon']) ?>
            </span>
        </div>
        
        <!-- 工具信息 -->
        <h3 class="text-xl font-bold mb-2 group-hover:text-accent transition-colors duration-300">
            <?= htmlspecialchars($tool['name']) ?>
        </h3>
        
        <p class="text-gray-400 text-sm mb-4 leading-relaxed">
            <?= htmlspecialchars($tool['description']) ?>
        </p>
        
        <!-- 统计信息 -->
        <div class="flex justify-between items-center mb-4">
            <div class="text-xs text-gray-500">
                <span class="text-accent font-semibold"><?= number_format($tool['view_count']) ?></span> views
            </div>
            <div class="text-xs text-gray-500">
                <?= htmlspecialchars($tool['estimated_time'] ?? 'Quick & Easy') ?>
            </div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="flex items-center justify-between">
            <span class="text-accent font-medium">Try Featured Tool</span>
            <svg class="w-5 h-5 text-accent group-hover:translate-x-2 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
            </svg>
        </div>
    </div>
</a>
<?php
}

/**
 * 列表型工具卡片样式
 */
function renderListToolCard($tool) {
    $toolUrl = !empty($tool['category_slug']) ? "/tools/{$tool['category_slug']}/{$tool['slug']}" : "/tools/{$tool['slug']}";
?>
<a href="<?= htmlspecialchars($toolUrl) ?>"
   class="bg-gray-900 border border-gray-800 p-4 hover:border-accent transition-all duration-300 group cursor-pointer hover:bg-gray-800 block">
    
    <div class="flex items-center justify-between">
        <!-- 工具信息 -->
        <div class="flex items-center flex-1">
            <span class="text-2xl mr-4 group-hover:scale-110 transition-transform duration-300">
                <?= htmlspecialchars($tool['icon']) ?>
            </span>
            
            <div class="flex-1">
                <div class="flex items-center space-x-3 mb-1">
                    <h3 class="text-lg font-semibold group-hover:text-accent transition-colors duration-300">
                        <?= htmlspecialchars($tool['name']) ?>
                    </h3>
                    
                    <?php if ($tool['is_new'] ?? false): ?>
                        <span class="bg-success text-white text-xs px-2 py-1 font-medium">NEW</span>
                    <?php endif; ?>
                </div>
                
                <p class="text-gray-400 text-sm line-clamp-1">
                    <?= htmlspecialchars($tool['description']) ?>
                </p>
                
                <!-- 标签 -->
                <?php if (!empty($tool['tags'])): ?>
                <div class="flex space-x-2 mt-2">
                    <?php foreach (array_slice($tool['tags'], 0, 2) as $tag): ?>
                        <span class="bg-gray-800 text-gray-400 text-xs px-2 py-1">
                            <?= htmlspecialchars($tag) ?>
                        </span>
                    <?php endforeach; ?>
                </div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- 统计和操作 -->
        <div class="flex items-center space-x-6 ml-4">
            <div class="text-center">
                <div class="text-sm font-semibold text-white"><?= number_format($tool['view_count']) ?></div>
                <div class="text-xs text-gray-500">views</div>
            </div>
            
            <div class="text-center">
                <div class="text-sm font-semibold text-accent"><?= htmlspecialchars($tool['difficulty'] ?? 'Easy') ?></div>
                <div class="text-xs text-gray-500">level</div>
            </div>
            
            <svg class="w-5 h-5 text-accent group-hover:translate-x-2 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
            </svg>
        </div>
    </div>
</a>
<?php
}

/**
 * 新工具卡片样式
 */
function renderNewToolCard($tool) {
    $toolUrl = !empty($tool['category_slug']) ? "/tools/{$tool['category_slug']}/{$tool['slug']}" : "/tools/{$tool['slug']}";
?>
<a href="<?= htmlspecialchars($toolUrl) ?>"
   class="bg-gray-900 border border-blue-600 p-6 hover:border-blue-400 transition-all duration-300 group cursor-pointer block relative">

    <div class="h-full">
        <!-- NEW标签 -->
        <div class="flex justify-between items-start mb-4">
            <span class="bg-green-500 text-white text-xs px-3 py-1 font-bold">NEW</span>
            <span class="text-4xl group-hover:scale-110 transition-transform duration-300">
                <?= htmlspecialchars($tool['icon']) ?>
            </span>
        </div>

        <!-- 工具信息 -->
        <h3 class="text-xl font-bold mb-2 group-hover:text-green-400 transition-colors duration-300">
            <?= htmlspecialchars($tool['name']) ?>
        </h3>
        <p class="text-gray-400 text-sm mb-4 leading-relaxed">
            <?= htmlspecialchars($tool['description']) ?>
        </p>

        <!-- 工具标签 -->
        <div class="flex flex-wrap gap-2 mb-4">
            <?php
            $tags = is_string($tool['tags']) ? explode(',', $tool['tags']) : ($tool['tags'] ?? []);
            foreach (array_slice($tags, 0, 3) as $tag):
                $tag = trim($tag);
                if (!empty($tag)):
            ?>
            <span class="bg-gray-800 text-gray-300 text-xs px-2 py-1 rounded">
                <?= htmlspecialchars($tag) ?>
            </span>
            <?php
                endif;
            endforeach;
            ?>
        </div>

        <!-- 工具统计 -->
        <div class="flex items-center justify-between text-xs text-gray-500">
            <span><?= number_format($tool['view_count'] ?? 0) ?> views</span>
            <span class="flex items-center text-green-400 group-hover:translate-x-1 transition-transform duration-300">
                Try Tool
                <svg class="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
            </span>
        </div>
    </div>
</a>
<?php
}

/**
 * 渲染工具网格
 * @param array $tools 工具数组
 * @param string $style 卡片样式
 * @param int $columns 列数
 */
function renderToolGrid($tools, $style = 'default', $columns = 4) {
    $gridClass = match($columns) {
        1 => 'grid-cols-1',
        2 => 'grid-cols-1 md:grid-cols-2',
        3 => 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
        4 => 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4',
        default => 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'
    };

    if ($style === 'list') {
        $gridClass = 'grid-cols-1';
    }
?>
<div class="grid <?= $gridClass ?> gap-6">
    <?php foreach ($tools as $tool): ?>
        <?php renderToolCard($tool, $style); ?>
    <?php endforeach; ?>
</div>
<?php
}

/**
 * 渲染工具网格（带区域参数）
 * @param array $tools 工具数组
 * @param string $style 卡片样式
 * @param int $columns 列数
 * @param string $section 区域类型
 */
function renderToolGridWithSection($tools, $style = 'default', $columns = 4, $section = '') {
    $gridClass = match($columns) {
        1 => 'grid-cols-1',
        2 => 'grid-cols-1 md:grid-cols-2',
        3 => 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
        4 => 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4',
        default => 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'
    };

    if ($style === 'list') {
        $gridClass = 'grid-cols-1';
    }
?>
<div class="grid <?= $gridClass ?> gap-6">
    <?php foreach ($tools as $tool): ?>
        <?php renderToolCard($tool, $style, $section); ?>
    <?php endforeach; ?>
</div>
<?php
}

/**
 * 渲染热门工具组件
 */
function renderPopularTools($limit = 8) {
    // 示例数据 (实际项目中从数据库获取)
    $popularTools = [
        [
            'name' => 'HTML Formatter',
            'slug' => 'html-formatter',
            'description' => 'Format and beautify your HTML code with proper indentation and structure.',
            'icon' => '🔧',
            'category' => 'Development',
            'category_slug' => 'development',
            'view_count' => 15234,
            'is_featured' => true,
            'tags' => ['HTML', 'Formatter', 'Code'],
            'difficulty' => 'Easy'
        ],
        [
            'name' => 'CSS Minifier',
            'slug' => 'css-minifier',
            'description' => 'Compress and minify CSS files to reduce size and improve loading speed.',
            'icon' => '📦',
            'category' => 'Development',
            'category_slug' => 'development',
            'view_count' => 12876,
            'tags' => ['CSS', 'Minify', 'Optimize'],
            'difficulty' => 'Easy'
        ],
        [
            'name' => 'Image Converter',
            'slug' => 'image-converter',
            'description' => 'Convert images between different formats like JPG, PNG, WebP, and more.',
            'icon' => '🖼️',
            'category' => 'Design & Media',
            'category_slug' => 'design',
            'view_count' => 18567,
            'is_new' => true,
            'tags' => ['Image', 'Convert', 'Format'],
            'difficulty' => 'Easy'
        ],
        [
            'name' => 'QR Code Generator',
            'slug' => 'qr-generator',
            'description' => 'Generate QR codes for URLs, text, WiFi credentials, and other data types.',
            'icon' => '📱',
            'category' => 'Utilities',
            'category_slug' => 'utilities',
            'view_count' => 22145,
            'tags' => ['QR Code', 'Generate', 'Mobile'],
            'difficulty' => 'Easy'
        ]
    ];
    
    $tools = array_slice($popularTools, 0, $limit);
    renderToolGrid($tools, 'default', 4);
}
?>
