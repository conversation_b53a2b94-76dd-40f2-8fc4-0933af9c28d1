/**
 * 模态框组件
 * 处理模态框的显示、隐藏和交互
 */
class Modal {
    constructor() {
        this.activeModal = null;
        this.init();
    }

    init() {
        this.setupTriggers();
        this.setupKeyboardEvents();
        this.setupBackdropClick();
    }

    /**
     * 设置模态框触发器
     */
    setupTriggers() {
        // 打开模态框的触发器
        document.addEventListener('click', (e) => {
            const trigger = e.target.closest('[data-modal]');
            if (trigger) {
                e.preventDefault();
                const modalId = trigger.dataset.modal;
                this.open(modalId);
            }
        });

        // 关闭模态框的触发器
        document.addEventListener('click', (e) => {
            const closeTrigger = e.target.closest('[data-modal-close]');
            if (closeTrigger) {
                e.preventDefault();
                this.close();
            }
        });
    }

    /**
     * 设置键盘事件
     */
    setupKeyboardEvents() {
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.activeModal) {
                this.close();
            }
        });
    }

    /**
     * 设置背景点击关闭
     */
    setupBackdropClick() {
        document.addEventListener('click', (e) => {
            if (this.activeModal && e.target === this.activeModal) {
                this.close();
            }
        });
    }

    /**
     * 打开模态框
     */
    open(modalId) {
        const modal = document.getElementById(modalId);
        if (!modal) {
            console.warn(`Modal with id "${modalId}" not found`);
            return;
        }

        // 关闭当前活动的模态框
        if (this.activeModal) {
            this.close();
        }

        this.activeModal = modal;
        modal.classList.remove('hidden');
        modal.classList.add('flex');
        document.body.classList.add('modal-open');

        // 聚焦到第一个可聚焦元素
        const focusableElement = modal.querySelector('input, button, textarea, select, [tabindex]:not([tabindex="-1"])');
        if (focusableElement) {
            setTimeout(() => focusableElement.focus(), 100);
        }

        // 触发打开事件
        modal.dispatchEvent(new CustomEvent('modal:opened', { detail: { modalId } }));
    }

    /**
     * 关闭模态框
     */
    close() {
        if (!this.activeModal) return;

        const modalId = this.activeModal.id;
        this.activeModal.classList.add('hidden');
        this.activeModal.classList.remove('flex');
        document.body.classList.remove('modal-open');

        // 触发关闭事件
        this.activeModal.dispatchEvent(new CustomEvent('modal:closed', { detail: { modalId } }));

        this.activeModal = null;
    }

    /**
     * 切换模态框状态
     */
    toggle(modalId) {
        const modal = document.getElementById(modalId);
        if (!modal) return;

        if (modal.classList.contains('hidden')) {
            this.open(modalId);
        } else {
            this.close();
        }
    }

    /**
     * 检查模态框是否打开
     */
    isOpen(modalId = null) {
        if (modalId) {
            const modal = document.getElementById(modalId);
            return modal && !modal.classList.contains('hidden');
        }
        return this.activeModal !== null;
    }
}

// 自动初始化
if (typeof window !== 'undefined') {
    window.Modal = Modal;
    
    document.addEventListener('DOMContentLoaded', () => {
        window.modal = new Modal();
    });
}
