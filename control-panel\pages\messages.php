<?php
/**
 * 联系消息管理页面
 */

// 检查用户权限
if (!isset($_SESSION['admin_logged_in']) || !$_SESSION['admin_logged_in']) {
    header('Location: auth/login.php');
    exit;
}

// 使用统一的数据库连接
require_once ROOT_PATH . '/includes/database-connection.php';

// 检查是否有导出错误
if (isset($_SESSION['export_error'])) {
    $error_message = $_SESSION['export_error'];
    unset($_SESSION['export_error']);
}

// 处理操作
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    $message_id = $_POST['message_id'] ?? '';
    
    try {
        switch ($action) {
            case 'mark_read':
                $stmt = $pdo->prepare("UPDATE pt_contact_message SET status = 'read', updated_at = NOW() WHERE id = ?");
                $stmt->execute([$message_id]);
                $success_message = "Message marked as read";
                break;
                
            case 'mark_unread':
                $stmt = $pdo->prepare("UPDATE pt_contact_message SET status = 'unread', updated_at = NOW() WHERE id = ?");
                $stmt->execute([$message_id]);
                $success_message = "Message marked as unread";
                break;

            case 'archive':
                $stmt = $pdo->prepare("UPDATE pt_contact_message SET status = 'archived', updated_at = NOW() WHERE id = ?");
                $stmt->execute([$message_id]);
                $success_message = "Message archived";
                break;
                
            case 'delete':
                $stmt = $pdo->prepare("DELETE FROM pt_contact_message WHERE id = ?");
                $stmt->execute([$message_id]);
                $success_message = "Message deleted successfully";
                break;
                
            case 'add_note':
                $note = $_POST['admin_note'] ?? '';
                $stmt = $pdo->prepare("UPDATE pt_contact_message SET admin_note = ?, updated_at = NOW() WHERE id = ?");
                $stmt->execute([$note, $message_id]);
                $success_message = "Note added successfully";
                break;

            case 'clear_all':
                $pdo->exec("DELETE FROM pt_contact_message");
                $success_message = "All messages cleared successfully";
                break;
        }
    } catch (Exception $e) {
        $error_message = "Error: " . $e->getMessage();
    }
}

// 获取筛选参数
$status_filter = $_GET['status'] ?? '';
$search = $_GET['search'] ?? '';
$page = max(1, intval($_GET['p'] ?? 1));
$per_page = 10;
$offset = ($page - 1) * $per_page;

// 构建查询条件
$where_conditions = [];
$params = [];

if ($status_filter) {
    $where_conditions[] = "status = ?";
    $params[] = $status_filter;
}

if ($search) {
    $where_conditions[] = "(name LIKE ? OR email LIKE ? OR subject LIKE ? OR message LIKE ?)";
    $search_term = "%$search%";
    $params = array_merge($params, [$search_term, $search_term, $search_term, $search_term]);
}

$where_clause = $where_conditions ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// 获取总数
$count_sql = "SELECT COUNT(*) as total FROM pt_contact_message $where_clause";
$count_stmt = $pdo->prepare($count_sql);
$count_stmt->execute($params);
$total_messages = $count_stmt->fetch()['total'];
$total_pages = ceil($total_messages / $per_page);

// 获取消息列表
$sql = "SELECT * FROM pt_contact_message $where_clause ORDER BY created_at DESC LIMIT $per_page OFFSET $offset";
$stmt = $pdo->prepare($sql);
$stmt->execute($params);
$messages = $stmt->fetchAll();

// 获取统计信息
$stats_sql = "SELECT
    COUNT(*) as total,
    SUM(CASE WHEN status = 'unread' THEN 1 ELSE 0 END) as unread,
    SUM(CASE WHEN status = 'read' THEN 1 ELSE 0 END) as `read`
    FROM pt_contact_message";
$stats = $pdo->query($stats_sql)->fetch();
?>

<div class="space-y-6">
    <!-- 页面描述和统计 -->
    <div class="flex justify-between items-center">
        <div>
            <p class="text-gray-600">Manage and respond to user inquiries</p>
        </div>
        
        <div class="flex items-center space-x-4">
            <div class="flex space-x-4">
                <div class="bg-blue-100 px-4 py-2 text-blue-800">
                    Total: <?= $stats['total'] ?>
                </div>
                <div class="bg-red-100 px-4 py-2 text-red-800">
                    Unread: <?= $stats['unread'] ?>
                </div>
                <div class="bg-green-100 px-4 py-2 text-green-800">
                    Read: <?= $stats['read'] ?>
                </div>
            </div>

            <div class="flex space-x-2">
                <form method="POST" class="inline">
                    <input type="hidden" name="action" value="export_emails">
                    <button type="submit" class="px-4 py-2 bg-green-600 text-white hover:bg-green-700">
                        Export Emails
                    </button>
                </form>

                <button onclick="openClearAllModal()" class="px-4 py-2 bg-red-600 text-white hover:bg-red-700">
                    Clear All
                </button>
            </div>
        </div>
    </div>

    <!-- 成功/错误消息 -->
    <?php if (isset($success_message)): ?>
    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3">
        <?= htmlspecialchars($success_message) ?>
    </div>
    <?php endif; ?>

    <?php if (isset($error_message)): ?>
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3">
        <?= htmlspecialchars($error_message) ?>
    </div>
    <?php endif; ?>

    <!-- 筛选和搜索 -->
    <div class="bg-white p-4 border border-gray-200">
        <form method="GET" class="flex flex-wrap gap-4 items-end">
            <input type="hidden" name="page" value="messages">
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                <select name="status" class="px-3 py-2 border border-gray-300 focus:outline-none focus:border-accent">
                    <option value="">All Status</option>
                    <option value="unread" <?= $status_filter === 'unread' ? 'selected' : '' ?>>Unread</option>
                    <option value="read" <?= $status_filter === 'read' ? 'selected' : '' ?>>Read</option>
                    <option value="replied" <?= $status_filter === 'replied' ? 'selected' : '' ?>>Replied</option>
                    <option value="archived" <?= $status_filter === 'archived' ? 'selected' : '' ?>>Archived</option>
                </select>
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Search</label>
                <input type="text" name="search" value="<?= htmlspecialchars($search) ?>" 
                       placeholder="Search messages..." 
                       class="px-3 py-2 border border-gray-300 focus:outline-none focus:border-accent">
            </div>
            
            <button type="submit" class="px-4 py-2 bg-accent text-white hover:bg-blue-700 hover:text-white">
                Filter
            </button>
            
            <a href="?page=messages" class="px-4 py-2 bg-gray-300 text-gray-700 hover:bg-gray-400">
                Clear
            </a>
        </form>
    </div>

    <!-- 消息列表 -->
    <div class="bg-white border border-gray-200">
        <div class="overflow-x-auto">
            <table class="w-full">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subject</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php foreach ($messages as $message): ?>
                    <tr class="hover:bg-gray-50 <?= $message['status'] === 'unread' ? 'bg-blue-50' : '' ?>">
                        <td class="px-6 py-4">
                            <div>
                                <div class="text-sm font-medium text-gray-900"><?= htmlspecialchars($message['name']) ?></div>
                                <div class="text-sm text-gray-500"><?= htmlspecialchars($message['email']) ?></div>
                            </div>
                        </td>
                        <td class="px-6 py-4">
                            <div class="text-sm text-gray-900"><?= htmlspecialchars($message['subject']) ?></div>
                            <div class="text-sm text-gray-500"><?= htmlspecialchars(substr($message['message'], 0, 100)) ?>...</div>
                        </td>
                        <td class="px-6 py-4 text-sm text-gray-500">
                            <?= date('M j, Y H:i', strtotime($message['created_at'])) ?>
                        </td>
                        <td class="px-6 py-4">
                            <span class="inline-flex px-2 py-1 text-xs font-semibold leading-5
                                <?php
                                switch($message['status']) {
                                    case 'unread': echo 'bg-red-100 text-red-800'; break;
                                    case 'read': echo 'bg-blue-100 text-blue-800'; break;
                                    case 'replied': echo 'bg-green-100 text-green-800'; break;
                                    case 'archived': echo 'bg-gray-100 text-gray-800'; break;
                                    default: echo 'bg-gray-100 text-gray-800';
                                }
                                ?>">
                                <?= ucfirst($message['status']) ?>
                            </span>
                        </td>
                        <td class="px-6 py-4 text-sm font-medium">
                            <div class="flex space-x-2">
                                <a href="?page=message-view&id=<?= $message['id'] ?>" 
                                   class="text-blue-600 hover:text-blue-900">View</a>
                                
                                <?php if ($message['status'] === 'unread'): ?>
                                <form method="POST" class="inline">
                                    <input type="hidden" name="action" value="mark_read">
                                    <input type="hidden" name="message_id" value="<?= $message['id'] ?>">
                                    <button type="submit" class="text-green-600 hover:text-green-900">Mark Read</button>
                                </form>
                                <?php else: ?>
                                <form method="POST" class="inline">
                                    <input type="hidden" name="action" value="mark_unread">
                                    <input type="hidden" name="message_id" value="<?= $message['id'] ?>">
                                    <button type="submit" class="text-yellow-600 hover:text-yellow-900">Mark Unread</button>
                                </form>
                                <?php endif; ?>

                                <?php if ($message['status'] !== 'archived'): ?>
                                <form method="POST" class="inline">
                                    <input type="hidden" name="action" value="archive">
                                    <input type="hidden" name="message_id" value="<?= $message['id'] ?>">
                                    <button type="submit" class="text-gray-600 hover:text-gray-900">Archive</button>
                                </form>
                                <?php endif; ?>

                                <form method="POST" class="inline">
                                    <input type="hidden" name="action" value="delete">
                                    <input type="hidden" name="message_id" value="<?= $message['id'] ?>">
                                    <button type="submit" class="text-red-600 hover:text-red-900">Delete</button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>

    <!-- 分页导航 -->
    <?php if ($total_pages > 1): ?>
    <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
        <div class="flex-1 flex justify-between sm:hidden">
            <!-- 移动端分页 -->
            <?php if ($page > 1): ?>
                <a href="?page=messages&p=<?= $page - 1 ?><?= $status_filter ? '&status=' . urlencode($status_filter) : '' ?><?= $search ? '&search=' . urlencode($search) : '' ?>" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Previous
                </a>
            <?php endif; ?>

            <?php if ($page < $total_pages): ?>
                <a href="?page=messages&p=<?= $page + 1 ?><?= $status_filter ? '&status=' . urlencode($status_filter) : '' ?><?= $search ? '&search=' . urlencode($search) : '' ?>" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Next
                </a>
            <?php endif; ?>
        </div>

        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
                <p class="text-sm text-gray-700">
                    Showing <span class="font-medium"><?= $offset + 1 ?></span> to <span class="font-medium"><?= min($offset + $per_page, $total_messages) ?></span> of <span class="font-medium"><?= $total_messages ?></span> messages
                </p>
            </div>

            <div>
                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                    <!-- 上一页 -->
                    <?php if ($page > 1): ?>
                        <a href="?page=messages&p=<?= $page - 1 ?><?= $status_filter ? '&status=' . urlencode($status_filter) : '' ?><?= $search ? '&search=' . urlencode($search) : '' ?>" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            <span class="sr-only">Previous</span>
                            <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                            </svg>
                        </a>
                    <?php else: ?>
                        <span class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-gray-100 text-sm font-medium text-gray-400">
                            <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                            </svg>
                        </span>
                    <?php endif; ?>

                    <!-- 页码 -->
                    <?php
                    $start_page = max(1, $page - 2);
                    $end_page = min($total_pages, $page + 2);

                    if ($start_page > 1): ?>
                        <a href="?page=messages&p=1<?= $status_filter ? '&status=' . urlencode($status_filter) : '' ?><?= $search ? '&search=' . urlencode($search) : '' ?>" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">1</a>
                        <?php if ($start_page > 2): ?>
                            <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">...</span>
                        <?php endif; ?>
                    <?php endif; ?>

                    <?php for ($i = $start_page; $i <= $end_page; $i++): ?>
                        <?php if ($i == $page): ?>
                            <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600"><?= $i ?></span>
                        <?php else: ?>
                            <a href="?page=messages&p=<?= $i ?><?= $status_filter ? '&status=' . urlencode($status_filter) : '' ?><?= $search ? '&search=' . urlencode($search) : '' ?>" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50"><?= $i ?></a>
                        <?php endif; ?>
                    <?php endfor; ?>

                    <?php if ($end_page < $total_pages): ?>
                        <?php if ($end_page < $total_pages - 1): ?>
                            <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">...</span>
                        <?php endif; ?>
                        <a href="?page=messages&p=<?= $total_pages ?><?= $status_filter ? '&status=' . urlencode($status_filter) : '' ?><?= $search ? '&search=' . urlencode($search) : '' ?>" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50"><?= $total_pages ?></a>
                    <?php endif; ?>

                    <!-- 下一页 -->
                    <?php if ($page < $total_pages): ?>
                        <a href="?page=messages&p=<?= $page + 1 ?><?= $status_filter ? '&status=' . urlencode($status_filter) : '' ?><?= $search ? '&search=' . urlencode($search) : '' ?>" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            <span class="sr-only">Next</span>
                            <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                            </svg>
                        </a>
                    <?php else: ?>
                        <span class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-gray-100 text-sm font-medium text-gray-400">
                            <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                            </svg>
                        </span>
                    <?php endif; ?>
                </nav>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<!-- Clear All 确认模态框 -->
<div id="clearAllModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg bg-white">
        <div class="mt-3 text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 bg-red-100 mb-4">
                <svg class="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">Clear All Messages</h3>
            <p class="text-sm text-gray-500 mb-4">
                Are you sure you want to delete ALL messages? This action cannot be undone and will permanently remove all contact messages from the database.
            </p>

            <div class="bg-yellow-50 border border-yellow-200 p-3 mb-4">
                <p class="text-sm text-yellow-800">
                    <strong>Warning:</strong> This will delete <?= $stats['total'] ?> messages including <?= $stats['unread'] ?> unread messages.
                </p>
            </div>

            <form method="POST" class="inline">
                <input type="hidden" name="action" value="clear_all">

                <div class="flex justify-center space-x-4">
                    <button type="button" onclick="closeClearAllModal()"
                            class="px-4 py-2 bg-gray-300 text-gray-700 hover:bg-gray-400">
                        Cancel
                    </button>
                    <button type="submit"
                            class="px-4 py-2 bg-red-600 text-white hover:bg-red-700">
                        Yes, Clear All Messages
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function openClearAllModal() {
    document.getElementById('clearAllModal').classList.remove('hidden');
}

function closeClearAllModal() {
    document.getElementById('clearAllModal').classList.add('hidden');
}

// 点击模态框外部关闭
document.getElementById('clearAllModal')?.addEventListener('click', function(e) {
    if (e.target === this) {
        closeClearAllModal();
    }
});

// ESC键关闭模态框
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeClearAllModal();
    }
});
</script>
