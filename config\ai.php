<?php
/**
 * AI服务配置文件
 * 配置GLM-4.5-Flash和其他AI服务
 */

return [
    // GLM-4.5-Flash配置
    'glm' => [
        'api_key' => 'your-glm-api-key-here',
        'api_url' => 'https://open.bigmodel.cn/api/paas/v4/chat/completions',
        'model' => 'glm-4-flash',
        'timeout' => 30,
        'max_tokens' => 1000,
        'temperature' => 0.7,
        'enabled' => true
    ],
    
    // 文件分析配置
    'file_analysis' => [
        'max_content_length' => 5000, // 发送给AI的最大内容长度
        'supported_extensions' => ['php', 'html', 'htm'],
        'max_file_size' => 2 * 1024 * 1024, // 2MB
        'enable_fallback' => true, // 启用备用分析
    ],
    
    // 提示词模板
    'prompts' => [
        'tool_analysis' => "Analyze the following HTML/PHP tool file and extract information in JSON format:

1. Tool name (name)
2. URL-friendly slug
3. Tool description (description)  
4. Functional tags (tags)
5. Recommended emoji icon
6. Category suggestion
7. Difficulty level assessment
8. File type detection

File content: {content}

Please return JSON format:
{
  \"name\": \"Tool Name\",
  \"slug\": \"url-friendly-slug\", 
  \"description\": \"Brief tool description\",
  \"tags\": [\"tag1\", \"tag2\", \"tag3\"],
  \"icon\": \"🔧\",
  \"category\": \"suggested-category\",
  \"difficulty\": \"beginner|intermediate|advanced\",
  \"file_type\": \"php|html|htm\",
  \"confidence\": 0.95
}

Analysis requirements:
- Extract title from <title>, <h1>, or main heading
- Identify functionality from JavaScript functions and form elements
- Suggest relevant tags based on detected features
- Recommend appropriate emoji icon
- Assess complexity level based on code structure
- Detect file type and main programming language used
- Return confidence score between 0 and 1"
    ]
];
?>
