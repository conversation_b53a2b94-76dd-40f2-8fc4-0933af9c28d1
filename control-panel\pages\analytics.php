<?php
/**
 * Analytics Dashboard - 数据分析仪表
 */

// 处理导出请求


// 使用统一的数据库连接
require_once ROOT_PATH . '/includes/database-connection.php';

// 获取数据库连接
try {

    // 获取时间范围参数
    $timeRange = $_GET['range'] ?? 'today';
    $startDate = $_GET['start_date'] ?? '';
    $endDate = $_GET['end_date'] ?? '';

    // 设置时间条件
    switch ($timeRange) {
        case 'today':
            $timeLabel = "Today";
            $timeCondition = "DATE(created_at) = CURDATE()";
            break;
        case 'week':
            $timeLabel = "Last 7 Days";
            $timeCondition = "created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)";
            break;
        case 'month':
            $timeLabel = "Last 30 Days";
            $timeCondition = "created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)";
            break;
        case 'custom':
            if ($startDate && $endDate) {
                $timeLabel = "$startDate to $endDate";
                $timeCondition = "DATE(created_at) BETWEEN '$startDate' AND '$endDate'";
            } else {
                $timeLabel = "All Time";
                $timeCondition = "1=1";
            }
            break;
        default:
            $timeLabel = "All Time";
            $timeCondition = "1=1";
    }



    // 核心KPI数据
    $totalTools = $pdo->query("SELECT COUNT(*) FROM pt_tool WHERE status = 'active'")->fetchColumn();
    $totalUsers = $pdo->query("SELECT COUNT(*) FROM pt_member WHERE status = 'active'")->fetchColumn();
    $totalViews = $pdo->query("SELECT COALESCE(SUM(views), 0) FROM pt_product_launches")->fetchColumn() ?: 0;
    
    // 基于选择的时间范围的数据
    $periodUsers = $pdo->query("SELECT COUNT(*) FROM pt_member WHERE $timeCondition")->fetchColumn();
    $periodToolUsage = $pdo->query("SELECT COUNT(*) FROM pt_tool_usage WHERE " . str_replace('created_at', 'used_at', $timeCondition))->fetchColumn();
    $periodApiCalls = $pdo->query("SELECT COUNT(*) FROM pt_api_usage WHERE $timeCondition")->fetchColumn();
    $periodQuotaUsed = $pdo->query("SELECT COALESCE(SUM(quota_consumed), 0) FROM pt_api_usage WHERE $timeCondition")->fetchColumn();
    $periodMessages = $pdo->query("SELECT COUNT(*) FROM pt_contact_message WHERE $timeCondition")->fetchColumn();

    // 为了向后兼容，保留原变量名
    $todayUsers = $periodUsers;
    $todayToolUsage = $periodToolUsage;
    $todayApiCalls = $periodApiCalls;
    $todayQuotaUsed = $periodQuotaUsed;
    $todayMessages = $periodMessages;

    // 热门工具
    $topTools = $pdo->query("
        SELECT t.name, t.view_count, t.slug, c.name as category_name
        FROM pt_tool t
        LEFT JOIN pt_tool_category c ON t.category_id = c.id
        WHERE t.status = 'active'
        ORDER BY t.view_count DESC
        LIMIT 10
    ")->fetchAll();

    // 最近活动（基于选择的时间范围）
    $recentActivity = $pdo->query("
        SELECT 'tool_usage' as type, t.name as title, tu.used_at as created_at, m.username
        FROM pt_tool_usage tu
        JOIN pt_tool t ON tu.tool_id = t.id
        LEFT JOIN pt_member m ON tu.user_id = m.id
        WHERE " . str_replace('created_at', 'tu.used_at', $timeCondition) . "
        UNION ALL
        SELECT 'user_register' as type, CONCAT('New user: ', username) as title, created_at, username
        FROM pt_member
        WHERE $timeCondition
        UNION ALL
        SELECT 'message' as type, CONCAT('Message: ', subject) as title, created_at, name as username
        FROM pt_contact_message
        WHERE $timeCondition
        ORDER BY created_at DESC
        LIMIT 20
    ")->fetchAll();

    // 图表数据：用户增长趋势（最近7天）
    $userGrowthData = $pdo->query("
        SELECT DATE(created_at) as date, COUNT(*) as count
        FROM pt_member
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        GROUP BY DATE(created_at)
        ORDER BY date ASC
    ")->fetchAll();

    // 图表数据：工具使用趋势（最近7天）
    $toolUsageData = $pdo->query("
        SELECT DATE(tu.used_at) as date, COUNT(*) as count
        FROM pt_tool_usage tu
        WHERE tu.used_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        GROUP BY DATE(tu.used_at)
        ORDER BY date ASC
    ")->fetchAll();

    // 图表数据：API配额使用趋势（最近7天）
    $apiCallsData = $pdo->query("
        SELECT DATE(created_at) as date, COUNT(*) as count, SUM(quota_consumed) as quota_used
        FROM pt_api_usage
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        GROUP BY DATE(created_at)
        ORDER BY date ASC
    ")->fetchAll();

    // 图表数据：工具分类分
    $categoryData = $pdo->query("
        SELECT c.name as category, COUNT(t.id) as count
        FROM pt_tool_category c
        LEFT JOIN pt_tool t ON c.id = t.category_id AND t.status = 'active'
        GROUP BY c.id, c.name
        ORDER BY count DESC
        LIMIT 10
    ")->fetchAll();

    // 系统状态检查
    $systemStatus = [
        'database' => ['status' => 'online', 'label' => 'Online'],
        'storage' => ['status' => 'normal', 'label' => 'Normal'],
        'performance' => ['status' => 'optimal', 'label' => 'Optimal']
    ];

    // 检查存储使用情况（基于工具数量和用户数量）
    if ($totalTools > 100 || $totalUsers > 1000) {
        $systemStatus['storage'] = ['status' => 'high', 'label' => 'High Usage'];
    } elseif ($totalTools > 50 || $totalUsers > 500) {
        $systemStatus['storage'] = ['status' => 'medium', 'label' => 'Medium Usage'];
    } else {
        $systemStatus['storage'] = ['status' => 'normal', 'label' => 'Normal'];
    }

    // 检查性能状态（基于API配额使用情况）
    $recentQuotaUsage = $pdo->query("
        SELECT COUNT(*)
        FROM pt_api_usage
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
    ")->fetchColumn();

    if ($recentQuotaUsage > 100) {
        $systemStatus['performance'] = ['status' => 'critical', 'label' => 'High API Usage'];
    } elseif ($recentQuotaUsage > 50) {
        $systemStatus['performance'] = ['status' => 'warning', 'label' => 'Medium API Usage'];
    } else {
        $systemStatus['performance'] = ['status' => 'normal', 'label' => 'Normal API Usage'];
    }

} catch (Exception $e) {
    // 设置默认
    $totalTools = 0;
    $totalUsers = 0;
    $totalViews = 0;
    $todayUsers = 0;
    $todayToolUsage = 0;
    $todayApiCalls = 0;
    $todayQuotaUsed = 0;
    $todayMessages = 0;
    $topTools = [];
    $recentActivity = [];

    // 设置默认图表数据
    $userGrowthData = [];
    $toolUsageData = [];
    $apiCallsData = [];
    $categoryData = [];

    // 系统状态默认值
    $systemStatus = [
        'database' => ['status' => 'error', 'label' => 'Error'],
        'storage' => ['status' => 'unknown', 'label' => 'Unknown'],
        'performance' => ['status' => 'unknown', 'label' => 'Unknown']
    ];
}
?>

<!-- Analytics Dashboard -->
<div class="space-y-6">
    <!-- 页面描述和时间筛-->
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div>
            <p class="text-gray-600">Monitor platform performance and user engagement metrics</p>
        </div>
        
        <!-- 时间范围选择器和操作按钮 -->
        <div class="flex flex-col sm:flex-row items-start sm:items-center space-y-4 sm:space-y-0 sm:space-x-4">
            <form method="GET" class="flex items-center space-x-2">
                <input type="hidden" name="page" value="analytics">
                <select name="range" onchange="handleTimeRangeChange(this)" class="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="today" <?= $timeRange === 'today' ? 'selected' : '' ?>>Today</option>
                    <option value="week" <?= $timeRange === 'week' ? 'selected' : '' ?>>Last 7 Days</option>
                    <option value="month" <?= $timeRange === 'month' ? 'selected' : '' ?>>Last 30 Days</option>
                    <option value="custom" <?= $timeRange === 'custom' ? 'selected' : '' ?>>Custom Range</option>
                    <option value="all" <?= $timeRange === 'all' ? 'selected' : '' ?>>All Time</option>
                </select>

                <!-- 自定义时间范围输-->
                <div id="customRangeInputs" class="flex items-center space-x-2 <?= $timeRange !== 'custom' ? 'hidden' : '' ?>">
                    <input type="date" name="start_date" value="<?= $_GET['start_date'] ?? '' ?>" class="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <span class="text-gray-500">to</span>
                    <input type="date" name="end_date" value="<?= $_GET['end_date'] ?? '' ?>" class="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm">Apply</button>
                </div>
            </form>

            <div class="flex items-center space-x-2">
                <span class="text-sm text-gray-500">Showing: <?= $timeLabel ?></span>

                <!-- 导出按钮 -->
                <button onclick="exportData('csv')" class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 text-sm flex items-center">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    Export CSV
                </button>
            </div>
        </div>
    </div>

    <!-- 核心KPI指标 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- 总工具数 -->
        <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <div class="flex items-center">
                <div class="p-3 bg-blue-100 rounded-lg">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Active Tools</p>
                    <p class="text-2xl font-bold text-gray-900"><?= number_format($totalTools ?? 0) ?></p>
                </div>
            </div>
        </div>

        <!-- 总用户数 -->
        <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <div class="flex items-center">
                <div class="p-3 bg-green-100 rounded-lg">
                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Users</p>
                    <p class="text-2xl font-bold text-gray-900"><?= number_format($totalUsers ?? 0) ?></p>
                    <p class="text-xs text-green-600">+<?= $todayUsers ?? 0 ?> in <?= strtolower($timeLabel) ?></p>
                </div>
            </div>
        </div>

        <!-- 总浏览量 -->
        <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <div class="flex items-center">
                <div class="p-3 bg-purple-100 rounded-lg">
                    <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Views</p>
                    <p class="text-2xl font-bold text-gray-900"><?= number_format($totalViews ?? 0) ?></p>
                    <p class="text-xs text-purple-600"><?= $todayToolUsage ?? 0 ?> in <?= strtolower($timeLabel) ?></p>
                </div>
            </div>
        </div>

        <!-- 配额使用 -->
        <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
            <div class="flex items-center">
                <div class="p-3 bg-red-100 rounded-lg">
                    <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Quota Used</p>
                    <p class="text-2xl font-bold text-gray-900"><?= number_format($todayQuotaUsed ?? 0) ?></p>
                    <p class="text-xs text-red-600"><?= $timeLabel ?></p>
                </div>
            </div>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- 热门工具排行 -->
        <div class="lg:col-span-2">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="p-6 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                        <svg class="w-5 h-5 text-yellow-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                        Top Performing Tools
                    </h2>
                </div>
                <div class="p-6">
                    <?php if (!empty($topTools)): ?>
                    <div class="space-y-4 max-h-96 overflow-y-auto">
                        <?php foreach ($topTools as $index => $tool): ?>
                        <div class="flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg transition-colors">
                            <div class="flex items-center space-x-3">
                                <span class="inline-flex items-center justify-center w-8 h-8 <?= $index < 3 ? 'bg-gradient-to-r from-yellow-400 to-orange-500 text-white' : 'bg-gray-100 text-gray-600' ?> text-sm font-bold rounded-full">
                                    <?= $index + 1 ?>
                                </span>
                                <div>
                                    <p class="font-medium text-gray-900"><?= htmlspecialchars($tool['name']) ?></p>
                                    <p class="text-sm text-gray-500">
                                        <?= htmlspecialchars($tool['category_name'] ?? 'Uncategorized') ?> •
                                        <?= htmlspecialchars($tool['slug']) ?>
                                    </p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="font-bold text-gray-900"><?= number_format($tool['view_count'] ?? 0) ?></p>
                                <p class="text-sm text-gray-500">views</p>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                    <?php else: ?>
                    <div class="text-center py-8">
                        <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                        <p class="text-gray-500">No tool usage data available</p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- 实时活动-->
        <div class="lg:col-span-1">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="p-6 border-b border-gray-200">
                    <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                        <div class="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"></div>
                        Activity (<?= $timeLabel ?>)
                    </h2>
                </div>
                <div class="p-6">
                    <?php if (!empty($recentActivity)): ?>
                    <div class="space-y-4 max-h-96 overflow-y-auto">
                        <?php foreach ($recentActivity as $activity): ?>
                        <div class="flex items-start space-x-3">
                            <div class="flex-shrink-0">
                                <?php if ($activity['type'] === 'tool_usage'): ?>
                                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                    <svg class="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"></path>
                                    </svg>
                                </div>
                                <?php elseif ($activity['type'] === 'user_register'): ?>
                                <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                    <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M8 9a3 3 0 100-6 3 3 0 000 6zM8 11a6 6 0 016 6H2a6 6 0 016-6zM16 7a1 1 0 10-2 0v1h-1a1 1 0 100 2h1v1a1 1 0 102 0v-1h1a1 1 0 100-2h-1V7z"></path>
                                    </svg>
                                </div>
                                <?php else: ?>
                                <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                                    <svg class="w-4 h-4 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"></path>
                                        <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"></path>
                                    </svg>
                                </div>
                                <?php endif; ?>
                            </div>
                            <div class="flex-1 min-w-0">
                                <p class="text-sm font-medium text-gray-900 truncate">
                                    <?= htmlspecialchars($activity['title']) ?>
                                </p>
                                <p class="text-xs text-gray-500">
                                    <?= htmlspecialchars($activity['username'] ?? 'Anonymous') ?> •
                                    <?= date('H:i', strtotime($activity['created_at'])) ?>
                                </p>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                    <?php else: ?>
                    <div class="text-center py-8">
                        <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <p class="text-gray-500">No recent activity</p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- 图表可视化区-->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- 用户增长趋势-->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                    <svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                    </svg>
                    User Growth Trend (7 Days)
                </h2>
            </div>
            <div class="p-6">
                <canvas id="userGrowthChart" width="400" height="200"></canvas>
            </div>
        </div>

        <!-- 工具使用趋势-->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                    <svg class="w-5 h-5 text-blue-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    Tool Usage Trend (7 Days)
                </h2>
            </div>
            <div class="p-6">
                <canvas id="toolUsageChart" width="400" height="200"></canvas>
            </div>
        </div>

        <!-- 配额使用趋势-->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                    <svg class="w-5 h-5 text-purple-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                    Quota Usage Trend (7 Days)
                </h2>
            </div>
            <div class="p-6">
                <canvas id="apiCallsChart" width="400" height="200"></canvas>
            </div>
        </div>

        <!-- 工具分类分布-->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                    <svg class="w-5 h-5 text-orange-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.488 9H15V3.512A9.025 9.025 0 0120.488 9z"></path>
                    </svg>
                    Tool Categories Distribution
                </h2>
            </div>
            <div class="p-6">
                <canvas id="categoryChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>

    <!-- 快速统计和系统状-->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- 今日活动统计 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                    <svg class="w-5 h-5 text-blue-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                    </svg>
                    Activity (<?= $timeLabel ?>)
                </h2>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-2 gap-4">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-blue-600"><?= number_format($todayUsers ?? 0) ?></div>
                        <div class="text-sm text-gray-600">New Users</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-green-600"><?= number_format($todayToolUsage ?? 0) ?></div>
                        <div class="text-sm text-gray-600">Tool Uses</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-red-600"><?= number_format($todayQuotaUsed ?? 0) ?></div>
                        <div class="text-sm text-gray-600">Quota Used</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-orange-600"><?= number_format($todayMessages ?? 0) ?></div>
                        <div class="text-sm text-gray-600">Messages</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 系统状-->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900 flex items-center">
                    <svg class="w-5 h-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                    </svg>
                    System Status
                </h2>
            </div>
            <div class="p-6">
                <div class="space-y-4">
                    <!-- 数据库状态 -->
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-gray-600">Database</span>
                        <?php
                        $dbStatus = $systemStatus['database'];
                        $statusClass = $dbStatus['status'] === 'online' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800';
                        $dotClass = $dbStatus['status'] === 'online' ? 'bg-green-400' : 'bg-red-400';
                        ?>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?= $statusClass ?>">
                            <div class="w-1.5 h-1.5 <?= $dotClass ?> rounded-full mr-1"></div>
                            <?= $dbStatus['label'] ?>
                        </span>
                    </div>

                    <!-- 存储状态 -->
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-gray-600">Storage</span>
                        <?php
                        $storageStatus = $systemStatus['storage'];
                        $statusClass = 'bg-green-100 text-green-800';
                        $dotClass = 'bg-green-400';

                        if ($storageStatus['status'] === 'high') {
                            $statusClass = 'bg-red-100 text-red-800';
                            $dotClass = 'bg-red-400';
                        } elseif ($storageStatus['status'] === 'medium') {
                            $statusClass = 'bg-yellow-100 text-yellow-800';
                            $dotClass = 'bg-yellow-400';
                        }
                        ?>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?= $statusClass ?>">
                            <div class="w-1.5 h-1.5 <?= $dotClass ?> rounded-full mr-1"></div>
                            <?= $storageStatus['label'] ?>
                        </span>
                    </div>

                    <!-- 性能状态 -->
                    <div class="flex items-center justify-between">
                        <span class="text-sm font-medium text-gray-600">Performance</span>
                        <?php
                        $perfStatus = $systemStatus['performance'];
                        $statusClass = 'bg-green-100 text-green-800';
                        $dotClass = 'bg-green-400';

                        if ($perfStatus['status'] === 'slow') {
                            $statusClass = 'bg-red-100 text-red-800';
                            $dotClass = 'bg-red-400';
                        } elseif ($perfStatus['status'] === 'moderate') {
                            $statusClass = 'bg-yellow-100 text-yellow-800';
                            $dotClass = 'bg-yellow-400';
                        }
                        ?>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?= $statusClass ?>">
                            <div class="w-1.5 h-1.5 <?= $dotClass ?> rounded-full mr-1"></div>
                            <?= $perfStatus['label'] ?>
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 快速操-->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div class="p-6 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-900">Quick Actions</h2>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <a href="?page=tools" class="flex items-center p-4 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors">
                    <svg class="w-8 h-8 text-blue-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    <div>
                        <div class="font-medium text-gray-900">Add Tool</div>
                        <div class="text-sm text-gray-600">Create new tool</div>
                    </div>
                </a>

                <a href="?page=users" class="flex items-center p-4 bg-green-50 rounded-lg hover:bg-green-100 transition-colors">
                    <svg class="w-8 h-8 text-green-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                    </svg>
                    <div>
                        <div class="font-medium text-gray-900">Manage Users</div>
                        <div class="text-sm text-gray-600">User management</div>
                    </div>
                </a>

                <a href="?page=messages" class="flex items-center p-4 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors">
                    <svg class="w-8 h-8 text-purple-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                    </svg>
                    <div>
                        <div class="font-medium text-gray-900">Messages</div>
                        <div class="text-sm text-gray-600"><?= $todayMessages ?> new today</div>
                    </div>
                </a>

                <a href="?page=settings" class="flex items-center p-4 bg-orange-50 rounded-lg hover:bg-orange-100 transition-colors">
                    <svg class="w-8 h-8 text-orange-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                    <div>
                        <div class="font-medium text-gray-900">Settings</div>
                        <div class="text-sm text-gray-600">System config</div>
                    </div>
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Chart.js CDN -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<!-- Analytics脚本 -->
<script>
// 图表数据
const chartData = {
    userGrowth: <?= json_encode($userGrowthData ?? [], JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_QUOT | JSON_HEX_AMP) ?>,
    toolUsage: <?= json_encode($toolUsageData ?? [], JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_QUOT | JSON_HEX_AMP) ?>,
    apiCalls: <?= json_encode($apiCallsData ?? [], JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_QUOT | JSON_HEX_AMP) ?>,
    categories: <?= json_encode($categoryData ?? [], JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_QUOT | JSON_HEX_AMP) ?>
};

// 时间范围处理
function handleTimeRangeChange(select) {
    const customInputs = document.getElementById('customRangeInputs');
    if (select.value === 'custom') {
        customInputs.classList.remove('hidden');
        // 不自动提交，等待用户选择日期
    } else {
        customInputs.classList.add('hidden');
        // 立即提交表单
        select.form.submit();
    }
}

// 自定义日期范围验证
function validateCustomRange() {
    const startDate = document.querySelector('input[name="start_date"]').value;
    const endDate = document.querySelector('input[name="end_date"]').value;

    if (!startDate || !endDate) {
        alert('Please select both start and end dates.');
        return false;
    }

    if (new Date(startDate) > new Date(endDate)) {
        alert('Start date must be before end date.');
        return false;
    }

    return true;
}

// 数据导出功能
function exportData(format) {
    const params = new URLSearchParams(window.location.search);
    params.set('export', format);

    // 创建隐藏的表单来提交导出请求
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = window.location.pathname + '?' + params.toString();

    // 添加当前页面的所有参
    for (const [key, value] of params.entries()) {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = key;
        input.value = value;
        form.appendChild(input);
    }

    document.body.appendChild(form);
    form.submit();
    document.body.removeChild(form);
}

// 初始化图
document.addEventListener('DOMContentLoaded', function() {
    // 用户增长趋势
    const userGrowthCtx = document.getElementById('userGrowthChart').getContext('2d');
    new Chart(userGrowthCtx, {
        type: 'line',
        data: {
            labels: chartData.userGrowth.map(item => {
                const date = new Date(item.date);
                return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
            }),
            datasets: [{
                label: 'New Users',
                data: chartData.userGrowth.map(item => item.count),
                borderColor: 'rgb(34, 197, 94)',
                backgroundColor: 'rgba(34, 197, 94, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                }
            }
        }
    });

    // 工具使用趋势
    const toolUsageCtx = document.getElementById('toolUsageChart').getContext('2d');
    new Chart(toolUsageCtx, {
        type: 'bar',
        data: {
            labels: chartData.toolUsage.map(item => {
                const date = new Date(item.date);
                return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
            }),
            datasets: [{
                label: 'Tool Uses',
                data: chartData.toolUsage.map(item => item.count),
                backgroundColor: 'rgba(59, 130, 246, 0.8)',
                borderColor: 'rgb(59, 130, 246)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                }
            }
        }
    });

    // 配额使用趋势
    const apiCallsCtx = document.getElementById('apiCallsChart').getContext('2d');
    new Chart(apiCallsCtx, {
        type: 'line',
        data: {
            labels: chartData.apiCalls.map(item => {
                const date = new Date(item.date);
                return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
            }),
            datasets: [{
                label: 'Quota Used',
                data: chartData.apiCalls.map(item => item.quota_used || item.count),
                borderColor: 'rgb(220, 38, 38)',
                backgroundColor: 'rgba(220, 38, 38, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                }
            }
        }
    });

    // 工具分类分布饼图
    const categoryCtx = document.getElementById('categoryChart').getContext('2d');
    new Chart(categoryCtx, {
        type: 'doughnut',
        data: {
            labels: chartData.categories.map(item => item.category || 'Uncategorized'),
            datasets: [{
                data: chartData.categories.map(item => item.count),
                backgroundColor: [
                    '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0',
                    '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF',
                    '#4BC0C0', '#FF6384'
                ],
                borderWidth: 2,
                borderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true
                    }
                }
            }
        }
    });

    // 为KPI卡片添加悬停效果
    const kpiCards = document.querySelectorAll('.bg-white.p-6.rounded-lg.shadow-sm');
    kpiCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
            this.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.1)';
            this.style.transition = 'all 0.2s ease';
        });
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '';
        });
    });
});
</script>