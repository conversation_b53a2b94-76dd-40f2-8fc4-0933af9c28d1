<?php
/**
 * 工具详情页面模板
 */

// 从URL获取工具信息
$pathParts = explode('/', trim($_SERVER['REQUEST_URI'], '/'));
$categorySlug = $pathParts[1] ?? '';
$toolSlug = $pathParts[2] ?? '';

// 获取工具数据
$tool = getToolBySlug($toolSlug);
$category = getToolCategory($categorySlug);

if (!$tool) {
    // 工具不存在，显示404
    http_response_code(404);
    include ROOT_PATH . '/templates/pages/404.php';
    exit;
}

// 检查工具状态和用户访问权限（权限检查已在路由层面处理）
// 如果能到达这里，说明权限检查已通过，只需要设置状态警告
if ($tool['status'] !== 'active') {
    // 登录用户可以访问，但显示状态提示
    $showStatusWarning = true;
}

$currentPage = 'tool-detail';
$breadcrumbs = [
    ['name' => 'Home', 'url' => '/'],
    ['name' => 'Tools', 'url' => '/tools'],
    ['name' => $category['name'] ?? 'Category', 'url' => "/tools/{$categorySlug}"],
    ['name' => $tool['name']]
];

// 增加工具浏览量
incrementToolViews($toolSlug);

// 包含动态SEO助手
require_once ROOT_PATH . '/app/helpers/dynamic-seo.php';

// 生成工具详情页面的SEO数据
$toolTitle = 'Free ' . $tool['name'] . ' Online Tool - Prompt2Tool';
$toolDescription = $tool['description'] . ' Use this free online ' . strtolower($tool['name']) . ' tool to enhance your productivity and workflow.';

// 确保标题长度在SEO范围内
if (strlen($toolTitle) > 60) {
    $toolTitle = 'Free ' . $tool['name'] . ' - Prompt2Tool';
}

// 确保描述长度在SEO范围内
if (strlen($toolDescription) > 160) {
    $toolDescription = substr($toolDescription, 0, 157) . '...';
}

$seoData = getDynamicSEOData('tool', [
    'title' => $toolTitle,
    'description' => $toolDescription,
    'og_title' => 'Free ' . $tool['name'] . ' Online Tool - Prompt2Tool',
    'og_description' => $tool['description'],
    'og_image' => $tool['image'] ?? '/assets/images/og-default.jpg'
]);

// 包含公共头部
include_once ROOT_PATH . '/templates/layout/header.php';
?>



<!-- 工具详情页面内容 -->
<section class="py-20 bg-black">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">

        <?php if (isset($showStatusWarning) && $showStatusWarning): ?>
            <!-- 状态警告提示 -->
            <div class="mb-8 max-w-4xl mx-auto">
                <div class="bg-yellow-900 border border-yellow-600 rounded-lg p-4">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-yellow-400">
                                <?php if ($tool['status'] === 'coming_soon'): ?>
                                    Tool Under Review
                                <?php elseif ($tool['status'] === 'inactive'): ?>
                                    Tool Currently Inactive
                                <?php endif; ?>
                            </h3>
                            <div class="mt-1 text-sm text-yellow-300">
                                <?php if ($tool['status'] === 'coming_soon'): ?>
                                    This tool is currently under review and not yet publicly available. You can preview it because you're logged in.
                                <?php elseif ($tool['status'] === 'inactive'): ?>
                                    This tool is currently inactive and not publicly available. You can preview it because you're logged in.
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <!-- 工具头部信息 -->
        <div class="text-center mb-16">
            <div class="text-6xl mb-6"><?= htmlspecialchars($tool['icon']) ?></div>
            <h1 class="text-4xl md:text-5xl font-bold mb-6"><?= htmlspecialchars($tool['name']) ?></h1>
            <p class="text-xl text-gray-400 max-w-3xl mx-auto mb-8">
                <?= htmlspecialchars($tool['description']) ?>
            </p>
            
            <!-- 工具元信息 -->
            <div class="flex flex-wrap justify-center gap-6 text-sm text-gray-400 mb-8">
                <div class="flex items-center">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                    </svg>
                    <?= number_format($tool['view_count']) ?> uses
                </div>
                <div class="flex items-center">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                    <?= htmlspecialchars($tool['difficulty'] ?? 'Easy') ?>
                </div>
                <div class="flex items-center">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <?= htmlspecialchars($tool['estimated_time'] ?? 'Quick') ?>
                </div>
            </div>


            
            <!-- 工具标签 -->
            <?php if (!empty($tool['tags'])): ?>
            <div class="flex flex-wrap justify-center gap-2 mt-6">
                <?php foreach ($tool['tags'] as $tag): ?>
                    <span class="bg-gray-800 text-gray-300 text-sm px-3 py-1 hover:bg-gray-700 transition-colors duration-200">
                        <?= htmlspecialchars($tag) ?>
                    </span>
                <?php endforeach; ?>
            </div>
            <?php endif; ?>
        </div>
        
        <!-- 工具界面区域 -->
        <div class="bg-gray-900 border border-gray-800 p-8 mb-16">
            <div class="text-center">
                <h2 class="text-2xl font-bold mb-6">Tool Interface</h2>
                
                <!-- 这里是工具的实际功能界面 -->
                <div class="bg-black border border-gray-700 p-8 min-h-96 flex items-center justify-center">
                    <div class="text-center">
                        <div class="text-4xl mb-4">🚧</div>
                        <h3 class="text-xl font-semibold mb-2">Tool Interface Coming Soon</h3>
                        <p class="text-gray-400">
                            The interactive interface for <?= htmlspecialchars($tool['name']) ?> will be available soon.
                        </p>
                        <button class="mt-4 bg-accent text-white px-8 py-4 text-lg font-medium hover:bg-blue-700 hover:text-white transition-all duration-200 transform hover:scale-105">
                            Get Notified
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 使用说明 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16">
            <!-- 如何使用 -->
            <div class="bg-gray-900 border border-gray-800 p-8">
                <h3 class="text-2xl font-bold mb-6">How to Use</h3>
                <div class="space-y-4">
                    <div class="flex items-start">
                        <div class="bg-accent text-white w-6 h-6 flex items-center justify-center text-sm font-bold mr-4 mt-1">1</div>
                        <div>
                            <h4 class="font-semibold mb-1">Input Your Data</h4>
                            <p class="text-gray-400 text-sm">Enter or paste your content into the input area.</p>
                        </div>
                    </div>
                    <div class="flex items-start">
                        <div class="bg-accent text-white w-6 h-6 flex items-center justify-center text-sm font-bold mr-4 mt-1">2</div>
                        <div>
                            <h4 class="font-semibold mb-1">Configure Options</h4>
                            <p class="text-gray-400 text-sm">Adjust settings according to your needs.</p>
                        </div>
                    </div>
                    <div class="flex items-start">
                        <div class="bg-accent text-white w-6 h-6 flex items-center justify-center text-sm font-bold mr-4 mt-1">3</div>
                        <div>
                            <h4 class="font-semibold mb-1">Get Results</h4>
                            <p class="text-gray-400 text-sm">Copy or download your processed results.</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 特性 -->
            <div class="bg-gray-900 border border-gray-800 p-8">
                <h3 class="text-2xl font-bold mb-6">Features</h3>
                <div class="space-y-3">
                    <div class="flex items-center">
                        <svg class="w-5 h-5 text-success mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        <span>Fast and efficient processing</span>
                    </div>
                    <div class="flex items-center">
                        <svg class="w-5 h-5 text-success mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        <span>No registration required</span>
                    </div>
                    <div class="flex items-center">
                        <svg class="w-5 h-5 text-success mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        <span>Privacy-focused (no data stored)</span>
                    </div>
                    <div class="flex items-center">
                        <svg class="w-5 h-5 text-success mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        <span>Mobile-friendly interface</span>
                    </div>
                    <div class="flex items-center">
                        <svg class="w-5 h-5 text-success mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        <span>Copy to clipboard support</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 相关工具 -->
        <div class="mb-16">
            <h3 class="text-3xl font-bold mb-8 text-center">Related Tools</h3>
            
            <?php
            // 获取同分类的其他工具
            $relatedTools = array_filter(getToolsByCategory($categorySlug), function($t) use ($toolSlug) {
                return $t['slug'] !== $toolSlug;
            });
            $relatedTools = array_slice($relatedTools, 0, 3);
            
            if (!empty($relatedTools)):
            ?>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <?php foreach ($relatedTools as $relatedTool): ?>
                <div class="bg-gray-900 border border-gray-800 p-6 hover:border-accent transition-all duration-300 group cursor-pointer"
                     onclick="window.location.href='/tools/<?= $relatedTool['category_slug'] ?>/<?= $relatedTool['slug'] ?>'">
                    <div class="text-3xl mb-4 group-hover:scale-110 transition-transform duration-300">
                        <?= htmlspecialchars($relatedTool['icon']) ?>
                    </div>
                    <h4 class="text-lg font-semibold mb-2 group-hover:text-accent transition-colors duration-300">
                        <?= htmlspecialchars($relatedTool['name']) ?>
                    </h4>
                    <p class="text-gray-400 text-sm mb-4">
                        <?= htmlspecialchars(truncateText($relatedTool['description'], 80)) ?>
                    </p>
                    <div class="flex items-center text-accent group-hover:translate-x-2 transition-transform duration-300">
                        <span class="text-sm mr-1">Try Tool</span>
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                        </svg>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
            <?php else: ?>
            <div class="text-center text-gray-400">
                <p>No related tools found in this category.</p>
            </div>
            <?php endif; ?>
        </div>
    </div>
</section>



<?php
// 包含公共底部
include_once ROOT_PATH . '/templates/layout/footer.php';
?>
