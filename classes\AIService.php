<?php
/**
 * AI服务类
 * 处理AI模型交互
 */

class AIService {
    private $config;

    public function __construct() {
        $this->config = require ROOT_PATH . '/config/ai.php';

        // 从数据库获取API密钥
        $this->loadApiKeyFromDatabase();
    }

    /**
     * 从数据库加载API密钥（智能负载均衡）
     */
    private function loadApiKeyFromDatabase() {
        try {
            // 使用统一的数据库连接
            require_once ROOT_PATH . '/includes/database-connection.php';

            // 智能选择API密钥（负载均衡）
            $stmt = $pdo->prepare("
                SELECT ak.id, ak.api_key
                FROM pt_service_key ak
                JOIN pt_service_platform ap ON ak.platform_id = ap.id
                WHERE ap.code = 'bigmodel' AND ak.is_active = 1
                ORDER BY ak.usage_count ASC, RAND()
                LIMIT 1
            ");
            $stmt->execute();
            $apiKey = $stmt->fetch();

            if ($apiKey) {
                $this->config['glm']['api_key'] = $apiKey['api_key'];
                $this->config['glm']['key_id'] = $apiKey['id'];
                $this->config['glm']['enabled'] = true;

                // 更新使用统计
                $this->updateKeyUsage($pdo, $apiKey['id']);
            } else {
                $this->config['glm']['enabled'] = false;
            }

        } catch (Exception $e) {
            $this->config['glm']['enabled'] = false;
        }
    }

    /**
     * 更新API Key使用统计
     */
    private function updateKeyUsage($pdo, $keyId) {
        try {
            $stmt = $pdo->prepare("
                UPDATE pt_service_key
                SET usage_count = usage_count + 1,
                    last_used_at = NOW()
                WHERE id = ?
            ");
            $stmt->execute([$keyId]);
        } catch (Exception $e) {
            // 更新失败不影响主流程
        }
    }
    
    /**
     * 分析工具文件内容
     * @param string $content 文件内容
     * @param array $basicInfo 基础文件信息
     * @return array 分析结果
     */
    public function analyzeToolFile($content, $basicInfo = []) {
        if (!$this->config['glm']['enabled']) {
            return $this->generateFallbackAnalysis($basicInfo);
        }
        
        try {
            // 截取内容以避免token限制
            $maxLength = $this->config['file_analysis']['max_content_length'];
            $contentForAI = substr($content, 0, $maxLength);
            
            // 构建提示词
            $prompt = str_replace('{content}', $contentForAI, $this->config['prompts']['tool_analysis']);
            
            // 调用GLM API
            $response = $this->callGLMAPI($prompt);
            
            if ($response && isset($response['choices'][0]['message']['content'])) {
                $aiContent = $response['choices'][0]['message']['content'];
                $analysis = $this->parseAIResponse($aiContent);
                
                if ($analysis) {
                    return $analysis;
                }
            }
            
        } catch (Exception $e) {
            // AI分析失败，使用备用方案
        }
        
        // 如果AI分析失败，使用备用分析
        return $this->generateFallbackAnalysis($basicInfo);
    }
    
    /**
     * 调用GLM API
     * @param string $prompt 提示词
     * @return array|null API响应
     */
    private function callGLMAPI($prompt) {
        $glmConfig = $this->config['glm'];
        
        $requestData = [
            'model' => $glmConfig['model'],
            'messages' => [
                [
                    'role' => 'user',
                    'content' => $prompt
                ]
            ],
            'temperature' => $glmConfig['temperature'],
            'max_tokens' => $glmConfig['max_tokens']
        ];
        
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $glmConfig['api_url'],
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode($requestData),
            CURLOPT_HTTPHEADER => [
                'Content-Type: application/json',
                'Authorization: Bearer ' . $glmConfig['api_key']
            ],
            CURLOPT_TIMEOUT => $glmConfig['timeout']
        ]);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($response === false || !empty($error)) {
            throw new Exception('Network error occurred');
        }

        if ($httpCode !== 200) {
            throw new Exception('Service temporarily unavailable');
        }
        
        return json_decode($response, true);
    }
    
    /**
     * 解析AI响应
     * @param string $aiContent AI返回的内容
     * @return array|null 解析后的分析结果
     */
    private function parseAIResponse($aiContent) {
        // 尝试直接解析JSON
        $analysis = json_decode($aiContent, true);
        
        if (json_last_error() === JSON_ERROR_NONE && is_array($analysis)) {
            return $this->validateAnalysis($analysis);
        }
        
        // 如果直接解析失败，尝试提取JSON部分
        if (preg_match('/\{.*\}/s', $aiContent, $matches)) {
            $analysis = json_decode($matches[0], true);
            if (json_last_error() === JSON_ERROR_NONE && is_array($analysis)) {
                return $this->validateAnalysis($analysis);
            }
        }
        
        return null;
    }
    
    /**
     * 验证和标准化分析结果
     * @param array $analysis 原始分析结果
     * @return array 验证后的分析结果
     */
    private function validateAnalysis($analysis) {
        $defaults = [
            'name' => 'Uploaded Tool',
            'slug' => 'uploaded-tool',
            'description' => 'A tool uploaded for analysis',
            'tags' => ['tool', 'utility'],
            'icon' => '🔧',
            'category' => 'utility',
            'difficulty' => 'intermediate',
            'file_type' => 'html',
            'confidence' => 0.8
        ];
        
        // 合并默认值
        $result = array_merge($defaults, $analysis);
        
        // 验证和清理数据
        $result['name'] = trim($result['name']) ?: $defaults['name'];
        $result['slug'] = $this->generateSlug($result['slug'] ?: $result['name']);
        $result['description'] = trim($result['description']) ?: $defaults['description'];
        
        // 确保tags是数组
        if (!is_array($result['tags'])) {
            if (is_string($result['tags'])) {
                $result['tags'] = array_map('trim', explode(',', $result['tags']));
            } else {
                $result['tags'] = $defaults['tags'];
            }
        }
        
        // 验证文件类型
        $supportedTypes = $this->config['file_analysis']['supported_extensions'];
        if (!in_array($result['file_type'], $supportedTypes)) {
            $result['file_type'] = 'html';
        }
        
        // 验证难度级别
        $validDifficulties = ['beginner', 'intermediate', 'advanced'];
        if (!in_array($result['difficulty'], $validDifficulties)) {
            $result['difficulty'] = 'intermediate';
        }
        
        // 验证置信度
        $result['confidence'] = max(0, min(1, floatval($result['confidence'])));
        
        return $result;
    }
    
    /**
     * 生成URL友好的slug
     * @param string $text 原始文本
     * @return string 生成的slug
     */
    private function generateSlug($text) {
        $slug = strtolower(trim($text));
        $slug = preg_replace('/[^a-z0-9\s-]/', '', $slug);
        $slug = preg_replace('/\s+/', '-', $slug);
        $slug = preg_replace('/-+/', '-', $slug);
        $slug = trim($slug, '-');
        
        return $slug ?: 'uploaded-tool';
    }
    
    /**
     * 生成备用分析结果
     * @param array $basicInfo 基础文件信息
     * @return array 备用分析结果
     */
    private function generateFallbackAnalysis($basicInfo) {
        $name = $basicInfo['title'] ?? 'Uploaded Tool';
        $slug = $this->generateSlug($name);
        
        $tags = [];
        if (isset($basicInfo['keywords'])) {
            $tags = array_map('trim', explode(',', $basicInfo['keywords']));
        }
        if (empty($tags)) {
            $tags = ['tool', 'utility'];
        }
        
        return [
            'name' => $name,
            'slug' => $slug,
            'description' => $basicInfo['description'] ?? 'A tool uploaded for analysis',
            'tags' => $tags,
            'icon' => '🔧',
            'category' => 'utility',
            'difficulty' => 'intermediate',
            'file_type' => $basicInfo['detected_type'] ?? 'html',
            'confidence' => 0.6,
            'fallback' => true
        ];
    }
    
    /**
     * 检查AI服务是否可用
     * @return bool
     */
    public function isAvailable() {
        return $this->config['glm']['enabled'] &&
               !empty($this->config['glm']['api_key']) &&
               $this->config['glm']['api_key'] !== 'your-glm-api-key-here';
    }
}
?>
