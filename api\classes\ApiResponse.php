<?php
/**
 * API响应类
 * 统一处理API响应格式
 */

class ApiResponse {
    
    /**
     * 成功响应
     */
    public static function success($data = null, $message = 'Success', $code = 200) {
        self::sendResponse([
            'success' => true,
            'message' => $message,
            'data' => $data,
            'timestamp' => time(),
            'request_id' => self::generateRequestId()
        ], $code);
    }
    
    /**
     * 错误响应
     */
    public static function error($error, $code = 400) {
        $response = [
            'success' => false,
            'timestamp' => time(),
            'request_id' => self::generateRequestId()
        ];
        
        if (is_string($error)) {
            $response['message'] = $error;
            $response['error_code'] = 'GENERIC_ERROR';
        } elseif (is_array($error)) {
            $response['message'] = $error['message'] ?? 'An error occurred';
            $response['error_code'] = $error['error_code'] ?? 'GENERIC_ERROR';
            
            if (isset($error['errors'])) {
                $response['errors'] = $error['errors'];
            }
            
            if (isset($error['debug']) && self::isDebugMode()) {
                $response['debug'] = $error['debug'];
            }
        }
        
        self::sendResponse($response, $code);
    }
    
    /**
     * 分页响应
     */
    public static function paginated($data, $pagination, $message = 'Success') {
        self::success([
            'items' => $data,
            'pagination' => $pagination
        ], $message);
    }
    
    /**
     * 验证错误响应
     */
    public static function validationError($errors, $message = 'Validation failed') {
        self::error([
            'message' => $message,
            'error_code' => 'VALIDATION_ERROR',
            'errors' => $errors
        ], 422);
    }
    
    /**
     * 未授权响应
     */
    public static function unauthorized($message = 'Unauthorized') {
        self::error([
            'message' => $message,
            'error_code' => 'UNAUTHORIZED'
        ], 401);
    }
    
    /**
     * 禁止访问响应
     */
    public static function forbidden($message = 'Forbidden') {
        self::error([
            'message' => $message,
            'error_code' => 'FORBIDDEN'
        ], 403);
    }
    
    /**
     * 资源未找到响应
     */
    public static function notFound($message = 'Resource not found') {
        self::error([
            'message' => $message,
            'error_code' => 'NOT_FOUND'
        ], 404);
    }
    
    /**
     * 方法不允许响应
     */
    public static function methodNotAllowed($message = 'Method not allowed') {
        self::error([
            'message' => $message,
            'error_code' => 'METHOD_NOT_ALLOWED'
        ], 405);
    }
    
    /**
     * 频率限制响应
     */
    public static function rateLimited($message = 'Rate limit exceeded', $retryAfter = null) {
        if ($retryAfter) {
            header("Retry-After: $retryAfter");
        }
        
        self::error([
            'message' => $message,
            'error_code' => 'RATE_LIMITED',
            'retry_after' => $retryAfter
        ], 429);
    }
    
    /**
     * 服务器错误响应
     */
    public static function serverError($message = 'Internal server error', $debug = null) {
        $error = [
            'message' => $message,
            'error_code' => 'INTERNAL_ERROR'
        ];
        
        if ($debug && self::isDebugMode()) {
            $error['debug'] = $debug;
        }
        
        self::error($error, 500);
    }
    
    /**
     * 发送响应
     */
    private static function sendResponse($data, $code = 200) {
        // 设置HTTP状态码
        http_response_code($code);
        
        // 设置响应头
        header('Content-Type: application/json; charset=utf-8');
        
        // 添加API版本头
        header('X-API-Version: 1.0');
        
        // 添加响应时间头
        if (defined('API_START_TIME')) {
            $responseTime = round((microtime(true) - API_START_TIME) * 1000, 2);
            header("X-Response-Time: {$responseTime}ms");
        }
        
        // 输出JSON响应
        echo json_encode($data, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        exit;
    }
    
    /**
     * 生成请求ID
     */
    private static function generateRequestId() {
        return uniqid('req_', true);
    }
    
    /**
     * 检查是否为调试模式
     */
    private static function isDebugMode() {
        return defined('API_DEBUG') && API_DEBUG === true;
    }
    
    /**
     * 格式化数据
     */
    public static function formatData($data) {
        if (is_array($data)) {
            return array_map([self::class, 'formatData'], $data);
        }
        
        if (is_object($data)) {
            $formatted = [];
            foreach ($data as $key => $value) {
                $formatted[$key] = self::formatData($value);
            }
            return $formatted;
        }
        
        // 格式化日期时间
        if (is_string($data) && preg_match('/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/', $data)) {
            return [
                'datetime' => $data,
                'timestamp' => strtotime($data),
                'iso8601' => date('c', strtotime($data))
            ];
        }
        
        return $data;
    }
    
    /**
     * 创建分页信息
     */
    public static function createPagination($total, $page, $perPage, $path = null) {
        $lastPage = ceil($total / $perPage);
        $from = ($page - 1) * $perPage + 1;
        $to = min($page * $perPage, $total);
        
        $pagination = [
            'total' => $total,
            'per_page' => $perPage,
            'current_page' => $page,
            'last_page' => $lastPage,
            'from' => $from,
            'to' => $to,
            'has_more' => $page < $lastPage
        ];
        
        if ($path) {
            $pagination['links'] = [
                'first' => $path . '?page=1',
                'last' => $path . '?page=' . $lastPage,
                'prev' => $page > 1 ? $path . '?page=' . ($page - 1) : null,
                'next' => $page < $lastPage ? $path . '?page=' . ($page + 1) : null
            ];
        }
        
        return $pagination;
    }
    
    /**
     * 处理异常
     */
    public static function handleException($exception) {
        $message = 'An unexpected error occurred';
        $code = 500;
        $debug = null;
        
        if ($exception instanceof ApiException) {
            $message = $exception->getMessage();
            $code = $exception->getCode() ?: 400;
        } elseif ($exception instanceof ValidationException) {
            return self::validationError($exception->getErrors(), $exception->getMessage());
        } elseif ($exception instanceof AuthException) {
            $message = $exception->getMessage();
            $code = 401;
        } elseif ($exception instanceof PermissionException) {
            $message = $exception->getMessage();
            $code = 403;
        }
        
        if (self::isDebugMode()) {
            $debug = [
                'exception' => get_class($exception),
                'message' => $exception->getMessage(),
                'file' => $exception->getFile(),
                'line' => $exception->getLine(),
                'trace' => $exception->getTraceAsString()
            ];
        }
        
        self::error([
            'message' => $message,
            'error_code' => 'EXCEPTION_ERROR',
            'debug' => $debug
        ], $code);
    }
}

/**
 * API异常基类
 */
class ApiException extends Exception {}

/**
 * 验证异常
 */
class ValidationException extends Exception {
    private $errors;
    
    public function __construct($message, $errors = []) {
        parent::__construct($message);
        $this->errors = $errors;
    }
    
    public function getErrors() {
        return $this->errors;
    }
}

/**
 * 认证异常
 */
class AuthException extends Exception {}

/**
 * 权限异常
 */
class PermissionException extends Exception {}
?>
