/* 按钮组件样式 - 零圆角设计 */

/* 基础按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    border-radius: 0 !important; /* 零圆角强制 */
    font-family: inherit;
    font-size: 1rem;
    line-height: 1.5;
}

/* 主要按钮 */
.btn-primary {
    background-color: var(--accent-color);
    color: white;
}

.btn-primary:hover {
    background-color: #3d5fd8;
    color: white !important;
    transform: translateY(-1px);
}

.btn-primary:active {
    transform: translateY(0);
}

/* 次要按钮 */
.btn-secondary {
    background-color: transparent;
    color: var(--text-color);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background-color: var(--hover-bg);
    border-color: var(--accent-color);
    color: var(--text-color);
}

/* 成功按钮 */
.btn-success {
    background-color: var(--success-color);
    color: white;
}

.btn-success:hover {
    background-color: #17a673;
}

/* 危险按钮 */
.btn-danger {
    background-color: var(--danger-color);
    color: white;
}

.btn-danger:hover {
    background-color: #c82333;
}

/* 按钮尺寸 */
.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

.btn-lg {
    padding: 1rem 2rem;
    font-size: 1.125rem;
}

/* 按钮状态 */
.btn:disabled,
.btn.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
}

.btn:disabled:hover,
.btn.disabled:hover {
    transform: none;
}

/* 加载状态 */
.btn.loading {
    position: relative;
    color: transparent;
}

.btn.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 1rem;
    height: 1rem;
    margin: -0.5rem 0 0 -0.5rem;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 图标按钮 */
.btn-icon {
    padding: 0.75rem;
    width: auto;
    height: auto;
}

.btn-icon svg {
    width: 1.25rem;
    height: 1.25rem;
}

/* 按钮组 */
.btn-group {
    display: flex;
}

.btn-group .btn:not(:first-child) {
    margin-left: -1px;
}

.btn-group .btn:first-child {
    border-radius: 0 !important;
}

.btn-group .btn:last-child {
    border-radius: 0 !important;
}

/* 全宽按钮 */
.btn-block {
    width: 100%;
    display: flex;
}

/* 响应式按钮 */
@media (max-width: 768px) {
    .btn {
        padding: 0.625rem 1.25rem;
        font-size: 0.875rem;
    }
    
    .btn-lg {
        padding: 0.875rem 1.75rem;
        font-size: 1rem;
    }
    
    .btn-group {
        flex-direction: column;
    }
    
    .btn-group .btn:not(:first-child) {
        margin-left: 0;
        margin-top: -1px;
    }
}
