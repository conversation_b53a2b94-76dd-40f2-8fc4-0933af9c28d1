<?php
/**
 * 首页模板
 * Prompt2Tool - AI驱动的免费在线工具平台
 */

$currentPage = 'home';
$breadcrumbs = []; // 首页不需要面包屑导航

// 包含动态SEO助手
require_once ROOT_PATH . '/app/helpers/dynamic-seo.php';

// 获取动态设置
$seoData = getDynamicSEOData('home');
$companyInfo = getCompanyInfo();

// 使用统一的数据库连接
require_once ROOT_PATH . '/includes/database-connection.php';

// 数据库连接和统计数据查询
try {

    // 查询统计数据
    $stats = [];

    // 1. 统计已发布工具总数
    $toolsStmt = $pdo->query("SELECT COUNT(*) as count FROM pt_tool WHERE status = 'active'");
    $toolsResult = $toolsStmt->fetch();
    $stats['total_tools'] = $toolsResult['count'] ?? 0;

    // 2. 统计注册用户总数
    $usersStmt = $pdo->query("SELECT COUNT(*) as count FROM pt_member WHERE status = 'active'");
    $usersResult = $usersStmt->fetch();
    $stats['total_users'] = $usersResult['count'] ?? 0;

    // 3. 统计工具使用总次数
    $usageStmt = $pdo->query("SELECT COUNT(*) as count FROM pt_tool_usage WHERE status = 'success'");
    $usageResult = $usageStmt->fetch();
    $stats['total_usage'] = $usageResult['count'] ?? 0;

} catch (Exception $e) {
    // 如果数据库查询失败，使用默认值
    error_log("Database query failed in index.php: " . $e->getMessage());
    $stats = [
        'total_tools' => 100,
        'total_users' => 50000,
        'total_usage' => 1000000
    ];
}

// 包含工具辅助函数（包含formatNumber函数）
require_once ROOT_PATH . '/app/helpers/tool-helpers.php';

// 包含公共头部
include_once ROOT_PATH . '/templates/layout/header.php';
?>

<!-- 主要内容区域 -->
<div class="pt-8">
    <!-- Hero区域 -->
    <section class="bg-black py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <!-- 主标题 -->
            <h1 class="text-4xl md:text-6xl lg:text-7xl font-bold mb-6 leading-tight">
                AI-Powered<br>
                <span class="text-accent">Free Online Tools</span>
            </h1>
            
            <!-- 副标题 -->
            <p class="text-xl md:text-2xl text-gray-300 mb-8 max-w-4xl mx-auto leading-relaxed">
                <?= htmlspecialchars($companyInfo['description']) ?>
            </p>
            
            <!-- CTA按钮组 -->
            <div class="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
                <a href="/tools"
                   class="bg-accent text-white px-8 py-4 text-lg font-medium hover:bg-blue-700 hover:text-white transition-all duration-200 transform hover:scale-105">
                    Explore Tools
                </a>
                <a href="/tools/development" 
                   class="border border-gray-600 text-white px-8 py-4 text-lg font-medium hover:bg-gray-800 hover:border-accent transition-all duration-200">
                    Developer Tools
                </a>
            </div>
            
            <!-- 统计数据 -->
            <div class="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-3xl mx-auto">
                <div class="text-center">
                    <div class="text-3xl font-bold text-accent mb-2"><?= formatNumber($stats['total_tools']) ?></div>
                    <div class="text-gray-400 text-sm">Free Tools</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold text-accent mb-2"><?= formatNumber($stats['total_users']) ?></div>
                    <div class="text-gray-400 text-sm">Total Users</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold text-accent mb-2"><?= formatNumber($stats['total_usage']) ?></div>
                    <div class="text-gray-400 text-sm">Tools Used</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold text-accent mb-2">24/7</div>
                    <div class="text-gray-400 text-sm">Available</div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- 工具分类区域 -->
<section class="py-20 bg-gray-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- 区域标题 -->
        <div class="text-center mb-16">
            <h2 class="text-3xl md:text-4xl font-bold mb-4">Tool Categories</h2>
            <p class="text-xl text-gray-400 max-w-2xl mx-auto">
                Choose from our comprehensive collection of tools organized by category
            </p>
        </div>
        
        <!-- 分类网格 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- 开发工具 -->
            <a href="/tools/development" class="bg-black border border-gray-800 p-8 hover:border-accent transition-all duration-300 group cursor-pointer block">
                <div class="text-5xl mb-6 group-hover:scale-110 transition-transform duration-300">🛠️</div>
                <h3 class="text-2xl font-semibold mb-4 group-hover:text-accent transition-colors duration-300">Development</h3>
                <p class="text-gray-400 mb-6 leading-relaxed">
                    HTML, CSS, JavaScript formatters, minifiers, validators, and code generators for developers.
                </p>
                <div class="flex items-center text-accent group-hover:translate-x-2 transition-transform duration-300">
                    <span class="mr-2">Explore Tools</span>
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                    </svg>
                </div>
            </a>
            
            <!-- 设计工具 -->
            <a href="/tools/design" class="bg-black border border-gray-800 p-8 hover:border-accent transition-all duration-300 group cursor-pointer block">
                <div class="text-5xl mb-6 group-hover:scale-110 transition-transform duration-300">🎨</div>
                <h3 class="text-2xl font-semibold mb-4 group-hover:text-accent transition-colors duration-300">Design & Media</h3>
                <p class="text-gray-400 mb-6 leading-relaxed">
                    Image converters, compressors, generators, and design utilities for creative professionals.
                </p>
                <div class="flex items-center text-accent group-hover:translate-x-2 transition-transform duration-300">
                    <span class="mr-2">Explore Tools</span>
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                    </svg>
                </div>
            </a>
            
            <!-- 生产力工具 -->
            <a href="/tools/productivity" class="bg-black border border-gray-800 p-8 hover:border-accent transition-all duration-300 group cursor-pointer block">
                <div class="text-5xl mb-6 group-hover:scale-110 transition-transform duration-300">📝</div>
                <h3 class="text-2xl font-semibold mb-4 group-hover:text-accent transition-colors duration-300">Productivity</h3>
                <p class="text-gray-400 mb-6 leading-relaxed">
                    Text processors, encoders, generators, and productivity tools to streamline your workflow.
                </p>
                <div class="flex items-center text-accent group-hover:translate-x-2 transition-transform duration-300">
                    <span class="mr-2">Explore Tools</span>
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                    </svg>
                </div>
            </a>
            
            <!-- 营销工具 -->
            <a href="/tools/marketing" class="bg-black border border-gray-800 p-8 hover:border-accent transition-all duration-300 group cursor-pointer block">
                <div class="text-5xl mb-6 group-hover:scale-110 transition-transform duration-300">📈</div>
                <h3 class="text-2xl font-semibold mb-4 group-hover:text-accent transition-colors duration-300">Marketing & SEO</h3>
                <p class="text-gray-400 mb-6 leading-relaxed">
                    SEO analyzers, meta tag generators, and marketing tools to boost your online presence.
                </p>
                <div class="flex items-center text-accent group-hover:translate-x-2 transition-transform duration-300">
                    <span class="mr-2">Explore Tools</span>
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                    </svg>
                </div>
            </a>
            
            <!-- 实用工具 -->
            <a href="/tools/utilities" class="bg-black border border-gray-800 p-8 hover:border-accent transition-all duration-300 group cursor-pointer block">
                <div class="text-5xl mb-6 group-hover:scale-110 transition-transform duration-300">🔧</div>
                <h3 class="text-2xl font-semibold mb-4 group-hover:text-accent transition-colors duration-300">Utilities</h3>
                <p class="text-gray-400 mb-6 leading-relaxed">
                    Converters, calculators, generators, and various utility tools for everyday tasks.
                </p>
                <div class="flex items-center text-accent group-hover:translate-x-2 transition-transform duration-300">
                    <span class="mr-2">Explore Tools</span>
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                    </svg>
                </div>
            </a>
            
            <!-- 安全工具 -->
            <a href="/tools/security" class="bg-black border border-gray-800 p-8 hover:border-accent transition-all duration-300 group cursor-pointer block">
                <div class="text-5xl mb-6 group-hover:scale-110 transition-transform duration-300">🔒</div>
                <h3 class="text-2xl font-semibold mb-4 group-hover:text-accent transition-colors duration-300">Security & Privacy</h3>
                <p class="text-gray-400 mb-6 leading-relaxed">
                    Password generators, hash tools, encryption utilities, and security-focused tools.
                </p>
                <div class="flex items-center text-accent group-hover:translate-x-2 transition-transform duration-300">
                    <span class="mr-2">Explore Tools</span>
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                    </svg>
                </div>
            </a>
        </div>
    </div>
</section>

<!-- 精选工具区域 -->
<section class="py-20 bg-black">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <?php
        // 引入热门工具组件
        include_once ROOT_PATH . '/templates/components/tools/popular-tools.php';

        // 渲染精选工具区域
        renderFeaturedToolsSection();
        ?>
    </div>
</section>

<!-- 热门工具区域 -->
<section class="py-20 bg-gray-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <?php
        // 渲染热门工具 (显示标题和滑动功能)
        renderPopularToolsSection(8, 'default', true);
        ?>
    </div>
</section>

<!-- 最新工具区域 -->
<section class="py-20 bg-black">
    <div class="container mx-auto px-6">
        <?php
        // 渲染最新工具
        renderNewToolsSection(8, 'default', true);
        ?>
    </div>
</section>

<!-- 特性介绍区域 -->
<section class="py-20 bg-gray-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <!-- 左侧内容 -->
            <div>
                <h2 class="text-3xl md:text-4xl font-bold mb-6">
                    Why Choose <span class="text-accent">Prompt2Tool</span>?
                </h2>
                <p class="text-xl text-gray-400 mb-8 leading-relaxed">
                    We provide the most comprehensive collection of free online tools, 
                    powered by AI and designed for modern workflows.
                </p>
                
                <!-- 特性列表 -->
                <div class="space-y-6">
                    <div class="flex items-start">
                        <div class="bg-accent p-2 mr-4 mt-1">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold mb-2">100% Free Forever</h3>
                            <p class="text-gray-400">All tools are completely free with no hidden costs or premium tiers.</p>
                        </div>
                    </div>
                    
                    <div class="flex items-start">
                        <div class="bg-accent p-2 mr-4 mt-1">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold mb-2">AI-Powered</h3>
                            <p class="text-gray-400">Enhanced with artificial intelligence for better accuracy and performance.</p>
                        </div>
                    </div>
                    
                    <div class="flex items-start">
                        <div class="bg-accent p-2 mr-4 mt-1">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold mb-2">Privacy First</h3>
                            <p class="text-gray-400">Your data is processed locally and never stored on our servers.</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 右侧图片/动画区域 -->
            <div class="relative">
                <div class="bg-gradient-to-br from-accent to-blue-600 p-8 transform rotate-3">
                    <div class="bg-black p-8 transform -rotate-3">
                        <div class="text-center">
                            <div class="text-6xl mb-4">⚡</div>
                            <h3 class="text-2xl font-bold mb-4">Lightning Fast</h3>
                            <p class="text-gray-400">
                                All tools are optimized for speed and performance
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- 工具滑动功能JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Featured Tools滑动功能
    initToolSlider('featured');

    // Popular Tools滑动功能
    initToolSlider('popular');

    // New Tools滑动功能
    initToolSlider('new');
});

function initToolSlider(type) {
    const slider = document.getElementById(type + 'Slider');
    const prevBtn = document.getElementById(type + 'Prev');
    const nextBtn = document.getElementById(type + 'Next');

    if (!slider || !prevBtn || !nextBtn) return;

    const slideContainer = slider.querySelector('.flex');
    const slides = slideContainer.children;
    const totalSlides = slides.length;

    if (totalSlides <= 3) {
        // 如果工具数量不超过3个，隐藏箭头
        prevBtn.style.display = 'none';
        nextBtn.style.display = 'none';
        return;
    }

    let currentIndex = 0;
    const maxIndex = totalSlides - 3; // 一次显示3个

    function updateSlider() {
        const translateX = -(currentIndex * (100 / 3));
        slideContainer.style.transform = `translateX(${translateX}%)`;

        // 更新按钮状态
        prevBtn.disabled = currentIndex === 0;
        nextBtn.disabled = currentIndex >= maxIndex;

        prevBtn.style.opacity = currentIndex === 0 ? '0.5' : '1';
        nextBtn.style.opacity = currentIndex >= maxIndex ? '0.5' : '1';
    }

    prevBtn.addEventListener('click', function() {
        if (currentIndex > 0) {
            currentIndex--;
            updateSlider();
        }
    });

    nextBtn.addEventListener('click', function() {
        if (currentIndex < maxIndex) {
            currentIndex++;
            updateSlider();
        }
    });

    // 初始化
    updateSlider();
}


</script>
</div>

<?php
// 包含公共底部
include_once ROOT_PATH . '/templates/layout/footer.php';
?>
