<?php
/**
 * 工具状态警告组件
 * 用于显示非发布状态工具的警告信息
 */

// 获取当前工具的状态信息
if (!isset($toolSlug)) {
    // 从URL中提取工具slug
    $requestUri = $_SERVER['REQUEST_URI'];
    if (preg_match('/\/tools\/[^\/]+\/([^\/\?]+)/', $requestUri, $matches)) {
        $toolSlug = $matches[1];
    }
}

if (isset($toolSlug)) {
    require_once ROOT_PATH . '/app/helpers/tool-helpers.php';
    $toolData = getToolBySlug($toolSlug);
    
    if ($toolData && $toolData['status'] !== 'active') {
        $showStatusWarning = true;
        $toolStatus = $toolData['status'];
    }
}
?>

<?php if (isset($showStatusWarning) && $showStatusWarning): ?>
    <!-- 状态警告提示 -->
    <div class="mb-8 max-w-4xl mx-auto">
        <div class="bg-yellow-900 border border-yellow-600 rounded-lg p-4">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-yellow-400">
                        <?php if ($toolStatus === 'coming_soon'): ?>
                            Tool Under Review
                        <?php elseif ($toolStatus === 'inactive'): ?>
                            Tool Currently Inactive
                        <?php endif; ?>
                    </h3>
                    <div class="mt-1 text-sm text-yellow-300">
                        <?php if ($toolStatus === 'coming_soon'): ?>
                            This tool is currently under review and not yet publicly available. You can preview it because you're logged in.
                        <?php elseif ($toolStatus === 'inactive'): ?>
                            This tool is currently inactive and not publicly available. You can preview it because you're logged in.
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php endif; ?>
