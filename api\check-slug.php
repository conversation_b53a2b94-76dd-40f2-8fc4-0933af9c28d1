<?php
/**
 * 检查工具slug是否重复API
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

// 检查登录状态
session_start();
if (!isset($_SESSION['admin_logged_in']) || !$_SESSION['admin_logged_in']) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit;
}

// 只允许GET请求
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit;
}

$slug = $_GET['slug'] ?? '';
$excludeId = $_GET['exclude_id'] ?? null;

if (empty($slug)) {
    echo json_encode(['success' => false, 'error' => 'Slug is required']);
    exit;
}

// 使用统一的数据库连接
require_once dirname(__DIR__) . '/includes/database-connection.php';

try {

    // 检查slug是否已存在
    $sql = "SELECT id FROM pt_tool WHERE slug = ?";
    $params = [$slug];

    // 如果是编辑模式，排除当前工具的ID
    if ($excludeId) {
        $sql .= " AND id != ?";
        $params[] = $excludeId;
    }

    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $existingTool = $stmt->fetch();

    if ($existingTool) {
        echo json_encode([
            'success' => true,
            'available' => false,
            'message' => 'This slug is already in use'
        ]);
    } else {
        echo json_encode([
            'success' => true,
            'available' => true,
            'message' => 'Slug is available'
        ]);
    }

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => 'Database error: ' . $e->getMessage(),
        'debug' => [
            'slug' => $slug,
            'exclude_id' => $excludeId,
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]
    ]);
}
?>
