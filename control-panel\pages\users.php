<?php
/**
 * 用户管理主页面
 * 显示用户列表和管理功能
 */



// 使用统一的数据库连接
require_once ROOT_PATH . '/includes/database-connection.php';

// 获取数据库连接
try {

    // 获取筛选参数
    $search = $_GET['search'] ?? '';
    $status = $_GET['status'] ?? '';
    $currentPage = max(1, intval($_GET['p'] ?? 1)); // 使用 'p' 避免与控制面板的 'page' 参数冲突
    $perPage = 10;
    $offset = ($currentPage - 1) * $perPage;

    // 构建查询条件
    $whereConditions = [];
    $params = [];

    if (!empty($search)) {
        $whereConditions[] = "(username LIKE :search OR email LIKE :search OR first_name LIKE :search OR last_name LIKE :search)";
        $params['search'] = "%{$search}%";
    }

    if (!empty($status)) {
        $whereConditions[] = "status = :status";
        $params['status'] = $status;
    }

    $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

    // 检查pt_member表是否存在
    $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    $usersTableExists = in_array('pt_member', $tables);

    if ($usersTableExists) {
        // 获取用户总数
        $totalQuery = "SELECT COUNT(*) FROM pt_member {$whereClause}";
        $stmt = $pdo->prepare($totalQuery);
        $stmt->execute($params);
        $totalUsers = $stmt->fetchColumn();

        // 获取用户列表
        $usersQuery = "
            SELECT id, username, email, first_name, last_name, status,
                   created_at, last_login, subscription_type
            FROM pt_member
            {$whereClause}
            ORDER BY created_at DESC
            LIMIT :limit OFFSET :offset
        ";
        $stmt = $pdo->prepare($usersQuery);
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }
        $stmt->bindValue(':limit', $perPage, PDO::PARAM_INT);
        $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();
        $users = $stmt->fetchAll();

        // 获取统计数据
        $stats = [
            'total' => $pdo->query("SELECT COUNT(*) FROM pt_member")->fetchColumn(),
            'active' => $pdo->query("SELECT COUNT(*) FROM pt_member WHERE status = 'active'")->fetchColumn(),
            'inactive' => $pdo->query("SELECT COUNT(*) FROM pt_member WHERE status = 'inactive'")->fetchColumn(),
            'pending' => $pdo->query("SELECT COUNT(*) FROM pt_member WHERE status = 'pending'")->fetchColumn()
        ];
    } else {
        // 如果pt_member表不存在，使用模拟数据
        $totalUsers = 0;
        $users = [];
        $stats = ['total' => 0, 'active' => 0, 'inactive' => 0, 'pending' => 0];
    }

    $totalPages = ceil($totalUsers / $perPage);

} catch (Exception $e) {
    error_log('User management error: ' . $e->getMessage());
    $totalUsers = 0;
    $users = [];
    $stats = ['total' => 0, 'active' => 0, 'inactive' => 0, 'pending' => 0];
    $totalPages = 0;
    $usersTableExists = false;
}




?>

<div class="space-y-6">
    <!-- 页面描述和操作 -->
    <div class="flex items-center justify-between">
        <div>
            <p class="text-gray-600">Manage platform users and their accounts</p>
        </div>
        <div class="flex space-x-3">
            <?php if ($usersTableExists): ?>
            <form method="POST" style="display: inline;">
                <input type="hidden" name="action" value="export_users">
                <input type="hidden" name="search" value="<?= htmlspecialchars($search) ?>">
                <input type="hidden" name="status" value="<?= htmlspecialchars($status) ?>">
                <button type="submit" class="bg-green-600 text-white px-4 py-2 hover:bg-green-700 rounded">
                    Export Users
                </button>
            </form>
            <button onclick="openCreateUserModal()" class="bg-blue-600 text-white px-4 py-2 hover:bg-blue-700">
                <i class="fas fa-plus mr-2"></i>Add User
            </button>
            <?php endif; ?>
        </div>
    </div>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="bg-white p-6 rounded-lg shadow">
            <div class="flex items-center">
                <div class="p-2 bg-blue-100 rounded-lg">
                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Users</p>
                    <p class="text-2xl font-bold text-gray-900"><?= number_format($stats['total']) ?></p>
                </div>
            </div>
        </div>

        <div class="bg-white p-6 rounded-lg shadow">
            <div class="flex items-center">
                <div class="p-2 bg-green-100 rounded-lg">
                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Active Users</p>
                    <p class="text-2xl font-bold text-gray-900"><?= number_format($stats['active']) ?></p>
                </div>
            </div>
        </div>

        <div class="bg-white p-6 rounded-lg shadow">
            <div class="flex items-center">
                <div class="p-2 bg-yellow-100 rounded-lg">
                    <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Pending</p>
                    <p class="text-2xl font-bold text-gray-900"><?= number_format($stats['pending']) ?></p>
                </div>
            </div>
        </div>

        <div class="bg-white p-6 rounded-lg shadow">
            <div class="flex items-center">
                <div class="p-2 bg-red-100 rounded-lg">
                    <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Inactive</p>
                    <p class="text-2xl font-bold text-gray-900"><?= number_format($stats['inactive']) ?></p>
                </div>
            </div>
        </div>
    </div>

    <!-- 筛选和搜索 -->
    <div class="bg-white p-6 rounded-lg shadow">
        <form method="GET" class="flex flex-wrap gap-4">
            <input type="hidden" name="page" value="users">
            <div class="flex-1 min-w-64">
                <label class="block text-sm font-medium text-gray-700 mb-2">Search Users</label>
                <input type="text" name="search" value="<?= htmlspecialchars($search) ?>" 
                       placeholder="Search by username, email, or name..."
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>
            <div class="w-48">
                <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                <select name="status" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="">All Status</option>
                    <option value="active" <?= $status === 'active' ? 'selected' : '' ?>>Active</option>
                    <option value="inactive" <?= $status === 'inactive' ? 'selected' : '' ?>>Inactive</option>
                    <option value="pending" <?= $status === 'pending' ? 'selected' : '' ?>>Pending</option>
                    <option value="suspended" <?= $status === 'suspended' ? 'selected' : '' ?>>Suspended</option>
                </select>
            </div>
            <div class="flex items-end">
                <button type="submit" class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                    <i class="fas fa-search mr-2"></i>Search
                </button>
            </div>
            <?php if (!empty($search) || !empty($status)): ?>
            <div class="flex items-end">
                <a href="?page=users" class="px-6 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700">
                    <i class="fas fa-times mr-2"></i>Clear
                </a>
            </div>
            <?php endif; ?>
        </form>
    </div>

    <!-- 用户列表 -->
    <?php if (!$usersTableExists): ?>
    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
        <div class="flex items-center">
            <svg class="w-6 h-6 text-yellow-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
            </svg>
            <div>
                <h3 class="text-lg font-medium text-yellow-800">Users Table Not Found</h3>
                <p class="text-yellow-700 mt-1">The users table doesn't exist in the database. Please run the database migration to create the users table.</p>
                <div class="mt-4">
                    <button onclick="createUsersTable()" class="bg-yellow-600 text-white px-4 py-2 rounded hover:bg-yellow-700">
                        Create Users Table
                    </button>
                </div>
            </div>
        </div>
    </div>
    <?php elseif (empty($users)): ?>
    <div class="bg-white rounded-lg shadow p-6 text-center">
        <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
        </svg>
        <h3 class="text-lg font-medium text-gray-900 mb-2">No Users Found</h3>
        <p class="text-gray-600 mb-4">
            <?= !empty($search) || !empty($status) ? 'No users match your search criteria.' : 'No users have been created yet.' ?>
        </p>
        <?php if (empty($search) && empty($status)): ?>
        <button onclick="openCreateUserModal()" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
            <i class="fas fa-plus mr-2"></i>Create First User
        </button>
        <?php endif; ?>
    </div>
    <?php else: ?>
    <div class="bg-white rounded-lg shadow overflow-hidden">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subscription</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Joined</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Login</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php foreach ($users as $user): ?>
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
                                    <span class="text-sm font-medium text-gray-700">
                                        <?= strtoupper(substr($user['username'] ?? $user['email'], 0, 1)) ?>
                                    </span>
                                </div>
                                <div class="ml-4">
                                    <div class="text-sm font-medium text-gray-900">
                                        <?= htmlspecialchars($user['first_name'] . ' ' . $user['last_name']) ?>
                                    </div>
                                    <div class="text-sm text-gray-500"><?= htmlspecialchars($user['email']) ?></div>
                                    <?php if (!empty($user['username'])): ?>
                                    <div class="text-xs text-gray-400">@<?= htmlspecialchars($user['username']) ?></div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                <?php
                                switch ($user['status']) {
                                    case 'active': echo 'bg-green-100 text-green-800'; break;
                                    case 'inactive': echo 'bg-red-100 text-red-800'; break;
                                    case 'pending': echo 'bg-yellow-100 text-yellow-800'; break;
                                    case 'suspended': echo 'bg-gray-100 text-gray-800'; break;
                                    default: echo 'bg-gray-100 text-gray-800';
                                }
                                ?>">
                                <?= ucfirst($user['status']) ?>
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            <?= ucfirst($user['subscription_type'] ?? 'free') ?>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            <?= date('M j, Y', strtotime($user['created_at'])) ?>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            <?= $user['last_login'] ? timeAgo($user['last_login']) : 'Never' ?>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div class="flex items-center space-x-4">
                                <!-- View User -->
                                <button onclick="viewUser(<?= $user['id'] ?>)"
                                        class="inline-flex items-center px-2 py-1 text-blue-600 hover:text-blue-900 hover:bg-blue-50 rounded transition-colors"
                                        title="View Details">
                                    <i class="fas fa-eye text-sm"></i>
                                </button>

                                <!-- Edit User -->
                                <button onclick="editUser(<?= $user['id'] ?>)"
                                        class="inline-flex items-center px-2 py-1 text-indigo-600 hover:text-indigo-900 hover:bg-indigo-50 rounded transition-colors"
                                        title="Edit User">
                                    <i class="fas fa-edit text-sm"></i>
                                </button>

                                <!-- Toggle Status -->
                                <button onclick="toggleUserStatus(<?= $user['id'] ?>, '<?= $user['status'] === 'active' ? 'inactive' : 'active' ?>')"
                                        class="inline-flex items-center px-2 py-1 <?= $user['status'] === 'active' ? 'text-red-600 hover:text-red-900 hover:bg-red-50' : 'text-green-600 hover:text-green-900 hover:bg-green-50' ?> rounded transition-colors"
                                        title="<?= $user['status'] === 'active' ? 'Deactivate' : 'Activate' ?> User">
                                    <i class="fas fa-<?= $user['status'] === 'active' ? 'ban' : 'check' ?> text-sm"></i>
                                </button>

                                <!-- Delete User -->
                                <button onclick="deleteUser(<?= $user['id'] ?>)"
                                        class="inline-flex items-center px-2 py-1 text-red-600 hover:text-red-900 hover:bg-red-50 rounded transition-colors"
                                        title="Delete User">
                                    <i class="fas fa-trash text-sm"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>

        <!-- 分页 -->
        <?php if ($totalPages > 1): ?>
        <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
            <div class="flex items-center justify-between">
                <div class="flex-1 flex justify-between sm:hidden">
                    <?php if ($currentPage > 1): ?>
                    <a href="?page=users&<?= http_build_query(array_merge($_GET, ['p' => $currentPage - 1])) ?>"
                       class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        Previous
                    </a>
                    <?php endif; ?>
                    <?php if ($currentPage < $totalPages): ?>
                    <a href="?page=users&<?= http_build_query(array_merge($_GET, ['p' => $currentPage + 1])) ?>"
                       class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                        Next
                    </a>
                    <?php endif; ?>
                </div>
                <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                        <p class="text-sm text-gray-700">
                            Showing <span class="font-medium"><?= $offset + 1 ?></span> to
                            <span class="font-medium"><?= min($offset + $perPage, $totalUsers) ?></span> of
                            <span class="font-medium"><?= $totalUsers ?></span> results
                        </p>
                    </div>
                    <div>
                        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                            <?php if ($currentPage > 1): ?>
                            <a href="?page=users&<?= http_build_query(array_merge($_GET, ['p' => $currentPage - 1])) ?>"
                               class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                <i class="fas fa-chevron-left"></i>
                            </a>
                            <?php endif; ?>

                            <?php for ($i = max(1, $currentPage - 2); $i <= min($totalPages, $currentPage + 2); $i++): ?>
                            <a href="?page=users&<?= http_build_query(array_merge($_GET, ['p' => $i])) ?>"
                               class="relative inline-flex items-center px-4 py-2 border text-sm font-medium
                                      <?= $i === $currentPage ? 'z-10 bg-blue-50 border-blue-500 text-blue-600' : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50' ?>">
                                <?= $i ?>
                            </a>
                            <?php endfor; ?>

                            <?php if ($currentPage < $totalPages): ?>
                            <a href="?page=users&<?= http_build_query(array_merge($_GET, ['p' => $currentPage + 1])) ?>"
                               class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                <i class="fas fa-chevron-right"></i>
                            </a>
                            <?php endif; ?>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
    <?php endif; ?>
</div>

<script>
// 用户管理JavaScript功能

// 查看用户详情
function viewUser(userId) {
    // 通过AJAX获取用户详情并显示在模态框中
    fetch(window.location.href, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `action=get_user_details&user_id=${userId}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showUserDetailsModal(data.user);
        } else {
            showToast('Failed to load user details: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('Failed to load user details', 'error');
    });
}

// 显示用户详情模态框
function showUserDetailsModal(user) {
    const modal = `
    <div id="userDetailsModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-10 mx-auto p-6 border w-full max-w-2xl shadow-lg rounded-lg bg-white">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-xl font-semibold text-gray-900">User Details</h3>
                <button onclick="closeUserDetailsModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- 左列 -->
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Username</label>
                        <div class="px-3 py-2 bg-gray-50 border border-gray-200 rounded-md text-sm">
                            ${user.username || 'N/A'}
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                        <div class="px-3 py-2 bg-gray-50 border border-gray-200 rounded-md text-sm">
                            ${user.email || 'N/A'}
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Full Name</label>
                        <div class="px-3 py-2 bg-gray-50 border border-gray-200 rounded-md text-sm">
                            ${(user.first_name || '') + ' ' + (user.last_name || '') || 'N/A'}
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                        <div class="px-3 py-2 bg-gray-50 border border-gray-200 rounded-md text-sm">
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(user.status)}">
                                ${user.status ? user.status.charAt(0).toUpperCase() + user.status.slice(1) : 'N/A'}
                            </span>
                        </div>
                    </div>
                </div>

                <!-- 右列 -->
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Subscription</label>
                        <div class="px-3 py-2 bg-gray-50 border border-gray-200 rounded-md text-sm">
                            ${user.subscription_type ? user.subscription_type.charAt(0).toUpperCase() + user.subscription_type.slice(1) : 'Free'}
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Email Verified</label>
                        <div class="px-3 py-2 bg-gray-50 border border-gray-200 rounded-md text-sm">
                            <span class="inline-flex items-center">
                                <i class="fas fa-${user.email_verified ? 'check-circle text-green-500' : 'times-circle text-red-500'} mr-2"></i>
                                ${user.email_verified ? 'Verified' : 'Not Verified'}
                            </span>
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">API Usage</label>
                        <div class="px-3 py-2 bg-gray-50 border border-gray-200 rounded-md text-sm">
                            ${user.api_used || 0} / ${user.api_quota || 0} requests
                            <div class="w-full bg-gray-200 rounded-full h-2 mt-1">
                                <div class="bg-blue-600 h-2 rounded-full" style="width: ${Math.min(100, (user.api_used || 0) / (user.api_quota || 1) * 100)}%"></div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Member Since</label>
                        <div class="px-3 py-2 bg-gray-50 border border-gray-200 rounded-md text-sm">
                            ${user.created_at ? new Date(user.created_at).toLocaleDateString() : 'N/A'}
                        </div>
                    </div>
                </div>
            </div>

            <div class="flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-200">
                <button onclick="closeUserDetailsModal()"
                        class="px-6 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors">
                    Close
                </button>
                <button onclick="closeUserDetailsModal(); editUser(${user.id})"
                        class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                    <i class="fas fa-edit mr-2"></i>Edit User
                </button>
            </div>
        </div>
    </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modal);
}

// 关闭用户详情模态框
function closeUserDetailsModal() {
    const modal = document.getElementById('userDetailsModal');
    if (modal) {
        modal.remove();
    }
}

// 获取状态颜色类
function getStatusColor(status) {
    switch (status) {
        case 'active': return 'bg-green-100 text-green-800';
        case 'inactive': return 'bg-red-100 text-red-800';
        case 'pending': return 'bg-yellow-100 text-yellow-800';
        case 'suspended': return 'bg-gray-100 text-gray-800';
        default: return 'bg-gray-100 text-gray-800';
    }
}

// 编辑用户
function editUser(userId) {
    // 通过AJAX获取用户信息并显示编辑模态框
    fetch(window.location.href, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `action=get_user_details&user_id=${userId}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showEditUserModal(data.user);
        } else {
            showToast('Failed to load user details: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('Failed to load user details', 'error');
    });
}

// 显示编辑用户模态框
function showEditUserModal(user) {
    const modal = `
    <div id="editUserModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-10 mx-auto p-6 border w-full max-w-2xl shadow-lg rounded-lg bg-white">
            <div class="mb-4">
                <div class="flex items-center justify-between">
                    <h3 class="text-xl font-semibold text-gray-900">Edit User</h3>
                    <button onclick="closeEditUserModal()" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
            </div>
            <form id="editUserForm">
                <input type="hidden" name="user_id" value="${user.id}">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- 左列 -->
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Username *</label>
                            <input type="text" name="username" value="${user.username || ''}" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Email *</label>
                            <input type="email" name="email" value="${user.email || ''}" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">First Name</label>
                            <input type="text" name="first_name" value="${user.first_name || ''}"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Last Name</label>
                            <input type="text" name="last_name" value="${user.last_name || ''}"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>
                    </div>

                    <!-- 右列 -->
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                            <select name="status" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option value="active" ${user.status === 'active' ? 'selected' : ''}>Active</option>
                                <option value="pending" ${user.status === 'pending' ? 'selected' : ''}>Pending</option>
                                <option value="inactive" ${user.status === 'inactive' ? 'selected' : ''}>Inactive</option>
                                <option value="suspended" ${user.status === 'suspended' ? 'selected' : ''}>Suspended</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Subscription Type</label>
                            <select name="subscription_type" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option value="free" ${user.subscription_type === 'free' ? 'selected' : ''}>Free</option>
                                <option value="basic" ${user.subscription_type === 'basic' ? 'selected' : ''}>Basic</option>
                                <option value="premium" ${user.subscription_type === 'premium' ? 'selected' : ''}>Premium</option>
                                <option value="enterprise" ${user.subscription_type === 'enterprise' ? 'selected' : ''}>Enterprise</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">API Quota</label>
                            <input type="number" name="api_quota" value="${user.api_quota || 1000}" min="0"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Email Verified</label>
                            <select name="email_verified" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option value="1" ${user.email_verified ? 'selected' : ''}>Verified</option>
                                <option value="0" ${!user.email_verified ? 'selected' : ''}>Not Verified</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- 底部按钮 -->
                <div class="flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-200">
                    <button type="button" onclick="closeEditUserModal()"
                            class="px-6 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors">
                        Cancel
                    </button>
                    <button type="submit"
                            class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center">
                        <i class="fas fa-save mr-2"></i>
                        Update User
                    </button>
                </div>
            </form>
        </div>
    </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modal);

    // 绑定表单提交事件
    document.getElementById('editUserForm').addEventListener('submit', function(e) {
        e.preventDefault();
        updateUser();
    });
}

// 关闭编辑用户模态框
function closeEditUserModal() {
    const modal = document.getElementById('editUserModal');
    if (modal) {
        modal.remove();
    }
}

// 更新用户
function updateUser() {
    const form = document.getElementById('editUserForm');
    const formData = new FormData(form);
    formData.append('action', 'update_user');

    fetch(window.location.href, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast(data.message, 'success');
            closeEditUserModal();
            setTimeout(() => window.location.reload(), 1000);
        } else {
            showToast(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('An error occurred', 'error');
    });
}

// 切换用户状态
function toggleUserStatus(userId, newStatus) {
    const action = newStatus === 'active' ? 'activate' : 'deactivate';
    showConfirmDialog(
        `${action.charAt(0).toUpperCase() + action.slice(1)} User`,
        `Are you sure you want to ${action} this user?`,
        `confirmToggleUserStatus(${userId}, '${newStatus}')`
    );
}

// 确认切换用户状态
function confirmToggleUserStatus(userId, newStatus) {

    fetch(window.location.href, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `action=toggle_status&user_id=${userId}&status=${newStatus}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast(data.message, 'success');
            setTimeout(() => window.location.reload(), 1000);
        } else {
            showToast(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('An error occurred', 'error');
    });
}

// 删除用户
function deleteUser(userId) {
    showConfirmDialog(
        'Delete User',
        'Are you sure you want to delete this user? This action cannot be undone.',
        `confirmDeleteUser(${userId})`
    );
}

// 确认删除用户
function confirmDeleteUser(userId) {

    fetch(window.location.href, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `action=delete_user&user_id=${userId}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast(data.message, 'success');
            setTimeout(() => window.location.reload(), 1000);
        } else {
            showToast(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('An error occurred', 'error');
    });
}



// 打开创建用户模态框
function openCreateUserModal() {
    const modal = `
    <div id="createUserModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
        <div class="relative top-10 mx-auto p-6 border w-full max-w-2xl shadow-lg rounded-lg bg-white">
            <div class="mb-4">
                <div class="flex items-center justify-between">
                    <h3 class="text-xl font-semibold text-gray-900">Create New User</h3>
                    <button onclick="closeCreateUserModal()" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
            </div>
            <form id="createUserForm">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- 左列 -->
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Username *</label>
                            <input type="text" name="username" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Email *</label>
                            <input type="email" name="email" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Password *</label>
                            <div class="relative">
                                <input type="password" name="password" id="passwordInput" required
                                       class="w-full px-3 py-2 pr-20 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <button type="button" onclick="generateRandomPassword()"
                                        class="absolute right-2 top-1/2 transform -translate-y-1/2 text-blue-600 hover:text-blue-800 text-sm font-medium">
                                    Generate
                                </button>
                            </div>
                            <div class="mt-1 text-xs text-gray-500">
                                <span id="passwordStrength"></span>
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">First Name</label>
                            <input type="text" name="first_name"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>
                    </div>

                    <!-- 右列 -->
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Last Name</label>
                            <input type="text" name="last_name"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                            <select name="status" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option value="active">Active</option>
                                <option value="pending" selected>Pending</option>
                                <option value="inactive">Inactive</option>
                                <option value="suspended">Suspended</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Subscription Type</label>
                            <select name="subscription_type" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option value="free" selected>Free</option>
                                <option value="basic">Basic</option>
                                <option value="premium">Premium</option>
                                <option value="enterprise">Enterprise</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">API Quota</label>
                            <input type="number" name="api_quota" value="1000" min="0"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>
                    </div>
                </div>

                <!-- 底部按钮 -->
                <div class="flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-200">
                    <button type="button" onclick="closeCreateUserModal()"
                            class="px-6 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors">
                        Cancel
                    </button>
                    <button type="submit"
                            class="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center">
                        <i class="fas fa-user-plus mr-2"></i>
                        Create User
                    </button>
                </div>
            </form>
        </div>
    </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modal);

    // 绑定表单提交事件
    document.getElementById('createUserForm').addEventListener('submit', function(e) {
        e.preventDefault();
        createUser();
    });
}

// 关闭创建用户模态框
function closeCreateUserModal() {
    const modal = document.getElementById('createUserModal');
    if (modal) {
        modal.remove();
    }
}

// 创建用户
function createUser() {
    const form = document.getElementById('createUserForm');
    const formData = new FormData(form);
    formData.append('action', 'create_user');

    fetch(window.location.href, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast(data.message, 'success');
            closeCreateUserModal();
            setTimeout(() => window.location.reload(), 1000);
        } else {
            showToast(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('An error occurred', 'error');
    });
}

// 创建用户表
function createUsersTable() {
    showConfirmDialog(
        'Create Users Table',
        'This will create the users table in the database. Continue?',
        'confirmCreateUsersTable'
    );
}

// 确认创建用户表
function confirmCreateUsersTable() {
    fetch('/install/create_users_table.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('Users table created successfully!', 'success');
            setTimeout(() => window.location.reload(), 2000);
        } else {
            showToast(data.message || 'Failed to create users table', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('An error occurred while creating the table', 'error');
    });
}

// 显示美观的提示消息
function showToast(message, type = 'success') {
    // 移除现有的toast
    const existingToast = document.querySelector('.custom-toast');
    if (existingToast) {
        existingToast.remove();
    }

    const toast = document.createElement('div');
    toast.className = `custom-toast fixed top-4 right-4 px-6 py-4 rounded-lg shadow-lg z-50 transform transition-all duration-300 ${
        type === 'error' ? 'bg-red-50 border border-red-200' : 'bg-green-50 border border-green-200'
    }`;

    const iconColor = type === 'error' ? 'text-red-500' : 'text-green-500';
    const textColor = type === 'error' ? 'text-red-800' : 'text-green-800';
    const iconName = type === 'error' ? 'exclamation-circle' : 'check-circle';

    toast.innerHTML = `
        <div class="flex items-center">
            <i class="fas fa-${iconName} ${iconColor} mr-3 text-lg"></i>
            <span class="${textColor} font-medium">${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" class="ml-4 ${textColor} hover:opacity-70">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;

    // 添加动画效果
    toast.style.transform = 'translateX(100%)';
    document.body.appendChild(toast);

    // 触发动画
    setTimeout(() => {
        toast.style.transform = 'translateX(0)';
    }, 10);

    // 自动移除toast
    setTimeout(() => {
        if (toast.parentNode) {
            toast.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }
    }, 4000);
}

// 显示确认对话框
function showConfirmDialog(title, message, onConfirm, onCancel = null) {
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50';
    modal.innerHTML = `
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3 text-center">
                <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-yellow-100 mb-4">
                    <i class="fas fa-exclamation-triangle text-yellow-600 text-xl"></i>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">${title}</h3>
                <p class="text-sm text-gray-500 mb-6">${message}</p>
                <div class="flex justify-center space-x-3">
                    <button onclick="closeConfirmDialog(); ${onCancel ? onCancel + '()' : ''}"
                            class="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400 transition-colors">
                        Cancel
                    </button>
                    <button onclick="closeConfirmDialog(); ${onConfirm}()"
                            class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors">
                        Confirm
                    </button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    modal.id = 'confirmDialog';
}

// 关闭确认对话框
function closeConfirmDialog() {
    const modal = document.getElementById('confirmDialog');
    if (modal) {
        modal.remove();
    }
}

// 生成随机密码
function generateRandomPassword() {
    const length = 12;
    const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*";
    let password = "";

    // 确保至少包含一个大写字母、小写字母、数字和特殊字符
    const lowercase = "abcdefghijklmnopqrstuvwxyz";
    const uppercase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    const numbers = "0123456789";
    const special = "!@#$%^&*";

    password += lowercase[Math.floor(Math.random() * lowercase.length)];
    password += uppercase[Math.floor(Math.random() * uppercase.length)];
    password += numbers[Math.floor(Math.random() * numbers.length)];
    password += special[Math.floor(Math.random() * special.length)];

    // 填充剩余字符
    for (let i = 4; i < length; i++) {
        password += charset[Math.floor(Math.random() * charset.length)];
    }

    // 打乱密码字符顺序
    password = password.split('').sort(() => Math.random() - 0.5).join('');

    // 设置到密码输入框
    const passwordInput = document.getElementById('passwordInput');
    if (passwordInput) {
        passwordInput.value = password;
        passwordInput.type = 'text'; // 临时显示密码

        // 显示密码强度
        updatePasswordStrength(password);

        // 2秒后隐藏密码
        setTimeout(() => {
            passwordInput.type = 'password';
        }, 2000);

        showToast('Random password generated!', 'success');
    }
}

// 更新密码强度显示
function updatePasswordStrength(password) {
    const strengthElement = document.getElementById('passwordStrength');
    if (!strengthElement) return;

    let strength = 0;
    let feedback = [];

    if (password.length >= 8) strength++;
    else feedback.push('at least 8 characters');

    if (/[a-z]/.test(password)) strength++;
    else feedback.push('lowercase letter');

    if (/[A-Z]/.test(password)) strength++;
    else feedback.push('uppercase letter');

    if (/[0-9]/.test(password)) strength++;
    else feedback.push('number');

    if (/[^A-Za-z0-9]/.test(password)) strength++;
    else feedback.push('special character');

    const strengthLevels = ['Very Weak', 'Weak', 'Fair', 'Good', 'Strong'];
    const strengthColors = ['text-red-600', 'text-orange-600', 'text-yellow-600', 'text-blue-600', 'text-green-600'];

    strengthElement.className = `text-xs ${strengthColors[strength - 1] || 'text-gray-500'}`;

    if (strength >= 4) {
        strengthElement.textContent = `Password strength: ${strengthLevels[strength - 1]}`;
    } else {
        strengthElement.textContent = `Needs: ${feedback.join(', ')}`;
    }
}

// 批量操作
function selectAllUsers() {
    const checkboxes = document.querySelectorAll('input[name="selected_users[]"]');
    const selectAllCheckbox = document.getElementById('select-all');

    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
    });

    updateBulkActions();
}

function updateBulkActions() {
    const selectedCheckboxes = document.querySelectorAll('input[name="selected_users[]"]:checked');
    const bulkActionsDiv = document.getElementById('bulk-actions');

    if (selectedCheckboxes.length > 0) {
        bulkActionsDiv.classList.remove('hidden');
        document.getElementById('selected-count').textContent = selectedCheckboxes.length;
    } else {
        bulkActionsDiv.classList.add('hidden');
    }
}

// 批量激活用户
function bulkActivateUsers() {
    const selectedUsers = Array.from(document.querySelectorAll('input[name="selected_users[]"]:checked'))
                               .map(cb => cb.value);

    if (selectedUsers.length === 0) {
        showToast('No users selected', 'error');
        return;
    }

    if (!confirm(`Activate ${selectedUsers.length} selected users?`)) {
        return;
    }

    // 实现批量激活逻辑
    showToast(`Activating ${selectedUsers.length} users...`, 'success');
}

// 批量停用用户
function bulkDeactivateUsers() {
    const selectedUsers = Array.from(document.querySelectorAll('input[name="selected_users[]"]:checked'))
                               .map(cb => cb.value);

    if (selectedUsers.length === 0) {
        showToast('No users selected', 'error');
        return;
    }

    if (!confirm(`Deactivate ${selectedUsers.length} selected users?`)) {
        return;
    }

    // 实现批量停用逻辑
    showToast(`Deactivating ${selectedUsers.length} users...`, 'success');
}

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 绑定复选框事件
    const userCheckboxes = document.querySelectorAll('input[name="selected_users[]"]');
    userCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateBulkActions);
    });

    // 自动提交搜索表单（延迟搜索）
    const searchInput = document.querySelector('input[name="search"]');
    if (searchInput) {
        let searchTimeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                // 可以实现实时搜索
            }, 500);
        });
    }
});
</script>
