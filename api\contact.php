<?php
/**
 * Contact Form API Endpoint
 * 处理联系表单提交
 */

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// 使用统一的数据库连接
require_once dirname(__DIR__) . '/includes/database-connection.php';

try {
    // 获取POST数据
    $input = json_decode(file_get_contents('php://input'), true);
    
    // 如果不是JSON，尝试从$_POST获取
    if (!$input) {
        $input = $_POST;
    }
    
    // 验证必填字段
    $required_fields = ['name', 'email', 'subject', 'message', 'privacy'];
    $errors = [];
    
    foreach ($required_fields as $field) {
        if (empty($input[$field])) {
            $errors[] = "Field '$field' is required";
        }
    }
    
    // 验证邮箱格式
    if (!empty($input['email']) && !filter_var($input['email'], FILTER_VALIDATE_EMAIL)) {
        $errors[] = "Invalid email format";
    }
    
    // 验证隐私政策同意
    if (empty($input['privacy']) || $input['privacy'] !== 'on') {
        $errors[] = "You must agree to the privacy policy";
    }
    
    // 如果有错误，返回错误信息
    if (!empty($errors)) {
        http_response_code(400);
        echo json_encode([
            'success' => false, 
            'message' => 'Validation failed',
            'errors' => $errors
        ]);
        exit;
    }
    
    // 清理和准备数据
    $name = trim($input['name']);
    $email = trim($input['email']);
    $subject = trim($input['subject']);
    $message = trim($input['message']);
    $ip_address = $_SERVER['REMOTE_ADDR'] ?? null;
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? null;
    
    // 简单的垃圾邮件检测
    $spam_keywords = ['viagra', 'casino', 'lottery', 'winner', 'congratulations'];
    $message_lower = strtolower($message);
    foreach ($spam_keywords as $keyword) {
        if (strpos($message_lower, $keyword) !== false) {
            http_response_code(400);
            echo json_encode([
                'success' => false, 
                'message' => 'Message appears to be spam'
            ]);
            exit;
        }
    }
    
    // 检查是否在短时间内重复提交（防止垃圾邮件）
    $stmt = $pdo->prepare("
        SELECT COUNT(*) as count
        FROM pt_contact_message
        WHERE email = ? AND created_at > DATE_SUB(NOW(), INTERVAL 5 MINUTE)
    ");
    $stmt->execute([$email]);
    $recent_count = $stmt->fetch()['count'];
    
    if ($recent_count > 0) {
        http_response_code(429);
        echo json_encode([
            'success' => false, 
            'message' => 'Please wait a few minutes before sending another message'
        ]);
        exit;
    }
    
    // 插入到数据库
    $stmt = $pdo->prepare("
        INSERT INTO pt_contact_message (name, email, subject, message, ip_address, user_agent)
        VALUES (?, ?, ?, ?, ?, ?)
    ");
    
    $result = $stmt->execute([$name, $email, $subject, $message, $ip_address, $user_agent]);
    
    if ($result) {
        $message_id = $pdo->lastInsertId();
        
        // 可选：发送邮件通知管理员
        // sendAdminNotification($name, $email, $subject, $message);
        
        echo json_encode([
            'success' => true,
            'message' => 'Thank you for your message! We\'ll get back to you within 24 hours.',
            'message_id' => $message_id
        ]);
    } else {
        throw new Exception('Failed to save message');
    }
    
} catch (Exception $e) {
    error_log("Contact form error: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'An error occurred while processing your message. Please try again later.'
    ]);
}

/**
 * 发送管理员通知邮件（可选功能）
 */
function sendAdminNotification($name, $email, $subject, $message) {
    // 这里可以实现邮件发送功能
    // 例如使用PHPMailer或其他邮件库
    
    $admin_email = '<EMAIL>';
    $email_subject = "New Contact Form Message: $subject";
    $email_body = "
        New message received from contact form:
        
        Name: $name
        Email: $email
        Subject: $subject
        
        Message:
        $message
        
        ---
        Sent from Prompt2Tool Contact Form
    ";
    
    // mail($admin_email, $email_subject, $email_body);
}
?>
