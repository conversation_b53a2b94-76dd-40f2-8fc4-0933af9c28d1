<?php
/**
 * SEO Meta标签模板 - 简化版
 */

// 基础SEO数据
$title = 'Prompt2Tool - Free AI-Powered Online Tools Platform';
$description = 'Discover 100+ free online tools powered by AI. HTML formatter, CSS minifier, image converter, and more.';
$keywords = 'online tools, free tools, AI tools, developer tools, productivity tools';
?>

// 合并默认数据和页面特定数据
$seo = array_merge($defaultSEO, $seo ?? []);

// 生成结构化数据
$structuredData = generateStructuredData($currentPage, $seo);
?>

<!-- 基础Meta标签 -->
<title><?= htmlspecialchars($seo['title']) ?></title>
<meta name="description" content="<?= htmlspecialchars($seo['description']) ?>">
<meta name="robots" content="<?= $seo['robots'] ?>">
<meta name="author" content="<?= htmlspecialchars($seo['author']) ?>">
<meta name="generator" content="Prompt2Tool v1.0">
<link rel="canonical" href="<?= $seo['canonical'] ?>">

<!-- Open Graph标签 (Facebook, LinkedIn等) -->
<meta property="og:title" content="<?= htmlspecialchars($seo['og_title']) ?>">
<meta property="og:description" content="<?= htmlspecialchars($seo['og_description']) ?>">
<meta property="og:type" content="<?= $seo['og_type'] ?>">
<meta property="og:url" content="<?= $seo['canonical'] ?>">
<meta property="og:image" content="<?= getFullURL($seo['og_image']) ?>">
<meta property="og:image:width" content="1200">
<meta property="og:image:height" content="630">
<meta property="og:site_name" content="<?= htmlspecialchars($seo['site_name']) ?>">
<meta property="og:locale" content="en_US">

<!-- Twitter Cards -->
<meta name="twitter:card" content="<?= $seo['twitter_card'] ?>">
<meta name="twitter:title" content="<?= htmlspecialchars($seo['twitter_title'] ?? $seo['og_title']) ?>">
<meta name="twitter:description" content="<?= htmlspecialchars($seo['twitter_description'] ?? $seo['og_description']) ?>">
<meta name="twitter:image" content="<?= getFullURL($seo['twitter_image'] ?? $seo['og_image']) ?>">
<meta name="twitter:site" content="@Prompt2Tool">
<meta name="twitter:creator" content="@Prompt2Tool">

<!-- 移动端优化 -->
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0">
<meta name="theme-color" content="#4e73df">
<meta name="msapplication-TileColor" content="#4e73df">
<meta name="apple-mobile-web-app-capable" content="yes">
<meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
<meta name="apple-mobile-web-app-title" content="Prompt2Tool">

<!-- DNS预解析和预连接 -->
<link rel="dns-prefetch" href="//fonts.googleapis.com">
<link rel="dns-prefetch" href="//cdn.tailwindcss.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

<!-- 结构化数据 -->
<?php if (!empty($structuredData)): ?>
<script type="application/ld+json">
<?= json_encode($structuredData, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT) ?>
</script>
<?php endif; ?>

<!-- 其他Meta标签 -->
<meta name="format-detection" content="telephone=no">
<meta name="referrer" content="strict-origin-when-cross-origin">

<?php
/**
 * 获取当前页面完整URL
 */
function getCurrentURL() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
    $uri = $_SERVER['REQUEST_URI'] ?? '/';
    return $protocol . '://' . $host . $uri;
}

/**
 * 获取完整URL
 */
function getFullURL($path) {
    if (strpos($path, 'http') === 0) {
        return $path;
    }
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
    return $protocol . '://' . $host . $path;
}

/**
 * 获取页面SEO数据
 */
function getSEOData($page) {
    $seoData = [];
    
    switch ($page) {
        case 'home':
            $seoData = [
                'title' => 'Prompt2Tool - Free AI-Powered Online Tools Platform',
                'description' => 'Discover 100+ free online tools powered by AI. HTML formatter, CSS minifier, image converter, SEO analyzer, and more. Boost your productivity with Prompt2Tool.',
                'og_title' => 'Prompt2Tool - Free AI-Powered Online Tools',
                'og_description' => 'Access 100+ free online tools powered by AI. Perfect for developers, designers, and digital marketers.'
            ];
            break;
            
        case 'tools':
            $seoData = [
                'title' => 'All Tools - Prompt2Tool',
                'description' => 'Browse all free online tools available on Prompt2Tool. Find the perfect tool for your development, design, and productivity needs.',
                'og_title' => 'All Tools - Prompt2Tool',
                'og_description' => 'Browse 100+ free online tools for developers, designers, and digital marketers.'
            ];
            break;
            
        case 'about':
            $seoData = [
                'title' => 'About Us - Prompt2Tool',
                'description' => 'Learn about Prompt2Tool, our mission to provide free AI-powered online tools for developers, designers, and digital professionals.',
                'og_title' => 'About Prompt2Tool',
                'og_description' => 'Learn about our mission to provide free AI-powered online tools for everyone.'
            ];
            break;
            
        case 'contact':
            $seoData = [
                'title' => 'Contact Us - Prompt2Tool',
                'description' => 'Get in touch with the Prompt2Tool team. We\'d love to hear your feedback and suggestions for new tools.',
                'og_title' => 'Contact Prompt2Tool',
                'og_description' => 'Get in touch with our team. We\'d love to hear your feedback and suggestions.'
            ];
            break;
    }
    
    return $seoData;
}

/**
 * 生成结构化数据
 */
function generateStructuredData($page, $seo) {
    $baseData = [
        '@context' => 'https://schema.org',
        '@type' => 'WebSite',
        'name' => 'Prompt2Tool',
        'description' => 'Free AI-powered online tools platform',
        'url' => 'https://' . ($_SERVER['HTTP_HOST'] ?? 'localhost'),
        'potentialAction' => [
            '@type' => 'SearchAction',
            'target' => [
                '@type' => 'EntryPoint',
                'urlTemplate' => 'https://' . ($_SERVER['HTTP_HOST'] ?? 'localhost') . '/search?q={search_term_string}'
            ],
            'query-input' => 'required name=search_term_string'
        ],
        'publisher' => [
            '@type' => 'Organization',
            'name' => 'Prompt2Tool',
            'url' => 'https://' . ($_SERVER['HTTP_HOST'] ?? 'localhost'),
            'logo' => [
                '@type' => 'ImageObject',
                'url' => 'https://' . ($_SERVER['HTTP_HOST'] ?? 'localhost') . '/assets/images/logo.png'
            ]
        ]
    ];
    
    // 根据页面类型添加特定的结构化数据
    switch ($page) {
        case 'home':
            $baseData['@type'] = 'WebSite';
            $baseData['mainEntity'] = [
                '@type' => 'SoftwareApplication',
                'name' => 'Prompt2Tool',
                'applicationCategory' => 'DeveloperApplication',
                'operatingSystem' => 'Web Browser',
                'offers' => [
                    '@type' => 'Offer',
                    'price' => '0',
                    'priceCurrency' => 'USD'
                ]
            ];
            break;
            
        case 'tools':
            $baseData['@type'] = 'CollectionPage';
            $baseData['mainEntity'] = [
                '@type' => 'ItemList',
                'name' => 'Online Tools Collection',
                'description' => 'Collection of free online tools for developers and designers'
            ];
            break;
    }
    
    return $baseData;
}
?>
