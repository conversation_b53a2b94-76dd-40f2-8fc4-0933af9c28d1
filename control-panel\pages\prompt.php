<?php
/**
 * Prompt Generator Page
 * 对话式prompt生成器，用于生成工具开发需求
 */

// 检查登录状态
if (!isset($_SESSION['admin_logged_in']) || !$_SESSION['admin_logged_in']) {
    header('Location: /control-panel/login.php');
    exit;
}

// 获取aiHubMix平台配置
try {
    // 获取aiHubMix平台信息
    $stmt = $pdo->prepare("SELECT id FROM pt_service_platform WHERE code = 'aihubmix' AND is_active = 1");
    $stmt->execute();
    $aihubmixPlatform = $stmt->fetch();
    
    if (!$aihubmixPlatform) {
        throw new Exception('AiHubMix platform not found or inactive');
    }
    
    $platformId = $aihubmixPlatform['id'];
    
    // 获取gpt-4o-mini模型信息
    $stmt = $pdo->prepare("
        SELECT model_code, model_name, max_tokens 
        FROM pt_service_model 
        WHERE platform_id = ? AND model_code = 'gpt-4o-mini' AND is_active = 1
    ");
    $stmt->execute([$platformId]);
    $gptModel = $stmt->fetch();
    
    if (!$gptModel) {
        throw new Exception('GPT-4o-mini model not found or inactive');
    }
    
    // 获取API密钥
    $stmt = $pdo->prepare("
        SELECT api_key 
        FROM pt_service_key 
        WHERE platform_id = ? AND is_active = 1 
        ORDER BY id ASC 
        LIMIT 1
    ");
    $stmt->execute([$platformId]);
    $apiKeyData = $stmt->fetch();
    
    if (!$apiKeyData) {
        throw new Exception('No active API key found for AiHubMix');
    }
    
    // 获取APP-Code配置
    $stmt = $pdo->prepare("
        SELECT config_value 
        FROM pt_service_config 
        WHERE platform_id = ? AND config_key = 'app_code'
    ");
    $stmt->execute([$platformId]);
    $appCodeData = $stmt->fetch();
    
    $appCode = $appCodeData ? $appCodeData['config_value'] : 'KXTM3281';

    // 获取工具分类
    $stmt = $pdo->prepare("SELECT name FROM pt_tool_category WHERE status = 'active' ORDER BY sort_order ASC, name ASC");
    $stmt->execute();
    $toolCategories = $stmt->fetchAll(PDO::FETCH_COLUMN);

} catch (Exception $e) {
    $error_message = "Configuration error: " . $e->getMessage();
}
?>

<!-- Prompt Generator Content - Full Height -->
<div class="h-full flex flex-col p-6">

    <?php if (isset($error_message)): ?>
    <div class="mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded flex-shrink-0">
        <?= htmlspecialchars($error_message) ?>
    </div>
    <?php else: ?>

    <!-- Chat Interface - Full Height -->
    <div class="flex-1 bg-white shadow rounded-lg overflow-hidden flex flex-col min-h-0">
        <!-- Chat Header -->
        <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50">
            <div class="flex justify-between items-center">
                <div>
                    <h2 class="text-xl font-semibold text-gray-900 flex items-center">
                        <svg class="w-6 h-6 mr-3 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                        </svg>
                        Tool Prompt Generator
                    </h2>
                    <p class="text-sm text-gray-600 mt-1">Create detailed prompts for tool development through conversation</p>
                </div>
                <div class="flex items-center justify-end">
                    <!-- Reset Button -->
                    <button id="resetChat" class="px-4 py-2 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors">
                        <i class="fas fa-redo mr-2"></i>Reset
                    </button>
                </div>
            </div>
        </div>

        <!-- Chat Messages Area - Flexible Height -->
        <div class="flex-1 chat-container bg-gray-50 overflow-hidden">
            <div id="chatMessages" class="h-full chat-messages p-6 space-y-6 overflow-y-auto">
                <!-- Initial AI Message -->
                <div class="message ai-message">
                    <div class="flex items-start space-x-4">
                        <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center shadow-md">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                            </svg>
                        </div>
                        <div class="flex-1">
                            <div class="bg-white rounded-xl p-5 shadow-sm border border-gray-200">
                                <p class="text-gray-800 leading-relaxed">Hello! I'm your Product Manager specializing in web tools. I'll help you define exactly what your online tool should do through a focused conversation.</p>
                                <p class="text-gray-800 mt-3 leading-relaxed">Let's start with the basics:</p>
                                <p class="text-gray-800 mt-3 leading-relaxed font-medium">What specific problem or task do you want your online tool to solve?</p>
                            </div>
                            <div class="mt-3 flex flex-wrap gap-2">
                                <?php if (!empty($toolCategories)): ?>
                                    <?php foreach ($toolCategories as $category): ?>
                                        <button class="suggestion-btn" data-response="<?= strtolower(htmlspecialchars($category)) ?>">
                                            <?= htmlspecialchars($category) ?>
                                        </button>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                                <button class="suggestion-btn" data-response="other">Other</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Loading Indicator - Inside Chat Area -->
                <div id="loadingIndicator" class="hidden message ai-message">
                    <div class="flex items-start space-x-4">
                        <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center shadow-md">
                            <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                        </div>
                        <div class="flex-1">
                            <div class="bg-white rounded-xl p-5 shadow-sm border border-gray-200">
                                <p class="text-gray-600 flex items-center">
                                    <span class="text-sm font-medium">AI is thinking...</span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Chat Input Area - Fixed at Bottom -->
        <div class="flex-shrink-0 border-t border-gray-200 bg-white p-6">
                <!-- Suggested Responses (Dynamic) -->
                <div id="suggestedResponses" class="mb-4 hidden">
                    <div class="flex flex-wrap gap-2"></div>
                </div>

                <!-- Input Group -->
                <div class="flex space-x-4">
                    <div class="flex-1">
                        <input type="text"
                               id="userInput"
                               placeholder="Type your message or click a suggestion above..."
                               class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 text-gray-700">
                    </div>
                    <button id="sendBtn"
                            class="px-6 py-3 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-xl hover:from-blue-700 hover:to-blue-800 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed shadow-md">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <?php endif; ?>
</div>

<!-- Configuration for JavaScript -->
<script>
// Configuration from PHP
window.promptGeneratorConfig = {
    apiKey: <?= json_encode($apiKeyData['api_key'] ?? '') ?>,
    appCode: <?= json_encode($appCode) ?>,
    model: <?= json_encode($gptModel['model_code'] ?? '') ?>,
    maxTokens: <?= json_encode($gptModel['max_tokens'] ?? 1000) ?>,
    currentTime: <?= json_encode(date('c')) ?>,
    toolCategories: <?= json_encode($toolCategories ?? []) ?>
};

// Conversation state management
const conversationState = {
    currentStage: 'initial',
    collectedData: {
        problemStatement: '',
        toolCategory: '',
        toolName: '',
        primaryFunction: '',
        inputRequirements: '',
        outputSpecifications: '',
        keyFeatures: [],
        userWorkflow: '',
        processingLogic: ''
    },
    conversationHistory: [],
    isProcessing: false
};




// System prompts for different stages
const systemPrompts = {
    // Initial stage - understanding the problem
    initial: `You are an experienced Product Manager specializing in simple web tools.

Your goal: Understand what specific problem the user wants to solve with their online tool.

Guidelines:
- Ask ONE focused question at a time
- Be conversational and professional
- Use **bold** for key questions
- Focus on simple, single-purpose online tools
- Keep responses concise (2-3 sentences max)
- NEVER ask about visual design, colors, styles, or appearance
- Focus only on functionality and user needs

Current date: ${window.promptGeneratorConfig.currentTime}`,

    // Category selection stage
    tool_type: `You are a PM helping users categorize their tool idea.

Task: Based on their specific problem description, intelligently suggest 2-3 most relevant categories.

Available categories:
${window.promptGeneratorConfig.toolCategories ? window.promptGeneratorConfig.toolCategories.map(cat => `- ${cat}`).join('\n') : ''}
- Other

Guidelines:
- Analyze their problem/need carefully
- Suggest only the most relevant 2-3 categories (without "Tools" suffix)
- Explain briefly why each category fits
- Ask them to choose the best fit
- Be personalized based on their specific description`,

    // Functionality definition stage
    functionality: `You are a PM gathering detailed functionality requirements.

Task: Ask ONE specific question at a time about:
1. Input (what users provide)
2. Process (what the tool does)
3. Output (what users get)

Guidelines:
- Ask only ONE question per response
- Build on their previous answers
- Keep it conversational
- Focus on the most important aspect first
- DO NOT ask about design style, colors, or visual preferences
- Ignore any user mentions of style/design - focus only on functionality`,

    // Tool naming stage
    naming: `You are a PM suggesting professional tool names.

Task: Based on the specific functionality discussed, suggest 2-3 SEO-friendly English names.

Format: Present the names clearly and ask the user to choose their preferred option.

Guidelines:
- Names: 2-3 words, hyphen-separated (e.g., "unit-converter", "data-calculator")
- DO NOT include words like "tool", "app", "utility" in the names
- Names should clearly describe the core function
- Make them SEO-friendly and memorable
- Be specific to their use case (not generic)
- Present options and ask for their preference`,

    // Final specification generation
    generate: `You are a PM creating a comprehensive development specification.

Task: Generate a structured development prompt using the provided data.

CRITICAL: Use this EXACT markdown format with proper spacing and structure:

# Tool Development Specification

## Tool Overview
- **Name**: [tool name]
- **Purpose**: [clear problem statement]
- **Category**: [exact category provided]

## Core Functionality
- **Primary Function**: [main action]
- **Input Requirements**: [user inputs]
- **Output Specifications**: [expected results]
- **Key Features**: [essential features list]

## User Experience Requirements
- **User Workflow**: [step-by-step process]
- **Interface Elements**: [UI components needed]
- **Validation Rules**: [input validation]
- **Edge Cases**: [error scenarios]

## Technical Specifications
- **Processing Logic**: [conversion logic/formulas]
- **Data Handling**: [data requirements]
- **Performance Expectations**: [speed/accuracy]

## Acceptance Criteria
- [ ] Tool performs primary function correctly
- [ ] All input types are properly handled
- [ ] Output format meets specifications
- [ ] User workflow is intuitive and complete

IMPORTANT:
- Use proper markdown formatting with ## headers
- Include line breaks between sections
- Use bullet points with - for lists
- Keep content concise and developer-focused`
};

// DOM elements
let chatMessages, userInput, sendBtn, loadingIndicator, suggestedResponses, resetChat;

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeElements();
    bindEvents();
    enableInput();
    // Focus on input after page load
    if (userInput) {
        userInput.focus();
    }
});

function initializeElements() {
    chatMessages = document.getElementById('chatMessages');
    userInput = document.getElementById('userInput');
    sendBtn = document.getElementById('sendBtn');
    loadingIndicator = document.getElementById('loadingIndicator');
    suggestedResponses = document.getElementById('suggestedResponses');
    resetChat = document.getElementById('resetChat');
}

function bindEvents() {
    // Send button click
    sendBtn.addEventListener('click', handleSendMessage);

    // Enter key in input
    userInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            handleSendMessage();
        }
    });

    // Suggestion button clicks
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('suggestion-btn')) {
            const response = e.target.getAttribute('data-response');
            userInput.value = response;
            handleSendMessage();
        }
    });

    // Reset chat
    resetChat.addEventListener('click', resetConversation);

    // Note: Prompt result buttons are dynamically generated and bound in addPromptActionButtons()
}

function enableInput() {
    userInput.disabled = false;
    sendBtn.disabled = false;
}

function disableInput() {
    userInput.disabled = true;
    sendBtn.disabled = true;
}

async function handleSendMessage() {
    const message = userInput.value.trim();
    if (!message || conversationState.isProcessing) return;

    // Prevent duplicate processing
    if (conversationState.isProcessing) {
        return;
    }

    // Add user message to chat
    addUserMessage(message);

    // Clear input and disable
    userInput.value = '';
    disableInput();
    showLoading();

    // Add to conversation history
    conversationState.conversationHistory.push({
        role: 'user',
        content: message
    });

    try {
        // Generate AI response
        const aiResponse = await generateAIResponse(message);

        // Update conversation state
        updateConversationState(message, aiResponse);

    } catch (error) {
        console.error('Error generating AI response:', error);
        addAIMessage('Sorry, I encountered an error. Please try again.', []);
    } finally {
        hideLoading();
        enableInput();
        userInput.focus();
    }
}

async function generateAIResponse(userMessage) {
    conversationState.isProcessing = true;

    try {
        // Use stage-specific system prompt
        const systemPrompt = systemPrompts[conversationState.currentStage] || systemPrompts.initial;

        const response = await fetch('../api/chat-completion.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                model: window.promptGeneratorConfig.model,
                messages: [
                    {
                        role: 'system',
                        content: systemPrompt + '\n\nIMPORTANT: \n- Ask only ONE focused question at a time\n- Use **bold** for key questions and terms\n- Keep responses concise (2-3 sentences max)\n- Use proper Markdown formatting\n- Don\'t overwhelm users with multiple questions\n- Focus on the most important next step\n- NEVER ask about visual design, colors, styles, or appearance\n- Ignore any user mentions of design preferences - focus only on functionality'
                    },
                    ...conversationState.conversationHistory,
                    {
                        role: 'user',
                        content: userMessage
                    }
                ],
                stream: true,
                temperature: 0.7,
                max_tokens: 1000,
                app_code: window.promptGeneratorConfig.appCode
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        // Handle streaming response
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let content = '';

        // Create AI message element for streaming
        const messageDiv = createAIMessageElement();
        const contentElement = messageDiv.querySelector('.message-content p');

        try {
            while (true) {
                const { done, value } = await reader.read();

                if (done) break;

                const chunk = decoder.decode(value);
                const lines = chunk.split('\n');

                for (const line of lines) {
                    if (line.startsWith('data: ')) {
                        const data = line.slice(6);

                        if (data === '[DONE]') {
                            break;
                        }

                        try {
                            const parsed = JSON.parse(data);
                            if (parsed.choices && parsed.choices[0] && parsed.choices[0].delta && parsed.choices[0].delta.content) {
                                const deltaContent = parsed.choices[0].delta.content;
                                content += deltaContent;

                                // Parse markdown and display
                                contentElement.innerHTML = parseMarkdown(content);
                                // Auto scroll to bottom during streaming
                                setTimeout(scrollToBottom, 10);
                            }
                        } catch (e) {
                            // Skip invalid JSON
                        }
                    }
                }
            }
        } finally {
            reader.releaseLock();
        }

        // Add to conversation history
        conversationState.conversationHistory.push({
            role: 'assistant',
            content: content
        });

        // Generate suggestions based on current stage
        const suggestions = generateSuggestions(conversationState.currentStage, userMessage);

        // Add suggestions to the message
        if (suggestions.length > 0) {
            addSuggestionsToMessage(messageDiv, suggestions);
        }

        return {
            content: content,
            suggestions: suggestions
        };

    } finally {
        conversationState.isProcessing = false;
    }
}

function generateSuggestions(stage, userMessage) {
    const suggestions = [];

    switch (stage) {
        case 'tool_type':
            // After user selects tool type, suggest moving to functionality
            suggestions.push('Tell me more about the functionality');
            break;

        case 'functionality':
            // After functionality discussion, suggest generating prompt
            suggestions.push('Generate the prompt now');
            suggestions.push('Add more features');
            break;

        case 'generate':
            // Final stage suggestions
            suggestions.push('Regenerate prompt');
            suggestions.push('Start over');
            break;
    }

    return suggestions;
}

function updateConversationState(userMessage, aiResponse) {
    // Stage progression logic with data collection
    switch (conversationState.currentStage) {
        case 'initial':
            // Collect problem statement and move to tool_type
            if (userMessage.trim().length > 10) {
                conversationState.collectedData.problemStatement = userMessage;
                conversationState.currentStage = 'tool_type';
            }
            break;

        case 'tool_type':
            // Extract and store tool category (more flexible matching)
            const toolTypes = ['text', 'image', 'code', 'utilities', 'data', 'development', 'design', 'productivity', 'marketing', 'security', 'games', 'converters', 'calculators', 'pdf', 'network', 'analytics', 'finance', 'ai', 'qr', 'color', 'hash', 'json', 'css', 'html', 'api', 'database', 'regex', 'time', 'weather', 'maps', 'social', 'email'];
            const message = userMessage.toLowerCase();

            for (const type of toolTypes) {
                if (message.includes(type) || message.includes(type + 's')) {
                    conversationState.collectedData.toolCategory = type.charAt(0).toUpperCase() + type.slice(1);
                    conversationState.currentStage = 'functionality';
                    break;
                }
            }

            // Also check for "other"
            if (message.includes('other') || message.includes('其他')) {
                conversationState.collectedData.toolCategory = 'Other';
                conversationState.currentStage = 'functionality';
            }
            break;

        case 'functionality':
            // Check if user wants to generate prompt directly
            if (userMessage.toLowerCase().includes('generate the prompt now') ||
                userMessage.toLowerCase().includes('generate prompt') ||
                userMessage.toLowerCase().includes('生成prompt')) {
                // Set a default tool name if not provided
                if (!conversationState.collectedData.toolName) {
                    conversationState.collectedData.toolName = 'unit-converter';
                }
                conversationState.currentStage = 'generate';
                generateFinalPrompt();
                return;
            }

            // Collect functionality details progressively
            collectFunctionalityData(userMessage, aiResponse);

            // Check if ready to move to naming
            if (userMessage.toLowerCase().includes('没有') ||
                userMessage.toLowerCase().includes('no') ||
                userMessage.toLowerCase().includes('完成') ||
                aiResponse.content.toLowerCase().includes('工具名') ||
                aiResponse.content.toLowerCase().includes('name')) {
                conversationState.currentStage = 'naming';
            }
            break;

        case 'naming':
            // Check if user wants to generate prompt directly
            if (userMessage.toLowerCase().includes('generate the prompt now') ||
                userMessage.toLowerCase().includes('generate prompt') ||
                userMessage.toLowerCase().includes('生成prompt')) {
                // Set a default tool name if not provided
                if (!conversationState.collectedData.toolName) {
                    conversationState.collectedData.toolName = 'unit-converter';
                }
                conversationState.currentStage = 'generate';
                generateFinalPrompt();
                return;
            }

            // Collect tool name and move to generate
            if (userMessage.trim().length > 0) {
                // Extract tool name from user message
                conversationState.collectedData.toolName = userMessage.trim();

                // Ensure we have basic data before generating
                if (!conversationState.collectedData.primaryFunction) {
                    conversationState.collectedData.primaryFunction = conversationState.collectedData.problemStatement;
                }

                conversationState.currentStage = 'generate';
                generateFinalPrompt();
            }
            break;
    }
}

function collectFunctionalityData(userMessage, aiResponse) {
    // Extract key information from user responses
    const message = userMessage.toLowerCase();

    // Detect input requirements
    if (message.includes('输入') || message.includes('input') || message.includes('提供') || message.includes('enter') ||
        message.includes('numeric') || message.includes('dropdown') || message.includes('select')) {
        conversationState.collectedData.inputRequirements += (conversationState.collectedData.inputRequirements ? ' ' : '') + userMessage;
    }

    // Detect output specifications
    if (message.includes('输出') || message.includes('output') || message.includes('结果') || message.includes('result') ||
        message.includes('display') || message.includes('show') || message.includes('format')) {
        conversationState.collectedData.outputSpecifications += (conversationState.collectedData.outputSpecifications ? ' ' : '') + userMessage;
    }

    // Detect primary function
    if (message.includes('功能') || message.includes('function') || message.includes('作用') || message.includes('用途') ||
        message.includes('conversion') || message.includes('convert') || message.includes('换算')) {
        if (!conversationState.collectedData.primaryFunction) {
            conversationState.collectedData.primaryFunction = userMessage;
        }
    }

    // Detect processing logic
    if (message.includes('logic') || message.includes('calculation') || message.includes('formula') ||
        message.includes('rate') || message.includes('算法') || message.includes('公式')) {
        conversationState.collectedData.processingLogic += (conversationState.collectedData.processingLogic ? ' ' : '') + userMessage;
    }

    // Detect user workflow
    if (message.includes('workflow') || message.includes('process') || message.includes('step') ||
        message.includes('流程') || message.includes('步骤')) {
        conversationState.collectedData.userWorkflow += (conversationState.collectedData.userWorkflow ? ' ' : '') + userMessage;
    }

    // Collect features mentioned
    if (message.includes('feature') || message.includes('功能') || message.includes('特性')) {
        conversationState.collectedData.keyFeatures.push(userMessage);
    }
}



async function generateFinalPrompt() {
    try {
        showLoading();

        // Use structured collected data instead of conversation history
        const collectedData = conversationState.collectedData;

        // Create structured prompt data
        const promptData = {
            problemStatement: collectedData.problemStatement,
            toolCategory: collectedData.toolCategory,
            toolName: collectedData.toolName,
            primaryFunction: collectedData.primaryFunction,
            inputRequirements: collectedData.inputRequirements,
            outputSpecifications: collectedData.outputSpecifications,
            keyFeatures: collectedData.keyFeatures,
            userWorkflow: collectedData.userWorkflow,
            processingLogic: collectedData.processingLogic
        };

        const systemPrompt = systemPrompts.generate;

        const response = await fetch('../api/chat-completion.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                model: window.promptGeneratorConfig.model,
                messages: [
                    {
                        role: 'system',
                        content: systemPrompt
                    },
                    {
                        role: 'user',
                        content: `Generate a development specification using this collected data:

**Tool Name**: ${promptData.toolName || 'unit-converter'}
**Problem Statement**: ${promptData.problemStatement || 'Unit conversion tool for various measurements'}
**Tool Category**: ${promptData.toolCategory || 'Converters Tools'}
**Primary Function**: ${promptData.primaryFunction || 'Convert between different units of measurement'}
**Input Requirements**: ${promptData.inputRequirements || 'Numeric values and unit selection'}
**Output Specifications**: ${promptData.outputSpecifications || 'Converted values with clear formatting'}
**Key Features**: ${promptData.keyFeatures.length > 0 ? promptData.keyFeatures.join(', ') : 'Unit conversion, dropdown selection, clear output display'}
**User Workflow**: ${promptData.userWorkflow || 'Enter value, select units, view conversion result'}
**Processing Logic**: ${promptData.processingLogic || 'Apply conversion formulas and rates between units'}

Follow the exact markdown format specified in your system prompt. Ensure proper spacing and structure.`
                    }
                ],
                stream: true,
                temperature: 0.2,
                max_tokens: 2000,
                app_code: window.promptGeneratorConfig.appCode
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        // Handle streaming response for final prompt
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let promptContent = '';

        // Create AI message for the prompt
        const promptMessageDiv = createAIMessageElement();
        const contentElement = promptMessageDiv.querySelector('.message-content p');
        contentElement.innerHTML = '<div class="text-gray-600">Generating development specification...</div>';

        try {
            while (true) {
                const { done, value } = await reader.read();

                if (done) break;

                const chunk = decoder.decode(value);
                const lines = chunk.split('\n');

                for (const line of lines) {
                    if (line.startsWith('data: ')) {
                        const data = line.slice(6);

                        if (data === '[DONE]') {
                            break;
                        }

                        try {
                            const parsed = JSON.parse(data);
                            if (parsed.choices && parsed.choices[0] && parsed.choices[0].delta && parsed.choices[0].delta.content) {
                                const deltaContent = parsed.choices[0].delta.content;
                                promptContent += deltaContent;

                                // Update the message content with parsed markdown
                                contentElement.innerHTML = parseMarkdown(promptContent);
                                scrollToBottom();
                            }
                        } catch (e) {
                            // Skip invalid JSON
                        }
                    }
                }
            }
        } finally {
            reader.releaseLock();
        }

        // Add action buttons after prompt is generated
        if (promptContent.trim()) {
            addPromptActionButtons(promptMessageDiv, promptContent);
        }

    } catch (error) {
        console.error('Error generating final prompt:', error);
        addAIMessage('Sorry, I encountered an error generating the prompt. Please try again.', ['Regenerate prompt']);
    } finally {
        hideLoading();
    }
}

function addPromptActionButtons(messageDiv, promptContent) {
    const buttonsHtml = `
        <div class="mt-4 flex flex-wrap gap-3 pt-4 border-t border-gray-200">
            <button class="copy-prompt-btn px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                </svg>
                Copy
            </button>
            <button class="download-prompt-btn px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors flex items-center">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                Download
            </button>
            <button class="regenerate-prompt-btn px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors flex items-center">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                Regenerate
            </button>
            <button class="start-over-btn px-4 py-2 bg-red-100 text-red-700 rounded-md hover:bg-red-200 transition-colors flex items-center">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6"></path>
                </svg>
                Start Over
            </button>
        </div>
    `;

    const contentContainer = messageDiv.querySelector('.flex-1');
    contentContainer.insertAdjacentHTML('beforeend', buttonsHtml);

    // Add event listeners
    messageDiv.querySelector('.copy-prompt-btn').addEventListener('click', (e) => copyToClipboard(promptContent, e));
    messageDiv.querySelector('.download-prompt-btn').addEventListener('click', () => downloadPrompt(promptContent));
    messageDiv.querySelector('.regenerate-prompt-btn').addEventListener('click', () => generateFinalPrompt());
    messageDiv.querySelector('.start-over-btn').addEventListener('click', () => resetConversation());
}

function copyToClipboard(text, event) {
    // Store the button reference
    window.currentCopyButton = event.target.closest('.copy-prompt-btn');

    // Try modern clipboard API first
    if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard.writeText(text).then(() => {
            showCopySuccess();
        }).catch(err => {
            console.error('Clipboard API failed: ', err);
            fallbackCopyTextToClipboard(text);
        });
    } else {
        // Fallback for older browsers or non-secure contexts
        fallbackCopyTextToClipboard(text);
    }
}

function fallbackCopyTextToClipboard(text) {
    const textArea = document.createElement("textarea");
    textArea.value = text;

    // Avoid scrolling to bottom
    textArea.style.top = "0";
    textArea.style.left = "0";
    textArea.style.position = "fixed";
    textArea.style.opacity = "0";

    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
        const successful = document.execCommand('copy');
        if (successful) {
            showCopySuccess();
        } else {
            showCopyError();
        }
    } catch (err) {
        console.error('Fallback copy failed: ', err);
        showCopyError();
    }

    document.body.removeChild(textArea);
}

function showCopySuccess() {
    const btn = window.currentCopyButton;
    if (!btn) return;

    const originalText = btn.innerHTML;
    btn.innerHTML = '<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>Copied!';
    btn.classList.add('bg-green-600', 'hover:bg-green-700');
    btn.classList.remove('bg-blue-600', 'hover:bg-blue-700');

    setTimeout(() => {
        btn.innerHTML = originalText;
        btn.classList.remove('bg-green-600', 'hover:bg-green-700');
        btn.classList.add('bg-blue-600', 'hover:bg-blue-700');
    }, 2000);
}

function showCopyError() {
    const btn = window.currentCopyButton;
    if (!btn) return;

    const originalText = btn.innerHTML;
    btn.innerHTML = '<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg>Failed';
    btn.classList.add('bg-red-600', 'hover:bg-red-700');
    btn.classList.remove('bg-blue-600', 'hover:bg-blue-700');

    setTimeout(() => {
        btn.innerHTML = originalText;
        btn.classList.remove('bg-red-600', 'hover:bg-red-700');
        btn.classList.add('bg-blue-600', 'hover:bg-blue-700');
    }, 2000);

    // Also show alert as fallback
    alert('Failed to copy to clipboard. Please select and copy the text manually.');
}

function downloadPrompt(content) {
    const blob = new Blob([content], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;

    // Generate clean filename
    const toolName = conversationState.collectedData.toolName || 'tool';
    const cleanName = toolName.replace(/[^a-zA-Z0-9-]/g, '').toLowerCase();
    a.download = `${cleanName}.md`;

    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
}



function addUserMessage(message) {
    const messageDiv = document.createElement('div');
    messageDiv.className = 'message user-message';
    messageDiv.innerHTML = `
        <div class="flex items-start space-x-4 justify-end">
            <div class="flex-1 flex justify-end">
                <div class="message-content bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-xl p-4 max-w-md shadow-md">
                    <p class="leading-relaxed">${escapeHtml(message)}</p>
                </div>
            </div>
            <div class="w-10 h-10 bg-gradient-to-br from-gray-600 to-gray-700 rounded-full flex items-center justify-center shadow-md">
                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                </svg>
            </div>
        </div>
    `;

    chatMessages.appendChild(messageDiv);
    scrollToBottom();
}

function createAIMessageElement() {
    const messageDiv = document.createElement('div');
    messageDiv.className = 'message ai-message';

    messageDiv.innerHTML = `
        <div class="flex items-start space-x-4">
            <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center shadow-md">
                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                </svg>
            </div>
            <div class="flex-1">
                <div class="message-content bg-white rounded-xl p-5 shadow-sm border border-gray-200">
                    <p class="text-gray-800 leading-relaxed"></p>
                </div>
            </div>
        </div>
    `;

    chatMessages.appendChild(messageDiv);
    scrollToBottom();
    return messageDiv;
}

function addSuggestionsToMessage(messageDiv, suggestions) {
    if (suggestions.length === 0) return;

    const suggestionsHtml = `
        <div class="mt-4 flex flex-wrap gap-2">
            ${suggestions.map(suggestion =>
                `<button class="suggestion-btn" data-response="${escapeHtml(suggestion)}">${escapeHtml(suggestion)}</button>`
            ).join('')}
        </div>
    `;

    const contentContainer = messageDiv.querySelector('.flex-1');
    contentContainer.insertAdjacentHTML('beforeend', suggestionsHtml);
}

function addAIMessage(message, suggestions = []) {
    const messageDiv = createAIMessageElement();
    const contentElement = messageDiv.querySelector('.message-content p');

    // Parse markdown and display
    contentElement.innerHTML = parseMarkdown(message);

    // Add suggestions if provided
    if (suggestions.length > 0) {
        addSuggestionsToMessage(messageDiv, suggestions);
    }

    scrollToBottom();
}



function showLoading() {
    loadingIndicator.classList.remove('hidden');
    scrollToBottom();
}

function hideLoading() {
    loadingIndicator.classList.add('hidden');
}

function scrollToBottom() {
    if (chatMessages) {
        // Use setTimeout to ensure DOM is updated before scrolling
        setTimeout(() => {
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }, 100);
    }
}

function resetConversation() {
    // Reset state
    conversationState.currentStage = 'initial';
    conversationState.collectedData = {
        problemStatement: '',
        toolCategory: '',
        toolName: '',
        primaryFunction: '',
        inputRequirements: '',
        outputSpecifications: '',
        keyFeatures: [],
        userWorkflow: '',
        processingLogic: ''
    };
    conversationState.conversationHistory = [];

    // Clear chat messages except initial message
    const initialMessage = chatMessages.querySelector('.ai-message');
    chatMessages.innerHTML = '';
    chatMessages.appendChild(initialMessage);

    // Hide prompt result
    promptResult.classList.add('hidden');

    // Enable input
    enableInput();
    userInput.focus();
}

function copyPromptToClipboard() {
    const text = generatedPrompt.textContent;
    navigator.clipboard.writeText(text).then(() => {
        // Show success feedback using a simple alert or console log
        console.log('Prompt copied to clipboard successfully');
        // You could also show a toast notification here
        showNotification('Prompt copied to clipboard!', 'success');
    }).catch(err => {
        console.error('Failed to copy text: ', err);
        showNotification('Failed to copy prompt', 'error');
    });
}

// Note: regeneratePromptContent and editPromptContent functions removed
// as they were not being used. The dynamic buttons in addPromptActionButtons()
// handle these actions directly.

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 px-6 py-3 rounded-lg shadow-lg z-50 transition-all duration-300 ${
        type === 'success' ? 'bg-green-500 text-white' :
        type === 'error' ? 'bg-red-500 text-white' :
        'bg-blue-500 text-white'
    }`;
    notification.textContent = message;

    // Add to page
    document.body.appendChild(notification);

    // Remove after 3 seconds
    setTimeout(() => {
        notification.style.opacity = '0';
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

function parseMarkdown(text) {
    if (!text) return '';

    let html = text
        // Headers
        .replace(/^### (.*$)/gm, '<h3 class="text-lg font-semibold text-gray-900 mt-3 mb-2">$1</h3>')
        .replace(/^## (.*$)/gm, '<h2 class="text-xl font-semibold text-gray-900 mt-3 mb-2">$1</h2>')
        .replace(/^# (.*$)/gm, '<h1 class="text-2xl font-bold text-gray-900 mt-3 mb-2">$1</h1>')

        // Bold text
        .replace(/\*\*(.*?)\*\*/g, '<strong class="font-semibold text-gray-900">$1</strong>')

        // Italic text
        .replace(/\*(.*?)\*/g, '<em class="italic">$1</em>')

        // Numbered lists
        .replace(/^(\d+\.\s+)(.*)$/gm, '<div class="mb-1"><span class="font-medium text-blue-600">$1</span>$2</div>')

        // Bullet points
        .replace(/^[-*]\s+(.*)$/gm, '<div class="mb-1 ml-3 flex"><span class="text-blue-600 mr-2">•</span><span>$1</span></div>')

        // Code blocks (inline)
        .replace(/`([^`]+)`/g, '<code class="bg-gray-100 px-1 py-0.5 rounded text-sm font-mono">$1</code>')

        // Handle paragraphs - split by double newlines
        .split(/\n\s*\n/)
        .map(paragraph => {
            if (paragraph.trim()) {
                // If it's already a formatted element (starts with <), don't wrap
                if (paragraph.trim().startsWith('<')) {
                    return paragraph.trim();
                }
                // Otherwise wrap in paragraph with reduced margin
                return `<p class="mb-2">${paragraph.trim().replace(/\n/g, '<br>')}</p>`;
            }
            return '';
        })
        .filter(p => p)
        .join('');

    return html;
}



function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}
</script>

<style>
/* Full Height Layout */
.h-full {
    height: 100%;
}

.min-h-0 {
    min-height: 0;
}

/* Chat Interface Styles */
.chat-messages {
    scrollbar-width: thin;
    scrollbar-color: #cbd5e0 #f7fafc;
}

.chat-messages::-webkit-scrollbar {
    width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
    background: #f7fafc;
}

.chat-messages::-webkit-scrollbar-thumb {
    background: #cbd5e0;
    border-radius: 4px;
    border: 2px solid #f7fafc;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
    background: #a0aec0;
}

/* Enhanced scrollbar */
.chat-messages::-webkit-scrollbar {
    width: 8px;
}

/* Fixed height container with proper scrolling */
.chat-container {
    height: calc(100vh - 280px);
    min-height: 400px;
    max-height: calc(100vh - 280px);
}

/* Smooth scrolling */
.chat-messages {
    scroll-behavior: smooth;
}

.suggestion-btn {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
    color: #374151;
    border: 1px solid #d1d5db;
    border-radius: 0.75rem;
    transition: all 0.2s ease;
    cursor: pointer;
    font-weight: 500;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.suggestion-btn:hover {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
    border-color: #2563eb;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
}



.message {
    animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.user-message .message-content {
    background-color: #2563eb;
    color: white;
    margin-left: auto;
    max-width: 20rem;
}

.ai-message .message-content {
    background-color: #f3f4f6;
    color: #1f2937;
    margin-right: auto;
    max-width: 42rem;
}
</style>
