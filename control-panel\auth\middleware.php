<?php
/**
 * 认证中间件
 * 处理管理后台的认证和权限验证
 */

// 防止直接访问
if (!defined('ROOT_PATH')) {
    exit('Direct access not allowed');
}

/**
 * 管理员认证中间件类
 */
class AdminAuthMiddleware {
    
    /**
     * 检查管理员是否已登录
     */
    public static function checkAuth() {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        // 检查会话是否存在
        if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
            self::redirectToLogin('Authentication required');
            return false;
        }
        
        // 检查会话是否过期
        if (self::isSessionExpired()) {
            self::logout();
            self::redirectToLogin('Session expired');
            return false;
        }
        
        // 检查IP地址是否变化（安全检查）
        if (self::hasIPChanged()) {
            self::logout();
            self::redirectToLogin('Security violation detected');
            return false;
        }
        
        // 更新最后活动时间
        $_SESSION['admin_last_activity'] = time();
        
        return true;
    }
    
    /**
     * 检查管理员权限
     */
    public static function checkPermission($permission) {
        if (!self::checkAuth()) {
            return false;
        }
        
        $admin = self::getCurrentAdmin();
        if (!$admin) {
            return false;
        }
        
        // 超级管理员拥有所有权限
        if ($admin['role'] === 'super_admin') {
            return true;
        }
        
        // 检查具体权限
        return self::hasPermission($admin, $permission);
    }
    
    /**
     * 获取当前登录的管理员信息
     */
    public static function getCurrentAdmin() {
        if (!isset($_SESSION['admin_id'])) {
            return null;
        }
        
        // 从数据库获取管理员信息
        $adminId = $_SESSION['admin_id'];
        $admin = self::getAdminById($adminId);
        
        if (!$admin || $admin['status'] !== 'active') {
            self::logout();
            return null;
        }
        
        return $admin;
    }
    
    /**
     * 管理员登录
     */
    public static function login($username, $password, $remember = false) {
        // 检查登录尝试次数
        if (!self::checkLoginAttempts($username)) {
            return [
                'success' => false,
                'message' => 'Too many failed attempts. Account temporarily locked.'
            ];
        }
        
        // 验证用户名和密码
        $admin = self::validateCredentials($username, $password);
        if (!$admin) {
            self::recordFailedAttempt($username);
            return [
                'success' => false,
                'message' => 'Invalid username or password.'
            ];
        }
        
        // 检查账户状态
        if ($admin['status'] !== 'active') {
            return [
                'success' => false,
                'message' => 'Account is disabled.'
            ];
        }
        
        // 创建会话
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        session_regenerate_id(true); // 防止会话固定攻击
        
        $_SESSION['admin_logged_in'] = true;
        $_SESSION['admin_id'] = $admin['id'];
        $_SESSION['admin_username'] = $admin['username'];
        $_SESSION['admin_role'] = $admin['role'];
        $_SESSION['admin_login_time'] = time();
        $_SESSION['admin_last_activity'] = time();
        $_SESSION['admin_ip'] = self::getClientIP();
        
        // 记住我功能
        if ($remember) {
            self::setRememberToken($admin['id']);
        }
        
        // 清除失败尝试记录
        self::clearFailedAttempts($username);
        
        // 记录登录日志
        self::logSecurityEvent('admin_login', [
            'admin_id' => $admin['id'],
            'username' => $admin['username'],
            'ip_address' => self::getClientIP(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
        ]);
        
        // 更新最后登录时间
        self::updateLastLogin($admin['id']);
        
        return [
            'success' => true,
            'message' => 'Login successful.',
            'admin' => [
                'id' => $admin['id'],
                'username' => $admin['username'],
                'role' => $admin['role']
            ]
        ];
    }
    
    /**
     * 管理员登出
     */
    public static function logout() {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        // 记录登出日志
        if (isset($_SESSION['admin_id'])) {
            self::logSecurityEvent('admin_logout', [
                'admin_id' => $_SESSION['admin_id'],
                'username' => $_SESSION['admin_username'] ?? '',
                'ip_address' => self::getClientIP()
            ]);
        }
        
        // 清除记住我Cookie
        if (isset($_COOKIE['admin_remember_token'])) {
            setcookie('admin_remember_token', '', time() - 3600, '/', '', true, true);
            self::clearRememberToken($_SESSION['admin_id'] ?? 0);
        }
        
        // 销毁会话
        $_SESSION = array();
        
        if (ini_get("session.use_cookies")) {
            $params = session_get_cookie_params();
            setcookie(session_name(), '', time() - 42000,
                $params["path"], $params["domain"],
                $params["secure"], $params["httponly"]
            );
        }
        
        session_destroy();
    }
    
    /**
     * 检查会话是否过期
     */
    private static function isSessionExpired() {
        $timeout = 3600; // 1小时超时
        
        if (!isset($_SESSION['admin_last_activity'])) {
            return true;
        }
        
        return (time() - $_SESSION['admin_last_activity']) > $timeout;
    }
    
    /**
     * 检查IP地址是否变化
     */
    private static function hasIPChanged() {
        if (!isset($_SESSION['admin_ip'])) {
            return false;
        }
        
        return $_SESSION['admin_ip'] !== self::getClientIP();
    }
    
    /**
     * 获取客户端IP地址
     */
    private static function getClientIP() {
        $ipKeys = ['HTTP_CF_CONNECTING_IP', 'HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }
    
    /**
     * 验证用户凭据
     */
    private static function validateCredentials($username, $password) {
        // 简化的验证逻辑，实际应该从数据库查询
        $admins = [
            'admin' => [
                'id' => 1,
                'username' => 'admin',
                'password_hash' => password_hash('admin123', PASSWORD_DEFAULT),
                'role' => 'super_admin',
                'status' => 'active',
                'email' => '<EMAIL>'
            ],
            'manager' => [
                'id' => 2,
                'username' => 'manager',
                'password_hash' => password_hash('manager123', PASSWORD_DEFAULT),
                'role' => 'admin',
                'status' => 'active',
                'email' => '<EMAIL>'
            ]
        ];
        
        if (!isset($admins[$username])) {
            return false;
        }
        
        $admin = $admins[$username];
        
        if (!password_verify($password, $admin['password_hash'])) {
            return false;
        }
        
        return $admin;
    }
    
    /**
     * 检查登录尝试次数
     */
    private static function checkLoginAttempts($username) {
        $key = "login_attempts_{$username}";
        $attempts = $_SESSION[$key] ?? 0;
        
        return $attempts < 5; // 最多5次尝试
    }
    
    /**
     * 记录失败尝试
     */
    private static function recordFailedAttempt($username) {
        $key = "login_attempts_{$username}";
        $_SESSION[$key] = ($_SESSION[$key] ?? 0) + 1;
        $_SESSION["{$key}_time"] = time();
    }
    
    /**
     * 清除失败尝试记录
     */
    private static function clearFailedAttempts($username) {
        $key = "login_attempts_{$username}";
        unset($_SESSION[$key]);
        unset($_SESSION["{$key}_time"]);
    }
    
    /**
     * 检查管理员权限
     */
    private static function hasPermission($admin, $permission) {
        // 加载权限配置
        $permissionsConfig = include ROOT_PATH . '/control-panel/config/permissions.php';
        $roles = $permissionsConfig['roles'];
        
        if (!isset($roles[$admin['role']])) {
            return false;
        }
        
        $rolePermissions = $roles[$admin['role']]['permissions'];
        
        // 检查通配符权限
        if (in_array('*', $rolePermissions)) {
            return true;
        }
        
        // 检查具体权限
        if (in_array($permission, $rolePermissions)) {
            return true;
        }
        
        // 检查通配符匹配
        foreach ($rolePermissions as $rolePermission) {
            if (strpos($rolePermission, '*') !== false) {
                $pattern = str_replace('*', '.*', $rolePermission);
                if (preg_match("/^{$pattern}$/", $permission)) {
                    return true;
                }
            }
        }
        
        return false;
    }
    
    /**
     * 重定向到登录页面
     */
    private static function redirectToLogin($message = '') {
        $loginUrl = '/control-panel/auth/login.php';
        if ($message) {
            $loginUrl .= '?message=' . urlencode($message);
        }
        
        header('Location: ' . $loginUrl);
        exit;
    }
    
    /**
     * 记录安全事件
     */
    private static function logSecurityEvent($event, $data) {
        $logEntry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'event' => $event,
            'data' => $data
        ];
        
        $logFile = '/var/log/admin/security.log';
        $logLine = json_encode($logEntry) . "\n";
        
        // 确保日志目录存在
        $logDir = dirname($logFile);
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        file_put_contents($logFile, $logLine, FILE_APPEND | LOCK_EX);
    }
    
    /**
     * 获取管理员信息（模拟数据库查询）
     */
    private static function getAdminById($id) {
        // 实际应该从数据库查询
        $admins = [
            1 => [
                'id' => 1,
                'username' => 'admin',
                'role' => 'super_admin',
                'status' => 'active',
                'email' => '<EMAIL>'
            ],
            2 => [
                'id' => 2,
                'username' => 'manager',
                'role' => 'admin',
                'status' => 'active',
                'email' => '<EMAIL>'
            ]
        ];
        
        return $admins[$id] ?? null;
    }
    
    /**
     * 设置记住我Token
     */
    private static function setRememberToken($adminId) {
        $token = bin2hex(random_bytes(32));
        setcookie('admin_remember_token', $token, time() + (30 * 24 * 60 * 60), '/', '', true, true);
        
        // 实际应该存储到数据库
        $_SESSION['remember_token'] = $token;
    }
    
    /**
     * 清除记住我Token
     */
    private static function clearRememberToken($adminId) {
        // 实际应该从数据库删除
        unset($_SESSION['remember_token']);
    }
    
    /**
     * 更新最后登录时间
     */
    private static function updateLastLogin($adminId) {
        // 实际应该更新数据库
        $_SESSION['last_login_updated'] = time();
    }
}

/**
 * 权限检查辅助函数
 */
function hasPermission($permission) {
    return AdminAuthMiddleware::checkPermission($permission);
}

/**
 * 获取当前管理员
 */
function getCurrentAdmin() {
    return AdminAuthMiddleware::getCurrentAdmin();
}

/**
 * 检查是否已登录
 */
function isAdminLoggedIn() {
    return AdminAuthMiddleware::checkAuth();
}
?>
