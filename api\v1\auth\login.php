<?php
/**
 * 用户登录API
 * 处理普通邮箱登录
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// 使用统一的数据库连接
require_once dirname(dirname(dirname(__DIR__))) . '/includes/database-connection.php';

try {
    // 获取请求数据
    $input = json_decode(file_get_contents('php://input'), true);
    
    // 验证必需字段
    if (!isset($input['email']) || !isset($input['password'])) {
        throw new Exception('Email and password are required');
    }
    
    $email = trim(strtolower($input['email']));
    $password = $input['password'];
    $rememberMe = isset($input['remember_me']) && $input['remember_me'];
    
    // 验证邮箱格式
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        throw new Exception('Invalid email format');
    }
    
    // 查找用户
    $stmt = $pdo->prepare("SELECT * FROM pt_member WHERE email = ?");
    $stmt->execute([$email]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        error_log("Login failed: User not found for email {$email}");
        throw new Exception('Invalid email or password');
    }

    error_log("Login attempt for user {$user['email']}: status={$user['status']}, email_verified={$user['email_verified']}, has_password=" . (!empty($user['password']) ? 'yes' : 'no'));
    
    // 检查账户状态
    if ($user['status'] === 'suspended') {
        throw new Exception('Your account has been suspended. Please contact support.');
    }
    
    // 验证密码（Google用户没有密码）
    if (empty($user['password'])) {
        throw new Exception('This account uses Google Sign-In. Please use the Google login button.');
    }
    
    if (!password_verify($password, $user['password'])) {
        // 记录失败的登录尝试
        logLoginAttempt($pdo, $user['id'], false);
        error_log("Login failed for user {$user['email']}: Password verification failed");
        throw new Exception('Invalid email or password');
    }
    
    // 检查邮箱验证状态
    if (!$user['email_verified']) {
        error_log("Login failed for user {$user['email']}: Email not verified (email_verified = {$user['email_verified']})");
        throw new Exception('Please verify your email address before logging in. Go to the verification page to complete the process.');
    }
    
    // 更新最后登录时间
    $stmt = $pdo->prepare("UPDATE pt_member SET last_login = NOW(), updated_at = NOW() WHERE id = ?");
    $stmt->execute([$user['id']]);
    
    // 记录成功的登录
    logLoginAttempt($pdo, $user['id'], true);
    
    // 创建会话
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    $_SESSION['user_id'] = $user['id'];
    $_SESSION['user_email'] = $user['email'];
    $_SESSION['user_name'] = trim($user['first_name'] . ' ' . $user['last_name']);
    $_SESSION['username'] = $user['username'];
    $_SESSION['login_time'] = time();
    
    // 如果选择记住我，设置长期cookie
    if ($rememberMe) {
        $token = bin2hex(random_bytes(32));
        $expires = time() + (30 * 24 * 60 * 60); // 30天
        
        // 保存记住我令牌到数据库
        $stmt = $pdo->prepare("
            INSERT INTO pt_member_tokens (user_id, token, type, expires_at, created_at)
            VALUES (?, ?, 'remember_me', FROM_UNIXTIME(?), NOW())
            ON DUPLICATE KEY UPDATE token = VALUES(token), expires_at = VALUES(expires_at)
        ");
        $stmt->execute([$user['id'], hash('sha256', $token), $expires]);
        
        // 设置cookie
        setcookie('remember_token', $token, $expires, '/', '', false, true);
    }
    
    // 清理输出缓冲区，确保干净的JSON响应
    if (ob_get_level()) {
        ob_clean();
    }

    // 返回成功响应
    echo json_encode([
        'success' => true,
        'message' => 'Login successful',
        'data' => [
            'redirect' => isset($input['redirect']) ? $input['redirect'] : '/user-center/',
            'user' => [
                'id' => $user['id'],
                'email' => $user['email'],
                'name' => trim($user['first_name'] . ' ' . $user['last_name']),
                'username' => $user['username'],
                'avatar' => $user['avatar'],
                'subscription_type' => $user['subscription_type'],
                'api_quota' => $user['api_quota'],
                'api_used' => $user['api_used']
            ]
        ]
    ]);
    
} catch (Exception $e) {
    error_log("Login error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

/**
 * 记录登录尝试
 */
function logLoginAttempt($pdo, $userId, $success) {
    try {
        $action = $success ? 'login_success' : 'login_failed';
        $description = $success ? 'User logged in successfully' : 'Failed login attempt';
        
        $stmt = $pdo->prepare("
            INSERT INTO pt_member_activity_log (user_id, action, description, ip_address, user_agent, created_at)
            VALUES (?, ?, ?, ?, ?, NOW())
        ");
        $stmt->execute([
            $userId,
            $action,
            $description,
            $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        ]);
    } catch (Exception $e) {
        error_log("Failed to log login attempt: " . $e->getMessage());
    }
}
?>
