<?php
/**
 * API管理页面
 */

// 检查登录状态
if (!isset($_SESSION['admin_logged_in']) || !$_SESSION['admin_logged_in']) {
    header('Location: /control-panel/login.php');
    exit;
}

// 处理表单提交
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    try {
        if ($action === 'add_key') {
            // 添加API密钥
            $platform_id = $_POST['platform_id'];
            $name = $_POST['name'];
            $api_key = $_POST['api_key'];
            
            $stmt = $pdo->prepare("INSERT INTO pt_service_key (platform_id, name, api_key) VALUES (?, ?, ?)");
            $stmt->execute([$platform_id, $name, $api_key]);
            
            $success_message = "API key added successfully!";
            
        } elseif ($action === 'delete_key') {
            // 删除API密钥
            $key_id = $_POST['key_id'];
            
            $stmt = $pdo->prepare("DELETE FROM pt_service_key WHERE id = ?");
            $stmt->execute([$key_id]);
            
            $success_message = "API key deleted successfully!";

        } elseif ($action === 'toggle_key') {
            // Toggle API key status
            $key_id = $_POST['key_id'];
            $is_active = $_POST['is_active'];

            $stmt = $pdo->prepare("UPDATE pt_service_key SET is_active = ? WHERE id = ?");
            $stmt->execute([$is_active, $key_id]);

            $success_message = "API key status updated successfully!";

        } elseif ($action === 'edit_key') {
            // Edit API key
            $key_id = $_POST['key_id'];
            $name = $_POST['name'];
            $api_key = $_POST['api_key'];

            $stmt = $pdo->prepare("UPDATE pt_service_key SET name = ?, api_key = ? WHERE id = ?");
            $stmt->execute([$name, $api_key, $key_id]);

            $success_message = "API key updated successfully!";

        } elseif ($action === 'add_model') {
            // Add new model
            $platform_id = $_POST['platform_id'];
            $model_code = $_POST['model_code'];
            $model_name = $_POST['model_name'];
            $description = $_POST['description'];
            $max_tokens = $_POST['max_tokens'];
            $model_type = $_POST['model_type'] ?? 'chat';
            $supports_vision = isset($_POST['supports_vision']) ? 1 : 0;
            $supports_audio = isset($_POST['supports_audio']) ? 1 : 0;
            $supports_function_call = isset($_POST['supports_function_call']) ? 1 : 0;
            $is_free = isset($_POST['is_free']) ? 1 : 0;

            $stmt = $pdo->prepare("INSERT INTO pt_service_model (platform_id, model_code, model_name, description, max_tokens, supports_vision, supports_audio, supports_function_call, is_free, model_type) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
            $stmt->execute([$platform_id, $model_code, $model_name, $description, $max_tokens, $supports_vision, $supports_audio, $supports_function_call, $is_free, $model_type]);

            $success_message = "Model added successfully!";

        } elseif ($action === 'edit_model') {
            // Edit model
            $model_id = $_POST['model_id'];
            $model_code = $_POST['model_code'];
            $model_name = $_POST['model_name'];
            $description = $_POST['description'];
            $max_tokens = $_POST['max_tokens'];
            $model_type = $_POST['model_type'] ?? 'chat';
            $supports_vision = isset($_POST['supports_vision']) ? 1 : 0;
            $supports_audio = isset($_POST['supports_audio']) ? 1 : 0;
            $supports_function_call = isset($_POST['supports_function_call']) ? 1 : 0;
            $is_free = isset($_POST['is_free']) ? 1 : 0;

            $stmt = $pdo->prepare("UPDATE pt_service_model SET model_code = ?, model_name = ?, description = ?, max_tokens = ?, supports_vision = ?, supports_audio = ?, supports_function_call = ?, is_free = ?, model_type = ? WHERE id = ?");
            $stmt->execute([$model_code, $model_name, $description, $max_tokens, $supports_vision, $supports_audio, $supports_function_call, $is_free, $model_type, $model_id]);

            $success_message = "Model updated successfully!";

        } elseif ($action === 'delete_model') {
            // Delete model
            $model_id = $_POST['model_id'];

            $stmt = $pdo->prepare("DELETE FROM pt_service_model WHERE id = ?");
            $stmt->execute([$model_id]);

            $success_message = "Model deleted successfully!";

        } elseif ($action === 'update_config') {
            // Update platform configuration
            $platform_id = $_POST['platform_id'];
            $default_model = $_POST['default_model'];
            $max_concurrent = $_POST['max_concurrent'];
            $timeout = $_POST['timeout'];
            $app_code = $_POST['app_code'] ?? '';

            // Update or insert configuration
            $configs = [
                'default_model' => $default_model,
                'max_concurrent' => $max_concurrent,
                'timeout' => $timeout,
                'app_code' => $app_code
            ];

            foreach ($configs as $key => $value) {
                if ($value !== '') { // Only save non-empty values
                    $stmt = $pdo->prepare("INSERT INTO pt_service_config (platform_id, config_key, config_value) VALUES (?, ?, ?) ON DUPLICATE KEY UPDATE config_value = VALUES(config_value)");
                    $stmt->execute([$platform_id, $key, $value]);
                }
            }

            $success_message = "Platform configuration updated successfully!";
        }
        
    } catch (Exception $e) {
        $error_message = "Operation failed: " . $e->getMessage();
    }
}

// 获取所有平台
$stmt = $pdo->query("SELECT * FROM pt_service_platform ORDER BY name");
$platforms = $stmt->fetchAll();

// 获取所有API密钥
$stmt = $pdo->query("
    SELECT ak.*, ap.name as platform_name, ap.code as platform_code
    FROM pt_service_key ak
    JOIN pt_service_platform ap ON ak.platform_id = ap.id
    ORDER BY ap.name, ak.name
");
$api_keys = $stmt->fetchAll();

// 获取所有模型
$stmt = $pdo->query("
    SELECT am.*, ap.name as platform_name, ap.code as platform_code
    FROM pt_service_model am
    JOIN pt_service_platform ap ON am.platform_id = ap.id
    WHERE am.is_active = 1
    ORDER BY ap.name, am.sort_order, am.model_name
");
$models = $stmt->fetchAll();

// 按平台分组模型
$models_by_platform = [];
foreach ($models as $model) {
    $models_by_platform[$model['platform_id']][] = $model;
}

// 获取平台配置
$stmt = $pdo->query("
    SELECT ac.*, ap.name as platform_name
    FROM pt_service_config ac
    JOIN pt_service_platform ap ON ac.platform_id = ap.id
");
$configs_data = $stmt->fetchAll();

// 按平台分组配置
$configs = [];
foreach ($configs_data as $config) {
    $configs[$config['platform_id']][$config['config_key']] = $config['config_value'];
}
?>

<!-- API Management Content -->
<div class="space-y-6">
    <!-- Page Description -->
    <div class="mb-8">
        <p class="text-gray-600">Manage multi-platform API keys and model configurations</p>
    </div>

            <!-- 消息提示 -->
            <?php if (isset($success_message)): ?>
            <div class="mb-6 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
                <?= htmlspecialchars($success_message) ?>
            </div>
            <?php endif; ?>

            <?php if (isset($error_message)): ?>
            <div class="mb-6 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                <?= htmlspecialchars($error_message) ?>
            </div>
            <?php endif; ?>

            <!-- 标签页 -->
            <div class="mb-6">
                <div class="border-b border-gray-200">
                    <nav class="-mb-px flex space-x-8">
                        <button onclick="showTab('keys')" id="keys-tab" class="tab-button active py-2 px-1 border-b-2 border-blue-500 font-medium text-sm text-blue-600">
                            <i class="fas fa-key mr-2"></i>API Keys
                        </button>
                        <button onclick="showTab('models')" id="models-tab" class="tab-button py-2 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300">
                            <i class="fas fa-robot mr-2"></i>Models
                        </button>
                        <button onclick="showTab('platforms')" id="platforms-tab" class="tab-button py-2 px-1 border-b-2 border-transparent font-medium text-sm text-gray-500 hover:text-gray-700 hover:border-gray-300">
                            <i class="fas fa-cogs mr-2"></i>Platform Config
                        </button>
                    </nav>
                </div>
            </div>

            <!-- API密钥管理标签页 -->
            <div id="keys-content" class="tab-content">
                <!-- Add API Key -->
                <div class="bg-white shadow rounded-lg p-6 mb-6">
                    <h2 class="text-lg font-medium text-gray-900 mb-4">Add API Key</h2>
                    <form method="POST" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <input type="hidden" name="action" value="add_key">

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Platform</label>
                            <select name="platform_id" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">Select Platform</option>
                                <?php foreach ($platforms as $platform): ?>
                                <option value="<?= $platform['id'] ?>"><?= htmlspecialchars($platform['name']) ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Key Name</label>
                            <input type="text" name="name" required placeholder="e.g., Main Key 1"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">API Key</label>
                            <input type="password" name="api_key" required placeholder="Enter API Key"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>

                        <div class="flex items-end">
                            <button type="submit" class="w-full bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <i class="fas fa-plus mr-2"></i>Add Key
                            </button>
                        </div>
                    </form>
                </div>

                <!-- API Keys List -->
                <div class="bg-white shadow rounded-lg overflow-hidden">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <div class="flex justify-between items-center">
                            <h2 class="text-lg font-medium text-gray-900">API Keys List</h2>
                            <div class="relative">
                                <input type="text" id="keySearch" placeholder="Search keys..."
                                       class="w-64 px-3 py-2 pl-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-search text-gray-400"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="overflow-x-auto">
                        <table id="keysTable" class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Platform</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">API Key</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Usage Count</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Used</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <?php foreach ($api_keys as $key): ?>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                <?= htmlspecialchars($key['platform_name']) ?>
                                            </span>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <?= htmlspecialchars($key['name']) ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 font-mono">
                                        <span class="api-key-display" data-key="<?= htmlspecialchars($key['api_key']) ?>">
                                            <?= substr($key['api_key'], 0, 8) ?>...<?= substr($key['api_key'], -4) ?>
                                        </span>
                                        <button onclick="toggleKeyVisibility(this)" class="ml-2 text-gray-400 hover:text-gray-600">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <form method="POST" class="inline">
                                            <input type="hidden" name="action" value="toggle_key">
                                            <input type="hidden" name="key_id" value="<?= $key['id'] ?>">
                                            <input type="hidden" name="is_active" value="<?= $key['is_active'] ? 0 : 1 ?>">
                                            <button type="submit" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?= $key['is_active'] ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' ?>">
                                                <?= $key['is_active'] ? 'Active' : 'Inactive' ?>
                                            </button>
                                        </form>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <?= number_format($key['usage_count']) ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <?= $key['last_used_at'] ? date('Y-m-d H:i', strtotime($key['last_used_at'])) : 'Never' ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex space-x-2">
                                            <button onclick="editKey(<?= $key['id'] ?>, '<?= htmlspecialchars($key['name'], ENT_QUOTES) ?>', '<?= htmlspecialchars($key['api_key'], ENT_QUOTES) ?>')"
                                                    class="text-blue-600 hover:text-blue-900" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button onclick="showDeleteKeyModal(<?= $key['id'] ?>, '<?= htmlspecialchars($key['name'], ENT_QUOTES) ?>')"
                                                    class="text-red-600 hover:text-red-900" title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                                
                                <?php if (empty($api_keys)): ?>
                                <tr>
                                    <td colspan="7" class="px-6 py-4 text-center text-gray-500">
                                        No API keys found. Please add one first.
                                    </td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 模型配置标签页 -->
            <div id="models-content" class="tab-content hidden">
                <?php foreach ($platforms as $platform): ?>
                <div class="bg-white shadow rounded-lg p-6 mb-6">
                    <div class="flex justify-between items-center mb-4">
                        <h2 class="text-lg font-medium text-gray-900">
                            <i class="fas fa-robot mr-2"></i><?= htmlspecialchars($platform['name']) ?>
                        </h2>
                        <button onclick="showAddModelModal(<?= $platform['id'] ?>, '<?= htmlspecialchars($platform['name']) ?>')"
                                class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 text-sm">
                            <i class="fas fa-plus mr-2"></i>Add Model
                        </button>
                    </div>

                    <?php if (isset($models_by_platform[$platform['id']])): ?>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <?php foreach ($models_by_platform[$platform['id']] as $model): ?>
                        <div class="border border-gray-200 rounded-lg p-4 relative">
                            <div class="absolute top-2 right-2 flex space-x-1">
                                <button onclick="editModel(<?= $model['id'] ?>, '<?= htmlspecialchars($model['model_code'], ENT_QUOTES) ?>', '<?= htmlspecialchars($model['model_name'], ENT_QUOTES) ?>', '<?= htmlspecialchars($model['description'], ENT_QUOTES) ?>', <?= $model['max_tokens'] ?>, <?= $model['supports_vision'] ?>, <?= $model['supports_audio'] ?>, <?= $model['supports_function_call'] ?>, <?= isset($model['is_free']) ? $model['is_free'] : 0 ?>, '<?= htmlspecialchars($model['model_type'] ?? 'chat', ENT_QUOTES) ?>')"
                                        class="text-blue-600 hover:text-blue-900 text-sm" title="Edit">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button onclick="showDeleteModelModal(<?= $model['id'] ?>, '<?= htmlspecialchars($model['model_name'], ENT_QUOTES) ?>')"
                                        class="text-red-600 hover:text-red-900 text-sm" title="Delete">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>

                            <div class="flex items-center justify-between mb-2 pr-12">
                                <div class="flex items-center space-x-2">
                                    <?php
                                    // 根据模型类型显示图标
                                    $modelType = $model['model_type'] ?? 'chat';
                                    $typeIcons = [
                                        'chat' => '<i class="fas fa-comments text-blue-500"></i>',
                                        'vision' => '<i class="fas fa-eye text-purple-500"></i>',
                                        'audio' => '<i class="fas fa-microphone text-orange-500"></i>',
                                        'image' => '<i class="fas fa-image text-pink-500"></i>',
                                        'video' => '<i class="fas fa-video text-red-500"></i>',
                                        'embedding' => '<i class="fas fa-vector-square text-teal-500"></i>',
                                        'reranker' => '<i class="fas fa-sort text-indigo-500"></i>'
                                    ];
                                    echo $typeIcons[$modelType] ?? $typeIcons['chat'];
                                    ?>
                                    <h3 class="font-medium text-gray-900"><?= htmlspecialchars($model['model_name']) ?></h3>
                                    <?php if (isset($model['is_free']) && $model['is_free']): ?>
                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800" title="Free Model">
                                        FREE
                                    </span>
                                    <?php endif; ?>
                                </div>
                                <div class="flex space-x-1">
                                    <?php if ($model['supports_vision']): ?>
                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800" title="Supports Vision">
                                        <i class="fas fa-eye"></i>
                                    </span>
                                    <?php endif; ?>
                                    <?php if ($model['supports_audio']): ?>
                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-orange-100 text-orange-800" title="Supports Audio">
                                        <i class="fas fa-microphone"></i>
                                    </span>
                                    <?php endif; ?>
                                    <?php if ($model['supports_function_call']): ?>
                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800" title="Supports Function Call">
                                        <i class="fas fa-code"></i>
                                    </span>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <p class="text-sm text-gray-600 mb-2"><?= htmlspecialchars($model['description']) ?></p>
                            <div class="text-xs text-gray-500">
                                <span class="font-mono"><?= htmlspecialchars($model['model_code']) ?></span>
                                <span class="ml-2">Max Tokens: <?= number_format($model['max_tokens']) ?></span>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                    <?php else: ?>
                    <div class="text-center py-8">
                        <p class="text-gray-500 mb-4">No available models for this platform</p>
                        <button onclick="showAddModelModal(<?= $platform['id'] ?>, '<?= htmlspecialchars($platform['name']) ?>')"
                                class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                            <i class="fas fa-plus mr-2"></i>Add First Model
                        </button>
                    </div>
                    <?php endif; ?>
                </div>
                <?php endforeach; ?>

                <!-- Model Type Icons Legend -->
                <div class="bg-gray-50 rounded-lg p-6 mt-6">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- Model Type Icons -->
                        <div>
                            <h3 class="text-sm font-semibold text-gray-800 mb-4 flex items-center">
                                <i class="fas fa-palette mr-2 text-blue-500"></i>
                                Model Type Icons
                            </h3>
                            <div class="grid grid-cols-1 gap-3">
                                <div class="flex items-center justify-between py-2 px-3 bg-white rounded-md shadow-sm">
                                    <div class="flex items-center space-x-3">
                                        <i class="fas fa-comments text-blue-500 w-4"></i>
                                        <span class="text-sm text-gray-700 font-medium">Chat</span>
                                    </div>
                                    <span class="text-xs text-gray-500">Conversational models</span>
                                </div>
                                <div class="flex items-center justify-between py-2 px-3 bg-white rounded-md shadow-sm">
                                    <div class="flex items-center space-x-3">
                                        <i class="fas fa-eye text-purple-500 w-4"></i>
                                        <span class="text-sm text-gray-700 font-medium">Vision</span>
                                    </div>
                                    <span class="text-xs text-gray-500">Image understanding</span>
                                </div>
                                <div class="flex items-center justify-between py-2 px-3 bg-white rounded-md shadow-sm">
                                    <div class="flex items-center space-x-3">
                                        <i class="fas fa-microphone text-orange-500 w-4"></i>
                                        <span class="text-sm text-gray-700 font-medium">Audio</span>
                                    </div>
                                    <span class="text-xs text-gray-500">Speech processing</span>
                                </div>
                                <div class="flex items-center justify-between py-2 px-3 bg-white rounded-md shadow-sm">
                                    <div class="flex items-center space-x-3">
                                        <i class="fas fa-image text-pink-500 w-4"></i>
                                        <span class="text-sm text-gray-700 font-medium">Image Gen</span>
                                    </div>
                                    <span class="text-xs text-gray-500">Image generation</span>
                                </div>
                                <div class="flex items-center justify-between py-2 px-3 bg-white rounded-md shadow-sm">
                                    <div class="flex items-center space-x-3">
                                        <i class="fas fa-video text-red-500 w-4"></i>
                                        <span class="text-sm text-gray-700 font-medium">Video Gen</span>
                                    </div>
                                    <span class="text-xs text-gray-500">Video generation</span>
                                </div>
                                <div class="flex items-center justify-between py-2 px-3 bg-white rounded-md shadow-sm">
                                    <div class="flex items-center space-x-3">
                                        <i class="fas fa-vector-square text-teal-500 w-4"></i>
                                        <span class="text-sm text-gray-700 font-medium">Embedding</span>
                                    </div>
                                    <span class="text-xs text-gray-500">Text vectorization</span>
                                </div>
                                <div class="flex items-center justify-between py-2 px-3 bg-white rounded-md shadow-sm">
                                    <div class="flex items-center space-x-3">
                                        <i class="fas fa-sort text-indigo-500 w-4"></i>
                                        <span class="text-sm text-gray-700 font-medium">Reranker</span>
                                    </div>
                                    <span class="text-xs text-gray-500">Result reranking</span>
                                </div>
                            </div>
                        </div>

                        <!-- Capability Badges -->
                        <div>
                            <h3 class="text-sm font-semibold text-gray-800 mb-4 flex items-center">
                                <i class="fas fa-tags mr-2 text-green-500"></i>
                                Capability Badges
                            </h3>
                            <div class="grid grid-cols-1 gap-3">
                                <div class="flex items-center justify-between py-2 px-3 bg-white rounded-md shadow-sm">
                                    <div class="flex items-center space-x-3">
                                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800">
                                            <i class="fas fa-eye"></i>
                                        </span>
                                        <span class="text-sm text-gray-700 font-medium">Vision Support</span>
                                    </div>
                                    <span class="text-xs text-gray-500">Can process images</span>
                                </div>
                                <div class="flex items-center justify-between py-2 px-3 bg-white rounded-md shadow-sm">
                                    <div class="flex items-center space-x-3">
                                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-orange-100 text-orange-800">
                                            <i class="fas fa-microphone"></i>
                                        </span>
                                        <span class="text-sm text-gray-700 font-medium">Audio Support</span>
                                    </div>
                                    <span class="text-xs text-gray-500">Can process audio</span>
                                </div>
                                <div class="flex items-center justify-between py-2 px-3 bg-white rounded-md shadow-sm">
                                    <div class="flex items-center space-x-3">
                                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                            <i class="fas fa-code"></i>
                                        </span>
                                        <span class="text-sm text-gray-700 font-medium">Function Call</span>
                                    </div>
                                    <span class="text-xs text-gray-500">Can call external tools</span>
                                </div>
                                <div class="flex items-center justify-between py-2 px-3 bg-white rounded-md shadow-sm">
                                    <div class="flex items-center space-x-3">
                                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                                            FREE
                                        </span>
                                        <span class="text-sm text-gray-700 font-medium">Free Model</span>
                                    </div>
                                    <span class="text-xs text-gray-500">No usage cost</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 平台配置标签页 -->
            <div id="platforms-content" class="tab-content hidden">
                <?php foreach ($platforms as $platform): ?>
                <div class="bg-white shadow rounded-lg p-6 mb-6">
                    <h2 class="text-lg font-medium text-gray-900 mb-4">
                        <i class="fas fa-cogs mr-2"></i><?= htmlspecialchars($platform['name']) ?>
                    </h2>
                    
                    <form method="POST" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <input type="hidden" name="action" value="update_config">
                        <input type="hidden" name="platform_id" value="<?= $platform['id'] ?>">

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Default Model</label>
                            <select name="default_model" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">Select Default Model</option>
                                <?php if (isset($models_by_platform[$platform['id']])): ?>
                                    <?php foreach ($models_by_platform[$platform['id']] as $model): ?>
                                    <option value="<?= htmlspecialchars($model['model_code']) ?>"
                                            <?= (isset($configs[$platform['id']]['default_model']) && $configs[$platform['id']]['default_model'] === $model['model_code']) ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($model['model_name']) ?>
                                    </option>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </select>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Max Concurrent</label>
                            <input type="number" name="max_concurrent" min="1" max="100"
                                   value="<?= htmlspecialchars($configs[$platform['id']]['max_concurrent'] ?? '5') ?>"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Timeout (seconds)</label>
                            <input type="number" name="timeout" min="5" max="300"
                                   value="<?= htmlspecialchars($configs[$platform['id']]['timeout'] ?? '30') ?>"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>

                        <?php if ($platform['code'] === 'aihubmix'): ?>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">APP-Code <span class="text-xs text-gray-500">(10% discount)</span></label>
                            <input type="text" name="app_code" placeholder="KXTM3281"
                                   value="<?= htmlspecialchars($configs[$platform['id']]['app_code'] ?? 'KXTM3281') ?>"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>
                        <?php endif; ?>

                        <div class="<?= $platform['code'] === 'aihubmix' ? 'lg:col-span-4' : 'md:col-span-2 lg:col-span-4' ?>">
                            <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <i class="fas fa-save mr-2"></i>Save Configuration
                            </button>
                        </div>
                    </form>
                    
                    <div class="mt-4 pt-4 border-t border-gray-200">
                        <h3 class="text-sm font-medium text-gray-700 mb-2">Platform Information</h3>
                        <div class="text-sm text-gray-600">
                            <p><strong>API Base URL:</strong> <code class="bg-gray-100 px-2 py-1 rounded"><?= htmlspecialchars($platform['base_url']) ?></code></p>
                            <p class="mt-1"><strong>Platform Code:</strong> <code class="bg-gray-100 px-2 py-1 rounded"><?= htmlspecialchars($platform['code']) ?></code></p>
                            <?php if ($platform['description']): ?>
                            <p class="mt-1"><strong>Description:</strong> <?= htmlspecialchars($platform['description']) ?></p>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
    </div>
</div>

<!-- Edit API Key Modal -->
<div id="editKeyModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Edit API Key</h3>
            <form method="POST" id="editKeyForm">
                <input type="hidden" name="action" value="edit_key">
                <input type="hidden" name="key_id" id="editKeyId">

                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Key Name</label>
                    <input type="text" name="name" id="editKeyName" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>

                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">API Key</label>
                    <input type="text" name="api_key" id="editKeyValue" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>

                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="closeEditModal()"
                            class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">
                        Cancel
                    </button>
                    <button type="submit"
                            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                        Update Key
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Add/Edit Model Modal -->
<div id="modelModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-10 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <h3 id="modelModalTitle" class="text-lg font-medium text-gray-900 mb-4">Add Model</h3>
            <form method="POST" id="modelForm">
                <input type="hidden" name="action" id="modelAction" value="add_model">
                <input type="hidden" name="model_id" id="modelId">
                <input type="hidden" name="platform_id" id="modelPlatformId">

                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Model Code</label>
                        <input type="text" name="model_code" id="modelCode" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="e.g., gpt-4o-mini">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Model Name</label>
                        <input type="text" name="model_name" id="modelName" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="e.g., GPT-4o Mini">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Model Type</label>
                        <select name="model_type" id="modelType"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="chat">💬 Chat</option>
                            <option value="vision">👁️ Vision</option>
                            <option value="audio">🎵 Audio</option>
                            <option value="image">🖼️ Image Generation</option>
                            <option value="video">🎬 Video Generation</option>
                            <option value="embedding">📊 Embedding</option>
                            <option value="reranker">🔄 Reranker</option>
                        </select>
                    </div>
                </div>

                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                    <textarea name="description" id="modelDescription" rows="3"
                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                              placeholder="Model description..."></textarea>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Max Tokens</label>
                        <input type="number" name="max_tokens" id="modelMaxTokens" required min="1"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                               placeholder="e.g., 128000">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Capabilities</label>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="checkbox" name="supports_vision" id="modelSupportsVision" class="mr-2">
                                <span class="text-sm">Supports Vision</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="supports_audio" id="modelSupportsAudio" class="mr-2">
                                <span class="text-sm">Supports Audio</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="supports_function_call" id="modelSupportsFunctionCall" class="mr-2">
                                <span class="text-sm">Supports Function Call</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" name="is_free" id="modelIsFree" class="mr-2">
                                <span class="text-sm">Free Model</span>
                            </label>
                        </div>
                    </div>
                </div>

                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="closeModelModal()"
                            class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">
                        Cancel
                    </button>
                    <button type="submit" id="modelSubmitBtn"
                            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                        Add Model
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 删除API密钥确认模态框 -->
<div id="deleteKeyModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3 text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mt-4">Delete API Key</h3>
            <div class="mt-2 px-7 py-3">
                <p class="text-sm text-gray-500">
                    Are you sure you want to delete the API key "<span id="deleteKeyName" class="font-semibold"></span>"?
                    This action cannot be undone.
                </p>
            </div>
            <div class="items-center px-4 py-3">
                <form id="deleteKeyForm" method="POST" class="inline">
                    <input type="hidden" name="action" value="delete_key">
                    <input type="hidden" name="key_id" id="deleteKeyId">
                    <div class="flex space-x-3 justify-center">
                        <button type="button" onclick="closeDeleteKeyModal()"
                                class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors">
                            Cancel
                        </button>
                        <button type="submit"
                                class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors">
                            Delete
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- 删除模型确认模态框 -->
<div id="deleteModelModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3 text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mt-4">Delete Model</h3>
            <div class="mt-2 px-7 py-3">
                <p class="text-sm text-gray-500">
                    Are you sure you want to delete the model "<span id="deleteModelName" class="font-semibold"></span>"?
                    This action cannot be undone.
                </p>
            </div>
            <div class="items-center px-4 py-3">
                <form id="deleteModelForm" method="POST" class="inline">
                    <input type="hidden" name="action" value="delete_model">
                    <input type="hidden" name="model_id" id="deleteModelId">
                    <div class="flex space-x-3 justify-center">
                        <button type="button" onclick="closeDeleteModelModal()"
                                class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 transition-colors">
                            Cancel
                        </button>
                        <button type="submit"
                                class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors">
                            Delete
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
        // 标签页切换
        function showTab(tabName) {
            // 隐藏所有标签页内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.add('hidden');
            });
            
            // 移除所有标签按钮的激活状态
            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('active', 'border-blue-500', 'text-blue-600');
                button.classList.add('border-transparent', 'text-gray-500');
            });
            
            // 显示选中的标签页内容
            document.getElementById(tabName + '-content').classList.remove('hidden');
            
            // 激活选中的标签按钮
            const activeButton = document.getElementById(tabName + '-tab');
            activeButton.classList.add('active', 'border-blue-500', 'text-blue-600');
            activeButton.classList.remove('border-transparent', 'text-gray-500');
        }

        // 切换API密钥可见性
        function toggleKeyVisibility(button) {
            const keyDisplay = button.previousElementSibling;
            const fullKey = keyDisplay.getAttribute('data-key');
            const icon = button.querySelector('i');
            
            if (icon.classList.contains('fa-eye')) {
                // 显示完整密钥
                keyDisplay.textContent = fullKey;
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                // 隐藏密钥
                keyDisplay.textContent = fullKey.substr(0, 8) + '...' + fullKey.substr(-4);
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        }

        // Edit API Key functions
        function editKey(keyId, keyName, keyValue) {
            document.getElementById('editKeyId').value = keyId;
            document.getElementById('editKeyName').value = keyName;
            document.getElementById('editKeyValue').value = keyValue;
            document.getElementById('editKeyModal').classList.remove('hidden');
        }

        function closeEditModal() {
            document.getElementById('editKeyModal').classList.add('hidden');
        }

        // Model management functions
        function showAddModelModal(platformId, platformName) {
            document.getElementById('modelModalTitle').textContent = `Add Model - ${platformName}`;
            document.getElementById('modelAction').value = 'add_model';
            document.getElementById('modelPlatformId').value = platformId;
            document.getElementById('modelSubmitBtn').textContent = 'Add Model';

            // Reset form
            document.getElementById('modelForm').reset();
            document.getElementById('modelPlatformId').value = platformId;

            document.getElementById('modelModal').classList.remove('hidden');
        }

        function editModel(modelId, modelCode, modelName, description, maxTokens, supportsVision, supportsAudio, supportsFunctionCall, isFree, modelType) {
            document.getElementById('modelModalTitle').textContent = 'Edit Model';
            document.getElementById('modelAction').value = 'edit_model';
            document.getElementById('modelId').value = modelId;
            document.getElementById('modelSubmitBtn').textContent = 'Update Model';

            // Fill form with current values
            document.getElementById('modelCode').value = modelCode;
            document.getElementById('modelName').value = modelName;
            document.getElementById('modelDescription').value = description;
            document.getElementById('modelMaxTokens').value = maxTokens;
            document.getElementById('modelType').value = modelType || 'chat';
            document.getElementById('modelSupportsVision').checked = supportsVision == 1;
            document.getElementById('modelSupportsAudio').checked = supportsAudio == 1;
            document.getElementById('modelSupportsFunctionCall').checked = supportsFunctionCall == 1;
            document.getElementById('modelIsFree').checked = isFree == 1;

            document.getElementById('modelModal').classList.remove('hidden');
        }

        function closeModelModal() {
            document.getElementById('modelModal').classList.add('hidden');
        }

        // 删除确认模态框函数
        function showDeleteKeyModal(keyId, keyName) {
            document.getElementById('deleteKeyId').value = keyId;
            document.getElementById('deleteKeyName').textContent = keyName;
            document.getElementById('deleteKeyModal').classList.remove('hidden');
        }

        function closeDeleteKeyModal() {
            document.getElementById('deleteKeyModal').classList.add('hidden');
        }

        function showDeleteModelModal(modelId, modelName) {
            document.getElementById('deleteModelId').value = modelId;
            document.getElementById('deleteModelName').textContent = modelName;
            document.getElementById('deleteModelModal').classList.remove('hidden');
        }

        function closeDeleteModelModal() {
            document.getElementById('deleteModelModal').classList.add('hidden');
        }

        // Search functionality
        function filterKeys() {
            const searchTerm = document.getElementById('keySearch').value.toLowerCase();
            const rows = document.querySelectorAll('#keysTable tbody tr');

            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 默认显示API密钥管理标签页
            showTab('keys');

            // 绑定搜索事件
            document.getElementById('keySearch').addEventListener('input', filterKeys);

            // 点击模态框外部关闭
            document.getElementById('editKeyModal').addEventListener('click', function(e) {
                if (e.target === this) {
                    closeEditModal();
                }
            });

            document.getElementById('modelModal').addEventListener('click', function(e) {
                if (e.target === this) {
                    closeModelModal();
                }
            });

            // 删除模态框点击外部关闭
            document.getElementById('deleteKeyModal').addEventListener('click', function(e) {
                if (e.target === this) {
                    closeDeleteKeyModal();
                }
            });

            document.getElementById('deleteModelModal').addEventListener('click', function(e) {
                if (e.target === this) {
                    closeDeleteModelModal();
                }
            });

            // ESC键关闭模态框
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    closeEditModal();
                    closeModelModal();
                    closeDeleteKeyModal();
                    closeDeleteModelModal();
                }
            });
        });
    </script>
