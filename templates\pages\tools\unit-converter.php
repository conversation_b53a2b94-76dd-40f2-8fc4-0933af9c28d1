<?php
/**
 * Unit Converter Tool Page
 * Zero border-radius design - no rounded corners
 */

$currentPage = 'tool-unit-converter';
$breadcrumbs = [
    ['name' => 'Home', 'url' => '/'],
    ['name' => 'Tools', 'url' => '/tools'],
    ['name' => 'Converters', 'url' => '/tools/converters'],
    ['name' => 'Unit Converter']
];

// Include dynamic SEO helper
require_once ROOT_PATH . '/app/helpers/dynamic-seo.php';

// Generate SEO data
$seoData = [
    'title' => 'Unit Converter Online - Prompt2Tool',
    'description' => 'Convert between different units of length, weight, volume, and temperature quickly and accurately. Free online unit conversion tool.',
    'keywords' => 'unit converter, length converter, weight converter, volume converter, temperature converter, metric conversion, imperial conversion',
    'og_title' => 'Unit Converter Online - Prompt2Tool',
    'og_description' => 'Convert between different units of length, weight, volume, and temperature quickly and accurately.',
    'canonical' => getCurrentURL()
];

// Include common header
include_once ROOT_PATH . '/templates/layout/header.php';
?>

<!-- Tool page main content -->
<div class="bg-gray-900 text-white">

    <!-- Hero Section with SEO optimized content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-12">
        <div class="text-center mb-12">
            <div class="flex items-center justify-center mb-4">
                <div class="bg-blue-600 p-3 mr-4">
                    <div class="w-10 h-10 text-white text-2xl flex items-center justify-center">⚖️</div>
                </div>
                <div class="text-left">
                    <h1 class="text-4xl font-bold text-white mb-2">Unit Converter</h1>
                    <p class="text-xl text-gray-400">Convert between different units of measurement</p>
                </div>
            </div>
        </div>

        <!-- Tool main content -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Input area -->
            <div class="bg-gray-800 border border-gray-700 p-6">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-xl font-semibold text-white">Input</h2>
                    <div class="flex space-x-2">
                        <button id="clearInput" class="px-3 py-1 bg-red-600 text-white text-sm hover:bg-red-700 transition-colors">
                            <i class="fas fa-times mr-1"></i>Clear
                        </button>
                        <button id="swapUnits" class="px-3 py-1 bg-blue-600 text-white text-sm hover:bg-blue-700 transition-colors">
                            <i class="fas fa-exchange-alt mr-1"></i>Swap
                        </button>
                    </div>
                </div>
                
                <!-- Category Selection -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-300 mb-2">Category</label>
                    <select id="categorySelect" class="w-full bg-gray-700 border border-gray-600 text-white p-3 focus:outline-none focus:border-blue-500">
                        <option value="length">Length</option>
                        <option value="weight">Weight</option>
                        <option value="volume">Volume</option>
                        <option value="temperature">Temperature</option>
                    </select>
                </div>

                <!-- Value Input -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-300 mb-2">Value</label>
                    <input type="number" id="valueInput" 
                           placeholder="Enter value to convert..."
                           class="w-full bg-gray-700 border border-gray-600 text-white p-3 focus:outline-none focus:border-blue-500"
                           step="any">
                    <div id="inputError" class="text-red-400 text-sm mt-1 hidden">Please enter a valid number</div>
                </div>

                <!-- From Unit -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-300 mb-2">From</label>
                    <select id="fromUnit" class="w-full bg-gray-700 border border-gray-600 text-white p-3 focus:outline-none focus:border-blue-500">
                        <!-- Options will be populated by JavaScript -->
                    </select>
                </div>

                <!-- To Unit -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-300 mb-2">To</label>
                    <select id="toUnit" class="w-full bg-gray-700 border border-gray-600 text-white p-3 focus:outline-none focus:border-blue-500">
                        <!-- Options will be populated by JavaScript -->
                    </select>
                </div>
            </div>

            <!-- Output area -->
            <div class="bg-gray-800 border border-gray-700 p-6">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-xl font-semibold text-white">Result</h2>
                    <div class="flex space-x-2">
                        <button id="copyResult" class="px-3 py-1 bg-green-600 text-white text-sm hover:bg-green-700 transition-colors">
                            <i class="fas fa-copy mr-1"></i>Copy
                        </button>
                    </div>
                </div>
                
                <!-- Conversion Result -->
                <div class="bg-gray-700 p-6 mb-4">
                    <div id="conversionResult" class="text-center">
                        <div class="text-3xl font-bold text-blue-400 mb-2" id="resultValue">0</div>
                        <div class="text-gray-300" id="resultUnit">Select units to convert</div>
                        <div class="text-sm text-gray-400 mt-2" id="conversionFormula">Enter a value to see conversion</div>
                    </div>
                </div>

                <!-- Conversion Details -->
                <div class="space-y-3">
                    <div class="bg-gray-700 p-4">
                        <div class="text-sm text-gray-300">Original Value</div>
                        <div class="text-lg font-semibold text-white" id="originalDisplay">-</div>
                    </div>
                    <div class="bg-gray-700 p-4">
                        <div class="text-sm text-gray-300">Converted Value</div>
                        <div class="text-lg font-semibold text-white" id="convertedDisplay">-</div>
                    </div>
                    <div class="bg-gray-700 p-4">
                        <div class="text-sm text-gray-300">Conversion Factor</div>
                        <div class="text-lg font-semibold text-white" id="conversionFactor">-</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- FAQ Section -->
        <div class="px-8 pt-16 pb-16">
            <h2 class="text-3xl font-bold text-white text-center mb-8">Frequently Asked Questions</h2>
            <div class="max-w-4xl mx-auto space-y-6">
                <div class="border-b border-gray-700 pb-6">
                    <h3 class="text-xl font-semibold text-white mb-3">What units can I convert?</h3>
                    <p class="text-gray-300">You can convert between various units of length (meters, feet, inches), weight (kilograms, pounds, ounces), volume (liters, gallons, cups), and temperature (Celsius, Fahrenheit, Kelvin).</p>
                </div>

                <div class="border-b border-gray-700 pb-6">
                    <h3 class="text-xl font-semibold text-white mb-3">How accurate are the conversions?</h3>
                    <p class="text-gray-300">Our conversions use precise mathematical formulas and are accurate to multiple decimal places. The results are suitable for both everyday use and professional applications.</p>
                </div>

                <div class="border-b border-gray-700 pb-6">
                    <h3 class="text-xl font-semibold text-white mb-3">Can I convert negative numbers?</h3>
                    <p class="text-gray-300">Yes, the converter supports both positive and negative numbers, as well as decimal values. This is particularly useful for temperature conversions.</p>
                </div>

                <div class="border-b border-gray-700 pb-6">
                    <h3 class="text-xl font-semibold text-white mb-3">Does the conversion happen automatically?</h3>
                    <p class="text-gray-300">Yes, conversions happen in real-time as you type or change the unit selections. There's no need to click a convert button.</p>
                </div>

                <div class="mb-0">
                    <h3 class="text-xl font-semibold text-white mb-3">What if I enter an invalid value?</h3>
                    <p class="text-gray-300 mb-0">The tool will display an error message if you enter non-numeric values. Simply enter a valid number to continue with the conversion.</p>
                </div>
            </div>
        </div>
    </div>

<script>
// Unit Converter functionality
document.addEventListener('DOMContentLoaded', function() {
    // Initialize DOM elements
    const categorySelect = document.getElementById('categorySelect');
    const valueInput = document.getElementById('valueInput');
    const fromUnit = document.getElementById('fromUnit');
    const toUnit = document.getElementById('toUnit');
    const inputError = document.getElementById('inputError');
    const resultValue = document.getElementById('resultValue');
    const resultUnit = document.getElementById('resultUnit');
    const conversionFormula = document.getElementById('conversionFormula');
    const originalDisplay = document.getElementById('originalDisplay');
    const convertedDisplay = document.getElementById('convertedDisplay');
    const conversionFactor = document.getElementById('conversionFactor');
    const clearBtn = document.getElementById('clearInput');
    const swapBtn = document.getElementById('swapUnits');
    const copyBtn = document.getElementById('copyResult');

    // Unit definitions with conversion factors to base unit
    const units = {
        length: {
            meter: { name: 'Meter (m)', factor: 1 },
            kilometer: { name: 'Kilometer (km)', factor: 1000 },
            centimeter: { name: 'Centimeter (cm)', factor: 0.01 },
            millimeter: { name: 'Millimeter (mm)', factor: 0.001 },
            inch: { name: 'Inch (in)', factor: 0.0254 },
            foot: { name: 'Foot (ft)', factor: 0.3048 },
            yard: { name: 'Yard (yd)', factor: 0.9144 },
            mile: { name: 'Mile (mi)', factor: 1609.344 }
        },
        weight: {
            kilogram: { name: 'Kilogram (kg)', factor: 1 },
            gram: { name: 'Gram (g)', factor: 0.001 },
            pound: { name: 'Pound (lb)', factor: 0.453592 },
            ounce: { name: 'Ounce (oz)', factor: 0.0283495 },
            ton: { name: 'Metric Ton (t)', factor: 1000 },
            stone: { name: 'Stone (st)', factor: 6.35029 }
        },
        volume: {
            liter: { name: 'Liter (L)', factor: 1 },
            milliliter: { name: 'Milliliter (mL)', factor: 0.001 },
            gallon: { name: 'Gallon (gal)', factor: 3.78541 },
            quart: { name: 'Quart (qt)', factor: 0.946353 },
            pint: { name: 'Pint (pt)', factor: 0.473176 },
            cup: { name: 'Cup (cup)', factor: 0.236588 },
            fluid_ounce: { name: 'Fluid Ounce (fl oz)', factor: 0.0295735 }
        },
        temperature: {
            celsius: { name: 'Celsius (°C)' },
            fahrenheit: { name: 'Fahrenheit (°F)' },
            kelvin: { name: 'Kelvin (K)' }
        }
    };

    // Initialize unit options
    function updateUnitOptions() {
        const category = categorySelect.value;
        const categoryUnits = units[category];
        
        fromUnit.innerHTML = '';
        toUnit.innerHTML = '';
        
        Object.keys(categoryUnits).forEach(key => {
            const option1 = new Option(categoryUnits[key].name, key);
            const option2 = new Option(categoryUnits[key].name, key);
            fromUnit.add(option1);
            toUnit.add(option2);
        });
        
        // Set default selections
        if (category === 'length') {
            fromUnit.value = 'meter';
            toUnit.value = 'centimeter';
        } else if (category === 'weight') {
            fromUnit.value = 'kilogram';
            toUnit.value = 'pound';
        } else if (category === 'volume') {
            fromUnit.value = 'liter';
            toUnit.value = 'gallon';
        } else if (category === 'temperature') {
            fromUnit.value = 'celsius';
            toUnit.value = 'fahrenheit';
        }
        
        performConversion();
    }

    // Temperature conversion functions
    function convertTemperature(value, from, to) {
        let celsius;
        
        // Convert to Celsius first
        switch(from) {
            case 'celsius':
                celsius = value;
                break;
            case 'fahrenheit':
                celsius = (value - 32) * 5/9;
                break;
            case 'kelvin':
                celsius = value - 273.15;
                break;
        }
        
        // Convert from Celsius to target
        switch(to) {
            case 'celsius':
                return celsius;
            case 'fahrenheit':
                return celsius * 9/5 + 32;
            case 'kelvin':
                return celsius + 273.15;
        }
    }

    // Main conversion function
    function performConversion() {
        const value = parseFloat(valueInput.value);
        const category = categorySelect.value;
        const from = fromUnit.value;
        const to = toUnit.value;
        
        // Clear error
        inputError.classList.add('hidden');
        
        if (isNaN(value) || valueInput.value === '') {
            resultValue.textContent = '0';
            resultUnit.textContent = 'Enter a value to convert';
            conversionFormula.textContent = 'Enter a value to see conversion';
            originalDisplay.textContent = '-';
            convertedDisplay.textContent = '-';
            conversionFactor.textContent = '-';
            return;
        }
        
        let result;
        let factor = 1;
        
        if (category === 'temperature') {
            result = convertTemperature(value, from, to);
        } else {
            const fromFactor = units[category][from].factor;
            const toFactor = units[category][to].factor;
            factor = fromFactor / toFactor;
            result = value * factor;
        }
        
        // Update display
        resultValue.textContent = result.toFixed(6).replace(/\.?0+$/, '');
        resultUnit.textContent = units[category][to].name;
        
        const fromUnitName = units[category][from].name;
        const toUnitName = units[category][to].name;
        
        conversionFormula.textContent = `${value} ${fromUnitName} = ${result.toFixed(6).replace(/\.?0+$/, '')} ${toUnitName}`;
        originalDisplay.textContent = `${value} ${fromUnitName}`;
        convertedDisplay.textContent = `${result.toFixed(6).replace(/\.?0+$/, '')} ${toUnitName}`;
        
        if (category === 'temperature') {
            conversionFactor.textContent = 'Temperature formula applied';
        } else {
            conversionFactor.textContent = factor.toFixed(8).replace(/\.?0+$/, '');
        }
    }

    // Event listeners
    categorySelect.addEventListener('change', updateUnitOptions);
    valueInput.addEventListener('input', performConversion);
    fromUnit.addEventListener('change', performConversion);
    toUnit.addEventListener('change', performConversion);

    // Clear input
    clearBtn.addEventListener('click', function() {
        valueInput.value = '';
        performConversion();
        valueInput.focus();
    });

    // Swap units
    swapBtn.addEventListener('click', function() {
        const fromValue = fromUnit.value;
        const toValue = toUnit.value;
        fromUnit.value = toValue;
        toUnit.value = fromValue;
        performConversion();
    });

    // Copy result
    copyBtn.addEventListener('click', function() {
        const resultText = conversionFormula.textContent;
        navigator.clipboard.writeText(resultText).then(() => {
            const originalText = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check mr-1"></i>Copied!';
            copyBtn.classList.add('bg-green-700');
            copyBtn.classList.remove('bg-green-600');
            
            setTimeout(() => {
                copyBtn.innerHTML = originalText;
                copyBtn.classList.remove('bg-green-700');
                copyBtn.classList.add('bg-green-600');
            }, 2000);
        }).catch(() => {
            alert('Failed to copy to clipboard');
        });
    });

    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        if (e.ctrlKey && e.key === 'Enter') {
            e.preventDefault();
            performConversion();
        }
        if (e.ctrlKey && e.key === 'c' && e.target.tagName !== 'INPUT' && e.target.tagName !== 'TEXTAREA') {
            e.preventDefault();
            copyBtn.click();
        }
    });

    // Initialize
    updateUnitOptions();
});
</script>

</div>

<?php include_once ROOT_PATH . '/templates/layout/footer.php'; ?>
