<?php
/**
 * 结构化数据模板
 * 为不同类型的页面生成Schema.org标记
 */

/**
 * 生成工具页面的结构化数据
 */
function generateToolStructuredData($tool) {
    return [
        '@context' => 'https://schema.org',
        '@type' => 'SoftwareApplication',
        'name' => $tool['name'],
        'description' => $tool['description'],
        'url' => getCurrentURL(),
        'applicationCategory' => 'DeveloperApplication',
        'operatingSystem' => 'Web Browser',
        'offers' => [
            '@type' => 'Offer',
            'price' => '0',
            'priceCurrency' => 'USD'
        ],
        'publisher' => [
            '@type' => 'Organization',
            'name' => 'Prompt2Tool',
            'url' => 'https://' . ($_SERVER['HTTP_HOST'] ?? 'localhost')
        ],
        'aggregateRating' => [
            '@type' => 'AggregateRating',
            'ratingValue' => $tool['rating'] ?? '4.5',
            'reviewCount' => $tool['review_count'] ?? '100'
        ]
    ];
}

/**
 * 生成工具分类页面的结构化数据
 */
function generateCategoryStructuredData($category, $tools) {
    $toolList = [];
    foreach ($tools as $tool) {
        $toolList[] = [
            '@type' => 'SoftwareApplication',
            'name' => $tool['name'],
            'description' => $tool['description'],
            'url' => $tool['url']
        ];
    }
    
    return [
        '@context' => 'https://schema.org',
        '@type' => 'CollectionPage',
        'name' => $category['name'] . ' Tools - Prompt2Tool',
        'description' => $category['description'],
        'url' => getCurrentURL(),
        'mainEntity' => [
            '@type' => 'ItemList',
            'name' => $category['name'] . ' Tools',
            'description' => 'Collection of ' . strtolower($category['name']) . ' tools',
            'itemListElement' => $toolList
        ],
        'breadcrumb' => [
            '@type' => 'BreadcrumbList',
            'itemListElement' => [
                [
                    '@type' => 'ListItem',
                    'position' => 1,
                    'name' => 'Home',
                    'item' => 'https://' . ($_SERVER['HTTP_HOST'] ?? 'localhost')
                ],
                [
                    '@type' => 'ListItem',
                    'position' => 2,
                    'name' => 'Tools',
                    'item' => 'https://' . ($_SERVER['HTTP_HOST'] ?? 'localhost') . '/tools'
                ],
                [
                    '@type' => 'ListItem',
                    'position' => 3,
                    'name' => $category['name'],
                    'item' => getCurrentURL()
                ]
            ]
        ]
    ];
}

/**
 * 生成组织信息的结构化数据
 */
function generateOrganizationStructuredData() {
    return [
        '@context' => 'https://schema.org',
        '@type' => 'Organization',
        'name' => 'Prompt2Tool',
        'description' => 'Free AI-powered online tools platform for developers, designers, and digital professionals',
        'url' => 'https://' . ($_SERVER['HTTP_HOST'] ?? 'localhost'),
        'logo' => [
            '@type' => 'ImageObject',
            'url' => 'https://' . ($_SERVER['HTTP_HOST'] ?? 'localhost') . '/assets/images/logo.png',
            'width' => 200,
            'height' => 60
        ],
        'sameAs' => [
            'https://github.com/prompt2tool',
            'https://twitter.com/prompt2tool'
        ],
        'contactPoint' => [
            '@type' => 'ContactPoint',
            'contactType' => 'Customer Service',
            'email' => '<EMAIL>',
            'url' => 'https://' . ($_SERVER['HTTP_HOST'] ?? 'localhost') . '/contact'
        ]
    ];
}

/**
 * 生成FAQ页面的结构化数据
 */
function generateFAQStructuredData($faqs) {
    $faqList = [];
    foreach ($faqs as $faq) {
        $faqList[] = [
            '@type' => 'Question',
            'name' => $faq['question'],
            'acceptedAnswer' => [
                '@type' => 'Answer',
                'text' => $faq['answer']
            ]
        ];
    }
    
    return [
        '@context' => 'https://schema.org',
        '@type' => 'FAQPage',
        'mainEntity' => $faqList
    ];
}

/**
 * 生成面包屑导航的结构化数据
 */
function generateBreadcrumbStructuredData($breadcrumbs) {
    $breadcrumbList = [];
    foreach ($breadcrumbs as $index => $crumb) {
        $breadcrumbList[] = [
            '@type' => 'ListItem',
            'position' => $index + 1,
            'name' => $crumb['name'],
            'item' => isset($crumb['url']) ? $crumb['url'] : null
        ];
    }
    
    return [
        '@context' => 'https://schema.org',
        '@type' => 'BreadcrumbList',
        'itemListElement' => $breadcrumbList
    ];
}

/**
 * 生成文章/博客的结构化数据
 */
function generateArticleStructuredData($article) {
    return [
        '@context' => 'https://schema.org',
        '@type' => 'Article',
        'headline' => $article['title'],
        'description' => $article['description'],
        'image' => $article['image'] ?? '/assets/images/default-article.jpg',
        'author' => [
            '@type' => 'Person',
            'name' => $article['author'] ?? 'Prompt2Tool Team'
        ],
        'publisher' => [
            '@type' => 'Organization',
            'name' => 'Prompt2Tool',
            'logo' => [
                '@type' => 'ImageObject',
                'url' => 'https://' . ($_SERVER['HTTP_HOST'] ?? 'localhost') . '/assets/images/logo.png'
            ]
        ],
        'datePublished' => $article['published_date'],
        'dateModified' => $article['modified_date'] ?? $article['published_date'],
        'mainEntityOfPage' => [
            '@type' => 'WebPage',
            '@id' => getCurrentURL()
        ]
    ];
}

/**
 * 生成产品评论的结构化数据
 */
function generateReviewStructuredData($reviews, $product) {
    $reviewList = [];
    $totalRating = 0;
    $reviewCount = count($reviews);
    
    foreach ($reviews as $review) {
        $totalRating += $review['rating'];
        $reviewList[] = [
            '@type' => 'Review',
            'author' => [
                '@type' => 'Person',
                'name' => $review['author']
            ],
            'reviewRating' => [
                '@type' => 'Rating',
                'ratingValue' => $review['rating'],
                'bestRating' => '5'
            ],
            'reviewBody' => $review['content']
        ];
    }
    
    $averageRating = $reviewCount > 0 ? round($totalRating / $reviewCount, 1) : 0;
    
    return [
        '@context' => 'https://schema.org',
        '@type' => 'Product',
        'name' => $product['name'],
        'description' => $product['description'],
        'aggregateRating' => [
            '@type' => 'AggregateRating',
            'ratingValue' => $averageRating,
            'reviewCount' => $reviewCount
        ],
        'review' => $reviewList
    ];
}

/**
 * 输出结构化数据JSON-LD
 */
function outputStructuredData($data) {
    if (empty($data)) return;
    
    echo '<script type="application/ld+json">' . "\n";
    echo json_encode($data, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT);
    echo "\n" . '</script>' . "\n";
}
?>
