<?php
/**
 * 工具删除处理页面
 * 处理工具删除请求
 */

// 定义根目录
define('ROOT_PATH', dirname(dirname(dirname(__DIR__))));

// 加载依赖
require_once dirname(__DIR__) . '/../auth/middleware.php';
require_once dirname(__DIR__) . '/../middleware/PermissionMiddleware.php';
require_once dirname(__DIR__) . '/../models/ToolModel.php';

// 权限检查
requirePermission('tools.delete');

// 初始化模型
$toolModel = new ToolModel();

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('Location: index.php?error=' . urlencode('Invalid request method'));
    exit;
}

// 获取工具ID
$toolId = intval($_POST['id'] ?? 0);
if (!$toolId) {
    header('Location: index.php?error=' . urlencode('Tool ID is required'));
    exit;
}

try {
    // 获取工具信息
    $tool = $toolModel->find($toolId);
    if (!$tool) {
        throw new Exception('Tool not found');
    }
    
    // 检查是否有使用记录
    $usageCount = $toolModel->db->fetchColumn(
        "SELECT COUNT(*) FROM pt_tool_usage WHERE tool_id = :tool_id",
        ['tool_id' => $toolId]
    );
    
    if ($usageCount > 0) {
        // 如果有使用记录，使用软删除
        $result = $toolModel->softDelete($toolId);
        $message = 'Tool has been archived due to existing usage records';
    } else {
        // 如果没有使用记录，可以完全删除
        $result = $toolModel->delete($toolId);
        $message = 'Tool has been permanently deleted';
    }
    
    if ($result) {
        // 记录删除操作
        $currentUser = getCurrentAdmin();
        $logData = [
            'admin_id' => $currentUser['id'],
            'activity_type' => 'tool_delete',
            'description' => "Deleted tool: {$tool['name']} (ID: {$toolId})",
            'target_type' => 'tool',
            'target_id' => $toolId,
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'metadata' => json_encode([
                'tool_name' => $tool['name'],
                'tool_slug' => $tool['slug'],
                'deletion_type' => $usageCount > 0 ? 'soft' : 'hard',
                'usage_count' => $usageCount
            ])
        ];
        
        $toolModel->db->insert('pt_manager_activity_log', $logData);
        
        header('Location: index.php?message=' . urlencode($message));
        exit;
    } else {
        throw new Exception('Failed to delete tool');
    }
    
} catch (Exception $e) {
    header('Location: index.php?error=' . urlencode($e->getMessage()));
    exit;
}
?>
