<?php
/**
 * QR Code Generator Tool Page
 * 遵循01-UI设计规范.md - 零圆角设计
 */

$currentPage = 'tool-qr-generator';
$breadcrumbs = [
    ['name' => 'Home', 'url' => '/'],
    ['name' => 'Tools', 'url' => '/tools'],
    ['name' => 'Utilities', 'url' => '/tools/utilities'],
    ['name' => 'QR Generator']
];

// 包含动态SEO助手
require_once ROOT_PATH . '/app/helpers/dynamic-seo.php';

// 生成SEO数据 - 优化长度
$seoData = [
    'title' => 'Free QR Code Generator Online - Prompt2Tool',
    'description' => 'Generate QR codes for URLs, text, WiFi, contact info and more. Customize colors, add logos, and download high-quality QR codes.',
    'keywords' => 'qr code generator, qr code maker, free qr code, custom qr code, qr code with logo, wifi qr code, contact qr code',
    'og_title' => 'Free QR Code Generator Online - Prompt2Tool',
    'og_description' => 'Generate custom QR codes for URLs, text, WiFi, and contact info. Free, fast, and high-quality.',
    'canonical' => getCurrentURL()
];

// 包含公共头部
include_once ROOT_PATH . '/templates/layout/header.php';
?>

<!-- 工具页面主体 -->
<div class="bg-gray-900 text-white">

    <!-- Hero Section with SEO optimized content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-12">
        <div class="text-center mb-12">
            <div class="flex items-center justify-center mb-4">
                <div class="bg-indigo-600 p-3 mr-4">
                    <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z"></path>
                    </svg>
                </div>
                <div class="text-left">
                    <h1 class="text-4xl font-bold text-white mb-2">QR Code Generator</h1>
                    <p class="text-xl text-gray-400">Create custom QR codes for any purpose</p>
                </div>
            </div>
        </div>

        <!-- 工具主体 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- 输入区域 -->
            <div class="bg-gray-800 border border-gray-700 p-6">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-xl font-semibold text-white">QR Code Content</h2>
                    <div class="flex space-x-2">
                        <button id="clearForm" class="px-3 py-1 bg-red-600 text-white text-sm hover:bg-red-700 transition-colors">
                            Clear
                        </button>
                    </div>
                </div>
                
                <!-- QR码类型选择 -->
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-300 mb-2">QR Code Type</label>
                    <select id="qrType" class="w-full bg-gray-900 border border-gray-600 text-gray-100 p-2 focus:outline-none focus:border-blue-500">
                        <option value="url">Website URL</option>
                        <option value="text">Plain Text</option>
                        <option value="email">Email</option>
                        <option value="phone">Phone Number</option>
                        <option value="sms">SMS</option>
                        <option value="wifi">WiFi Network</option>
                        <option value="vcard">Contact Card</option>
                    </select>
                </div>
                
                <!-- 动态内容区域 -->
                <div id="contentArea">
                    <!-- URL输入 (默认) -->
                    <div id="urlContent">
                        <label class="block text-sm font-medium text-gray-300 mb-2">Website URL</label>
                        <input type="url" id="urlInput" class="w-full bg-gray-900 border border-gray-600 text-gray-100 p-2 focus:outline-none focus:border-blue-500" placeholder="https://www.example.com">
                    </div>
                </div>
                
                <!-- 自定义选项 -->
                <div class="mt-6 space-y-4">
                    <h3 class="text-lg font-semibold text-white">Customization</h3>
                    
                    <!-- 尺寸 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">Size: <span id="sizeValue">200</span>px</label>
                        <input type="range" id="qrSize" min="100" max="500" value="200" class="w-full">
                    </div>
                    
                    <!-- 前景色和背景色 -->
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">Foreground Color</label>
                            <input type="color" id="fgColor" value="#000000" class="w-full h-10 border border-gray-600 bg-gray-900 cursor-pointer">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">Background Color</label>
                            <input type="color" id="bgColor" value="#ffffff" class="w-full h-10 border border-gray-600 bg-gray-900 cursor-pointer">
                        </div>
                    </div>
                    
                    <!-- 错误纠正级别 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">Error Correction</label>
                        <select id="errorLevel" class="w-full bg-gray-900 border border-gray-600 text-gray-100 p-2 focus:outline-none focus:border-blue-500">
                            <option value="L">Low (7%)</option>
                            <option value="M" selected>Medium (15%)</option>
                            <option value="Q">Quartile (25%)</option>
                            <option value="H">High (30%)</option>
                        </select>
                    </div>
                </div>
                
                <!-- 生成按钮 -->
                <button id="generateQR" class="w-full mt-6 bg-indigo-600 text-white py-3 hover:bg-indigo-700 transition-colors">
                    Generate QR Code
                </button>
            </div>

            <!-- 输出区域 -->
            <div class="bg-gray-800 border border-gray-700 p-6">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-xl font-semibold text-white">Generated QR Code</h2>
                    <div class="flex space-x-2">
                        <div class="relative">
                            <button id="downloadQR" class="px-3 py-1 bg-green-600 text-white text-sm hover:bg-green-700 transition-colors" disabled>
                                Download
                            </button>
                            <div id="downloadTooltip" class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-700 text-white text-xs rounded pointer-events-none whitespace-nowrap z-50" style="opacity: 0; visibility: hidden; transition: opacity 0.2s, visibility 0.2s;">
                                Download QR code
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- QR码显示区域 -->
                <div class="text-center">
                    <div id="qrDisplay" class="inline-block p-4 bg-white border border-gray-600 mb-4">
                        <div class="w-48 h-48 bg-gray-200 flex items-center justify-center text-gray-500">
                            QR Code will appear here
                        </div>
                    </div>
                    <canvas id="qrCanvas" class="hidden"></canvas>
                </div>
                
                <!-- QR码信息 -->
                <div id="qrInfo" class="mt-4 text-sm text-gray-400 hidden">
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <span class="text-gray-500">Type:</span>
                            <span id="infoType">-</span>
                        </div>
                        <div>
                            <span class="text-gray-500">Size:</span>
                            <span id="infoSize">-</span>
                        </div>
                        <div>
                            <span class="text-gray-500">Error Level:</span>
                            <span id="infoError">-</span>
                        </div>
                        <div>
                            <span class="text-gray-500">Data Length:</span>
                            <span id="infoLength">-</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 工具信息 -->
        <div class="mt-12 grid grid-cols-1 md:grid-cols-3 gap-8">
            <div class="bg-gray-800 border border-gray-700 p-6">
                <h4 class="text-lg font-semibold text-white mb-3">QR Code Types</h4>
                <ul class="text-gray-300 space-y-2">
                    <li>• Website URLs</li>
                    <li>• Plain text</li>
                    <li>• Email addresses</li>
                    <li>• Phone numbers</li>
                    <li>• SMS messages</li>
                    <li>• WiFi credentials</li>
                    <li>• Contact cards</li>
                </ul>
            </div>
            <div class="bg-gray-800 border border-gray-700 p-6">
                <h4 class="text-lg font-semibold text-white mb-3">Features</h4>
                <ul class="text-gray-300 space-y-2">
                    <li>• Custom colors</li>
                    <li>• Multiple sizes</li>
                    <li>• Error correction levels</li>
                    <li>• High-quality output</li>
                    <li>• Instant generation</li>
                    <li>• No registration required</li>
                </ul>
            </div>
            <div class="bg-gray-800 border border-gray-700 p-6">
                <h4 class="text-lg font-semibold text-white mb-3">Use Cases</h4>
                <ul class="text-gray-300 space-y-2">
                    <li>• Marketing campaigns</li>
                    <li>• Business cards</li>
                    <li>• Event tickets</li>
                    <li>• Restaurant menus</li>
                    <li>• Product packaging</li>
                    <li>• WiFi sharing</li>
                </ul>
            </div>
        </div>

        <!-- FAQ Section -->
        <div class="px-8 pt-16 pb-16">
            <h2 class="text-3xl font-bold text-white text-center mb-8">Frequently Asked Questions</h2>
            <div class="max-w-4xl mx-auto space-y-6">
                <div class="border-b border-gray-700 pb-6">
                    <h3 class="text-xl font-semibold text-white mb-3">What is a QR code?</h3>
                    <p class="text-gray-300">A QR (Quick Response) code is a two-dimensional barcode that can store various types of information like URLs, text, contact details, and more.</p>
                </div>

                <div class="border-b border-gray-700 pb-6">
                    <h3 class="text-xl font-semibold text-white mb-3">How do I scan a QR code?</h3>
                    <p class="text-gray-300">Most smartphones have built-in QR code scanners in their camera apps. Simply point your camera at the QR code and tap the notification that appears.</p>
                </div>

                <div class="border-b border-gray-700 pb-6">
                    <h3 class="text-xl font-semibold text-white mb-3">What's the difference between error correction levels?</h3>
                    <p class="text-gray-300">Higher error correction levels allow the QR code to be read even if partially damaged or obscured, but result in larger, more complex codes.</p>
                </div>

                <div class="border-b border-gray-700 pb-6">
                    <h3 class="text-xl font-semibold text-white mb-3">Can I customize the appearance of my QR code?</h3>
                    <p class="text-gray-300">Yes, you can change the colors, size, and error correction level. However, ensure sufficient contrast between foreground and background for reliable scanning.</p>
                </div>

                <div class="mb-0">
                    <h3 class="text-xl font-semibold text-white mb-3">Are the QR codes generated here permanent?</h3>
                    <p class="text-gray-300 mb-0">Yes, once generated, QR codes work permanently. They contain the data directly, so they don't rely on our servers to function.</p>
                </div>
            </div>
        </div>
    </div>

<!-- 引入QR码生成库 -->
<script src="https://cdn.jsdelivr.net/npm/qrious@4.0.2/dist/qrious.min.js"></script>

<script>
// QR Code Generator functionality
document.addEventListener('DOMContentLoaded', function() {
    const qrType = document.getElementById('qrType');
    const contentArea = document.getElementById('contentArea');
    const qrSize = document.getElementById('qrSize');
    const sizeValue = document.getElementById('sizeValue');
    const fgColor = document.getElementById('fgColor');
    const bgColor = document.getElementById('bgColor');
    const errorLevel = document.getElementById('errorLevel');
    const generateQR = document.getElementById('generateQR');
    const clearForm = document.getElementById('clearForm');
    const qrDisplay = document.getElementById('qrDisplay');
    const qrCanvas = document.getElementById('qrCanvas');
    const downloadQR = document.getElementById('downloadQR');
    const downloadTooltip = document.getElementById('downloadTooltip');
    const qrInfo = document.getElementById('qrInfo');
    
    // 尺寸滑块
    qrSize.addEventListener('input', () => {
        sizeValue.textContent = qrSize.value;
    });

    // QR码类型变化
    qrType.addEventListener('change', updateContentArea);

    // 生成QR码
    generateQR.addEventListener('click', generateQRCode);

    // 颜色和尺寸变化时自动重新生成
    fgColor.addEventListener('change', () => {
        if (!downloadQR.disabled) generateQRCode();
    });
    bgColor.addEventListener('change', () => {
        if (!downloadQR.disabled) generateQRCode();
    });
    errorLevel.addEventListener('change', () => {
        if (!downloadQR.disabled) generateQRCode();
    });
    qrSize.addEventListener('input', () => {
        if (!downloadQR.disabled) generateQRCode();
    });

    // 清除表单
    clearForm.addEventListener('click', () => {
        document.querySelectorAll('input[type="text"], input[type="url"], input[type="email"], input[type="tel"], textarea').forEach(input => {
            input.value = '';
        });
        qrDisplay.innerHTML = '<div class="w-48 h-48 bg-gray-200 flex items-center justify-center text-gray-500">QR Code will appear here</div>';
        downloadQR.disabled = true;
        qrInfo.classList.add('hidden');
    });

    // 下载QR码
    downloadQR.addEventListener('click', () => {
        const link = document.createElement('a');
        link.download = 'qr-code.png';
        link.href = qrCanvas.toDataURL();
        link.click();
        
        if (downloadTooltip) {
            downloadTooltip.textContent = 'Downloaded!';
            downloadTooltip.style.opacity = '1';
            downloadTooltip.style.visibility = 'visible';
            
            setTimeout(() => {
                downloadTooltip.textContent = 'Download QR code';
                downloadTooltip.style.opacity = '0';
                downloadTooltip.style.visibility = 'hidden';
            }, 2000);
        }
    });

    function updateContentArea() {
        const type = qrType.value;
        let html = '';
        
        switch(type) {
            case 'url':
                html = `
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">Website URL</label>
                        <input type="url" id="urlInput" class="w-full bg-gray-900 border border-gray-600 text-gray-100 p-2 focus:outline-none focus:border-blue-500" placeholder="https://www.example.com">
                    </div>
                `;
                break;
            case 'text':
                html = `
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">Text Content</label>
                        <textarea id="textInput" class="w-full h-24 bg-gray-900 border border-gray-600 text-gray-100 p-2 focus:outline-none focus:border-blue-500" placeholder="Enter your text here..."></textarea>
                    </div>
                `;
                break;
            case 'email':
                html = `
                    <div class="space-y-3">
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">Email Address</label>
                            <input type="email" id="emailInput" class="w-full bg-gray-900 border border-gray-600 text-gray-100 p-2 focus:outline-none focus:border-blue-500" placeholder="<EMAIL>">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">Subject (Optional)</label>
                            <input type="text" id="subjectInput" class="w-full bg-gray-900 border border-gray-600 text-gray-100 p-2 focus:outline-none focus:border-blue-500" placeholder="Email subject">
                        </div>
                    </div>
                `;
                break;
            case 'phone':
                html = `
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">Phone Number</label>
                        <input type="tel" id="phoneInput" class="w-full bg-gray-900 border border-gray-600 text-gray-100 p-2 focus:outline-none focus:border-blue-500" placeholder="+1234567890">
                    </div>
                `;
                break;
            case 'sms':
                html = `
                    <div class="space-y-3">
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">Phone Number</label>
                            <input type="tel" id="smsPhoneInput" class="w-full bg-gray-900 border border-gray-600 text-gray-100 p-2 focus:outline-none focus:border-blue-500" placeholder="+1234567890">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">Message</label>
                            <textarea id="smsMessageInput" class="w-full h-20 bg-gray-900 border border-gray-600 text-gray-100 p-2 focus:outline-none focus:border-blue-500" placeholder="SMS message"></textarea>
                        </div>
                    </div>
                `;
                break;
            case 'wifi':
                html = `
                    <div class="space-y-3">
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">Network Name (SSID)</label>
                            <input type="text" id="wifiSSID" class="w-full bg-gray-900 border border-gray-600 text-gray-100 p-2 focus:outline-none focus:border-blue-500" placeholder="WiFi Network Name">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">Password</label>
                            <input type="text" id="wifiPassword" class="w-full bg-gray-900 border border-gray-600 text-gray-100 p-2 focus:outline-none focus:border-blue-500" placeholder="WiFi Password">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">Security Type</label>
                            <select id="wifiSecurity" class="w-full bg-gray-900 border border-gray-600 text-gray-100 p-2 focus:outline-none focus:border-blue-500">
                                <option value="WPA">WPA/WPA2</option>
                                <option value="WEP">WEP</option>
                                <option value="nopass">No Password</option>
                            </select>
                        </div>
                    </div>
                `;
                break;
            case 'vcard':
                html = `
                    <div class="space-y-3">
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">Full Name</label>
                            <input type="text" id="vcardName" class="w-full bg-gray-900 border border-gray-600 text-gray-100 p-2 focus:outline-none focus:border-blue-500" placeholder="John Doe">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">Phone</label>
                            <input type="tel" id="vcardPhone" class="w-full bg-gray-900 border border-gray-600 text-gray-100 p-2 focus:outline-none focus:border-blue-500" placeholder="+1234567890">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">Email</label>
                            <input type="email" id="vcardEmail" class="w-full bg-gray-900 border border-gray-600 text-gray-100 p-2 focus:outline-none focus:border-blue-500" placeholder="<EMAIL>">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">Organization</label>
                            <input type="text" id="vcardOrg" class="w-full bg-gray-900 border border-gray-600 text-gray-100 p-2 focus:outline-none focus:border-blue-500" placeholder="Company Name">
                        </div>
                    </div>
                `;
                break;
        }
        
        contentArea.innerHTML = html;

        // 为新创建的输入元素添加事件监听器
        setTimeout(() => {
            const inputs = contentArea.querySelectorAll('input, textarea, select');
            inputs.forEach(input => {
                input.addEventListener('input', () => {
                    // 延迟生成，避免频繁调用
                    clearTimeout(window.qrGenerateTimeout);
                    window.qrGenerateTimeout = setTimeout(() => {
                        const currentData = getCurrentData();
                        if (currentData.trim()) {
                            generateQRCode();
                        }
                    }, 500);
                });
            });
        }, 100);
    }

    function getCurrentData() {
        const type = qrType.value;
        let data = '';


        return data;
    }

    function generateQRCode() {
        const type = qrType.value;
        let data = getCurrentData();
        switch(type) {
            case 'url':
                data = document.getElementById('urlInput')?.value || '';
                break;
            case 'text':
                data = document.getElementById('textInput')?.value || '';
                break;
            case 'email':
                const emailAddr = document.getElementById('emailInput')?.value || '';
                const subject = document.getElementById('subjectInput')?.value || '';
                data = `mailto:${emailAddr}${subject ? '?subject=' + encodeURIComponent(subject) : ''}`;
                break;
            case 'phone':
                data = `tel:${document.getElementById('phoneInput')?.value || ''}`;
                break;
            case 'sms':
                const smsPhone = document.getElementById('smsPhoneInput')?.value || '';
                const smsMessage = document.getElementById('smsMessageInput')?.value || '';
                data = `sms:${smsPhone}${smsMessage ? '?body=' + encodeURIComponent(smsMessage) : ''}`;
                break;
            case 'wifi':
                const ssid = document.getElementById('wifiSSID')?.value || '';
                const password = document.getElementById('wifiPassword')?.value || '';
                const security = document.getElementById('wifiSecurity')?.value || 'WPA';
                data = `WIFI:T:${security};S:${ssid};P:${password};;`;
                break;
            case 'vcard':
                const name = document.getElementById('vcardName')?.value || '';
                const phone = document.getElementById('vcardPhone')?.value || '';
                const vcardEmail = document.getElementById('vcardEmail')?.value || '';
                const org = document.getElementById('vcardOrg')?.value || '';
                data = `BEGIN:VCARD\nVERSION:3.0\nFN:${name}\nTEL:${phone}\nEMAIL:${vcardEmail}\nORG:${org}\nEND:VCARD`;
                break;
        }
        
        if (!data.trim()) {
            return;
        }

        // 检查QRious库是否可用
        if (checkQRiousLoaded()) {
            try {
                // 使用QRious生成真实的QR码
                const canvas = qrCanvas;
                const size = parseInt(qrSize.value);

                // 创建QRious实例
                const qr = new QRious({
                    element: canvas,
                    value: data,
                    size: size,
                    foreground: fgColor.value,
                    background: bgColor.value,
                    level: errorLevel.value
                });

                // 更新显示
                qrDisplay.innerHTML = '';
                const img = new Image();
                img.src = canvas.toDataURL();
                img.style.width = '200px';
                img.style.height = '200px';
                qrDisplay.appendChild(img);

                // 启用下载按钮
                downloadQR.disabled = false;

            } catch (error) {
                console.error('QRious generation failed, using fallback:', error);
                generateQRCodeFallback(data);
            }
        } else {
            // 使用备用方案
            console.log('QRious not loaded, using fallback API');
            generateQRCodeFallback(data);
        }

        // 显示信息
        document.getElementById('infoType').textContent = type.toUpperCase();
        document.getElementById('infoSize').textContent = parseInt(qrSize.value) + 'px';
        document.getElementById('infoError').textContent = errorLevel.value;
        document.getElementById('infoLength').textContent = data.length + ' chars';
        qrInfo.classList.remove('hidden');
    }

    // 检查QRious库是否加载
    function checkQRiousLoaded() {
        return typeof QRious !== 'undefined';
    }

    // 备用QR码生成方法（使用在线API）
    function generateQRCodeFallback(data) {
        const size = parseInt(qrSize.value);
        const fgColorHex = fgColor.value.replace('#', '');
        const bgColorHex = bgColor.value.replace('#', '');

        // 使用QR Server API作为备用方案
        const qrUrl = `https://api.qrserver.com/v1/create-qr-code/?size=${size}x${size}&data=${encodeURIComponent(data)}&color=${fgColorHex}&bgcolor=${bgColorHex}`;

        qrDisplay.innerHTML = '';
        const img = new Image();
        img.crossOrigin = 'anonymous';
        img.onload = function() {
            // 将图片绘制到canvas上以支持下载
            const canvas = qrCanvas;
            canvas.width = size;
            canvas.height = size;
            const ctx = canvas.getContext('2d');
            ctx.drawImage(img, 0, 0, size, size);

            // 显示图片
            const displayImg = new Image();
            displayImg.src = canvas.toDataURL();
            displayImg.style.width = '200px';
            displayImg.style.height = '200px';
            qrDisplay.appendChild(displayImg);

            // 启用下载按钮
            downloadQR.disabled = false;
        };
        img.onerror = function() {
            qrDisplay.innerHTML = '<div class="w-48 h-48 bg-red-100 flex items-center justify-center text-red-500 text-sm">QR Code generation failed</div>';
        };
        img.src = qrUrl;
    }

    // 初始化
    updateContentArea();

    // 等待库加载后生成默认示例QR码
    setTimeout(() => {
        const urlInput = document.getElementById('urlInput');
        if (urlInput) {
            urlInput.value = 'https://www.google.com';
            generateQRCode();
        }
    }, 1000);
});
</script>

</div>

<?php include_once ROOT_PATH . '/templates/layout/footer.php'; ?>
