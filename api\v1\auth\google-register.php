<?php
/**
 * Google OAuth Register API
 * 处理Google Identity Services的JWT token注册
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// 使用统一的数据库连接
require_once dirname(dirname(dirname(__DIR__))) . '/includes/database-connection.php';
require_once __DIR__ . '/google-jwt-helper.php';

try {
    // 获取请求数据
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($input['credential'])) {
        throw new Exception('Missing Google credential');
    }
    
    $credential = $input['credential'];
    
    // 获取Google OAuth配置
    $stmt = $pdo->prepare("SELECT setting_value FROM pt_system_config WHERE setting_key = ?");
    $stmt->execute(['google_oauth_client_id']);
    $clientId = $stmt->fetchColumn();
    
    if (!$clientId) {
        throw new Exception('Google OAuth not configured');
    }
    
    // 验证JWT token
    $userInfo = GoogleJWTHelper::verifyGoogleJWT($credential, $clientId);
    
    if (!$userInfo) {
        throw new Exception('Invalid Google credential');
    }
    
    // 检查用户是否已存在
    $stmt = $pdo->prepare("SELECT id FROM pt_member WHERE email = ?");
    $stmt->execute([$userInfo['email']]);
    $existingUser = $stmt->fetch();
    
    if ($existingUser) {
        // 用户已存在，直接登录
        $stmt = $pdo->prepare("UPDATE pt_member SET last_login = NOW() WHERE id = ?");
        $stmt->execute([$existingUser['id']]);
        
        // 创建会话
        session_start();
        $_SESSION['user_id'] = $existingUser['id'];
        $_SESSION['user_email'] = $userInfo['email'];
        
        echo json_encode([
            'success' => true,
            'message' => 'Account already exists. Logged in successfully.',
            'redirect' => '/'
        ]);
    } else {
        // 创建新用户
        $firstName = $userInfo['given_name'] ?? '';
        $lastName = $userInfo['family_name'] ?? '';
        $avatar = $userInfo['picture'] ?? null;
        
        // 生成唯一用户名
        $username = generateUniqueUsername($userInfo['email'], $pdo);
        
        $stmt = $pdo->prepare("
            INSERT INTO pt_member (
                username, email, password, first_name, last_name, avatar,
                status, email_verified, created_at, last_login
            ) VALUES (?, ?, '', ?, ?, ?, 'active', 1, NOW(), NOW())
        ");
        
        $stmt->execute([
            $username,
            $userInfo['email'],
            $firstName,
            $lastName,
            $avatar
        ]);
        
        $userId = $pdo->lastInsertId();
        
        // 创建会话
        session_start();
        $_SESSION['user_id'] = $userId;
        $_SESSION['user_email'] = $userInfo['email'];
        $_SESSION['user_name'] = trim($firstName . ' ' . $lastName);
        
        echo json_encode([
            'success' => true,
            'message' => 'Account created successfully!',
            'redirect' => '/',
            'user' => [
                'id' => $userId,
                'email' => $userInfo['email'],
                'name' => trim($firstName . ' ' . $lastName)
            ]
        ]);
    }
    
} catch (Exception $e) {
    error_log("Google register error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}



/**
 * 生成唯一用户名
 */
function generateUniqueUsername($email, $pdo) {
    $baseUsername = strtolower(explode('@', $email)[0]);
    $baseUsername = preg_replace('/[^a-z0-9_]/', '', $baseUsername);
    
    $username = $baseUsername;
    $counter = 1;
    
    while (true) {
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM pt_member WHERE username = ?");
        $stmt->execute([$username]);
        
        if ($stmt->fetchColumn() == 0) {
            return $username;
        }
        
        $username = $baseUsername . '_' . $counter;
        $counter++;
        
        if ($counter > 1000) {
            $username = $baseUsername . '_' . uniqid();
            break;
        }
    }
    
    return $username;
}
?>
