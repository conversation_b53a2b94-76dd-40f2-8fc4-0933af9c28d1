<?php
/**
 * Upload Open Graph Image
 */

header('Content-Type: application/json');

try {
    // Check if file was uploaded
    if (!isset($_FILES['og_image']) || $_FILES['og_image']['error'] !== UPLOAD_ERR_OK) {
        throw new Exception('No file uploaded or upload error occurred.');
    }
    
    $file = $_FILES['og_image'];
    
    // Validate file type
    $allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];
    if (!in_array($file['type'], $allowedTypes)) {
        throw new Exception('Invalid file type. Only JPEG and PNG images are allowed.');
    }
    
    // Validate file size (max 5MB)
    if ($file['size'] > 5 * 1024 * 1024) {
        throw new Exception('File size too large. Maximum size is 5MB.');
    }
    
    // Create upload directory if it doesn't exist
    $uploadDir = __DIR__ . '/../../assets/images/uploads/';
    if (!is_dir($uploadDir)) {
        if (!mkdir($uploadDir, 0755, true)) {
            throw new Exception('Failed to create upload directory.');
        }
    }
    
    // Use fixed filename (will overwrite previous image)
    $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    $filename = 'og-image.' . $extension;
    $uploadPath = $uploadDir . $filename;
    $webPath = '/assets/images/uploads/' . $filename;

    // Remove old og-image files with different extensions
    $oldFiles = glob($uploadDir . 'og-image.*');
    foreach ($oldFiles as $oldFile) {
        if (file_exists($oldFile)) {
            unlink($oldFile);
        }
    }
    
    // Move uploaded file
    if (!move_uploaded_file($file['tmp_name'], $uploadPath)) {
        throw new Exception('Failed to move uploaded file.');
    }
    
    // Validate image and get dimensions
    $imageInfo = getimagesize($uploadPath);
    if ($imageInfo === false) {
        unlink($uploadPath); // Delete invalid file
        throw new Exception('Invalid image file.');
    }
    
    $width = $imageInfo[0];
    $height = $imageInfo[1];
    
    // Optional: Resize image if needed (for OG images, 1200x630 is recommended)
    if ($width > 1200 || $height > 630) {
        $resized = resizeImage($uploadPath, $uploadPath, 1200, 630);
        if (!$resized) {
            error_log('Failed to resize image, but keeping original');
        }
    }
    
    echo json_encode([
        'success' => true,
        'path' => $webPath,
        'filename' => $filename,
        'size' => filesize($uploadPath),
        'dimensions' => [
            'width' => $width,
            'height' => $height
        ]
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

/**
 * Resize image to fit within specified dimensions
 */
function resizeImage($sourcePath, $destPath, $maxWidth, $maxHeight) {
    try {
        $imageInfo = getimagesize($sourcePath);
        if (!$imageInfo) return false;
        
        $sourceWidth = $imageInfo[0];
        $sourceHeight = $imageInfo[1];
        $mimeType = $imageInfo['mime'];
        
        // Calculate new dimensions
        $ratio = min($maxWidth / $sourceWidth, $maxHeight / $sourceHeight);
        $newWidth = round($sourceWidth * $ratio);
        $newHeight = round($sourceHeight * $ratio);
        
        // Create source image
        switch ($mimeType) {
            case 'image/jpeg':
                $sourceImage = imagecreatefromjpeg($sourcePath);
                break;
            case 'image/png':
                $sourceImage = imagecreatefrompng($sourcePath);
                break;
            default:
                return false;
        }
        
        if (!$sourceImage) return false;
        
        // Create destination image
        $destImage = imagecreatetruecolor($newWidth, $newHeight);
        
        // Preserve transparency for PNG
        if ($mimeType === 'image/png') {
            imagealphablending($destImage, false);
            imagesavealpha($destImage, true);
            $transparent = imagecolorallocatealpha($destImage, 255, 255, 255, 127);
            imagefilledrectangle($destImage, 0, 0, $newWidth, $newHeight, $transparent);
        }
        
        // Resize image
        imagecopyresampled(
            $destImage, $sourceImage,
            0, 0, 0, 0,
            $newWidth, $newHeight,
            $sourceWidth, $sourceHeight
        );
        
        // Save resized image
        $result = false;
        switch ($mimeType) {
            case 'image/jpeg':
                $result = imagejpeg($destImage, $destPath, 90);
                break;
            case 'image/png':
                $result = imagepng($destImage, $destPath, 9);
                break;
        }
        
        // Clean up
        imagedestroy($sourceImage);
        imagedestroy($destImage);
        
        return $result;
        
    } catch (Exception $e) {
        error_log('Image resize error: ' . $e->getMessage());
        return false;
    }
}
?>
