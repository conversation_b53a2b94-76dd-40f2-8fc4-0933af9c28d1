<?php
/**
 * API入口文件
 * 处理所有API请求的路由和分发
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 0);

// 定义根目录
define('ROOT_PATH', dirname(__DIR__));
define('API_PATH', __DIR__);

// 设置时区
date_default_timezone_set('UTC');

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 使用统一的数据库连接
require_once ROOT_PATH . '/includes/database-connection.php';
require_once API_PATH . '/classes/ApiRouter.php';
require_once API_PATH . '/classes/ApiResponse.php';
require_once API_PATH . '/classes/ApiAuth.php';
require_once API_PATH . '/classes/ApiRateLimit.php';
require_once API_PATH . '/middleware/ApiMiddleware.php';

// 加载控制器
require_once API_PATH . '/controllers/AuthController.php';
require_once API_PATH . '/controllers/ToolController.php';
require_once API_PATH . '/controllers/ToolAnalysisController.php';

try {
    // 初始化路由器
    $router = new ApiRouter();
    
    // 注册中间件
    $router->addMiddleware(new ApiMiddleware());
    
    // 注册API路由
    
    // 认证相关
    $router->post('/auth/login', 'AuthController@login');
    $router->post('/auth/register', 'AuthController@register');
    $router->post('/auth/google-login', 'AuthController@googleLogin');
    $router->post('/auth/google-register', 'AuthController@googleRegister');
    $router->post('/auth/refresh', 'AuthController@refresh');
    $router->post('/auth/logout', 'AuthController@logout');
    $router->get('/auth/me', 'AuthController@me');
    
    // 工具相关
    $router->get('/tools', 'ToolController@index');
    $router->get('/tools/{id}', 'ToolController@show');
    $router->post('/tools', 'ToolController@store');
    $router->put('/tools/{id}', 'ToolController@update');
    $router->delete('/tools/{id}', 'ToolController@destroy');
    $router->get('/tools/{id}/usage', 'ToolController@usage');
    $router->post('/tools/{id}/execute', 'ToolController@execute');
    $router->post('/tools/{id}/rate', 'ToolController@rate');
    
    // 分类相关
    $router->get('/categories', 'CategoryController@index');
    $router->get('/categories/{id}', 'CategoryController@show');
    $router->get('/categories/{id}/tools', 'CategoryController@tools');
    
    // 用户相关
    $router->get('/users', 'UserController@index');
    $router->get('/users/{id}', 'UserController@show');
    $router->put('/users/{id}', 'UserController@update');
    $router->delete('/users/{id}', 'UserController@destroy');
    $router->get('/users/{id}/tools', 'UserController@tools');
    $router->get('/users/{id}/usage', 'UserController@usage');
    
    // 搜索相关
    $router->get('/search', 'SearchController@index');
    $router->get('/search/tools', 'SearchController@tools');
    $router->get('/search/suggestions', 'SearchController@suggestions');
    
    // 统计相关
    $router->get('/stats/overview', 'StatsController@overview');
    $router->get('/stats/tools', 'StatsController@tools');
    $router->get('/stats/users', 'StatsController@users');
    $router->get('/stats/analytics', 'StatsController@analytics');
    
    // 文件上传
    $router->post('/upload', 'UploadController@store');
    $router->delete('/upload/{id}', 'UploadController@destroy');

    // 工具文件分析
    $router->post('/analyze-tool-file', 'ToolAnalysisController@analyze');
    
    // 系统信息
    $router->get('/system/info', 'SystemController@info');
    $router->get('/system/health', 'SystemController@health');
    $router->get('/system/version', 'SystemController@version');
    
    // 管理员API（需要管理员权限）
    $router->group('/admin', function($router) {
        // 管理员管理
        $router->get('/admins', 'AdminController@index');
        $router->post('/admins', 'AdminController@store');
        $router->get('/admins/{id}', 'AdminController@show');
        $router->put('/admins/{id}', 'AdminController@update');
        $router->delete('/admins/{id}', 'AdminController@destroy');
        
        // 系统设置
        $router->get('/settings', 'SettingsController@index');
        $router->put('/settings', 'SettingsController@update');
        $router->get('/settings/{group}', 'SettingsController@group');
        
        // 日志管理
        $router->get('/logs', 'LogController@index');
        $router->get('/logs/{type}', 'LogController@type');
        $router->delete('/logs', 'LogController@clear');
        
        // 权限管理
        $router->get('/permissions', 'PermissionController@index');
        $router->get('/roles', 'RoleController@index');
        $router->post('/roles', 'RoleController@store');
        $router->put('/roles/{id}', 'RoleController@update');
        $router->delete('/roles/{id}', 'RoleController@destroy');
        
        // 系统维护
        $router->post('/maintenance/enable', 'MaintenanceController@enable');
        $router->post('/maintenance/disable', 'MaintenanceController@disable');
        $router->post('/cache/clear', 'CacheController@clear');
        $router->post('/database/optimize', 'DatabaseController@optimize');
    });
    
    // 处理请求
    $router->dispatch();
    
} catch (Exception $e) {
    // 记录错误
    error_log('API Error: ' . $e->getMessage());
    
    // 返回错误响应
    ApiResponse::error([
        'message' => 'Internal server error',
        'error_code' => 'INTERNAL_ERROR'
    ], 500);
}
?>
