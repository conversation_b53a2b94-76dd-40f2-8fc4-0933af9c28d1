-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.2
-- https://www.phpmyadmin.net/
--
-- Host: localhost:3306
-- Generation Time: Aug 22, 2025 at 02:23 AM
-- Server version: 8.4.3
-- PHP Version: 8.3.16

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `prompt2tool`
--

DELIMITER $$
--
-- Procedures
--
CREATE DEFINER=`root`@`localhost` PROCEDURE `ResetMonthlyAPIQuota` ()   BEGIN
    -- 重置pt_member表中的api_used字段
    UPDATE pt_member 
    SET api_used = 0, 
        api_reset_date = CURDATE()
    WHERE api_reset_date IS NULL 
       OR api_reset_date < DATE_SUB(CURDATE(), INTERVAL 1 MONTH);
    
    -- 清理pt_api_usage表中需要重置的用户的历史记录
    DELETE FROM pt_api_usage 
    WHERE user_id IN (
        SELECT id FROM pt_member 
        WHERE api_reset_date = CURDATE()
    );
END$$

DELIMITER ;

-- --------------------------------------------------------

--
-- Table structure for table `pt_activity_log`
--

CREATE TABLE `pt_activity_log` (
  `id` int NOT NULL,
  `manager_id` int NOT NULL COMMENT '管理员ID，关联pt_manager表',
  `action` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '操作类型，如login、update_settings等',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '操作描述详情',
  `icon` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'fas fa-info-circle' COMMENT '图标类名，用于前端显示',
  `color` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'blue' COMMENT '颜色标识，用于前端显示',
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '操作者IP地址，支持IPv6',
  `user_agent` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '浏览器用户代理信息',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='活动日志表';

--
-- Dumping data for table `pt_activity_log`
--

INSERT INTO `pt_activity_log` (`id`, `manager_id`, `action`, `description`, `icon`, `color`, `ip_address`, `user_agent`, `created_at`) VALUES
(73, 2, 'database_cleanup', 'Database cleanup - Action: all, Deleted: 3 records', 'fas fa-info-circle', 'blue', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-20 15:14:44'),
(74, 2, 'login', 'Logged in to admin panel', 'fas fa-sign-in-alt', 'green', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 01:53:23'),
(75, 2, 'delete_launch', 'Deleted launch \'Hostinger Horizons\' (ID: 123, User ID: 112)', 'fas fa-info-circle', 'blue', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 05:36:29'),
(76, 2, 'login', 'Logged in to admin panel', 'fas fa-sign-in-alt', 'green', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 07:39:26'),
(77, 2, 'login', 'Logged in to admin panel', 'fas fa-sign-in-alt', 'green', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 09:10:46'),
(78, 2, 'login', 'Logged in to admin panel', 'fas fa-sign-in-alt', 'green', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 09:46:57');

-- --------------------------------------------------------

--
-- Table structure for table `pt_analytics_event`
--

CREATE TABLE `pt_analytics_event` (
  `id` int NOT NULL,
  `event_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '事件类型',
  `event_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '事件名称',
  `user_id` int DEFAULT NULL COMMENT '用户ID，关联pt_member表',
  `session_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '会话ID',
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'IP地址',
  `user_agent` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '用户代理',
  `referer` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '来源页面',
  `url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '当前页面URL',
  `properties` json DEFAULT NULL COMMENT '事件属性（JSON格式）',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分析事件表';

-- --------------------------------------------------------

--
-- Table structure for table `pt_api_access_logs`
--

CREATE TABLE `pt_api_access_logs` (
  `id` int NOT NULL,
  `user_id` int DEFAULT NULL COMMENT '用户ID，关联pt_member表',
  `endpoint` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'API端点',
  `method` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'HTTP方法',
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'IP地址',
  `user_agent` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '用户代理',
  `response_code` int DEFAULT NULL COMMENT '响应状态码',
  `response_time` decimal(10,3) DEFAULT NULL COMMENT '响应时间（毫秒）',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='API访问日志表';

-- --------------------------------------------------------

--
-- Table structure for table `pt_api_rate_limits`
--

CREATE TABLE `pt_api_rate_limits` (
  `id` int NOT NULL,
  `cache_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '缓存键名',
  `request_time` int NOT NULL COMMENT '请求时间戳',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='API速率限制记录表';

-- --------------------------------------------------------

--
-- Table structure for table `pt_api_rate_limit_whitelist`
--

CREATE TABLE `pt_api_rate_limit_whitelist` (
  `id` int NOT NULL,
  `identifier` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '标识符（IP地址或用户ID）',
  `type` enum('ip','user') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'ip' COMMENT '白名单类型：ip-IP地址，user-用户',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='API速率限制白名单表';

-- --------------------------------------------------------

--
-- Table structure for table `pt_api_usage`
--

CREATE TABLE `pt_api_usage` (
  `id` int NOT NULL,
  `user_id` int NOT NULL COMMENT '用户ID，关联pt_member表',
  `action` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '操作类型：chat_interaction, tool_generation, submit_launch, submit_request',
  `quota_consumed` int NOT NULL DEFAULT '0' COMMENT '消耗的配额数量',
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'IP地址',
  `user_agent` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '用户代理',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='API使用记录表';

--
-- Dumping data for table `pt_api_usage`
--

INSERT INTO `pt_api_usage` (`id`, `user_id`, `action`, `quota_consumed`, `ip_address`, `user_agent`, `created_at`) VALUES
(57, 15, 'chat_interaction', 20, '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '2025-08-20 15:25:12'),
(59, 35, 'chat_interaction', 20, '*************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36', '2025-08-20 15:25:12'),
(60, 36, 'submit_launch', 100, '*************', 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36', '2025-08-20 15:25:12'),
(61, 2, 'submit_request', -50, '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '2025-08-20 15:25:12'),
(62, 3, 'chat_interaction', 20, '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '2025-08-20 15:25:12'),
(63, 6, 'tool_generation', 100, '*************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36', '2025-08-20 15:25:12'),
(64, 15, 'tool_generation', 100, '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '2025-08-19 15:25:12'),
(66, 35, 'submit_launch', 100, '*************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36', '2025-08-19 15:25:12'),
(67, 2, 'chat_interaction', 20, '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '2025-08-19 15:25:12'),
(68, 3, 'tool_generation', 100, '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '2025-08-19 15:25:12'),
(69, 6, 'chat_interaction', 20, '*************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36', '2025-08-19 15:25:12'),
(70, 16, 'submit_request', -50, '*************', 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36', '2025-08-19 15:25:12'),
(71, 15, 'chat_interaction', 20, '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '2025-08-18 15:25:12'),
(73, 35, 'tool_generation', 100, '*************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36', '2025-08-18 15:25:12'),
(74, 2, 'submit_request', -50, '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '2025-08-18 15:25:12'),
(75, 3, 'chat_interaction', 20, '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '2025-08-18 15:25:12'),
(76, 6, 'tool_generation', 100, '*************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36', '2025-08-18 15:25:12'),
(77, 17, 'chat_interaction', 20, '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '2025-08-18 15:25:12'),
(78, 18, 'submit_launch', 100, '*************', 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36', '2025-08-18 15:25:12'),
(79, 15, 'tool_generation', 100, '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '2025-08-17 15:25:12'),
(81, 2, 'tool_generation', 100, '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '2025-08-17 15:25:12'),
(82, 3, 'submit_request', -50, '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '2025-08-17 15:25:12'),
(83, 19, 'chat_interaction', 20, '192.168.1.110', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36', '2025-08-17 15:25:12'),
(84, 20, 'submit_launch', 100, '192.168.1.111', 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36', '2025-08-17 15:25:12'),
(85, 15, 'chat_interaction', 20, '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '2025-08-16 15:25:12'),
(87, 2, 'chat_interaction', 20, '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '2025-08-16 15:25:12'),
(88, 21, 'submit_launch', 100, '192.168.1.112', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '2025-08-16 15:25:12'),
(89, 22, 'tool_generation', 100, '192.168.1.113', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36', '2025-08-16 15:25:12'),
(90, 23, 'chat_interaction', 20, '192.168.1.114', 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36', '2025-08-16 15:25:12'),
(91, 15, 'submit_request', -50, '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '2025-08-15 15:25:12'),
(93, 24, 'tool_generation', 100, '192.168.1.115', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '2025-08-15 15:25:12'),
(94, 25, 'submit_launch', 100, '192.168.1.116', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36', '2025-08-15 15:25:12'),
(95, 26, 'chat_interaction', 20, '192.168.1.117', 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36', '2025-08-15 15:25:12'),
(96, 15, 'tool_generation', 100, '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '2025-08-14 15:25:12'),
(97, 27, 'chat_interaction', 20, '192.168.1.118', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '2025-08-14 15:25:12'),
(98, 28, 'submit_launch', 100, '192.168.1.119', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36', '2025-08-14 15:25:12'),
(99, 29, 'tool_generation', 100, '192.168.1.120', 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36', '2025-08-14 15:25:12'),
(100, 30, 'chat_interaction', 20, '192.168.1.121', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '2025-08-14 15:25:12'),
(101, 15, 'chat_interaction', 20, '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '2025-08-13 15:25:12'),
(102, 31, 'tool_generation', 100, '192.168.1.122', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '2025-08-13 15:25:12'),
(103, 32, 'submit_launch', 100, '192.168.1.123', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36', '2025-08-13 15:25:12'),
(104, 33, 'chat_interaction', 20, '192.168.1.124', 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36', '2025-08-13 15:25:12'),
(105, 2, 'submit_request', -50, '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '2025-08-13 15:25:12'),
(106, 2, 'chat_interaction', 20, '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '2025-08-20 15:26:36'),
(107, 3, 'tool_generation', 100, '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '2025-08-20 15:26:36'),
(108, 4, 'chat_interaction', 20, '*************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36', '2025-08-20 15:26:36'),
(109, 5, 'submit_launch', 100, '*************', 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36', '2025-08-20 15:26:36'),
(110, 6, 'submit_request', 50, '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '2025-08-20 15:26:36'),
(111, 15, 'chat_interaction', 20, '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '2025-08-20 15:26:36'),
(112, 16, 'tool_generation', 100, '*************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36', '2025-08-20 15:26:36'),
(113, 2, 'tool_generation', 100, '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '2025-08-19 15:26:36'),
(114, 3, 'chat_interaction', 20, '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '2025-08-19 15:26:36'),
(115, 5, 'chat_interaction', 20, '*************', 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36', '2025-08-19 15:26:36'),
(116, 6, 'submit_launch', 100, '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '2025-08-19 15:26:36'),
(117, 15, 'tool_generation', 100, '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '2025-08-19 15:26:36'),
(118, 17, 'chat_interaction', 20, '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '2025-08-19 15:26:36'),
(119, 2, 'chat_interaction', 20, '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '2025-08-18 15:26:36'),
(120, 4, 'tool_generation', 100, '*************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36', '2025-08-18 15:26:36'),
(121, 6, 'chat_interaction', 20, '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '2025-08-18 15:26:36'),
(122, 18, 'submit_launch', 100, '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '2025-08-18 15:26:36'),
(123, 19, 'chat_interaction', 20, '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '2025-08-18 15:26:36'),
(124, 3, 'submit_launch', 100, '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '2025-08-17 15:26:36'),
(125, 5, 'tool_generation', 100, '*************', 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36', '2025-08-17 15:26:36'),
(126, 7, 'chat_interaction', 20, '192.168.1.110', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '2025-08-17 15:26:36'),
(127, 20, 'submit_request', 50, '192.168.1.111', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '2025-08-17 15:26:36'),
(128, 2, 'submit_launch', 100, '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '2025-08-16 15:26:36'),
(129, 6, 'tool_generation', 100, '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '2025-08-16 15:26:36'),
(130, 15, 'chat_interaction', 20, '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '2025-08-16 15:26:36'),
(131, 21, 'chat_interaction', 20, '192.168.1.112', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '2025-08-16 15:26:36'),
(132, 3, 'chat_interaction', 20, '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '2025-08-15 15:26:36'),
(133, 4, 'submit_launch', 100, '*************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36', '2025-08-15 15:26:36'),
(134, 8, 'tool_generation', 100, '192.168.1.113', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '2025-08-15 15:26:36'),
(135, 22, 'chat_interaction', 20, '192.168.1.114', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '2025-08-15 15:26:36'),
(136, 5, 'submit_request', 50, '*************', 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36', '2025-08-14 15:26:36'),
(137, 7, 'tool_generation', 100, '192.168.1.110', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '2025-08-14 15:26:36'),
(138, 16, 'chat_interaction', 20, '*************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36', '2025-08-14 15:26:36'),
(139, 17, 'submit_launch', 100, '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '2025-08-14 15:26:36'),
(142, 34, 'chat_interaction', 20, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-20 16:06:56'),
(143, 34, 'submit_launch', 100, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-20 16:21:56'),
(191, 112, 'chat_interaction', 20, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 06:27:48'),
(192, 112, 'chat_interaction', 20, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 06:28:21'),
(193, 112, 'chat_interaction', 20, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 06:29:07'),
(194, 112, 'chat_interaction', 20, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 06:29:23'),
(195, 112, 'chat_interaction', 20, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 06:29:43'),
(196, 112, 'chat_interaction', 20, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 06:30:01'),
(197, 112, 'chat_interaction', 20, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 06:30:18'),
(198, 112, 'chat_interaction', 20, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 06:30:35'),
(199, 112, 'chat_interaction', 20, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 06:30:52'),
(200, 112, 'chat_interaction', 20, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 06:31:05'),
(201, 112, 'chat_interaction', 20, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 06:31:18'),
(202, 112, 'chat_interaction', 20, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 06:31:30'),
(203, 112, 'chat_interaction', 20, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 06:31:43'),
(204, 112, 'chat_interaction', 20, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 06:31:57'),
(205, 112, 'chat_interaction', 20, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 06:32:12'),
(206, 112, 'chat_interaction', 20, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 06:32:25'),
(207, 112, 'chat_interaction', 20, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 06:32:37'),
(208, 112, 'chat_interaction', 20, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 06:32:48'),
(209, 112, 'chat_interaction', 20, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 06:44:14'),
(210, 112, 'chat_interaction', 20, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 06:44:43'),
(211, 112, 'chat_interaction', 20, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 06:45:14'),
(212, 112, 'chat_interaction', 20, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 06:45:45'),
(213, 112, 'chat_interaction', 20, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 06:46:08'),
(214, 112, 'chat_interaction', 20, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 06:47:39'),
(215, 112, 'chat_interaction', 20, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 06:53:31'),
(216, 112, 'chat_interaction', 20, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 06:53:45'),
(217, 112, 'chat_interaction', 20, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 06:54:03'),
(218, 112, 'chat_interaction', 20, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 06:54:23'),
(219, 112, 'chat_interaction', 20, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 06:54:37'),
(220, 112, 'chat_interaction', 20, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 06:54:56'),
(221, 112, 'chat_interaction', 20, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 06:55:09'),
(222, 112, 'chat_interaction', 20, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 06:55:22'),
(223, 112, 'chat_interaction', 20, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 06:55:36'),
(224, 112, 'chat_interaction', 20, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 07:05:36'),
(225, 112, 'chat_interaction', 20, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 07:09:05'),
(226, 112, 'chat_interaction', 20, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 07:09:23'),
(227, 112, 'chat_interaction', 20, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 07:10:36'),
(228, 112, 'chat_interaction', 20, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 07:11:38'),
(229, 112, 'chat_interaction', 20, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 07:13:56'),
(230, 112, 'chat_interaction', 20, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 07:14:16'),
(231, 112, 'chat_interaction', 20, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 07:14:34'),
(232, 112, 'chat_interaction', 20, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 07:14:59'),
(233, 112, 'chat_interaction', 20, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 07:23:33'),
(234, 112, 'chat_interaction', 20, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 07:23:49'),
(235, 112, 'chat_interaction', 20, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 07:24:06'),
(236, 112, 'chat_interaction', 20, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 07:24:27'),
(237, 112, 'chat_interaction', 20, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 07:24:40'),
(268, 114, 'chat_interaction', 20, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 08:51:09'),
(269, 114, 'chat_interaction', 20, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 08:51:34'),
(270, 114, 'chat_interaction', 20, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 08:51:58'),
(271, 114, 'chat_interaction', 20, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 08:52:23'),
(272, 114, 'chat_interaction', 20, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 08:52:52'),
(273, 114, 'tool_generation', 100, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 08:54:36'),
(274, 114, 'chat_interaction', 20, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 09:14:27'),
(275, 114, 'chat_interaction', 20, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 09:15:08'),
(276, 114, 'chat_interaction', 20, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 09:16:16'),
(277, 114, 'chat_interaction', 20, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 09:16:35'),
(278, 114, 'chat_interaction', 20, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 09:16:57'),
(279, 114, 'tool_generation', 100, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 09:18:49'),
(280, 115, 'chat_interaction', 20, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 09:52:20');

-- --------------------------------------------------------

--
-- Table structure for table `pt_contact_message`
--

CREATE TABLE `pt_contact_message` (
  `id` int NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '联系人姓名',
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '联系人邮箱',
  `subject` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '消息主题',
  `message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '消息内容',
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '发送者IP地址',
  `user_agent` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '浏览器用户代理',
  `status` enum('unread','read','replied','archived') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'unread' COMMENT '处理状态：unread-未读，read-已读，replied-已回复，archived-已归档',
  `admin_notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '管理员备注信息',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='联系消息表';

-- --------------------------------------------------------

--
-- Table structure for table `pt_launch_categories`
--

CREATE TABLE `pt_launch_categories` (
  `id` int NOT NULL,
  `slug` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `icon` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `color` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '#4e73df',
  `parent_id` int DEFAULT NULL,
  `sort_order` int DEFAULT '0',
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='产品启动分类表';

--
-- Dumping data for table `pt_launch_categories`
--

INSERT INTO `pt_launch_categories` (`id`, `slug`, `name`, `description`, `icon`, `color`, `parent_id`, `sort_order`, `is_active`, `created_at`) VALUES
(1, 'ai-tools', 'AI Tools', 'Artificial Intelligence and Machine Learning tools', 'fas fa-robot', '#e74c3c', NULL, 1, 1, '2025-08-19 06:55:11'),
(2, 'productivity', 'Productivity', 'Tools to boost productivity and efficiency', 'fas fa-rocket', '#3498db', NULL, 2, 1, '2025-08-19 06:55:11'),
(3, 'developer-tools', 'Developer Tools', 'Development and programming utilities', 'fas fa-code', '#2ecc71', NULL, 3, 1, '2025-08-19 06:55:11'),
(4, 'design', 'Design', 'Design and creative tools', 'fas fa-palette', '#9b59b6', NULL, 4, 1, '2025-08-19 06:55:11'),
(5, 'marketing', 'Marketing', 'Marketing and growth tools', 'fas fa-bullhorn', '#f39c12', NULL, 5, 1, '2025-08-19 06:55:11'),
(6, 'analytics', 'Analytics', 'Data analysis and tracking tools', 'fas fa-chart-bar', '#1abc9c', NULL, 6, 1, '2025-08-19 06:55:11'),
(7, 'e-commerce', 'E-commerce', 'Online store and sales tools', 'fas fa-shopping-cart', '#e67e22', NULL, 7, 1, '2025-08-19 06:55:11'),
(8, 'communication', 'Communication', 'Communication and collaboration tools', 'fas fa-comments', '#34495e', NULL, 8, 1, '2025-08-19 06:55:11'),
(9, 'finance', 'Finance', 'Financial and accounting tools', 'fas fa-dollar-sign', '#27ae60', NULL, 9, 1, '2025-08-19 06:55:11'),
(10, 'education', 'Education', 'Learning and educational platforms', 'fas fa-graduation-cap', '#8e44ad', NULL, 10, 1, '2025-08-19 06:55:11'),
(11, 'security', 'Security', 'Security and privacy tools', 'fas fa-shield-alt', '#e74c3c', NULL, 11, 1, '2025-08-19 15:29:17'),
(12, 'automation', 'Automation', 'Workflow automation and productivity tools', 'fas fa-cogs', '#f39c12', NULL, 12, 1, '2025-08-19 15:29:17'),
(13, 'content-creation', 'Content Creation', 'Content creation and media tools', 'fas fa-pen-fancy', '#9b59b6', NULL, 13, 1, '2025-08-19 15:29:17'),
(14, 'project-management', 'Project Management', 'Project management and collaboration tools', 'fas fa-tasks', '#2ecc71', NULL, 14, 1, '2025-08-19 15:29:17');

-- --------------------------------------------------------

--
-- Table structure for table `pt_launch_votes`
--

CREATE TABLE `pt_launch_votes` (
  `id` int NOT NULL,
  `launch_id` int NOT NULL COMMENT '产品ID',
  `user_id` int NOT NULL COMMENT '用户ID',
  `vote_type` enum('up','down') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'up' COMMENT '投票类型',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='产品启动投票表';

--
-- Dumping data for table `pt_launch_votes`
--

INSERT INTO `pt_launch_votes` (`id`, `launch_id`, `user_id`, `vote_type`, `created_at`) VALUES
(1, 3, 58, 'up', '2025-08-19 07:36:31'),
(5, 101, 5, 'up', '2025-08-15 07:30:00'),
(6, 101, 16, 'up', '2025-08-15 08:45:00'),
(7, 101, 17, 'up', '2025-08-15 11:15:00'),
(8, 102, 6, 'up', '2025-08-16 02:20:00'),
(9, 102, 18, 'up', '2025-08-16 06:30:00'),
(10, 103, 5, 'up', '2025-08-12 04:00:00'),
(11, 103, 6, 'up', '2025-08-12 06:20:00'),
(12, 103, 15, 'up', '2025-08-12 10:30:00'),
(13, 103, 17, 'up', '2025-08-13 01:45:00'),
(14, 104, 16, 'up', '2025-08-14 09:20:00'),
(15, 104, 18, 'up', '2025-08-14 11:45:00'),
(16, 105, 19, 'up', '2025-08-17 06:15:00'),
(17, 106, 15, 'up', '2025-08-18 01:30:00');

-- --------------------------------------------------------

--
-- Table structure for table `pt_manager`
--

CREATE TABLE `pt_manager` (
  `id` int NOT NULL,
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户名，唯一标识',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '邮箱地址，唯一标识',
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '密码哈希值',
  `display_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '显示名称',
  `role` enum('admin','manager','editor') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'admin' COMMENT '角色权限：admin-超级管理员，manager-管理员，editor-编辑员',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '个人描述信息',
  `two_factor_enabled` tinyint(1) DEFAULT '0' COMMENT '是否启用双因子认证：0-未启用，1-已启用',
  `two_factor_secret` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '双因子认证密钥',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `last_login` timestamp NULL DEFAULT NULL COMMENT '最后登录时间',
  `status` enum('active','inactive','suspended') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'active' COMMENT '账户状态：active-活跃，inactive-非活跃，suspended-暂停'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='管理员表';

--
-- Dumping data for table `pt_manager`
--

INSERT INTO `pt_manager` (`id`, `username`, `email`, `password`, `display_name`, `role`, `description`, `two_factor_enabled`, `two_factor_secret`, `created_at`, `updated_at`, `last_login`, `status`) VALUES
(1, 'asasasa', '<EMAIL>', '$2y$10$ACsKxEfqMICY2zS9yhKdBumPZZqijtPX4H3aK8jH/NI6xMutm43PC', 'refgrfvg', 'admin', 'fedgwvdf ew ds edsf  f fw few fe', 1, '5CGIEDQHFQKKEBD2', '2025-08-15 11:12:47', '2025-08-15 11:28:17', NULL, 'active'),
(2, 'Prompt2Tool', '<EMAIL>', '$2y$10$kyxnTgkxmTmhtgKf2Z18deanBX9NYObL932nt2iFGr36kzgxqeHli', 'Prompt2Tool', 'admin', 'I don\'t just write prompts, I craft digital magic.', 1, '2RNYD27WHYIIB5M6', '2025-08-15 11:29:47', '2025-08-21 09:46:57', '2025-08-21 09:46:57', 'active');

-- --------------------------------------------------------

--
-- Table structure for table `pt_manager_session`
--

CREATE TABLE `pt_manager_session` (
  `id` int NOT NULL,
  `manager_id` int NOT NULL COMMENT '管理员ID，关联pt_manager表',
  `session_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '会话ID，唯一标识',
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '登录IP地址，支持IPv6',
  `user_agent` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '浏览器用户代理信息',
  `device_info` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '设备信息描述',
  `location` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '登录地理位置',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '会话创建时间',
  `last_activity` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后活动时间',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '会话状态：0-已失效，1-活跃中'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='管理员会话表';

-- --------------------------------------------------------

--
-- Table structure for table `pt_manager_token`
--

CREATE TABLE `pt_manager_token` (
  `id` int NOT NULL,
  `manager_id` int NOT NULL COMMENT '管理员ID，关联pt_manager表',
  `token` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '记住令牌哈希值',
  `expires_at` datetime NOT NULL COMMENT '令牌过期时间',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='管理员记住令牌表';

-- --------------------------------------------------------

--
-- Table structure for table `pt_member`
--

CREATE TABLE `pt_member` (
  `id` int NOT NULL,
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户名，唯一标识',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '邮箱地址，唯一标识',
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '密码哈希值，Google用户此字段为空',
  `first_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '名字',
  `last_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '姓氏',
  `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '头像图片URL',
  `bio` text COLLATE utf8mb4_unicode_ci COMMENT '个人简介/签名',
  `api_quota` int DEFAULT '1000' COMMENT 'API调用配额',
  `api_used` int DEFAULT '0' COMMENT 'API已使用次数',
  `api_reset_date` date DEFAULT NULL COMMENT 'API配额重置日期',
  `subscription_type` enum('free','basic','premium','enterprise') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'free' COMMENT '订阅类型：free-免费，basic-基础，premium-高级，enterprise-企业',
  `subscription_expires` date DEFAULT NULL COMMENT '订阅到期日期',
  `status` enum('active','inactive','suspended','pending') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'pending' COMMENT '账户状态：active-活跃，inactive-非活跃，suspended-暂停，pending-待激活',
  `email_verified` tinyint(1) DEFAULT '0' COMMENT '邮箱验证状态：0-未验证，1-已验证',
  `email_verification_token` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '邮箱验证码（6位数字）',
  `password_reset_token` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '密码重置令牌',
  `password_reset_expires` timestamp NULL DEFAULT NULL COMMENT '密码重置令牌过期时间',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `last_login` timestamp NULL DEFAULT NULL COMMENT '最后登录时间',
  `login_count` int DEFAULT '0' COMMENT '登录次数统计',
  `preferences` json DEFAULT NULL COMMENT '用户偏好设置'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会员表';

--
-- Dumping data for table `pt_member`
--

INSERT INTO `pt_member` (`id`, `username`, `email`, `password`, `first_name`, `last_name`, `avatar`, `bio`, `api_quota`, `api_used`, `api_reset_date`, `subscription_type`, `subscription_expires`, `status`, `email_verified`, `email_verification_token`, `password_reset_token`, `password_reset_expires`, `created_at`, `updated_at`, `last_login`, `login_count`, `preferences`) VALUES
(2, 'jane_smith', '<EMAIL>', '$2y$10$oIvbRb0OTFKxC2MaxcIVGOl7cF3IiKBp/ZuUOHUyQpwpfvPoY7wty', 'Jane', 'Smith', NULL, NULL, 5000, 234, '2025-08-18', 'premium', NULL, 'inactive', 1, NULL, NULL, NULL, '2025-08-16 13:09:24', '2025-08-18 05:54:44', '2025-08-15 05:09:23', 0, NULL),
(3, 'mike_wilson', '<EMAIL>', '$2y$10$MsyFIpbg9AWvidZSkK3dNO6PJmXIJAIupzKfdw9BFz04NSx9BQ8IC', 'Mike', 'Wilson', NULL, NULL, 2000, 45, '2025-08-18', 'basic', NULL, 'inactive', 1, NULL, NULL, NULL, '2025-08-16 13:09:24', '2025-08-18 05:54:44', '2025-08-09 05:09:23', 0, NULL),
(4, 'pending_user', '<EMAIL>', '$2y$10$..L2WD12fpEvZLOtNiua2OgayrzWD0vqPcq.fBxWMLSIRSxxTToEe', 'Pending', 'User', NULL, NULL, 1000, 0, '2025-08-18', 'free', NULL, 'pending', 0, NULL, NULL, NULL, '2025-08-16 13:09:24', '2025-08-18 05:54:44', NULL, 0, NULL),
(5, 'google_user1', '<EMAIL>', NULL, 'Google', 'User One', NULL, NULL, 1000, 67, '2025-08-18', 'free', NULL, 'active', 1, NULL, NULL, NULL, '2025-08-16 13:09:24', '2025-08-18 05:54:44', '2025-08-16 04:39:24', 0, NULL),
(6, 'sarah_google', '<EMAIL>', NULL, 'Sarah', 'Johnson', NULL, NULL, 5000, 1234, '2025-08-18', 'premium', NULL, 'active', 1, NULL, NULL, NULL, '2025-08-16 13:09:24', '2025-08-18 05:54:44', '2025-08-16 02:09:24', 0, NULL),
(7, 'alex_dev', '<EMAIL>', NULL, 'Alex', 'Developer', NULL, NULL, 10000, 3456, '2025-08-18', 'enterprise', NULL, 'inactive', 1, NULL, NULL, NULL, '2025-08-16 13:09:24', '2025-08-18 05:54:44', '2025-08-16 04:09:24', 0, NULL),
(8, 'inactive_google', '<EMAIL>', NULL, 'Inactive', 'Google User', NULL, NULL, 1000, 12, '2025-08-18', 'free', NULL, 'suspended', 1, NULL, NULL, NULL, '2025-08-16 13:09:24', '2025-08-18 05:54:44', '2025-08-02 05:09:24', 0, NULL),
(15, 'jnianhui', '<EMAIL>', '$2y$10$UaAZjlKq7RDW45UVpPvwnODbLJf/9lZl3X7LV02/QH0eAG0fRURTC', 'jnianhui', 'jnianhui', NULL, NULL, 1000, 0, '2025-08-18', 'free', NULL, 'active', 1, NULL, NULL, NULL, '2025-08-16 13:34:17', '2025-08-18 05:54:44', NULL, 0, NULL),
(16, 'new_user_today1', '<EMAIL>', '$2y$10$oIvbRb0OTFKxC2MaxcIVGOl7cF3IiKBp/ZuUOHUyQpwpfvPoY7wty', 'Today', 'User One', NULL, NULL, 1000, 0, '2025-08-18', 'free', NULL, 'active', 1, NULL, NULL, NULL, '2025-08-17 05:14:08', '2025-08-18 05:54:44', '2025-08-17 06:14:08', 0, NULL),
(17, 'new_user_today2', '<EMAIL>', '$2y$10$oIvbRb0OTFKxC2MaxcIVGOl7cF3IiKBp/ZuUOHUyQpwpfvPoY7wty', 'Today', 'User Two', NULL, NULL, 1000, 5, '2025-08-18', 'free', NULL, 'active', 1, NULL, NULL, NULL, '2025-08-17 03:14:08', '2025-08-18 05:54:44', '2025-08-17 04:14:08', 0, NULL),
(18, 'yesterday_user1', '<EMAIL>', '$2y$10$oIvbRb0OTFKxC2MaxcIVGOl7cF3IiKBp/ZuUOHUyQpwpfvPoY7wty', 'Yesterday', 'User One', NULL, NULL, 2000, 12, '2025-08-18', 'basic', NULL, 'active', 1, NULL, NULL, NULL, '2025-08-16 05:14:08', '2025-08-18 05:54:44', '2025-08-16 06:14:08', 0, NULL),
(19, 'yesterday_user2', '<EMAIL>', '$2y$10$oIvbRb0OTFKxC2MaxcIVGOl7cF3IiKBp/ZuUOHUyQpwpfvPoY7wty', 'Yesterday', 'User Two', NULL, NULL, 1000, 8, '2025-08-18', 'free', NULL, 'active', 1, NULL, NULL, NULL, '2025-08-16 02:14:08', '2025-08-18 05:54:44', '2025-08-16 03:14:08', 0, NULL),
(20, 'yesterday_user3', '<EMAIL>', '$2y$10$oIvbRb0OTFKxC2MaxcIVGOl7cF3IiKBp/ZuUOHUyQpwpfvPoY7wty', 'Yesterday', 'User Three', NULL, NULL, 5000, 45, '2025-08-18', 'premium', NULL, 'active', 1, NULL, NULL, NULL, '2025-08-15 23:14:08', '2025-08-18 05:54:44', '2025-08-16 00:14:08', 0, NULL),
(21, 'day2_user1', '<EMAIL>', '$2y$10$oIvbRb0OTFKxC2MaxcIVGOl7cF3IiKBp/ZuUOHUyQpwpfvPoY7wty', 'Day Two', 'User', NULL, NULL, 1000, 3, '2025-08-18', 'free', NULL, 'active', 1, NULL, NULL, NULL, '2025-08-15 04:14:08', '2025-08-18 05:54:44', '2025-08-15 05:14:08', 0, NULL),
(22, 'day3_user1', '<EMAIL>', '$2y$10$oIvbRb0OTFKxC2MaxcIVGOl7cF3IiKBp/ZuUOHUyQpwpfvPoY7wty', 'Day Three', 'User One', NULL, NULL, 2000, 23, '2025-08-18', 'basic', NULL, 'active', 1, NULL, NULL, NULL, '2025-08-14 06:14:08', '2025-08-18 05:54:44', '2025-08-14 07:14:08', 0, NULL),
(23, 'day3_user2', '<EMAIL>', '$2y$10$oIvbRb0OTFKxC2MaxcIVGOl7cF3IiKBp/ZuUOHUyQpwpfvPoY7wty', 'Day Three', 'User Two', NULL, NULL, 1000, 15, '2025-08-18', 'free', NULL, 'active', 1, NULL, NULL, NULL, '2025-08-14 03:14:08', '2025-08-18 05:54:44', '2025-08-14 04:14:08', 0, NULL),
(24, 'day3_user3', '<EMAIL>', '$2y$10$oIvbRb0OTFKxC2MaxcIVGOl7cF3IiKBp/ZuUOHUyQpwpfvPoY7wty', 'Day Three', 'User Three', NULL, NULL, 1000, 7, '2025-08-18', 'free', NULL, 'active', 1, NULL, NULL, NULL, '2025-08-14 01:14:08', '2025-08-18 05:54:44', '2025-08-14 02:14:08', 0, NULL),
(25, 'day3_user4', '<EMAIL>', '$2y$10$oIvbRb0OTFKxC2MaxcIVGOl7cF3IiKBp/ZuUOHUyQpwpfvPoY7wty', 'Day Three', 'User Four', NULL, NULL, 5000, 89, '2025-08-18', 'premium', NULL, 'active', 1, NULL, NULL, NULL, '2025-08-13 22:14:08', '2025-08-18 05:54:44', '2025-08-13 23:14:08', 0, NULL),
(26, 'day4_user1', '<EMAIL>', '$2y$10$oIvbRb0OTFKxC2MaxcIVGOl7cF3IiKBp/ZuUOHUyQpwpfvPoY7wty', 'Day Four', 'User One', NULL, NULL, 1000, 34, '2025-08-18', 'free', NULL, 'active', 1, NULL, NULL, NULL, '2025-08-13 05:14:08', '2025-08-18 05:54:44', '2025-08-13 06:14:08', 0, NULL),
(27, 'day4_user2', '<EMAIL>', '$2y$10$oIvbRb0OTFKxC2MaxcIVGOl7cF3IiKBp/ZuUOHUyQpwpfvPoY7wty', 'Day Four', 'User Two', NULL, NULL, 2000, 56, '2025-08-18', 'basic', NULL, 'active', 1, NULL, NULL, NULL, '2025-08-13 00:14:08', '2025-08-18 05:54:44', '2025-08-13 01:14:08', 0, NULL),
(28, 'day5_user1', '<EMAIL>', '$2y$10$oIvbRb0OTFKxC2MaxcIVGOl7cF3IiKBp/ZuUOHUyQpwpfvPoY7wty', 'Day Five', 'User One', NULL, NULL, 1000, 12, '2025-08-18', 'free', NULL, 'active', 1, NULL, NULL, NULL, '2025-08-12 06:14:08', '2025-08-18 05:54:44', '2025-08-12 07:14:08', 0, NULL),
(29, 'day5_user2', '<EMAIL>', '$2y$10$oIvbRb0OTFKxC2MaxcIVGOl7cF3IiKBp/ZuUOHUyQpwpfvPoY7wty', 'Day Five', 'User Two', NULL, NULL, 1000, 28, '2025-08-18', 'free', NULL, 'active', 1, NULL, NULL, NULL, '2025-08-12 02:14:08', '2025-08-18 05:54:44', '2025-08-12 03:14:08', 0, NULL),
(30, 'day5_user3', '<EMAIL>', '$2y$10$oIvbRb0OTFKxC2MaxcIVGOl7cF3IiKBp/ZuUOHUyQpwpfvPoY7wty', 'Day Five', 'User Three', NULL, NULL, 5000, 123, '2025-08-18', 'premium', NULL, 'active', 1, NULL, NULL, NULL, '2025-08-11 23:14:08', '2025-08-18 05:54:44', '2025-08-12 00:14:08', 0, NULL),
(31, 'day6_user1', '<EMAIL>', '$2y$10$oIvbRb0OTFKxC2MaxcIVGOl7cF3IiKBp/ZuUOHUyQpwpfvPoY7wty', 'Day Six', 'User', NULL, NULL, 2000, 67, '2025-08-18', 'basic', NULL, 'active', 1, NULL, NULL, NULL, '2025-08-11 04:14:08', '2025-08-18 05:54:44', '2025-08-11 05:14:08', 0, NULL),
(32, 'day7_user1', '<EMAIL>', '$2y$10$oIvbRb0OTFKxC2MaxcIVGOl7cF3IiKBp/ZuUOHUyQpwpfvPoY7wty', 'Day Seven', 'User One', NULL, NULL, 1000, 45, '2025-08-18', 'free', NULL, 'active', 1, NULL, NULL, NULL, '2025-08-10 05:14:08', '2025-08-18 05:54:44', '2025-08-10 06:14:08', 0, NULL),
(33, 'day7_user2', '<EMAIL>', '$2y$10$oIvbRb0OTFKxC2MaxcIVGOl7cF3IiKBp/ZuUOHUyQpwpfvPoY7wty', 'Day Seven', 'User Two', NULL, NULL, 1000, 23, '2025-08-18', 'free', NULL, 'active', 1, NULL, NULL, NULL, '2025-08-10 01:14:08', '2025-08-18 05:54:44', '2025-08-10 02:14:08', 0, NULL),
(34, 'dcf32f111', '<EMAIL>', '', '21ww2', 'e2e21', '/uploads/avatars/avatar_34_1755698396.jpg', 'aqfqfeq', 1000, 100, '2025-08-20', 'free', NULL, 'active', 1, NULL, NULL, NULL, '2025-08-18 06:05:34', '2025-08-20 16:26:02', '2025-08-20 13:11:36', 0, NULL),
(35, 'jnianhui111', '<EMAIL>', '$2y$10$6G6CutkZBd2Df0YUHs5bhOFSH.vFy/Yvx.J4fChF4Wh6bKZUDKRTi', 'Nathaniel', 'Mercer', NULL, NULL, 1000, 0, '2025-08-20', 'free', NULL, 'active', 1, NULL, NULL, NULL, '2025-08-18 06:18:35', '2025-08-20 14:58:51', '2025-08-18 06:19:07', 0, NULL),
(36, 'jnianhui11', '<EMAIL>', '$2y$10$uiRJPNYAtvUIcvixqN75UOkGBW19a0hIb1tq28kosBrAuc/2VQHiK', 'Nathaniel', 'Mercer', NULL, NULL, 1000, 0, '2025-08-20', 'free', NULL, 'active', 1, NULL, NULL, NULL, '2025-08-18 06:52:37', '2025-08-20 14:58:51', NULL, 0, NULL),
(37, 'laomaoseo', '<EMAIL>', '$2y$10$IeqMC0JMMDc39uUDeJJUje/lZiy9PQgN5bMdc8O7ZimQonDmLd2iW', 'Nathaniel', 'Mercer', NULL, NULL, 1000, 0, '2025-08-20', 'free', NULL, 'pending', 0, '699472', NULL, NULL, '2025-08-18 06:57:44', '2025-08-20 14:58:51', NULL, 0, NULL),
(38, 'laomaoseo11', '<EMAIL>', '$2y$10$ZcHy4He/f8jGqgmslnLRkOKHT66V0MEhrLLOAJI13lEGC/cE2s6hG', 'Nathaniel', 'Mercer', NULL, NULL, 1000, 0, '2025-08-20', 'free', NULL, 'active', 1, NULL, NULL, NULL, '2025-08-18 06:58:04', '2025-08-20 14:58:51', NULL, 0, NULL),
(39, 'admin111', '<EMAIL>', '$2y$10$2FbrkVzCTj.Sd6P3H.yd1Oq.xRAgqt9Rw0KsdOuLVEyJpOKS.wm9.', 'Nathaniel', 'Mercer', NULL, NULL, 1000, 0, '2025-08-20', 'free', NULL, 'pending', 0, '600235', NULL, NULL, '2025-08-18 06:59:34', '2025-08-20 14:58:51', NULL, 0, NULL),
(40, 'b642158112', '<EMAIL>', '$2y$10$Lm7VAK28hXQV.VMOPzatVe2ZeTjQ./TUpnsV9dgFQ15yrOPW15rmG', 'Nathaniel', 'Mercer', NULL, NULL, 1000, 0, '2025-08-20', 'free', NULL, 'pending', 0, '558652', NULL, NULL, '2025-08-18 07:01:50', '2025-08-20 14:58:51', NULL, 0, NULL),
(41, 'jnianhu111i', '<EMAIL>', '$2y$10$wJRnXGgBvGDRfJzET9MmOunuWSNU3BAD5.8E7t7939zZOmRQtTdlC', 'Nathaniel', 'Mercer', NULL, NULL, 1000, 0, '2025-08-20', 'free', NULL, 'active', 1, NULL, NULL, NULL, '2025-08-18 07:17:26', '2025-08-20 14:58:51', NULL, 0, NULL),
(42, 'b6421581112', '<EMAIL>', '$2y$10$Xfl8vRVOPUru74ztTQJtvudldnE7ZjOA.AXFC/3xi3z.uCYeeXkb.', 'Nathaniel', 'Mercer', NULL, NULL, 1000, 0, '2025-08-20', 'free', NULL, 'active', 1, NULL, NULL, NULL, '2025-08-18 07:21:03', '2025-08-20 14:58:51', NULL, 0, NULL),
(43, 'hal111o', '<EMAIL>', '$2y$10$xFLQlx6Azai2jIx75Lz5Z.RnLjfd/YSNqmhXyNGo3nwR0izOOWdyi', 'Nathaniel', 'Mercer', NULL, NULL, 1000, 0, '2025-08-20', 'free', NULL, 'active', 1, NULL, NULL, NULL, '2025-08-18 07:25:39', '2025-08-20 14:58:51', NULL, 0, NULL),
(44, 'b64215811112', '<EMAIL>', '$2y$10$qYqByYROWx/np.s0nSBHeO..3z3UEWyYansfIfuNmUmTEJyo8m0QO', 'Nathaniel', 'Mercer', NULL, NULL, 1000, 0, '2025-08-20', 'free', NULL, 'pending', 0, '146006', NULL, NULL, '2025-08-18 07:27:01', '2025-08-20 14:58:51', NULL, 0, NULL),
(45, 'manag121er', '<EMAIL>', '$2y$10$lS8yM8U.vYGZ1lvqcG5UuO2kjq7CvdMCuzSgkl1WvCH4oJ8ZJm3Lm', 'Nathaniel', 'Mercer', NULL, NULL, 1000, 0, '2025-08-20', 'free', NULL, 'active', 1, NULL, NULL, NULL, '2025-08-18 07:29:39', '2025-08-20 14:58:51', NULL, 0, NULL),
(46, 'jni2121anhui', '<EMAIL>', '$2y$10$UgVCqsOjZqWhT6.NSp9TPePUvO0X9RzwGDmKTWZLZnOcj/lHE.nQe', 'Nathaniel', 'Mercer', NULL, NULL, 1000, 0, '2025-08-20', 'free', NULL, 'active', 1, NULL, NULL, NULL, '2025-08-18 07:36:04', '2025-08-20 14:58:51', NULL, 0, NULL),
(47, 'jnia21112nhui', '<EMAIL>', '$2y$10$qZ01h/3LxeqcRs9uicsk..CpL/UyPsjui41ok3pbdMpCkpjfIkGwK', 'Nathaniel', 'Mercer', NULL, NULL, 1000, 0, '2025-08-20', 'free', NULL, 'active', 1, NULL, NULL, NULL, '2025-08-18 07:48:03', '2025-08-20 14:58:51', NULL, 0, NULL),
(48, 'b64222221582', '<EMAIL>', '$2y$10$6fYw.BbGV04gJC6FU4CURe8j8tgBaeW2HwCvVIk3Ype/DkORnyEBm', 'Nathaniel', 'Mercer', NULL, NULL, 1000, 0, '2025-08-20', 'free', NULL, 'active', 1, NULL, NULL, NULL, '2025-08-18 07:55:10', '2025-08-20 14:58:51', NULL, 0, NULL),
(49, 'jnianh22222ui', '<EMAIL>', '$2y$10$hNiQo2g.1F7kLvQ3YCdzs.yLYRv/FpWlW8xJds8rkhZzYJT1V33C.', 'Nathaniel', 'Mercer', NULL, NULL, 1000, 0, '2025-08-20', 'free', NULL, 'active', 1, NULL, NULL, NULL, '2025-08-18 08:03:24', '2025-08-20 14:58:51', NULL, 0, NULL),
(50, 'jnianh222ui', '<EMAIL>', '$2y$10$ZRtBRTUX0MbEqfTt37Yxq.lZL6bhNkJoBbeloqnt4L6jDLtMAajh.', 'Nathaniel', 'Mercer', NULL, NULL, 1000, 0, '2025-08-20', 'free', NULL, 'active', 1, NULL, NULL, NULL, '2025-08-18 08:10:35', '2025-08-20 14:58:51', NULL, 0, NULL),
(51, '222111111', '<EMAIL>', '$2y$10$IUVo8WPLlC4lLb3c78I.3eUQ1SRbGLCq3xX9ZK9F0.RMAW4b2cRy.', '2323', 'Mer2cer11111', '/uploads/avatars/avatar_51_1755518016.jpg', '1111', 1000, 0, '2025-08-20', 'free', NULL, 'active', 1, NULL, NULL, NULL, '2025-08-18 11:24:48', '2025-08-20 14:58:51', NULL, 0, NULL),
(52, 'admicwcwn', '<EMAIL>', '$2y$10$a5qeOehpfIT6spceXjtZe.ljhx..eC6GSnSA.v7CWtt52Lo/rMtFm', 'Nathaniel', 'Mercer', NULL, NULL, 1000, 0, '2025-08-20', 'free', NULL, 'active', 1, NULL, NULL, NULL, '2025-08-18 12:06:53', '2025-08-20 14:58:51', '2025-08-18 12:10:24', 0, NULL),
(53, '2222hui', '<EMAIL>', '$2y$10$IY5XNkg/xqqpTcHB584fCu2pGyfWcVlfm0Ux4zkLGi8pBdImLB40.', 'dwfe', 'wef', NULL, NULL, 1000, 0, '2025-08-20', 'free', NULL, 'active', 1, NULL, NULL, NULL, '2025-08-18 12:22:22', '2025-08-20 14:58:51', '2025-08-18 12:25:20', 0, NULL),
(57, 'jni2232232anhui', '<EMAIL>', '$2y$10$yA26yt5TvYyeeMGYjw3lxeluzfj8RcZWsQ7n5ZkPiBxLBgeF4aMg.', 'Nathaniel', 'Mercer', NULL, NULL, 1000, 0, '2025-08-20', 'free', NULL, 'active', 1, NULL, NULL, NULL, '2025-08-19 02:53:24', '2025-08-20 14:58:51', NULL, 0, NULL),
(58, 's2wdqin', '<EMAIL>', '$2y$10$zuavuBLFfxXG//EfFD3tfeT9G7rdQqBOyEPydi8TO11bKrQRXfjLC', 'Nathaniel', 'Mercer', NULL, NULL, 1000, 0, '2025-08-20', 'free', NULL, 'active', 1, NULL, NULL, NULL, '2025-08-19 04:14:16', '2025-08-20 14:58:51', NULL, 0, NULL),
(100, 'emma_tech', '<EMAIL>', '$2y$10$oIvbRb0OTFKxC2MaxcIVGOl7cF3IiKBp/ZuUOHUyQpwpfvPoY7wty', 'Emma', 'Tech', NULL, 'AI enthusiast and product manager', 2000, 150, '2025-09-01', 'basic', '2025-12-31', 'active', 1, NULL, NULL, NULL, '2025-08-01 02:00:00', '2025-08-19 02:00:00', '2025-08-19 01:30:00', 25, NULL),
(101, 'david_startup', '<EMAIL>', NULL, 'David', 'Chen', NULL, 'Startup founder building the future', 5000, 890, '2025-09-01', 'premium', '2026-01-31', 'active', 1, NULL, NULL, NULL, '2025-07-15 06:20:00', '2025-08-19 03:15:00', '2025-08-19 00:45:00', 42, NULL),
(102, 'lisa_designer', '<EMAIL>', '$2y$10$oIvbRb0OTFKxC2MaxcIVGOl7cF3IiKBp/ZuUOHUyQpwpfvPoY7wty', 'Lisa', 'Rodriguez', NULL, 'UX/UI Designer passionate about user experience', 1000, 45, '2025-09-01', 'free', NULL, 'active', 1, NULL, NULL, NULL, '2025-08-10 01:15:00', '2025-08-19 04:00:00', '2025-08-18 08:20:00', 18, NULL),
(103, 'alex_coder', '<EMAIL>', '$2y$10$oIvbRb0OTFKxC2MaxcIVGOl7cF3IiKBp/ZuUOHUyQpwpfvPoY7wty', 'Alex', 'Johnson', NULL, 'Full-stack developer and open source contributor', 10000, 2340, '2025-09-01', 'enterprise', '2025-11-30', 'active', 1, NULL, NULL, NULL, '2025-06-20 03:30:00', '2025-08-19 05:45:00', '2025-08-18 23:15:00', 67, NULL),
(104, 'maria_ai', '<EMAIL>', NULL, 'Maria', 'Santos', NULL, 'AI researcher and machine learning expert', 3000, 567, '2025-09-01', 'premium', '2025-10-15', 'active', 1, NULL, NULL, NULL, '2025-07-05 08:45:00', '2025-08-19 06:30:00', '2025-08-18 22:00:00', 33, NULL),
(105, 'tom_marketing', '<EMAIL>', '$2y$10$oIvbRb0OTFKxC2MaxcIVGOl7cF3IiKBp/ZuUOHUyQpwpfvPoY7wty', 'Tom', 'Wilson', NULL, 'Digital marketing specialist', 1500, 234, '2025-09-01', 'basic', '2025-12-01', 'active', 1, NULL, NULL, NULL, '2025-08-05 05:20:00', '2025-08-19 07:10:00', '2025-08-18 11:30:00', 29, NULL),
(106, 'sarah_content', '<EMAIL>', '$2y$10$oIvbRb0OTFKxC2MaxcIVGOl7cF3IiKBp/ZuUOHUyQpwpfvPoY7wty', 'Sarah', 'Miller', NULL, 'Content creator and copywriter', 1000, 78, '2025-09-01', 'free', NULL, 'active', 1, NULL, NULL, NULL, '2025-08-12 00:45:00', '2025-08-19 08:00:00', '2025-08-19 02:15:00', 15, NULL),
(107, 'james_analytics', '<EMAIL>', NULL, 'James', 'Brown', NULL, 'Data analyst and business intelligence expert', 2500, 445, '2025-09-01', 'basic', '2026-02-28', 'active', 1, NULL, NULL, NULL, '2025-07-25 04:10:00', '2025-08-19 09:20:00', '2025-08-19 03:45:00', 38, NULL),
(108, 'pending_creator', '<EMAIL>', '$2y$10$oIvbRb0OTFKxC2MaxcIVGOl7cF3IiKBp/ZuUOHUyQpwpfvPoY7wty', 'Pending', 'Creator', NULL, 'New user waiting for verification', 1000, 0, '2025-09-01', 'free', NULL, 'pending', 0, NULL, NULL, NULL, '2025-08-18 12:30:00', '2025-08-18 12:30:00', NULL, 0, NULL),
(109, 'inactive_dev', '<EMAIL>', '$2y$10$oIvbRb0OTFKxC2MaxcIVGOl7cF3IiKBp/ZuUOHUyQpwpfvPoY7wty', 'Inactive', 'Developer', NULL, 'Former active user', 1000, 123, '2025-09-01', 'free', NULL, 'inactive', 1, NULL, NULL, NULL, '2025-05-15 06:20:00', '2025-06-20 02:15:00', '2025-06-20 02:15:00', 45, NULL),
(110, 'jnian22111ui', '<EMAIL>', '$2y$10$GOcgk/Dzd8oPrhnsM98.dOtawrlcuC7ONOhZwgzYAOnzVC4heLY8S', 'Nathaniel', 'Mercer', NULL, NULL, 1000, 0, '2025-08-20', 'free', NULL, 'active', 1, NULL, NULL, NULL, '2025-08-20 11:34:30', '2025-08-20 14:58:51', NULL, 0, NULL),
(111, 'jns32dwhui', '<EMAIL>', '$2y$10$sY1gJGTNwhxjVvyptCYEyutKhb6YxTJpfWna/qSfR2Pw9ys8InOP.', 'Nathaniel', 'Mercer', NULL, NULL, 1000, 0, '2025-08-21', 'free', NULL, 'active', 1, NULL, NULL, NULL, '2025-08-20 16:26:16', '2025-08-21 06:00:01', NULL, 0, NULL),
(112, 'jnic32d3anhui', '<EMAIL>', '$2y$10$4xPk9m9dCssPJ97f9.oRReZypfw72fEmC8jnyFrUQwWnpV6iO3dgm', 'Nathaniel', 'Mercer', NULL, NULL, 1000, 0, '2025-08-21', 'free', NULL, 'active', 1, NULL, NULL, NULL, '2025-08-21 01:52:45', '2025-08-21 07:30:50', NULL, 0, NULL),
(114, 'erciisme', '<EMAIL>', '$2y$10$TOxBVvtfhP4xUfuvFtgsLO/q8eIsR6sHkv6K.PdNsRhMB7BCIuuti', 'Nathaniel', 'Mercer', NULL, NULL, 1000, 0, NULL, 'free', NULL, 'active', 1, NULL, NULL, NULL, '2025-08-21 08:50:22', '2025-08-21 08:50:28', NULL, 0, NULL),
(115, 'wed32wdf', '<EMAIL>', '$2y$10$jATjEaPyEvJBpvOSKjjAt.ZD3JnG29oTDyNu8JReh9LIwmgTpg/by', 'Nathaniel', 'Mercer', NULL, NULL, 1000, 0, NULL, 'free', NULL, 'active', 1, NULL, NULL, NULL, '2025-08-21 09:46:27', '2025-08-21 09:46:31', NULL, 0, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `pt_member_activity_log`
--

CREATE TABLE `pt_member_activity_log` (
  `id` int NOT NULL,
  `user_id` int NOT NULL COMMENT '用户ID，关联pt_member表',
  `action` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '操作类型：login, logout, register等',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '活动描述',
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'IP地址',
  `user_agent` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '用户代理',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户活动日志表';

--
-- Dumping data for table `pt_member_activity_log`
--

INSERT INTO `pt_member_activity_log` (`id`, `user_id`, `action`, `description`, `ip_address`, `user_agent`, `created_at`) VALUES
(302, 34, 'tool_favorite', 'Added \'Color Palette Generator\' to favorites', '::1', NULL, '2025-08-20 15:28:59'),
(303, 34, 'api_key_create', 'Created API key: 111', '::1', NULL, '2025-08-20 15:30:56'),
(304, 34, 'tool_unfavorite', 'Removed \'Color Palette Generator\' from favorites', '::1', NULL, '2025-08-20 15:36:37'),
(305, 34, 'tool_favorite', 'Added \'Color Palette Generator\' to favorites', '::1', NULL, '2025-08-20 15:36:38'),
(306, 34, 'tool_favorite', 'Added \'HTML Formatter\' to favorites', '::1', NULL, '2025-08-20 15:59:38'),
(307, 34, 'profile_update', 'Updated profile information', '::1', NULL, '2025-08-20 15:59:48'),
(308, 34, 'logout', 'User logged out', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-20 16:26:02'),
(309, 111, 'register', 'User registered with email', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-20 16:26:16'),
(310, 111, 'email_verified', 'Email address verified successfully', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-20 16:26:22'),
(311, 111, 'login', 'Auto-login after email verification', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-20 16:26:22'),
(312, 111, 'request_submit', 'Submitted feature request: Build a Professional Peer Review Verification Plat...', '::1', NULL, '2025-08-20 16:28:02'),
(313, 111, 'tool_favorite', 'Added \'HTML Formatter\' to favorites', '::1', NULL, '2025-08-20 16:49:58'),
(314, 112, 'register', 'User registered with email', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 01:52:45'),
(315, 112, 'email_verified', 'Email address verified successfully', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 01:52:50'),
(316, 112, 'login', 'Auto-login after email verification', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 01:52:50'),
(317, 112, 'api_key_create', 'Created API key: 111', '::1', NULL, '2025-08-21 05:35:54'),
(318, 112, 'request_submit', 'Submitted feature request: Develop an AI-powered DevOps rule engine for conte...', '::1', NULL, '2025-08-21 05:47:39'),
(319, 112, 'logout', 'User logged out', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 07:30:50'),
(324, 114, 'register', 'User registered with email', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 08:50:22'),
(325, 114, 'email_verified', 'Email address verified successfully', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 08:50:28'),
(326, 114, 'login', 'Auto-login after email verification', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 08:50:28'),
(327, 115, 'register', 'User registered with email', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 09:46:27'),
(328, 115, 'email_verified', 'Email address verified successfully', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 09:46:31'),
(329, 115, 'login', 'Auto-login after email verification', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 09:46:31');

--
-- Triggers `pt_member_activity_log`
--
DELIMITER $$
CREATE TRIGGER `update_member_last_activity` AFTER INSERT ON `pt_member_activity_log` FOR EACH ROW BEGIN
    UPDATE pt_member 
    SET updated_at = NOW() 
    WHERE id = NEW.user_id;
END
$$
DELIMITER ;

-- --------------------------------------------------------

--
-- Table structure for table `pt_member_notifications`
--

CREATE TABLE `pt_member_notifications` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '通知类型：quota_warning, security_alert, feature_update等',
  `title` varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL,
  `message` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `data` json DEFAULT NULL COMMENT '额外数据',
  `is_read` tinyint(1) DEFAULT '0',
  `is_important` tinyint(1) DEFAULT '0',
  `expires_at` datetime DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户通知表';

--
-- Dumping data for table `pt_member_notifications`
--

INSERT INTO `pt_member_notifications` (`id`, `user_id`, `type`, `title`, `message`, `data`, `is_read`, `is_important`, `expires_at`, `created_at`) VALUES
(25, 111, 'welcome', 'Welcome to Prompt2Tool!', 'Your account has been successfully activated. Start exploring our AI tools and features.', NULL, 0, 1, NULL, '2025-08-21 00:26:22'),
(26, 112, 'welcome', 'Welcome to Prompt2Tool!', 'Your account has been successfully activated. Start exploring our AI tools and features.', NULL, 0, 1, NULL, '2025-08-21 09:52:50'),
(28, 114, 'welcome', 'Welcome to Prompt2Tool!', 'Your account has been successfully activated. Start exploring our AI tools and features.', NULL, 0, 1, NULL, '2025-08-21 16:50:28'),
(29, 115, 'welcome', 'Welcome to Prompt2Tool!', 'Your account has been successfully activated. Start exploring our AI tools and features.', NULL, 0, 1, NULL, '2025-08-21 17:46:31');

-- --------------------------------------------------------

--
-- Table structure for table `pt_member_sessions`
--

CREATE TABLE `pt_member_sessions` (
  `id` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` int DEFAULT NULL,
  `ip_address` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_agent` text COLLATE utf8mb4_unicode_ci,
  `payload` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `last_activity` int NOT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户会话表';

-- --------------------------------------------------------

--
-- Table structure for table `pt_member_tokens`
--

CREATE TABLE `pt_member_tokens` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `token` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` enum('remember_me','api_key','reset_password','email_verification') COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'API密钥名称',
  `permissions` json DEFAULT NULL COMMENT '权限设置',
  `last_used` datetime DEFAULT NULL,
  `expires_at` datetime DEFAULT NULL,
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户令牌表';

--
-- Dumping data for table `pt_member_tokens`
--

INSERT INTO `pt_member_tokens` (`id`, `user_id`, `token`, `type`, `name`, `permissions`, `last_used`, `expires_at`, `created_at`) VALUES
(36, 34, 'pt-proj-r9N4Uv55JePsJJYgC4YLpN9AV2TKlT9IUMcfE0RAqLCrGjNE7ydpKQyQ', 'api_key', '111', '[\"read\"]', NULL, NULL, '2025-08-20 23:30:56'),
(37, 112, 'pt-proj-txqkbmREe4j2LQXYHXKUxv8mTgBkBa9jsaIdgda7iZCo3gZCtIt27Uco', 'api_key', '111', '[\"read\"]', NULL, NULL, '2025-08-21 13:35:54');

-- --------------------------------------------------------

--
-- Table structure for table `pt_product_launches`
--

CREATE TABLE `pt_product_launches` (
  `id` int NOT NULL,
  `user_id` int NOT NULL COMMENT '用户ID，关联pt_member表',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '产品名称',
  `slug` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'URL友好标识符',
  `tagline` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '一句话描述（最多150字符）',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '详细描述',
  `website_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '产品网站URL',
  `logo_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '产品Logo URL',
  `category` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '主分类',
  `subcategory` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '子分类',
  `tags` json DEFAULT NULL COMMENT '标签数组',
  `key_features` json DEFAULT NULL COMMENT '核心功能列表',
  `target_audience` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '目标用户群体',
  `use_cases` json DEFAULT NULL COMMENT '使用场景',
  `pricing_model` enum('free','freemium','paid','enterprise','unknown') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'unknown' COMMENT '定价模式',
  `launch_status` enum('coming-soon','beta','launched') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'launched' COMMENT '发布状态',
  `screenshots` json DEFAULT NULL COMMENT '产品截图URLs',
  `tech_stack` json DEFAULT NULL COMMENT '技术栈',
  `tech_category` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '技术分类',
  `social_links` json DEFAULT NULL COMMENT '社交媒体链接',
  `video_tutorial_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '视频教程链接',
  `ai_analysis` json DEFAULT NULL COMMENT 'AI分析的完整结果',
  `innovation_score` int DEFAULT '5' COMMENT '创新度评分1-10',
  `status` enum('pending','approved','rejected') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'pending' COMMENT '审核状态',
  `admin_notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '管理员备注',
  `admin_id` int DEFAULT NULL COMMENT '处理管理员ID',
  `views` int DEFAULT '0' COMMENT '浏览次数',
  `clicks` int DEFAULT '0' COMMENT '点击次数',
  `votes` int DEFAULT '0' COMMENT '投票数',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `featured_date` date DEFAULT NULL COMMENT '精选日期',
  `processed_at` timestamp NULL DEFAULT NULL COMMENT '审核处理时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='产品启动提交表';

--
-- Dumping data for table `pt_product_launches`
--

INSERT INTO `pt_product_launches` (`id`, `user_id`, `name`, `slug`, `tagline`, `description`, `website_url`, `logo_url`, `category`, `subcategory`, `tags`, `key_features`, `target_audience`, `use_cases`, `pricing_model`, `launch_status`, `screenshots`, `tech_stack`, `tech_category`, `social_links`, `video_tutorial_url`, `ai_analysis`, `innovation_score`, `status`, `admin_notes`, `admin_id`, `views`, `clicks`, `votes`, `created_at`, `updated_at`, `featured_date`, `processed_at`) VALUES
(1, 2, 'ChatGPT', 'chatgpt', 'AI-powered conversational assistant', 'ChatGPT is an AI chatbot that uses natural language processing to have human-like conversations and assist with various tasks including writing, coding, and problem-solving.', 'https://chat.openai.com', '/uploads/launches/logos/68a4812294bad_1755611426.jpg', 'ai-tools', '', '[]', '[]', '', '[]', 'freemium', 'launched', '[\"/uploads/launches/screenshots/68a48137d6861_1755611447.jpg\"]', '[]', '', '[]', '', NULL, 5, 'approved', '', NULL, 2360, 0, 156, '2025-08-19 06:55:11', '2025-08-19 14:18:20', NULL, NULL),
(2, 3, 'Notion', 'notion', 'All-in-one workspace for notes, docs, and collaboration', 'Notion is a productivity platform that combines note-taking, task management, databases, and collaboration tools in one unified workspace.', 'https://notion.so', NULL, 'productivity', NULL, NULL, NULL, NULL, NULL, 'freemium', 'launched', NULL, NULL, NULL, NULL, NULL, NULL, 5, 'approved', NULL, NULL, 1567, 0, 89, '2025-08-19 06:55:11', '2025-08-19 06:55:11', NULL, NULL),
(3, 5, 'Figma', 'figma', 'Collaborative design platform', 'Figma is a web-based design tool that enables real-time collaboration for UI/UX design, prototyping, and design systems.', 'https://figma.com', NULL, 'design', NULL, NULL, NULL, NULL, NULL, 'freemium', 'launched', NULL, NULL, NULL, NULL, NULL, NULL, 5, 'approved', NULL, NULL, 1896, 1, 135, '2025-08-19 06:55:11', '2025-08-19 08:20:03', NULL, NULL),
(5, 58, 'DesignFlow Pro', 'designflow-pro', 'Professional design workflow automation for creative teams', 'DesignFlow Pro streamlines the entire design process from concept to delivery. Our platform integrates with popular design tools like Figma, Sketch, and Adobe Creative Suite to automate repetitive tasks, manage design systems, and facilitate seamless collaboration between designers and developers. With built-in version control, asset management, and automated handoff features, DesignFlow Pro reduces design-to-development time by up to 60%.', 'https://designflow.pro', 'http://localhost/uploads/launches/logos/logo_58_1755605850_68a46b5ae686b.jpg', 'productivity', 'workflow-automation', '[\"Design\", \"Workflow\", \"Automation\", \"Collaboration\", \"Productivity\"]', '[\"Design system management\", \"Automated asset generation\", \"Version control\", \"Developer handoff\", \"Team collaboration\"]', 'Design teams, UI/UX designers, and creative agencies', '[\"Design system maintenance\", \"Asset management\", \"Team collaboration\", \"Design handoff\", \"Version control\"]', 'paid', 'launched', '[\"http://localhost/uploads/launches/screenshots/screenshot_58_1755605853_68a46b5db12ba.jpg\"]', '[\"Vue.js\", \"Python\", \"PostgreSQL\", \"Redis\", \"Docker\"]', 'Web App', '[]', '', '{\"analysis_version\": \"1.0\", \"confidence_score\": 0.92, \"extracted_features\": [\"design workflow\", \"automation\", \"collaboration\"], \"category_confidence\": 0.94}', 8, 'approved', 'Great tool for design teams, very professional', 1, 898, 67, 0, '2025-08-12 06:20:00', '2025-08-19 15:17:55', NULL, NULL),
(6, 58, 'CodeSync Hub', 'codesync-hub', 'Real-time code collaboration and synchronization platform', 'CodeSync Hub revolutionizes how development teams collaborate on code. Our platform provides real-time code synchronization, intelligent conflict resolution, and seamless integration with popular IDEs and version control systems. With features like live coding sessions, automated code reviews, and smart merge suggestions, CodeSync Hub eliminates the friction in team development workflows. The platform supports over 50 programming languages and integrates with GitHub, GitLab, and Bitbucket.', 'https://codesync.hub', 'http://localhost/uploads/launches/logos/logo_58_1755613474_68a4892210746.jpg', 'developer-tools', 'collaboration-tools', '[\"Development\", \"Collaboration\", \"Version Control\", \"IDE\", \"Programming\"]', '[\"Real-time code sync\", \"Intelligent conflict resolution\", \"Live coding sessions\", \"Automated code reviews\", \"Multi-language support\"]', 'Software developers, development teams, and tech companies', '[\"Team coding sessions\", \"Code review automation\", \"Merge conflict resolution\", \"Remote pair programming\", \"Code quality assurance\"]', 'freemium', 'beta', '[\"http://localhost/uploads/launches/screenshots/screenshot_58_1755613479_68a4892726c13.jpg\", \"http://localhost/uploads/launches/screenshots/screenshot_58_1755613482_68a4892a4dd59.jpg\"]', '[\"TypeScript\", \"Go\", \"WebSocket\", \"MySQL\", \"Kubernetes\"]', 'SaaS', '[]', 'https://www.youtube.com/watch?v=example123', '{\"analysis_version\": \"1.0\", \"confidence_score\": 0.89, \"extracted_features\": [\"code collaboration\", \"real-time sync\", \"developer tools\"], \"category_confidence\": 0.91}', 7, 'approved', 'Innovative collaboration tool for developers', 1, 658, 45, 0, '2025-08-10 01:15:00', '2025-08-19 14:32:24', NULL, NULL),
(7, 58, 'WriteRush', 'writerush', 'Turn writing into an addictive game and build unbreakable habits with gamified productivity.', 'WriteRush transforms writing into an engaging game experience with progress tracking, rewards, and a distraction-free interface. Trusted by thousands of authors, it helps build consistent writing habits through gamification and focus tools.', 'https://www.writerush.net', '/uploads/launches/logos/logo_68a463017f67c.png', 'productivity', NULL, '[\"writing-app\", \"gamified-productivity\", \"writing-habit\"]', '[\"Gamified writing experience with rewards and effects\", \"Progress tracking and writing streaks\", \"Distraction-free minimalist interface\", \"No sign-up required to start writing\", \"Celebratory effects for completed writing sessions\", \"Easy-access sidebar for tools and resources\"]', 'Writers, authors, and content creators looking to establish consistent writing habits and overcome procrastination through gamification', '[\"Building daily writing habits for authors\", \"Overcoming writer\'s block through gamification\", \"Maintaining focus during writing sessions\", \"Tracking progress on writing projects\"]', 'freemium', 'launched', '[\"/uploads/launches/screenshots/screenshot_68a46301800a2.jpg\", \"/uploads/launches/screenshots/screenshot_68a4630180b93.jpg\", \"/uploads/launches/screenshots/screenshot_68a463018141a.jpg\"]', NULL, NULL, '[]', NULL, NULL, 5, 'rejected', '', NULL, 0, 0, 0, '2025-08-19 11:41:53', '2025-08-19 13:19:32', NULL, NULL),
(8, 58, 'WriteRush', 'writerush-111', 'Intelligent automation for smarter workflows', 'The Writing App That Feels Like a GameWriterush uses writing effects, progress tracking, and a distraction-free UI to make building your daily writing habit fun and genuinely addictive. No sign up neededTrusted by 1000+ authors20,000+ followers on Instagram4. ioBENEFIT 01Turn Writing Into an Addictive GameDon\'t just face the blank page; conquer it.', 'https://www.writerush.net', 'http://localhost/uploads/launches/logos/logo_58_1755613960_68a48b084999b.jpg', 'ai-tools', '', '[\"ai\", \"automation\", \"machine-learning\"]', '[\"User-friendly interface\", \"Fast and reliable performance\", \"Cross-platform compatibility\", \"Secure data handling\", \"Regular updates and support\"]', 'General Users', '[\"General productivity\", \"Workflow optimization\", \"Task management\"]', 'freemium', 'coming-soon', '[\"/uploads/launches/screenshots/writerush-screenshot-14.jpg\", \"/uploads/launches/screenshots/writerush-screenshot-15.jpg\", \"/uploads/launches/screenshots/writerush-screenshot-16.jpg\"]', '[]', '', '[]', '', NULL, 5, 'approved', '111', NULL, 27, 1, 0, '2025-08-19 11:58:52', '2025-08-19 15:17:58', NULL, NULL),
(101, 6, 'VoiceFlow AI', 'voiceflow-ai', 'Create conversational AI experiences without coding', 'VoiceFlow AI empowers teams to design, prototype, and deploy voice and chat applications without writing a single line of code. Our visual interface makes it easy to create complex conversational flows, integrate with APIs, and deploy across multiple platforms including Alexa, Google Assistant, and custom chatbots. Perfect for businesses looking to enhance customer engagement through conversational AI.', 'https://voiceflow.ai', NULL, 'ai-tools', 'chatbots', '[\"Voice AI\", \"Chatbots\", \"No-Code\", \"Conversation Design\", \"Automation\"]', '[\"Visual conversation builder\", \"Multi-platform deployment\", \"API integrations\", \"Analytics dashboard\", \"Team collaboration\"]', 'Product managers, UX designers, marketers, and customer service teams', '[\"Customer support automation\", \"Voice app development\", \"Lead qualification\", \"Interactive tutorials\", \"Survey automation\"]', 'paid', 'launched', NULL, '[\"Node.js\", \"React\", \"MongoDB\", \"WebSocket\", \"Google Cloud\"]', 'SaaS', '[]', NULL, NULL, 9, 'approved', '', 1, 2156, 178, 134, '2025-08-15 06:20:00', '2025-08-19 13:21:58', NULL, NULL),
(102, 15, 'DesignSync', 'designsync', 'Collaborative design system management for teams', 'DesignSync streamlines design system management by providing a centralized platform where design teams can create, maintain, and distribute design tokens, components, and guidelines. With version control, automated documentation, and seamless integration with popular design tools, DesignSync ensures consistency across all your digital products while reducing design debt.', 'https://designsync.io', NULL, 'design', 'design-systems', '[\"Design Systems\", \"Collaboration\", \"Design Tokens\", \"Component Library\", \"Documentation\"]', '[\"Design token management\", \"Component versioning\", \"Automated documentation\", \"Design tool integration\", \"Usage analytics\"]', 'Design teams, UI/UX designers, front-end developers, and product managers', '[\"Design system maintenance\", \"Component library management\", \"Design documentation\", \"Cross-team collaboration\", \"Design consistency\"]', 'freemium', 'beta', NULL, '[\"Vue.js\", \"Figma API\", \"Node.js\", \"PostgreSQL\", \"Docker\"]', 'Web App', '[]', 'https://vimeo.com/designsync456', NULL, 7, 'pending', '', 1, 892, 67, 45, '2025-08-16 01:15:00', '2025-08-19 13:18:40', NULL, NULL),
(103, 16, 'CodeReview Bot', 'codereview-bot', 'AI-powered code review assistant for development teams', 'CodeReview Bot uses advanced machine learning to automatically review code changes, identify potential bugs, suggest improvements, and ensure coding standards compliance. Integrated with GitHub, GitLab, and Bitbucket, our AI assistant learns from your team\'s coding patterns and provides personalized feedback to improve code quality and reduce review time by up to 70%.', 'https://codereview.bot', NULL, 'developer-tools', 'code-quality', '[\"Code Review\", \"AI\", \"Development Tools\", \"Quality Assurance\", \"Automation\"]', '[\"Automated code analysis\", \"Bug detection\", \"Style checking\", \"Security scanning\", \"Performance optimization\"]', 'Software developers, development teams, tech leads, and DevOps engineers', '[\"Pull request automation\", \"Code quality assurance\", \"Security vulnerability detection\", \"Performance optimization\", \"Coding standard enforcement\"]', 'paid', 'launched', NULL, '[\"Python\", \"Machine Learning\", \"GitHub API\", \"Docker\", \"Kubernetes\"]', 'SaaS', '[]', 'https://www.youtube.com/watch?v=codereview789', NULL, 9, 'approved', 'Outstanding AI tool for code review, highly recommended', 1, 3429, 289, 201, '2025-08-12 03:30:00', '2025-08-21 10:37:02', '2025-08-19', NULL),
(104, 17, 'DataMind', 'datamind', 'Automated machine learning platform for non-technical users', 'DataMind democratizes machine learning by providing an intuitive platform where business users can build, train, and deploy ML models without coding. Our automated feature engineering, model selection, and hyperparameter tuning make it possible for anyone to harness the power of AI. With built-in data visualization and explainable AI features, DataMind makes machine learning accessible to everyone.', 'https://datamind.ai', NULL, 'ai-tools', 'machine-learning', '[\"Machine Learning\", \"AutoML\", \"No-Code\", \"Data Science\", \"Business Intelligence\"]', '[\"Automated model building\", \"Feature engineering\", \"Model explanation\", \"Data visualization\", \"One-click deployment\"]', 'Business analysts, product managers, marketers, and domain experts', '[\"Predictive modeling\", \"Customer segmentation\", \"Demand forecasting\", \"Fraud detection\", \"Recommendation systems\"]', 'freemium', 'launched', NULL, '[\"Python\", \"Scikit-learn\", \"TensorFlow\", \"React\", \"AWS\"]', 'SaaS', '[]', NULL, NULL, 8, 'approved', 'Great AutoML platform, user-friendly interface', 1, 1567, 123, 89, '2025-08-14 08:45:00', '2025-08-19 08:45:00', NULL, NULL),
(105, 18, 'AdOptimizer', 'adoptimizer', 'AI-driven advertising campaign optimization platform', 'AdOptimizer uses machine learning to automatically optimize your advertising campaigns across Google Ads, Facebook, and other platforms. Our AI analyzes performance data in real-time, adjusts bids, pauses underperforming ads, and scales winning campaigns to maximize your ROI. With predictive budget allocation and automated A/B testing, AdOptimizer takes the guesswork out of digital advertising.', 'https://adoptimizer.com', NULL, 'marketing', 'advertising', '[\"Digital Advertising\", \"Campaign Optimization\", \"AI\", \"Marketing Automation\", \"ROI\"]', '[\"Automated bid management\", \"Performance prediction\", \"Budget optimization\", \"A/B testing\", \"Cross-platform integration\"]', 'Digital marketers, advertising agencies, e-commerce businesses, and marketing managers', '[\"Campaign optimization\", \"Budget allocation\", \"Performance monitoring\", \"Ad creative testing\", \"ROI maximization\"]', 'paid', 'coming-soon', NULL, '[\"Python\", \"Google Ads API\", \"Facebook API\", \"React\", \"Redis\"]', 'SaaS', '[]', 'https://www.youtube.com/watch?v=adoptimizer123', NULL, 7, 'rejected', '', NULL, 234, 12, 8, '2025-08-17 05:20:00', '2025-08-19 14:26:05', NULL, NULL),
(106, 19, 'ContentGenius', 'contentgenius', 'AI content creation and optimization suite', 'ContentGenius is an all-in-one content creation platform that helps writers, marketers, and content creators produce high-quality content faster. Our AI assists with ideation, writing, editing, and SEO optimization. With support for multiple content types including blog posts, social media, emails, and product descriptions, ContentGenius adapts to your brand voice and style.', 'https://contentgenius.io', NULL, 'ai-tools', 'content-creation', '[\"Content Creation\", \"AI Writing\", \"SEO\", \"Marketing\", \"Copywriting\"]', '[\"AI writing assistant\", \"SEO optimization\", \"Brand voice training\", \"Content planning\", \"Performance analytics\"]', 'Content creators, marketers, bloggers, copywriters, and small businesses', '[\"Blog writing\", \"Social media content\", \"Email campaigns\", \"Product descriptions\", \"SEO content\"]', 'freemium', 'beta', NULL, '[\"OpenAI GPT\", \"React\", \"Node.js\", \"MongoDB\", \"Elasticsearch\"]', 'SaaS', '[]', NULL, NULL, 6, 'rejected', 'Similar to existing tools, needs more differentiation', 1, 456, 23, 12, '2025-08-18 00:45:00', '2025-08-19 00:45:00', NULL, NULL),
(107, 58, 'Done It', 'done-it', 'Streamline team work management with Done It - your productivity powerhouse', 'Done It is a comprehensive work management software designed to help teams organize, track, and complete projects efficiently. It provides a centralized platform for task management, collaboration, and progress tracking to boost team productivity.', 'https://doneit.online', NULL, 'productivity', NULL, '[\"team-collaboration\", \"project-management\", \"productivity\"]', '[\"Team task management\", \"Project tracking\", \"Collaboration tools\", \"Progress monitoring\", \"Workflow organization\", \"Team communication\"]', 'Teams and organizations looking to streamline their work processes and improve productivity through centralized task and project management.', '[\"Project management for teams\", \"Task delegation and tracking\", \"Collaborative work environments\", \"Productivity enhancement\"]', 'unknown', 'coming-soon', '[]', NULL, NULL, '[]', NULL, NULL, 5, 'approved', '', NULL, 1, 0, 0, '2025-08-19 15:18:28', '2025-08-19 15:20:35', NULL, NULL),
(108, 58, 'Midshift', 'midshift', 'Shift your workflow into high gear with seamless productivity tools', 'Midshift offers a comprehensive productivity solution designed to streamline workflows and enhance efficiency. With its intuitive interface and powerful features, it helps teams collaborate effectively and achieve their goals faster.', 'https://midshift.co.uk', NULL, 'productivity', NULL, '[\"productivity\", \"collaboration\", \"workflow\"]', '[\"Flexible pricing tiers to suit different needs\", \"Built with React for smooth performance\", \"Multiple subscription options from basic to professional\", \"Designed for team collaboration\", \"Workflow optimization tools\", \"User-friendly interface\"]', 'Teams and professionals seeking to enhance productivity and streamline workflows', '[\"Team project management and collaboration\", \"Workflow automation and optimization\", \"Productivity enhancement for remote teams\", \"Task tracking and progress monitoring\"]', 'free', 'coming-soon', '[]', NULL, NULL, '[]', NULL, NULL, 5, 'pending', NULL, NULL, 0, 0, 0, '2025-08-19 15:22:06', '2025-08-19 15:22:06', NULL, NULL),
(109, 58, 'The Commune', 'the-commune', 'Where creative minds connect, collaborate, and thrive together.', 'The Commune is an online community platform designed specifically for creative individuals to connect, share ideas, and collaborate on projects. It provides a dedicated space where artists, designers, writers, and other creative professionals can network and find inspiration.', 'https://www.thecommune.online', '', 'productivity', NULL, '[\"creative-community\", \"collaboration\", \"networking\"]', '[\"Community platform for creative professionals\", \"Collaboration tools for creative projects\", \"Networking opportunities with like-minded individuals\", \"Resource sharing and knowledge exchange\", \"Project showcase and portfolio display\"]', 'Creative professionals including artists, designers, writers, photographers, and other creative individuals seeking community and collaboration opportunities.', '[\"Creative professionals networking and finding collaborators\", \"Showcasing creative work and building portfolios\", \"Group collaboration on creative projects\", \"Access to resources and industry insights\"]', 'free', 'launched', '[]', '[]', 'saas', '[]', '', NULL, 5, 'approved', '', NULL, 1, 0, 0, '2025-08-19 15:23:36', '2025-08-19 15:25:15', NULL, NULL),
(110, 58, 'Pleo Chat', 'pleo-chat', 'Transform conversations into actionable insights with AI-powered chat assistance', 'Pleo Chat is an intelligent communication platform that enhances productivity through AI-powered conversation analysis and assistance. It helps users extract valuable information, organize discussions, and streamline communication workflows.', 'https://www.pleo.chat', NULL, 'communication', NULL, '[\"ai-chat\", \"communication\", \"productivity\"]', '[\"AI-powered conversation analysis\", \"Multi-tier pricing options\", \"Real-time chat assistance\", \"Information extraction and organization\", \"Conversation summarization\", \"Integration capabilities\"]', 'Businesses and teams looking to improve communication efficiency and extract value from conversations', '[\"Business communication enhancement\", \"Team collaboration and information sharing\", \"Customer support automation\", \"Meeting documentation and follow-up\"]', 'freemium', 'coming-soon', '[]', NULL, NULL, '[]', NULL, NULL, 5, 'pending', NULL, NULL, 0, 0, 0, '2025-08-19 15:54:27', '2025-08-19 15:54:27', NULL, NULL),
(111, 58, 'Incomy', 'incomy', 'Track income and monitor your investment portfolio in one place', 'Incomy is a comprehensive financial tracking platform that helps users monitor their income streams and investment portfolio performance with intuitive analytics and insights.', 'https://incomy.app/zh/', NULL, 'finance', NULL, '[\"finance\", \"investment\", \"tracking\"]', '[\"Income tracking and monitoring\", \"Portfolio performance analytics\", \"Financial insights and reporting\", \"Investment portfolio management\", \"Google Analytics integration\"]', 'Individual investors and finance professionals looking to track multiple income sources and investment portfolios', '[\"Personal finance management\", \"Investment portfolio tracking\", \"Income stream monitoring\", \"Financial performance analysis\"]', 'unknown', 'coming-soon', '[]', NULL, NULL, '[]', NULL, NULL, 5, 'pending', NULL, NULL, 0, 0, 0, '2025-08-19 15:58:51', '2025-08-19 15:58:51', NULL, NULL),
(112, 58, 'Talent Compass', 'talent-compass', 'Navigate talent acquisition with AI-powered precision and efficiency', 'Talent Compass revolutionizes hiring by leveraging AI to match candidates with perfect precision, reducing time-to-hire by 60% while improving quality of hire through intelligent screening and matching algorithms.', 'https://talentcompass.replit.app', NULL, 'productivity', NULL, '[\"recruitment\", \"ai-hiring\", \"talent-acquisition\"]', '[\"AI-powered candidate matching and screening\", \"Automated interview scheduling\", \"Skills assessment and gap analysis\", \"Diversity and inclusion analytics\", \"Integration with popular HR platforms\", \"Real-time talent market insights\"]', 'HR professionals, recruitment agencies, and talent acquisition teams in mid to large-sized companies', '[\"Streamlining recruitment processes for HR departments\", \"Improving hiring quality for tech companies\", \"Enhancing diversity in workforce composition\", \"Reducing time-to-hire for high-volume recruitment\"]', 'unknown', 'coming-soon', '[]', NULL, NULL, '[]', NULL, NULL, 5, 'pending', NULL, NULL, 0, 0, 0, '2025-08-19 16:01:25', '2025-08-19 16:01:25', NULL, NULL),
(113, 58, 'SoundPrint', 'soundprint', 'Transform your audio memories into stunning visual waveform art', 'SoundPrint creates custom waveform art from your audio files, turning cherished sounds, music, and voice recordings into beautiful visual keepsakes. Perfect for preserving special moments in a unique artistic format.', 'https://www.soundprint.shop', NULL, 'productivity', NULL, '[\"audio-visualization\", \"custom-art\", \"memory-preserving\"]', '[\"Custom waveform visualization from audio files\", \"High-quality print-ready artwork generation\", \"Personalized audio memory preservation\", \"Professional-grade design output\", \"Multiple customization options for waveforms\"]', 'Music enthusiasts, gift shoppers, memory preservers, and design-conscious individuals looking to transform audio into visual art', '[\"Creating personalized gifts from voice messages or music\", \"Preserving special audio memories as visual art\", \"Generating unique wall decor from meaningful sounds\", \"Creating custom merchandise with audio waveforms\"]', 'paid', 'coming-soon', '[]', NULL, 'web-app', '[]', NULL, NULL, 5, 'pending', NULL, NULL, 0, 0, 0, '2025-08-19 16:08:21', '2025-08-19 16:08:21', NULL, NULL),
(114, 34, 'RegaloBot', 'regalobot', 'Personalized Gift Ideas Powered by AI', 'RegaloBot uses AI to generate unique, customized gift suggestions tailored to user preferences and occasions, streamlining decision-making for both individuals and businesses.', 'https://regalobot.com/it/', NULL, 'ai-tools', NULL, '[\"AI gifts\", \"personalized recommendations\", \"smart gifting\"]', '[\"AI-driven gift personalization engine\", \"Customizable preferences and filters\", \"Integration with e-commerce platforms\", \"User profile synchronization\", \"Real-time gift trend analysis\"]', 'Consumers and businesses seeking efficient, data-driven gifting solutions', '[\"Personal gifting (birthdays, weddings)\", \"Corporate event planning\", \"Holiday shopping automation\", \"E-commerce product bundling\"]', 'paid', 'coming-soon', '[]', NULL, 'web-app', '[]', NULL, NULL, 5, 'approved', '', NULL, 0, 0, 0, '2025-08-20 05:35:50', '2025-08-20 12:35:46', NULL, NULL),
(115, 34, 'Fabric', 'fabric', 'AI-Powered UX Design Partner to Accelerate Your Design Process', 'Fabric is an AI-powered UX design platform that enhances team collaboration and streamlines design processes through adaptive strategies and iterative improvements, built for seamless workflow alignment and scalability.', 'https://fabrichq.co/marketing', 'http://localhost/uploads/launches/logos/logo_34_1755697281_68a5d081045ca.jpg', 'design', NULL, '[\"AI UX design\", \"workflow automation\", \"team collaboration\"]', '[\"Persistent Product Memory\", \"UX Expertise Built-In\", \"Adaptive to Your Skill Level\", \"Live Strategy Alignment\", \"Iterative Flow Improvement\", \"Built for Teams\"]', 'UX and product teams in tech companies, digital agencies, and startups seeking to enhance design efficiency and team collaboration.', '[\"Streamlining UX design workflows for product teams\", \"Enhancing collaboration with real-time strategy alignment\", \"Optimizing design processes through adaptive skill-level support\"]', 'paid', 'coming-soon', '[\"http://localhost/uploads/launches/screenshots/screenshot_34_1755697295_68a5d08fda215.jpg\"]', NULL, 'chrome-extension', '[]', '', NULL, 5, 'pending', NULL, NULL, 0, 0, 0, '2025-08-20 13:40:28', '2025-08-20 13:41:43', NULL, NULL),
(116, 34, 'Ace Step', 'ace-step', 'Create music instantly with free AI tools for everyone', 'Ace Step enables users to transform ideas into songs in seconds using AI, offering free access to multiple music styles, multi-language lyrics, and partial editing features. Ideal for musicians, educators, and creators seeking fast, accessible music generation.', 'https://acestepai.app', NULL, 'content-creation', NULL, '[\"AI music\", \"music generation\", \"freemium\"]', '[\"Instant AI song generation in seconds\", \"Supports multiple music styles (e.g., pop, rock, electronic)\", \"Editable sections within generated tracks\", \"Multi-language lyric support\", \"No limits on generated music tracks\"]', 'Musicians, content creators, educators, and hobbyists needing fast, accessible AI music tools', '[\"Quick background music for social media content\", \"Educational tools for teaching music theory\", \"Personal project prototyping for songwriters\", \"Business branding with custom soundtracks\"]', 'freemium', 'coming-soon', '[]', NULL, NULL, '[]', NULL, NULL, 5, 'pending', NULL, NULL, 0, 0, 0, '2025-08-20 13:51:40', '2025-08-20 13:51:40', NULL, NULL),
(117, 34, 'Macroaxis AI', 'macroaxis-ai', 'AI-powered investment insights for smarter financial decisions', 'Macroaxis AI leverages artificial intelligence to provide sophisticated investment analysis and portfolio optimization tools. It helps investors make data-driven decisions with advanced market intelligence and personalized recommendations.', 'https://www.macroaxis.com/invest/ai', NULL, 'finance', NULL, '[\"investment\", \"ai-finance\", \"portfolio-management\"]', '[\"AI-driven investment analysis\", \"Portfolio optimization tools\", \"Market intelligence and insights\", \"Personalized investment recommendations\", \"Risk assessment and management\", \"Performance tracking and analytics\"]', 'Individual investors, financial advisors, and institutional investment professionals seeking AI-powered market insights', '[\"Individual investment portfolio management\", \"Financial advisory services\", \"Institutional investment strategies\", \"Market research and analysis\"]', 'unknown', 'coming-soon', '[]', NULL, 'discord-bot', '[]', NULL, NULL, 5, 'pending', NULL, NULL, 0, 0, 0, '2025-08-20 13:52:20', '2025-08-20 13:52:20', NULL, NULL),
(118, 34, 'Kain', 'kain', 'Transform your cooking experience with AI-powered recipe guidance', 'Kain is an innovative cooking platform that leverages artificial intelligence to provide personalized recipe recommendations and step-by-step cooking guidance. It helps users discover new dishes, master cooking techniques, and create memorable meals with confidence.', 'https://www.cookwithkain.com', NULL, 'content-creation', NULL, '[\"cooking\", \"ai-recipes\", \"meal-planning\"]', '[\"AI-powered recipe recommendations\", \"Personalized cooking guidance\", \"Step-by-step visual instructions\", \"Ingredient substitution suggestions\", \"Nutritional information tracking\", \"Meal planning capabilities\"]', 'Home cooking enthusiasts, culinary beginners, health-conscious individuals, and busy professionals seeking to improve their cooking skills and meal planning', '[\"Home cooks looking to expand their culinary repertoire\", \"Beginners seeking to build cooking confidence\", \"Health-conscious individuals tracking nutritional intake\", \"Busy professionals planning efficient weekly meals\"]', 'freemium', 'coming-soon', '[]', NULL, 'web-app', '[]', NULL, NULL, 5, 'pending', NULL, NULL, 0, 0, 0, '2025-08-20 13:52:59', '2025-08-20 13:52:59', NULL, NULL),
(119, 34, 'Nest Your CSS', 'nest-your-css', 'Simplify CSS Management with Smart Nesting, Unnesting, and Minification', 'Nest Your CSS streamlines web development by offering intuitive tools to nest, unnest, and minify CSS stylesheets, reducing code clutter and improving maintainability. Free and Pro tiers provide varying levels of functionality for developers and teams.', 'https://nestyourcss.com', NULL, 'developer-tools', NULL, '[\"css-formatting\", \"web-development\", \"code-minification\"]', '[\"Smart nesting/unnesting of CSS selectors\", \"Automatic code minification\", \"Custom nesting rules and templates\", \"Real-time syntax validation\", \"Team collaboration features (Pro)\"]', 'Front-end developers, web design teams, and project managers focused on efficient CSS management', '[\"Front-end development workflows\", \"Legacy codebase cleanup\", \"Cross-team design consistency\", \"Production-ready code optimization\"]', 'freemium', 'coming-soon', '[]', NULL, 'web-app', '[]', NULL, NULL, 5, 'pending', NULL, NULL, 0, 0, 0, '2025-08-20 13:57:23', '2025-08-20 13:57:23', NULL, NULL),
(120, 34, 'AI Brand Insights', 'ai-brand-insights', 'Transform your brand strategy with AI-powered market intelligence and competitive analysis', 'AI Brand Insights provides comprehensive market analysis and brand intelligence through advanced AI algorithms. It helps businesses understand their competitive landscape and make data-driven decisions to enhance their market position.', 'https://ai-brand-insights.com', NULL, 'marketing', NULL, '[\"brand-analytics\", \"market-intelligence\", \"ai-marketing\"]', '[\"Competitive brand analysis\", \"Market trend identification\", \"Brand positioning insights\", \"Performance benchmarking\", \"Strategic recommendation engine\", \"Customizable reporting dashboard\"]', 'Marketing professionals, brand managers, and business strategists seeking data-driven insights to enhance their brand positioning', '[\"Marketing strategy development\", \"Brand positioning optimization\", \"Competitive intelligence gathering\", \"Market opportunity identification\"]', 'unknown', 'coming-soon', '[]', NULL, 'saas', '[]', NULL, NULL, 5, 'pending', NULL, NULL, 0, 0, 0, '2025-08-20 14:04:36', '2025-08-20 14:04:36', NULL, NULL),
(121, 34, 'PeerVerified', 'peerverified', 'Hire with Verified Trust and AI-Driven Insights', 'PeerVerified offers AI-powered background verification and employee screening to help employers make confident hiring decisions. With real-time compliance checks, customizable reports, and integration tools, it reduces hiring risks while saving time.', 'https://peerverified.com', NULL, 'security', NULL, '[\"hiring-verifications\", \"ai-hiring-tools\", \"compliance软件\"]', '[\"AI-driven background verification\", \"Real-time candidate compliance checks\", \"Customizable screening reports\", \"Integration with HR management systems\", \"Compliance automation for 50+ industries\"]', 'HR departments, recruitment agencies, and hiring managers in mid-to-large organizations prioritizing compliance and risk reduction', '[\"Enterprise level pre-employment screening\", \"Freelancer vetting for agencies\", \"Contractor verification for compliance teams\", \"University campus hiring programs\"]', 'unknown', 'coming-soon', '[]', NULL, 'saas', '[]', NULL, NULL, 5, 'pending', NULL, NULL, 0, 0, 0, '2025-08-20 16:22:15', '2025-08-20 16:22:15', NULL, NULL),
(122, 111, 'PeerVerified', 'peerverified-1', 'Verify Candidates, Build Trust, Ensure Quality', 'PeerVerified provides comprehensive candidate verification tools to help businesses ensure authenticity, compliance, and quality in their hiring processes. Key features include background checks, identity validation, skill assessments, and compliance reporting.', 'https://peerverified.com', NULL, 'security', NULL, '[\"candidate-verification\", \"hiring-software\", \"background-checks\"]', '[\"Background and identity verification\", \"Skill assessment integration\", \"Compliance report generation\", \"Real-time candidate monitoring\", \"Customizable verification workflows\", \"Integration with HR platforms\"]', 'HR managers, recruitment teams, and businesses prioritizing hiring accuracy and regulatory compliance', '[\"Recruitment agencies verifying candidate trustworthiness\", \"Enterprises ensuring compliance in hiring\", \"Startups streamlining talent acquisition\", \"Gig economy employers validating contractor credentials\"]', 'freemium', 'coming-soon', '[]', NULL, 'web-app', '[]', NULL, NULL, 5, 'pending', NULL, NULL, 0, 0, 0, '2025-08-20 16:28:32', '2025-08-20 16:28:32', NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `pt_quota_usage`
--

CREATE TABLE `pt_quota_usage` (
  `id` int NOT NULL,
  `user_id` int NOT NULL COMMENT '用户ID',
  `action` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '操作类型',
  `quota_change` int NOT NULL COMMENT '配额变化（正数为消耗，负数为增加）',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '操作描述',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='配额使用记录表';

--
-- Dumping data for table `pt_quota_usage`
--

INSERT INTO `pt_quota_usage` (`id`, `user_id`, `action`, `quota_change`, `description`, `created_at`) VALUES
(1, 34, 'chat_interaction', 20, 'AI Chat Interaction', '2025-08-20 15:05:12'),
(2, 34, 'chat_interaction', 20, 'AI Chat Interaction', '2025-08-20 15:05:16'),
(3, 34, 'chat_interaction', 20, 'AI Chat Interaction', '2025-08-20 15:06:41');

-- --------------------------------------------------------

--
-- Table structure for table `pt_request_duplicates`
--

CREATE TABLE `pt_request_duplicates` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `title_hash` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '标题MD5哈希',
  `description_hash` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '描述MD5哈希',
  `request_id` int NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='需求防重复表';

--
-- Dumping data for table `pt_request_duplicates`
--

INSERT INTO `pt_request_duplicates` (`id`, `user_id`, `title_hash`, `description_hash`, `request_id`, `created_at`) VALUES
(1, 2, '30031ae15351b77d89b6861e55b892e4', '7830d15f473c7f97ca71de94248eacd9', 1, '2025-08-10 01:30:00'),
(2, 5, '1ce8bdc5fd66ea3aeecc4264e604313e', 'c1997022849f6617599f5f8f3c200d6b', 2, '2025-08-12 08:45:00'),
(3, 6, '933b89c42101c332cee27f4529465544', 'b8abd34739b172b3f713d321507406d8', 3, '2025-08-14 03:20:00'),
(4, 34, 'd052c89e3481edfa98f5e341e6bae3a8', 'ecf4cf48b458ba86a11301b76f9a6bf6', 4, '2025-08-16 00:15:00'),
(5, 2, 'd7991bd41dda519c371cb0e40494d705', '7a80dac31ad130d28418839f70b2e1ae', 5, '2025-08-11 06:30:00'),
(6, 5, 'e5076231dc788c8f75375a5303098efa', 'ad21e9bf0a0a626f4b24f925fa504beb', 6, '2025-08-13 02:20:00'),
(7, 6, '459018d8f5c6a1379f62d5a214b5a894', '66fcc0f5cca37f85b04827f43716ece9', 7, '2025-08-09 05:45:00'),
(8, 34, '5b81aa9e7f292ad5c40054d36ace80c1', '5eec79f3c273a38d8692a6f471c391e9', 8, '2025-08-15 01:30:00'),
(10, 5, '3a73a4c34d6c8b76f588612b7d468b26', '6697388795305175b76f50fa0afce21b', 10, '2025-08-16 04:45:00'),
(11, 6, 'a185925235be5d624645be2432a7f298', 'fceaa17e0adba4a6be2bf2bfbd5cb965', 11, '2025-08-08 08:30:00'),
(12, 34, 'fda8470c98369788073bf714c7de4900', '859eb826f72397536fdcde710b988598', 12, '2025-08-17 02:20:00'),
(14, 5, '24273eed2d25104eeebd5bd7a13a1051', '9ca61ef40dc3d39b2ec983aaddebd205', 14, '2025-08-15 06:30:00'),
(15, 6, '6828b0ec148dfcde05567af6f7ac89c4', '2a8e63e93333d9db123fd43305360528', 15, '2025-08-18 01:15:00'),
(16, 34, 'a3e63f0c31327f2a13623cbb1cfede3e', '5b8b766d8a9c6451fe84da1b82cfe64f', 16, '2025-08-18 03:30:00'),
(28, 34, 'c695955ecb2ddc0d569dadb08ae87edf', '3231c1cdbddb145e58c61d9a31c9988d', 47, '2025-08-19 02:47:35'),
(29, 34, 'd83acf40c8171b1b4cf2c0815e5a3aab', '5f037f7835c77603834aac147aeb0749', 48, '2025-08-19 02:49:08'),
(30, 34, '2827a1ca06af5b2e75eb05081dd563d8', 'ff6dec97e97ba9d330975bbb34c9ec04', 49, '2025-08-19 02:49:55'),
(32, 34, '269d87776bace8d080f587e16e51ab24', '7d61e082310ba37dca00a6ead18774fb', 51, '2025-08-19 02:51:16'),
(33, 57, '81da42f45289e81742b001e4baf03467', 'a8080f71e870277dec50e5aeb556ffc0', 52, '2025-08-19 02:54:02'),
(34, 57, '4233ab449f879fcf71392de253026786', '75f656f71369ce44334b63b65b7d04fd', 53, '2025-08-19 02:56:53'),
(35, 57, '002c8e5d52e863201d40ec5fd9922514', '889a32c3a9b9f47a6963402a4fe30384', 54, '2025-08-19 03:02:31'),
(36, 57, 'acf37a442cc884dafd9512ccdd2cdf8a', '74355ddfc2b7df44326fbf87796d4b1a', 55, '2025-08-19 03:04:18'),
(37, 57, 'c2b255f5e09ffb8433a702acbfa0dac1', '668e339e1c38a1564bfb3863be557c22', 56, '2025-08-19 03:09:47'),
(46, 34, 'dac57cf18b5068f12ee6c2931ce506f4', '764a7fdf6d04835c1107e1a1ecf5c473', 65, '2025-08-20 05:31:58'),
(47, 34, 'd6f3a363e08534e75501641408cfb554', '193e0e1f1f2875717339eeda5a9a0916', 66, '2025-08-20 13:12:08'),
(48, 34, 'eb0acc2e9b3a60f6029ef213e4f7a8c7', 'a601748dbe21c447cd2e3c018558d4f7', 67, '2025-08-20 13:23:32'),
(49, 34, 'c883bc1e32154d13f57df795e52e282c', 'fc1cca444d0fc42c8b6ab665fc7f86eb', 68, '2025-08-20 13:28:25'),
(50, 34, '7df1b2e17ea303c3100c7eb12c70eb4e', '6366aa1f02dd67e48e4b591ec6412fa0', 69, '2025-08-20 13:54:25'),
(51, 111, 'c6d123884761d739f9946c624690f7c8', '6e34515572dac2296891b0b92ad1da60', 70, '2025-08-20 16:28:02'),
(52, 112, '7ff2dd6190651988f20ad7c9085e8d32', '98b07fc5902af9dd6914b87365bcd42b', 71, '2025-08-21 05:47:39');

-- --------------------------------------------------------

--
-- Table structure for table `pt_request_votes`
--

CREATE TABLE `pt_request_votes` (
  `id` int NOT NULL,
  `request_id` int NOT NULL,
  `user_id` int NOT NULL,
  `vote_type` enum('up','down') COLLATE utf8mb4_unicode_ci DEFAULT 'up' COMMENT '投票类型',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='需求投票表';

--
-- Dumping data for table `pt_request_votes`
--

INSERT INTO `pt_request_votes` (`id`, `request_id`, `user_id`, `vote_type`, `created_at`) VALUES
(1, 1, 5, 'up', '2025-08-10 02:30:00'),
(2, 1, 6, 'up', '2025-08-10 03:15:00'),
(4, 2, 2, 'up', '2025-08-12 09:00:00'),
(5, 2, 6, 'up', '2025-08-12 10:30:00'),
(7, 3, 2, 'up', '2025-08-14 04:00:00'),
(8, 3, 5, 'up', '2025-08-14 06:30:00'),
(9, 4, 2, 'up', '2025-08-16 01:00:00'),
(10, 4, 5, 'up', '2025-08-16 02:30:00'),
(11, 5, 5, 'up', '2025-08-11 07:00:00'),
(12, 5, 6, 'up', '2025-08-11 08:20:00'),
(14, 6, 2, 'up', '2025-08-13 03:00:00'),
(15, 6, 34, 'up', '2025-08-13 05:30:00'),
(16, 7, 2, 'up', '2025-08-09 06:00:00'),
(17, 7, 5, 'up', '2025-08-09 07:30:00'),
(18, 8, 2, 'up', '2025-08-15 02:00:00'),
(19, 8, 5, 'up', '2025-08-15 03:30:00'),
(23, 10, 2, 'up', '2025-08-16 05:30:00'),
(24, 10, 6, 'up', '2025-08-16 07:20:00'),
(25, 11, 2, 'up', '2025-08-08 09:00:00'),
(26, 11, 5, 'up', '2025-08-08 10:30:00'),
(27, 12, 2, 'up', '2025-08-17 03:00:00'),
(28, 12, 5, 'up', '2025-08-17 04:30:00'),
(31, 14, 2, 'up', '2025-08-15 07:00:00'),
(32, 14, 6, 'up', '2025-08-16 02:30:00'),
(33, 14, 34, 'up', '2025-08-16 06:15:00'),
(34, 15, 2, 'up', '2025-08-18 02:00:00'),
(35, 15, 5, 'up', '2025-08-18 03:30:00'),
(36, 16, 2, 'up', '2025-08-18 04:00:00'),
(37, 16, 5, 'up', '2025-08-18 05:30:00'),
(49, 20, 34, 'up', '2025-08-18 15:25:53'),
(51, 16, 34, 'up', '2025-08-18 15:49:15'),
(54, 19, 34, 'up', '2025-08-18 16:49:47'),
(56, 10, 34, 'up', '2025-08-18 16:56:27'),
(57, 48, 58, 'up', '2025-08-19 05:29:19');

--
-- Triggers `pt_request_votes`
--
DELIMITER $$
CREATE TRIGGER `update_request_votes_count` AFTER INSERT ON `pt_request_votes` FOR EACH ROW BEGIN
    UPDATE pt_user_requests 
    SET votes = (
        SELECT COUNT(*) FROM pt_request_votes 
        WHERE request_id = NEW.request_id AND vote_type = 'up'
    ) - (
        SELECT COUNT(*) FROM pt_request_votes 
        WHERE request_id = NEW.request_id AND vote_type = 'down'
    )
    WHERE id = NEW.request_id;
END
$$
DELIMITER ;
DELIMITER $$
CREATE TRIGGER `update_request_votes_count_delete` AFTER DELETE ON `pt_request_votes` FOR EACH ROW BEGIN
    UPDATE pt_user_requests 
    SET votes = (
        SELECT COUNT(*) FROM pt_request_votes 
        WHERE request_id = OLD.request_id AND vote_type = 'up'
    ) - (
        SELECT COUNT(*) FROM pt_request_votes 
        WHERE request_id = OLD.request_id AND vote_type = 'down'
    )
    WHERE id = OLD.request_id;
END
$$
DELIMITER ;

-- --------------------------------------------------------

--
-- Table structure for table `pt_service_config`
--

CREATE TABLE `pt_service_config` (
  `id` int NOT NULL,
  `platform_id` int NOT NULL COMMENT '平台ID，关联pt_service_platform表',
  `config_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '配置键名',
  `config_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '配置值内容',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '配置项描述说明',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='服务配置表';

--
-- Dumping data for table `pt_service_config`
--

INSERT INTO `pt_service_config` (`id`, `platform_id`, `config_key`, `config_value`, `description`, `created_at`, `updated_at`) VALUES
(1, 3, 'app_code', 'KXTM3281', 'AiHubMix referral code for 10% discount', '2025-08-16 07:40:30', '2025-08-16 07:40:30'),
(2, 1, 'default_model', 'glm-4.5-flash', 'Default model for this platform', '2025-08-16 07:40:30', '2025-08-16 07:40:30'),
(3, 2, 'default_model', 'Qwen/Qwen3-8B', 'Default model for this platform', '2025-08-16 07:40:30', '2025-08-16 08:06:29'),
(4, 3, 'default_model', 'claude-sonnet-4-20250514', 'Default model for this platform', '2025-08-16 07:40:30', '2025-08-16 07:51:54'),
(5, 3, 'max_concurrent', '5', NULL, '2025-08-16 07:40:30', '2025-08-16 07:40:30'),
(6, 3, 'timeout', '30', NULL, '2025-08-16 07:40:30', '2025-08-16 07:40:30'),
(7, 1, 'max_concurrent', '5', NULL, '2025-08-16 07:40:30', '2025-08-16 07:40:30'),
(8, 1, 'timeout', '30', NULL, '2025-08-16 07:40:30', '2025-08-16 07:40:30'),
(9, 2, 'max_concurrent', '5', NULL, '2025-08-16 07:40:30', '2025-08-16 07:40:30'),
(10, 2, 'timeout', '30', NULL, '2025-08-16 07:40:30', '2025-08-16 07:40:30');

-- --------------------------------------------------------

--
-- Table structure for table `pt_service_key`
--

CREATE TABLE `pt_service_key` (
  `id` int NOT NULL,
  `platform_id` int NOT NULL COMMENT '平台ID，关联pt_service_platform表',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '密钥名称标识',
  `api_key` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'API访问密钥',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '启用状态：0-禁用，1-启用',
  `usage_count` int DEFAULT '0' COMMENT '使用次数统计',
  `last_used_at` timestamp NULL DEFAULT NULL COMMENT '最后使用时间',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='服务密钥表';

--
-- Dumping data for table `pt_service_key`
--

INSERT INTO `pt_service_key` (`id`, `platform_id`, `name`, `api_key`, `is_active`, `usage_count`, `last_used_at`, `created_at`, `updated_at`) VALUES
(1, 2, 'siliconFlow-1', 'sk-fehvttrlgxgxwomdpegycdldtqlumyxsawgvgblaquzhhrfz', 1, 0, NULL, '2025-08-16 07:40:30', '2025-08-20 05:18:31'),
(2, 1, 'bigModel-1', 'c01491ac241f6f6dd4b3ea3fe031e69e.FuGMMgihmALxM2Pi', 1, 143, '2025-08-21 07:36:18', '2025-08-16 07:40:30', '2025-08-21 07:36:18'),
(3, 3, 'aiHubMix-1', 'sk-eHEyTuYYHaDXFxYFEb12208085694b07A70dF4Bf5bB70e37', 1, 60, '2025-08-21 09:52:20', '2025-08-16 07:40:30', '2025-08-21 09:52:20'),
(4, 1, 'bigModel-2', '7acde22930be4220aa052fc33d799135.deE60VFxPoooPEVR', 1, 144, '2025-08-21 07:36:34', '2025-08-20 05:18:43', '2025-08-21 07:36:34'),
(5, 1, 'bigModel-3', 'dad1ca67ac2340369be39363fc080acd.zpBZeRZlB6FPpc2y', 1, 143, '2025-08-21 07:36:18', '2025-08-20 05:19:07', '2025-08-21 07:36:18'),
(6, 1, 'bigModel-4', '199e15d5207a45de8ddd6b01072534dd.AC1zc9xTeq15FZaf', 1, 143, '2025-08-21 07:38:11', '2025-08-20 05:19:18', '2025-08-21 07:38:11');

-- --------------------------------------------------------

--
-- Table structure for table `pt_service_model`
--

CREATE TABLE `pt_service_model` (
  `id` int NOT NULL,
  `platform_id` int NOT NULL COMMENT '平台ID，关联pt_service_platform表',
  `model_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模型代码标识',
  `model_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模型显示名称',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '模型功能描述',
  `max_tokens` int DEFAULT '4096' COMMENT '最大令牌数限制',
  `supports_vision` tinyint(1) DEFAULT '0' COMMENT '视觉支持：0-不支持，1-支持',
  `supports_audio` tinyint(1) DEFAULT '0' COMMENT '音频支持：0-不支持，1-支持',
  `supports_function_call` tinyint(1) DEFAULT '0' COMMENT '函数调用支持：0-不支持，1-支持',
  `is_free` tinyint(1) DEFAULT '0' COMMENT '免费模型：0-付费，1-免费',
  `model_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'chat' COMMENT '模型类型：chat-对话，image-图像，video-视频等',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '启用状态：0-禁用，1-启用',
  `sort_order` int DEFAULT '0' COMMENT '排序权重，数字越小越靠前',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='服务模型表';

--
-- Dumping data for table `pt_service_model`
--

INSERT INTO `pt_service_model` (`id`, `platform_id`, `model_code`, `model_name`, `description`, `max_tokens`, `supports_vision`, `supports_audio`, `supports_function_call`, `is_free`, `model_type`, `is_active`, `sort_order`, `created_at`, `updated_at`) VALUES
(1, 1, 'glm-4.5', 'GLM-4.5', 'Latest flagship model designed for intelligent agent applications', 32768, 0, 0, 1, 0, 'chat', 1, 0, '2025-08-16 07:34:22', '2025-08-16 07:53:45'),
(2, 1, 'glm-4.5-air', 'GLM-4.5-Air', 'Lightweight flagship model balancing performance and cost', 32768, 0, 0, 1, 0, 'chat', 1, 1, '2025-08-16 07:34:22', '2025-08-16 07:53:45'),
(3, 1, 'glm-4.5-x', 'GLM-4.5-X', 'Enhanced model with stronger reasoning capabilities', 32768, 0, 0, 1, 0, 'chat', 1, 2, '2025-08-16 07:34:22', '2025-08-16 07:53:45'),
(4, 1, 'glm-4.5-airx', 'GLM-4.5-AirX', 'Lightweight enhanced model with improved efficiency', 32768, 0, 0, 1, 0, 'chat', 1, 3, '2025-08-16 07:34:22', '2025-08-16 07:53:45'),
(5, 1, 'glm-4.5-flash', 'GLM-4.5-Flash', 'Completely free language model with fast inference', 128000, 0, 0, 1, 1, 'chat', 1, 0, '2025-08-16 07:34:22', '2025-08-16 08:06:22'),
(6, 1, 'glm-4-plus', 'GLM-4-Plus', 'Enhanced GLM-4 model with improved capabilities', 32768, 0, 0, 1, 0, 'chat', 1, 5, '2025-08-16 07:34:22', '2025-08-16 07:53:45'),
(7, 1, 'glm-4-air-250414', 'GLM-4-Air', 'Lightweight GLM-4 model for efficient processing', 32768, 0, 0, 1, 0, 'chat', 1, 6, '2025-08-16 07:34:22', '2025-08-16 07:53:45'),
(8, 1, 'glm-4-airx', 'GLM-4-AirX', 'GLM-4 lightweight enhanced version', 32768, 0, 0, 1, 0, 'chat', 1, 7, '2025-08-16 07:34:22', '2025-08-16 07:53:45'),
(9, 1, 'glm-4-flashx', 'GLM-4-FlashX', 'GLM-4 ultra-fast enhanced version', 32768, 0, 0, 1, 0, 'chat', 1, 8, '2025-08-16 07:34:22', '2025-08-16 07:53:45'),
(10, 1, 'glm-4-flashx-250414', 'GLM-4-FlashX-250414', 'GLM-4 ultra-fast enhanced version (250414)', 32768, 0, 0, 1, 0, 'chat', 1, 9, '2025-08-16 07:34:22', '2025-08-16 07:53:45'),
(11, 1, 'glm-z1-air', 'GLM-Z1-Air', 'Z1 series lightweight model', 32768, 0, 0, 1, 0, 'chat', 1, 10, '2025-08-16 07:34:22', '2025-08-16 07:53:45'),
(12, 1, 'glm-z1-airx', 'GLM-Z1-AirX', 'Z1 series lightweight enhanced version', 32768, 0, 0, 1, 0, 'chat', 1, 11, '2025-08-16 07:34:22', '2025-08-16 07:53:46'),
(13, 1, 'glm-z1-flash', 'GLM-Z1-Flash', 'Completely free deep thinking model', 128000, 0, 0, 1, 1, 'chat', 1, 1, '2025-08-16 07:34:22', '2025-08-16 08:06:22'),
(14, 1, 'glm-z1-flashx', 'GLM-Z1-FlashX', 'Z1 series ultra-fast enhanced version', 32768, 0, 0, 1, 0, 'chat', 1, 13, '2025-08-16 07:34:22', '2025-08-16 07:53:46'),
(15, 1, 'glm-4v', 'GLM-4V', 'Multimodal vision model for image understanding', 8192, 1, 0, 1, 0, 'chat', 1, 14, '2025-08-16 07:34:22', '2025-08-16 07:53:46'),
(16, 1, 'glm-4v-plus', 'GLM-4V-Plus', 'Enhanced multimodal model with advanced vision capabilities', 8192, 1, 0, 1, 0, 'chat', 1, 15, '2025-08-16 07:34:22', '2025-08-16 07:53:46'),
(17, 1, 'glm-4-voice', 'GLM-4-Voice', 'Voice conversation model for audio interactions', 8192, 0, 1, 1, 0, 'chat', 1, 16, '2025-08-16 07:34:22', '2025-08-16 07:53:46'),
(39, 3, 'claude-sonnet-4-20250514', 'Claude Sonnet 4', 'Latest Claude Sonnet 4 model with enhanced reasoning capabilities', 200000, 1, 0, 1, 0, 'chat', 1, 0, '2025-08-16 07:53:45', '2025-08-16 07:53:45'),
(40, 3, 'gpt-4.1', 'GPT-4.1', 'Latest GPT-4.1 model with improved performance', 128000, 1, 0, 1, 0, 'chat', 1, 1, '2025-08-16 07:53:45', '2025-08-16 07:53:45'),
(41, 3, 'gpt-4o-mini', 'GPT-4o Mini', 'Efficient and cost-effective GPT-4o mini model', 128000, 1, 0, 1, 0, 'chat', 1, 2, '2025-08-16 07:53:45', '2025-08-16 07:53:45'),
(42, 1, 'glm-4v-flash', 'GLM-4V-Flash', 'Supports image understanding, completely free', 128000, 1, 0, 1, 1, 'chat', 1, 2, '2025-08-16 08:06:22', '2025-08-16 08:06:22'),
(43, 1, 'cogview-3-flash', 'CogView-3-Flash', 'Supports text-to-image generation, completely free', 8192, 0, 0, 0, 1, 'image', 1, 3, '2025-08-16 08:06:22', '2025-08-16 08:10:53'),
(44, 1, 'cogvideox-flash', 'CogVideoX-Flash', 'Supports video generation, completely free', 8192, 0, 0, 0, 1, 'video', 1, 4, '2025-08-16 08:06:22', '2025-08-16 08:10:53'),
(45, 2, 'THUDM/GLM-4.1V-9B-Thinking', 'GLM-4.1V-9B-Thinking', 'Open-source visual language model with chain-of-thought reasoning, excellent for multimodal cognitive tasks', 64000, 1, 0, 1, 1, 'chat', 1, 0, '2025-08-16 08:06:28', '2025-08-16 08:06:28'),
(46, 2, 'deepseek-ai/DeepSeek-R1-0528-Qwen3-8B', 'DeepSeek-R1-0528-Qwen3-8B', 'SOTA performance model distilled from DeepSeek-R1, excellent for math reasoning and programming', 128000, 0, 0, 1, 1, 'chat', 1, 1, '2025-08-16 08:06:28', '2025-08-16 08:06:28'),
(47, 2, 'Qwen/Qwen3-8B', 'Qwen3-8B', 'Latest Qwen model supporting thinking and non-thinking modes, excellent for reasoning and multilingual tasks', 128000, 0, 0, 1, 1, 'chat', 1, 2, '2025-08-16 08:06:29', '2025-08-16 08:06:29'),
(48, 2, 'THUDM/GLM-Z1-9B-0414', 'GLM-Z1-9B-0414', 'Compact GLM model with 9B parameters, excellent balance of efficiency and performance', 128000, 0, 0, 1, 1, 'chat', 1, 3, '2025-08-16 08:06:29', '2025-08-16 08:06:29'),
(49, 2, 'THUDM/GLM-4-9B-0414', 'GLM-4-9B-0414', 'Lightweight GLM-4 model with 9B parameters, supports code generation and function calling', 32000, 0, 0, 1, 1, 'chat', 1, 4, '2025-08-16 08:06:29', '2025-08-16 08:06:29'),
(50, 2, 'deepseek-ai/DeepSeek-R1-Distill-Qwen-7B', 'DeepSeek-R1-Distill-Qwen-7B', 'Knowledge distilled model with excellent reasoning capabilities, strong in math and programming', 128000, 0, 0, 1, 1, 'chat', 1, 5, '2025-08-16 08:06:29', '2025-08-16 08:06:29'),
(51, 2, 'Qwen/Qwen2.5-7B-Instruct', 'Qwen2.5-7B-Instruct', 'Latest Qwen model with improved coding and math capabilities, supports 29+ languages', 32000, 0, 0, 1, 1, 'chat', 1, 6, '2025-08-16 08:06:29', '2025-08-16 08:06:29'),
(52, 2, 'Qwen/Qwen2.5-Coder-7B-Instruct', 'Qwen2.5-Coder-7B-Instruct', 'Code-specific model with enhanced coding, reasoning and repair capabilities', 32000, 0, 0, 1, 1, 'chat', 1, 7, '2025-08-16 08:06:29', '2025-08-16 08:06:29'),
(53, 2, 'internlm/internlm2_5-7b-chat', 'InternLM2.5-7B-Chat', 'Open-source dialogue model supporting Chinese and English interactions', 32000, 0, 0, 1, 1, 'chat', 1, 8, '2025-08-16 08:06:29', '2025-08-16 08:06:29'),
(54, 2, 'Qwen/Qwen2-7B-Instruct', 'Qwen2-7B-Instruct', 'Instruction-tuned model with excellent performance in language understanding and generation', 32000, 0, 0, 1, 1, 'chat', 1, 9, '2025-08-16 08:06:29', '2025-08-16 08:06:29'),
(55, 2, 'THUDM/glm-4-9b-chat', 'GLM-4-9B-Chat', 'Open-source GLM-4 model supporting 26 languages with advanced features like web browsing and code execution', 128000, 0, 0, 1, 1, 'chat', 1, 10, '2025-08-16 08:06:29', '2025-08-16 08:06:29'),
(56, 2, 'FunAudioLLM/SenseVoiceSmall', 'SenseVoice Small', 'Multi-language speech foundation model with ASR, LID, SER and AED capabilities, 15x faster than Whisper', 8192, 0, 1, 0, 1, 'audio', 1, 11, '2025-08-16 08:06:29', '2025-08-16 08:10:54'),
(57, 2, 'BAAI/bge-m3', 'BGE-M3', 'Multi-functional, multi-lingual, multi-granularity text embedding model supporting 100+ languages', 8192, 0, 0, 0, 1, 'embedding', 1, 12, '2025-08-16 08:06:29', '2025-08-16 08:10:54'),
(58, 2, 'BAAI/bge-reranker-v2-m3', 'BGE-Reranker-v2-M3', 'Lightweight multilingual reranking model with fast inference speed', 8192, 0, 0, 0, 1, 'embedding', 1, 13, '2025-08-16 08:06:29', '2025-08-16 08:10:54'),
(59, 2, 'netease-youdao/bce-embedding-base_v1', 'BCE-Embedding-Base-v1', 'Bilingual and cross-lingual embedding model optimized for RAG systems', 512, 0, 0, 0, 1, 'embedding', 1, 14, '2025-08-16 08:06:29', '2025-08-16 08:10:54'),
(60, 2, 'netease-youdao/bce-reranker-base_v1', 'BCE-Reranker-Base-v1', 'Bilingual and cross-lingual reranking model supporting Chinese, English, Japanese and Korean', 512, 0, 0, 0, 1, 'reranker', 1, 15, '2025-08-16 08:06:29', '2025-08-16 08:10:54'),
(61, 2, 'BAAI/bge-large-zh-v1.5', 'BGE-Large-ZH-v1.5', 'Large Chinese text embedding model with excellent performance on C-MTEB benchmark', 512, 0, 0, 0, 1, 'embedding', 1, 16, '2025-08-16 08:06:29', '2025-08-16 08:10:54'),
(62, 2, 'BAAI/bge-large-en-v1.5', 'BGE-Large-EN-v1.5', 'Large English text embedding model with excellent performance on MTEB benchmark', 512, 0, 0, 0, 1, 'embedding', 1, 17, '2025-08-16 08:06:29', '2025-08-16 08:10:54'),
(63, 2, 'Kwai-Kolors/Kolors', 'Kolors', 'Large-scale text-to-image generation model with excellent Chinese and English character rendering', 2048, 0, 0, 0, 1, 'image', 1, 18, '2025-08-16 08:06:29', '2025-08-16 08:10:54');

-- --------------------------------------------------------

--
-- Table structure for table `pt_service_platform`
--

CREATE TABLE `pt_service_platform` (
  `id` int NOT NULL,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '平台名称',
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '平台代码标识，唯一',
  `base_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'API基础URL地址',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '平台详细描述',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '启用状态：0-禁用，1-启用',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='服务平台表';

--
-- Dumping data for table `pt_service_platform`
--

INSERT INTO `pt_service_platform` (`id`, `name`, `code`, `base_url`, `description`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 'bigModel', 'bigmodel', 'https://open.bigmodel.cn/api/paas/v4', 'BigModel AI platform providing GLM series models with advanced reasoning capabilities', 1, '2025-08-16 07:34:22', '2025-08-16 08:00:47'),
(2, 'siliconFlow', 'siliconflow', 'https://api.siliconflow.cn/v1', 'SiliconFlow AI platform offering various open-source models including Qwen, DeepSeek, and Llama', 1, '2025-08-16 07:34:22', '2025-08-16 08:00:47'),
(3, 'aiHubMix', 'aihubmix', 'https://aihubmix.com/v1', 'AiHubMix AI platform aggregating multiple premium AI models including GPT, Claude, and Gemini', 1, '2025-08-16 07:34:22', '2025-08-16 08:00:47');

-- --------------------------------------------------------

--
-- Table structure for table `pt_system_config`
--

CREATE TABLE `pt_system_config` (
  `id` int NOT NULL,
  `setting_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '配置键名，唯一标识',
  `setting_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '配置值内容',
  `setting_type` enum('string','integer','boolean','json') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'string' COMMENT '配置值类型：string-字符串，integer-整数，boolean-布尔值，json-JSON对象',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '配置项描述说明',
  `is_public` tinyint(1) DEFAULT '0' COMMENT '是否公开：0-私有配置，1-公开配置',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `updated_by` int DEFAULT NULL COMMENT '更新者ID，关联pt_manager表'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

--
-- Dumping data for table `pt_system_config`
--

INSERT INTO `pt_system_config` (`id`, `setting_key`, `setting_value`, `setting_type`, `description`, `is_public`, `updated_at`, `updated_by`) VALUES
(1, 'site_name', 'Prompt2Tool', 'string', 'Website name', 1, '2025-08-15 12:26:52', NULL),
(2, 'site_description', 'Discover 100+ free online tools powered by AI. HTML formatter, CSS minifier, image converter, SEO analyzer, and more. Boost your productivity.', 'string', 'Meta description for homepage', 1, '2025-08-15 13:12:06', NULL),
(3, 'maintenance_mode', '0', 'boolean', 'Enable maintenance mode', 0, '2025-08-15 11:12:47', NULL),
(4, 'user_registration', '1', 'boolean', 'Allow user registration', 0, '2025-08-15 11:12:47', NULL),
(5, 'email_verification', '1', 'boolean', 'Require email verification', 0, '2025-08-15 11:12:47', NULL),
(6, 'default_api_quota', '1000', 'integer', 'Default API quota for new users', 0, '2025-08-15 11:12:47', NULL),
(7, 'site_title', 'Free AI-Powered Online Tools Platform - Prompt2Tool', 'string', 'Main website title for SEO', 1, '2025-08-15 13:12:06', NULL),
(9, 'site_keywords', 'online tools, free tools, AI tools, developer tools, productivity tools, HTML formatter, CSS minifier, image converter, SEO tools', 'string', 'Meta keywords for SEO', 1, '2025-08-15 12:54:22', NULL),
(10, 'og_title', 'Free AI-Powered Online Tools - Prompt2Tool', 'string', 'Open Graph title for social sharing', 1, '2025-08-15 13:12:06', NULL),
(11, 'og_description', 'Access 100+ free online tools powered by AI. Perfect for developers, designers, and digital marketers.', 'string', 'Open Graph description for social sharing', 1, '2025-08-15 12:54:22', NULL),
(12, 'og_image', '/assets/images/og-default.jpg', 'string', 'Open Graph image URL', 1, '2025-08-15 13:06:50', NULL),
(13, 'twitter_handle', '@Prompt2Tool', 'string', 'Twitter handle (with @)', 1, '2025-08-15 12:56:43', NULL),
(14, 'twitter_card', 'summary_large_image', 'string', 'Twitter card type', 1, '2025-08-15 12:56:43', NULL),
(15, 'company_description', 'AI-powered free online tools platform providing development, design, SEO, and various practical tools to boost your productivity.', 'string', 'Company description for footer', 1, '2025-08-15 12:56:43', NULL),
(16, 'copyright_text', 'Prompt2Tool. All rights reserved.', 'string', 'Copyright text for footer', 1, '2025-08-15 13:12:06', NULL),
(17, 'github_url', '', 'string', 'GitHub profile URL', 1, '2025-08-16 09:26:19', NULL),
(18, 'twitter_url', '', 'string', 'Twitter profile URL', 1, '2025-08-16 09:26:19', NULL),
(19, 'linkedin_url', '', 'string', 'LinkedIn profile URL', 1, '2025-08-16 09:26:19', NULL),
(20, 'contact_email', '<EMAIL>', 'string', 'Contact email address', 1, '2025-08-15 12:56:43', NULL),
(21, 'google_analytics_id', '', 'string', 'Google Analytics tracking ID', 1, '2025-08-15 12:56:43', NULL),
(22, 'facebook_pixel_id', '', 'string', 'Facebook Pixel ID', 1, '2025-08-15 12:56:43', NULL),
(101, 'google_adsense_id', '', 'string', 'Google AdSense publisher ID', 1, '2025-08-15 13:05:27', NULL),
(162, 'google_oauth_enabled', '1', 'string', 'Enable Google OAuth login functionality', 1, '2025-08-15 15:45:13', NULL),
(163, 'google_oauth_client_id', '32585891078-a0lgl8jcmjra1oglbeuvvr1er21d1fvk.apps.googleusercontent.com', 'string', 'Google OAuth Client ID for login integration', 1, '2025-08-15 15:45:13', NULL),
(164, 'google_oauth_client_secret', '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', 'string', 'Google OAuth Client Secret (keep secure)', 1, '2025-08-15 15:45:13', NULL),
(178, 'discord_url', '', 'string', 'System setting', 1, '2025-08-16 09:26:19', NULL),
(201, 'jwt_secret', '7K9mP2xR8vN3qL6wE1tY4uI0oA5sD9fG2hJ7kM8nB3cV6zX1qW4eR5tY8uI0pL9m3nQ7wE2rT5yU8iO1pA4sD6fG9hJ2kL5mN8bV1cX4zQ7wE0rT3yU6iO9pA2sD5fG8hJ1kL4mN7bV0cX3z', 'string', 'JWT secret key for API token signing (keep secure)', 0, '2025-08-20 12:27:20', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `pt_tech_categories`
--

CREATE TABLE `pt_tech_categories` (
  `id` int NOT NULL,
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `slug` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `sort_order` int DEFAULT '0',
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `pt_tech_categories`
--

INSERT INTO `pt_tech_categories` (`id`, `name`, `slug`, `description`, `sort_order`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 'SaaS', 'saas', 'Software as a Service', 1, 1, '2025-08-19 14:03:00', '2025-08-19 14:03:00'),
(2, 'Web Application', 'web-app', 'Web-based applications', 2, 1, '2025-08-19 14:03:00', '2025-08-19 14:03:00'),
(3, 'Mobile Application', 'mobile-app', 'Mobile apps for iOS/Android', 3, 1, '2025-08-19 14:03:00', '2025-08-19 14:03:00'),
(4, 'Desktop Application', 'desktop-app', 'Desktop software applications', 4, 1, '2025-08-19 14:03:00', '2025-08-19 14:03:00'),
(5, 'API/Service', 'api', 'APIs and web services', 5, 1, '2025-08-19 14:03:00', '2025-08-19 14:03:00'),
(6, 'Browser Extension', 'browser-extension', 'Browser extensions and add-ons', 6, 1, '2025-08-19 14:03:00', '2025-08-19 14:03:00'),
(7, 'CLI Tool', 'cli-tool', 'Command line tools', 7, 1, '2025-08-19 14:03:00', '2025-08-19 14:03:00'),
(8, 'Library/Framework', 'library-framework', 'Programming libraries and frameworks', 8, 1, '2025-08-19 14:03:00', '2025-08-19 14:03:00'),
(9, 'Plugin', 'plugin', 'Plugins and add-ons', 9, 1, '2025-08-19 14:03:00', '2025-08-19 14:03:00'),
(10, 'AI Model', 'ai-model', 'AI models and services', 10, 1, '2025-08-19 14:03:00', '2025-08-19 14:03:00'),
(11, 'Hardware', 'hardware', 'Hardware devices and IoT', 11, 1, '2025-08-19 14:03:00', '2025-08-19 14:03:00'),
(12, 'No-Code Platform', 'no-code-platform', 'No-code/low-code platforms', 12, 1, '2025-08-19 14:03:00', '2025-08-19 14:03:00'),
(13, 'Chrome Extension', 'chrome-extension', 'Chrome browser extensions', 13, 1, '2025-08-19 15:29:17', '2025-08-19 15:29:17'),
(14, 'WordPress Plugin', 'wordpress-plugin', 'WordPress plugins and themes', 14, 1, '2025-08-19 15:29:17', '2025-08-19 15:29:17'),
(15, 'Slack Bot', 'slack-bot', 'Slack bots and integrations', 15, 1, '2025-08-19 15:29:17', '2025-08-19 15:29:17'),
(16, 'Discord Bot', 'discord-bot', 'Discord bots and applications', 16, 1, '2025-08-19 15:29:17', '2025-08-19 15:29:17'),
(17, 'Figma Plugin', 'figma-plugin', 'Figma plugins and widgets', 17, 1, '2025-08-19 15:29:17', '2025-08-19 15:29:17'),
(18, 'VS Code Extension', 'vscode-extension', 'Visual Studio Code extensions', 18, 1, '2025-08-19 15:29:17', '2025-08-19 15:29:17');

-- --------------------------------------------------------

--
-- Table structure for table `pt_tool`
--

CREATE TABLE `pt_tool` (
  `id` int NOT NULL,
  `name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '工具名称',
  `slug` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'URL友好标识，唯一',
  `file_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'php' COMMENT '文件类型扩展名',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '工具功能描述',
  `category_id` int DEFAULT NULL COMMENT '分类ID，关联pt_tool_category表',
  `url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '工具访问URL',
  `icon` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '工具图标',
  `color` varchar(7) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '#4e73df' COMMENT '主题颜色代码',
  `tags` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '标签列表，逗号分隔',
  `features` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '功能特性描述',
  `sort_order` int DEFAULT '1' COMMENT '排序权重',
  `status` enum('active','inactive','coming_soon') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'active' COMMENT '状态：active-活跃，inactive-非活跃，coming_soon-即将推出',
  `is_featured` tinyint(1) DEFAULT '0' COMMENT '是否推荐：0-否，1-是',
  `rating` decimal(3,2) DEFAULT '0.00' COMMENT '用户评分，0.00-5.00',
  `rating_count` int DEFAULT '0' COMMENT '评分人数',
  `view_count` int NOT NULL DEFAULT '0' COMMENT '浏览次数统计',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_by` int DEFAULT NULL COMMENT '创建者用户ID，关联pt_member表'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='工具表';

--
-- Dumping data for table `pt_tool`
--

INSERT INTO `pt_tool` (`id`, `name`, `slug`, `file_type`, `description`, `category_id`, `url`, `icon`, `color`, `tags`, `features`, `sort_order`, `status`, `is_featured`, `rating`, `rating_count`, `view_count`, `created_at`, `updated_at`, `created_by`) VALUES
(1, 'HTML Formatter', 'html-formatter', 'php', 'Format and beautify your HTML code with proper indentation and structure for better readability.', 1, '/tools/html-formatter', '🔧', '#4e73df', 'HTML,Formatter,Code,Development', NULL, 1, 'active', 0, 0.00, 0, 21, '2025-08-16 04:43:49', '2025-08-21 05:00:13', 112),
(2, 'CSS Minifier', 'css-minifier', 'php', 'Compress and minify CSS files to reduce file size and improve website loading speed.', 1, '/tools/css-minifier', '📦', '#4e73df', 'CSS,Minify,Optimize,Development', NULL, 2, 'active', 0, 0.00, 0, 8, '2025-08-16 04:43:49', '2025-08-21 04:31:56', 112),
(3, 'JavaScript Formatter', 'js-formatter', 'php', 'Format and beautify JavaScript code with proper indentation and syntax highlighting.', 1, '/tools/js-formatter', '⚡', '#4e73df', 'JavaScript,Formatter,Code,Development', NULL, 3, 'active', 0, 0.00, 0, 3, '2025-08-16 04:43:49', '2025-08-21 04:19:54', 112),
(4, 'Image Converter', 'image-converter', 'php', 'Convert images between different formats like JPG, PNG, WebP, GIF with high quality output.', 8, '/tools/image-converter', '🖼️', '#4e73df', 'Image,Convert,Format,Design', NULL, 1, 'active', 1, 0.00, 0, 21, '2025-08-16 04:43:49', '2025-08-21 10:33:53', 112),
(5, 'Color Palette Generator', 'color-palette-generator', 'php', 'Generate beautiful color palettes and schemes for your design projects.', 2, '/tools/color-palette-generator', '🎨', '#4e73df', 'Color,Palette,Design,Generator', NULL, 2, 'active', 0, 0.00, 0, 7, '2025-08-16 04:43:49', '2025-08-21 10:09:43', 112),
(6, 'Text Counter', 'text-counter', 'php', 'Count characters, words, sentences, and paragraphs in your text with detailed statistics.', 10, '/tools/text-counter', '📊', '#4e73df', 'Text,Counter,Statistics,Productivity', NULL, 1, 'active', 0, 0.00, 0, 2, '2025-08-16 04:43:49', '2025-08-21 03:51:10', 112),
(7, 'UTM Builder', 'utm-builder', 'php', 'Generate UTM tracking parameters for your marketing campaigns to track traffic sources.', 4, '/tools/utm-builder', '📈', '#4e73df', 'UTM,Analytics,Marketing,Tracking', NULL, 1, 'active', 1, 0.00, 0, 12, '2025-08-16 04:43:49', '2025-08-21 10:35:21', 112),
(8, 'QR Code Generator', 'qr-generator', 'php', 'Generate QR codes for URLs, text, WiFi credentials, contact info, and other data types.', 5, '/tools/qr-generator', '📱', '#4e73df', 'QR Code,Generate,Utility,Mobile', NULL, 1, 'active', 1, 0.00, 0, 12, '2025-08-16 04:43:49', '2025-08-21 03:51:10', 112),
(9, 'Password Generator', 'password-generator', 'php', 'Generate strong, secure passwords with customizable length and character sets for better security.', 6, '/tools/password-generator', '🔐', '#4e73df', 'Password,Security,Generate,Safety', NULL, 1, 'active', 0, 0.00, 0, 7, '2025-08-16 04:43:49', '2025-08-21 03:53:42', 112),
(17, 'Quick Calc', 'quick-calc', 'php', 'Quick Calc is an AI-powered calculator that simplifies complex calculations with precision.', 16, NULL, '🔧', '#4e73df', 'AI_calculator, math_simplification, complex_calculation, precision_tool, intelligent_computation', NULL, 1, 'active', 0, 0.00, 0, 8, '2025-08-21 03:14:32', '2025-08-21 10:34:20', 112),
(21, 'Calc-Fusion', 'calc-fusion-1', 'php', 'A simple calculator tool that performs basic arithmetic operations.', 9, NULL, '🔧', '#4e73df', 'calculator, arithmetic, utilities', NULL, 1, 'active', 0, 0.00, 0, 3, '2025-08-21 09:18:49', '2025-08-21 11:32:29', 114);

-- --------------------------------------------------------

--
-- Table structure for table `pt_tool_category`
--

CREATE TABLE `pt_tool_category` (
  `id` int NOT NULL,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '分类名称',
  `slug` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'URL友好标识，唯一',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '分类描述',
  `icon` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '分类图标',
  `sort_order` int DEFAULT '1' COMMENT '排序权重',
  `status` enum('active','inactive') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'active' COMMENT '状态：active-活跃，inactive-非活跃',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='工具分类表';

--
-- Dumping data for table `pt_tool_category`
--

INSERT INTO `pt_tool_category` (`id`, `name`, `slug`, `description`, `icon`, `sort_order`, `status`, `created_at`, `updated_at`) VALUES
(1, 'Development', 'development', 'Tools for developers and programmers', '💻', 1, 'active', '2025-08-17 09:32:14', '2025-08-17 09:32:14'),
(2, 'Design', 'design', 'Creative and design tools', '🎨', 2, 'active', '2025-08-17 09:32:14', '2025-08-17 09:32:14'),
(3, 'Productivity', 'productivity', 'Tools to boost your productivity', '⚡', 3, 'active', '2025-08-17 09:32:14', '2025-08-17 09:32:14'),
(4, 'Marketing', 'marketing', 'Marketing and SEO tools', '📈', 4, 'active', '2025-08-17 09:32:14', '2025-08-17 09:32:14'),
(5, 'Utilities', 'utilities', 'General utility tools', '🛠️', 5, 'active', '2025-08-17 09:32:14', '2025-08-17 09:32:14'),
(6, 'Security', 'security', 'Security and privacy tools', '🔒', 6, 'active', '2025-08-17 09:32:14', '2025-08-17 09:32:14'),
(7, 'Games', 'games', 'Fun games and entertainment tools', '🎮', 7, 'active', '2025-08-17 09:32:14', '2025-08-17 09:32:14'),
(8, 'Converters', 'converters', 'Format and unit conversion tools', '🔄', 8, 'active', '2025-08-17 09:32:14', '2025-08-17 09:32:14'),
(9, 'Calculators', 'calculators', 'Mathematical and financial calculators', '🧮', 9, 'active', '2025-08-17 09:32:14', '2025-08-17 09:32:14'),
(10, 'Text', 'text', 'Text processing and manipulation tools', '📝', 10, 'active', '2025-08-17 09:32:14', '2025-08-17 09:32:14'),
(11, 'Image', 'image', 'Image editing and processing tools', '🖼️', 11, 'active', '2025-08-17 09:32:14', '2025-08-17 09:32:14'),
(12, 'PDF', 'pdf', 'PDF creation and manipulation tools', '📄', 12, 'active', '2025-08-17 09:32:14', '2025-08-17 09:32:14'),
(13, 'Network', 'network', 'Network and internet tools', '🌐', 13, 'active', '2025-08-17 09:32:14', '2025-08-17 09:32:14'),
(14, 'Analytics', 'analytics', 'Data analysis and statistics tools', '📊', 14, 'active', '2025-08-17 09:32:14', '2025-08-17 09:32:14'),
(15, 'Finance', 'finance', 'Financial and investment tools', '💰', 15, 'active', '2025-08-17 09:32:14', '2025-08-17 09:32:14'),
(16, 'AI', 'ai', 'Artificial intelligence powered tools', '🤖', 16, 'active', '2025-08-17 09:32:14', '2025-08-17 09:32:14'),
(17, 'QR', 'qr', 'QR code generation and scanning tools', '📱', 17, 'active', '2025-08-17 09:32:14', '2025-08-17 09:32:14'),
(18, 'Color', 'color', 'Color palette and design tools', '🎨', 18, 'active', '2025-08-17 09:32:14', '2025-08-17 09:32:14'),
(19, 'Hash', 'hash', 'Hashing and encoding tools', '🔐', 19, 'active', '2025-08-17 09:32:14', '2025-08-17 09:32:14'),
(20, 'JSON', 'json', 'JSON processing and validation tools', '📋', 20, 'active', '2025-08-17 09:32:14', '2025-08-17 09:32:14'),
(21, 'CSS', 'css', 'CSS generation and styling tools', '🎯', 21, 'active', '2025-08-17 09:32:14', '2025-08-17 09:32:14'),
(22, 'HTML', 'html', 'HTML generation and processing tools', '🌍', 22, 'active', '2025-08-17 09:32:14', '2025-08-17 09:32:14'),
(23, 'API', 'api', 'API testing and development tools', '🔌', 23, 'active', '2025-08-17 09:32:14', '2025-08-17 09:32:14'),
(24, 'Database', 'database', 'Database tools and utilities', '🗄️', 24, 'active', '2025-08-17 09:32:14', '2025-08-17 09:32:14'),
(25, 'Regex', 'regex', 'Regular expression tools', '🔍', 25, 'active', '2025-08-17 09:32:14', '2025-08-17 09:32:14'),
(26, 'Time', 'time', 'Date and time utilities', '⏰', 26, 'active', '2025-08-17 09:32:14', '2025-08-17 09:32:14'),
(27, 'Weather', 'weather', 'Weather and climate tools', '🌤️', 27, 'active', '2025-08-17 09:32:14', '2025-08-17 09:32:14'),
(28, 'Maps', 'maps', 'Location and mapping tools', '🗺️', 28, 'active', '2025-08-17 09:32:14', '2025-08-17 09:32:14'),
(29, 'Social', 'social', 'Social media and sharing tools', '📲', 29, 'active', '2025-08-17 09:32:14', '2025-08-17 09:32:14'),
(30, 'Email', 'email', 'Email utilities and validators', '📧', 30, 'active', '2025-08-17 09:32:14', '2025-08-17 09:32:14');

-- --------------------------------------------------------

--
-- Table structure for table `pt_tool_usage`
--

CREATE TABLE `pt_tool_usage` (
  `id` int NOT NULL,
  `tool_id` int NOT NULL COMMENT '工具ID，关联pt_tool表',
  `user_id` int DEFAULT NULL COMMENT '用户ID，关联pt_member表',
  `usage_type` enum('api_call','web_usage','batch_process') COLLATE utf8mb4_unicode_ci DEFAULT 'web_usage' COMMENT '使用类型',
  `input_data` json DEFAULT NULL COMMENT '输入数据',
  `output_data` json DEFAULT NULL COMMENT '输出数据',
  `tokens_used` int DEFAULT '0' COMMENT '使用的token数量',
  `processing_time` decimal(10,3) DEFAULT NULL COMMENT '处理时间(秒)',
  `status` enum('success','error','timeout','cancelled') COLLATE utf8mb4_unicode_ci DEFAULT 'success' COMMENT '执行状态',
  `error_message` text COLLATE utf8mb4_unicode_ci COMMENT '错误信息',
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'IP地址',
  `user_agent` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '用户代理',
  `used_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '使用时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='工具使用记录表';

--
-- Dumping data for table `pt_tool_usage`
--

INSERT INTO `pt_tool_usage` (`id`, `tool_id`, `user_id`, `usage_type`, `input_data`, `output_data`, `tokens_used`, `processing_time`, `status`, `error_message`, `ip_address`, `user_agent`, `used_at`) VALUES
(81, 1, 2, 'web_usage', '{\"query\": \"test tool 1\"}', '{\"result\": \"success\"}', 150, 1.200, 'success', NULL, '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '2025-08-20 15:26:36'),
(82, 2, 3, 'api_call', '{\"input\": \"generate code\"}', '{\"code\": \"function test() {}\"}', 300, 2.500, 'success', NULL, '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '2025-08-20 15:26:36'),
(83, 3, 15, 'web_usage', '{\"task\": \"analyze data\"}', '{\"analysis\": \"complete\"}', 200, 1.800, 'success', NULL, '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '2025-08-20 15:26:36'),
(84, 1, 16, 'web_usage', '{\"query\": \"test tool 1 again\"}', '{\"result\": \"success\"}', 120, 0.900, 'success', NULL, '*************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36', '2025-08-20 15:26:36'),
(85, 2, 2, 'api_call', '{\"input\": \"create function\"}', '{\"code\": \"const func = () => {}\"}', 250, 2.100, 'success', NULL, '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '2025-08-19 15:26:36'),
(86, 1, 5, 'web_usage', '{\"query\": \"tool usage\"}', '{\"result\": \"completed\"}', 180, 1.500, 'success', NULL, '*************', 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36', '2025-08-19 15:26:36'),
(87, 3, 6, 'web_usage', '{\"task\": \"process data\"}', '{\"status\": \"done\"}', 220, 1.700, 'success', NULL, '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '2025-08-19 15:26:36'),
(88, 2, 17, 'api_call', '{\"input\": \"generate script\"}', '{\"script\": \"#!/bin/bash\"}', 280, 2.300, 'success', NULL, '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '2025-08-19 15:26:36'),
(89, 1, 4, 'web_usage', '{\"query\": \"search tools\"}', '{\"results\": \"found 5 tools\"}', 160, 1.300, 'success', NULL, '*************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36', '2025-08-18 15:26:36'),
(90, 3, 18, 'web_usage', '{\"task\": \"validate input\"}', '{\"validation\": \"passed\"}', 190, 1.600, 'success', NULL, '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '2025-08-18 15:26:36'),
(91, 2, 19, 'api_call', '{\"input\": \"optimize code\"}', '{\"optimized\": \"true\"}', 320, 2.800, 'success', NULL, '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '2025-08-18 15:26:36'),
(92, 1, 3, 'web_usage', '{\"query\": \"find resources\"}', '{\"resources\": \"list of 10 items\"}', 140, 1.100, 'success', NULL, '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '2025-08-17 15:26:36'),
(93, 2, 7, 'api_call', '{\"input\": \"build component\"}', '{\"component\": \"React component\"}', 350, 3.200, 'success', NULL, '192.168.1.110', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '2025-08-17 15:26:36'),
(94, 3, 20, 'web_usage', '{\"task\": \"format data\"}', '{\"formatted\": \"JSON output\"}', 210, 1.900, 'success', NULL, '192.168.1.111', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '2025-08-17 15:26:36'),
(95, 1, 6, 'web_usage', '{\"query\": \"tool comparison\"}', '{\"comparison\": \"detailed analysis\"}', 170, 1.400, 'success', NULL, '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '2025-08-16 15:26:36'),
(96, 2, 21, 'api_call', '{\"input\": \"generate template\"}', '{\"template\": \"HTML template\"}', 290, 2.600, 'success', NULL, '192.168.1.112', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '2025-08-16 15:26:36'),
(97, 3, 8, 'web_usage', '{\"task\": \"clean data\"}', '{\"cleaned\": \"dataset ready\"}', 230, 2.000, 'success', NULL, '192.168.1.113', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '2025-08-15 15:26:36'),
(98, 1, 22, 'web_usage', '{\"query\": \"performance test\"}', '{\"performance\": \"excellent\"}', 130, 0.800, 'success', NULL, '192.168.1.114', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '2025-08-15 15:26:36'),
(99, 2, 5, 'api_call', '{\"input\": \"create API\"}', '{\"api\": \"REST endpoint\"}', 400, 3.500, 'success', NULL, '*************', 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36', '2025-08-14 15:26:36'),
(100, 3, 16, 'web_usage', '{\"task\": \"export data\"}', '{\"export\": \"CSV file\"}', 240, 2.200, 'success', NULL, '*************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36', '2025-08-14 15:26:36'),
(101, 1, 17, 'web_usage', '{\"query\": \"debug tool\"}', '{\"debug\": \"issues resolved\"}', 155, 1.200, 'success', NULL, '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', '2025-08-14 15:26:36'),
(102, 5, 34, 'web_usage', NULL, NULL, 0, NULL, 'success', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-20 15:28:50'),
(103, 5, 34, 'web_usage', NULL, NULL, 0, NULL, 'success', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-20 15:36:36'),
(104, 1, 34, 'web_usage', NULL, NULL, 0, NULL, 'success', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-20 15:59:35'),
(105, 2, 111, 'web_usage', NULL, NULL, 0, NULL, 'success', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-20 16:49:47'),
(106, 1, 111, 'web_usage', NULL, NULL, 0, NULL, 'success', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-20 16:49:56'),
(107, 17, 112, 'web_usage', NULL, NULL, 0, NULL, 'success', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 03:14:51'),
(108, 4, 112, 'web_usage', NULL, NULL, 0, NULL, 'success', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 03:15:04'),
(109, 17, 112, 'web_usage', NULL, NULL, 0, NULL, 'success', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 03:15:07'),
(110, 17, 112, 'web_usage', NULL, NULL, 0, NULL, 'success', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 03:17:12'),
(111, 9, 112, 'web_usage', NULL, NULL, 0, NULL, 'success', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 03:53:42'),
(114, 17, 112, 'web_usage', NULL, NULL, 0, NULL, 'success', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 04:08:28'),
(124, 17, 112, 'web_usage', NULL, NULL, 0, NULL, 'success', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 05:00:04'),
(125, 1, 112, 'web_usage', NULL, NULL, 0, NULL, 'success', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 05:00:13'),
(127, 17, 114, 'web_usage', NULL, NULL, 0, NULL, 'success', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 09:10:59'),
(128, 17, 114, 'web_usage', NULL, NULL, 0, NULL, 'success', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 09:11:09'),
(129, 21, 114, 'web_usage', NULL, NULL, 0, NULL, 'success', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 09:18:52'),
(130, 5, 115, 'web_usage', NULL, NULL, 0, NULL, 'success', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 10:03:50'),
(131, 5, 115, 'web_usage', NULL, NULL, 0, NULL, 'success', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 10:09:43'),
(132, 4, 115, 'web_usage', NULL, NULL, 0, NULL, 'success', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 10:33:53'),
(133, 7, 115, 'web_usage', NULL, NULL, 0, NULL, 'success', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 10:35:21'),
(134, 21, 115, 'web_usage', NULL, NULL, 0, NULL, 'success', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 10:36:03'),
(135, 21, 115, 'web_usage', NULL, NULL, 0, NULL, 'success', NULL, '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-08-21 11:32:29');

-- --------------------------------------------------------

--
-- Table structure for table `pt_user_favorites`
--

CREATE TABLE `pt_user_favorites` (
  `id` int NOT NULL COMMENT '主键ID',
  `user_id` int NOT NULL COMMENT '用户ID，关联pt_member表',
  `tool_id` int NOT NULL COMMENT '工具ID，关联pt_tool表',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '收藏时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户收藏工具表';

--
-- Dumping data for table `pt_user_favorites`
--

INSERT INTO `pt_user_favorites` (`id`, `user_id`, `tool_id`, `created_at`) VALUES
(1, 34, 2, '2025-08-20 03:46:23'),
(6, 34, 4, '2025-08-20 03:56:54'),
(7, 34, 8, '2025-08-20 11:15:32'),
(9, 34, 5, '2025-08-20 15:36:38'),
(10, 34, 1, '2025-08-20 15:59:38'),
(11, 111, 1, '2025-08-20 16:49:58');

-- --------------------------------------------------------

--
-- Table structure for table `pt_user_requests`
--

CREATE TABLE `pt_user_requests` (
  `id` int NOT NULL,
  `user_id` int NOT NULL COMMENT '用户ID，关联pt_member表',
  `title` varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '需求标题',
  `slug` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'SEO友好的URL标识符',
  `description` text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '需求详细描述',
  `category` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT 'general' COMMENT '需求分类：development,design,productivity,marketing,utilities,other',
  `priority` enum('low','medium','high') COLLATE utf8mb4_unicode_ci DEFAULT 'medium' COMMENT '优先级',
  `status` enum('pending','reviewing','accepted','rejected','completed') COLLATE utf8mb4_unicode_ci DEFAULT 'pending' COMMENT '需求状态',
  `admin_reply` text COLLATE utf8mb4_unicode_ci COMMENT '管理员回复',
  `admin_id` int DEFAULT NULL COMMENT '处理管理员ID',
  `votes` int DEFAULT '0' COMMENT '支持票数',
  `ip_address` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '提交IP',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `processed_at` timestamp NULL DEFAULT NULL COMMENT '处理时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户需求表';

--
-- Dumping data for table `pt_user_requests`
--

INSERT INTO `pt_user_requests` (`id`, `user_id`, `title`, `slug`, `description`, `category`, `priority`, `status`, `admin_reply`, `admin_id`, `votes`, `ip_address`, `created_at`, `updated_at`, `processed_at`) VALUES
(1, 2, 'JSON Formatter Tool', 'request-1', 'Would love to have a JSON formatting and validation tool with syntax highlighting and error detection', 'development', 'medium', 'completed', NULL, NULL, 2, NULL, '2025-08-15 02:00:00', '2025-08-19 03:54:16', NULL),
(2, 3, 'PDF to Word Converter', 'request-2', 'Need an online tool that can convert PDF files to Word documents with good formatting preservation', 'productivity', 'medium', 'accepted', NULL, NULL, 2, NULL, '2025-08-16 06:30:00', '2025-08-19 03:54:16', NULL),
(3, 5, 'AI Logo Generator', 'request-3', 'Hope to have an AI-powered logo design tool that can generate logos based on company name and industry', 'design', 'medium', 'reviewing', NULL, NULL, 2, NULL, '2025-08-17 01:15:00', '2025-08-19 03:54:16', NULL),
(4, 6, 'Markdown Editor', 'request-4', 'Need an online Markdown editor with live preview and export functionality', 'productivity', 'medium', 'pending', NULL, NULL, 2, NULL, '2025-08-18 00:30:00', '2025-08-19 03:54:16', NULL),
(5, 2, 'QR Code Generator', 'request-5', 'Would like a QR code generator that supports various types and custom styling options', 'utilities', 'medium', 'accepted', NULL, NULL, 2, NULL, '2025-08-18 02:15:00', '2025-08-19 03:54:16', NULL),
(6, 2, 'JSON Formatter and Validator', 'request-6', 'Need a comprehensive JSON formatting tool with syntax highlighting, error detection, and minification options. Should support large files and provide clear error messages.', 'development', 'high', 'completed', 'Great suggestion! We have implemented this tool and it is now available in our development tools section.', 2, 2, '127.0.0.1', '2025-08-10 01:30:00', '2025-08-19 03:54:16', '2025-08-12 06:20:00'),
(7, 5, 'SQL Query Builder', 'request-7', 'Would love to have a visual SQL query builder that can generate complex queries with joins, subqueries, and aggregations. Should support multiple database types.', 'development', 'medium', 'accepted', 'This is a fantastic idea! We are currently working on this feature and expect to release it in the next update.', 2, 2, '*************', '2025-08-12 08:45:00', '2025-08-19 03:54:16', '2025-08-15 02:30:00'),
(8, 6, 'API Documentation Generator', 'request-8', 'An automated tool that can generate beautiful API documentation from OpenAPI/Swagger specs with interactive examples and testing capabilities.', 'development', 'medium', 'reviewing', NULL, NULL, 2, '********', '2025-08-14 03:20:00', '2025-08-19 03:54:16', NULL),
(10, 2, 'Color Palette Generator', 'request-10', 'AI-powered color palette generator that can create harmonious color schemes based on a base color, mood, or uploaded image. Should export in various formats.', 'design', 'medium', 'accepted', 'We love this idea! Our design team is already working on implementing this feature with AI assistance.', 2, 3, '127.0.0.1', '2025-08-11 06:30:00', '2025-08-19 03:54:16', '2025-08-13 01:45:00'),
(11, 5, 'Logo Maker with AI', 'request-11', 'An intelligent logo creation tool that can generate professional logos based on company name, industry, and style preferences. Should offer customization options.', 'design', 'high', 'reviewing', NULL, NULL, 2, '*************', '2025-08-13 02:20:00', '2025-08-19 03:54:16', NULL),
(12, 6, 'Favicon Generator', 'request-12', 'Tool to create favicons in multiple sizes and formats from uploaded images or text. Should generate all necessary files for web deployment.', 'design', 'low', 'completed', 'This tool has been implemented and is available in our design tools section!', 2, 2, '********', '2025-08-09 05:45:00', '2025-08-19 03:54:16', '2025-08-11 08:20:00'),
(14, 2, 'QR Code Generator Pro', 'request-14', 'Advanced QR code generator with custom styling, logo embedding, batch generation, and analytics tracking. Support for various data types.', 'utilities', 'medium', 'accepted', 'This is exactly what our users need! We are prioritizing this feature for the next release.', 2, 3, '127.0.0.1', '2025-08-14 07:20:00', '2025-08-19 03:54:16', '2025-08-16 03:30:00'),
(15, 5, 'Password Manager Integration', 'request-15', 'Secure password generator with strength analysis, breach checking, and integration with popular password managers. Should support custom rules.', 'utilities', 'high', 'reviewing', NULL, NULL, 2, '*************', '2025-08-16 04:45:00', '2025-08-19 03:54:16', NULL),
(16, 6, 'Meta Tags Generator', 'request-16', 'Comprehensive meta tags generator for SEO optimization with preview functionality, social media cards, and best practices recommendations.', 'marketing', 'medium', 'completed', 'This tool is now live! Check out our new SEO meta tags generator in the marketing tools section.', 2, 3, '********', '2025-08-08 08:30:00', '2025-08-19 03:54:16', '2025-08-10 06:15:00'),
(18, 2, 'Unit Converter Pro', 'request-18', 'Comprehensive unit converter supporting length, weight, temperature, currency, and more with historical exchange rates and offline capability.', 'utilities', 'low', 'pending', 'While this is a good idea, we already have several unit conversion tools available. We recommend checking our existing utilities section.', 2, 3, '127.0.0.1', '2025-08-07 03:15:00', '2025-08-19 03:54:16', '2025-08-18 14:36:10'),
(19, 5, 'Text Encryption Tool', 'request-19', 'Secure text encryption and decryption tool supporting multiple algorithms (AES, RSA) with key generation and secure sharing options.', 'utilities', 'high', 'accepted', 'Security is important to us! We are working on implementing this with the highest security standards.', 2, 1, '*************', '2025-08-15 06:30:00', '2025-08-19 03:54:16', '2025-08-17 00:45:00'),
(20, 6, 'AI Content Summarizer', 'request-20', 'Intelligent text summarization tool that can extract key points from long articles, documents, or web pages using advanced AI algorithms.', 'productivity', 'high', 'pending', NULL, NULL, 1, '********', '2025-08-18 01:15:00', '2025-08-19 03:54:16', NULL),
(33, 2, 'AI Code Assistant with Real-time Collaboration', 'request-33', 'Revolutionary AI-powered code assistant that provides intelligent code completion, bug detection, and real-time collaboration features. Should integrate with popular IDEs and support multiple programming languages with advanced debugging capabilities.', 'development', 'high', 'accepted', 'This is an amazing idea! Our development team is excited to work on this cutting-edge feature.', 2, 156, '127.0.0.1', '2025-08-01 02:00:00', '2025-08-19 03:54:16', '2025-08-05 06:30:00'),
(34, 3, 'Universal File Converter with AI Enhancement', 'request-34', 'Ultimate file conversion tool supporting 200+ formats with AI-powered optimization, batch processing, and cloud integration. Should handle documents, images, videos, audio files with quality preservation and format recommendations.', 'productivity', 'high', 'reviewing', NULL, NULL, 127, '************', '2025-08-02 00:30:00', '2025-08-19 03:54:16', NULL),
(35, 5, 'Advanced Data Visualization Dashboard Builder', 'request-35', 'Professional dashboard creation tool with drag-and-drop interface, real-time data connections, interactive charts, and customizable widgets. Should support multiple data sources and export options.', 'development', 'medium', 'accepted', 'Data visualization is crucial for modern businesses. We are prioritizing this feature!', 2, 78, '*********', '2025-08-03 06:20:00', '2025-08-19 03:54:16', '2025-08-08 01:15:00'),
(36, 6, 'Smart Email Template Generator with AI', 'request-36', 'Intelligent email template creator using AI to generate professional emails for various purposes. Should include personalization, A/B testing, and performance analytics with industry-specific templates.', 'marketing', 'medium', 'pending', NULL, NULL, 63, '***********', '2025-08-04 03:45:00', '2025-08-19 03:54:16', NULL),
(37, 2, 'Automated Testing Framework Generator', 'request-37', 'Comprehensive testing framework that automatically generates test cases, performs regression testing, and provides detailed reports. Should support web, mobile, and API testing with CI/CD integration.', 'development', 'high', 'reviewing', NULL, NULL, 34, '127.0.0.1', '2025-08-05 08:10:00', '2025-08-19 03:54:16', NULL),
(38, 3, 'Social Media Content Scheduler Pro', 'request-38', 'Advanced social media management tool with AI-powered content suggestions, optimal posting times, hashtag recommendations, and cross-platform scheduling with analytics dashboard.', 'marketing', 'medium', 'accepted', 'Social media automation is in high demand. We are working on this feature!', 2, 28, '192.168.1.75', '2025-08-06 01:30:00', '2025-08-19 03:54:16', '2025-08-10 07:45:00'),
(39, 5, 'Interactive Learning Path Creator', 'request-39', 'Educational tool that creates personalized learning paths with interactive modules, progress tracking, and adaptive difficulty adjustment. Should support multimedia content and collaborative learning.', 'productivity', 'medium', 'pending', NULL, NULL, 15, '*********', '2025-08-07 05:25:00', '2025-08-19 03:54:16', NULL),
(40, 6, 'Smart Invoice Generator with Automation', 'request-40', 'Professional invoice creation tool with automated calculations, tax handling, payment tracking, and client management. Should integrate with accounting software and support multiple currencies.', 'productivity', 'low', 'completed', 'This tool has been successfully implemented and is available in our productivity section!', 2, 12, '***********', '2025-08-07 23:50:00', '2025-08-19 03:54:16', '2025-08-12 03:20:00'),
(41, 2, 'Website Performance Analyzer', 'request-41', 'Comprehensive website analysis tool that checks loading speed, SEO optimization, accessibility, and provides actionable recommendations for improvement with detailed reports.', 'utilities', 'medium', 'reviewing', NULL, NULL, 7, '127.0.0.1', '2025-08-09 04:15:00', '2025-08-19 03:54:16', NULL),
(42, 3, 'Custom CSS Animation Builder', 'request-42', 'Visual CSS animation creator with timeline editor, keyframe management, and code export functionality. Should include preset animations and responsive design support.', 'design', 'low', 'pending', NULL, NULL, 6, '*************', '2025-08-10 07:40:00', '2025-08-19 03:54:16', NULL),
(43, 5, 'Text-to-Speech Converter with Voice Options', 'request-43', 'Advanced text-to-speech tool with multiple voice options, speed control, and audio export capabilities. Should support different languages and accent variations.', 'utilities', 'low', 'pending', NULL, NULL, 3, '*********', '2025-08-11 02:20:00', '2025-08-19 03:54:16', NULL),
(44, 6, 'Simple Habit Tracker', 'request-44', 'Basic habit tracking application with daily check-ins, streak counting, and simple progress visualization. Should have clean interface and reminder notifications.', 'productivity', 'low', 'pending', NULL, NULL, 2, '***********', '2025-08-12 06:30:00', '2025-08-19 03:54:16', NULL),
(45, 2, 'Basic Color Picker Tool', 'request-45', 'Simple color picker utility for web developers with hex, RGB, and HSL support. Should include color palette saving and copy-to-clipboard functionality.', 'design', 'low', 'pending', NULL, NULL, 0, '127.0.0.1', '2025-08-18 08:45:00', '2025-08-19 03:54:16', NULL),
(46, 3, 'URL Shortener Service', 'request-46', 'Basic URL shortening service with custom aliases, click tracking, and expiration dates. Should provide simple analytics and QR code generation.', 'utilities', 'low', 'pending', NULL, NULL, 0, '*************', '2025-08-18 09:00:00', '2025-08-19 03:54:16', NULL),
(47, 34, 'Test File Generator for Developers', 'request-47', 'A desktop application that generates sample files of various types and sizes for testing purposes. Target users: developers, testers, and product managers who need to validate file upload functionality, test performance with different file sizes, and create demo content without using sensitive data. Key features: support for 10+ file formats (documents, media, archives), local file generation for privacy, customizable file sizes, drag-and-drop integration, and platform-specific optimizations. Business value: accelerates testing cycles, eliminates dependency on finding sample files, and ensures applications handle various file types and sizes correctly.', 'development', 'medium', 'pending', NULL, NULL, 0, '::1', '2025-08-19 02:47:35', '2025-08-19 03:54:16', NULL),
(48, 34, 'Automated Mobile App Testing Platform with Natural Language Scripting', 'request-48', 'A no-code testing platform that enables QA teams and developers to create end-to-end tests for mobile applications using natural language commands. The tool should support both iOS and Android platforms across all major frameworks, with self-healing capabilities that adapt to UI changes. Features should include visual test builder, automated test execution, detailed reporting, and integration with CI/CD pipelines. Target users are mobile app developers, QA engineers, and product managers seeking to reduce testing time and improve app quality without requiring specialized coding skills.', 'development', 'medium', 'pending', NULL, NULL, 1, '::1', '2025-08-19 02:49:08', '2025-08-19 05:29:19', NULL),
(49, 34, 'AI-Powered Email Productivity Suite with Task Extraction', 'request-49', 'Develop an AI-powered email client that automatically transforms incoming emails into actionable summaries, generates quick reply suggestions, and extracts AI-powered to-do items. The tool should target professionals overwhelmed with email volume, helping them process and respond to messages efficiently. Key functionality includes: email summarization, smart categorization, automated task extraction with due dates, priority assignment, and integration with popular calendar/to-do applications. This tool would provide significant value by reducing email processing time, improving response rates, and ensuring important tasks are captured and managed across work platforms.', 'productivity', 'medium', 'pending', NULL, NULL, 0, '::1', '2025-08-19 02:49:55', '2025-08-19 03:54:16', NULL),
(51, 34, 'Voice-Driven AI Assistant for Email Management and Daily Task Planning', 'request-51', 'Develop a voice-first AI assistant that automates email processing, inbox management, and daily planning. The tool should allow users to interact via voice commands to triage emails, schedule tasks, set reminders, and organize calendar events. Target professionals seeking to optimize their workflow during commutes, breaks, or multitasking scenarios. Key features include natural language processing for email categorization, integration with popular email platforms, task prioritization algorithms, and cross-device synchronization. The assistant should learn user preferences over time to provide increasingly personalized assistance, ultimately reducing administrative burden while enhancing productivity and time management capabilities.', 'productivity', 'medium', 'pending', NULL, NULL, 0, '::1', '2025-08-19 02:51:16', '2025-08-19 03:54:16', NULL),
(52, 57, 'Carbon-Aware CI/CD Runner with Multi-Cloud Optimization', 'request-52', 'A CI/CD tool that automatically routes workflow jobs to low-carbon cloud regions, reducing environmental impact and operational costs. The tool should integrate with major CI platforms via a simple configuration change, monitoring real-time carbon intensity data across multiple cloud providers. Features include: automatic region selection based on current carbon emissions, multi-cloud support for resilience, carbon savings tracking, cost optimization, and reporting capabilities. Target audience is development teams and organizations seeking to reduce their carbon footprint without compromising CI performance. The tool would provide measurable environmental impact data while maintaining or improving build times and reducing costs by approximately 25%.', 'development', 'medium', 'pending', NULL, NULL, 0, '::1', '2025-08-19 02:54:02', '2025-08-19 03:54:16', NULL),
(53, 57, 'AI-Powered Investment Research and Strategy Platform', 'request-53', 'Develop an AI-driven investment platform that combines natural language processing with multi-layered decision systems. Features should include: 1) AI strategy generation studio that creates investment strategies based on user input with real-time factor optimization, 2) Signal processing engine that filters noise from 85+ platforms to provide market sentiment analysis with 65%+ improved signal-to-noise ratio, 3) Three-tier industry chain risk monitoring with millisecond-level alerts, 4) Dynamic visualization tools for strategy modeling and adjustment, 5) Cross-validation against 6000+ authoritative data sources. Target users include quantitative traders, investment analysts, and portfolio managers who need to identify market opportunities and risks faster than competitors. The platform should reduce research time while improving decision accuracy through AI-powered insights.', 'other', 'medium', 'pending', NULL, NULL, 0, '::1', '2025-08-19 02:56:53', '2025-08-19 03:54:16', NULL),
(54, 57, 'AI-Powered Architectural Rendering Tool for Sketch-to-Photorealistic Transformation', 'request-54', 'Develop an AI-powered tool that transforms architectural sketches into photorealistic renders in under 2 minutes. The solution should support multiple architectural styles (15+), custom style options, and 4K resolution output. Target users include architects, designers, builders, and homeowners. Key features include sketch upload, style selection, real-time preview, batch processing, and export options. The tool must leverage advanced AI models for realistic lighting, materials, and textures. Business value lies in accelerating design visualization, reducing rendering costs, and enabling quick iterations for architectural projects.', 'other', 'medium', 'pending', NULL, NULL, 0, '::1', '2025-08-19 03:02:31', '2025-08-19 03:54:16', NULL),
(55, 57, 'Develop a post-quantum encrypted email platform with sender verification and anti-phishing protection', 'request-55', 'Create an encrypted email solution that implements post-quantum cryptography to protect against future quantum computing threats. The tool should include robust sender verification mechanisms to prevent phishing and spoofing, comprehensive spam filtering, and end-to-end encryption. Target users include privacy-conscious professionals, security researchers, and organizations handling sensitive data. The platform should offer a user-friendly interface while maintaining strong security protocols, with both free and premium tiers to accommodate different user needs. Integration with existing email protocols would enhance adoption, while regular security audits would ensure ongoing protection against emerging threats.', 'other', 'medium', 'pending', NULL, NULL, 0, '::1', '2025-08-19 03:04:18', '2025-08-19 03:54:16', NULL),
(56, 57, 'LinkedIn Analytics Data Processing Tool with Competitive Benchmarking', 'request-56', 'Develop a privacy-first analytics tool that processes LinkedIn export files to generate insights about company page performance, follower growth, content engagement, audience demographics, and competitive analysis. The tool should visualize data through dashboards, provide performance optimization recommendations, and support team collaboration. Target users include marketing professionals, social media managers, and sales teams seeking to optimize their LinkedIn presence without granting API access to their accounts. Key features should include content performance tracking, audience analytics, competitive benchmarking, AI-powered recommendations, and customizable reporting.', 'other', 'medium', 'pending', NULL, NULL, 0, '::1', '2025-08-19 03:09:47', '2025-08-19 03:54:16', NULL),
(65, 34, 'Develop an Interactive Learning Path Platform for Electronics Programming Education', 'electronics-learning-path', 'Create a comprehensive learning management system for electronics programming, featuring structured weekly curriculum modules alternating between software and hardware lessons. The platform should include interactive tutorials with downloadable code examples, hardware compatibility guides, project assignments, and community integration. Target audience includes electronics beginners, hobbyists, and aspiring makers. The system should support multiple hardware kits, provide documentation links, offer simulator compatibility, and include progress tracking. Key value proposition is providing a clear, practical roadmap for electronics education that emphasizes learning by doing rather than passive tutorial following.', 'development', 'medium', 'pending', NULL, NULL, 0, '::1', '2025-08-20 05:31:58', '2025-08-20 05:31:58', NULL),
(66, 34, 'Cloud Service Management Platform with Automated IT Support and Business Solutions', 'cloud-service-management-platform', 'A comprehensive cloud service management platform that provides automated IT support, server monitoring, and business solutions including website building, domain registration, and license management. The platform should offer 1:1 expert consultation services, automated server maintenance, data processing capabilities, and scalable business solutions for companies transitioning to cloud infrastructure. Features should include a dashboard for service monitoring, automated support ticketing, service marketplace integration, and customizable service packages with tiered pricing options.', 'productivity', 'medium', 'pending', NULL, NULL, 0, '::1', '2025-08-20 13:12:08', '2025-08-20 13:12:08', NULL),
(67, 34, 'AI-Powered UX Design Assistant with Contextual Memory and Iterative Flow Improvement', 'ai-ux-design-assistant', 'A specialized AI assistant that remembers product context across conversations, provides UX expertise based on cognitive psychology, and offers iterative flow improvements. Targeted at UX designers and product teams to streamline design workflows and reduce decision overhead. Features include persistent product memory, adaptive explanations based on user expertise, team collaboration capabilities, and alignment with product strategy changes. Unlike generic AI assistants, this tool maintains context about users, decisions, and design patterns to provide increasingly valuable insights over time.', 'design', 'medium', 'pending', NULL, NULL, 0, '::1', '2025-08-20 13:23:32', '2025-08-20 13:23:32', NULL),
(68, 34, 'Develop an AI-powered UX design assistant that remembers product context and provides iterative design guidance', 'ai-ux-design-assistant1', 'Build an AI tool that maintains persistent product memory to avoid re-explaining user context, provides UX expertise based on cognitive psychology principles, offers adaptive guidance based on user skill level, enables live strategy alignment as product goals change, facilitates iterative flow improvement with actionable suggestions, and supports team collaboration through shared product context. The tool should focus on UX strategy and flow iteration through natural language conversation, with future potential for code component generation.', 'design', 'medium', 'pending', NULL, NULL, 0, '::1', '2025-08-20 13:28:25', '2025-08-20 13:28:25', NULL),
(69, 34, 'Build a comprehensive timezone meeting coordination tool for global teams', 'timezone-meeting-coordinator', 'Develop a timezone conversion and meeting scheduling tool that helps users coordinate across multiple timezones. Features should include: 1) Real-time timezone conversion with an intuitive interface, 2) Meeting scheduler showing multiple timezones simultaneously, 3) Business hours display for global locations, 4) Team schedule management for international teams, 5) Calendar integration for seamless scheduling. Target users include remote teams, international businesses, and anyone coordinating across timezones. The tool should solve the core problem of scheduling confusion and missed meetings due to timezone differences, enhancing productivity for global collaboration.', 'productivity', 'medium', 'pending', NULL, NULL, 0, '::1', '2025-08-20 13:54:25', '2025-08-20 13:54:25', NULL),
(70, 111, 'Build a Professional Peer Review Verification Platform for Enhanced Hiring and Career Development', 'peer-review-verification-platform', 'Develop a comprehensive platform that enables companies to access verified peer feedback during hiring processes while allowing professionals to build their reputation through authentic peer reviews. The platform should include profile management, peer review collection and verification, AI-powered insights and summaries, risk signal detection, and comparison tools. Target HR departments, recruiters, and individual professionals seeking to validate skills and work history. The tool should streamline background checks, reduce hiring risks, and provide valuable career development insights through verified peer assessments.', 'other', 'medium', 'pending', NULL, NULL, 0, '::1', '2025-08-20 16:28:02', '2025-08-20 16:28:02', NULL),
(71, 112, 'Develop an AI-powered DevOps rule engine for context-aware deployment protection', 'devops-guardrails-engine', 'Create a tool that enables teams to implement natural language-based DevOps protection rules across multiple platforms (GitHub, Slack, Linear, etc.). The tool should monitor all DevOps events in real-time, flag risky operations, and enforce context-aware rules that go beyond simple binary checks. Key features include: 1) Rule creation in plain English without technical syntax, 2) Integration with popular DevOps tools, 3) Real-time event tracking and pattern recognition, 4) Automated alerts and notifications, 5) Reporting capabilities with natural language queries, 6) Team-based rule management and assignment. The tool should reduce deployment risks, eliminate alert noise, and provide full traceability from code commit to production deployment.', 'development', 'medium', 'pending', NULL, NULL, 0, '::1', '2025-08-21 05:47:39', '2025-08-21 05:47:39', NULL);

-- --------------------------------------------------------

--
-- Stand-in structure for view `pt_v_member_stats`
-- (See below for the actual view)
--
CREATE TABLE `pt_v_member_stats` (
`api_quota` int
,`api_usage_percent` decimal(16,2)
,`api_used` int
,`completed_requests` bigint
,`created_at` timestamp
,`email` varchar(100)
,`first_name` varchar(50)
,`id` int
,`last_login` timestamp
,`last_name` varchar(50)
,`login_count` int
,`status` enum('active','inactive','suspended','pending')
,`subscription_type` enum('free','basic','premium','enterprise')
,`total_requests_submitted` bigint
,`total_tokens_used` decimal(32,0)
,`total_tool_usage` bigint
,`total_votes_given` decimal(23,0)
,`unique_tools_used` bigint
,`username` varchar(50)
);

--
-- Indexes for dumped tables
--

--
-- Indexes for table `pt_activity_log`
--
ALTER TABLE `pt_activity_log`
  ADD PRIMARY KEY (`id`),
  ADD KEY `manager_id` (`manager_id`),
  ADD KEY `created_at` (`created_at`);

--
-- Indexes for table `pt_analytics_event`
--
ALTER TABLE `pt_analytics_event`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_event_type` (`event_type`),
  ADD KEY `idx_event_name` (`event_name`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_session_id` (`session_id`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `pt_api_access_logs`
--
ALTER TABLE `pt_api_access_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_endpoint` (`endpoint`),
  ADD KEY `idx_ip_address` (`ip_address`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `pt_api_usage`
--
ALTER TABLE `pt_api_usage`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_action` (`action`),
  ADD KEY `idx_user_action` (`user_id`,`action`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `pt_contact_message`
--
ALTER TABLE `pt_contact_message`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_email` (`email`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `pt_launch_categories`
--
ALTER TABLE `pt_launch_categories`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `slug` (`slug`),
  ADD KEY `idx_parent_id` (`parent_id`),
  ADD KEY `idx_sort_order` (`sort_order`);

--
-- Indexes for table `pt_launch_votes`
--
ALTER TABLE `pt_launch_votes`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_vote` (`launch_id`,`user_id`),
  ADD KEY `idx_launch_id` (`launch_id`),
  ADD KEY `idx_user_id` (`user_id`);

--
-- Indexes for table `pt_manager`
--
ALTER TABLE `pt_manager`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`),
  ADD UNIQUE KEY `email` (`email`);

--
-- Indexes for table `pt_manager_token`
--
ALTER TABLE `pt_manager_token`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `manager_id` (`manager_id`),
  ADD KEY `token` (`token`),
  ADD KEY `expires_at` (`expires_at`);

--
-- Indexes for table `pt_member`
--
ALTER TABLE `pt_member`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`),
  ADD UNIQUE KEY `email` (`email`),
  ADD KEY `status` (`status`),
  ADD KEY `subscription_type` (`subscription_type`),
  ADD KEY `idx_member_api_reset` (`api_reset_date`),
  ADD KEY `idx_member_login_count` (`login_count`);

--
-- Indexes for table `pt_member_activity_log`
--
ALTER TABLE `pt_member_activity_log`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_activity_type` (`action`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `pt_member_notifications`
--
ALTER TABLE `pt_member_notifications`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_type` (`type`),
  ADD KEY `idx_is_read` (`is_read`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `pt_member_sessions`
--
ALTER TABLE `pt_member_sessions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_last_activity` (`last_activity`);

--
-- Indexes for table `pt_member_tokens`
--
ALTER TABLE `pt_member_tokens`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_user_type_remember` (`user_id`,`type`),
  ADD KEY `idx_token` (`token`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_type` (`type`),
  ADD KEY `idx_expires` (`expires_at`);

--
-- Indexes for table `pt_product_launches`
--
ALTER TABLE `pt_product_launches`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `slug` (`slug`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_category` (`category`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_featured_date` (`featured_date`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `pt_quota_usage`
--
ALTER TABLE `pt_quota_usage`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_action` (`action`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `pt_request_duplicates`
--
ALTER TABLE `pt_request_duplicates`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_user_content` (`user_id`,`title_hash`,`description_hash`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_request_id` (`request_id`);

--
-- Indexes for table `pt_request_votes`
--
ALTER TABLE `pt_request_votes`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_user_request_vote` (`request_id`,`user_id`),
  ADD KEY `idx_request_id` (`request_id`),
  ADD KEY `idx_user_id` (`user_id`);

--
-- Indexes for table `pt_service_config`
--
ALTER TABLE `pt_service_config`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `uk_platform_config` (`platform_id`,`config_key`);

--
-- Indexes for table `pt_service_key`
--
ALTER TABLE `pt_service_key`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_platform_active` (`platform_id`,`is_active`);

--
-- Indexes for table `pt_service_model`
--
ALTER TABLE `pt_service_model`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `uk_platform_model` (`platform_id`,`model_code`),
  ADD KEY `idx_platform_active` (`platform_id`,`is_active`);

--
-- Indexes for table `pt_service_platform`
--
ALTER TABLE `pt_service_platform`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `code` (`code`);

--
-- Indexes for table `pt_system_config`
--
ALTER TABLE `pt_system_config`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `uk_setting_key` (`setting_key`),
  ADD KEY `idx_is_public` (`is_public`),
  ADD KEY `idx_setting_type` (`setting_type`),
  ADD KEY `idx_updated_at` (`updated_at`);

--
-- Indexes for table `pt_tech_categories`
--
ALTER TABLE `pt_tech_categories`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `slug` (`slug`);

--
-- Indexes for table `pt_tool`
--
ALTER TABLE `pt_tool`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `slug` (`slug`),
  ADD KEY `idx_slug` (`slug`),
  ADD KEY `idx_category` (`category_id`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_featured` (`is_featured`),
  ADD KEY `idx_sort_order` (`sort_order`),
  ADD KEY `idx_tool_created_by` (`created_by`);

--
-- Indexes for table `pt_tool_category`
--
ALTER TABLE `pt_tool_category`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `slug` (`slug`),
  ADD KEY `idx_slug` (`slug`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_sort_order` (`sort_order`);

--
-- Indexes for table `pt_tool_usage`
--
ALTER TABLE `pt_tool_usage`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_tool_id` (`tool_id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_created_at` (`used_at`),
  ADD KEY `idx_tool_usage_status` (`status`),
  ADD KEY `idx_tool_usage_used_at` (`used_at`);

--
-- Indexes for table `pt_user_favorites`
--
ALTER TABLE `pt_user_favorites`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_user_tool` (`user_id`,`tool_id`) COMMENT '防止重复收藏',
  ADD KEY `idx_user_id` (`user_id`) COMMENT '用户ID索引',
  ADD KEY `idx_tool_id` (`tool_id`) COMMENT '工具ID索引',
  ADD KEY `idx_created_at` (`created_at`) COMMENT '创建时间索引';

--
-- Indexes for table `pt_user_requests`
--
ALTER TABLE `pt_user_requests`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `idx_slug` (`slug`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_category` (`category`),
  ADD KEY `idx_created_at` (`created_at`),
  ADD KEY `idx_votes` (`votes`),
  ADD KEY `fk_user_requests_admin` (`admin_id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `pt_activity_log`
--
ALTER TABLE `pt_activity_log`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=79;

--
-- AUTO_INCREMENT for table `pt_analytics_event`
--
ALTER TABLE `pt_analytics_event`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `pt_api_access_logs`
--
ALTER TABLE `pt_api_access_logs`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=45;

--
-- AUTO_INCREMENT for table `pt_api_usage`
--
ALTER TABLE `pt_api_usage`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=281;

--
-- AUTO_INCREMENT for table `pt_contact_message`
--
ALTER TABLE `pt_contact_message`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=20;

--
-- AUTO_INCREMENT for table `pt_launch_categories`
--
ALTER TABLE `pt_launch_categories`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=15;

--
-- AUTO_INCREMENT for table `pt_launch_votes`
--
ALTER TABLE `pt_launch_votes`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=20;

--
-- AUTO_INCREMENT for table `pt_manager`
--
ALTER TABLE `pt_manager`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `pt_manager_token`
--
ALTER TABLE `pt_manager_token`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `pt_member`
--
ALTER TABLE `pt_member`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=116;

--
-- AUTO_INCREMENT for table `pt_member_activity_log`
--
ALTER TABLE `pt_member_activity_log`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=330;

--
-- AUTO_INCREMENT for table `pt_member_notifications`
--
ALTER TABLE `pt_member_notifications`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=30;

--
-- AUTO_INCREMENT for table `pt_member_tokens`
--
ALTER TABLE `pt_member_tokens`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=39;

--
-- AUTO_INCREMENT for table `pt_product_launches`
--
ALTER TABLE `pt_product_launches`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=124;

--
-- AUTO_INCREMENT for table `pt_quota_usage`
--
ALTER TABLE `pt_quota_usage`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `pt_request_duplicates`
--
ALTER TABLE `pt_request_duplicates`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=53;

--
-- AUTO_INCREMENT for table `pt_request_votes`
--
ALTER TABLE `pt_request_votes`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=59;

--
-- AUTO_INCREMENT for table `pt_service_config`
--
ALTER TABLE `pt_service_config`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=19;

--
-- AUTO_INCREMENT for table `pt_service_key`
--
ALTER TABLE `pt_service_key`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `pt_service_model`
--
ALTER TABLE `pt_service_model`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=64;

--
-- AUTO_INCREMENT for table `pt_service_platform`
--
ALTER TABLE `pt_service_platform`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `pt_system_config`
--
ALTER TABLE `pt_system_config`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=202;

--
-- AUTO_INCREMENT for table `pt_tech_categories`
--
ALTER TABLE `pt_tech_categories`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=19;

--
-- AUTO_INCREMENT for table `pt_tool`
--
ALTER TABLE `pt_tool`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=22;

--
-- AUTO_INCREMENT for table `pt_tool_category`
--
ALTER TABLE `pt_tool_category`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=31;

--
-- AUTO_INCREMENT for table `pt_tool_usage`
--
ALTER TABLE `pt_tool_usage`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=136;

--
-- AUTO_INCREMENT for table `pt_user_favorites`
--
ALTER TABLE `pt_user_favorites`
  MODIFY `id` int NOT NULL AUTO_INCREMENT COMMENT '主键ID', AUTO_INCREMENT=12;

--
-- AUTO_INCREMENT for table `pt_user_requests`
--
ALTER TABLE `pt_user_requests`
  MODIFY `id` int NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=72;

-- --------------------------------------------------------

--
-- Structure for view `pt_v_member_stats`
--
DROP TABLE IF EXISTS `pt_v_member_stats`;

CREATE ALGORITHM=UNDEFINED DEFINER=`root`@`localhost` SQL SECURITY DEFINER VIEW `pt_v_member_stats`  AS SELECT `m`.`id` AS `id`, `m`.`username` AS `username`, `m`.`email` AS `email`, `m`.`first_name` AS `first_name`, `m`.`last_name` AS `last_name`, `m`.`status` AS `status`, `m`.`subscription_type` AS `subscription_type`, `m`.`api_quota` AS `api_quota`, `m`.`api_used` AS `api_used`, round(((`m`.`api_used` / `m`.`api_quota`) * 100),2) AS `api_usage_percent`, `m`.`login_count` AS `login_count`, `m`.`last_login` AS `last_login`, `m`.`created_at` AS `created_at`, count(`tu`.`id`) AS `total_tool_usage`, count(distinct `tu`.`tool_id`) AS `unique_tools_used`, coalesce(sum(`tu`.`tokens_used`),0) AS `total_tokens_used`, count(`ur`.`id`) AS `total_requests_submitted`, count((case when (`ur`.`status` = 'completed') then 1 end)) AS `completed_requests`, coalesce(sum((`rv`.`vote_type` = 'up')),0) AS `total_votes_given` FROM (((`pt_member` `m` left join `pt_tool_usage` `tu` on((`m`.`id` = `tu`.`user_id`))) left join `pt_user_requests` `ur` on((`m`.`id` = `ur`.`user_id`))) left join `pt_request_votes` `rv` on((`m`.`id` = `rv`.`user_id`))) GROUP BY `m`.`id` ;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `pt_activity_log`
--
ALTER TABLE `pt_activity_log`
  ADD CONSTRAINT `pt_activity_log_ibfk_1` FOREIGN KEY (`manager_id`) REFERENCES `pt_manager` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `pt_analytics_event`
--
ALTER TABLE `pt_analytics_event`
  ADD CONSTRAINT `pt_analytics_event_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `pt_member` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `pt_api_access_logs`
--
ALTER TABLE `pt_api_access_logs`
  ADD CONSTRAINT `pt_api_access_logs_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `pt_member` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `pt_api_usage`
--
ALTER TABLE `pt_api_usage`
  ADD CONSTRAINT `fk_api_usage_user` FOREIGN KEY (`user_id`) REFERENCES `pt_member` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `pt_manager_token`
--
ALTER TABLE `pt_manager_token`
  ADD CONSTRAINT `pt_manager_token_ibfk_1` FOREIGN KEY (`manager_id`) REFERENCES `pt_manager` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `pt_member_activity_log`
--
ALTER TABLE `pt_member_activity_log`
  ADD CONSTRAINT `pt_member_activity_log_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `pt_member` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `pt_member_notifications`
--
ALTER TABLE `pt_member_notifications`
  ADD CONSTRAINT `pt_member_notifications_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `pt_member` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `pt_member_sessions`
--
ALTER TABLE `pt_member_sessions`
  ADD CONSTRAINT `pt_member_sessions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `pt_member` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `pt_member_tokens`
--
ALTER TABLE `pt_member_tokens`
  ADD CONSTRAINT `pt_member_tokens_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `pt_member` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `pt_request_duplicates`
--
ALTER TABLE `pt_request_duplicates`
  ADD CONSTRAINT `fk_request_duplicates_request` FOREIGN KEY (`request_id`) REFERENCES `pt_user_requests` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_request_duplicates_user` FOREIGN KEY (`user_id`) REFERENCES `pt_member` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `pt_request_votes`
--
ALTER TABLE `pt_request_votes`
  ADD CONSTRAINT `fk_request_votes_request` FOREIGN KEY (`request_id`) REFERENCES `pt_user_requests` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_request_votes_user` FOREIGN KEY (`user_id`) REFERENCES `pt_member` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `pt_service_config`
--
ALTER TABLE `pt_service_config`
  ADD CONSTRAINT `pt_service_config_ibfk_1` FOREIGN KEY (`platform_id`) REFERENCES `pt_service_platform` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `pt_service_key`
--
ALTER TABLE `pt_service_key`
  ADD CONSTRAINT `pt_service_key_ibfk_1` FOREIGN KEY (`platform_id`) REFERENCES `pt_service_platform` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `pt_service_model`
--
ALTER TABLE `pt_service_model`
  ADD CONSTRAINT `pt_service_model_ibfk_1` FOREIGN KEY (`platform_id`) REFERENCES `pt_service_platform` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `pt_tool`
--
ALTER TABLE `pt_tool`
  ADD CONSTRAINT `fk_tool_created_by` FOREIGN KEY (`created_by`) REFERENCES `pt_member` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  ADD CONSTRAINT `pt_tool_ibfk_1` FOREIGN KEY (`category_id`) REFERENCES `pt_tool_category` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `pt_tool_usage`
--
ALTER TABLE `pt_tool_usage`
  ADD CONSTRAINT `pt_tool_usage_ibfk_1` FOREIGN KEY (`tool_id`) REFERENCES `pt_tool` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `pt_tool_usage_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `pt_member` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `pt_user_favorites`
--
ALTER TABLE `pt_user_favorites`
  ADD CONSTRAINT `pt_user_favorites_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `pt_member` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `pt_user_favorites_ibfk_2` FOREIGN KEY (`tool_id`) REFERENCES `pt_tool` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `pt_user_requests`
--
ALTER TABLE `pt_user_requests`
  ADD CONSTRAINT `fk_user_requests_admin` FOREIGN KEY (`admin_id`) REFERENCES `pt_manager` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_user_requests_user` FOREIGN KEY (`user_id`) REFERENCES `pt_member` (`id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
