<?php
/**
 * 简化版编辑产品页面
 */

// 确保用户已登录
if (!isset($_SESSION['user_id'])) {
    header('Location: /auth/login');
    exit;
}

$userId = $_SESSION['user_id'];
$productId = $_GET['id'] ?? null;

if (!$productId) {
    header('Location: ?page=my-products');
    exit;
}

// 使用统一的数据库连接
require_once ROOT_PATH . '/includes/database-connection.php';

try {

    // 获取产品信息，确保是当前用户的产品
    $stmt = $pdo->prepare("
        SELECT * FROM pt_product_launches
        WHERE id = ? AND user_id = ?
    ");
    $stmt->execute([$productId, $userId]);
    $product = $stmt->fetch();

    if (!$product) {
        header('Location: ?page=my-products');
        exit;
    }

    // 获取分类列表
    $categoryStmt = $pdo->query("SELECT slug, name FROM pt_launch_categories WHERE is_active = 1 ORDER BY sort_order ASC, name ASC");
    $categories = $categoryStmt->fetchAll();

    // 获取技术分类列表
    $techCategoryStmt = $pdo->query("SELECT slug, name FROM pt_tech_categories WHERE is_active = 1 ORDER BY sort_order ASC, name ASC");
    $techCategories = $techCategoryStmt->fetchAll();

} catch (Exception $e) {
    $error = "Database error: " . $e->getMessage();
}

// 解析JSON字段
$tags = json_decode($product['tags'] ?? '[]', true) ?: [];
$keyFeatures = json_decode($product['key_features'] ?? '[]', true) ?: [];
$useCases = json_decode($product['use_cases'] ?? '[]', true) ?: [];
$socialLinks = json_decode($product['social_links'] ?? '{}', true) ?: [];
?>

<div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Edit Product</h1>
            <p class="text-gray-600 mt-1">Update your product information</p>
        </div>
        <a href="?page=my-products"
           class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Back to My Products
        </a>
    </div>

    <!-- 编辑表单 -->
    <div class="bg-white rounded-lg border border-gray-200">
        <form id="edit-product-form" class="space-y-6 p-6">
            <input type="hidden" name="product_id" value="<?= htmlspecialchars($product['id']) ?>">

            <!-- 基本信息 -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="product_name" class="block text-sm font-medium text-gray-700 mb-2">
                        Product Name *
                    </label>
                    <input type="text"
                           id="product_name"
                           name="product_name"
                           required
                           maxlength="100"
                           value="<?= htmlspecialchars($product['name']) ?>"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent">
                </div>

                <div>
                    <label for="website_url" class="block text-sm font-medium text-gray-700 mb-2">
                        Website URL *
                    </label>
                    <input type="url"
                           id="website_url"
                           name="website_url"
                           required
                           value="<?= htmlspecialchars($product['website_url']) ?>"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent">
                </div>
            </div>

            <!-- 标语 -->
            <div>
                <label for="tagline" class="block text-sm font-medium text-gray-700 mb-2">
                    Tagline
                </label>
                <input type="text"
                       id="tagline"
                       name="tagline"
                       maxlength="500"
                       value="<?= htmlspecialchars($product['tagline'] ?? '') ?>"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent"
                       placeholder="A brief, compelling description of your product">
            </div>

            <!-- 描述 -->
            <div>
                <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                    Description *
                </label>
                <textarea id="description"
                          name="description"
                          required
                          rows="4"
                          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent"
                          placeholder="Detailed description of your product"><?= htmlspecialchars($product['description'] ?? '') ?></textarea>
            </div>

            <!-- 分类和状态 -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div>
                    <label for="category" class="block text-sm font-medium text-gray-700 mb-2">
                        Category *
                    </label>
                    <select id="category"
                            name="category"
                            required
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent">
                        <option value="">Select Category</option>
                        <?php foreach ($categories as $cat): ?>
                            <option value="<?= htmlspecialchars($cat['slug']) ?>"
                                    <?= $product['category'] === $cat['slug'] ? 'selected' : '' ?>>
                                <?= htmlspecialchars($cat['name']) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div>
                    <label for="tech_category" class="block text-sm font-medium text-gray-700 mb-2">
                        Tech Category
                    </label>
                    <select id="tech_category"
                            name="tech_category"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent">
                        <option value="">Select Tech Category (Optional)</option>
                        <?php foreach ($techCategories as $techCat): ?>
                            <option value="<?= htmlspecialchars($techCat['slug']) ?>"
                                    <?= ($product['tech_category'] ?? '') === $techCat['slug'] ? 'selected' : '' ?>>
                                <?= htmlspecialchars($techCat['name']) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>

                <div>
                    <label for="pricing_model" class="block text-sm font-medium text-gray-700 mb-2">
                        Pricing Model *
                    </label>
                    <select id="pricing_model"
                            name="pricing_model"
                            required
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent">
                        <option value="free" <?= $product['pricing_model'] === 'free' ? 'selected' : '' ?>>Free</option>
                        <option value="freemium" <?= $product['pricing_model'] === 'freemium' ? 'selected' : '' ?>>Freemium</option>
                        <option value="paid" <?= $product['pricing_model'] === 'paid' ? 'selected' : '' ?>>Paid</option>
                        <option value="enterprise" <?= $product['pricing_model'] === 'enterprise' ? 'selected' : '' ?>>Enterprise</option>
                    </select>
                </div>

                <div>
                    <label for="launch_status" class="block text-sm font-medium text-gray-700 mb-2">
                        Launch Status
                    </label>
                    <?php
                    $currentStatus = $product['launch_status'] ?? 'coming-soon';

                    // 根据状态设置显示内容和样式
                    switch ($currentStatus) {
                        case 'launched':
                            $statusLabel = 'Launched';
                            $statusColor = 'bg-green-50 border-green-200 text-green-800';
                            $statusIcon = '🎉';
                            $statusMessage = 'Congratulations! Your product is now live';
                            break;
                        case 'beta':
                            $statusLabel = 'Beta';
                            $statusColor = 'bg-blue-50 border-blue-200 text-blue-800';
                            $statusIcon = '🚀';
                            $statusMessage = 'Your product is in beta testing phase';
                            break;
                        case 'coming-soon':
                        default:
                            $statusLabel = 'Coming Soon';
                            $statusColor = 'bg-orange-50 border-orange-200 text-orange-800';
                            $statusIcon = '⏳';
                            $statusMessage = 'Status can only be updated by admin';
                            break;
                    }
                    ?>
                    <div class="w-full px-3 py-2 border rounded-md <?= $statusColor ?>">
                        <div class="flex items-center">
                            <span class="mr-2"><?= $statusIcon ?></span>
                            <span class="font-medium"><?= htmlspecialchars($statusLabel) ?></span>
                        </div>
                    </div>
                    <input type="hidden" id="launch_status" name="launch_status" value="<?= htmlspecialchars($currentStatus) ?>">
                    <p class="text-xs text-gray-500 mt-1"><?= htmlspecialchars($statusMessage) ?></p>
                </div>
            </div>

            <!-- Logo -->
            <div class="space-y-4">
                <label class="block text-sm font-medium text-gray-700">
                    Product Logo
                </label>

                <!-- 当前Logo预览 -->
                <?php if (!empty($product['logo_url'])): ?>
                <div id="current-logo" class="flex items-center space-x-4 p-3 bg-gray-50 rounded-lg">
                    <img src="<?= htmlspecialchars($product['logo_url']) ?>"
                         alt="Current Logo"
                         class="w-12 h-12 object-contain rounded border border-gray-200">
                    <div>
                        <p class="text-sm font-medium text-gray-900">Current Logo</p>
                        <p class="text-xs text-gray-500">Upload new file to replace</p>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Logo上传 -->
                <div class="flex items-center space-x-4">
                    <button type="button" id="logo-trigger" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                        </svg>
                        Upload Logo
                    </button>
                    <input id="logo-file" type="file" class="hidden" accept="image/*" tabindex="-1" aria-hidden="true">
                    <span id="logo-status" class="text-sm text-gray-500"></span>
                </div>

                <!-- Logo URL输入框 -->
                <div>
                    <label for="logo_url" class="block text-sm font-medium text-gray-700 mb-2">
                        Or enter Logo URL directly
                    </label>
                    <input type="url"
                           id="logo_url"
                           name="logo_url"
                           value="<?= htmlspecialchars($product['logo_url'] ?? '') ?>"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent"
                           placeholder="https://example.com/logo.png">
                </div>
            </div>

            <!-- 视频教程 -->
            <div>
                <label for="video_tutorial_url" class="block text-sm font-medium text-gray-700 mb-2">
                    Video Tutorial URL
                </label>
                <input type="url"
                       id="video_tutorial_url"
                       name="video_tutorial_url"
                       value="<?= htmlspecialchars($product['video_tutorial_url'] ?? '') ?>"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent"
                       placeholder="https://www.youtube.com/watch?v=...">
            </div>

            <!-- 标签 -->
            <div>
                <label for="tags" class="block text-sm font-medium text-gray-700 mb-2">
                    Tags
                </label>
                <input type="text"
                       id="tags"
                       name="tags"
                       value="<?= htmlspecialchars(implode(', ', $tags)) ?>"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent"
                       placeholder="AI, Writing, Content Creation (comma separated)">
                <p class="text-xs text-gray-500 mt-1">Separate tags with commas</p>
            </div>

            <!-- 核心功能 -->
            <div>
                <label for="key_features" class="block text-sm font-medium text-gray-700 mb-2">
                    Key Features
                </label>
                <textarea id="key_features"
                          name="key_features"
                          rows="3"
                          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent"
                          placeholder="Feature 1, Feature 2, Feature 3 (comma separated)"><?= htmlspecialchars(implode(', ', $keyFeatures)) ?></textarea>
                <p class="text-xs text-gray-500 mt-1">Separate features with commas</p>
            </div>

            <!-- 使用场景 -->
            <div>
                <label for="use_cases" class="block text-sm font-medium text-gray-700 mb-2">
                    Use Cases
                </label>
                <textarea id="use_cases"
                          name="use_cases"
                          rows="3"
                          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent"
                          placeholder="Use case 1, Use case 2, Use case 3 (comma separated)"><?= htmlspecialchars(implode(', ', $useCases)) ?></textarea>
                <p class="text-xs text-gray-500 mt-1">Separate use cases with commas</p>
            </div>

            <!-- 目标用户 -->
            <div>
                <label for="target_audience" class="block text-sm font-medium text-gray-700 mb-2">
                    Target Audience
                </label>
                <input type="text"
                       id="target_audience"
                       name="target_audience"
                       value="<?= htmlspecialchars($product['target_audience'] ?? '') ?>"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent"
                       placeholder="Developers, designers, content creators">
            </div>

            <!-- 截图 -->
            <div class="space-y-4">
                <label class="block text-sm font-medium text-gray-700">
                    Product Screenshots
                </label>

                <!-- 当前截图预览 -->
                <div id="screenshots-preview" class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 max-h-60 overflow-y-auto p-2 border border-gray-200 rounded-lg bg-gray-50" style="min-height: 0;"></div>

                <!-- 截图上传 -->
                <div class="flex items-center space-x-4">
                    <button type="button" id="screenshot-trigger" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6 6h.01M6 20h36a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V22a2 2 0 012-2z"></path>
                        </svg>
                        Upload Screenshots
                    </button>
                    <input id="screenshot-files" type="file" class="hidden" accept="image/*" multiple tabindex="-1" aria-hidden="true">
                    <span id="screenshot-status" class="text-sm text-gray-500 min-w-0 flex-1"></span>
                </div>

                <!-- 截图URL输入框（隐藏，用于存储数据） -->
                <input type="hidden" id="screenshots" name="screenshots" value="<?= htmlspecialchars($product['screenshots'] ?? '[]') ?>">
                    <!-- 提示：最多3张 -->
                    <span class="text-xs text-gray-500">Up to 3 screenshots</span>
            </div>

            <!-- 提交按钮 -->
            <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
                <a href="?page=my-products"
                   class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors">
                    Cancel
                </a>
                <button type="submit"
                        id="submit-btn"
                        class="inline-flex items-center px-6 py-2 bg-black text-white text-sm font-medium rounded-md hover:bg-gray-800 transition-colors">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Update Product
                </button>
            </div>
        </form>
    </div>
</div>

<!-- 成功/错误提示 -->
<div id="alert-container" class="fixed top-4 right-4 z-50"></div>

<!-- 图片裁剪模态框 -->
<div id="crop-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center p-4">
    <div class="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-hidden">
        <div class="p-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">Crop Logo</h3>
            <p class="text-sm text-gray-600 mt-1">Select a square area for your logo</p>
        </div>

        <div class="p-4">
            <div class="max-h-96 overflow-hidden flex items-center justify-center bg-gray-100 rounded-lg">
                <img id="crop-image" src="" alt="Crop preview" class="max-w-full max-h-full">
            </div>
        </div>

        <div class="p-4 border-t border-gray-200 flex justify-end space-x-3">
            <button id="crop-cancel" class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors">
                Cancel
            </button>
            <button id="crop-confirm" class="px-4 py-2 bg-black text-white rounded-md text-sm font-medium hover:bg-gray-800 transition-colors">
                Crop & Upload
            </button>
        </div>
    </div>
</div>

<!-- Cropper.js CSS -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.5.13/cropper.min.css">

<!-- 自定义样式 -->
<style>
#crop-modal .cropper-container {
    max-height: 400px;
}

#crop-modal .cropper-canvas {
    max-height: 400px;
}

.cropper-crop-box {
    border-color: #000 !important;
}

.cropper-view-box {
    outline-color: rgba(0, 0, 0, 0.1) !important;
}

.cropper-face {
    background-color: inherit !important;
}

/* 防止页面布局跳动 */
#screenshots-preview:empty {
    display: none !important;
}

/* 截图预览区域样式优化 */
#screenshots-preview {
    scrollbar-width: thin;
    scrollbar-color: #cbd5e0 #f7fafc;
}

#screenshots-preview::-webkit-scrollbar {
    width: 6px;
}

#screenshots-preview::-webkit-scrollbar-track {
    background: #f7fafc;
    border-radius: 3px;
}

#screenshots-preview::-webkit-scrollbar-thumb {
    background: #cbd5e0;
    border-radius: 3px;
}

#screenshots-preview::-webkit-scrollbar-thumb:hover {
    background: #a0aec0;
}
</style>

<!-- Cropper.js JavaScript -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.5.13/cropper.min.js"></script>

<script>
// 全局变量
let screenshotUrls = [];
let cropper = null;
let currentFile = null;
const MAX_SCREENSHOTS = 3; // 最多3张截图

// 页面加载时初始化截图 + 绑定触发按钮
document.addEventListener('DOMContentLoaded', function() {
    try {
        const screenshotsData = document.getElementById('screenshots').value;
        screenshotUrls = JSON.parse(screenshotsData) || [];
        renderScreenshots();
    } catch (e) {
        screenshotUrls = [];
    }

    // 绑定Logo触发按钮
    const logoTrigger = document.getElementById('logo-trigger');
    const logoInput = document.getElementById('logo-file');
    if (logoTrigger && logoInput) {
        logoTrigger.addEventListener('click', function(ev){
            ev.preventDefault();
            const y = window.scrollY;
            logoInput.click();
            setTimeout(() => window.scrollTo(0, y), 0);
        });
    }

    // 绑定Screenshots触发按钮
    const ssTrigger = document.getElementById('screenshot-trigger');
    const ssInput = document.getElementById('screenshot-files');
    if (ssTrigger && ssInput) {
        ssTrigger.addEventListener('click', function(ev){
            ev.preventDefault();
            const y = window.scrollY;
            ssInput.click();
            setTimeout(() => window.scrollTo(0, y), 0);
        });
    }
});

// Logo上传处理
document.getElementById('logo-file').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (!file) return;

    // 验证文件类型
    if (!file.type.startsWith('image/')) {
        showAlert('Please select an image file', 'error');


        e.target.value = '';
        return;
    }

    // 验证文件大小 (5MB)
    if (file.size > 5 * 1024 * 1024) {
        showAlert('File too large. Maximum size is 5MB', 'error');
        e.target.value = '';
        return;
    }

    currentFile = file;

    // 创建图片预览
    const reader = new FileReader();
    reader.onload = function(event) {
        const img = new Image();
        img.onload = function() {
            // 检查图片尺寸
            if (img.width < 64 || img.height < 64) {
                showAlert('Image too small. Minimum size is 64x64 pixels', 'error');
                e.target.value = '';
                return;
            }

            // 检查是否为正方形
            if (img.width === img.height) {
                // 正方形图片，直接上传
                uploadLogo(file);
            } else {
                // 非正方形图片，显示裁剪界面
                showCropModal(event.target.result);
            }
        };
        img.src = event.target.result;
    };
    reader.readAsDataURL(file);

    e.target.value = ''; // 清空文件输入
});

// 截图上传处理
document.getElementById('screenshot-files').addEventListener('change', function(e) {
    let files = Array.from(e.target.files);
    if (files.length === 0) return;

    // 数量限制：最多3张
    if (screenshotUrls.length >= MAX_SCREENSHOTS) {
        showAlert(`You can upload up to ${MAX_SCREENSHOTS} screenshots.`, 'warning');
        e.target.value = '';
        return;
    }

    const remaining = MAX_SCREENSHOTS - screenshotUrls.length;
    if (files.length > remaining) {
        files = files.slice(0, remaining);
        showAlert(`Only ${remaining} more screenshot(s) allowed (max ${MAX_SCREENSHOTS}).`, 'warning');
    }

    const statusEl = document.getElementById('screenshot-status');
    statusEl.textContent = `Uploading ${files.length} file(s)...`;
    statusEl.className = 'text-sm text-blue-600';

    // 使用Promise.all来处理多个文件上传，避免async/await导致的页面跳动
    const uploadPromises = files.map(file => {
        // 验证文件
        if (!file.type.startsWith('image/')) {
            return Promise.reject(new Error('Invalid file type'));
        }

        if (file.size > 5 * 1024 * 1024) {
            return Promise.reject(new Error('File too large'));
        }

        const formData = new FormData();
        formData.append('image', file);
        formData.append('type', 'screenshot');

        return fetch('/user-center/ajax/upload-image.php', {
            method: 'POST',
            body: formData
        }).then(response => response.json());
    });

    Promise.allSettled(uploadPromises).then(results => {
        let successCount = 0;

        results.forEach(result => {
            if (result.status === 'fulfilled' && result.value.success) {
                screenshotUrls.push(result.value.url);
                successCount++;
            }
        });

        // 更新截图预览和隐藏字段
        renderScreenshots();
        document.getElementById('screenshots').value = JSON.stringify(screenshotUrls);

        if (successCount > 0) {
            statusEl.textContent = `Successfully uploaded ${successCount} screenshot(s)!`;
            statusEl.className = 'text-sm text-green-600';
        } else {
            statusEl.textContent = 'No files were uploaded successfully';
            statusEl.className = 'text-sm text-red-600';
        }

        setTimeout(() => {
            statusEl.textContent = '';
        }, 3000);
    }).catch(error => {
        console.error('Screenshot upload error:', error);
        statusEl.textContent = 'Upload failed: Network error';
        statusEl.className = 'text-sm text-red-600';
    });

    e.target.value = ''; // 清空文件输入
});

// 更新Logo预览
function updateLogoPreview(url) {
    let logoPreview = document.getElementById('current-logo');
    if (!logoPreview) {
        // 创建新的预览元素
        logoPreview = document.createElement('div');
        logoPreview.id = 'current-logo';
        logoPreview.className = 'flex items-center space-x-4 p-3 bg-gray-50 rounded-lg';

        // 插入到Logo上传按钮之前（兼容button触发器）
        const uploadSection = document.getElementById('logo-trigger')?.parentElement || document.getElementById('logo-file')?.parentElement || document.querySelector('[for="logo-file"]')?.parentElement;
        if (uploadSection && uploadSection.parentElement) {
            uploadSection.parentElement.insertBefore(logoPreview, uploadSection);
        }
    }

    logoPreview.innerHTML = `
        <img src="${url}" alt="Current Logo" class="w-12 h-12 object-contain rounded border border-gray-200">
        <div>
            <p class="text-sm font-medium text-gray-900">Current Logo</p>
            <p class="text-xs text-gray-500">Upload new file to replace</p>
        </div>
    `;
}

// 渲染截图预览 - 简化版本，模仿Logo处理方式
function renderScreenshots() {
    const container = document.getElementById('screenshots-preview');

    // 如果没有截图，隐藏容器
    if (screenshotUrls.length === 0) {
        container.style.display = 'none';
        container.innerHTML = '';
        return;
    }

    // 显示容器
    container.style.display = 'grid';

    // 简单的innerHTML替换，类似Logo的处理方式
    const screenshotHTML = screenshotUrls.map((url, index) => `
        <div class="relative group">
            <img src="${url}" alt="Screenshot ${index + 1}" class="w-full h-20 object-cover rounded border border-gray-200" loading="lazy">
            <button type="button" onclick="removeScreenshot(${index})" class="absolute top-1 right-1 bg-red-500 hover:bg-red-600 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity">
                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
    `).join('');

    container.innerHTML = screenshotHTML;
}

// 显示裁剪模态框
function showCropModal(imageSrc) {
    const modal = document.getElementById('crop-modal');
    const cropImage = document.getElementById('crop-image');

    cropImage.src = imageSrc;
    modal.classList.remove('hidden');

    // 初始化裁剪器
    cropImage.onload = function() {
        if (cropper) {
            cropper.destroy();
        }

        cropper = new Cropper(cropImage, {
            aspectRatio: 1, // 正方形
            viewMode: 1,
            dragMode: 'move',
            autoCropArea: 0.8,
            restore: false,
            guides: false,
            center: false,
            highlight: false,
            cropBoxMovable: true,
            cropBoxResizable: true,
            toggleDragModeOnDblclick: false,
        });
    };
}

// 关闭裁剪模态框
function closeCropModal() {
    const modal = document.getElementById('crop-modal');
    modal.classList.add('hidden');

    if (cropper) {
        cropper.destroy();
        cropper = null;
    }
}

// 裁剪模态框事件处理
document.getElementById('crop-cancel').addEventListener('click', closeCropModal);

// 点击背景关闭模态框
document.getElementById('crop-modal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeCropModal();
    }
});

// ESC键关闭模态框
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape' && !document.getElementById('crop-modal').classList.contains('hidden')) {
        closeCropModal();
    }
});

document.getElementById('crop-confirm').addEventListener('click', function() {
    if (!cropper || !currentFile) return;

    // 获取裁剪后的canvas
    const canvas = cropper.getCroppedCanvas({
        width: 256,
        height: 256,
        imageSmoothingEnabled: true,
        imageSmoothingQuality: 'high'
    });

    // 将canvas转换为blob
    canvas.toBlob(function(blob) {
        if (blob) {
            // 创建新的文件对象
            const croppedFile = new File([blob], currentFile.name, {
                type: currentFile.type,
                lastModified: Date.now()
            });

            // 关闭模态框
            closeCropModal();

            // 上传裁剪后的图片
            uploadLogo(croppedFile);
        }
    }, currentFile.type, 0.9);
});

// 上传Logo函数
async function uploadLogo(file) {
    const statusEl = document.getElementById('logo-status');
    statusEl.textContent = 'Uploading...';
    statusEl.className = 'text-sm text-blue-600';

    try {
        const formData = new FormData();
        formData.append('image', file);
        formData.append('type', 'logo');

        const response = await fetch('/user-center/ajax/upload-image.php', {
            method: 'POST',
            body: formData
        });

        const result = await response.json();

        if (result.success) {
            // 更新Logo URL输入框
            document.getElementById('logo_url').value = result.url;

            // 更新预览
            updateLogoPreview(result.url);

            statusEl.textContent = 'Upload successful!';
            statusEl.className = 'text-sm text-green-600';

            setTimeout(() => {
                statusEl.textContent = '';
            }, 3000);
        } else {
            statusEl.textContent = 'Upload failed: ' + (result.message || 'Unknown error');
            statusEl.className = 'text-sm text-red-600';
        }
    } catch (error) {
        console.error('Logo upload error:', error);
        statusEl.textContent = 'Upload failed: Network error';
        statusEl.className = 'text-sm text-red-600';
    }
}

// 删除截图
function removeScreenshot(index) {
    screenshotUrls.splice(index, 1);
    renderScreenshots();
    document.getElementById('screenshots').value = JSON.stringify(screenshotUrls);
}

document.getElementById('edit-product-form').addEventListener('submit', async function(e) {
    e.preventDefault();

    const submitBtn = document.getElementById('submit-btn');
    const originalText = submitBtn.innerHTML;

    // 显示加载状态
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<svg class="w-4 h-4 mr-2 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path></svg>Updating...';

    try {
        const formData = new FormData(this);

        const response = await fetch('/user-center/ajax/update-product.php', {
            method: 'POST',
            body: formData
        });

        const result = await response.json();

        if (result.success) {
            showAlert('Product updated successfully!', 'success');
            setTimeout(() => {
                window.location.href = '?page=my-products';
            }, 1500);
        } else {
            showAlert(result.message || 'Failed to update product', 'error');
        }
    } catch (error) {
        console.error('Update error:', error);
        showAlert('An error occurred while updating the product', 'error');
    } finally {
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
    }
});

function showAlert(message, type) {
    const alertContainer = document.getElementById('alert-container');
    const alertId = 'alert-' + Date.now();

    const alertColors = {
        success: 'bg-green-100 border-green-400 text-green-700',
        error: 'bg-red-100 border-red-400 text-red-700',
        warning: 'bg-yellow-100 border-yellow-400 text-yellow-700'
    };

    const alertHtml = `
        <div id="${alertId}" class="border-l-4 p-4 mb-4 rounded-md ${alertColors[type] || alertColors.error}">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    ${type === 'success' ?
                        '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path></svg>' :
                        '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path></svg>'
                    }
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium">${message}</p>
                </div>
                <div class="ml-auto pl-3">
                    <button onclick="document.getElementById('${alertId}').remove()" class="text-current hover:opacity-75">
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    `;

    alertContainer.insertAdjacentHTML('beforeend', alertHtml);

    // 自动移除提示
    setTimeout(() => {
        const alertElement = document.getElementById(alertId);
        if (alertElement) {
            alertElement.remove();
        }
    }, 5000);
}
</script>
