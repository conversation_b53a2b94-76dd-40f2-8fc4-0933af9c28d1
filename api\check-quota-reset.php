<?php
/**
 * API配额重置检查器
 * 基于用户注册时间的个性化月度配额重置
 * 需要验证参数防止恶意访问
 */

header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');

// 安全验证 - 需要验证密钥
$requiredKey = 'P2T_QR_8f7e6d5c4b3a2918_2025_SecureReset_Key_9x8y7z';
$providedKey = $_GET['key'] ?? $_POST['key'] ?? '';

if (empty($providedKey) || $providedKey !== $requiredKey) {
    http_response_code(403);
    echo json_encode([
        'success' => false,
        'message' => 'Access denied. Valid security key required.',
        'error_code' => 'INVALID_KEY'
    ]);
    exit;
}

// 定义根路径
define('ROOT_PATH', dirname(__DIR__));
define('APP_INITIALIZED', true);

// 引入数据库连接
require_once ROOT_PATH . '/includes/database-connection.php';

/**
 * 检查是否需要配额重置（基于用户注册时间的个性化周期）
 */
function needsQuotaReset($pdo) {
    try {
        // 检查是否有用户需要配额重置
        // 基于用户注册时间计算个性化重置周期
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as users_need_reset
            FROM pt_member
            WHERE status = 'active'
            AND (
                -- 从未重置过，且注册超过1个月
                (api_reset_date IS NULL AND created_at <= DATE_SUB(CURDATE(), INTERVAL 1 MONTH))
                OR
                -- 已重置过，且距离上次重置超过1个月
                (api_reset_date IS NOT NULL AND api_reset_date <= DATE_SUB(CURDATE(), INTERVAL 1 MONTH))
            )
        ");
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        return $result['users_need_reset'] > 0;
    } catch (Exception $e) {
        return false;
    }
}

/**
 * 执行配额重置（基于用户注册时间的个性化周期）
 */
function executeQuotaReset($pdo) {
    try {
        // 获取重置前的统计信息
        $stmt = $pdo->prepare("
            SELECT
                COUNT(*) as total_users,
                COUNT(CASE WHEN
                    (api_reset_date IS NULL AND created_at <= DATE_SUB(CURDATE(), INTERVAL 1 MONTH))
                    OR
                    (api_reset_date IS NOT NULL AND api_reset_date <= DATE_SUB(CURDATE(), INTERVAL 1 MONTH))
                THEN 1 END) as users_to_reset,
                SUM(CASE WHEN
                    (api_reset_date IS NULL AND created_at <= DATE_SUB(CURDATE(), INTERVAL 1 MONTH))
                    OR
                    (api_reset_date IS NOT NULL AND api_reset_date <= DATE_SUB(CURDATE(), INTERVAL 1 MONTH))
                THEN api_used ELSE 0 END) as total_api_used_to_reset
            FROM pt_member
            WHERE status = 'active'
        ");
        $stmt->execute();
        $beforeStats = $stmt->fetch(PDO::FETCH_ASSOC);

        // 执行个性化配额重置（不使用存储过程，直接执行SQL）
        $stmt = $pdo->prepare("
            UPDATE pt_member
            SET api_used = 0,
                api_reset_date = CURDATE()
            WHERE status = 'active'
            AND (
                -- 从未重置过，且注册超过1个月
                (api_reset_date IS NULL AND created_at <= DATE_SUB(CURDATE(), INTERVAL 1 MONTH))
                OR
                -- 已重置过，且距离上次重置超过1个月
                (api_reset_date IS NOT NULL AND api_reset_date <= DATE_SUB(CURDATE(), INTERVAL 1 MONTH))
            )
        ");
        $stmt->execute();
        $affectedRows = $stmt->rowCount();

        // 清理被重置用户的历史记录
        $stmt = $pdo->prepare("
            DELETE FROM pt_api_usage
            WHERE user_id IN (
                SELECT id FROM pt_member
                WHERE status = 'active' AND api_reset_date = CURDATE()
            )
        ");
        $stmt->execute();

        // 获取重置后的统计信息
        $stmt = $pdo->prepare("
            SELECT
                COUNT(CASE WHEN api_reset_date = CURDATE() THEN 1 END) as users_reset_today
            FROM pt_member
            WHERE status = 'active'
        ");
        $stmt->execute();
        $afterStats = $stmt->fetch(PDO::FETCH_ASSOC);

        return [
            'success' => true,
            'users_reset' => $afterStats['users_reset_today'],
            'affected_rows' => $affectedRows,
            'before_total_used' => $beforeStats['total_api_used_to_reset'] ?? 0,
            'users_to_reset' => $beforeStats['users_to_reset'],
            'reset_method' => 'personalized_by_registration_date'
        ];

    } catch (Exception $e) {
        return [
            'success' => false,
            'error' => $e->getMessage()
        ];
    }
}

try {
    // 获取数据库连接
    $pdo = getDatabaseConnection();

    // 检查是否需要重置
    if (needsQuotaReset($pdo)) {
        // 执行重置
        $result = executeQuotaReset($pdo);

        if ($result['success']) {
            echo json_encode([
                'success' => true,
                'reset_executed' => true,
                'message' => 'Personalized monthly quota reset completed based on registration dates',
                'users_reset' => $result['users_reset'],
                'stats' => [
                    'users_reset' => $result['users_reset'],
                    'affected_rows' => $result['affected_rows'],
                    'before_total_used' => $result['before_total_used'],
                    'users_to_reset' => $result['users_to_reset'],
                    'reset_method' => $result['reset_method'],
                    'execution_time' => date('Y-m-d H:i:s')
                ]
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'reset_executed' => false,
                'message' => 'Quota reset failed: ' . $result['error'],
                'execution_time' => date('Y-m-d H:i:s')
            ]);
        }
    } else {
        echo json_encode([
            'success' => true,
            'reset_executed' => false,
            'message' => 'No users need quota reset at this time',
            'execution_time' => date('Y-m-d H:i:s'),
            'reset_method' => 'personalized_by_registration_date'
        ]);
    }

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'reset_executed' => false,
        'message' => 'Error: ' . $e->getMessage()
    ]);
}
?>
