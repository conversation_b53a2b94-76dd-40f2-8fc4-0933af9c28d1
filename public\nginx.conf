# Nginx配置文件 - AI Tools Platform
# 替代Apache .htaccess文件

server {
    listen 80;
    server_name your-domain.com;
    root /path/to/ai-tools-platform/public;
    index index.php index.html;

    # 启用gzip压缩
    gzip on;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

    # 静态资源缓存
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # PHP文件处理
    location ~ \.php$ {
        try_files $uri =404;
        fastcgi_split_path_info ^(.+\.php)(/.+)$;
        fastcgi_pass unix:/var/run/php/php8.3-fpm.sock;
        fastcgi_index index.php;
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_param PATH_INFO $fastcgi_path_info;
    }

    # URL重写规则 - 工具页面
    location /tools/ {
        try_files $uri $uri/ /index.php?$query_string;
    }

    # API路由
    location /api/ {
        try_files $uri $uri/ /index.php?$query_string;
    }

    # 禁止访问敏感文件和目录
    location ~ /\. {
        deny all;
    }

    location ~ ^/(app|config|database)/ {
        deny all;
    }

    # 默认路由
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }
}
