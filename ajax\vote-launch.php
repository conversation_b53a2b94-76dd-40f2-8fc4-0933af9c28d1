<?php
/**
 * 产品启动投票AJAX处理
 */

header('Content-Type: application/json');

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit;
}

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// 检查用户登录状态
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'Please login to vote']);
    exit;
}

// 使用统一的数据库连接
require_once __DIR__ . '/includes/ajax-database.php';

try {

    $launchId = intval($_POST['launch_id'] ?? 0);
    $userId = $_SESSION['user_id'];

    if ($launchId <= 0) {
        echo json_encode(['success' => false, 'message' => 'Invalid launch ID']);
        exit;
    }

    // 检查产品是否存在且已审核通过
    $launchStmt = $pdo->prepare("SELECT id, votes FROM pt_product_launches WHERE id = ? AND status = 'approved'");
    $launchStmt->execute([$launchId]);
    $launch = $launchStmt->fetch();

    if (!$launch) {
        echo json_encode(['success' => false, 'message' => 'Launch not found or not approved']);
        exit;
    }

    // 检查用户是否已经投票
    $voteCheckStmt = $pdo->prepare("SELECT id FROM pt_launch_votes WHERE launch_id = ? AND user_id = ?");
    $voteCheckStmt->execute([$launchId, $userId]);
    $existingVote = $voteCheckStmt->fetch();

    $pdo->beginTransaction();

    if ($existingVote) {
        // 取消投票
        $deleteVoteStmt = $pdo->prepare("DELETE FROM pt_launch_votes WHERE launch_id = ? AND user_id = ?");
        $deleteVoteStmt->execute([$launchId, $userId]);

        // 减少投票数
        $updateLaunchStmt = $pdo->prepare("UPDATE pt_product_launches SET votes = GREATEST(0, votes - 1) WHERE id = ?");
        $updateLaunchStmt->execute([$launchId]);

        $action = 'removed';
        $newVoteCount = max(0, $launch['votes'] - 1);
    } else {
        // 添加投票
        $insertVoteStmt = $pdo->prepare("INSERT INTO pt_launch_votes (launch_id, user_id, vote_type) VALUES (?, ?, 'up')");
        $insertVoteStmt->execute([$launchId, $userId]);

        // 增加投票数
        $updateLaunchStmt = $pdo->prepare("UPDATE pt_product_launches SET votes = votes + 1 WHERE id = ?");
        $updateLaunchStmt->execute([$launchId]);

        $action = 'added';
        $newVoteCount = $launch['votes'] + 1;
    }

    $pdo->commit();

    echo json_encode([
        'success' => true,
        'action' => $action,
        'votes' => $newVoteCount,
        'message' => $action === 'added' ? 'Vote added successfully!' : 'Vote removed successfully!'
    ]);

} catch (Exception $e) {
    if (isset($pdo)) {
        $pdo->rollBack();
    }
    
    error_log("Vote launch error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'An error occurred while processing your vote. Please try again.'
    ]);
}
?>
