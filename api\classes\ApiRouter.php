<?php
/**
 * API路由器类
 * 处理API路由注册和请求分发
 */

class ApiRouter {
    
    private $routes = [];
    private $middleware = [];
    private $groupPrefix = '';
    private $groupMiddleware = [];
    
    /**
     * 注册GET路由
     */
    public function get($path, $handler) {
        $this->addRoute('GET', $path, $handler);
    }
    
    /**
     * 注册POST路由
     */
    public function post($path, $handler) {
        $this->addRoute('POST', $path, $handler);
    }
    
    /**
     * 注册PUT路由
     */
    public function put($path, $handler) {
        $this->addRoute('PUT', $path, $handler);
    }
    
    /**
     * 注册DELETE路由
     */
    public function delete($path, $handler) {
        $this->addRoute('DELETE', $path, $handler);
    }
    
    /**
     * 注册PATCH路由
     */
    public function patch($path, $handler) {
        $this->addRoute('PATCH', $path, $handler);
    }
    
    /**
     * 路由组
     */
    public function group($prefix, $callback) {
        $originalPrefix = $this->groupPrefix;
        $originalMiddleware = $this->groupMiddleware;
        
        $this->groupPrefix = $originalPrefix . $prefix;
        
        $callback($this);
        
        $this->groupPrefix = $originalPrefix;
        $this->groupMiddleware = $originalMiddleware;
    }
    
    /**
     * 添加中间件
     */
    public function addMiddleware($middleware) {
        $this->middleware[] = $middleware;
    }
    
    /**
     * 添加路由
     */
    private function addRoute($method, $path, $handler) {
        $fullPath = $this->groupPrefix . $path;
        $this->routes[] = [
            'method' => $method,
            'path' => $fullPath,
            'handler' => $handler,
            'middleware' => array_merge($this->middleware, $this->groupMiddleware)
        ];
    }
    
    /**
     * 分发请求
     */
    public function dispatch() {
        $method = $_SERVER['REQUEST_METHOD'];
        $path = $this->getRequestPath();
        
        // 查找匹配的路由
        $route = $this->findRoute($method, $path);
        
        if (!$route) {
            ApiResponse::error([
                'message' => 'Route not found',
                'error_code' => 'ROUTE_NOT_FOUND'
            ], 404);
            return;
        }
        
        // 执行中间件
        foreach ($route['middleware'] as $middleware) {
            if (method_exists($middleware, 'handle')) {
                $result = $middleware->handle();
                if ($result === false) {
                    return; // 中间件阻止了请求
                }
            }
        }
        
        // 执行控制器方法
        $this->executeHandler($route['handler'], $route['params'] ?? []);
    }
    
    /**
     * 获取请求路径
     */
    private function getRequestPath() {
        $path = $_SERVER['REQUEST_URI'];
        
        // 移除查询字符串
        if (($pos = strpos($path, '?')) !== false) {
            $path = substr($path, 0, $pos);
        }
        
        // 移除API前缀
        $apiPrefix = '/api';
        if (strpos($path, $apiPrefix) === 0) {
            $path = substr($path, strlen($apiPrefix));
        }
        
        return $path ?: '/';
    }
    
    /**
     * 查找匹配的路由
     */
    private function findRoute($method, $path) {
        foreach ($this->routes as $route) {
            if ($route['method'] !== $method) {
                continue;
            }
            
            $params = $this->matchPath($route['path'], $path);
            if ($params !== false) {
                $route['params'] = $params;
                return $route;
            }
        }
        
        return null;
    }
    
    /**
     * 匹配路径参数
     */
    private function matchPath($routePath, $requestPath) {
        // 转换路由路径为正则表达式
        $pattern = preg_replace('/\{([^}]+)\}/', '([^/]+)', $routePath);
        $pattern = '#^' . $pattern . '$#';
        
        if (!preg_match($pattern, $requestPath, $matches)) {
            return false;
        }
        
        // 提取参数名
        preg_match_all('/\{([^}]+)\}/', $routePath, $paramNames);
        
        $params = [];
        for ($i = 1; $i < count($matches); $i++) {
            $paramName = $paramNames[1][$i - 1];
            $params[$paramName] = $matches[$i];
        }
        
        return $params;
    }
    
    /**
     * 执行处理器
     */
    private function executeHandler($handler, $params = []) {
        if (is_string($handler)) {
            // 解析控制器@方法格式
            if (strpos($handler, '@') !== false) {
                list($controllerName, $methodName) = explode('@', $handler);
                
                $controllerFile = API_PATH . '/controllers/' . $controllerName . '.php';
                if (!file_exists($controllerFile)) {
                    ApiResponse::error([
                        'message' => 'Controller not found',
                        'error_code' => 'CONTROLLER_NOT_FOUND'
                    ], 500);
                    return;
                }
                
                require_once $controllerFile;
                
                if (!class_exists($controllerName)) {
                    ApiResponse::error([
                        'message' => 'Controller class not found',
                        'error_code' => 'CONTROLLER_CLASS_NOT_FOUND'
                    ], 500);
                    return;
                }
                
                $controller = new $controllerName();
                
                if (!method_exists($controller, $methodName)) {
                    ApiResponse::error([
                        'message' => 'Controller method not found',
                        'error_code' => 'CONTROLLER_METHOD_NOT_FOUND'
                    ], 500);
                    return;
                }
                
                // 调用控制器方法
                call_user_func_array([$controller, $methodName], $params);
            }
        } elseif (is_callable($handler)) {
            // 直接调用函数
            call_user_func_array($handler, $params);
        }
    }
    
    /**
     * 获取所有路由
     */
    public function getRoutes() {
        return $this->routes;
    }
    
    /**
     * 生成API文档数据
     */
    public function generateDocumentation() {
        $docs = [];
        
        foreach ($this->routes as $route) {
            $docs[] = [
                'method' => $route['method'],
                'path' => $route['path'],
                'handler' => $route['handler'],
                'middleware_count' => count($route['middleware'])
            ];
        }
        
        return $docs;
    }
}
?>
