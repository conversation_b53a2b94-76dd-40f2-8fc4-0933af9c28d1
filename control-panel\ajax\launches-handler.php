<?php
/**
 * 产品发布管理 AJAX 处理器
 */

// 关闭错误显示，避免破坏JSON响应
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// 设置响应头
header('Content-Type: application/json');

// 捕获所有输出
ob_start();

session_start();

// 错误处理函数
function sendJsonError($message, $code = 500) {
    ob_clean(); // 清除任何之前的输出
    http_response_code($code);
    echo json_encode(['success' => false, 'message' => $message]);
    exit;
}

// 设置错误处理器
set_error_handler(function($severity, $message, $file, $line) {
    sendJsonError("PHP Error: $message in $file on line $line");
});

// 设置异常处理器
set_exception_handler(function($exception) {
    sendJsonError("Exception: " . $exception->getMessage());
});

// 检查管理员权限
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    sendJsonError('Unauthorized', 401);
}

// 定义必要的常量
if (!defined('ROOT_PATH')) {
    define('ROOT_PATH', dirname(dirname(__DIR__)));
}

// 使用统一的数据库连接
try {
    require_once ROOT_PATH . '/includes/database-connection.php';
    // $pdo 变量已在 database-connection.php 中创建
} catch (Exception $e) {
    sendJsonError('Database connection failed: ' . $e->getMessage());
}

// 获取请求数据
try {
    $rawInput = file_get_contents('php://input');
    if (empty($rawInput)) {
        sendJsonError('No input data received');
    }

    $input = json_decode($rawInput, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        sendJsonError('Invalid JSON data: ' . json_last_error_msg());
    }

    $action = $input['action'] ?? '';
    if (empty($action)) {
        sendJsonError('Missing action parameter');
    }

    switch ($action) {
        case 'update_status':
            updateLaunchStatus($pdo, $input);
            break;

        case 'delete':
            deleteLaunch($pdo, $input);
            break;

        default:
            sendJsonError('Unknown action: ' . $action);
    }
} catch (Exception $e) {
    sendJsonError('Request processing error: ' . $e->getMessage());
}

/**
 * 更新产品状态
 */
function updateLaunchStatus($pdo, $input) {
    $id = intval($input['id'] ?? 0);
    $status = $input['status'] ?? '';
    $notes = $input['notes'] ?? '';

    if (!$id || !in_array($status, ['pending', 'approved', 'rejected', 'featured', 'unfeature'])) {
        echo json_encode(['success' => false, 'message' => 'Invalid parameters']);
        return;
    }

    try {
        $pdo->beginTransaction();

        // 处理精选状态
        if ($status === 'featured') {
            // 设为精选：状态改为approved，设置featured_date
            $stmt = $pdo->prepare("
                UPDATE pt_product_launches
                SET status = 'approved', featured_date = CURDATE(), admin_notes = ?, updated_at = NOW()
                WHERE id = ?
            ");
            $stmt->execute([$notes, $id]);
        } elseif ($status === 'unfeature') {
            // 取消精选：状态保持approved，清除featured_date
            $stmt = $pdo->prepare("
                UPDATE pt_product_launches
                SET status = 'approved', featured_date = NULL, admin_notes = ?, updated_at = NOW()
                WHERE id = ?
            ");
            $stmt->execute([$notes, $id]);
        } else {
            // 普通状态更新
            $featuredDate = null;
            if ($status === 'approved') {
                // 如果从其他状态改为approved，检查是否需要保留featured_date
                $checkStmt = $pdo->prepare("SELECT featured_date FROM pt_product_launches WHERE id = ?");
                $checkStmt->execute([$id]);
                $current = $checkStmt->fetch();
                $featuredDate = $current['featured_date'] ?? null;
            }

            $stmt = $pdo->prepare("
                UPDATE pt_product_launches
                SET status = ?, featured_date = ?, admin_notes = ?, updated_at = NOW()
                WHERE id = ?
            ");
            $stmt->execute([$status, $featuredDate, $notes, $id]);
        }

        // 记录管理员操作日志
        $managerId = $_SESSION['manager_id'] ?? $_SESSION['admin_id'] ?? 1; // 默认使用1作为管理员ID
        $logStmt = $pdo->prepare("
            INSERT INTO pt_activity_log (manager_id, action, description, ip_address, user_agent, created_at)
            VALUES (?, ?, ?, ?, ?, NOW())
        ");
        $logStmt->execute([
            $managerId,
            'update_launch_status',
            "Updated launch ID {$id} status to {$status}" . ($notes ? " with notes: {$notes}" : ""),
            $_SERVER['REMOTE_ADDR'] ?? null,
            $_SERVER['HTTP_USER_AGENT'] ?? null
        ]);

        $pdo->commit();

        echo json_encode(['success' => true, 'message' => 'Launch status updated successfully']);

    } catch (Exception $e) {
        $pdo->rollback();
        error_log("Update launch status error: " . $e->getMessage());
        echo json_encode(['success' => false, 'message' => 'Failed to update launch status']);
    }
}

/**
 * 删除产品
 */
function deleteLaunch($pdo, $input) {
    $id = intval($input['id'] ?? 0);
    
    if (!$id) {
        echo json_encode(['success' => false, 'message' => 'Invalid launch ID']);
        return;
    }
    
    try {
        $pdo->beginTransaction();
        
        // 获取产品信息用于日志
        $stmt = $pdo->prepare("SELECT name, user_id FROM pt_product_launches WHERE id = ?");
        $stmt->execute([$id]);
        $launch = $stmt->fetch();
        
        if (!$launch) {
            echo json_encode(['success' => false, 'message' => 'Launch not found']);
            return;
        }
        
        // 删除相关的投票记录
        $pdo->prepare("DELETE FROM pt_launch_votes WHERE launch_id = ?")->execute([$id]);
        
        // 删除产品
        $stmt = $pdo->prepare("DELETE FROM pt_product_launches WHERE id = ?");
        $stmt->execute([$id]);
        
        // 记录管理员操作日志
        $managerId = $_SESSION['manager_id'] ?? $_SESSION['admin_id'] ?? 1; // 默认使用1作为管理员ID
        $logStmt = $pdo->prepare("
            INSERT INTO pt_activity_log (manager_id, action, description, ip_address, user_agent, created_at)
            VALUES (?, ?, ?, ?, ?, NOW())
        ");
        $logStmt->execute([
            $managerId,
            'delete_launch',
            "Deleted launch '{$launch['name']}' (ID: {$id}, User ID: {$launch['user_id']})",
            $_SERVER['REMOTE_ADDR'] ?? null,
            $_SERVER['HTTP_USER_AGENT'] ?? null
        ]);
        
        $pdo->commit();
        
        echo json_encode(['success' => true, 'message' => 'Launch deleted successfully']);
        
    } catch (Exception $e) {
        $pdo->rollback();
        error_log("Delete launch error: " . $e->getMessage());
        echo json_encode(['success' => false, 'message' => 'Failed to delete launch']);
    }
}
?>
