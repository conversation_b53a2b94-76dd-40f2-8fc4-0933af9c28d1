<?php
/**
 * 通用函数库
 * 提供项目中常用的工具函数
 */

/**
 * 获取当前完整URL
 */
function getCurrentURL() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
    $uri = $_SERVER['REQUEST_URI'] ?? '/';
    return $protocol . '://' . $host . $uri;
}

/**
 * 获取完整URL
 */
function getFullURL($path) {
    // 检查 $path 是否为 null 或空
    if (empty($path)) {
        return '';
    }

    if (strpos($path, 'http') === 0) {
        return $path;
    }
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
    return $protocol . '://' . $host . $path;
}

/**
 * 安全的HTML输出
 */
function e($string) {
    return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
}

/**
 * 生成CSRF令牌
 */
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

/**
 * 验证CSRF令牌
 */
function validateCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * 重定向函数
 */
function redirect($url, $statusCode = 302) {
    header('Location: ' . $url, true, $statusCode);
    exit;
}

/**
 * JSON响应
 */
function jsonResponse($data, $statusCode = 200) {
    http_response_code($statusCode);
    header('Content-Type: application/json');
    echo json_encode($data, JSON_UNESCAPED_UNICODE);
    exit;
}

/**
 * 格式化字节大小
 */
function formatBytes($bytes, $precision = 2) {
    $units = array('B', 'KB', 'MB', 'GB', 'TB');
    
    for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
        $bytes /= 1024;
    }
    
    return round($bytes, $precision) . ' ' . $units[$i];
}

/**
 * 生成随机字符串
 */
function generateRandomString($length = 10) {
    return substr(str_shuffle(str_repeat($x='0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ', ceil($length/strlen($x)) )),1,$length);
}

/**
 * 检查是否为AJAX请求
 */
function isAjaxRequest() {
    return isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
           strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
}

/**
 * 获取客户端IP地址
 */
function getClientIP() {
    $ipKeys = ['HTTP_CF_CONNECTING_IP', 'HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR'];
    
    foreach ($ipKeys as $key) {
        if (array_key_exists($key, $_SERVER) === true) {
            foreach (explode(',', $_SERVER[$key]) as $ip) {
                $ip = trim($ip);
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) !== false) {
                    return $ip;
                }
            }
        }
    }
    
    return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
}



/**
 * 简单的模板渲染函数
 */
function renderTemplate($template, $data = []) {
    extract($data);
    ob_start();
    include ROOT_PATH . '/templates/' . $template . '.php';
    return ob_get_clean();
}

/**
 * 获取配置值
 */
function config($key, $default = null) {
    static $config = null;

    if ($config === null) {
        $appConfig = file_exists(ROOT_PATH . '/config/app.php') ? include ROOT_PATH . '/config/app.php' : [];
        $dbConfig = file_exists(ROOT_PATH . '/config/database.php') ? include ROOT_PATH . '/config/database.php' : [];
        $config = array_merge($appConfig, ['database' => $dbConfig]);
    }

    $keys = explode('.', $key);
    $value = $config;

    foreach ($keys as $k) {
        if (!isset($value[$k])) {
            return $default;
        }
        $value = $value[$k];
    }

    return $value;
}

/**
 * 验证邮箱地址
 */
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * 验证URL
 */
function isValidURL($url) {
    return filter_var($url, FILTER_VALIDATE_URL) !== false;
}

/**
 * 生成安全的文件名
 */
function sanitizeFileName($filename) {
    // 移除危险字符
    $filename = preg_replace('/[^a-zA-Z0-9._-]/', '', $filename);
    // 限制长度
    $filename = substr($filename, 0, 255);
    // 确保不为空
    return $filename ?: 'file_' . time();
}

/**
 * 获取文件扩展名
 */
function getFileExtension($filename) {
    return strtolower(pathinfo($filename, PATHINFO_EXTENSION));
}

/**
 * 检查文件类型是否允许
 */
function isAllowedFileType($filename, $category = 'image') {
    $allowedTypes = config('upload.allowed_types.' . $category, []);
    $extension = getFileExtension($filename);
    return in_array($extension, $allowedTypes);
}

/**
 * 格式化时间为人类可读格式
 */
function timeAgo($datetime) {
    $time = time() - strtotime($datetime);

    if ($time < 60) return 'just now';
    if ($time < 3600) return floor($time/60) . ' minutes ago';
    if ($time < 86400) return floor($time/3600) . ' hours ago';
    if ($time < 2592000) return floor($time/86400) . ' days ago';
    if ($time < 31536000) return floor($time/2592000) . ' months ago';

    return floor($time/31536000) . ' years ago';
}

/**
 * 截断文本
 */
function truncateText($text, $length = 100, $suffix = '...') {
    if (strlen($text) <= $length) {
        return $text;
    }

    return substr($text, 0, $length) . $suffix;
}

/**
 * 生成SEO友好的URL slug
 */
function generateSlug($text) {
    // 转换为小写
    $text = strtolower($text);
    // 移除特殊字符
    $text = preg_replace('/[^a-z0-9\s-]/', '', $text);
    // 替换空格和多个连字符为单个连字符
    $text = preg_replace('/[\s-]+/', '-', $text);
    // 移除首尾连字符
    return trim($text, '-');
}

/**
 * 验证SLUG是否唯一 (示例函数，需要数据库支持)
 */
function isUniqueSlug($slug, $table = 'tools', $excludeId = null) {
    // 这里应该连接数据库检查
    // 暂时返回true作为示例
    return true;
}

/**
 * 生成唯一的SLUG
 */
function generateUniqueSlug($text, $table = 'tools', $excludeId = null) {
    $baseSlug = generateSlug($text);
    $slug = $baseSlug;
    $counter = 1;

    while (!isUniqueSlug($slug, $table, $excludeId)) {
        $slug = $baseSlug . '-' . $counter;
        $counter++;
    }

    return $slug;
}

/**
 * 清理和验证输入数据
 */
function sanitizeInput($data) {
    if (is_array($data)) {
        return array_map('sanitizeInput', $data);
    }

    return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
}

/**
 * 生成随机颜色
 */
function generateRandomColor() {
    return sprintf('#%06X', mt_rand(0, 0xFFFFFF));
}

/**
 * 检查是否为移动设备
 */
function isMobileDevice() {
    return preg_match('/Mobile|Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i', $_SERVER['HTTP_USER_AGENT'] ?? '');
}

/**
 * 获取用户代理信息
 */
function getUserAgent() {
    return $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown';
}

/**
 * 记录简单的访问统计 (内存中，不写文件)
 */
function logPageView($page) {
    if (!isset($_SESSION['page_views'])) {
        $_SESSION['page_views'] = [];
    }

    $_SESSION['page_views'][] = [
        'page' => $page,
        'timestamp' => time(),
        'ip' => getClientIP(),
        'user_agent' => getUserAgent()
    ];

    // 只保留最近50条记录
    if (count($_SESSION['page_views']) > 50) {
        $_SESSION['page_views'] = array_slice($_SESSION['page_views'], -50);
    }
}

/**
 * 获取页面访问统计
 */
function getPageViewStats() {
    return $_SESSION['page_views'] ?? [];
}

/**
 * 简单的缓存函数 (基于文件)
 */
function cache($key, $callback = null, $ttl = 3600) {
    $cacheFile = CACHE_PATH . '/' . md5($key) . '.cache';

    // 如果只是获取缓存
    if ($callback === null) {
        if (file_exists($cacheFile) && (time() - filemtime($cacheFile)) < $ttl) {
            return unserialize(file_get_contents($cacheFile));
        }
        return null;
    }

    // 检查缓存是否存在且未过期
    if (file_exists($cacheFile) && (time() - filemtime($cacheFile)) < $ttl) {
        return unserialize(file_get_contents($cacheFile));
    }

    // 生成新的缓存
    $data = $callback();
    file_put_contents($cacheFile, serialize($data));

    return $data;
}

/**
 * 清理过期缓存
 */
function clearExpiredCache() {
    if (!is_dir(CACHE_PATH)) return;

    $files = glob(CACHE_PATH . '/*.cache');
    foreach ($files as $file) {
        if (time() - filemtime($file) > 86400) { // 24小时
            unlink($file);
        }
    }
}
