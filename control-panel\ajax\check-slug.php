<?php
/**
 * 检查Slug唯一性的AJAX处理器
 */

// 关闭错误显示，避免破坏JSON响应
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// 设置响应头
header('Content-Type: application/json');

// 捕获所有输出
ob_start();

session_start();

// 错误处理函数
function sendJsonError($message, $code = 500) {
    ob_clean(); // 清除任何之前的输出
    http_response_code($code);
    echo json_encode(['success' => false, 'message' => $message]);
    exit;
}

// 检查管理员权限
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    sendJsonError('Unauthorized', 401);
}

// 定义必要的常量
if (!defined('ROOT_PATH')) {
    define('ROOT_PATH', dirname(dirname(__DIR__)));
}

// 使用统一的数据库连接
try {
    require_once dirname(dirname(__DIR__)) . '/includes/database-connection.php';
    // $pdo 变量已在 database-connection.php 中创建
} catch (Exception $e) {
    sendJsonError('Database connection failed: ' . $e->getMessage());
}

// 获取请求参数
$slug = $_GET['slug'] ?? '';
$excludeId = $_GET['exclude_id'] ?? null;

if (empty($slug)) {
    sendJsonError('Slug parameter is required');
}

try {
    // 检查slug是否已存在（排除当前产品ID）
    if ($excludeId) {
        $stmt = $pdo->prepare("SELECT id FROM pt_product_launches WHERE slug = ? AND id != ?");
        $stmt->execute([$slug, $excludeId]);
    } else {
        $stmt = $pdo->prepare("SELECT id FROM pt_product_launches WHERE slug = ?");
        $stmt->execute([$slug]);
    }
    
    $exists = $stmt->fetch();
    
    ob_clean(); // 清除任何输出
    echo json_encode([
        'success' => true,
        'available' => !$exists,
        'message' => $exists ? 'Slug already exists' : 'Slug is available'
    ]);

} catch (Exception $e) {
    sendJsonError('Database error: ' . $e->getMessage());
}
?>
