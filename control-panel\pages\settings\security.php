<?php
/**
 * 安全设置页面
 * 提供系统安全配置管理
 */

// 定义根目录
define('ROOT_PATH', dirname(dirname(dirname(__DIR__))));

// 加载依赖
require_once dirname(__DIR__) . '/../auth/middleware.php';
require_once dirname(__DIR__) . '/../middleware/PermissionMiddleware.php';
require_once dirname(__DIR__) . '/../classes/Database.php';

// 权限检查
requirePermission('settings.security');

// 初始化数据库
$db = Database::getInstance();

// 处理表单提交
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // 获取表单数据
        $settings = [
            // 密码策略
            'password_min_length' => intval($_POST['password_min_length'] ?? 8),
            'password_require_uppercase' => isset($_POST['password_require_uppercase']) ? 1 : 0,
            'password_require_lowercase' => isset($_POST['password_require_lowercase']) ? 1 : 0,
            'password_require_numbers' => isset($_POST['password_require_numbers']) ? 1 : 0,
            'password_require_symbols' => isset($_POST['password_require_symbols']) ? 1 : 0,
            'password_expiry_days' => intval($_POST['password_expiry_days'] ?? 0),
            
            // 登录安全
            'max_login_attempts' => intval($_POST['max_login_attempts'] ?? 5),
            'login_lockout_duration' => intval($_POST['login_lockout_duration'] ?? 15),
            'session_timeout' => intval($_POST['session_timeout'] ?? 1440),
            'force_https' => isset($_POST['force_https']) ? 1 : 0,
            'remember_me_enabled' => isset($_POST['remember_me_enabled']) ? 1 : 0,
            'remember_me_duration' => intval($_POST['remember_me_duration'] ?? 30),
            
            // 两步验证
            'two_factor_enabled' => isset($_POST['two_factor_enabled']) ? 1 : 0,
            'two_factor_required_for_admins' => isset($_POST['two_factor_required_for_admins']) ? 1 : 0,
            
            // IP访问控制
            'ip_whitelist_enabled' => isset($_POST['ip_whitelist_enabled']) ? 1 : 0,
            'ip_whitelist' => trim($_POST['ip_whitelist'] ?? ''),
            'ip_blacklist_enabled' => isset($_POST['ip_blacklist_enabled']) ? 1 : 0,
            'ip_blacklist' => trim($_POST['ip_blacklist'] ?? ''),
            
            // 访问频率限制
            'rate_limit_enabled' => isset($_POST['rate_limit_enabled']) ? 1 : 0,
            'rate_limit_requests' => intval($_POST['rate_limit_requests'] ?? 100),
            'rate_limit_window' => intval($_POST['rate_limit_window'] ?? 60),
            
            // 文件上传安全
            'upload_max_size' => intval($_POST['upload_max_size'] ?? 10),
            'upload_allowed_types' => trim($_POST['upload_allowed_types'] ?? ''),
            'upload_scan_viruses' => isset($_POST['upload_scan_viruses']) ? 1 : 0,
            
            // 安全头部
            'security_headers_enabled' => isset($_POST['security_headers_enabled']) ? 1 : 0,
            'content_security_policy' => trim($_POST['content_security_policy'] ?? ''),
            
            // 审计日志
            'audit_log_enabled' => isset($_POST['audit_log_enabled']) ? 1 : 0,
            'audit_log_retention_days' => intval($_POST['audit_log_retention_days'] ?? 90),
            
            // 入侵检测
            'intrusion_detection_enabled' => isset($_POST['intrusion_detection_enabled']) ? 1 : 0,
            'suspicious_activity_threshold' => intval($_POST['suspicious_activity_threshold'] ?? 10)
        ];
        
        // 验证设置
        $errors = [];
        if ($settings['password_min_length'] < 6 || $settings['password_min_length'] > 128) {
            $errors[] = 'Password minimum length must be between 6 and 128 characters';
        }
        
        if ($settings['max_login_attempts'] < 1 || $settings['max_login_attempts'] > 100) {
            $errors[] = 'Max login attempts must be between 1 and 100';
        }
        
        if ($settings['session_timeout'] < 5 || $settings['session_timeout'] > 10080) {
            $errors[] = 'Session timeout must be between 5 minutes and 1 week';
        }
        
        if (!empty($errors)) {
            throw new Exception(implode(', ', $errors));
        }
        
        // 开始事务
        $db->beginTransaction();
        
        try {
            // 更新设置
            foreach ($settings as $key => $value) {
                $existing = $db->fetch(
                    "SELECT id FROM pt_system_config WHERE setting_key = :key",
                    ['key' => $key]
                );
                
                if ($existing) {
                    $db->update(
                        'pt_system_config',
                        ['setting_value' => $value, 'updated_at' => date('Y-m-d H:i:s')],
                        'id = :id',
                        ['id' => $existing['id']]
                    );
                } else {
                    $db->insert('pt_system_config', [
                        'setting_key' => $key,
                        'setting_value' => $value,
                        'setting_type' => is_numeric($value) ? 'integer' : 'string',
                        'setting_group' => 'security',
                        'is_public' => 0
                    ]);
                }
            }
            
            // 记录操作
            $currentUser = getCurrentAdmin();
            $logData = [
                'admin_id' => $currentUser['id'],
                'activity_type' => 'settings_update',
                'description' => 'Updated security settings',
                'target_type' => 'settings',
                'target_id' => null,
                'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
                'metadata' => json_encode(['settings_group' => 'security'])
            ];
            $db->insert('pt_activity_log', $logData);
            
            $db->commit();
            
            $message = 'Security settings updated successfully!';
            $messageType = 'success';
            
        } catch (Exception $e) {
            $db->rollback();
            throw $e;
        }
        
    } catch (Exception $e) {
        $message = $e->getMessage();
        $messageType = 'error';
    }
}

// 获取当前设置
$currentSettings = [];
$settingsResult = $db->fetchAll(
    "SELECT setting_key as `key`, setting_value as `value` FROM pt_system_config WHERE setting_group = 'security'"
);

foreach ($settingsResult as $setting) {
    $currentSettings[$setting['key']] = $setting['value'];
}

// 设置默认值
$defaults = [
    'password_min_length' => 8,
    'password_require_uppercase' => 1,
    'password_require_lowercase' => 1,
    'password_require_numbers' => 1,
    'password_require_symbols' => 1,
    'password_expiry_days' => 0,
    'max_login_attempts' => 5,
    'login_lockout_duration' => 15,
    'session_timeout' => 1440,
    'force_https' => 1,
    'remember_me_enabled' => 1,
    'remember_me_duration' => 30,
    'two_factor_enabled' => 0,
    'two_factor_required_for_admins' => 0,
    'ip_whitelist_enabled' => 0,
    'ip_whitelist' => '',
    'ip_blacklist_enabled' => 0,
    'ip_blacklist' => '',
    'rate_limit_enabled' => 1,
    'rate_limit_requests' => 100,
    'rate_limit_window' => 60,
    'upload_max_size' => 10,
    'upload_allowed_types' => 'jpg,jpeg,png,gif,pdf,doc,docx,txt',
    'upload_scan_viruses' => 0,
    'security_headers_enabled' => 1,
    'content_security_policy' => "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';",
    'audit_log_enabled' => 1,
    'audit_log_retention_days' => 90,
    'intrusion_detection_enabled' => 1,
    'suspicious_activity_threshold' => 10
];

foreach ($defaults as $key => $value) {
    if (!isset($currentSettings[$key])) {
        $currentSettings[$key] = $value;
    }
}

$currentPage = 'settings';
include_once dirname(__DIR__) . '/../components/layout/header.php';
?>

<div class="min-h-screen bg-gray-100">
    <!-- 侧边栏 -->
    <div class="fixed inset-y-0 left-0 w-64 bg-gray-900">
        <?php include_once dirname(__DIR__) . '/../components/layout/sidebar.php'; ?>
    </div>
    
    <!-- 主内容区 -->
    <div class="ml-64">
        <!-- 顶部导航栏 -->
        <div class="bg-white shadow-sm border-b border-gray-200">
            <div class="px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-16">
                    <div class="flex items-center">
                        <nav class="flex space-x-4">
                            <a href="general.php" class="text-gray-500 hover:text-gray-700 px-3 py-2 text-sm font-medium">
                                General
                            </a>
                            <a href="security.php" class="text-accent px-3 py-2 text-sm font-medium border-b-2 border-accent">
                                Security
                            </a>
                            <a href="email.php" class="text-gray-500 hover:text-gray-700 px-3 py-2 text-sm font-medium">
                                Email
                            </a>
                        </nav>
                        <h1 class="ml-4 text-2xl font-semibold text-gray-900">Security Settings</h1>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 页面内容 -->
        <div class="p-6">
            <!-- 消息提示 -->
            <?php if ($message): ?>
            <div class="mb-6 p-4 border-l-4 <?= $messageType === 'success' ? 'bg-green-50 border-green-400 text-green-700' : 'bg-red-50 border-red-400 text-red-700' ?>">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <i class="fas fa-<?= $messageType === 'success' ? 'check-circle' : 'exclamation-circle' ?>"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm"><?= htmlspecialchars($message) ?></p>
                    </div>
                </div>
            </div>
            <?php endif; ?>
            
            <!-- 设置表单 -->
            <div class="bg-white border border-gray-200">
                <form method="POST" id="securitySettingsForm" class="space-y-6">
                    <!-- 密码策略 -->
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Password Policy</h3>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Minimum Length</label>
                                <input type="number" name="password_min_length" min="6" max="128"
                                       value="<?= htmlspecialchars($currentSettings['password_min_length']) ?>"
                                       class="w-full px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Password Expiry (days, 0 = never)</label>
                                <input type="number" name="password_expiry_days" min="0" max="365"
                                       value="<?= htmlspecialchars($currentSettings['password_expiry_days']) ?>"
                                       class="w-full px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent">
                            </div>
                        </div>
                        
                        <div class="mt-6 space-y-3">
                            <label class="flex items-center">
                                <input type="checkbox" name="password_require_uppercase" value="1" 
                                       <?= $currentSettings['password_require_uppercase'] ? 'checked' : '' ?>
                                       class="mr-2 text-accent focus:ring-accent">
                                <span class="text-sm font-medium text-gray-700">Require uppercase letters</span>
                            </label>
                            
                            <label class="flex items-center">
                                <input type="checkbox" name="password_require_lowercase" value="1" 
                                       <?= $currentSettings['password_require_lowercase'] ? 'checked' : '' ?>
                                       class="mr-2 text-accent focus:ring-accent">
                                <span class="text-sm font-medium text-gray-700">Require lowercase letters</span>
                            </label>
                            
                            <label class="flex items-center">
                                <input type="checkbox" name="password_require_numbers" value="1" 
                                       <?= $currentSettings['password_require_numbers'] ? 'checked' : '' ?>
                                       class="mr-2 text-accent focus:ring-accent">
                                <span class="text-sm font-medium text-gray-700">Require numbers</span>
                            </label>
                            
                            <label class="flex items-center">
                                <input type="checkbox" name="password_require_symbols" value="1" 
                                       <?= $currentSettings['password_require_symbols'] ? 'checked' : '' ?>
                                       class="mr-2 text-accent focus:ring-accent">
                                <span class="text-sm font-medium text-gray-700">Require special characters</span>
                            </label>
                        </div>
                    </div>
                    
                    <!-- 登录安全 -->
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Login Security</h3>
                        
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Max Login Attempts</label>
                                <input type="number" name="max_login_attempts" min="1" max="100"
                                       value="<?= htmlspecialchars($currentSettings['max_login_attempts']) ?>"
                                       class="w-full px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Lockout Duration (minutes)</label>
                                <input type="number" name="login_lockout_duration" min="1" max="1440"
                                       value="<?= htmlspecialchars($currentSettings['login_lockout_duration']) ?>"
                                       class="w-full px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Session Timeout (minutes)</label>
                                <input type="number" name="session_timeout" min="5" max="10080"
                                       value="<?= htmlspecialchars($currentSettings['session_timeout']) ?>"
                                       class="w-full px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent">
                            </div>
                        </div>
                        
                        <div class="mt-6 space-y-3">
                            <label class="flex items-center">
                                <input type="checkbox" name="force_https" value="1" 
                                       <?= $currentSettings['force_https'] ? 'checked' : '' ?>
                                       class="mr-2 text-accent focus:ring-accent">
                                <span class="text-sm font-medium text-gray-700">Force HTTPS</span>
                            </label>
                            
                            <label class="flex items-center">
                                <input type="checkbox" name="remember_me_enabled" value="1" 
                                       <?= $currentSettings['remember_me_enabled'] ? 'checked' : '' ?>
                                       class="mr-2 text-accent focus:ring-accent">
                                <span class="text-sm font-medium text-gray-700">Enable "Remember Me"</span>
                            </label>
                        </div>
                        
                        <div class="mt-6">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Remember Me Duration (days)</label>
                            <input type="number" name="remember_me_duration" min="1" max="365"
                                   value="<?= htmlspecialchars($currentSettings['remember_me_duration']) ?>"
                                   class="w-full md:w-1/3 px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent">
                        </div>
                    </div>
                    
                    <!-- 两步验证 -->
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Two-Factor Authentication</h3>
                        
                        <div class="space-y-3">
                            <label class="flex items-center">
                                <input type="checkbox" name="two_factor_enabled" value="1" 
                                       <?= $currentSettings['two_factor_enabled'] ? 'checked' : '' ?>
                                       class="mr-2 text-accent focus:ring-accent">
                                <span class="text-sm font-medium text-gray-700">Enable Two-Factor Authentication</span>
                            </label>
                            
                            <label class="flex items-center">
                                <input type="checkbox" name="two_factor_required_for_admins" value="1" 
                                       <?= $currentSettings['two_factor_required_for_admins'] ? 'checked' : '' ?>
                                       class="mr-2 text-accent focus:ring-accent">
                                <span class="text-sm font-medium text-gray-700">Require 2FA for all administrators</span>
                            </label>
                        </div>
                    </div>
                    
                    <!-- IP访问控制 -->
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">IP Access Control</h3>
                        
                        <div class="space-y-6">
                            <div>
                                <label class="flex items-center mb-2">
                                    <input type="checkbox" name="ip_whitelist_enabled" value="1" 
                                           <?= $currentSettings['ip_whitelist_enabled'] ? 'checked' : '' ?>
                                           class="mr-2 text-accent focus:ring-accent">
                                    <span class="text-sm font-medium text-gray-700">Enable IP Whitelist</span>
                                </label>
                                <textarea name="ip_whitelist" rows="3"
                                          class="w-full px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent"
                                          placeholder="Enter IP addresses, one per line (e.g., ***********, 10.0.0.0/8)"><?= htmlspecialchars($currentSettings['ip_whitelist']) ?></textarea>
                            </div>
                            
                            <div>
                                <label class="flex items-center mb-2">
                                    <input type="checkbox" name="ip_blacklist_enabled" value="1" 
                                           <?= $currentSettings['ip_blacklist_enabled'] ? 'checked' : '' ?>
                                           class="mr-2 text-accent focus:ring-accent">
                                    <span class="text-sm font-medium text-gray-700">Enable IP Blacklist</span>
                                </label>
                                <textarea name="ip_blacklist" rows="3"
                                          class="w-full px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent"
                                          placeholder="Enter IP addresses to block, one per line"><?= htmlspecialchars($currentSettings['ip_blacklist']) ?></textarea>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 访问频率限制 -->
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Rate Limiting</h3>
                        
                        <div class="space-y-6">
                            <label class="flex items-center">
                                <input type="checkbox" name="rate_limit_enabled" value="1" 
                                       <?= $currentSettings['rate_limit_enabled'] ? 'checked' : '' ?>
                                       class="mr-2 text-accent focus:ring-accent">
                                <span class="text-sm font-medium text-gray-700">Enable Rate Limiting</span>
                            </label>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Max Requests</label>
                                    <input type="number" name="rate_limit_requests" min="1" max="10000"
                                           value="<?= htmlspecialchars($currentSettings['rate_limit_requests']) ?>"
                                           class="w-full px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent">
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Time Window (seconds)</label>
                                    <input type="number" name="rate_limit_window" min="1" max="3600"
                                           value="<?= htmlspecialchars($currentSettings['rate_limit_window']) ?>"
                                           class="w-full px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent">
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 文件上传安全 -->
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">File Upload Security</h3>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Max Upload Size (MB)</label>
                                <input type="number" name="upload_max_size" min="1" max="1024"
                                       value="<?= htmlspecialchars($currentSettings['upload_max_size']) ?>"
                                       class="w-full px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent">
                            </div>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Allowed File Types</label>
                                <input type="text" name="upload_allowed_types"
                                       value="<?= htmlspecialchars($currentSettings['upload_allowed_types']) ?>"
                                       class="w-full px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent"
                                       placeholder="jpg,png,pdf,doc">
                            </div>
                        </div>
                        
                        <div class="mt-6">
                            <label class="flex items-center">
                                <input type="checkbox" name="upload_scan_viruses" value="1" 
                                       <?= $currentSettings['upload_scan_viruses'] ? 'checked' : '' ?>
                                       class="mr-2 text-accent focus:ring-accent">
                                <span class="text-sm font-medium text-gray-700">Scan uploads for viruses</span>
                            </label>
                        </div>
                    </div>
                    
                    <!-- 安全头部 -->
                    <div class="p-6 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Security Headers</h3>
                        
                        <div class="space-y-6">
                            <label class="flex items-center">
                                <input type="checkbox" name="security_headers_enabled" value="1" 
                                       <?= $currentSettings['security_headers_enabled'] ? 'checked' : '' ?>
                                       class="mr-2 text-accent focus:ring-accent">
                                <span class="text-sm font-medium text-gray-700">Enable Security Headers</span>
                            </label>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Content Security Policy</label>
                                <textarea name="content_security_policy" rows="3"
                                          class="w-full px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent"
                                          placeholder="default-src 'self'; script-src 'self';"><?= htmlspecialchars($currentSettings['content_security_policy']) ?></textarea>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 审计和监控 -->
                    <div class="p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Audit & Monitoring</h3>
                        
                        <div class="space-y-6">
                            <label class="flex items-center">
                                <input type="checkbox" name="audit_log_enabled" value="1" 
                                       <?= $currentSettings['audit_log_enabled'] ? 'checked' : '' ?>
                                       class="mr-2 text-accent focus:ring-accent">
                                <span class="text-sm font-medium text-gray-700">Enable Audit Logging</span>
                            </label>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Log Retention (days)</label>
                                <input type="number" name="audit_log_retention_days" min="1" max="3650"
                                       value="<?= htmlspecialchars($currentSettings['audit_log_retention_days']) ?>"
                                       class="w-full md:w-1/3 px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent">
                            </div>
                            
                            <label class="flex items-center">
                                <input type="checkbox" name="intrusion_detection_enabled" value="1" 
                                       <?= $currentSettings['intrusion_detection_enabled'] ? 'checked' : '' ?>
                                       class="mr-2 text-accent focus:ring-accent">
                                <span class="text-sm font-medium text-gray-700">Enable Intrusion Detection</span>
                            </label>
                            
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Suspicious Activity Threshold</label>
                                <input type="number" name="suspicious_activity_threshold" min="1" max="100"
                                       value="<?= htmlspecialchars($currentSettings['suspicious_activity_threshold']) ?>"
                                       class="w-full md:w-1/3 px-3 py-2 border border-gray-300 focus:ring-2 focus:ring-accent focus:border-accent">
                            </div>
                        </div>
                    </div>
                    
                    <!-- 提交按钮 -->
                    <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-end">
                        <button type="submit" 
                                class="px-6 py-2 bg-accent text-white hover:bg-blue-600">
                            <i class="fas fa-save mr-2"></i>Save Security Settings
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// 表单验证
document.getElementById('securitySettingsForm').addEventListener('submit', function(e) {
    const passwordMinLength = parseInt(document.querySelector('input[name="password_min_length"]').value);
    const maxLoginAttempts = parseInt(document.querySelector('input[name="max_login_attempts"]').value);
    const sessionTimeout = parseInt(document.querySelector('input[name="session_timeout"]').value);
    
    if (passwordMinLength < 6 || passwordMinLength > 128) {
        alert('Password minimum length must be between 6 and 128 characters');
        e.preventDefault();
        return;
    }
    
    if (maxLoginAttempts < 1 || maxLoginAttempts > 100) {
        alert('Max login attempts must be between 1 and 100');
        e.preventDefault();
        return;
    }
    
    if (sessionTimeout < 5 || sessionTimeout > 10080) {
        alert('Session timeout must be between 5 minutes and 1 week');
        e.preventDefault();
        return;
    }
});
</script>

<?php include_once dirname(__DIR__) . '/../components/layout/footer.php'; ?>
