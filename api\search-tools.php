<?php
/**
 * 工具搜索API
 * 提供实时搜索功能
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type');

// 设置根目录和安全常量
define('ROOT_PATH', dirname(__DIR__));
define('APP_INITIALIZED', true);

try {
    // 获取搜索关键词 - 只处理POST请求
    $query = '';
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $query = trim($_POST['query'] ?? '');
    }

    // 验证搜索关键词
    if (empty($query)) {
        echo json_encode([
            'success' => false,
            'message' => 'Search query is required',
            'method' => $_SERVER['REQUEST_METHOD'],
            'post_data' => $_POST,
            'results' => []
        ]);
        exit;
    }

    // 搜索关键词长度限制
    if (strlen($query) < 2) {
        echo json_encode([
            'success' => false,
            'message' => 'Search query must be at least 2 characters',
            'results' => []
        ]);
        exit;
    }

    // 使用统一的数据库连接
    require_once dirname(__DIR__) . '/includes/database-connection.php';

    // 首先获取所有分类数据
    $categoryMap = [];
    try {
        $categoryStmt = $pdo->query("SELECT id, name, slug FROM pt_tool_category WHERE status = 'active'");
        $categories = $categoryStmt->fetchAll();
        foreach ($categories as $category) {
            $categoryMap[$category['id']] = [
                'name' => $category['name'],
                'slug' => $category['slug']
            ];
        }
    } catch (Exception $e) {
        // 如果pt_tool_category表不存在，使用默认分类
        $categoryMap = [
            1 => ['name' => 'Development', 'slug' => 'development'],
            2 => ['name' => 'Design', 'slug' => 'design'],
            3 => ['name' => 'Utilities', 'slug' => 'utilities'],
            4 => ['name' => 'Security', 'slug' => 'security']
        ];
    }

    // 构建搜索查询
    $searchQuery = "
        SELECT
            id,
            name,
            slug,
            description,
            view_count,
            status,
            category_id
        FROM pt_tool
        WHERE status = 'active'
        AND (
            name LIKE :query1
            OR description LIKE :query2
            OR slug LIKE :query3
        )
        ORDER BY
            CASE
                WHEN name LIKE :exact_query THEN 1
                WHEN name LIKE :start_query THEN 2
                WHEN description LIKE :start_query2 THEN 3
                ELSE 4
            END,
            view_count DESC,
            name ASC
        LIMIT 20
    ";

    $searchTerm = "%{$query}%";
    $exactTerm = $query;
    $startTerm = "{$query}%";

    $stmt = $pdo->prepare($searchQuery);
    $stmt->execute([
        'query1' => $searchTerm,
        'query2' => $searchTerm,
        'query3' => $searchTerm,
        'exact_query' => $exactTerm,
        'start_query' => $startTerm,
        'start_query2' => $startTerm
    ]);

    $results = $stmt->fetchAll();

    // 格式化结果
    $formattedResults = [];
    foreach ($results as $tool) {
        $categoryInfo = $categoryMap[$tool['category_id']] ?? ['name' => 'Other', 'slug' => 'other'];
        $toolUrl = "/tools/" . $categoryInfo['slug'] . "/" . $tool['slug'];

        $formattedResults[] = [
            'id' => $tool['id'],
            'name' => $tool['name'],
            'slug' => $tool['slug'],
            'description' => $tool['description'],
            'category' => $categoryInfo['name'],
            'category_slug' => $categoryInfo['slug'],
            'view_count' => intval($tool['view_count']),
            'url' => $toolUrl,
            'highlighted_name' => highlightSearchTerm($tool['name'], $query),
            'highlighted_description' => highlightSearchTerm($tool['description'], $query)
        ];
    }

    // 返回结果
    echo json_encode([
        'success' => true,
        'query' => $query,
        'total' => count($formattedResults),
        'results' => $formattedResults
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Search failed: ' . $e->getMessage(),
        'results' => []
    ]);
}

/**
 * 高亮搜索关键词
 */
function highlightSearchTerm($text, $term) {
    if (empty($term) || empty($text)) {
        return $text;
    }
    
    // 转义特殊字符
    $escapedTerm = preg_quote($term, '/');
    
    // 高亮匹配的文本（不区分大小写）- 使用蓝色主题
    return preg_replace(
        '/(' . $escapedTerm . ')/i',
        '<mark class="bg-blue-500 text-white px-1 rounded">$1</mark>',
        $text
    );
}
?>
