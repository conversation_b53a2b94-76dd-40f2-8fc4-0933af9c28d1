<?php
/**
 * Check Duplicate Message API
 * 检查消息是否重复的API端点
 */

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// 使用统一的数据库连接
require_once dirname(__DIR__) . '/includes/database-connection.php';

try {
    // 获取POST数据
    $input = json_decode(file_get_contents('php://input'), true);
    
    // 如果不是JSON，尝试从$_POST获取
    if (!$input) {
        $input = $_POST;
    }
    
    // 验证必填字段
    if (empty($input['message'])) {
        echo json_encode([
            'success' => false, 
            'message' => 'Message is required'
        ]);
        exit;
    }
    
    $message = trim($input['message']);
    
    // 如果消息太短，不需要检查重复
    if (strlen($message) < 10) {
        echo json_encode([
            'success' => true, 
            'is_duplicate' => false,
            'message' => 'Message too short to check for duplicates'
        ]);
        exit;
    }
    
    // 检查完全相同的消息
    $stmt = $pdo->prepare("
        SELECT COUNT(*) as count, MAX(created_at) as last_submitted, name
        FROM pt_contact_message
        WHERE message = ?
    ");
    $stmt->execute([$message]);
    $exact_duplicate = $stmt->fetch();
    
    if ($exact_duplicate['count'] > 0) {
        $last_submitted = new DateTime($exact_duplicate['last_submitted']);
        $formatted_date = $last_submitted->format('M j, Y g:i A');
        
        echo json_encode([
            'success' => true,
            'is_duplicate' => true,
            'duplicate_type' => 'exact',
            'message' => 'This exact message was already submitted on ' . $formatted_date,
            'last_submitted' => $formatted_date
        ]);
        exit;
    }
    
    // 检查相似的消息（过去24小时内）
    $stmt = $pdo->prepare("
        SELECT message, created_at, name
        FROM pt_contact_message
        WHERE created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)
        ORDER BY created_at DESC
        LIMIT 50
    ");
    $stmt->execute();
    $recent_messages = $stmt->fetchAll();
    
    $highest_similarity = 0;
    $similar_message_date = null;
    
    foreach ($recent_messages as $recent) {
        // 计算消息相似度
        similar_text(strtolower($message), strtolower($recent['message']), $percent);
        
        if ($percent > $highest_similarity) {
            $highest_similarity = $percent;
            $similar_message_date = $recent['created_at'];
        }
        
        // 如果相似度超过85%，认为是重复内容
        if ($percent > 85) {
            $last_submitted = new DateTime($recent['created_at']);
            $formatted_date = $last_submitted->format('M j, Y g:i A');
            
            echo json_encode([
                'success' => true,
                'is_duplicate' => true,
                'duplicate_type' => 'similar',
                'similarity_percent' => round($percent, 1),
                'message' => 'A very similar message (' . round($percent, 1) . '% similar) was submitted on ' . $formatted_date,
                'last_submitted' => $formatted_date
            ]);
            exit;
        }
    }
    
    // 没有发现重复
    echo json_encode([
        'success' => true,
        'is_duplicate' => false,
        'highest_similarity' => round($highest_similarity, 1),
        'message' => 'No duplicate messages found'
    ]);
    
} catch (Exception $e) {
    error_log("Check duplicate error: " . $e->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'An error occurred while checking for duplicates'
    ]);
}
?>
