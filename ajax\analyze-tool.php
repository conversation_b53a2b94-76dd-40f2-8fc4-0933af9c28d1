<?php
/**
 * 工具网站分析API
 * 抓取网站内容并使用AI分析生成工具请求信息
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit;
}

// 获取请求数据
$input = json_decode(file_get_contents('php://input'), true);
$url = $input['url'] ?? '';

if (empty($url)) {
    echo json_encode(['success' => false, 'message' => 'URL is required']);
    exit;
}

// 验证URL格式
if (!filter_var($url, FILTER_VALIDATE_URL)) {
    echo json_encode(['success' => false, 'message' => 'Invalid URL format']);
    exit;
}

// 检查用户登录状态
session_start();
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'Please login first']);
    exit;
}

// 使用统一的数据库连接
require_once __DIR__ . '/includes/ajax-database.php';
require_once dirname(__DIR__) . '/classes/QuotaManager.php';

try {
    // 检查配额是否足够（需要10配额进行分析）
    $quotaManager = new QuotaManager($pdo);
    $userId = $_SESSION['user_id'];

    // 创建一个临时的配额规则用于检查10配额
    $requiredQuota = 10;
    $userQuota = $quotaManager->getUserQuota($userId);

    if ($userQuota['api_used'] + $requiredQuota > $userQuota['api_quota']) {
        echo json_encode(['success' => false, 'message' => 'Insufficient quota! Tool analysis requires 10 quota. Please upgrade your subscription plan.']);
        exit;
    }

    // 1. 抓取网站内容
    $websiteContent = crawlWebsite($url);
    if (!$websiteContent) {
        echo json_encode(['success' => false, 'message' => 'Failed to crawl website content']);
        exit;
    }

    // 2. 提取主要内容
    $mainContent = extractMainContent($websiteContent);
    if (strlen($mainContent) < 50) {
        echo json_encode(['success' => false, 'message' => 'Insufficient content extracted from website']);
        exit;
    }

    // 3. 获取AI模型配置
    $modelConfig = getGLMConfig($pdo);
    if (!$modelConfig) {
        echo json_encode(['success' => false, 'message' => 'AI analysis service not available']);
        exit;
    }

    // 4. 获取分类列表
    $categories = getCategories($pdo);

    // 5. 调用AI API分析（支持重试机制）
    $analysisResult = analyzeWithGLMRetry($mainContent, $url, $pdo, $categories);
    if (!$analysisResult) {
        echo json_encode(['success' => false, 'message' => 'Failed to analyze content with AI after trying all available keys']);
        exit;
    }

    // AI分析成功，扣除10配额
    try {
        // 记录配额使用
        $stmt = $pdo->prepare("
            INSERT INTO pt_api_usage (user_id, action_type, quota_consumed, description, created_at)
            VALUES (?, 'tool_analysis', ?, 'AI Tool Website Analysis', NOW())
        ");
        $stmt->execute([$userId, $requiredQuota]);
    } catch (Exception $e) {
        error_log("Failed to record quota usage for user $userId: " . $e->getMessage());
    }

    // 5. 返回结果
    echo json_encode([
        'success' => true,
        'data' => $analysisResult
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Server error: ' . $e->getMessage()
    ]);
}

/**
 * 抓取网站内容
 */
function crawlWebsite($url) {
    $userAgent = 'Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)';
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_USERAGENT, $userAgent);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_MAXREDIRS, 5);
    
    $html = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($httpCode !== 200 || !$html) {
        return false;
    }
    
    return $html;
}

/**
 * 提取网页主要内容
 */
function extractMainContent($html) {
    // 创建DOM文档
    $dom = new DOMDocument();
    libxml_use_internal_errors(true);
    $dom->loadHTML('<?xml encoding="UTF-8">' . $html);
    libxml_clear_errors();
    
    // 移除无用元素
    $removeElements = ['script', 'style', 'nav', 'header', 'footer', 'aside', 'iframe', 'noscript'];
    foreach ($removeElements as $tag) {
        $elements = $dom->getElementsByTagName($tag);
        for ($i = $elements->length - 1; $i >= 0; $i--) {
            $element = $elements->item($i);
            if ($element && $element->parentNode) {
                $element->parentNode->removeChild($element);
            }
        }
    }
    
    // 提取标题
    $title = '';
    $titleElements = $dom->getElementsByTagName('title');
    if ($titleElements->length > 0) {
        $title = trim($titleElements->item(0)->textContent);
    }
    
    // 提取meta描述
    $description = '';
    $metaElements = $dom->getElementsByTagName('meta');
    foreach ($metaElements as $meta) {
        if ($meta->getAttribute('name') === 'description') {
            $description = trim($meta->getAttribute('content'));
            break;
        }
    }
    
    // 提取主要内容区域
    $contentSelectors = ['main', 'article', '[role="main"]', '.content', '#content', '.main'];
    $mainText = '';
    
    foreach ($contentSelectors as $selector) {
        if (strpos($selector, '.') === 0) {
            // 类选择器
            $className = substr($selector, 1);
            $xpath = new DOMXPath($dom);
            $elements = $xpath->query("//*[contains(@class, '$className')]");
        } elseif (strpos($selector, '#') === 0) {
            // ID选择器
            $id = substr($selector, 1);
            $elements = $dom->getElementById($id);
            $elements = $elements ? [$elements] : [];
        } else {
            // 标签选择器
            $elements = $dom->getElementsByTagName($selector);
        }
        
        if (!empty($elements)) {
            foreach ($elements as $element) {
                $text = trim($element->textContent);
                if (strlen($text) > strlen($mainText)) {
                    $mainText = $text;
                }
            }
            if (!empty($mainText)) break;
        }
    }
    
    // 如果没有找到主要内容，提取body内容
    if (empty($mainText)) {
        $bodyElements = $dom->getElementsByTagName('body');
        if ($bodyElements->length > 0) {
            $mainText = trim($bodyElements->item(0)->textContent);
        }
    }
    
    // 清理和压缩文本
    $mainText = preg_replace('/\s+/', ' ', $mainText);
    $mainText = trim($mainText);
    
    // 限制长度以适应模型token限制
    if (strlen($mainText) > 3000) {
        $mainText = substr($mainText, 0, 3000) . '...';
    }
    
    // 组合最终内容
    $finalContent = '';
    if ($title) $finalContent .= "Title: $title\n\n";
    if ($description) $finalContent .= "Description: $description\n\n";
    $finalContent .= "Content: $mainText";
    
    return $finalContent;
}

/**
 * 获取分类列表
 */
function getCategories($pdo) {
    $stmt = $pdo->prepare("SELECT slug, name FROM pt_tool_category WHERE status = 'active' ORDER BY sort_order ASC, name ASC");
    $stmt->execute();
    $dbCategories = $stmt->fetchAll();

    $categories = [];
    foreach ($dbCategories as $cat) {
        $categories[] = $cat['slug'];
    }

    // 添加其他分类
    $categories[] = 'other';

    return $categories;
}

/**
 * 获取AI模型配置（支持随机API Key选择和负载均衡）
 */
function getGLMConfig($pdo) {
    // 获取bigModel平台信息
    $platformStmt = $pdo->prepare("SELECT * FROM pt_service_platform WHERE code = 'bigmodel' AND is_active = 1");
    $platformStmt->execute();
    $platform = $platformStmt->fetch();

    if (!$platform) {
        return false;
    }

    // 获取免费AI模型信息
    $modelStmt = $pdo->prepare("SELECT * FROM pt_service_model WHERE platform_id = ? AND model_code = 'glm-4.5-flash' AND is_active = 1");
    $modelStmt->execute([$platform['id']]);
    $model = $modelStmt->fetch();

    if (!$model) {
        return false;
    }

    // 获取所有活跃的API密钥（用于负载均衡）
    $keyStmt = $pdo->prepare("SELECT * FROM pt_service_key WHERE platform_id = ? AND is_active = 1 ORDER BY usage_count ASC, RAND()");
    $keyStmt->execute([$platform['id']]);
    $apiKeys = $keyStmt->fetchAll();

    if (empty($apiKeys)) {
        return false;
    }

    // 选择使用次数最少的Key，如果有多个相同使用次数的Key则随机选择
    $selectedKey = $apiKeys[0];

    // 更新选中Key的使用次数和最后使用时间
    try {
        $updateStmt = $pdo->prepare("UPDATE pt_service_key SET usage_count = usage_count + 1, last_used_at = NOW() WHERE id = ?");
        $updateStmt->execute([$selectedKey['id']]);
    } catch (Exception $e) {
        // 如果更新失败，不影响主流程
    }

    return [
        'platform' => $platform,
        'model' => $model,
        'api_key' => $selectedKey['api_key'],
        'key_id' => $selectedKey['id'],
        'key_name' => $selectedKey['name'],
        'base_url' => $platform['base_url'],
        'total_keys' => count($apiKeys)
    ];
}

/**
 * 使用AI分析内容
 */
function analyzeWithGLM($content, $url, $config, $categories) {
    $categoryList = implode(', ', $categories);

    $prompt = "As a professional product analyst, your task is to analyze website functionality and generate tool development requirements.

\"\"\"
Website URL: {$url}
Website Content: {$content}
\"\"\"

Analysis Steps:
1. Identify core functionality and value proposition
2. Understand user pain points being addressed
3. Generate development request for similar tool

Requirements:
- Title: Describe what tool needs to be DEVELOPED (action-oriented, professional)
- Slug: Generate SEO-friendly URL identifier (3-60 chars, lowercase, hyphens only, focus on core tool keywords)
- Category: Must be exactly one from the provided list
- Description: Explain required features, target users, and business value
- Focus on functionality and user needs, not existing tool's branding
- Use clear, professional language that developers would understand

Slug Generation Rules:
- Use only lowercase letters, numbers, and hyphens
- Focus on core tool functionality keywords (e.g., \"password-generator\", \"pdf-converter\", \"qr-scanner\")
- Avoid generic words like \"tool\", \"app\", \"platform\", \"system\"
- Keep it concise but descriptive (3-60 characters)
- Examples: \"email-validator\", \"color-palette-generator\", \"markdown-editor\"

Available Categories: {$categoryList}

Return ONLY valid JSON format (no other text):
{
    \"title\": \"Professional development request title describing what tool should be built (STRICT LIMIT: max 200 characters)\",
    \"slug\": \"seo-friendly-url-identifier (3-60 chars, lowercase, hyphens, core keywords only)\",
    \"category\": \"Select exactly one from the category list\",
    \"description\": \"Comprehensive description of required features, target audience, key functionality, and why this tool would be valuable (STRICT LIMIT: max 1000 characters)\"
}";

    $data = [
        'model' => 'glm-4.5-flash',
        'messages' => [
            [
                'role' => 'system',
                'content' => 'You are a professional product analyst specializing in web tool analysis and development requirement generation. You excel at extracting key information from website content, understanding user needs, and creating precise development specifications. Always respond with structured JSON output as requested.'
            ],
            [
                'role' => 'user',
                'content' => $prompt
            ]
        ],
        'thinking' => [
            'type' => 'disabled'  // 关闭思考模式，提高响应速度
        ],
        'max_tokens' => 1000,
        'temperature' => 0.7
    ];

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $config['base_url'] . '/chat/completions');
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Authorization: Bearer ' . $config['api_key']
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curlError = curl_error($ch);
    curl_close($ch);

    // 详细的错误日志记录
    if ($httpCode !== 200 || !$response) {
        error_log("API call failed - HTTP Code: $httpCode, cURL Error: $curlError, Response: " . substr($response, 0, 200));
        return false;
    }

    $result = json_decode($response, true);
    if (!$result) {
        error_log("Failed to parse JSON response: " . substr($response, 0, 200));
        return false;
    }

    // 检查API错误响应
    if (isset($result['error'])) {
        error_log("API returned error: " . json_encode($result['error']));
        return false;
    }

    if (!isset($result['choices'][0]['message']['content'])) {
        error_log("Unexpected API response structure: " . json_encode($result));
        return false;
    }

    // 直接获取响应内容，提高处理效率
    $content = trim($result['choices'][0]['message']['content']);
    
    // 尝试解析JSON响应
    $analysisData = json_decode($content, true);
    if (!$analysisData) {
        // 如果直接解析失败，尝试提取JSON部分
        if (preg_match('/\{.*\}/s', $content, $matches)) {
            $analysisData = json_decode($matches[0], true);
        }
    }
    
    if (!$analysisData || !isset($analysisData['title']) || !isset($analysisData['category']) || !isset($analysisData['description'])) {
        return false;
    }
    
    // 验证和清理数据
    $analysisData['title'] = substr(trim($analysisData['title']), 0, 200);
    $analysisData['description'] = substr(trim($analysisData['description']), 0, 1000);

    // 验证和清理slug
    $analysisData['slug'] = trim($analysisData['slug'] ?? '');
    $analysisData['slug'] = strtolower($analysisData['slug']);
    $analysisData['slug'] = preg_replace('/[^a-z0-9\-]/', '', $analysisData['slug']);
    $analysisData['slug'] = substr($analysisData['slug'], 0, 60);

    // 如果AI没有生成有效的slug，则使用SlugGenerator作为后备
    if (empty($analysisData['slug']) || strlen($analysisData['slug']) < 3) {
        require_once dirname(__DIR__) . '/classes/SlugGenerator.php';
        $analysisData['slug'] = SlugGenerator::generateFromTitle($analysisData['title']);
    }

    // 验证分类（使用传入的分类列表）
    if (!in_array($analysisData['category'], $categories)) {
        $analysisData['category'] = 'other';
    }

    return $analysisData;
}

/**
 * 带重试机制的AI分析函数
 * 如果当前Key失效，会自动尝试其他可用的Key
 */
function analyzeWithGLMRetry($content, $url, $pdo, $categories, $maxRetries = 3) {
    $attemptCount = 0;
    $usedKeyIds = [];

    while ($attemptCount < $maxRetries) {
        $attemptCount++;

        // 获取配置（排除已经尝试过的Key）
        $modelConfig = getGLMConfigExcluding($pdo, $usedKeyIds);
        if (!$modelConfig) {
            error_log("No more API keys available for retry attempt $attemptCount");
            break;
        }

        // 记录使用的Key ID
        $usedKeyIds[] = $modelConfig['key_id'];

        // 尝试分析
        $result = analyzeWithGLM($content, $url, $modelConfig, $categories);

        if ($result) {
            // 成功，记录日志并返回结果
            error_log("AI analysis successful on attempt $attemptCount using key: " . $modelConfig['key_name']);
            return $result;
        }

        // 失败，记录日志并继续重试
        error_log("AI analysis failed on attempt $attemptCount using key: " . $modelConfig['key_name']);

        // 如果是API Key相关错误，标记该Key为有问题（可选）
        // 这里可以添加更详细的错误分析逻辑
    }

    error_log("AI analysis failed after $attemptCount attempts with " . count($usedKeyIds) . " different keys");
    return false;
}

/**
 * 获取AI模型配置（排除指定的Key ID）
 */
function getGLMConfigExcluding($pdo, $excludeKeyIds = []) {
    // 获取bigModel平台信息
    $platformStmt = $pdo->prepare("SELECT * FROM pt_service_platform WHERE code = 'bigmodel' AND is_active = 1");
    $platformStmt->execute();
    $platform = $platformStmt->fetch();

    if (!$platform) {
        return false;
    }

    // 获取免费AI模型信息
    $modelStmt = $pdo->prepare("SELECT * FROM pt_service_model WHERE platform_id = ? AND model_code = 'glm-4.5-flash' AND is_active = 1");
    $modelStmt->execute([$platform['id']]);
    $model = $modelStmt->fetch();

    if (!$model) {
        return false;
    }

    // 构建排除条件
    $excludeCondition = '';
    $params = [$platform['id']];

    if (!empty($excludeKeyIds)) {
        $placeholders = str_repeat('?,', count($excludeKeyIds) - 1) . '?';
        $excludeCondition = " AND id NOT IN ($placeholders)";
        $params = array_merge($params, $excludeKeyIds);
    }

    // 获取可用的API密钥（排除已使用的）
    $sql = "SELECT * FROM pt_service_key WHERE platform_id = ? AND is_active = 1" . $excludeCondition . " ORDER BY usage_count ASC, RAND() LIMIT 1";
    $keyStmt = $pdo->prepare($sql);
    $keyStmt->execute($params);
    $selectedKey = $keyStmt->fetch();

    if (!$selectedKey) {
        return false;
    }

    // 更新选中Key的使用次数和最后使用时间
    try {
        $updateStmt = $pdo->prepare("UPDATE pt_service_key SET usage_count = usage_count + 1, last_used_at = NOW() WHERE id = ?");
        $updateStmt->execute([$selectedKey['id']]);
    } catch (Exception $e) {
        error_log("Failed to update API key usage: " . $e->getMessage());
    }

    return [
        'platform' => $platform,
        'model' => $model,
        'api_key' => $selectedKey['api_key'],
        'key_id' => $selectedKey['id'],
        'key_name' => $selectedKey['name'],
        'base_url' => $platform['base_url']
    ];
}
?>
