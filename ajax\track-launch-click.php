<?php
/**
 * 产品启动点击跟踪AJAX处理
 */

header('Content-Type: application/json');

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit;
}

// 使用统一的数据库连接
require_once __DIR__ . '/includes/ajax-database.php';

try {

    $launchId = intval($_POST['launch_id'] ?? 0);

    if ($launchId <= 0) {
        echo json_encode(['success' => false, 'message' => 'Invalid launch ID']);
        exit;
    }

    // 检查产品是否存在
    $launchStmt = $pdo->prepare("SELECT id FROM pt_product_launches WHERE id = ?");
    $launchStmt->execute([$launchId]);
    $launch = $launchStmt->fetch();

    if (!$launch) {
        echo json_encode(['success' => false, 'message' => 'Launch not found']);
        exit;
    }

    // 更新点击数
    $updateStmt = $pdo->prepare("UPDATE pt_product_launches SET clicks = clicks + 1 WHERE id = ?");
    $updateStmt->execute([$launchId]);

    echo json_encode(['success' => true, 'message' => 'Click tracked']);

} catch (Exception $e) {
    error_log("Track launch click error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Tracking failed']);
}
?>
