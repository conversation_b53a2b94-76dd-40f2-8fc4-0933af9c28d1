<?php
/**
 * Pricing Page
 */

$currentPage = 'pricing';
$breadcrumbs = [
    ['name' => 'Home', 'url' => '/'],
    ['name' => 'Pricing']
];

// 包含动态SEO助手
require_once ROOT_PATH . '/app/helpers/dynamic-seo.php';

// 获取动态SEO数据
$seoData = getDynamicSEOData('pricing');

// 包含公共头部
include_once ROOT_PATH . '/templates/layout/header.php';
?>

<!-- Pricing页面主体 -->
<div class="min-h-screen bg-gray-900 text-white">
    <!-- Hero Section -->
    <div class="bg-black">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
            <div class="text-center">
                <h1 class="text-4xl md:text-6xl font-bold text-white mb-6">
                    Simple, Transparent Pricing
                </h1>
                <p class="text-xl md:text-2xl text-gray-400 max-w-4xl mx-auto leading-relaxed">
                    Choose the perfect plan for your needs. All plans include access to our AI-powered tools.
                </p>
            </div>
        </div>
    </div>

    <!-- Pricing Cards Section -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            
            <!-- Free Plan -->
            <div class="bg-gray-800 border border-gray-700 p-8 relative">
                <div class="text-center">
                    <h3 class="text-2xl font-bold text-white mb-4">Free</h3>
                    <div class="mb-6">
                        <span class="text-4xl font-bold text-white">$0</span>
                        <span class="text-gray-400">/month</span>
                    </div>
                    <div class="text-center mb-6">
                        <span class="text-3xl font-bold text-accent">1,000</span>
                        <p class="text-gray-400">API Credits</p>
                    </div>
                </div>
                
                <ul class="space-y-4 mb-8">
                    <li class="flex items-center text-gray-300">
                        <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        Access to all tools
                    </li>
                    <li class="flex items-center text-gray-300">
                        <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        Basic AI features
                    </li>
                    <li class="flex items-center text-gray-300">
                        <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        Community support
                    </li>
                    <li class="flex items-center text-gray-300">
                        <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        Monthly reset
                    </li>
                </ul>
                
                <button class="w-full px-6 py-3 text-lg font-medium bg-gray-700 text-white hover:bg-gray-600 transition-all duration-200">
                    Get Started
                </button>
            </div>

            <!-- Basic Plan -->
            <div class="bg-gray-800 border border-gray-700 p-8 relative">
                <div class="text-center">
                    <h3 class="text-2xl font-bold text-white mb-4">Basic</h3>
                    <div class="mb-6">
                        <span class="text-4xl font-bold text-white">$9</span>
                        <span class="text-gray-400">/month</span>
                    </div>
                    <div class="text-center mb-6">
                        <span class="text-3xl font-bold text-accent">2,000</span>
                        <p class="text-gray-400">API Credits</p>
                    </div>
                </div>
                
                <ul class="space-y-4 mb-8">
                    <li class="flex items-center text-gray-300">
                        <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        Everything in Free
                    </li>
                    <li class="flex items-center text-gray-300">
                        <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        Priority processing
                    </li>
                    <li class="flex items-center text-gray-300">
                        <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        Email support
                    </li>
                    <li class="flex items-center text-gray-300">
                        <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        Advanced features
                    </li>
                </ul>
                
                <button class="w-full px-6 py-3 text-lg font-medium bg-accent text-white hover:bg-blue-700 transition-all duration-200">
                    Choose Basic
                </button>
            </div>

            <!-- Premium Plan -->
            <div class="bg-gray-800 border-2 border-accent p-8 relative">
                <!-- Popular Badge -->
                <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <span class="bg-accent text-white px-4 py-1 text-sm font-medium">Most Popular</span>
                </div>
                
                <div class="text-center">
                    <h3 class="text-2xl font-bold text-white mb-4">Premium</h3>
                    <div class="mb-6">
                        <span class="text-4xl font-bold text-white">$29</span>
                        <span class="text-gray-400">/month</span>
                    </div>
                    <div class="text-center mb-6">
                        <span class="text-3xl font-bold text-accent">5,000</span>
                        <p class="text-gray-400">API Credits</p>
                    </div>
                </div>
                
                <ul class="space-y-4 mb-8">
                    <li class="flex items-center text-gray-300">
                        <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        Everything in Basic
                    </li>
                    <li class="flex items-center text-gray-300">
                        <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        Premium AI models
                    </li>
                    <li class="flex items-center text-gray-300">
                        <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        Priority support
                    </li>
                    <li class="flex items-center text-gray-300">
                        <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        API access
                    </li>
                </ul>
                
                <button class="w-full px-6 py-3 text-lg font-medium bg-accent text-white hover:bg-blue-700 transition-all duration-200">
                    Choose Premium
                </button>
            </div>

            <!-- Enterprise Plan -->
            <div class="bg-gray-800 border border-gray-700 p-8 relative">
                <div class="text-center">
                    <h3 class="text-2xl font-bold text-white mb-4">Enterprise</h3>
                    <div class="mb-6">
                        <span class="text-4xl font-bold text-white">Custom</span>
                    </div>
                    <div class="text-center mb-6">
                        <span class="text-3xl font-bold text-accent">Unlimited</span>
                        <p class="text-gray-400">API Credits</p>
                    </div>
                </div>
                
                <ul class="space-y-4 mb-8">
                    <li class="flex items-center text-gray-300">
                        <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        Everything in Premium
                    </li>
                    <li class="flex items-center text-gray-300">
                        <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        Custom integrations
                    </li>
                    <li class="flex items-center text-gray-300">
                        <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        Dedicated support
                    </li>
                    <li class="flex items-center text-gray-300">
                        <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        SLA guarantee
                    </li>
                </ul>
                
                <button class="w-full px-6 py-3 text-lg font-medium bg-accent text-white hover:bg-blue-700 transition-all duration-200">
                    Contact Sales
                </button>
            </div>
        </div>
    </div>

    <!-- FAQ Section -->
    <div class="bg-gray-800">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
            <div class="text-center mb-16">
                <h2 class="text-3xl font-bold text-white mb-4">Frequently Asked Questions</h2>
                <p class="text-xl text-gray-400">Everything you need to know about our pricing</p>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div class="space-y-8">
                    <div>
                        <h3 class="text-xl font-semibold text-white mb-3">What are API Credits?</h3>
                        <p class="text-gray-400">API Credits are used when you interact with our AI-powered tools. Different actions consume different amounts of credits based on complexity.</p>
                    </div>

                    <div>
                        <h3 class="text-xl font-semibold text-white mb-3">Do credits roll over?</h3>
                        <p class="text-gray-400">No, credits reset monthly on your billing date. We recommend choosing a plan that fits your monthly usage.</p>
                    </div>

                    <div>
                        <h3 class="text-xl font-semibold text-white mb-3">Can I change plans anytime?</h3>
                        <p class="text-gray-400">Yes, you can upgrade or downgrade your plan at any time. Changes take effect immediately.</p>
                    </div>
                </div>

                <div class="space-y-8">
                    <div>
                        <h3 class="text-xl font-semibold text-white mb-3">What happens if I exceed my credits?</h3>
                        <p class="text-gray-400">You'll be notified when you're close to your limit. You can upgrade your plan or wait for the monthly reset.</p>
                    </div>

                    <div>
                        <h3 class="text-xl font-semibold text-white mb-3">Is there a free trial?</h3>
                        <p class="text-gray-400">Yes! Every new user gets 1,000 free credits to try our platform. No credit card required.</p>
                    </div>

                    <div>
                        <h3 class="text-xl font-semibold text-white mb-3">How does Enterprise pricing work?</h3>
                        <p class="text-gray-400">Enterprise plans are customized based on your specific needs, usage volume, and required features. Contact our sales team for a quote.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- CTA Section -->
    <div class="bg-black">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
            <div class="text-center">
                <h2 class="text-3xl font-bold text-white mb-4">Ready to Get Started?</h2>
                <p class="text-xl text-gray-400 mb-8 max-w-2xl mx-auto">
                    Join thousands of users who trust Prompt2Tool for their AI-powered productivity needs.
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="/register" class="px-8 py-4 text-lg font-medium bg-accent text-white hover:bg-blue-700 hover:text-white transition-all duration-200 transform hover:scale-105">
                        Start Free Trial
                    </a>
                    <a href="/contact" class="px-8 py-4 text-lg font-medium border-2 border-gray-600 text-white hover:bg-gray-800 hover:border-accent transition-all duration-200">
                        Contact Sales
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Features Comparison Section -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <div class="text-center mb-16">
            <h2 class="text-3xl font-bold text-white mb-4">Feature Comparison</h2>
            <p class="text-xl text-gray-400">See what's included in each plan</p>
        </div>

        <div class="overflow-x-auto">
            <table class="w-full bg-gray-800 border border-gray-700">
                <thead>
                    <tr class="border-b border-gray-700">
                        <th class="text-left p-6 text-white font-semibold">Features</th>
                        <th class="text-center p-6 text-white font-semibold">Free</th>
                        <th class="text-center p-6 text-white font-semibold">Basic</th>
                        <th class="text-center p-6 text-white font-semibold bg-gray-700">Premium</th>
                        <th class="text-center p-6 text-white font-semibold">Enterprise</th>
                    </tr>
                </thead>
                <tbody>
                    <tr class="border-b border-gray-700">
                        <td class="p-6 text-gray-300">Monthly API Credits</td>
                        <td class="p-6 text-center text-gray-300">1,000</td>
                        <td class="p-6 text-center text-gray-300">2,000</td>
                        <td class="p-6 text-center text-accent bg-gray-700 font-semibold">5,000</td>
                        <td class="p-6 text-center text-gray-300">Unlimited</td>
                    </tr>
                    <tr class="border-b border-gray-700">
                        <td class="p-6 text-gray-300">All AI Tools Access</td>
                        <td class="p-6 text-center">
                            <svg class="w-5 h-5 text-green-500 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        </td>
                        <td class="p-6 text-center">
                            <svg class="w-5 h-5 text-green-500 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        </td>
                        <td class="p-6 text-center bg-gray-700">
                            <svg class="w-5 h-5 text-green-500 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        </td>
                        <td class="p-6 text-center">
                            <svg class="w-5 h-5 text-green-500 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        </td>
                    </tr>
                    <tr class="border-b border-gray-700">
                        <td class="p-6 text-gray-300">Priority Processing</td>
                        <td class="p-6 text-center">
                            <svg class="w-5 h-5 text-red-500 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                            </svg>
                        </td>
                        <td class="p-6 text-center">
                            <svg class="w-5 h-5 text-green-500 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        </td>
                        <td class="p-6 text-center bg-gray-700">
                            <svg class="w-5 h-5 text-green-500 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        </td>
                        <td class="p-6 text-center">
                            <svg class="w-5 h-5 text-green-500 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        </td>
                    </tr>
                    <tr class="border-b border-gray-700">
                        <td class="p-6 text-gray-300">Premium AI Models</td>
                        <td class="p-6 text-center">
                            <svg class="w-5 h-5 text-red-500 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                            </svg>
                        </td>
                        <td class="p-6 text-center">
                            <svg class="w-5 h-5 text-red-500 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                            </svg>
                        </td>
                        <td class="p-6 text-center bg-gray-700">
                            <svg class="w-5 h-5 text-green-500 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        </td>
                        <td class="p-6 text-center">
                            <svg class="w-5 h-5 text-green-500 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        </td>
                    </tr>
                    <tr class="border-b border-gray-700">
                        <td class="p-6 text-gray-300">API Access</td>
                        <td class="p-6 text-center">
                            <svg class="w-5 h-5 text-red-500 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                            </svg>
                        </td>
                        <td class="p-6 text-center">
                            <svg class="w-5 h-5 text-red-500 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                            </svg>
                        </td>
                        <td class="p-6 text-center bg-gray-700">
                            <svg class="w-5 h-5 text-green-500 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        </td>
                        <td class="p-6 text-center">
                            <svg class="w-5 h-5 text-green-500 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                        </td>
                    </tr>
                    <tr>
                        <td class="p-6 text-gray-300">Support Level</td>
                        <td class="p-6 text-center text-gray-400">Community</td>
                        <td class="p-6 text-center text-gray-400">Email</td>
                        <td class="p-6 text-center text-accent bg-gray-700 font-semibold">Priority</td>
                        <td class="p-6 text-center text-gray-400">Dedicated</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- 自定义提示窗口 -->
<div id="customAlert" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center">
    <div class="bg-gray-800 border border-gray-700 p-8 max-w-md w-full mx-4">
        <div class="text-center">
            <div id="alertIcon" class="mb-4"></div>
            <h3 id="alertTitle" class="text-xl font-bold text-white mb-4"></h3>
            <p id="alertMessage" class="text-gray-400 mb-6"></p>
            <div class="flex gap-4 justify-center">
                <button id="alertConfirm" class="px-6 py-2 bg-accent text-white hover:bg-blue-700 transition-all duration-200">
                    Confirm
                </button>
                <button id="alertCancel" class="px-6 py-2 bg-gray-600 text-white hover:bg-gray-500 transition-all duration-200 hidden">
                    Cancel
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// 自定义提示窗口功能
function showCustomAlert(title, message, type = 'info', showCancel = false) {
    const alertModal = document.getElementById('customAlert');
    const alertTitle = document.getElementById('alertTitle');
    const alertMessage = document.getElementById('alertMessage');
    const alertIcon = document.getElementById('alertIcon');
    const alertConfirm = document.getElementById('alertConfirm');
    const alertCancel = document.getElementById('alertCancel');

    // 设置内容
    alertTitle.textContent = title;
    alertMessage.textContent = message;

    // 设置图标
    let iconHTML = '';
    switch(type) {
        case 'success':
            iconHTML = '<svg class="w-12 h-12 text-green-500 mx-auto" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg>';
            break;
        case 'error':
            iconHTML = '<svg class="w-12 h-12 text-red-500 mx-auto" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>';
            break;
        case 'warning':
            iconHTML = '<svg class="w-12 h-12 text-yellow-500 mx-auto" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg>';
            break;
        default:
            iconHTML = '<svg class="w-12 h-12 text-blue-500 mx-auto" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path></svg>';
    }
    alertIcon.innerHTML = iconHTML;

    // 显示/隐藏取消按钮
    if (showCancel) {
        alertCancel.classList.remove('hidden');
    } else {
        alertCancel.classList.add('hidden');
    }

    // 显示模态框
    alertModal.classList.remove('hidden');

    return new Promise((resolve) => {
        alertConfirm.onclick = () => {
            alertModal.classList.add('hidden');
            resolve(true);
        };

        alertCancel.onclick = () => {
            alertModal.classList.add('hidden');
            resolve(false);
        };

        // 点击背景关闭
        alertModal.onclick = (e) => {
            if (e.target === alertModal) {
                alertModal.classList.add('hidden');
                resolve(false);
            }
        };
    });
}

// 价格计划按钮点击事件
document.addEventListener('DOMContentLoaded', function() {
    // 获取所有计划按钮
    const planButtons = document.querySelectorAll('button[class*="bg-accent"], button[class*="bg-gray-700"]');

    planButtons.forEach(button => {
        button.addEventListener('click', function() {
            const planName = this.closest('.bg-gray-800').querySelector('h3').textContent;

            if (planName === 'Enterprise') {
                showCustomAlert(
                    'Contact Sales Team',
                    'Enterprise plans require custom configuration. Our sales team will provide you with professional solutions.',
                    'info'
                );
            } else if (planName === 'Free') {
                showCustomAlert(
                    'Start Free Trial',
                    'Register an account to get 1000 free API credits and start experiencing our AI tools immediately!',
                    'success'
                );
            } else {
                showCustomAlert(
                    'Feature in Development',
                    `${planName} plan paid features are currently in development. Stay tuned! You can use all tools for free now.`,
                    'info'
                );
            }
        });
    });

    // CTA按钮事件
    const ctaButtons = document.querySelectorAll('a[href="/register"], a[href="/contact"]');
    ctaButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const isRegister = this.getAttribute('href') === '/register';

            if (isRegister) {
                showCustomAlert(
                    'Registration Feature',
                    'User registration feature is being improved. Stay tuned!',
                    'info'
                );
            } else {
                showCustomAlert(
                    'Contact Us',
                    'For more information, please send an email to: <EMAIL>',
                    'info'
                );
            }
        });
    });
});
</script>

<?php
// 包含公共底部
include_once ROOT_PATH . '/templates/layout/footer.php';
?>
