<?php
/**
 * 权限中间件
 * 在请求处理前检查用户权限
 */

// 防止直接访问
if (!defined('ROOT_PATH')) {
    exit('Direct access not allowed');
}

// 加载依赖
require_once ROOT_PATH . '/control-panel/classes/PermissionManager.php';
require_once ROOT_PATH . '/control-panel/auth/middleware.php';

class PermissionMiddleware {
    
    private $permissionManager;
    private $config;
    
    /**
     * 构造函数
     */
    public function __construct() {
        $this->permissionManager = new PermissionManager();
        $this->config = include ROOT_PATH . '/control-panel/config/permissions.php';
    }
    
    /**
     * 检查权限
     */
    public function checkPermission($requiredPermission, $options = []) {
        // 获取当前用户
        $currentUser = getCurrentAdmin();
        
        if (!$currentUser) {
            return $this->handleUnauthorized('User not authenticated');
        }
        
        // 检查用户状态
        if (!$this->isUserActive($currentUser)) {
            return $this->handleForbidden('User account is inactive');
        }
        
        // 检查权限
        $hasPermission = $this->permissionManager->hasPermission($currentUser, $requiredPermission);
        
        if (!$hasPermission) {
            // 记录权限拒绝日志
            $this->logPermissionDenied($currentUser, $requiredPermission);
            
            return $this->handleForbidden('Insufficient permissions');
        }
        
        // 检查额外条件
        if (!empty($options)) {
            $extraCheckResult = $this->checkExtraConditions($currentUser, $options);
            if (!$extraCheckResult['allowed']) {
                return $this->handleForbidden($extraCheckResult['message']);
            }
        }
        
        // 记录权限检查成功
        $this->logPermissionGranted($currentUser, $requiredPermission);
        
        return true;
    }
    
    /**
     * 检查多个权限（需要全部满足）
     */
    public function checkMultiplePermissions($permissions, $options = []) {
        $currentUser = getCurrentAdmin();
        
        if (!$currentUser) {
            return $this->handleUnauthorized('User not authenticated');
        }
        
        if (!$this->isUserActive($currentUser)) {
            return $this->handleForbidden('User account is inactive');
        }
        
        $deniedPermissions = [];
        
        foreach ($permissions as $permission) {
            if (!$this->permissionManager->hasPermission($currentUser, $permission)) {
                $deniedPermissions[] = $permission;
            }
        }
        
        if (!empty($deniedPermissions)) {
            $this->logPermissionDenied($currentUser, implode(', ', $deniedPermissions));
            return $this->handleForbidden('Missing required permissions: ' . implode(', ', $deniedPermissions));
        }
        
        // 检查额外条件
        if (!empty($options)) {
            $extraCheckResult = $this->checkExtraConditions($currentUser, $options);
            if (!$extraCheckResult['allowed']) {
                return $this->handleForbidden($extraCheckResult['message']);
            }
        }
        
        return true;
    }
    
    /**
     * 检查任一权限（只需满足其中一个）
     */
    public function checkAnyPermission($permissions, $options = []) {
        $currentUser = getCurrentAdmin();
        
        if (!$currentUser) {
            return $this->handleUnauthorized('User not authenticated');
        }
        
        if (!$this->isUserActive($currentUser)) {
            return $this->handleForbidden('User account is inactive');
        }
        
        $hasAnyPermission = false;
        
        foreach ($permissions as $permission) {
            if ($this->permissionManager->hasPermission($currentUser, $permission)) {
                $hasAnyPermission = true;
                break;
            }
        }
        
        if (!$hasAnyPermission) {
            $this->logPermissionDenied($currentUser, 'Any of: ' . implode(', ', $permissions));
            return $this->handleForbidden('None of the required permissions are satisfied');
        }
        
        // 检查额外条件
        if (!empty($options)) {
            $extraCheckResult = $this->checkExtraConditions($currentUser, $options);
            if (!$extraCheckResult['allowed']) {
                return $this->handleForbidden($extraCheckResult['message']);
            }
        }
        
        return true;
    }
    
    /**
     * 检查角色级别
     */
    public function checkRoleLevel($requiredLevel, $options = []) {
        $currentUser = getCurrentAdmin();
        
        if (!$currentUser) {
            return $this->handleUnauthorized('User not authenticated');
        }
        
        if (!$this->isUserActive($currentUser)) {
            return $this->handleForbidden('User account is inactive');
        }
        
        $userLevel = $this->permissionManager->getRoleLevel($currentUser['role']);
        
        if ($userLevel < $requiredLevel) {
            $this->logPermissionDenied($currentUser, "Role level {$requiredLevel} required (user has {$userLevel})");
            return $this->handleForbidden('Insufficient role level');
        }
        
        return true;
    }
    
    /**
     * 检查资源所有权
     */
    public function checkResourceOwnership($resourceType, $resourceId, $allowedRoles = ['super_admin']) {
        $currentUser = getCurrentAdmin();
        
        if (!$currentUser) {
            return $this->handleUnauthorized('User not authenticated');
        }
        
        // 特定角色可以访问所有资源
        if (in_array($currentUser['role'], $allowedRoles)) {
            return true;
        }
        
        // 检查资源所有权
        $isOwner = $this->checkResourceOwner($currentUser, $resourceType, $resourceId);
        
        if (!$isOwner) {
            $this->logPermissionDenied($currentUser, "Resource ownership check failed for {$resourceType}:{$resourceId}");
            return $this->handleForbidden('You can only access your own resources');
        }
        
        return true;
    }
    
    /**
     * 检查用户是否处于活跃状态
     */
    private function isUserActive($user) {
        // 检查用户状态
        if (isset($user['status']) && $user['status'] !== 'active') {
            return false;
        }
        
        // 检查账户是否被锁定
        if (isset($user['is_locked']) && $user['is_locked']) {
            return false;
        }
        
        // 检查账户是否过期
        if (isset($user['expires_at']) && strtotime($user['expires_at']) < time()) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 检查额外条件
     */
    private function checkExtraConditions($user, $options) {
        $result = ['allowed' => true, 'message' => ''];
        
        // 检查IP限制
        if (isset($options['allowed_ips'])) {
            $clientIP = $this->getClientIP();
            if (!in_array($clientIP, $options['allowed_ips'])) {
                $result = ['allowed' => false, 'message' => 'IP address not allowed'];
            }
        }
        
        // 检查时间限制
        if (isset($options['time_restrictions'])) {
            $currentTime = date('H:i');
            $currentDay = date('w'); // 0 = Sunday, 6 = Saturday
            
            $restrictions = $options['time_restrictions'];
            
            if (isset($restrictions['allowed_hours'])) {
                $allowedHours = $restrictions['allowed_hours'];
                $currentHour = (int)date('H');
                
                if (!in_array($currentHour, $allowedHours)) {
                    $result = ['allowed' => false, 'message' => 'Access not allowed at this time'];
                }
            }
            
            if (isset($restrictions['allowed_days'])) {
                $allowedDays = $restrictions['allowed_days'];
                
                if (!in_array($currentDay, $allowedDays)) {
                    $result = ['allowed' => false, 'message' => 'Access not allowed on this day'];
                }
            }
        }
        
        // 检查会话限制
        if (isset($options['max_concurrent_sessions'])) {
            $activeSessions = $this->getActiveSessionCount($user['id']);
            if ($activeSessions > $options['max_concurrent_sessions']) {
                $result = ['allowed' => false, 'message' => 'Maximum concurrent sessions exceeded'];
            }
        }
        
        // 检查地理位置限制
        if (isset($options['allowed_countries'])) {
            $userCountry = $this->getUserCountry();
            if (!in_array($userCountry, $options['allowed_countries'])) {
                $result = ['allowed' => false, 'message' => 'Access not allowed from this location'];
            }
        }
        
        return $result;
    }
    
    /**
     * 检查资源所有权
     */
    private function checkResourceOwner($user, $resourceType, $resourceId) {
        // 这里应该根据资源类型查询数据库
        // 示例实现
        switch ($resourceType) {
            case 'tool':
                return $this->isToolOwner($user['id'], $resourceId);
            case 'user':
                return $user['id'] == $resourceId;
            case 'log':
                return $this->isLogAccessible($user, $resourceId);
            default:
                return false;
        }
    }
    
    /**
     * 处理未认证错误
     */
    private function handleUnauthorized($message) {
        if ($this->isAjaxRequest()) {
            http_response_code(401);
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'error' => 'Unauthorized',
                'message' => $message,
                'code' => 'AUTH_REQUIRED'
            ]);
            exit;
        } else {
            header('Location: /control-panel/auth/login.php?message=' . urlencode($message));
            exit;
        }
    }
    
    /**
     * 处理权限不足错误
     */
    private function handleForbidden($message) {
        if ($this->isAjaxRequest()) {
            http_response_code(403);
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'error' => 'Forbidden',
                'message' => $message,
                'code' => 'INSUFFICIENT_PERMISSIONS'
            ]);
            exit;
        } else {
            // 重定向到权限不足页面
            header('Location: /control-panel/errors/403.php?message=' . urlencode($message));
            exit;
        }
    }
    
    /**
     * 检查是否为AJAX请求
     */
    private function isAjaxRequest() {
        return !empty($_SERVER['HTTP_X_REQUESTED_WITH']) && 
               strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
    }
    
    /**
     * 获取客户端IP地址
     */
    private function getClientIP() {
        $ipKeys = [
            'HTTP_CF_CONNECTING_IP',
            'HTTP_CLIENT_IP',
            'HTTP_X_FORWARDED_FOR',
            'HTTP_X_FORWARDED',
            'HTTP_X_CLUSTER_CLIENT_IP',
            'HTTP_FORWARDED_FOR',
            'HTTP_FORWARDED',
            'REMOTE_ADDR'
        ];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }
    
    /**
     * 记录权限被拒绝的日志
     */
    private function logPermissionDenied($user, $permission) {
        $logData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'event' => 'permission_denied',
            'user_id' => $user['id'],
            'username' => $user['username'],
            'role' => $user['role'],
            'permission' => $permission,
            'ip_address' => $this->getClientIP(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'request_uri' => $_SERVER['REQUEST_URI'] ?? '',
            'request_method' => $_SERVER['REQUEST_METHOD'] ?? ''
        ];
        
        $logFile = '/var/log/prompt2tool/permission_denied.log';
        $logDir = dirname($logFile);
        
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        $logLine = json_encode($logData, JSON_UNESCAPED_UNICODE) . "\n";
        file_put_contents($logFile, $logLine, FILE_APPEND | LOCK_EX);
    }
    
    /**
     * 记录权限授予的日志
     */
    private function logPermissionGranted($user, $permission) {
        // 只在调试模式下记录成功的权限检查
        if (!defined('DEBUG_PERMISSIONS') || !DEBUG_PERMISSIONS) {
            return;
        }
        
        $logData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'event' => 'permission_granted',
            'user_id' => $user['id'],
            'username' => $user['username'],
            'role' => $user['role'],
            'permission' => $permission,
            'ip_address' => $this->getClientIP(),
            'request_uri' => $_SERVER['REQUEST_URI'] ?? ''
        ];
        
        $logFile = '/var/log/prompt2tool/permission_granted.log';
        $logDir = dirname($logFile);
        
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        $logLine = json_encode($logData, JSON_UNESCAPED_UNICODE) . "\n";
        file_put_contents($logFile, $logLine, FILE_APPEND | LOCK_EX);
    }
    
    // 以下方法需要根据实际数据库实现
    
    /**
     * 检查是否为工具所有者
     */
    private function isToolOwner($userId, $toolId) {
        // 实现数据库查询逻辑
        return false;
    }

    /**
     * 检查日志是否可访问
     */
    private function isLogAccessible($user, $logId) {
        // 实现访问控制逻辑
        return false;
    }

    /**
     * 获取活跃会话数量
     */
    private function getActiveSessionCount($userId) {
        // 实现会话计数逻辑
        return 1;
    }

    /**
     * 获取用户所在国家
     */
    private function getUserCountry() {
        // 实现地理位置检测逻辑
        return 'US';
    }
}

/**
 * 权限检查辅助函数
 */
function requirePermission($permission, $options = []) {
    static $middleware = null;
    
    if ($middleware === null) {
        $middleware = new PermissionMiddleware();
    }
    
    return $middleware->checkPermission($permission, $options);
}

/**
 * 检查多个权限
 */
function requireMultiplePermissions($permissions, $options = []) {
    static $middleware = null;
    
    if ($middleware === null) {
        $middleware = new PermissionMiddleware();
    }
    
    return $middleware->checkMultiplePermissions($permissions, $options);
}

/**
 * 检查任一权限
 */
function requireAnyPermission($permissions, $options = []) {
    static $middleware = null;
    
    if ($middleware === null) {
        $middleware = new PermissionMiddleware();
    }
    
    return $middleware->checkAnyPermission($permissions, $options);
}

/**
 * 检查角色级别
 */
function requireRoleLevel($level, $options = []) {
    static $middleware = null;
    
    if ($middleware === null) {
        $middleware = new PermissionMiddleware();
    }
    
    return $middleware->checkRoleLevel($level, $options);
}

/**
 * 检查资源所有权
 */
function requireResourceOwnership($resourceType, $resourceId, $allowedRoles = ['super_admin']) {
    static $middleware = null;
    
    if ($middleware === null) {
        $middleware = new PermissionMiddleware();
    }
    
    return $middleware->checkResourceOwnership($resourceType, $resourceId, $allowedRoles);
}
?>
