<?php
/**
 * API频率限制类
 * 实现API请求频率限制功能
 */

class ApiRateLimit {
    
    private $db;
    private $cachePrefix = 'rate_limit:';
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * 检查频率限制
     */
    public function checkLimit($identifier, $limit = 100, $window = 3600, $type = 'general') {
        $key = $this->getCacheKey($identifier, $type);
        $now = time();
        $windowStart = $now - $window;
        
        // 获取当前窗口内的请求数
        $currentRequests = $this->getCurrentRequests($key, $windowStart, $now);
        
        // 检查是否超过限制
        if ($currentRequests >= $limit) {
            $resetTime = $this->getResetTime($key, $window);
            return [
                'allowed' => false,
                'limit' => $limit,
                'remaining' => 0,
                'reset_time' => $resetTime,
                'retry_after' => $resetTime - $now
            ];
        }
        
        // 记录请求
        $this->recordRequest($key, $now);
        
        return [
            'allowed' => true,
            'limit' => $limit,
            'remaining' => $limit - $currentRequests - 1,
            'reset_time' => $now + $window,
            'retry_after' => null
        ];
    }
    
    /**
     * 检查用户频率限制
     */
    public function checkUserLimit($userId, $endpoint = null) {
        // 获取用户限制配置
        $userLimits = $this->getUserLimits($userId);
        
        // 全局限制
        $globalResult = $this->checkLimit(
            "user:{$userId}",
            $userLimits['global_limit'],
            $userLimits['global_window'],
            'user_global'
        );
        
        if (!$globalResult['allowed']) {
            return $globalResult;
        }
        
        // 端点特定限制
        if ($endpoint) {
            $endpointLimits = $this->getEndpointLimits($endpoint);
            $endpointResult = $this->checkLimit(
                "user:{$userId}:endpoint:{$endpoint}",
                $endpointLimits['limit'],
                $endpointLimits['window'],
                'user_endpoint'
            );
            
            if (!$endpointResult['allowed']) {
                return $endpointResult;
            }
        }
        
        return $globalResult;
    }
    
    /**
     * 检查IP频率限制
     */
    public function checkIpLimit($ipAddress, $endpoint = null) {
        // IP全局限制
        $ipResult = $this->checkLimit(
            "ip:{$ipAddress}",
            $this->getIpLimit(),
            3600, // 1小时窗口
            'ip_global'
        );
        
        if (!$ipResult['allowed']) {
            return $ipResult;
        }
        
        // IP端点限制
        if ($endpoint) {
            $endpointResult = $this->checkLimit(
                "ip:{$ipAddress}:endpoint:{$endpoint}",
                $this->getIpEndpointLimit($endpoint),
                300, // 5分钟窗口
                'ip_endpoint'
            );
            
            if (!$endpointResult['allowed']) {
                return $endpointResult;
            }
        }
        
        return $ipResult;
    }
    
    /**
     * 获取当前请求数
     */
    private function getCurrentRequests($key, $windowStart, $now) {
        // 使用数据库存储（生产环境建议使用Redis）
        $sql = "
            SELECT COUNT(*)
            FROM pt_api_rate_limits
            WHERE cache_key = :key
            AND request_time >= :window_start
            AND request_time <= :now
        ";
        
        return $this->db->fetchColumn($sql, [
            'key' => $key,
            'window_start' => $windowStart,
            'now' => $now
        ]) ?: 0;
    }
    
    /**
     * 记录请求
     */
    private function recordRequest($key, $timestamp) {
        $this->db->insert('pt_api_rate_limits', [
            'cache_key' => $key,
            'request_time' => $timestamp,
            'created_at' => date('Y-m-d H:i:s', $timestamp)
        ]);
        
        // 清理过期记录
        $this->cleanupExpiredRecords();
    }
    
    /**
     * 获取重置时间
     */
    private function getResetTime($key, $window) {
        $sql = "
            SELECT MIN(request_time) + :window as reset_time
            FROM pt_api_rate_limits
            WHERE cache_key = :key
            ORDER BY request_time DESC
            LIMIT 1
        ";
        
        $result = $this->db->fetchColumn($sql, [
            'key' => $key,
            'window' => $window
        ]);
        
        return $result ?: (time() + $window);
    }
    
    /**
     * 获取缓存键
     */
    private function getCacheKey($identifier, $type) {
        return $this->cachePrefix . $type . ':' . $identifier;
    }
    
    /**
     * 获取用户限制配置
     */
    private function getUserLimits($userId) {
        // 从数据库获取用户配置
        $user = $this->db->fetch("SELECT subscription_type, api_rate_limit FROM pt_member WHERE id = :id", ['id' => $userId]);
        
        if (!$user) {
            return $this->getDefaultLimits();
        }
        
        // 根据订阅类型设置限制
        $limits = [
            'free' => ['global_limit' => 100, 'global_window' => 3600],
            'basic' => ['global_limit' => 500, 'global_window' => 3600],
            'premium' => ['global_limit' => 2000, 'global_window' => 3600],
            'enterprise' => ['global_limit' => 10000, 'global_window' => 3600]
        ];
        
        $subscriptionLimits = $limits[$user['subscription_type']] ?? $limits['free'];
        
        // 如果用户有自定义限制，使用自定义值
        if ($user['api_rate_limit'] > 0) {
            $subscriptionLimits['global_limit'] = $user['api_rate_limit'];
        }
        
        return $subscriptionLimits;
    }
    
    /**
     * 获取端点限制配置
     */
    private function getEndpointLimits($endpoint) {
        $endpointLimits = [
            '/auth/login' => ['limit' => 5, 'window' => 300], // 5次/5分钟
            '/auth/register' => ['limit' => 3, 'window' => 3600], // 3次/小时
            '/tools/*/execute' => ['limit' => 50, 'window' => 3600], // 50次/小时
            '/upload' => ['limit' => 10, 'window' => 3600], // 10次/小时
            '/search' => ['limit' => 100, 'window' => 3600], // 100次/小时
        ];
        
        // 匹配端点模式
        foreach ($endpointLimits as $pattern => $limits) {
            if ($this->matchEndpoint($pattern, $endpoint)) {
                return $limits;
            }
        }
        
        // 默认端点限制
        return ['limit' => 200, 'window' => 3600];
    }
    
    /**
     * 获取IP限制
     */
    private function getIpLimit() {
        return 1000; // 每小时1000次请求
    }
    
    /**
     * 获取IP端点限制
     */
    private function getIpEndpointLimit($endpoint) {
        $limits = [
            '/auth/login' => 20, // 20次/5分钟
            '/auth/register' => 10, // 10次/5分钟
            '/upload' => 50, // 50次/5分钟
        ];
        
        return $limits[$endpoint] ?? 100; // 默认100次/5分钟
    }
    
    /**
     * 获取默认限制
     */
    private function getDefaultLimits() {
        return [
            'global_limit' => 100,
            'global_window' => 3600
        ];
    }
    
    /**
     * 匹配端点模式
     */
    private function matchEndpoint($pattern, $endpoint) {
        // 简单的通配符匹配
        $pattern = str_replace('*', '([^/]+)', $pattern);
        return preg_match('#^' . $pattern . '$#', $endpoint);
    }
    
    /**
     * 清理过期记录
     */
    private function cleanupExpiredRecords() {
        // 每100次请求清理一次过期记录
        if (rand(1, 100) === 1) {
            $expiredTime = time() - 7200; // 2小时前的记录
            $this->db->query(
                "DELETE FROM pt_api_rate_limits WHERE request_time < :expired_time",
                ['expired_time' => $expiredTime]
            );
        }
    }
    
    /**
     * 获取限制状态
     */
    public function getLimitStatus($identifier, $type = 'general') {
        $key = $this->getCacheKey($identifier, $type);
        $now = time();
        $windowStart = $now - 3600; // 1小时窗口
        
        $currentRequests = $this->getCurrentRequests($key, $windowStart, $now);
        
        return [
            'current_requests' => $currentRequests,
            'window_start' => $windowStart,
            'window_end' => $now
        ];
    }
    
    /**
     * 重置限制
     */
    public function resetLimit($identifier, $type = 'general') {
        $key = $this->getCacheKey($identifier, $type);
        
        $this->db->query(
            "DELETE FROM pt_api_rate_limits WHERE cache_key = :key",
            ['key' => $key]
        );
        
        return true;
    }
    
    /**
     * 添加白名单
     */
    public function addToWhitelist($identifier, $type = 'ip') {
        $this->db->insert('pt_api_rate_limit_whitelist', [
            'identifier' => $identifier,
            'type' => $type,
            'created_at' => date('Y-m-d H:i:s')
        ]);
    }
    
    /**
     * 检查是否在白名单
     */
    public function isWhitelisted($identifier, $type = 'ip') {
        $result = $this->db->fetch(
            "SELECT id FROM pt_api_rate_limit_whitelist WHERE identifier = :identifier AND type = :type",
            ['identifier' => $identifier, 'type' => $type]
        );
        
        return !empty($result);
    }
}
?>
