<?php
/**
 * 产品发布管理页面
 */

// 检查管理员权限
if (!isset($_SESSION['admin_id'])) {
    header('Location: /control-panel/auth/login.php');
    exit;
}

// 获取筛选参数
$status = $_GET['status'] ?? 'all';
$category = $_GET['category'] ?? 'all';
$launchStatus = $_GET['launch_status'] ?? 'all';
$sort = $_GET['sort'] ?? 'latest';
$search = $_GET['search'] ?? '';
$currentPageNum = max(1, intval($_GET['p'] ?? 1));
$perPage = 10;
$offset = ($currentPageNum - 1) * $perPage;



$currentPage = 'launches';
$pageTitle = 'Product Launches';

// 构建查询条件
$whereConditions = [];
$params = [];

if ($status !== 'all') {
    $whereConditions[] = "l.status = ?";
    $params[] = $status;
}

if ($category !== 'all') {
    $whereConditions[] = "l.category = ?";
    $params[] = $category;
}

if ($launchStatus !== 'all') {
    $whereConditions[] = "l.launch_status = ?";
    $params[] = $launchStatus;
}

if (!empty($search)) {
    $whereConditions[] = "(l.name LIKE ? OR l.tagline LIKE ? OR l.description LIKE ? OR m.username LIKE ?)";
    $searchTerm = "%$search%";
    $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm, $searchTerm]);
}

$whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

// 排序
$orderBy = match($sort) {
    'oldest' => 'l.created_at ASC',
    'name' => 'l.name ASC',
    'views' => 'l.views DESC',
    'votes' => 'l.votes DESC',
    default => 'l.created_at DESC'
};

// 获取总数
$countSql = "
    SELECT COUNT(*) 
    FROM pt_product_launches l
    LEFT JOIN pt_member m ON l.user_id = m.id
    $whereClause
";
$countStmt = $pdo->prepare($countSql);
$countStmt->execute($params);
$totalLaunches = $countStmt->fetchColumn();
$totalPages = ceil($totalLaunches / $perPage);

// 获取产品列表
$sql = "
    SELECT l.*, m.username, m.first_name, m.last_name, m.email
    FROM pt_product_launches l
    LEFT JOIN pt_member m ON l.user_id = m.id
    $whereClause
    ORDER BY $orderBy
    LIMIT $perPage OFFSET $offset
";
$stmt = $pdo->prepare($sql);
$stmt->execute($params);
$launchesList = $stmt->fetchAll();

// 获取统计数据
$stats = $pdo->query("
    SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
        SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved,
        SUM(CASE WHEN status = 'rejected' THEN 1 ELSE 0 END) as rejected,
        SUM(CASE WHEN status = 'featured' THEN 1 ELSE 0 END) as featured
    FROM pt_product_launches
")->fetch();

// 从数据库获取产品分类
$categoriesStmt = $pdo->query("SELECT slug, name FROM pt_launch_categories ORDER BY name ASC");
$categories = [];
while ($cat = $categoriesStmt->fetch()) {
    $categories[$cat['slug']] = $cat['name'];
}

// 状态颜色配置
$statusColors = [
    'pending' => 'bg-yellow-100 text-yellow-800',
    'approved' => 'bg-green-100 text-green-800',
    'rejected' => 'bg-red-100 text-red-800',
    'featured' => 'bg-purple-100 text-purple-800'
];

// 发布状态颜色配置
$launchStatusColors = [
    'coming-soon' => 'bg-blue-100 text-blue-800',
    'beta' => 'bg-purple-100 text-purple-800',
    'launched' => 'bg-green-100 text-green-800'
];
?>

<div class="space-y-6">
    <!-- 页面描述和操作 -->
    <div class="flex items-center justify-between">
        <div>
            <p class="text-gray-600">Manage product launches and moderate submissions</p>
        </div>
        <div class="flex space-x-3">
            <button onclick="exportLaunches()" class="bg-green-600 text-white px-4 py-2 hover:bg-green-700 rounded">
                Export Data
            </button>
        </div>
    </div>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-2 md:grid-cols-5 gap-4">
        <div class="bg-white p-4 rounded-lg shadow">
            <div class="text-2xl font-bold text-gray-900"><?= $stats['total'] ?></div>
            <div class="text-sm text-gray-600">Total Launches</div>
        </div>
        <div class="bg-white p-4 rounded-lg shadow">
            <div class="text-2xl font-bold text-yellow-600"><?= $stats['pending'] ?></div>
            <div class="text-sm text-gray-600">Pending</div>
        </div>
        <div class="bg-white p-4 rounded-lg shadow">
            <div class="text-2xl font-bold text-green-600"><?= $stats['approved'] ?></div>
            <div class="text-sm text-gray-600">Approved</div>
        </div>
        <div class="bg-white p-4 rounded-lg shadow">
            <div class="text-2xl font-bold text-purple-600"><?= $stats['featured'] ?></div>
            <div class="text-sm text-gray-600">Featured</div>
        </div>
        <div class="bg-white p-4 rounded-lg shadow">
            <div class="text-2xl font-bold text-red-600"><?= $stats['rejected'] ?></div>
            <div class="text-sm text-gray-600">Rejected</div>
        </div>
    </div>

    <!-- 筛选器 -->
    <div class="bg-white p-6 rounded-lg shadow">
        <form method="GET" class="grid grid-cols-1 md:grid-cols-6 gap-4">
            <input type="hidden" name="page" value="launches">
            
            <!-- 搜索 -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Search</label>
                <input type="text" name="search" value="<?= htmlspecialchars($search) ?>" 
                       placeholder="Product name, description, or user..."
                       class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
            </div>
            
            <!-- 审核状态筛选 -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                <select name="status" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                    <option value="all" <?= $status === 'all' ? 'selected' : '' ?>>All Status</option>
                    <option value="pending" <?= $status === 'pending' ? 'selected' : '' ?>>Pending</option>
                    <option value="approved" <?= $status === 'approved' ? 'selected' : '' ?>>Approved</option>
                    <option value="featured" <?= $status === 'featured' ? 'selected' : '' ?>>Featured</option>
                    <option value="rejected" <?= $status === 'rejected' ? 'selected' : '' ?>>Rejected</option>
                </select>
            </div>
            
            <!-- 分类筛选 -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Category</label>
                <select name="category" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                    <option value="all" <?= $category === 'all' ? 'selected' : '' ?>>All Categories</option>
                    <?php foreach ($categories as $slug => $name): ?>
                        <option value="<?= $slug ?>" <?= $category === $slug ? 'selected' : '' ?>><?= htmlspecialchars($name) ?></option>
                    <?php endforeach; ?>
                </select>
            </div>

            <!-- 发布状态筛选 -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Launch Status</label>
                <select name="launch_status" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                    <option value="all" <?= $launchStatus === 'all' ? 'selected' : '' ?>>All Launch Status</option>
                    <option value="coming-soon" <?= $launchStatus === 'coming-soon' ? 'selected' : '' ?>>Coming Soon</option>
                    <option value="beta" <?= $launchStatus === 'beta' ? 'selected' : '' ?>>Beta</option>
                    <option value="launched" <?= $launchStatus === 'launched' ? 'selected' : '' ?>>Launched</option>
                </select>
            </div>

            <!-- 排序 -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Sort By</label>
                <select name="sort" class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm">
                    <option value="latest" <?= $sort === 'latest' ? 'selected' : '' ?>>Latest</option>
                    <option value="oldest" <?= $sort === 'oldest' ? 'selected' : '' ?>>Oldest</option>
                    <option value="name" <?= $sort === 'name' ? 'selected' : '' ?>>Name</option>
                    <option value="views" <?= $sort === 'views' ? 'selected' : '' ?>>Most Views</option>
                    <option value="votes" <?= $sort === 'votes' ? 'selected' : '' ?>>Most Votes</option>
                </select>
            </div>
            
            <!-- 操作按钮 -->
            <div class="flex items-end space-x-2">
                <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 text-sm">
                    Filter
                </button>
                <a href="?page=launches" class="bg-gray-500 text-white px-4 py-2 rounded-md hover:bg-gray-600 text-sm">
                    Clear
                </a>
            </div>
        </form>
    </div>

    <!-- 产品列表 -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
        <?php if (empty($launchesList)): ?>
            <div class="p-8 text-center">
                <div class="text-gray-400 mb-4">
                    <svg class="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No launches found</h3>
                <p class="text-gray-600">No product launches match your current filters.</p>
            </div>
        <?php else: ?>


            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Launch Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Stats</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php foreach ($launchesList as $launch): ?>
                            <tr class="hover:bg-gray-50 <?= !empty($launch['featured_date']) ? 'bg-purple-50 border-l-4 border-purple-400' : '' ?>">
                                <td class="px-6 py-4">
                                    <div class="flex items-center space-x-3">
                                        <!-- Logo -->
                                        <div class="flex-shrink-0">
                                            <?php if (!empty($launch['logo_url'])): ?>
                                                <img src="<?= htmlspecialchars($launch['logo_url']) ?>"
                                                     alt="<?= htmlspecialchars($launch['name']) ?> logo"
                                                     class="w-10 h-10 rounded-lg object-cover border border-gray-200">
                                            <?php else: ?>
                                                <div class="w-10 h-10 bg-gray-200 rounded-lg flex items-center justify-center">
                                                    <i class="fas fa-rocket text-gray-400"></i>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        <!-- 产品信息 -->
                                        <div class="max-w-xs">
                                            <div class="text-sm font-medium text-gray-900 truncate">
                                                <?php if ($launch['status'] === 'approved'): ?>
                                                    <a href="/launch/<?= htmlspecialchars($launch['slug']) ?>" target="_blank" class="hover:text-blue-600">
                                                        <?= htmlspecialchars($launch['name']) ?>
                                                    </a>
                                                <?php else: ?>
                                                    <span class="text-gray-700">
                                                        <?= htmlspecialchars($launch['name']) ?>
                                                    </span>
                                                <?php endif; ?>
                                            </div>
                                            <?php if (!empty($launch['tagline'])): ?>
                                                <div class="text-sm text-gray-500 truncate"><?= htmlspecialchars(substr($launch['tagline'], 0, 60)) ?><?= strlen($launch['tagline']) > 60 ? '...' : '' ?></div>
                                            <?php endif; ?>
                                            <div class="text-xs mt-1">
                                                <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium">
                                                    <?= htmlspecialchars($categories[$launch['category']] ?? ucfirst($launch['category'])) ?>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900"><?= htmlspecialchars($launch['first_name'] ?? $launch['username']) ?></div>
                                    <div class="text-sm text-gray-500"><?= htmlspecialchars($launch['email']) ?></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 py-1 text-xs font-medium rounded-full <?= $statusColors[$launch['status']] ?>">
                                        <?= ucfirst($launch['status']) ?>
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 py-1 text-xs font-medium rounded-full <?= $launchStatusColors[$launch['launch_status']] ?>">
                                        <?= ucfirst(str_replace('-', ' ', $launch['launch_status'])) ?>
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <div class="flex items-center space-x-4 text-xs">
                                        <span title="Views"><i class="fas fa-eye text-gray-400"></i> <?= number_format($launch['views']) ?></span>
                                        <span title="Votes"><i class="fas fa-heart text-gray-400"></i> <?= number_format($launch['votes']) ?></span>
                                        <span title="Clicks"><i class="fas fa-mouse-pointer text-gray-400"></i> <?= number_format($launch['clicks']) ?></span>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <?= date('M j, Y', strtotime($launch['created_at'])) ?>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex items-center space-x-1">
                                        <!-- View Launch -->
                                        <?php if ($launch['status'] === 'approved'): ?>
                                            <a href="/launch/<?= htmlspecialchars($launch['slug']) ?>" target="_blank"
                                               class="inline-flex items-center justify-center w-8 h-8 text-blue-600 hover:text-white hover:bg-blue-600 border border-blue-300 hover:border-blue-600 rounded transition-all duration-200"
                                               title="View Launch">
                                                <i class="fas fa-external-link-alt text-xs"></i>
                                            </a>
                                        <?php endif; ?>

                                        <!-- Quick Actions for Pending -->
                                        <?php if ($launch['status'] === 'pending'): ?>
                                            <button onclick="quickApprove(<?= $launch['id'] ?>)"
                                                    class="inline-flex items-center justify-center w-8 h-8 text-green-600 hover:text-white hover:bg-green-600 border border-green-300 hover:border-green-600 rounded transition-all duration-200"
                                                    title="Approve">
                                                <i class="fas fa-check text-xs"></i>
                                            </button>
                                            <button onclick="quickReject(<?= $launch['id'] ?>)"
                                                    class="inline-flex items-center justify-center w-8 h-8 text-orange-600 hover:text-white hover:bg-orange-600 border border-orange-300 hover:border-orange-600 rounded transition-all duration-200"
                                                    title="Reject">
                                                <i class="fas fa-times text-xs"></i>
                                            </button>
                                        <?php endif; ?>

                                        <!-- Edit -->
                                        <a href="?page=edit-launch&id=<?= $launch['id'] ?>"
                                           class="inline-flex items-center justify-center w-8 h-8 text-indigo-600 hover:text-white hover:bg-indigo-600 border border-indigo-300 hover:border-indigo-600 rounded transition-all duration-200"
                                           title="Edit">
                                            <i class="fas fa-edit text-xs"></i>
                                        </a>

                                        <!-- Feature/Unfeature -->
                                        <?php if ($launch['status'] === 'approved'): ?>
                                            <?php if (!empty($launch['featured_date'])): ?>
                                                <button onclick="unfeatureLaunch(<?= $launch['id'] ?>)"
                                                        class="inline-flex items-center justify-center w-8 h-8 text-yellow-600 hover:text-white hover:bg-yellow-600 border border-yellow-300 hover:border-yellow-600 rounded transition-all duration-200"
                                                        title="Remove Featured">
                                                    <i class="fas fa-star text-xs"></i>
                                                </button>
                                            <?php else: ?>
                                                <button onclick="featureLaunch(<?= $launch['id'] ?>)"
                                                        class="inline-flex items-center justify-center w-8 h-8 text-gray-600 hover:text-white hover:bg-yellow-600 border border-gray-300 hover:border-yellow-600 rounded transition-all duration-200"
                                                        title="Set Featured">
                                                    <i class="far fa-star text-xs"></i>
                                                </button>
                                            <?php endif; ?>
                                        <?php endif; ?>

                                        <!-- Delete -->
                                        <button onclick="deleteLaunch(<?= $launch['id'] ?>)"
                                                class="inline-flex items-center justify-center w-8 h-8 text-red-600 hover:text-white hover:bg-red-600 border border-red-300 hover:border-red-600 rounded transition-all duration-200"
                                                title="Delete">
                                            <i class="fas fa-trash text-xs"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>

    <!-- 分页 -->
    <?php if ($totalPages > 1): ?>
    <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
        <div class="flex items-center justify-between">
            <div class="flex-1 flex justify-between sm:hidden">
                <?php if ($currentPageNum > 1): ?>
                <a href="?page=launches&<?= http_build_query(array_merge($_GET, ['p' => $currentPageNum - 1])) ?>"
                   class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Previous
                </a>
                <?php endif; ?>
                <?php if ($currentPageNum < $totalPages): ?>
                <a href="?page=launches&<?= http_build_query(array_merge($_GET, ['p' => $currentPageNum + 1])) ?>"
                   class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    Next
                </a>
                <?php endif; ?>
            </div>
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                    <p class="text-sm text-gray-700">
                        Showing <span class="font-medium"><?= $offset + 1 ?></span> to
                        <span class="font-medium"><?= min($offset + $perPage, $totalLaunches) ?></span> of
                        <span class="font-medium"><?= $totalLaunches ?></span> results
                    </p>
                </div>
                <div>
                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                        <?php if ($currentPageNum > 1): ?>
                        <a href="?page=launches&<?= http_build_query(array_merge($_GET, ['p' => $currentPageNum - 1])) ?>"
                           class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            <span class="sr-only">Previous</span>
                            <i class="fas fa-chevron-left"></i>
                        </a>
                        <?php endif; ?>

                        <?php
                        $start = max(1, $currentPageNum - 2);
                        $end = min($totalPages, $currentPageNum + 2);

                        for ($i = $start; $i <= $end; $i++):
                        ?>
                        <a href="?page=launches&<?= http_build_query(array_merge($_GET, ['p' => $i])) ?>"
                           class="relative inline-flex items-center px-4 py-2 border text-sm font-medium <?= $i === $currentPageNum ? 'z-10 bg-blue-50 border-blue-500 text-blue-600' : 'border-gray-300 bg-white text-gray-500 hover:bg-gray-50' ?>">
                            <?= $i ?>
                        </a>
                        <?php endfor; ?>

                        <?php if ($currentPageNum < $totalPages): ?>
                        <a href="?page=launches&<?= http_build_query(array_merge($_GET, ['p' => $currentPageNum + 1])) ?>"
                           class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                            <span class="sr-only">Next</span>
                            <i class="fas fa-chevron-right"></i>
                        </a>
                        <?php endif; ?>
                    </nav>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<!-- 编辑模态框已移除 - 现在使用单独的编辑页面 -->



<!-- 自定义提示窗组件 -->
<div id="customAlert" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4 transform transition-all">
        <div class="p-6">
            <div class="flex items-center mb-4">
                <div id="alertIcon" class="flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center mr-3">
                    <i id="alertIconClass" class="text-xl"></i>
                </div>
                <h3 id="alertTitle" class="text-lg font-semibold text-gray-900"></h3>
            </div>
            <p id="alertMessage" class="text-gray-600 mb-6"></p>
            <div class="flex justify-end space-x-3">
                <button id="alertCancel" class="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors hidden">
                    Cancel
                </button>
                <button id="alertConfirm" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
                    OK
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 成功/错误通知组件 -->
<div id="notification" class="fixed top-4 right-4 z-50 hidden">
    <div class="bg-white rounded-lg shadow-lg border-l-4 p-4 max-w-sm">
        <div class="flex items-center">
            <div id="notificationIcon" class="flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center mr-3">
                <i id="notificationIconClass" class="text-lg"></i>
            </div>
            <div class="flex-1">
                <p id="notificationMessage" class="text-sm font-medium text-gray-900"></p>
            </div>
            <button onclick="hideNotification()" class="ml-3 text-gray-400 hover:text-gray-600">
                <i class="fas fa-times"></i>
            </button>
        </div>
    </div>
</div>

<script>
// 自定义提示窗函数
function showAlert(title, message, type = 'info', confirmCallback = null) {
    const modal = document.getElementById('customAlert');
    const titleEl = document.getElementById('alertTitle');
    const messageEl = document.getElementById('alertMessage');
    const iconEl = document.getElementById('alertIcon');
    const iconClassEl = document.getElementById('alertIconClass');
    const cancelBtn = document.getElementById('alertCancel');
    const confirmBtn = document.getElementById('alertConfirm');

    titleEl.textContent = title;
    messageEl.textContent = message;

    // 设置图标和颜色
    switch(type) {
        case 'success':
            iconEl.className = 'flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center mr-3 bg-green-100';
            iconClassEl.className = 'text-xl fas fa-check text-green-600';
            confirmBtn.className = 'px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors';
            break;
        case 'error':
            iconEl.className = 'flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center mr-3 bg-red-100';
            iconClassEl.className = 'text-xl fas fa-times text-red-600';
            confirmBtn.className = 'px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors';
            break;
        case 'warning':
            iconEl.className = 'flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center mr-3 bg-yellow-100';
            iconClassEl.className = 'text-xl fas fa-exclamation-triangle text-yellow-600';
            confirmBtn.className = 'px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 transition-colors';
            break;
        case 'confirm':
            iconEl.className = 'flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center mr-3 bg-blue-100';
            iconClassEl.className = 'text-xl fas fa-question text-blue-600';
            confirmBtn.className = 'px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors';
            cancelBtn.classList.remove('hidden');
            confirmBtn.textContent = 'Confirm';
            break;
        default:
            iconEl.className = 'flex-shrink-0 w-10 h-10 rounded-full flex items-center justify-center mr-3 bg-blue-100';
            iconClassEl.className = 'text-xl fas fa-info text-blue-600';
            confirmBtn.className = 'px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors';
    }

    if (type !== 'confirm') {
        cancelBtn.classList.add('hidden');
        confirmBtn.textContent = 'OK';
    }

    modal.classList.remove('hidden');
    modal.classList.add('flex');

    // 处理确认按钮点击
    confirmBtn.onclick = function() {
        modal.classList.add('hidden');
        modal.classList.remove('flex');
        if (confirmCallback) confirmCallback();
    };

    // 处理取消按钮点击
    cancelBtn.onclick = function() {
        modal.classList.add('hidden');
        modal.classList.remove('flex');
    };

    // 点击外部关闭
    modal.onclick = function(e) {
        if (e.target === modal) {
            modal.classList.add('hidden');
            modal.classList.remove('flex');
        }
    };
}

// 显示通知
function showNotification(message, type = 'success') {
    const notification = document.getElementById('notification');
    const messageEl = document.getElementById('notificationMessage');
    const iconEl = document.getElementById('notificationIcon');
    const iconClassEl = document.getElementById('notificationIconClass');

    messageEl.textContent = message;

    switch(type) {
        case 'success':
            iconEl.className = 'flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center mr-3 bg-green-100';
            iconClassEl.className = 'text-lg fas fa-check text-green-600';
            notification.querySelector('.bg-white').className = 'bg-white rounded-lg shadow-lg border-l-4 border-green-500 p-4 max-w-sm';
            break;
        case 'error':
            iconEl.className = 'flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center mr-3 bg-red-100';
            iconClassEl.className = 'text-lg fas fa-times text-red-600';
            notification.querySelector('.bg-white').className = 'bg-white rounded-lg shadow-lg border-l-4 border-red-500 p-4 max-w-sm';
            break;
        case 'warning':
            iconEl.className = 'flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center mr-3 bg-yellow-100';
            iconClassEl.className = 'text-lg fas fa-exclamation-triangle text-yellow-600';
            notification.querySelector('.bg-white').className = 'bg-white rounded-lg shadow-lg border-l-4 border-yellow-500 p-4 max-w-sm';
            break;
    }

    notification.classList.remove('hidden');

    // 3秒后自动隐藏
    setTimeout(() => {
        hideNotification();
    }, 3000);
}

// 隐藏通知
function hideNotification() {
    document.getElementById('notification').classList.add('hidden');
}



// 编辑产品 - 现在跳转到单独页面
// 编辑按钮已改为链接，无需JavaScript函数

// 编辑表单提交处理已移除 - 现在使用单独的编辑页面

// 设为精选
async function featureLaunch(id) {
    showAlert(
        'Set as Featured',
        'Set this launch as featured?',
        'confirm',
        () => updateLaunchStatus(id, 'featured')
    );
}

// 取消精选
async function unfeatureLaunch(id) {
    showAlert(
        'Remove from Featured',
        'Remove this launch from featured?',
        'confirm',
        () => updateLaunchStatus(id, 'unfeature')
    );
}

// 删除产品
async function deleteLaunch(id) {
    showAlert(
        '⚠️ Permanently Delete Launch',
        'This will permanently delete this launch from the database. This action cannot be undone. Are you absolutely sure?',
        'error',
        () => updateLaunchStatus(id, 'delete')
    );
}

// 快速批准
async function quickApprove(id) {
    showAlert(
        'Approve Launch',
        'Approve this launch?',
        'confirm',
        () => updateLaunchStatus(id, 'approved')
    );
}

// 快速拒绝
async function quickReject(id) {
    showAlert(
        'Reject Launch',
        'Reject this launch?',
        'confirm',
        () => updateLaunchStatus(id, 'rejected')
    );
}

// 更新状态的通用函数
async function updateLaunchStatus(id, action) {
    // 调试信息
    console.log('updateLaunchStatus called:', { id, action });

    try {
        const requestData = {
            action: action === 'delete' ? 'delete' : 'update_status',
            id: id,
            status: action !== 'delete' ? action : undefined
        };

        console.log('Request data:', requestData);

        const response = await fetch('/control-panel/ajax/launches-handler.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestData)
        });

        console.log('Response status:', response.status);
        console.log('Response headers:', response.headers);

        // 检查响应是否为JSON
        const contentType = response.headers.get('content-type');
        if (!contentType || !contentType.includes('application/json')) {
            const textResponse = await response.text();
            console.error('Non-JSON response:', textResponse);
            showAlert('Error', 'Server returned non-JSON response: ' + textResponse.substring(0, 200), 'error');
            return;
        }

        const result = await response.json();
        console.log('Response result:', result);

        if (result.success) {
            let message = '';
            switch(action) {
                case 'delete':
                    message = 'Launch deleted successfully';
                    break;
                case 'featured':
                    message = 'Launch set as featured';
                    break;
                case 'unfeature':
                    message = 'Launch removed from featured';
                    break;
                case 'approved':
                    message = 'Launch approved successfully';
                    break;
                case 'rejected':
                    message = 'Launch rejected';
                    break;
                default:
                    message = 'Launch status updated successfully';
            }
            showNotification(message, 'success');
            setTimeout(() => {
                location.reload();
            }, 1000);
        } else {
            console.error('Server error:', result.message);
            showAlert('Error', result.message || 'Unknown server error', 'error');
        }
    } catch (error) {
        console.error('Fetch error:', error);
        showAlert('Error', 'Network error: ' + error.message, 'error');
    }
}

// 导出数据
function exportLaunches() {
    const params = new URLSearchParams(window.location.search);
    params.set('export', '1');
    window.location.href = '?' + params.toString();
}
</script>
