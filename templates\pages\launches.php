<?php
/**
 * 产品启动展示页面
 * 参考 requests.php 的样式和结构
 */

// 包含工具辅助函数（包含formatNumber函数）
require_once ROOT_PATH . '/app/helpers/tool-helpers.php';

// 投票相关函数已移除

/**
 * 获取状态颜色样式
 */
function getLaunchStatusColor($status) {
    $colors = [
        'pending' => 'bg-yellow-100 text-yellow-800',
        'approved' => 'bg-green-100 text-green-800',
        'rejected' => 'bg-red-100 text-red-800'
    ];
    
    return $colors[$status] ?? 'bg-gray-100 text-gray-800';
}

/**
 * 获取发布状态颜色样式
 */
function getLaunchStatusBadge($launchStatus) {
    $badges = [
        'coming-soon' => 'bg-blue-100 text-blue-800',
        'beta' => 'bg-purple-100 text-purple-800',
        'launched' => 'bg-green-100 text-green-800'
    ];
    
    return $badges[$launchStatus] ?? 'bg-gray-100 text-gray-800';
}

// 使用统一的数据库连接
require_once ROOT_PATH . '/includes/database-connection.php';

// 数据库连接已通过 includes/database-connection.php 建立

// 获取筛选参数 - 支持POST和GET
$statusFilter = $_POST['status'] ?? $_GET['status'] ?? 'all';
$categoryFilter = $_POST['category'] ?? $_GET['category'] ?? 'all';
$launchStatusFilter = $_POST['launch_status'] ?? $_GET['launch_status'] ?? 'all';
$sort = $_POST['sort'] ?? $_GET['sort'] ?? 'latest';
$page = max(1, intval($_POST['page'] ?? $_GET['page'] ?? 1));
$limit = 5;
$offset = ($page - 1) * $limit;

// 构建查询条件
$whereConditions = ["l.status = 'approved'"]; // 只显示已审核通过的产品
$params = [];

if ($categoryFilter !== 'all') {
    $whereConditions[] = "l.category = ?";
    $params[] = $categoryFilter;
}

if ($launchStatusFilter !== 'all') {
    $whereConditions[] = "l.launch_status = ?";
    $params[] = $launchStatusFilter;
}

$whereClause = 'WHERE ' . implode(' AND ', $whereConditions);

// 排序逻辑
$orderBy = match($sort) {
    'votes' => 'l.votes DESC, l.created_at DESC',
    'views' => 'l.views DESC, l.created_at DESC',
    'featured' => 'l.featured_date DESC, l.created_at DESC',
    'oldest' => 'l.created_at ASC',
    default => 'l.featured_date DESC, l.created_at DESC'
};

// 获取产品列表
$sql = "
    SELECT l.*, m.username, m.first_name, m.last_name,
           CASE WHEN lv.user_id IS NOT NULL THEN 1 ELSE 0 END as user_voted
    FROM pt_product_launches l
    LEFT JOIN pt_member m ON l.user_id = m.id
    LEFT JOIN pt_launch_votes lv ON l.id = lv.launch_id AND lv.user_id = ?
    {$whereClause}
    ORDER BY {$orderBy}
    LIMIT {$limit} OFFSET {$offset}
";

$userVoteParams = [isset($_SESSION['user_id']) ? $_SESSION['user_id'] : 0];
$launches = $pdo->prepare($sql);
$allParams = array_merge($userVoteParams, $params);
$launches->execute($allParams);
$launchesList = $launches->fetchAll();

// 获取分类数据
$categoryStmt = $pdo->query("SELECT slug, name FROM pt_launch_categories WHERE is_active = 1 ORDER BY sort_order ASC, name ASC");
$dbCategories = $categoryStmt->fetchAll();
$categories = [];
foreach ($dbCategories as $cat) {
    $categories[$cat['slug']] = $cat['name'];
}

// 获取统计数据
$statsStmt = $pdo->query("
    SELECT
        COUNT(*) as total_launches,
        COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved_launches,
        COUNT(CASE WHEN featured_date IS NOT NULL THEN 1 END) as featured_launches,
        COUNT(CASE WHEN launch_status = 'launched' THEN 1 END) as live_products,
        SUM(views) as total_views
    FROM pt_product_launches
    WHERE status = 'approved'
");
$stats = $statsStmt->fetch();

// 页面标题和描述
$pageTitle = "Product Launches - Discover Amazing Tools - Prompt2Tool";
$pageDescription = "Discover the latest product launches and innovative tools. Submit your own products and explore community favorites on Prompt2Tool platform.";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($pageTitle) ?></title>
    <meta name="description" content="<?= htmlspecialchars($pageDescription) ?>">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Custom Styles -->
    <style>
        /* 产品卡片样式 */
        .product-card {
            transition: all 0.3s ease;
        }

        .product-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
        }

        .product-card:active {
            transform: translateY(0);
        }

        /* 加载更多按钮样式 */
        #load-more-btn {
            position: relative;
            overflow: hidden;
        }

        #load-more-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        #load-more-btn:hover::before {
            left: 100%;
        }

        /* 加载动画 */
        .fade-in {
            animation: fadeInUp 0.5s ease forwards;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        /* 自定义提示窗样式 */
        .custom-modal {
            backdrop-filter: blur(4px);
        }
        
        .custom-alert {
            animation: slideInDown 0.3s ease-out;
        }
        
        @keyframes slideInDown {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>

<body class="bg-gray-950 text-white min-h-screen">
    <!-- 包含公共头部 -->
    <?php include ROOT_PATH . '/templates/layout/header.php'; ?>

    <main class="pt-8">
        <!-- Hero区域 -->
        <section class="bg-black py-6">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
                <h1 class="text-4xl md:text-5xl font-bold text-white mb-6">
                    Product Launches
                </h1>
                <p class="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
                    Discover the latest product launches and innovative tools from our community
                </p>
                <!-- 统计数据 -->
                <div class="grid grid-cols-2 md:grid-cols-4 gap-6 mt-12">
                    <div class="bg-gray-900 border border-gray-800 p-6">
                        <div class="text-3xl font-bold text-blue-400"><?= formatNumber($stats['total_launches']) ?></div>
                        <div class="text-gray-400 mt-2">Total Launches</div>
                    </div>
                    <div class="bg-gray-900 border border-gray-800 p-6">
                        <div class="text-3xl font-bold text-green-400"><?= formatNumber($stats['live_products']) ?></div>
                        <div class="text-gray-400 mt-2">Live Products</div>
                    </div>
                    <div class="bg-gray-900 border border-gray-800 p-6">
                        <div class="text-3xl font-bold text-purple-400"><?= formatNumber($stats['total_views'] ?? 0) ?></div>
                        <div class="text-gray-400 mt-2">Total Views</div>
                    </div>
                    <div class="bg-gray-900 border border-gray-800 p-6">
                        <div class="text-3xl font-bold text-yellow-400"><?= formatNumber($stats['featured_launches']) ?></div>
                        <div class="text-gray-400 mt-2">Featured</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 主内容区域 -->
        <section class="bg-black py-12">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <!-- 提交产品按钮 -->
        <div class="text-center mb-8">
            <a href="/submit-launch"
               class="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white hover:text-white font-semibold rounded-lg transition-colors">
                <i class="fas fa-plus mr-2"></i>
                Submit Your Product
            </a>
        </div>

        <!-- 筛选器 -->
        <div class="bg-gray-900 rounded-xl p-6 mb-8">
            <form id="filter-form" method="POST" action="/launches" class="space-y-4">
                <div class="flex flex-wrap items-center gap-4">
                    <!-- 分类筛选 -->
                    <div class="flex items-center gap-2 flex-shrink-0">
                        <label class="text-sm font-medium text-gray-300 whitespace-nowrap">Category:</label>
                        <select name="category" id="category-filter" class="px-3 py-2 bg-gray-800 border border-gray-700 text-white text-sm rounded-lg focus:outline-none focus:border-blue-500 w-32">
                            <option value="all" <?= $categoryFilter === 'all' ? 'selected' : '' ?>>All</option>
                            <?php foreach ($categories as $value => $label): ?>
                                <option value="<?= $value ?>" <?= $categoryFilter === $value ? 'selected' : '' ?>><?= htmlspecialchars($label) ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <!-- 发布状态筛选 -->
                    <div class="flex items-center gap-2 flex-shrink-0">
                        <label class="text-sm font-medium text-gray-300 whitespace-nowrap">Status:</label>
                        <select name="launch_status" id="launch-status-filter" class="px-3 py-2 bg-gray-800 border border-gray-700 text-white text-sm rounded-lg focus:outline-none focus:border-blue-500 w-32">
                            <option value="all" <?= $launchStatusFilter === 'all' ? 'selected' : '' ?>>All</option>
                            <option value="launched" <?= $launchStatusFilter === 'launched' ? 'selected' : '' ?>>Launched</option>
                            <option value="beta" <?= $launchStatusFilter === 'beta' ? 'selected' : '' ?>>Beta</option>
                            <option value="coming-soon" <?= $launchStatusFilter === 'coming-soon' ? 'selected' : '' ?>>Coming Soon</option>
                        </select>
                    </div>

                    <!-- 排序 -->
                    <div class="flex items-center gap-2 flex-shrink-0">
                        <label class="text-sm font-medium text-gray-300 whitespace-nowrap">Sort:</label>
                        <select name="sort" id="sort-filter" class="px-3 py-2 bg-gray-800 border border-gray-700 text-white text-sm rounded-lg focus:outline-none focus:border-blue-500 w-32">
                            <option value="latest" <?= $sort === 'latest' ? 'selected' : '' ?>>Latest</option>
                            <option value="featured" <?= $sort === 'featured' ? 'selected' : '' ?>>Featured</option>
                            <option value="votes" <?= $sort === 'votes' ? 'selected' : '' ?>>Most Voted</option>
                            <option value="views" <?= $sort === 'views' ? 'selected' : '' ?>>Most Viewed</option>
                            <option value="oldest" <?= $sort === 'oldest' ? 'selected' : '' ?>>Oldest</option>
                        </select>
                    </div>

                    <!-- 清除筛选按钮 -->
                    <button type="button" id="clear-filters" class="px-4 py-2 bg-gray-700 text-white text-sm hover:bg-gray-600 transition-colors whitespace-nowrap flex-shrink-0 rounded-lg ml-auto">
                        <i class="fas fa-times mr-1"></i>
                        Clear
                    </button>
                </div>

                <!-- 隐藏的页码字段 -->
                <input type="hidden" name="page" value="<?= $page ?>">
            </form>
        </div>

        <!-- 产品列表 -->
        <div id="launches-container" class="space-y-6">
            <?php if (empty($launchesList)): ?>
                <div class="text-center py-12">
                    <div class="text-gray-400 mb-4">
                        <i class="fas fa-rocket text-4xl"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-300 mb-2">No launches found</h3>
                    <p class="text-gray-400 mb-4">Be the first to submit a product launch!</p>
                    <a href="/submit-launch" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white hover:text-white rounded-lg transition-colors">
                        <i class="fas fa-plus mr-2"></i>
                        Submit Product
                    </a>
                </div>
            <?php else: ?>
                <?php foreach ($launchesList as $launch): ?>
                    <a href="/launch/<?= htmlspecialchars($launch['slug']) ?>"
                       class="product-card block bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl p-6 hover:border-gray-600 hover:shadow-lg transition-all duration-300 group cursor-pointer">
                        <!-- 产品内容 -->
                        <div class="flex-1 min-w-0">
                            <!-- 标题和标签区域 -->
                            <div class="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-3 mb-4">
                                <div>
                                    <div class="flex items-baseline gap-2">
                                        <h3 class="text-xl font-bold text-white leading-tight group-hover:text-blue-300 transition-colors duration-200">
                                            <?= htmlspecialchars($launch['name']) ?>
                                        </h3>
                                        <?php if (!empty($launch['featured_date'])): ?>
                                            <span class="text-yellow-400 text-lg flex-shrink-0" title="Featured Product">
                                                <i class="fas fa-star"></i>
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                    <?php if (!empty($launch['tagline'])): ?>
                                        <p class="text-gray-400 text-sm mt-1"><?= htmlspecialchars($launch['tagline']) ?></p>
                                    <?php endif; ?>
                                </div>

                                    <div class="flex items-center gap-2 flex-shrink-0">
                                        <span class="px-3 py-1.5 text-xs font-semibold rounded-full <?= getLaunchStatusBadge($launch['launch_status']) ?> shadow-sm">
                                            <?= ucfirst(str_replace('-', ' ', $launch['launch_status'])) ?>
                                        </span>
                                        <span class="px-3 py-1.5 text-xs font-semibold rounded-full bg-gradient-to-r from-gray-700 to-gray-600 text-gray-200 shadow-sm">
                                            <?= htmlspecialchars($categories[$launch['category']] ?? ucfirst($launch['category'])) ?>
                                        </span>
                                    </div>
                                </div>

                                <!-- 描述内容 -->
                                <div class="mb-5">
                                    <p class="text-gray-300 leading-relaxed line-clamp-3 hover:line-clamp-none transition-all duration-300"><?= htmlspecialchars($launch['description']) ?></p>
                                </div>

                            <!-- 元信息 -->
                            <div class="flex items-center space-x-4 text-sm text-gray-400">
                                <span class="flex items-center">
                                    <svg class="w-4 h-4 mr-1.5" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd"></path>
                                    </svg>
                                    <?= htmlspecialchars($launch['username'] ?? 'Anonymous') ?>
                                </span>
                                <span class="flex items-center">
                                    <svg class="w-4 h-4 mr-1.5" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"></path>
                                    </svg>
                                    <?= date('M j, Y', strtotime($launch['created_at'])) ?>
                                </span>
                                <span class="flex items-center">
                                    <svg class="w-4 h-4 mr-1.5" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"/>
                                        <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"/>
                                    </svg>
                                    <?= number_format($launch['views']) ?> views
                                </span>
                            </div>
                        </div>
                    </a>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>

        <!-- 加载更多按钮 -->
        <?php if (count($launchesList) === $limit): ?>
            <div class="text-center mt-12 mb-8">
                <button id="load-more-btn"
                        class="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white hover:text-white font-semibold rounded-lg transition-colors">
                    <i class="fas fa-chevron-down mr-2"></i>
                    <span>Load More Products</span>
                    <div class="ml-2 w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin hidden" id="load-spinner"></div>
                </button>
            </div>
        <?php endif; ?>

        <!-- 自定义提示窗 -->
        <div id="custom-modal" class="fixed inset-0 bg-black bg-opacity-50 custom-modal hidden z-50 flex items-center justify-center p-4">
            <div class="bg-gray-900 rounded-xl p-6 max-w-md w-full mx-4 custom-alert border border-gray-700">
                <div class="flex items-center mb-4">
                    <div id="modal-icon" class="w-10 h-10 rounded-full flex items-center justify-center mr-3">
                        <i id="modal-icon-class" class="text-xl"></i>
                    </div>
                    <h3 id="modal-title" class="text-lg font-semibold text-white"></h3>
                </div>
                <p id="modal-message" class="text-gray-300 mb-6"></p>
                <div class="flex justify-end space-x-3">
                    <button id="modal-cancel" class="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors hidden">
                        Cancel
                    </button>
                    <button id="modal-confirm" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors">
                        OK
                    </button>
                </div>
            </div>
        </div>
            </div>
        </section>
    </main>

    <!-- 包含公共底部 -->
    <?php include ROOT_PATH . '/templates/layout/footer.php'; ?>

    <!-- JavaScript -->
    <script>
        let currentPage = <?= $page ?>;
        let isLoading = false;

        // 自定义提示窗函数
        function showCustomAlert(title, message, type = 'info', showCancel = false) {
            const modal = document.getElementById('custom-modal');
            const modalIcon = document.getElementById('modal-icon');
            const modalIconClass = document.getElementById('modal-icon-class');
            const modalTitle = document.getElementById('modal-title');
            const modalMessage = document.getElementById('modal-message');
            const modalCancel = document.getElementById('modal-cancel');
            const modalConfirm = document.getElementById('modal-confirm');

            // 设置图标和颜色
            const config = {
                success: { icon: 'fas fa-check', bgColor: 'bg-green-600', iconColor: 'text-white' },
                error: { icon: 'fas fa-times', bgColor: 'bg-red-600', iconColor: 'text-white' },
                warning: { icon: 'fas fa-exclamation', bgColor: 'bg-yellow-600', iconColor: 'text-white' },
                info: { icon: 'fas fa-info', bgColor: 'bg-blue-600', iconColor: 'text-white' }
            };

            const typeConfig = config[type] || config.info;
            modalIcon.className = `w-10 h-10 rounded-full flex items-center justify-center mr-3 ${typeConfig.bgColor}`;
            modalIconClass.className = `${typeConfig.icon} text-xl ${typeConfig.iconColor}`;
            modalTitle.textContent = title;
            modalMessage.textContent = message;

            // 显示/隐藏取消按钮
            modalCancel.classList.toggle('hidden', !showCancel);

            modal.classList.remove('hidden');

            return new Promise((resolve) => {
                modalConfirm.onclick = () => {
                    modal.classList.add('hidden');
                    resolve(true);
                };
                modalCancel.onclick = () => {
                    modal.classList.add('hidden');
                    resolve(false);
                };
            });
        }

        // 投票功能已移除

        // 跟踪点击
        function trackLaunchClick(launchId) {
            fetch('/ajax/track-launch-click.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `launch_id=${launchId}`
            }).catch(error => console.error('Tracking error:', error));
        }

        // 分享功能已移除

        // 加载更多功能
        document.getElementById('load-more-btn')?.addEventListener('click', async function() {
            if (isLoading) return;

            isLoading = true;
            this.disabled = true;

            // 显示加载动画
            const icon = this.querySelector('i');
            const spinner = document.getElementById('load-spinner');
            const text = this.querySelector('span');

            icon.style.display = 'none';
            spinner.classList.remove('hidden');
            text.textContent = 'Loading...';
            this.classList.add('opacity-75', 'cursor-not-allowed');

            try {
                // 获取当前筛选参数
                const form = document.getElementById('filter-form');
                const formData = new FormData(form);
                formData.set('page', currentPage + 1);

                const response = await fetch('/ajax/load-more-launches.php', {
                    method: 'POST',
                    body: formData
                });

                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);

                const responseText = await response.text();
                console.log('Raw response:', responseText);

                let result;
                try {
                    result = JSON.parse(responseText);
                } catch (parseError) {
                    console.error('JSON parse error:', parseError);
                    console.error('Response text:', responseText);
                    throw new Error('Invalid JSON response: ' + responseText.substring(0, 100));
                }

                if (result.success) {
                    // 添加淡入动画
                    const tempDiv = document.createElement('div');
                    tempDiv.innerHTML = result.html;
                    tempDiv.style.opacity = '0';
                    tempDiv.style.transform = 'translateY(20px)';
                    tempDiv.style.transition = 'all 0.5s ease';

                    document.getElementById('launches-container').appendChild(tempDiv);

                    // 触发动画
                    setTimeout(() => {
                        tempDiv.style.opacity = '1';
                        tempDiv.style.transform = 'translateY(0)';
                    }, 100);

                    currentPage++;

                    if (!result.hasMore) {
                        // 显示"没有更多产品"提示
                        this.outerHTML = `
                            <div class="text-center mt-8 mb-8">
                                <div class="inline-flex items-center px-6 py-3 bg-gray-800 text-gray-400 rounded-lg border border-gray-700">
                                    <i class="fas fa-check-circle mr-2 text-green-400"></i>
                                    <span>All products loaded</span>
                                </div>
                            </div>
                        `;
                    }
                } else {
                    showCustomAlert('Load Failed', 'Failed to load more products. Please try again.', 'error');
                }
            } catch (error) {
                console.error('Load more error:', error);
                showCustomAlert('Error', 'An error occurred while loading more products.', 'error');
            } finally {
                isLoading = false;
                this.disabled = false;

                // 恢复按钮状态
                icon.style.display = 'inline';
                spinner.classList.add('hidden');
                text.textContent = 'Load More Products';
                this.classList.remove('opacity-75', 'cursor-not-allowed');
            }
        });

        // 筛选器自动提交
        document.querySelectorAll('#category-filter, #launch-status-filter, #sort-filter').forEach(select => {
            select.addEventListener('change', function() {
                // 重置页码为1
                const form = this.form;
                let pageInput = form.querySelector('input[name="page"]');
                if (!pageInput) {
                    pageInput = document.createElement('input');
                    pageInput.type = 'hidden';
                    pageInput.name = 'page';
                    form.appendChild(pageInput);
                }
                pageInput.value = '1';
                form.submit();
            });
        });

        // 清除筛选器
        document.getElementById('clear-filters').addEventListener('click', function() {
            // 重置所有筛选器
            document.getElementById('category-filter').value = 'all';
            document.getElementById('launch-status-filter').value = 'all';
            document.getElementById('sort-filter').value = 'latest';

            // 提交表单
            const form = document.getElementById('filter-form');
            let pageInput = form.querySelector('input[name="page"]');
            if (!pageInput) {
                pageInput = document.createElement('input');
                pageInput.type = 'hidden';
                pageInput.name = 'page';
                form.appendChild(pageInput);
            }
            pageInput.value = '1';
            form.submit();
        });

        // 点击模态框外部关闭
        document.getElementById('custom-modal').addEventListener('click', function(e) {
            if (e.target === this) {
                this.classList.add('hidden');
            }
        });
    </script>
</body>
</html>
