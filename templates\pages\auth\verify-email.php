<?php
/**
 * 邮箱验证页面
 * 用户输入验证码来激活账户
 */

// 定义根路径（如果还没有定义）
if (!defined('ROOT_PATH')) {
    define('ROOT_PATH', dirname(dirname(dirname(__DIR__))));
}

// 获取邮箱参数
$email = $_GET['email'] ?? '';

// URL解码邮箱地址
if (!empty($email)) {
    $email = urldecode($email);
}

// 验证邮箱格式
if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
    header('Location: /auth/register?error=' . urlencode('Invalid email address'));
    exit;
}

// 包含公共头部
include_once ROOT_PATH . '/templates/layout/header.php';
?>

<div class="min-h-screen bg-gray-900 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <!-- 头部 -->
        <div class="text-center">
            <div class="mx-auto h-12 w-12 bg-white text-black flex items-center justify-center font-bold text-xl rounded">
                P
            </div>
            <h2 class="mt-6 text-3xl font-extrabold text-white">
                Verify Your Email
            </h2>
            <p class="mt-2 text-sm text-gray-400">
                Enter the verification code to activate your account
            </p>
        </div>

        <!-- 消息区域 -->
        <div id="messageArea" class="hidden">
            <div id="messageContent" class="p-4 text-sm"></div>
        </div>

        <!-- 验证表单 -->
        <div class="bg-gray-800 p-8 rounded-lg shadow-lg">
            <div class="mb-6">
                <h3 class="text-xl font-semibold text-white text-center mb-2">
                    Account Created Successfully!
                </h3>
                <p class="text-gray-300 text-center text-sm">
                    We've created your account for <strong><?= htmlspecialchars($email) ?></strong>
                </p>
                <p class="text-gray-400 text-center text-sm mt-2">
                    Please <strong>manually enter</strong> the 6-digit verification code displayed below to activate your account.
                </p>
            </div>

            <!-- 验证码显示区域 -->
            <div class="mb-6 p-4 bg-gray-700 rounded-lg border-2 border-dashed border-gray-600">
                <div class="text-center">
                    <p class="text-gray-300 text-sm mb-2">Your Verification Code:</p>
                    <div class="text-3xl font-mono font-bold text-green-400 tracking-widest" id="verificationCodeDisplay">
                        ------
                    </div>
                    <p class="text-gray-500 text-xs mt-2">
                        <i class="fas fa-info-circle mr-1"></i>
                        This code was generated automatically for security
                    </p>
                </div>
            </div>

            <form id="verifyForm" class="space-y-6">
                <input type="hidden" name="email" value="<?= htmlspecialchars($email) ?>">
                
                <!-- 验证码输入 -->
                <div>
                    <label for="verification_code" class="block text-sm font-medium text-gray-300 mb-2">
                        Enter Verification Code
                    </label>
                    <input type="text"
                           id="verification_code"
                           name="verification_code"
                           required
                           maxlength="6"
                           pattern="[0-9]{6}"
                           class="w-full bg-gray-900 border border-gray-600 text-gray-100 p-3 text-center text-2xl font-mono tracking-widest focus:outline-none focus:border-blue-500"
                           placeholder="000000"
                           autocomplete="off">
                    <p class="text-xs text-gray-500 mt-1">
                        Enter the 6-digit code shown above
                    </p>
                </div>

                <!-- 提交按钮 -->
                <div>
                    <button type="submit"
                            class="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
                        <i class="fas fa-check-circle mr-2"></i>
                        Verify & Activate Account
                    </button>
                </div>
            </form>

            <!-- 重新生成验证码 -->
            <div class="mt-6 text-center">
                <button id="regenerateBtn" 
                        class="text-blue-400 hover:text-blue-300 text-sm font-medium">
                    <i class="fas fa-refresh mr-1"></i>
                    Generate New Code
                </button>
            </div>

            <!-- 返回注册 -->
            <div class="mt-6 text-center">
                <a href="/auth/register" class="text-gray-400 hover:text-gray-300 text-sm">
                    <i class="fas fa-arrow-left mr-1"></i>
                    Back to Registration
                </a>
            </div>
        </div>




    </div>
</div>

<script>
// 全局错误处理
window.addEventListener('error', function(e) {
    console.error('JavaScript error:', e.error);
});

// 未处理的Promise错误
window.addEventListener('unhandledrejection', function(e) {
    console.error('Unhandled promise rejection:', e.reason);
});

document.addEventListener('DOMContentLoaded', function() {
    console.log('Verify email page loaded');

    // 获取DOM元素
    const verifyForm = document.getElementById('verifyForm');
    const messageArea = document.getElementById('messageArea');
    const messageContent = document.getElementById('messageContent');
    const verificationCodeDisplay = document.getElementById('verificationCodeDisplay');
    const verificationCodeInput = document.getElementById('verification_code');
    const regenerateBtn = document.getElementById('regenerateBtn');

    // 检查必要元素是否存在
    if (!verifyForm || !messageArea || !messageContent || !verificationCodeDisplay || !verificationCodeInput) {
        console.error('Required DOM elements not found');
        return;
    }

    // 从URL参数或localStorage获取验证码
    let currentVerificationCode = localStorage.getItem('verification_code') || generateVerificationCode();

    console.log('Current verification code:', currentVerificationCode);
    
    // 显示验证码
    function displayVerificationCode() {
        if (!verificationCodeDisplay) {
            console.error('Verification code display element not found');
            return;
        }

        if (!currentVerificationCode) {
            console.error('No verification code to display');
            return;
        }

        verificationCodeDisplay.textContent = currentVerificationCode;
        localStorage.setItem('verification_code', currentVerificationCode);
        console.log('Verification code displayed:', currentVerificationCode);
    }

    // 生成6位验证码
    function generateVerificationCode() {
        return Math.floor(100000 + Math.random() * 900000).toString();
    }

    // 显示消息
    function showMessage(message, type = 'error') {
        if (!messageContent || !messageArea) {
            console.error('Message elements not found');
            return;
        }

        messageContent.textContent = message;
        messageContent.className = `p-4 text-sm rounded-lg ${type === 'error' ? 'bg-red-600 text-white' : 'bg-green-600 text-white'}`;
        messageArea.classList.remove('hidden');

        // 自动隐藏消息
        setTimeout(() => {
            if (messageArea) {
                messageArea.classList.add('hidden');
            }
        }, type === 'success' ? 3000 : 5000);
    }

    // 初始化显示验证码
    try {
        displayVerificationCode();

        // 确保输入框为空，用户需要手动输入
        if (verificationCodeInput) {
            verificationCodeInput.value = '';
        }

        console.log('Page initialization completed successfully');
    } catch (error) {
        console.error('Error during page initialization:', error);
        showMessage('Page initialization error. Please refresh the page.', 'error');
    }

    // 重新生成验证码
    regenerateBtn.addEventListener('click', async function() {
        const email = '<?= htmlspecialchars($email) ?>';

        this.disabled = true;
        this.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i>Generating...';

        try {
            const response = await fetch('/api/v1/auth/resend-verification', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ email: email })
            });

            const data = await response.json();

            if (data.success) {
                currentVerificationCode = data.data.verification_code;
                displayVerificationCode();
                verificationCodeInput.value = '';
                showMessage('New verification code generated successfully!', 'success');
            } else {
                showMessage(data.message || 'Failed to generate new code');
            }
        } catch (error) {
            showMessage('Network error. Please try again.');
        } finally {
            this.disabled = false;
            this.innerHTML = '<i class="fas fa-refresh mr-1"></i>Generate New Code';
        }
    });

    // 验证码输入格式化
    verificationCodeInput.addEventListener('input', function() {
        this.value = this.value.replace(/\D/g, '').substring(0, 6);
    });

    // 表单提交
    verifyForm.addEventListener('submit', async function(e) {
        e.preventDefault();

        const formData = new FormData(verifyForm);
        const inputCode = formData.get('verification_code');

        // 客户端验证
        if (inputCode.length !== 6) {
            showMessage('Please enter a 6-digit verification code');
            return;
        }
        
        const submitBtn = verifyForm.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Verifying...';
        submitBtn.disabled = true;
        
        try {
            const response = await fetch('/api/v1/auth/verify-email', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    email: formData.get('email'),
                    verification_code: inputCode
                })
            });

            const data = await response.json();
            
            if (data.success) {
                showMessage('Email verified successfully! Welcome to Prompt2Tool! Redirecting...', 'success');
                localStorage.removeItem('verification_code');

                // 如果返回了自动登录标志，直接跳转到用户中心
                if (data.data && data.data.auto_login) {
                    setTimeout(() => {
                        window.location.href = data.data.redirect || '/dashboard';
                    }, 2000);
                } else {
                    setTimeout(() => {
                        window.location.href = '/auth/login?verified=1';
                    }, 2000);
                }
            } else {
                showMessage(data.message || 'Verification failed. Please try again.');
            }
        } catch (error) {
            showMessage('Network error. Please try again.');
        } finally {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }
    });

    // 从URL参数获取验证码（如果有）
    const urlParams = new URLSearchParams(window.location.search);
    const urlCode = urlParams.get('code');
    if (urlCode && urlCode.length === 6) {
        currentVerificationCode = urlCode;
        displayVerificationCode();
        console.log('Verification code loaded from URL:', urlCode);
    }

    // 检查localStorage中是否有从注册页面传递的验证码
    // 注册页面使用 'verification_code' 键名
    const registrationCode = localStorage.getItem('verification_code');
    if (registrationCode && registrationCode.length === 6 && !urlCode) {
        currentVerificationCode = registrationCode;
        displayVerificationCode();
        console.log('Verification code loaded from registration:', registrationCode);

        // 显示提示信息
        showMessage('Verification code displayed above. Please manually enter it below to activate your account.', 'success');
    }

    // 检查URL参数中的错误消息
    const errorMessage = urlParams.get('error');
    if (errorMessage) {
        showMessage(decodeURIComponent(errorMessage), 'error');
    }

    // 检查URL参数中的成功消息
    const successMessage = urlParams.get('success');
    if (successMessage) {
        showMessage(decodeURIComponent(successMessage), 'success');
    }
});
</script>

<?php include_once ROOT_PATH . '/templates/layout/footer.php'; ?>
