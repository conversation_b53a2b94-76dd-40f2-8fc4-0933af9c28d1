<?php
/**
 * User Prompt Generator Page
 * Create detailed prompts for tool development through conversation
 */

// 登录检查已在主文件中完成，这里直接使用用户信息
// $user 变量已在 index.php 中定义并可用

// 获取工具分类
try {
    $stmt = $pdo->prepare("SELECT name FROM pt_tool_category WHERE status = 'active' ORDER BY sort_order ASC, name ASC");
    $stmt->execute();
    $toolCategories = $stmt->fetchAll(PDO::FETCH_COLUMN);
} catch (Exception $e) {
    $toolCategories = [];
}
?>

<style>
/* 自定义样式优化 */
#chatMessages {
    max-height: calc(100vh - 200px);
    min-height: 500px;
}

#generatedPromptPanel {
    max-height: 100vh;
}

#generatedPromptText {
    max-height: calc(100vh - 200px);
    min-height: 400px;
}

/* 聊天面板过渡动画 */
#chatPanel {
    transition: width 0.3s ease-in-out;
}

/* 消息动画 */
.message-bubble {
    animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 消息气泡样式优化 */
.message-bubble {
    max-width: 85%;
    word-wrap: break-word;
}

/* 确保按钮样式不被覆盖 */
#copyPrompt, #downloadPrompt {
    background-color: #000000 !important;
    color: #ffffff !important;
}

#copyPrompt:hover, #downloadPrompt:hover {
    background-color: #1f2937 !important;
}

/* 滚动条样式 */
#chatMessages::-webkit-scrollbar,
#generatedPromptText::-webkit-scrollbar {
    width: 6px;
}

#chatMessages::-webkit-scrollbar-track,
#generatedPromptText::-webkit-scrollbar-track {
    background: #f1f1f1;
}

#chatMessages::-webkit-scrollbar-thumb,
#generatedPromptText::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

#chatMessages::-webkit-scrollbar-thumb:hover,
#generatedPromptText::-webkit-scrollbar-thumb:hover {
    background: #a1a1a1;
}
</style>

<!-- Prompt Generator Interface - Full Screen -->
<div class="fixed inset-0 bg-white z-40 flex flex-col" style="margin-left: 260px; top: 64px;">
    <!-- Header -->
    <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50 flex-shrink-0">
        <div class="flex justify-between items-center">
            <div>
                <h2 class="text-xl font-semibold text-gray-900 flex items-center">
                    <svg class="w-6 h-6 mr-3 text-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                    </svg>
                    AI Prompt Generator
                </h2>
                <p class="text-sm text-gray-600">Describe your tool idea and I'll help you create a detailed prompt</p>
            </div>
            <div class="flex items-center space-x-3">
                <button id="resetChat" class="px-4 py-2 text-sm bg-black text-white rounded-md hover:bg-gray-800 transition-colors">
                    Reset Chat
                </button>
                <div class="flex items-center text-sm text-gray-500">
                    <div class="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"></div>
                    Online
                </div>
            </div>
        </div>
    </div>

    <!-- Chat Messages Container -->
    <div class="flex-1 flex overflow-hidden">
        <!-- Chat Panel: Full Width Initially, Half Width When Prompt Generated -->
        <div id="chatPanel" class="w-full flex flex-col transition-all duration-300">
            <!-- Chat Messages -->
            <div id="chatMessages" class="flex-1 p-6 overflow-y-auto bg-gray-50">
                <!-- Welcome Message -->
                <div class="mb-6">
                    <div class="flex items-start space-x-3">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 ai-avatar text-white rounded-full flex items-center justify-center">
                                <span class="text-white font-bold text-xs">P2T</span>
                            </div>
                        </div>
                        <div class="flex-1">
                            <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200 mr-12">
                                <p class="text-gray-800">
                                    👋 I'm a tech-savvy product manager who turns ideas into development specs.
                                </p>
                                <p class="text-gray-600 text-sm mt-2">
                                    What tool would you like to build?
                                </p>
                            </div>
                            <div class="text-xs text-gray-500 mt-1">Prompt2Tool • Just now</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Chat Input -->
            <div class="px-6 py-4 border-t border-gray-200 bg-white flex-shrink-0">
                <div class="relative">
                    <textarea id="messageInput"
                              placeholder="Describe your tool idea... (e.g., 'I want to create a password generator with custom options')"
                              class="w-full px-4 py-3 pr-20 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent resize-none"
                              rows="2"></textarea>
                    <div class="absolute right-2 top-1/2 transform -translate-y-1/2 flex space-x-1">
                        <button id="cancelGeneration"
                                class="p-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors hidden">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                        <button id="sendMessage"
                                class="p-2 bg-black text-white rounded-md hover:bg-gray-800 transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                            </svg>
                        </button>
                    </div>
                </div>
                <div class="flex items-center justify-between mt-2">
                    <div class="text-xs text-gray-500">
                        Press Enter to send, Shift+Enter for new line
                    </div>
                    <div class="text-xs text-gray-500">
                        <span id="charCount">0</span>/2000 characters
                    </div>
                </div>
            </div>
        </div>

        <!-- Generated Prompt Panel (Right Side) -->
        <div id="generatedPromptPanel" class="w-1/2 border-l border-gray-200 bg-white flex flex-col hidden">
            <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-green-50 to-emerald-50 flex-shrink-0">
                <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                    <svg class="w-5 h-5 mr-2 text-black" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    Generated Prompt
                </h3>
                <p class="text-sm text-gray-600 mt-1">Your comprehensive tool development prompt</p>
            </div>
            <div class="flex-1 p-6 overflow-y-auto">
                <div class="bg-gray-50 border border-gray-200 rounded-lg p-4 h-full">
                    <pre id="generatedPromptText" class="whitespace-pre-wrap text-sm text-gray-800 font-mono h-full overflow-y-auto"></pre>
                </div>
            </div>
            <div class="px-6 py-4 border-t border-gray-200 bg-gray-50 flex-shrink-0">
                <div class="grid grid-cols-2 gap-3 mb-3">
                    <button id="copyPrompt" class="px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors text-sm font-medium">
                        Copy Prompt
                    </button>
                    <button id="downloadPrompt" class="px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors text-sm font-medium">
                        Download as MD
                    </button>
                </div>
                <button id="generateNew" class="w-full px-4 py-2 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors text-sm">
                    Generate New Prompt
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 自定义提示窗口组件 -->
<div id="customModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center">
    <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4 transform transition-all duration-300 scale-95" id="modalContent">
        <div class="p-6">
            <div class="flex items-center mb-4">
                <div id="modalIcon" class="w-8 h-8 rounded-full flex items-center justify-center mr-3">
                    <!-- Icon will be inserted here -->
                </div>
                <h3 id="modalTitle" class="text-lg font-semibold text-gray-900"></h3>
            </div>
            <p id="modalMessage" class="text-gray-600 mb-6"></p>
            <div class="flex justify-end space-x-3">
                <button id="modalCancel" class="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors hidden">
                    Cancel
                </button>
                <button id="modalConfirm" class="px-4 py-2 bg-black text-white rounded hover:bg-gray-800 transition-colors">
                    OK
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Loading Overlay -->
<div id="loadingOverlay" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white p-6 border border-gray-200">
        <div class="flex items-center space-x-3">
            <div class="animate-spin h-6 w-6 border-b-2 border-black"></div>
            <span class="text-gray-700">AI is thinking...</span>
        </div>
    </div>
</div>

<script>
// PHP数据传递到JavaScript
const toolCategories = <?= json_encode($toolCategories) ?>;

// Prompt Generator JavaScript
document.addEventListener('DOMContentLoaded', function() {
    const messageInput = document.getElementById('messageInput');
    const sendButton = document.getElementById('sendMessage');
    const chatMessages = document.getElementById('chatMessages');
    const resetButton = document.getElementById('resetChat');
    const cancelButton = document.getElementById('cancelGeneration');
    const charCount = document.getElementById('charCount');
    const loadingOverlay = document.getElementById('loadingOverlay');
    const generatedPromptPanel = document.getElementById('generatedPromptPanel');
    const generatedPromptText = document.getElementById('generatedPromptText');
    
    let conversationHistory = [];
    let isGenerating = false;
    let currentAbortController = null; // 用于取消AI请求

    // 自定义提示窗口函数
    // 使用示例:
    // showCustomAlert('Success', 'Operation completed successfully!', 'success');
    // showCustomAlert('Error', 'Something went wrong.', 'error');
    // showCustomAlert('Warning', 'Please check your input.', 'warning');
    // showCustomAlert('Info', 'This is an information message.', 'info');
    //
    // const confirmed = await showCustomConfirm('Confirm', 'Are you sure you want to proceed?');
    // if (confirmed) { /* user clicked confirm */ }
    function showCustomAlert(title, message, type = 'info') {
        return new Promise((resolve) => {
            const modal = document.getElementById('customModal');
            const modalTitle = document.getElementById('modalTitle');
            const modalMessage = document.getElementById('modalMessage');
            const modalIcon = document.getElementById('modalIcon');
            const modalConfirm = document.getElementById('modalConfirm');
            const modalCancel = document.getElementById('modalCancel');
            const modalContent = document.getElementById('modalContent');

            modalTitle.textContent = title;
            modalMessage.textContent = message;

            // 设置图标和颜色
            let iconHtml = '';
            let iconBg = '';
            switch (type) {
                case 'success':
                    iconHtml = '<svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path></svg>';
                    iconBg = 'bg-green-500';
                    break;
                case 'error':
                    iconHtml = '<svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>';
                    iconBg = 'bg-red-500';
                    break;
                case 'warning':
                    iconHtml = '<svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg>';
                    iconBg = 'bg-yellow-500';
                    break;
                default:
                    iconHtml = '<svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path></svg>';
                    iconBg = 'bg-blue-500';
            }

            modalIcon.innerHTML = iconHtml;
            modalIcon.className = `w-8 h-8 rounded-full flex items-center justify-center mr-3 ${iconBg}`;
            modalCancel.classList.add('hidden');

            modal.classList.remove('hidden');
            setTimeout(() => {
                modalContent.classList.remove('scale-95');
                modalContent.classList.add('scale-100');
            }, 10);

            const confirmHandler = () => {
                modalContent.classList.remove('scale-100');
                modalContent.classList.add('scale-95');
                setTimeout(() => modal.classList.add('hidden'), 300);
                modalConfirm.removeEventListener('click', confirmHandler);
                resolve(true);
            };

            modalConfirm.addEventListener('click', confirmHandler);
        });
    }

    function showCustomConfirm(title, message) {
        return new Promise((resolve) => {
            const modal = document.getElementById('customModal');
            const modalTitle = document.getElementById('modalTitle');
            const modalMessage = document.getElementById('modalMessage');
            const modalIcon = document.getElementById('modalIcon');
            const modalConfirm = document.getElementById('modalConfirm');
            const modalCancel = document.getElementById('modalCancel');
            const modalContent = document.getElementById('modalContent');

            modalTitle.textContent = title;
            modalMessage.textContent = message;
            modalIcon.innerHTML = '<svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"></path></svg>';
            modalIcon.className = 'w-8 h-8 rounded-full flex items-center justify-center mr-3 bg-blue-500';
            modalCancel.classList.remove('hidden');
            modalConfirm.textContent = 'Confirm';

            modal.classList.remove('hidden');
            setTimeout(() => {
                modalContent.classList.remove('scale-95');
                modalContent.classList.add('scale-100');
            }, 10);

            const hideModal = () => {
                modalContent.classList.remove('scale-100');
                modalContent.classList.add('scale-95');
                setTimeout(() => {
                    modal.classList.add('hidden');
                    modalConfirm.textContent = 'OK';
                }, 300);
            };

            const confirmHandler = () => {
                hideModal();
                modalConfirm.removeEventListener('click', confirmHandler);
                modalCancel.removeEventListener('click', cancelHandler);
                resolve(true);
            };

            const cancelHandler = () => {
                hideModal();
                modalConfirm.removeEventListener('click', confirmHandler);
                modalCancel.removeEventListener('click', cancelHandler);
                resolve(false);
            };

            modalConfirm.addEventListener('click', confirmHandler);
            modalCancel.addEventListener('click', cancelHandler);
        });
    }

    // Character count
    messageInput.addEventListener('input', function() {
        const count = this.value.length;
        charCount.textContent = count;
        charCount.className = count > 2000 ? 'text-red-500' : 'text-gray-500';
    });

    // Send message on Enter (but not Shift+Enter)
    messageInput.addEventListener('keydown', function(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            sendMessage();
        }
    });

    // Send button click
    sendButton.addEventListener('click', sendMessage);

    // Cancel generation button
    cancelButton.addEventListener('click', function() {
        if (isGenerating) {
            cancelAIGeneration();
            showCustomAlert('Info', 'AI generation cancelled!', 'info');
        }
    });

    // Reset chat
    resetButton.addEventListener('click', async function() {
        // 如果正在生成，先询问是否取消
        if (isGenerating) {
            const confirmed = await showCustomConfirm('Cancel Generation', 'AI is currently generating. Do you want to cancel and reset the chat?');
            if (confirmed) {
                resetChat();
            }
        } else {
            const confirmed = await showCustomConfirm('Reset Chat', 'Are you sure you want to reset the chat? This will clear all conversation history.');
            if (confirmed) {
                resetChat();
            }
        }
    });

    function sendMessage() {
        const message = messageInput.value.trim();
        if (!message || isGenerating || message.length > 2000) return;

        // Add user message to chat
        addMessageToChat('user', message);

        // Clear input
        messageInput.value = '';
        charCount.textContent = '0';

        // Add to conversation history
        conversationHistory.push({
            role: 'user',
            content: message
        });

        // Send to AI
        generateAIResponse();
    }

    function addMessageToChat(sender, message, isTyping = false) {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'mb-6';

        const isUser = sender === 'user';
        const avatarBg = isUser ? 'bg-gray-600' : 'bg-black';
        const messageBg = isUser ? 'bg-gray-50 border-gray-200' : 'bg-white border-gray-200';
        const alignment = isUser ? 'flex-row-reverse' : '';
        const avatarClass = 'rounded-full';

        messageDiv.innerHTML = `
            <div class="flex items-start space-x-3 ${alignment}">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 ${avatarBg} text-white ${avatarClass} flex items-center justify-center">
                        ${isUser ?
                            '<svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path></svg>' :
                            '<svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path></svg>'
                        }
                    </div>
                </div>
                <div class="flex-1 ${isUser ? 'text-right' : ''}">
                    <div class="message-bubble p-4 border ${messageBg} ${isUser ? 'ml-12' : 'mr-12'} rounded-lg shadow-sm">
                        <p class="message-content text-gray-800 whitespace-pre-wrap">${message}</p>
                    </div>
                    <div class="text-xs text-gray-500 mt-1">${isUser ? 'You' : 'Prompt2Tool'} • Just now</div>
                </div>
            </div>
        `;

        chatMessages.appendChild(messageDiv);
        chatMessages.scrollTop = chatMessages.scrollHeight;

        return messageDiv;
    }

    function addMessageToChat(sender, message, isTyping = false) {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'mb-6';

        const isUser = sender === 'user';
        const avatarBg = isUser ? 'bg-gray-600' : 'bg-black';
        const messageBg = isUser ? 'bg-gray-50 border-gray-200' : 'bg-white border-gray-200';
        const alignment = isUser ? 'flex-row-reverse' : '';
        const avatarClass = 'rounded-full';

        messageDiv.innerHTML = `
            <div class="flex items-start space-x-3 ${alignment}">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 ${avatarBg} text-white ${avatarClass} flex items-center justify-center">
                        ${isUser ?
                            '<svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path></svg>' :
                            '<svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 002 2v10a2 2 0 002 2z"></path></svg>'
                        }
                    </div>
                </div>
                <div class="flex-1 ${isUser ? 'text-right' : ''}">
                    <div class="message-bubble p-4 border ${messageBg} ${isUser ? 'ml-12' : 'mr-12'} rounded-lg shadow-sm">
                        <p class="message-content text-gray-800 whitespace-pre-wrap">${message}</p>
                    </div>
                    <div class="text-xs text-gray-500 mt-1">${isUser ? 'You' : 'Prompt2Tool'} • Just now</div>
                </div>
            </div>
        `;

        chatMessages.appendChild(messageDiv);
        chatMessages.scrollTop = chatMessages.scrollHeight;

        return messageDiv;
    }



    async function generateAIResponse() {
        isGenerating = true;
        sendButton.disabled = true;

        // 显示取消按钮
        cancelButton.classList.remove('hidden');

        // Add typing indicator
        const typingMessage = addMessageToChat('ai', '', true);
        const messageContent = typingMessage.querySelector('.message-content');

        // 显示配额检查动画
        messageContent.innerHTML = `
            <div class="flex items-center space-x-2">
                <div class="flex space-x-1">
                    <div class="w-2 h-2 bg-blue-500 rounded-full animate-bounce"></div>
                    <div class="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
                    <div class="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
                </div>
                <span class="text-gray-600">Checking quota...</span>
            </div>
        `;

        try {
            // 检查配额是否足够
            const quotaCheck = await fetch('../../api/check-quota.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    action: 'chat_interaction'
                })
            });

            const quotaResult = await quotaCheck.json();
            if (!quotaResult.success) {
                throw new Error(quotaResult.message || 'Quota check failed');
            }

            if (!quotaResult.has_enough_quota) {
                throw new Error(quotaResult.message || 'Insufficient quota for chat interaction');
            }

            // 配额检查通过，显示思考动画
            messageContent.innerHTML = `
                <div class="flex items-center space-x-2">
                    <div class="flex space-x-1">
                        <div class="w-2 h-2 bg-black rounded-full animate-bounce"></div>
                        <div class="w-2 h-2 bg-black rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
                        <div class="w-2 h-2 bg-black rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
                    </div>
                    <span class="text-gray-600">AI is thinking...</span>
                </div>
            `;

        } catch (quotaError) {
            // 配额检查失败，显示错误并退出
            messageContent.innerHTML = `
                <div class="flex items-center space-x-2 text-red-600">
                    <span>🚫</span>
                    <span>${quotaError.message}</span>
                </div>
            `;

            isGenerating = false;
            sendButton.disabled = false;
            cancelButton.classList.add('hidden');
            return;
        }

        try {
            // 获取当前时间信息
            const currentTime = new Date();
            const timeInfo = `Current time: ${currentTime.toLocaleString('zh-CN', {
                timeZone: 'Asia/Shanghai',
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                weekday: 'long'
            })}`;



            // 系统化需求收集流程
            let systemPrompt = '';
            const step = Math.floor(conversationHistory.length / 2) + 1;

            // 调试信息
            console.log(`Conversation length: ${conversationHistory.length}, Step: ${step}`);

            // 基础系统提示
            const baseSystemPrompt = `You are a professional requirements analyst for web tool development. Your role is to collect requirements and generate development specifications, NOT to write code. ${timeInfo}

CRITICAL ROLE DEFINITION:
- You are in the REQUIREMENTS GATHERING phase
- Your job is to collect user needs and create development prompts
- You do NOT generate HTML, CSS, or JavaScript code
- You do NOT create actual tools - you create specifications for tools
- After requirements are complete, you generate a development prompt for another AI to use

TARGET PLATFORM CONTEXT:
- Tools will be built using PHP+HTML templates with predefined styling
- Focus on functional requirements, not technical implementation
- Follow MVP (Minimum Viable Product) principles - focus on core functionality only
- Collect enough detail for another AI to implement the tool

COMMUNICATION RULES:
- Ask only ONE specific question per response
- Keep responses under 30 words when possible
- No markdown symbols (* # etc.)
- Be concise and focused
- NEVER generate code or offer to create HTML files
- Use current time context when relevant`;

            if (conversationHistory.length === 0) {
                systemPrompt = `${baseSystemPrompt}

STEP 1: TOOL IDENTIFICATION
Ask what type of tool they want to build. Provide 3-4 brief examples.

GREETING TEMPLATE:
"👋 I'm a tech-savvy product manager who turns ideas into development specs.

What tool would you like to build? Here are some popular options:
- Text processors (formatters, converters, analyzers)
- Calculators (basic, scientific, financial, unit converters)
- Generators (passwords, QR codes, color palettes)
- Utilities (image tools, data validators, productivity helpers)"

RULES:
- Use the exact greeting template above
- Keep the examples section concise
- Be encouraging and professional`;

            } else if (step === 2) {
                systemPrompt = `${baseSystemPrompt}

STEP 2: TOOL NAMING & BRANDING
Based on their tool idea, help them create an appropriate English name for their tool. Generate 3-4 creative, professional English name suggestions that:
- Reflect the tool's purpose and functionality
- Use relevant keywords from their requirements
- Are memorable and brandable
- Avoid generic terms like "Tool" or "Generator"

Ask them to choose one or suggest modifications.`;

            } else if (step === 3) {
                systemPrompt = `${baseSystemPrompt}

STEP 3: MVP FUNCTIONALITY ANALYSIS
Based on the tool type they mentioned, analyze and propose the core MVP functionality.

ANALYSIS APPROACH:
1. Identify the tool category from their previous response
2. Determine the essential MVP features for that tool type
3. Present 3-4 core functions that make this tool useful
4. Ask for confirmation or modifications

TOOL-SPECIFIC MVP EXAMPLES:
- Calculator: basic operations (+, -, ×, ÷), clear, decimal support
- Text Generator: input parameters, generate button, copy result, basic customization
- Converter: input field, conversion type selector, output display, swap function
- Tracker: add item, view list, mark complete, delete item

Present the MVP features and ask: "Are these the core features you want, or would you like to modify anything?"

Focus on 3-4 essential features only - no advanced functionality.`;

            } else if (step === 4) {
                systemPrompt = `${baseSystemPrompt}

STEP 4: FUNCTIONALITY VALIDATION
Review the conversation history and validate what has been collected:

VALIDATION PROCESS:
1. Check if tool type is clearly defined from user's first response
2. Check if tool name has been suggested and confirmed
3. Check if MVP features have been analyzed and presented
4. Check if user has responded to the MVP feature questions

CRITICAL: Only proceed to completion if ALL previous steps are genuinely complete.

If ANY step is missing:
- Continue with the missing step (tool naming, MVP analysis, etc.)
- Do NOT call showRequirementsComplete()

If ALL steps are complete:
- Summarize: "Perfect! Let me summarize what we've defined: [Tool Name] - [Core Features]"
- State: "All necessary information has been gathered. Ready to generate your development prompt."
- Call showRequirementsComplete()

CRITICAL: Do NOT generate any code, HTML, or offer to create files. Your role is requirements gathering only.

Be honest about what information is actually missing.`;

            } else {
                // 步骤超出预期，检查是否应该完成需求收集
                console.log(`Step ${step} reached, conversation length: ${conversationHistory.length}`);
                systemPrompt = `${baseSystemPrompt}

FINAL VALIDATION (Step ${step})
Review the entire conversation history and determine if requirements collection is complete:

VALIDATION CHECKLIST:
1. ✓ Tool type identified from user's first message
2. ✓ Tool name suggested and confirmed by user
3. ✓ MVP features analyzed and presented
4. ✓ User confirmed or modified the features
5. ✓ Any additional features discussed

If ALL requirements are complete:
- Summarize: "Perfect! Let me summarize what we've defined: [Tool Name] - [Core Features and Optional Features]"
- State: "All necessary information has been gathered. Ready to generate your development prompt."

If anything is still missing:
- Continue with the missing step
- Ask specific clarifying questions

CRITICAL: Do NOT generate code or development prompts. Your role is requirements gathering only.`;
            }

            // 准备消息历史
            const messages = [
                {
                    role: 'system',
                    content: systemPrompt
                },
                ...conversationHistory
            ];

            // 创建新的AbortController
            currentAbortController = new AbortController();

            // 调用AI API
            const response = await fetch('../../api/user-chat.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                signal: currentAbortController.signal, // 添加取消信号
                body: JSON.stringify({
                    messages: messages,
                    max_tokens: 1000,
                    temperature: 0.7,
                    stream: true
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            // 清除思考动画，开始流式输出
            messageContent.innerHTML = '';
            let fullResponse = '';

            // 处理流式响应
            const reader = response.body.getReader();
            const decoder = new TextDecoder();

            try {
                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;

                    const chunk = decoder.decode(value);
                    const lines = chunk.split('\n');

                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            const data = line.slice(6);
                            if (data === '[DONE]') {
                                // 流式输出完成，添加AI回复到对话历史
                                const cleanResponse = fullResponse.replace(/[*#]/g, '');
                                conversationHistory.push({
                                    role: 'assistant',
                                    content: cleanResponse
                                });

                                // 消耗配额
                                try {
                                    await fetch('../../api/consume-quota.php', {
                                        method: 'POST',
                                        headers: {
                                            'Content-Type': 'application/json'
                                        },
                                        body: JSON.stringify({
                                            action: 'chat_interaction',
                                            description: 'AI Chat Interaction'
                                        })
                                    });
                                } catch (error) {
                                    console.error('Failed to consume quota:', error);
                                }

                                // 检查需求收集是否完成（基于内容而不是轮数）
                                if (isRequirementsComplete()) {
                                    showRequirementsComplete();
                                }
                                return;
                            }

                            try {
                                const parsed = JSON.parse(data);

                                // 处理错误响应
                                if (parsed.type === 'error') {
                                    messageContent.textContent = `Error: ${parsed.message}`;
                                    return;
                                }

                                // 处理aiHubMix原始格式
                                let content = null;
                                if (parsed.choices && parsed.choices[0] && parsed.choices[0].delta && parsed.choices[0].delta.content) {
                                    content = parsed.choices[0].delta.content;
                                } else if (parsed.content) {
                                    // 兼容旧格式
                                    content = parsed.content;
                                }

                                if (content) {
                                    fullResponse += content;
                                    // 过滤markdown符号
                                    const cleanContent = fullResponse.replace(/[*#]/g, '');
                                    messageContent.textContent = cleanContent;
                                    // 滚动到底部
                                    chatMessages.scrollTop = chatMessages.scrollHeight;
                                }
                            } catch (e) {
                                // 忽略解析错误
                                console.log('Parse error:', e, 'Data:', data);
                            }
                        }
                    }
                }
            } catch (error) {
                messageContent.textContent = 'Stream processing error occurred.';
                console.error('Stream error:', error);
            }

        } catch (error) {
            // Remove typing indicator
            typingMessage.remove();

            // 检查是否是用户取消操作
            if (error.name === 'AbortError') {
                console.log('AI generation was cancelled by user');
                // 不显示错误消息，因为取消是用户主动操作
                return;
            }

            console.error('Network Error:', error);
            addMessageToChat('ai', `Connection error: ${error.message}. Please check your internet connection and try again.`);
        } finally {
            isGenerating = false;
            sendButton.disabled = false;
            loadingOverlay.classList.add('hidden');

            // 隐藏取消按钮
            cancelButton.classList.add('hidden');

            // 清理AbortController
            currentAbortController = null;
        }
    }



    function isRequirementsComplete() {
        // 检查需求收集是否完成（基于内容分析）
        const userMessages = conversationHistory.filter(msg => msg.role === 'user');
        const aiMessages = conversationHistory.filter(msg => msg.role === 'assistant');

        // 至少需要4轮对话：工具类型、工具名称、MVP功能、确认
        if (userMessages.length < 4 || aiMessages.length < 4) {
            return false;
        }

        // 检查是否有工具类型识别（第一个用户消息应该描述工具）
        const hasToolType = userMessages.length > 0 &&
            userMessages[0].content.trim().length > 10;

        // 检查是否有工具名称确认（用户消息中应该包含名称选择）
        const hasToolNameConfirmation = userMessages.some(msg =>
            msg.content.toLowerCase().includes('prefer') ||
            msg.content.toLowerCase().includes('choose') ||
            msg.content.toLowerCase().includes('like') ||
            msg.content.toLowerCase().includes('好的') ||
            msg.content.toLowerCase().includes('选择')
        );

        // 检查是否有MVP功能讨论（AI消息中应该包含功能相关内容）
        const hasMVPDiscussion = aiMessages.some(msg =>
            msg.content.toLowerCase().includes('feature') ||
            msg.content.toLowerCase().includes('function') ||
            msg.content.toLowerCase().includes('core') ||
            msg.content.toLowerCase().includes('mvp') ||
            msg.content.toLowerCase().includes('功能')
        );

        // 检查用户是否确认了功能（用户消息中应该包含确认词汇）
        const hasUserFeatureConfirmation = userMessages.some(msg =>
            msg.content.toLowerCase().includes('yes') ||
            msg.content.toLowerCase().includes('correct') ||
            msg.content.toLowerCase().includes('good') ||
            msg.content.toLowerCase().includes('perfect') ||
            msg.content.toLowerCase().includes('ready') ||
            msg.content.toLowerCase().includes('proceed') ||
            msg.content.toLowerCase().includes('是的') ||
            msg.content.toLowerCase().includes('好的') ||
            msg.content.toLowerCase().includes('可以')
        );

        // 检查AI是否确认了需求完整性（最后一个AI回复包含完成标识）
        const lastAIMessage = aiMessages[aiMessages.length - 1];
        const hasCompletionConfirmation = lastAIMessage &&
            (lastAIMessage.content.includes('All necessary information has been gathered') ||
             lastAIMessage.content.includes('Ready to generate your development prompt') ||
             lastAIMessage.content.includes('Perfect! Let me summarize') ||
             lastAIMessage.content.includes('summarize the requirements') ||
             lastAIMessage.content.includes('ready for me to generate'));

        console.log('Requirements check:', {
            hasToolType,
            hasToolNameConfirmation,
            hasMVPDiscussion,
            hasUserFeatureConfirmation,
            hasCompletionConfirmation,
            userCount: userMessages.length,
            aiCount: aiMessages.length
        });

        return hasToolType && hasToolNameConfirmation && hasMVPDiscussion &&
               hasUserFeatureConfirmation && hasCompletionConfirmation;
    }

    function showRequirementsComplete() {
        // 检查是否已经存在生成按钮，避免重复添加
        const existingButton = document.getElementById('generateFinalPrompt');
        if (existingButton) {
            return; // 如果按钮已存在，直接返回
        }

        // 在聊天区域添加一个生成prompt的按钮
        const generateButtonDiv = document.createElement('div');
        generateButtonDiv.className = 'mb-6 text-center';
        generateButtonDiv.id = 'generateButtonContainer'; // 添加容器ID便于管理
        generateButtonDiv.innerHTML = `
            <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
                <h3 class="text-lg font-semibold text-green-800 mb-2">Requirements Collection Complete!</h3>
                <p class="text-green-700 mb-4">All necessary information has been gathered. Ready to generate your development prompt.</p>
                <button id="generateFinalPrompt" class="px-6 py-3 bg-black text-white rounded-lg hover:bg-gray-800 transition-colors">
                    Generate Development Prompt
                </button>
            </div>
        `;

        chatMessages.appendChild(generateButtonDiv);
        chatMessages.scrollTop = chatMessages.scrollHeight;

        // 添加事件监听器
        document.getElementById('generateFinalPrompt').addEventListener('click', confirmAndGeneratePrompt);
    }

    async function confirmAndGeneratePrompt() {
        // 显示确认对话框
        const confirmed = await showCustomConfirm(
            'Generate Development Prompt',
            'Are you ready to generate the final development prompt based on the collected requirements?'
        );

        if (confirmed) {
            generateFinalPrompt();
        }
    }

    async function generateFinalPrompt() {
        // 关闭聊天输入框
        const chatInputContainer = document.querySelector('.chat-input-container');
        if (chatInputContainer) {
            chatInputContainer.style.display = 'none';
        }

        // 切换到分屏模式
        const chatPanel = document.getElementById('chatPanel');
        chatPanel.classList.remove('w-full');
        chatPanel.classList.add('w-1/2');

        // 显示右侧面板
        generatedPromptPanel.classList.remove('hidden');

        // 禁用Copy和Download按钮，显示生成中状态
        const copyButton = document.getElementById('copyPrompt');
        const downloadButton = document.getElementById('downloadPrompt');
        copyButton.disabled = true;
        downloadButton.disabled = true;
        copyButton.classList.add('opacity-50', 'cursor-not-allowed');
        downloadButton.classList.add('opacity-50', 'cursor-not-allowed');
        copyButton.textContent = 'Generating...';
        downloadButton.textContent = 'Generating...';

        // 显示生成中的状态
        generatedPromptText.innerHTML = `
            <div class="flex items-center space-x-2 text-gray-600">
                <div class="flex space-x-1">
                    <div class="w-2 h-2 bg-black rounded-full animate-bounce"></div>
                    <div class="w-2 h-2 bg-black rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
                    <div class="w-2 h-2 bg-black rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
                </div>
                <span>Analyzing requirements and generating development prompt...</span>
            </div>
        `;

        try {
            // 检查配额是否足够
            const quotaCheck = await fetch('../../api/check-quota.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    action: 'chat_interaction'
                })
            });

            const quotaResult = await quotaCheck.json();
            if (!quotaResult.success) {
                throw new Error(quotaResult.message || 'Quota check failed');
            }

            if (!quotaResult.has_enough_quota) {
                throw new Error(quotaResult.message);
            }

            // 获取当前时间信息
            const currentTime = new Date();
            const timeInfo = `Current time: ${currentTime.toLocaleString('zh-CN', {
                timeZone: 'Asia/Shanghai',
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                weekday: 'long'
            })}`;

            // 提取和结构化需求
            const requirements = extractRequirements();

            // 创建新的AbortController用于生成prompt
            currentAbortController = new AbortController();

            // 调用AI生成专业的开发提示词（流式输出）
            const promptResponse = await fetch('../../api/user-chat.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                signal: currentAbortController.signal, // 添加取消信号
                body: JSON.stringify({
                    messages: [
                        {
                            role: 'system',
                            content: `You are a professional requirements analyst creating functional specifications for web tools. ${timeInfo}

ROLE DEFINITION:
- You create pure functional specifications, not technical implementation guides
- Focus on business requirements, user needs, and functional workflows
- Specify WHAT the tool should do, not HOW to implement it
- Create specifications that any developer can understand and implement

SPECIFICATION REQUIREMENTS:
- Create concise, focused specifications (60-100 lines)
- Focus on user experience and functional requirements
- Avoid technical implementation details
- Provide clear, actionable requirements

FOCUS AREAS:
1. Business requirements and user value proposition
2. Functional specifications and feature descriptions
3. User interaction workflows and processes
4. Interface layout and user experience requirements
5. Data processing logic and validation rules
6. Success criteria and acceptance standards

AVOID INCLUDING:
- Technical implementation details (PHP, HTML, JavaScript)
- Code generation instructions or templates
- Styling requirements or color schemes
- Platform-specific technical constraints
- Development methodologies or frameworks

SPECIFICATION STRUCTURE:
1. TOOL OVERVIEW (Purpose, target users, value proposition)
2. CORE FUNCTIONALITY (Features and capabilities)
3. USER WORKFLOW (Step-by-step user interactions)
4. INTERFACE REQUIREMENTS (Layout and user experience)
5. PROCESSING LOGIC (Business rules and validation)
6. ACCEPTANCE CRITERIA (Success metrics and requirements)

Create a clear, focused functional specification that describes what the tool should accomplish for users. Format as plain text without markdown.`
                        },
                        {
                            role: 'user',
                            content: `Based on the following user requirements, create a focused functional specification. Transform the requirements into clear, actionable specifications:

User Requirements Summary:
${requirements}

Create a functional specification with these sections:

TOOL INFORMATION:
- Tool Name: [Specify the exact tool name]
- Slug: [URL-friendly version of tool name, lowercase with hyphens]
- Category: [Choose from: ${toolCategories.join(', ')}]
- Description: [One-sentence description of what the tool does]
- Tags: [3 relevant tags separated by commas]

1. TOOL OVERVIEW
   - Purpose: [Detailed purpose description starting with the tool name]
   - Target users and main use cases
   - Key value proposition

2. CORE FUNCTIONALITY
   - Main features and capabilities
   - User actions and expected outcomes
   - Input types and validation needs
   - Output formats and content

3. USER WORKFLOW
   - Step-by-step user process
   - Data entry and interaction flow
   - Processing steps and logic
   - Result presentation

4. INTERFACE REQUIREMENTS
   - Input areas and form elements needed
   - Output display requirements
   - Essential buttons and controls
   - User feedback and status indicators

5. PROCESSING LOGIC
   - Data validation rules
   - Core calculations or transformations
   - Error handling requirements
   - Performance expectations

6. ACCEPTANCE CRITERIA
   - Must-have functional requirements
   - User experience standards
   - Quality and performance benchmarks

Focus on what the tool should do for users, not how to implement it technically. Keep it concise and actionable.`
                        }
                    ],
                    max_tokens: 1000,
                    temperature: 0.3,
                    stream: true
                })
            });

            if (!promptResponse.ok) {
                throw new Error(`HTTP error! status: ${promptResponse.status}`);
            }

            // 清除加载动画，开始流式输出
            generatedPromptText.innerHTML = '';
            let fullResponse = '';

            // 处理流式响应
            const reader = promptResponse.body.getReader();
            const decoder = new TextDecoder();

            try {
                while (true) {
                    const { done, value } = await reader.read();
                    if (done) break;

                    const chunk = decoder.decode(value);
                    const lines = chunk.split('\n');

                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            const data = line.slice(6);
                            if (data === '[DONE]') {
                                // 流式输出完成
                                generatedPromptText.textContent = fullResponse;

                                // 恢复Copy和Download按钮状态
                                const copyButton = document.getElementById('copyPrompt');
                                const downloadButton = document.getElementById('downloadPrompt');
                                copyButton.disabled = false;
                                downloadButton.disabled = false;
                                copyButton.classList.remove('opacity-50', 'cursor-not-allowed');
                                downloadButton.classList.remove('opacity-50', 'cursor-not-allowed');
                                copyButton.textContent = 'Copy Prompt';
                                downloadButton.textContent = 'Download as MD';

                                // 显示完成提示
                                showCustomAlert('Success', 'Development prompt generated successfully!', 'success');

                                // 消耗配额
                                try {
                                    await fetch('../../api/consume-quota.php', {
                                        method: 'POST',
                                        headers: {
                                            'Content-Type': 'application/json'
                                        },
                                        body: JSON.stringify({
                                            action: 'chat_interaction',
                                            description: 'AI Prompt Generation'
                                        })
                                    });
                                } catch (error) {
                                    console.error('Failed to consume quota:', error);
                                }

                                return;
                            }

                            try {
                                const parsed = JSON.parse(data);
                                if (parsed.type === 'error') {
                                    throw new Error(parsed.message);
                                }

                                // 处理aiHubMix原始格式
                                let content = null;
                                if (parsed.choices && parsed.choices[0] && parsed.choices[0].delta && parsed.choices[0].delta.content) {
                                    content = parsed.choices[0].delta.content;
                                } else if (parsed.content) {
                                    // 兼容旧格式
                                    content = parsed.content;
                                }

                                if (content) {
                                    fullResponse += content;
                                    generatedPromptText.textContent = fullResponse;
                                    // 滚动到底部
                                    generatedPromptText.scrollTop = generatedPromptText.scrollHeight;
                                }
                            } catch (e) {
                                // 忽略解析错误
                            }
                        }
                    }
                }
            } catch (streamError) {
                console.error('Stream error:', streamError);
                throw streamError;
            }

        } catch (error) {
            // 检查是否是用户取消操作
            if (error.name === 'AbortError') {
                console.log('Prompt generation was cancelled by user');
                return;
            }

            console.error('Error generating prompt:', error);

            // 恢复Copy和Download按钮状态（错误情况下）
            const copyButton = document.getElementById('copyPrompt');
            const downloadButton = document.getElementById('downloadPrompt');
            copyButton.disabled = false;
            downloadButton.disabled = false;
            copyButton.classList.remove('opacity-50', 'cursor-not-allowed');
            downloadButton.classList.remove('opacity-50', 'cursor-not-allowed');
            copyButton.textContent = 'Copy Prompt';
            downloadButton.textContent = 'Download as MD';

            // 显示错误信息，不使用fallback模板
            generatedPromptText.innerHTML = `
                <div class="text-red-600 p-4 border border-red-200 rounded-lg bg-red-50">
                    <h3 class="font-semibold mb-2">Generation Failed</h3>
                    <p class="mb-3">Unable to generate the development prompt due to a connection issue.</p>
                    <button onclick="generateFinalPrompt()" class="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700">
                        Try Again
                    </button>
                </div>
            `;

            showCustomAlert('Error', 'Failed to generate prompt. Please try again.', 'error');
        } finally {
            // 清理AbortController
            currentAbortController = null;
        }
    }

    function extractRequirements() {
        // 从对话历史中提取结构化需求
        const userMessages = conversationHistory.filter(msg => msg.role === 'user');
        const aiMessages = conversationHistory.filter(msg => msg.role === 'assistant');

        let requirements = '';

        // 工具类型和基本功能
        if (userMessages.length > 0) {
            requirements += `TOOL TYPE: ${userMessages[0].content}\n\n`;
        }

        // 工具名称
        if (userMessages.length > 1) {
            requirements += `TOOL NAME: ${userMessages[1].content}\n\n`;
        }

        // 功能需求
        if (userMessages.length > 2) {
            requirements += `FUNCTIONAL REQUIREMENTS:\n${userMessages[2].content}\n\n`;
        }

        // 用户界面需求
        if (userMessages.length > 3) {
            requirements += `USER INTERFACE REQUIREMENTS:\n${userMessages[3].content}\n\n`;
        }

        // 技术需求
        if (userMessages.length > 4) {
            requirements += `TECHNICAL SPECIFICATIONS:\n${userMessages[4].content}\n\n`;
        }

        // 附加功能
        if (userMessages.length > 5) {
            requirements += `ADDITIONAL FEATURES:\n${userMessages[5].content}\n\n`;
        }

        return requirements;
    }



    // Cancel current AI generation
    function cancelAIGeneration() {
        if (currentAbortController) {
            currentAbortController.abort();
            currentAbortController = null;
        }

        if (isGenerating) {
            isGenerating = false;
            sendButton.disabled = false;

            // 隐藏取消按钮
            cancelButton.classList.add('hidden');

            // 移除正在输入的消息
            const typingMessages = chatMessages.querySelectorAll('.typing-indicator');
            typingMessages.forEach(msg => msg.remove());

            // 添加取消消息
            addMessageToChat('ai', 'Generation cancelled. You can continue our conversation or ask me something else.');
        }
    }

    function resetChat() {
        // 先取消任何正在进行的AI生成
        cancelAIGeneration();
        conversationHistory = [];

        // 重置到全屏聊天模式
        const chatPanel = document.getElementById('chatPanel');
        chatPanel.classList.remove('w-1/2');
        chatPanel.classList.add('w-full');

        // 隐藏右侧面板
        generatedPromptPanel.classList.add('hidden');

        // 重置聊天内容
        chatMessages.innerHTML = `
            <div class="mb-6">
                <div class="flex items-start space-x-3">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-black text-white rounded-full flex items-center justify-center">
                            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="flex-1">
                        <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
                            <p class="text-gray-800">
                                👋 I'm a tech-savvy product manager who turns ideas into development specs.
                            </p>
                            <p class="text-gray-600 text-sm mt-2">
                                What tool would you like to build?
                            </p>
                        </div>
                        <div class="text-xs text-gray-500 mt-1">Prompt2Tool • Just now</div>
                    </div>
                </div>
            </div>
        `;

        // 清空输入框
        messageInput.value = '';
        charCount.textContent = '0';
    }

    // 移除自动取消逻辑 - 只允许用户手动取消

    // Copy prompt functionality
    document.getElementById('copyPrompt')?.addEventListener('click', async function() {
        try {
            await navigator.clipboard.writeText(generatedPromptText.textContent);
            const originalText = this.textContent;
            this.textContent = 'Copied!';
            this.classList.add('bg-green-600');
            this.classList.remove('bg-black');
            setTimeout(() => {
                this.textContent = originalText;
                this.classList.remove('bg-green-600');
                this.classList.add('bg-black');
            }, 2000);
        } catch (err) {
            showCustomAlert('Error', 'Failed to copy to clipboard', 'error');
        }
    });

    // Download prompt functionality
    document.getElementById('downloadPrompt')?.addEventListener('click', function() {
        const blob = new Blob([generatedPromptText.textContent], { type: 'text/markdown' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;

        // 生成智能文件名
        const fileName = generateSmartFileName();
        a.download = fileName;

        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        showCustomAlert('Success', 'Prompt downloaded as Markdown file!', 'success');
    });

    // Generate new prompt functionality
    document.getElementById('generateNew')?.addEventListener('click', function() {
        resetChat();
        showCustomAlert('Info', 'Chat reset! You can now describe a new tool idea.', 'info');
    });

    // 生成智能文件名
    function generateSmartFileName() {
        let toolName = '';

        // 尝试从对话历史中提取工具名称
        if (conversationHistory.length > 1) {
            // 第二个用户消息通常是工具名称
            const nameMessage = conversationHistory.find((msg, index) =>
                msg.role === 'user' && index > 0 && msg.content.length < 50
            );

            if (nameMessage) {
                toolName = nameMessage.content.trim();
            }
        }

        // 如果没有找到合适的工具名，尝试从第一个消息中提取
        if (!toolName && conversationHistory.length > 0) {
            const firstMessage = conversationHistory[0].content.toLowerCase();

            // 常见工具类型关键词
            const toolTypes = {
                'calculator': 'calculator',
                'converter': 'converter',
                'generator': 'generator',
                'formatter': 'formatter',
                'validator': 'validator',
                'analyzer': 'analyzer',
                'editor': 'editor',
                'viewer': 'viewer',
                'parser': 'parser',
                'encoder': 'encoder',
                'decoder': 'decoder',
                'compressor': 'compressor',
                'minifier': 'minifier',
                'beautifier': 'beautifier'
            };

            for (const [keyword, type] of Object.entries(toolTypes)) {
                if (firstMessage.includes(keyword)) {
                    toolName = type;
                    break;
                }
            }
        }

        // 清理和格式化工具名
        if (toolName) {
            toolName = toolName
                .toLowerCase()
                .replace(/[^a-z0-9\s-]/g, '') // 移除特殊字符
                .replace(/\s+/g, '-') // 空格替换为连字符
                .replace(/-+/g, '-') // 多个连字符合并为一个
                .replace(/^-|-$/g, '') // 移除开头和结尾的连字符
                .substring(0, 20); // 限制长度

            return `${toolName}-prompt.md`;
        }

        // 如果没有工具名，使用简短的默认名称
        const timestamp = Date.now().toString().slice(-6); // 使用时间戳后6位
        return `tool-prompt-${timestamp}.md`;
    }

});
</script>

</div>
