/* 模态框组件样式 - 零圆角设计 */

/* 模态框遮罩层 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    padding: 1rem;
    border-radius: 0 !important; /* 零圆角强制 */
}

.modal.active {
    opacity: 1;
    visibility: visible;
}

.modal.active .modal-content {
    transform: scale(1);
    opacity: 1;
}

/* 模态框内容容器 */
.modal-content {
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    max-width: 90vw;
    max-height: 90vh;
    width: 100%;
    position: relative;
    transform: scale(0.9);
    opacity: 0;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    border-radius: 0 !important; /* 零圆角强制 */
}

/* 模态框尺寸变体 */
.modal-sm .modal-content {
    max-width: 400px;
}

.modal-md .modal-content {
    max-width: 600px;
}

.modal-lg .modal-content {
    max-width: 800px;
}

.modal-xl .modal-content {
    max-width: 1200px;
}

.modal-fullscreen .modal-content {
    max-width: 100vw;
    max-height: 100vh;
    width: 100vw;
    height: 100vh;
    margin: 0;
}

/* 模态框头部 */
.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-shrink: 0;
}

.modal-title {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-color);
}

.modal-close {
    background: none;
    border: none;
    color: var(--text-color);
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0.25rem;
    transition: all 0.2s ease;
    opacity: 0.7;
    border-radius: 0 !important; /* 零圆角强制 */
}

.modal-close:hover {
    opacity: 1;
    color: var(--accent-color);
    transform: scale(1.1);
}

/* 模态框主体 */
.modal-body {
    padding: 1.5rem;
    flex: 1;
    overflow-y: auto;
    color: var(--text-color);
    line-height: 1.6;
}

.modal-body::-webkit-scrollbar {
    width: 6px;
}

.modal-body::-webkit-scrollbar-track {
    background: var(--secondary-color);
}

.modal-body::-webkit-scrollbar-thumb {
    background: var(--border-color);
    transition: background 0.2s ease;
}

.modal-body::-webkit-scrollbar-thumb:hover {
    background: var(--accent-color);
}

/* 模态框底部 */
.modal-footer {
    padding: 1.5rem;
    border-top: 1px solid var(--border-color);
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    flex-shrink: 0;
}

.modal-footer.centered {
    justify-content: center;
}

.modal-footer.left {
    justify-content: flex-start;
}

.modal-footer.space-between {
    justify-content: space-between;
}

/* 搜索模态框特殊样式 */
.modal-search .modal-content {
    max-width: 600px;
    margin-top: 10vh;
}

.modal-search .modal-body {
    padding: 2rem;
}

.modal-search-input {
    width: 100%;
    padding: 1rem;
    font-size: 1.125rem;
    background-color: var(--primary-color);
    border: 1px solid var(--border-color);
    color: var(--text-color);
    margin-bottom: 1rem;
    border-radius: 0 !important; /* 零圆角强制 */
}

.modal-search-input:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 2px rgba(78, 115, 223, 0.1);
}

.modal-search-results {
    max-height: 300px;
    overflow-y: auto;
}

.modal-search-item {
    padding: 0.75rem;
    border-bottom: 1px solid var(--border-color);
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.modal-search-item:hover {
    background-color: var(--hover-bg);
}

.modal-search-item:last-child {
    border-bottom: none;
}

.modal-search-title {
    font-weight: 500;
    color: var(--text-color);
    margin-bottom: 0.25rem;
}

.modal-search-description {
    font-size: 0.875rem;
    color: var(--text-color);
    opacity: 0.7;
}

.modal-search-hint {
    text-align: center;
    color: var(--text-color);
    opacity: 0.6;
    font-size: 0.875rem;
    padding: 2rem;
}

/* 确认模态框 */
.modal-confirm .modal-content {
    max-width: 400px;
}

.modal-confirm .modal-body {
    text-align: center;
    padding: 2rem;
}

.modal-confirm-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    display: block;
}

.modal-confirm-icon.warning {
    color: var(--warning-color);
}

.modal-confirm-icon.danger {
    color: var(--danger-color);
}

.modal-confirm-icon.success {
    color: var(--success-color);
}

.modal-confirm-icon.info {
    color: var(--accent-color);
}

.modal-confirm-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-color);
}

.modal-confirm-message {
    color: var(--text-color);
    opacity: 0.8;
    margin-bottom: 1.5rem;
}

/* 图片模态框 */
.modal-image .modal-content {
    max-width: 90vw;
    max-height: 90vh;
    background: transparent;
    border: none;
    padding: 0;
}

.modal-image .modal-body {
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-image img {
    max-width: 100%;
    max-height: 90vh;
    object-fit: contain;
}

.modal-image .modal-close {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    width: 3rem;
    height: 3rem;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1;
}

/* 视频模态框 */
.modal-video .modal-content {
    max-width: 90vw;
    max-height: 90vh;
    background: transparent;
    border: none;
    padding: 0;
}

.modal-video .modal-body {
    padding: 0;
    position: relative;
    aspect-ratio: 16/9;
}

.modal-video iframe,
.modal-video video {
    width: 100%;
    height: 100%;
    border: none;
}

/* 加载状态 */
.modal-loading .modal-body {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 3rem;
}

.modal-spinner {
    width: 3rem;
    height: 3rem;
    border: 3px solid var(--border-color);
    border-top: 3px solid var(--accent-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 模态框动画变体 */
.modal.fade-in {
    animation: modalFadeIn 0.3s ease;
}

.modal.slide-down .modal-content {
    animation: modalSlideDown 0.3s ease;
}

.modal.slide-up .modal-content {
    animation: modalSlideUp 0.3s ease;
}

.modal.zoom-in .modal-content {
    animation: modalZoomIn 0.3s ease;
}

@keyframes modalFadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes modalSlideDown {
    from { transform: translateY(-50px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

@keyframes modalSlideUp {
    from { transform: translateY(50px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

@keyframes modalZoomIn {
    from { transform: scale(0.8); opacity: 0; }
    to { transform: scale(1); opacity: 1; }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .modal {
        padding: 0.5rem;
    }
    
    .modal-content {
        max-width: 100vw;
        max-height: 100vh;
        margin: 0;
    }
    
    .modal-header,
    .modal-body,
    .modal-footer {
        padding: 1rem;
    }
    
    .modal-title {
        font-size: 1.125rem;
    }
    
    .modal-footer {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .modal-footer.space-between {
        flex-direction: column-reverse;
    }
    
    .modal-search .modal-content {
        margin-top: 0;
        height: 100vh;
    }
    
    .modal-search .modal-body {
        padding: 1rem;
    }
    
    .modal-search-input {
        font-size: 1rem;
    }
}

/* 防止页面滚动 */
body.modal-open {
    overflow: hidden;
}

/* 模态框堆叠 */
.modal[data-level="1"] { z-index: 9999; }
.modal[data-level="2"] { z-index: 10000; }
.modal[data-level="3"] { z-index: 10001; }
