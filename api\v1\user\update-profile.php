<?php
/**
 * 更新用户资料API
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

session_start();

// 检查用户是否已登录
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

// 使用统一的数据库连接
require_once dirname(dirname(dirname(__DIR__))) . '/includes/database-connection.php';

try {
    // 获取请求数据
    $input = json_decode(file_get_contents('php://input'), true);
    
    $userId = $_SESSION['user_id'];
    $firstName = trim($input['first_name'] ?? '');
    $lastName = trim($input['last_name'] ?? '');
    $username = trim($input['username'] ?? '');
    $bio = trim($input['bio'] ?? '');
    
    // 验证必需字段
    if (empty($firstName) || empty($lastName) || empty($username)) {
        throw new Exception('First name, last name, and username are required');
    }
    
    // 检查用户名是否已被其他用户使用
    $stmt = $pdo->prepare("SELECT id FROM pt_member WHERE username = ? AND id != ?");
    $stmt->execute([$username, $userId]);
    if ($stmt->fetch()) {
        throw new Exception('Username is already taken');
    }
    
    // 更新用户资料
    $stmt = $pdo->prepare("
        UPDATE pt_member 
        SET first_name = ?, last_name = ?, username = ?, bio = ?, updated_at = NOW()
        WHERE id = ?
    ");
    $stmt->execute([$firstName, $lastName, $username, $bio, $userId]);
    
    // 记录活动
    try {
        $stmt = $pdo->prepare("
            INSERT INTO pt_member_activity_log (user_id, action, description, ip_address, user_agent, created_at)
            VALUES (?, 'profile_updated', 'User updated profile information', ?, ?, NOW())
        ");
        $stmt->execute([
            $userId,
            $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        ]);
    } catch (Exception $e) {
        // 日志记录失败不影响主流程
    }
    
    echo json_encode([
        'success' => true,
        'message' => 'Profile updated successfully'
    ]);
    
} catch (Exception $e) {
    error_log("Update profile error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
