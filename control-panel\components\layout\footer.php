<?php
/**
 * 管理后台底部布局
 * 包含JavaScript和结束标签
 */
?>

<!-- 管理后台专用JavaScript -->
<script>
/**
 * 管理后台通用功能
 */
class AdminPanel {
    constructor() {
        this.init();
    }

    init() {
        this.setupSidebar();
        this.setupUserMenu();
        this.setupNotifications();
        this.setupDataTables();
    }

    /**
     * 设置侧边栏功能
     */
    setupSidebar() {
        const mobileMenuBtn = document.getElementById('mobile-menu-btn');
        const sidebar = document.getElementById('sidebar');

        if (mobileMenuBtn && sidebar) {
            mobileMenuBtn.addEventListener('click', () => {
                sidebar.classList.toggle('-translate-x-full');
            });

            // 点击外部关闭侧边栏 (移动端)
            document.addEventListener('click', (e) => {
                if (window.innerWidth < 1024) {
                    if (!sidebar.contains(e.target) && !mobileMenuBtn.contains(e.target)) {
                        sidebar.classList.add('-translate-x-full');
                    }
                }
            });
        }
    }

    /**
     * 设置用户菜单
     */
    setupUserMenu() {
        const userMenuBtn = document.getElementById('user-menu-btn');
        const userMenu = document.getElementById('user-menu');

        if (userMenuBtn && userMenu) {
            userMenuBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                userMenu.classList.toggle('hidden');
            });

            // 点击外部关闭菜单
            document.addEventListener('click', () => {
                userMenu.classList.add('hidden');
            });
        }
    }

    /**
     * 设置通知功能
     */
    setupNotifications() {
        // 自动关闭通知
        const notifications = document.querySelectorAll('.notification');
        notifications.forEach(notification => {
            const closeBtn = notification.querySelector('.notification-close');
            if (closeBtn) {
                closeBtn.addEventListener('click', () => {
                    notification.remove();
                });
            }

            // 自动关闭
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.style.opacity = '0';
                    setTimeout(() => notification.remove(), 300);
                }
            }, 5000);
        });
    }

    /**
     * 设置数据表格功能
     */
    setupDataTables() {
        const tables = document.querySelectorAll('.data-table');
        tables.forEach(table => {
            // 添加排序功能
            const headers = table.querySelectorAll('th[data-sort]');
            headers.forEach(header => {
                header.style.cursor = 'pointer';
                header.addEventListener('click', () => {
                    this.sortTable(table, header.dataset.sort);
                });
            });

            // 添加搜索功能
            const searchInput = table.parentElement.querySelector('.table-search');
            if (searchInput) {
                searchInput.addEventListener('input', (e) => {
                    this.filterTable(table, e.target.value);
                });
            }
        });
    }

    /**
     * 表格排序
     */
    sortTable(table, column) {
        const tbody = table.querySelector('tbody');
        const rows = Array.from(tbody.querySelectorAll('tr'));
        const columnIndex = Array.from(table.querySelectorAll('th')).findIndex(th => th.dataset.sort === column);

        rows.sort((a, b) => {
            const aValue = a.cells[columnIndex].textContent.trim();
            const bValue = b.cells[columnIndex].textContent.trim();
            
            // 尝试数字比较
            const aNum = parseFloat(aValue);
            const bNum = parseFloat(bValue);
            
            if (!isNaN(aNum) && !isNaN(bNum)) {
                return aNum - bNum;
            }
            
            // 字符串比较
            return aValue.localeCompare(bValue);
        });

        // 重新排列行
        rows.forEach(row => tbody.appendChild(row));
    }

    /**
     * 表格过滤
     */
    filterTable(table, searchTerm) {
        const tbody = table.querySelector('tbody');
        const rows = tbody.querySelectorAll('tr');
        
        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            const matches = text.includes(searchTerm.toLowerCase());
            row.style.display = matches ? '' : 'none';
        });
    }

    /**
     * 显示通知
     */
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification fixed top-4 right-4 z-50 p-4 border max-w-sm ${
            type === 'success' ? 'bg-green-100 border-green-400 text-green-700' :
            type === 'error' ? 'bg-red-100 border-red-400 text-red-700' :
            type === 'warning' ? 'bg-yellow-100 border-yellow-400 text-yellow-700' :
            'bg-blue-100 border-blue-400 text-blue-700'
        }`;
        
        notification.innerHTML = `
            <div class="flex items-center justify-between">
                <span>${message}</span>
                <button class="notification-close ml-4 text-current opacity-70 hover:opacity-100">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // 设置关闭事件
        notification.querySelector('.notification-close').addEventListener('click', () => {
            notification.remove();
        });
        
        // 自动关闭
        setTimeout(() => {
            if (notification.parentElement) {
                notification.style.opacity = '0';
                setTimeout(() => notification.remove(), 300);
            }
        }, 5000);
    }

    /**
     * 确认对话框
     */
    confirm(message, callback) {
        if (window.confirm(message)) {
            callback();
        }
    }

    /**
     * AJAX请求封装
     */
    async request(url, options = {}) {
        try {
            const response = await fetch(url, {
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    ...options.headers
                },
                ...options
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('Request failed:', error);
            this.showNotification('Request failed. Please try again.', 'error');
            throw error;
        }
    }
}

// 初始化管理后台
document.addEventListener('DOMContentLoaded', () => {
    window.adminPanel = new AdminPanel();
});

// 全局函数
window.showNotification = (message, type) => {
    if (window.adminPanel) {
        window.adminPanel.showNotification(message, type);
    }
};

window.confirmAction = (message, callback) => {
    if (window.adminPanel) {
        window.adminPanel.confirm(message, callback);
    }
};
</script>

</body>
</html>
