<?php
/**
 * 文件上传处理器
 */

// 关闭错误显示，避免破坏JSON响应
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// 设置响应头
header('Content-Type: application/json');

// 捕获所有输出
ob_start();

session_start();

// 错误处理函数
function sendJsonError($message, $code = 500) {
    ob_clean();
    http_response_code($code);
    echo json_encode(['success' => false, 'message' => $message]);
    exit;
}

// 生成合法的文件名
function generateSafeFilename($productName, $type, $extension, $index = null) {
    // 清理产品名称，只保留字母、数字、空格和连字符
    $safeName = preg_replace('/[^a-zA-Z0-9\s\-_]/', '', $productName);
    // 将空格替换为连字符
    $safeName = preg_replace('/\s+/', '-', trim($safeName));
    // 转换为小写
    $safeName = strtolower($safeName);
    // 限制长度
    $safeName = substr($safeName, 0, 50);

    // 如果清理后为空，使用默认名称
    if (empty($safeName)) {
        $safeName = 'product';
    }

    // 生成文件名
    if ($type === 'logo') {
        return $safeName . '-logo.' . $extension;
    } else {
        $suffix = $index !== null ? '-screenshot-' . ($index + 1) : '-screenshot';
        return $safeName . $suffix . '.' . $extension;
    }
}

// 检查管理员权限
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    sendJsonError('Unauthorized', 401);
}

// 检查是否有文件上传
if (!isset($_FILES['file']) || $_FILES['file']['error'] !== UPLOAD_ERR_OK) {
    sendJsonError('No file uploaded or upload error');
}

$file = $_FILES['file'];
$uploadType = $_POST['type'] ?? 'logo'; // logo 或 screenshot
$productName = $_POST['product_name'] ?? 'product'; // 产品名称

// 验证文件类型
$allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
if (!in_array($file['type'], $allowedTypes)) {
    sendJsonError('Invalid file type. Only JPEG, PNG, GIF, and WebP are allowed.');
}

// 验证文件大小 (最大5MB)
$maxSize = 5 * 1024 * 1024; // 5MB
if ($file['size'] > $maxSize) {
    sendJsonError('File size too large. Maximum size is 5MB.');
}

try {
    // 确定上传目录
    $baseDir = dirname(dirname(__DIR__)) . '/public/uploads/launches/';
    
    if ($uploadType === 'logo') {
        $uploadDir = $baseDir . 'logos/';
    } else {
        $uploadDir = $baseDir . 'screenshots/';
    }
    
    // 创建目录（如果不存在）
    if (!is_dir($uploadDir)) {
        if (!mkdir($uploadDir, 0755, true)) {
            sendJsonError('Failed to create upload directory');
        }
    }
    
    // 生成基于产品名称的文件名
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);

    // 如果是截图，需要检查已有截图数量来确定索引
    $screenshotIndex = null;
    if ($uploadType === 'screenshot') {
        // 计算当前截图的索引
        $existingFiles = glob($uploadDir . '*.{jpg,jpeg,png,gif,webp}', GLOB_BRACE);
        $screenshotIndex = count($existingFiles);
    }

    $filename = generateSafeFilename($productName, $uploadType, $extension, $screenshotIndex);

    // 如果文件已存在，添加时间戳确保唯一性
    if (file_exists($uploadDir . $filename)) {
        $nameWithoutExt = pathinfo($filename, PATHINFO_FILENAME);
        $filename = $nameWithoutExt . '-' . time() . '.' . $extension;
    }

    $filepath = $uploadDir . $filename;
    
    // 移动上传的文件
    if (!move_uploaded_file($file['tmp_name'], $filepath)) {
        sendJsonError('Failed to save uploaded file');
    }
    
    // 生成访问URL
    $webPath = '/uploads/launches/' . ($uploadType === 'logo' ? 'logos' : 'screenshots') . '/' . $filename;
    
    // 如果是logo，删除旧的logo文件（可选）
    if ($uploadType === 'logo' && isset($_POST['old_logo_url'])) {
        $oldLogoUrl = $_POST['old_logo_url'];
        if (strpos($oldLogoUrl, '/uploads/launches/logos/') !== false) {
            $oldFilename = basename($oldLogoUrl);
            $oldFilepath = $uploadDir . $oldFilename;
            if (file_exists($oldFilepath)) {
                unlink($oldFilepath);
            }
        }
    }
    
    ob_clean();
    echo json_encode([
        'success' => true,
        'url' => $webPath,
        'filename' => $filename,
        'message' => ucfirst($uploadType) . ' uploaded successfully'
    ]);

} catch (Exception $e) {
    sendJsonError('Upload failed: ' . $e->getMessage());
}
?>
