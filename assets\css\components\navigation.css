/* 导航组件样式 - 零圆角设计 */

/* 主导航栏 */
.navbar {
    background-color: var(--primary-color);
    border-bottom: 1px solid var(--border-color);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    transition: all 0.3s ease;
    border-radius: 0 !important; /* 零圆角强制 */
}

.navbar.scrolled {
    background-color: rgba(0, 0, 0, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.navbar.hidden {
    transform: translateY(-100%);
}

/* 导航容器 */
.navbar-container {
    max-width: 1280px;
    margin: 0 auto;
    padding: 0 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 4rem;
}

/* Logo区域 */
.navbar-brand {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
    color: var(--text-color);
    font-weight: 700;
    font-size: 1.25rem;
    transition: color 0.2s ease;
}

.navbar-brand:hover {
    color: var(--accent-color);
}

.navbar-logo {
    width: 2rem;
    height: 2rem;
    background-color: var(--accent-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 1.125rem;
}

.navbar-title {
    color: var(--text-color);
    font-size: 1.25rem;
    font-weight: 700;
}

/* 主导航菜单 */
.navbar-nav {
    display: flex;
    align-items: center;
    gap: 2rem;
    list-style: none;
    margin: 0;
    padding: 0;
}

.navbar-nav-item {
    position: relative;
}

.navbar-nav-link {
    color: var(--text-color);
    text-decoration: none;
    font-weight: 500;
    padding: 0.5rem 0;
    transition: color 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.navbar-nav-link:hover,
.navbar-nav-link.active {
    color: var(--accent-color);
}

.navbar-nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -1rem;
    left: 0;
    right: 0;
    height: 2px;
    background-color: var(--accent-color);
}

/* 下拉菜单 */
.navbar-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    min-width: 12rem;
    background-color: var(--secondary-color);
    border: 1px solid var(--border-color);
    margin-top: 0.5rem;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.2s ease;
    z-index: 1001;
    border-radius: 0 !important; /* 零圆角强制 */
}

.navbar-nav-item:hover .navbar-dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.navbar-dropdown-item {
    display: block;
    padding: 0.75rem 1rem;
    color: var(--text-color);
    text-decoration: none;
    font-size: 0.875rem;
    transition: all 0.2s ease;
    border-bottom: 1px solid var(--border-color);
}

.navbar-dropdown-item:last-child {
    border-bottom: none;
}

.navbar-dropdown-item:hover {
    background-color: var(--hover-bg);
    color: var(--accent-color);
}

.navbar-dropdown-icon {
    margin-right: 0.5rem;
    font-size: 1rem;
}

/* 用户菜单区域 */
.navbar-user {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.navbar-search-btn,
.navbar-user-btn {
    background: none;
    border: none;
    color: var(--text-color);
    padding: 0.5rem;
    cursor: pointer;
    transition: color 0.2s ease;
    border-radius: 0 !important; /* 零圆角强制 */
}

.navbar-search-btn:hover,
.navbar-user-btn:hover {
    color: var(--accent-color);
}

/* 用户头像 */
.navbar-avatar {
    width: 2rem;
    height: 2rem;
    background-color: var(--accent-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.navbar-avatar:hover {
    background-color: var(--accent-color);
    transform: scale(1.05);
}

/* 移动端菜单按钮 */
.navbar-mobile-btn {
    display: none;
    background: none;
    border: none;
    color: var(--text-color);
    padding: 0.5rem;
    cursor: pointer;
    transition: color 0.2s ease;
}

.navbar-mobile-btn:hover {
    color: var(--accent-color);
}

/* 移动端菜单 */
.navbar-mobile {
    display: none;
    background-color: var(--secondary-color);
    border-top: 1px solid var(--border-color);
    padding: 1rem;
}

.navbar-mobile.active {
    display: block;
}

.navbar-mobile-nav {
    list-style: none;
    margin: 0;
    padding: 0;
}

.navbar-mobile-item {
    margin-bottom: 0.5rem;
}

.navbar-mobile-link {
    display: block;
    color: var(--text-color);
    text-decoration: none;
    padding: 0.75rem 0;
    font-weight: 500;
    transition: color 0.2s ease;
    border-bottom: 1px solid var(--border-color);
}

.navbar-mobile-link:hover,
.navbar-mobile-link.active {
    color: var(--accent-color);
}

.navbar-mobile-link:last-child {
    border-bottom: none;
}

/* 面包屑导航 */
.breadcrumb {
    background-color: var(--secondary-color);
    border-bottom: 1px solid var(--border-color);
    padding: 0.75rem 0;
}

.breadcrumb-container {
    max-width: 1280px;
    margin: 0 auto;
    padding: 0 1rem;
}

.breadcrumb-nav {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: var(--text-color);
    opacity: 0.8;
}

.breadcrumb-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.breadcrumb-link {
    color: var(--text-color);
    text-decoration: none;
    transition: color 0.2s ease;
}

.breadcrumb-link:hover {
    color: var(--accent-color);
}

.breadcrumb-separator {
    color: var(--text-color);
    opacity: 0.5;
}

.breadcrumb-current {
    color: var(--accent-color);
    font-weight: 500;
}

/* 侧边栏导航 */
.sidebar {
    width: 16rem;
    background-color: var(--secondary-color);
    border-right: 1px solid var(--border-color);
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 999;
    overflow-y: auto;
    transition: transform 0.3s ease;
    border-radius: 0 !important; /* 零圆角强制 */
}

.sidebar.collapsed {
    transform: translateX(-100%);
}

.sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.sidebar-nav {
    padding: 1rem 0;
}

.sidebar-nav-item {
    margin-bottom: 0.25rem;
}

.sidebar-nav-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1.5rem;
    color: var(--text-color);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.2s ease;
}

.sidebar-nav-link:hover,
.sidebar-nav-link.active {
    background-color: var(--hover-bg);
    color: var(--accent-color);
}

.sidebar-nav-link.active {
    border-right: 3px solid var(--accent-color);
}

.sidebar-nav-icon {
    font-size: 1.125rem;
    width: 1.25rem;
    text-align: center;
}

.sidebar-nav-text {
    flex: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .navbar-nav {
        display: none;
    }
    
    .navbar-mobile-btn {
        display: block;
    }
    
    .navbar-container {
        padding: 0 1rem;
    }
    
    .navbar-brand {
        font-size: 1.125rem;
    }
    
    .navbar-title {
        display: none;
    }
    
    .navbar-user {
        gap: 0.5rem;
    }
    
    .breadcrumb-container {
        padding: 0 1rem;
    }
    
    .breadcrumb-nav {
        font-size: 0.75rem;
    }
    
    .sidebar {
        width: 100%;
        transform: translateX(-100%);
    }
    
    .sidebar.active {
        transform: translateX(0);
    }
}

@media (max-width: 480px) {
    .navbar-container {
        height: 3.5rem;
    }
    
    .navbar-logo {
        width: 1.75rem;
        height: 1.75rem;
        font-size: 1rem;
    }
    
    .navbar-brand {
        font-size: 1rem;
    }
    
    .navbar-avatar {
        width: 1.75rem;
        height: 1.75rem;
        font-size: 0.75rem;
    }
}
