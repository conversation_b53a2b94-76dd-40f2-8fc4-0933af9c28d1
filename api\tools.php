<?php
/**
 * Tools API - 获取工具数据（支持分页）
 * 返回数据库中的真实工具数据
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

// 移除测试代码

// 包含数据库配置
try {
    // 获取参数并输出调试信息
    $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
    $limit = isset($_GET['limit']) ? max(1, min(50, intval($_GET['limit']))) : 12;
    $category = isset($_GET['category']) ? trim($_GET['category']) : '';
    $search = isset($_GET['search']) ? trim($_GET['search']) : '';
    $sort = isset($_GET['sort']) ? trim($_GET['sort']) : 'name';

    // 使用统一的数据库连接
    require_once dirname(__DIR__) . '/includes/database-connection.php';

    // 计算偏移量
    $offset = ($page - 1) * $limit;

    // 构建WHERE条件
    $whereConditions = ["t.status = 'active'"];
    $params = [];

    if (!empty($category)) {
        $whereConditions[] = "tc.slug = :category";
        $params['category'] = $category;
    }

    if (!empty($search)) {
        $whereConditions[] = "(t.name LIKE :search1 OR t.description LIKE :search2 OR t.tags LIKE :search3)";
        $params['search1'] = "%{$search}%";
        $params['search2'] = "%{$search}%";
        $params['search3'] = "%{$search}%";
    }

    $whereClause = implode(' AND ', $whereConditions);

    // 调试信息
    $debugInfo = [
        'whereConditions' => $whereConditions,
        'whereClause' => $whereClause,
        'params' => $params,
        'category' => $category,
        'search' => $search,
        'sort' => $sort
    ];

    // 移除测试代码，继续正常执行

    // 构建排序条件
    switch($sort) {
        case 'popular':
            $orderBy = 't.view_count DESC, t.name ASC';
            break;
        case 'newest':
            $orderBy = 't.created_at DESC, t.id DESC';
            break;
        case 'featured':
            $orderBy = 't.is_featured DESC, t.name ASC';
            break;
        default:
            $orderBy = 't.name ASC';
            break;
    }

    // 获取总数
    $countStmt = $pdo->prepare("
        SELECT COUNT(*) as total
        FROM pt_tool t
        LEFT JOIN pt_tool_category tc ON t.category_id = tc.id
        WHERE {$whereClause}
    ");

    // 绑定参数
    foreach ($params as $key => $value) {
        $countStmt->bindValue(":{$key}", $value);
    }

    $countStmt->execute();
    $totalTools = $countStmt->fetch()['total'];

    // 获取当前页的工具
    $stmt = $pdo->prepare("
        SELECT
            t.id,
            t.name,
            t.slug,
            t.description,
            t.icon,
            t.tags,
            t.view_count,
            t.is_featured as featured,
            t.status,
            t.url,
            t.created_at,
            tc.name as category_name,
            tc.slug as category_slug,
            tc.icon as category_icon
        FROM pt_tool t
        LEFT JOIN pt_tool_category tc ON t.category_id = tc.id
        WHERE {$whereClause}
        ORDER BY {$orderBy}
        LIMIT :limit OFFSET :offset
    ");

    // 绑定参数
    foreach ($params as $key => $value) {
        $stmt->bindValue(":{$key}", $value);
    }
    $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
    $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);

    $stmt->execute();
    $tools = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 处理工具数据
    $processedTools = [];
    foreach ($tools as $tool) {
        // 处理标签
        $tags = [];
        if (!empty($tool['tags'])) {
            $tags = array_map('trim', explode(',', $tool['tags']));
        }
        
        // 构建工具URL
        $toolUrl = $tool['url'];
        if (empty($toolUrl)) {
            if (!empty($tool['category_slug'])) {
                $toolUrl = '/tools/' . $tool['category_slug'] . '/' . $tool['slug'];
            } else {
                $toolUrl = '/tools/' . $tool['slug'];
            }
        }
        
        $processedTools[] = [
            'id' => (int)$tool['id'],
            'name' => $tool['name'],
            'slug' => $tool['slug'],
            'description' => $tool['description'] ?: 'No description available',
            'icon' => $tool['icon'] ?: '🔧',
            'tags' => $tags,
            'view_count' => (int)($tool['view_count'] ?: 0),
            'featured' => (bool)$tool['featured'],
            'status' => $tool['status'],
            'url' => $toolUrl,
            'created_at' => $tool['created_at'],
            'category_name' => $tool['category_name'],
            'category_slug' => $tool['category_slug'],
            'category_icon' => $tool['category_icon']
        ];
    }
    
    // 计算分页信息
    $totalPages = ceil($totalTools / $limit);
    $hasMore = $page < $totalPages;

    // 返回成功响应
    echo json_encode([
        'success' => true,
        'tools' => $processedTools,
        'pagination' => [
            'current_page' => $page,
            'per_page' => $limit,
            'total' => $totalTools,
            'total_pages' => $totalPages,
            'has_more' => $hasMore
        ],

        'message' => 'Tools loaded successfully'
    ], JSON_UNESCAPED_UNICODE);
    
} catch (PDOException $e) {
    // 数据库错误
    $errorDetails = [
        'error' => $e->getMessage(),
        'code' => $e->getCode(),
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'dsn' => $dsn ?? 'DSN not set',
        'database_config' => [
            'host' => $dbConfig['host'] ?? 'not set',
            'database' => $dbConfig['database'] ?? 'not set',
            'username' => $dbConfig['username'] ?? 'not set'
        ]
    ];

    error_log('Database error in tools API: ' . json_encode($errorDetails));

    echo json_encode([
        'success' => false,
        'tools' => [],
        'total' => 0,
        'message' => 'Database error occurred: ' . $e->getMessage(),
        'debug' => array_merge($errorDetails, [
            'whereClause' => $whereClause ?? 'not set',
            'params' => $params ?? [],
            'debugInfo' => $debugInfo ?? []
        ])
    ], JSON_UNESCAPED_UNICODE);

} catch (Exception $e) {
    // 其他错误
    $errorDetails = [
        'error' => $e->getMessage(),
        'code' => $e->getCode(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ];

    error_log('General error in tools API: ' . json_encode($errorDetails));

    echo json_encode([
        'success' => false,
        'tools' => [],
        'total' => 0,
        'message' => 'An error occurred while loading tools: ' . $e->getMessage(),
        'debug' => $errorDetails
    ], JSON_UNESCAPED_UNICODE);
}
?>
