<?php
/**
 * 需求管理AJAX处理器
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 0);

// 设置JSON响应头
header('Content-Type: application/json');

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// 启动会话
session_start();

// 检查管理员权限
if (!isset($_SESSION['admin_id'])) {
    echo json_encode(['success' => false, 'message' => 'Unauthorized access']);
    exit;
}

// 使用统一的数据库连接
require_once dirname(dirname(__DIR__)) . '/includes/database-connection.php';

$response = ['success' => false, 'message' => ''];
$action = $_POST['action'] ?? '';

try {
    switch ($action) {
        case 'get_request':
            $requestId = intval($_POST['id'] ?? 0);
            if ($requestId > 0) {
                $stmt = $pdo->prepare("
                    SELECT r.*, m.username, m.first_name, m.last_name, m.email
                    FROM pt_user_requests r
                    LEFT JOIN pt_member m ON r.user_id = m.id
                    WHERE r.id = ?
                ");
                $stmt->execute([$requestId]);
                $request = $stmt->fetch();
                
                if ($request) {
                    $response['success'] = true;
                    $response['request'] = $request;
                } else {
                    $response['message'] = 'Request not found';
                }
            } else {
                $response['message'] = 'Invalid request ID';
            }
            break;
            
        case 'reply_request':
            $requestId = intval($_POST['request_id'] ?? 0);
            $status = $_POST['status'] ?? '';
            $priority = $_POST['priority'] ?? '';
            $adminReply = trim($_POST['admin_reply'] ?? '');
            
            // 验证状态值
            $validStatuses = ['pending', 'reviewing', 'accepted', 'rejected', 'completed'];
            $validPriorities = ['low', 'medium', 'high'];
            
            if (!in_array($status, $validStatuses)) {
                $response['message'] = 'Invalid status value';
                break;
            }
            
            if (!in_array($priority, $validPriorities)) {
                $response['message'] = 'Invalid priority value';
                break;
            }
            
            if ($requestId > 0) {
                $stmt = $pdo->prepare("
                    UPDATE pt_user_requests 
                    SET status = ?, priority = ?, admin_reply = ?, admin_id = ?, processed_at = NOW(), updated_at = NOW()
                    WHERE id = ?
                ");
                $stmt->execute([$status, $priority, $adminReply, $_SESSION['admin_id'], $requestId]);
                
                if ($stmt->rowCount() > 0) {
                    $response['success'] = true;
                    $response['message'] = 'Request updated successfully';
                } else {
                    $response['message'] = 'No changes made or request not found';
                }
            } else {
                $response['message'] = 'Invalid request ID';
            }
            break;
            
        case 'delete_request':
            $requestId = intval($_POST['id'] ?? 0);
            if ($requestId > 0) {
                // 开始事务
                $pdo->beginTransaction();
                
                try {
                    // 删除相关的投票记录
                    $stmt = $pdo->prepare("DELETE FROM pt_request_votes WHERE request_id = ?");
                    $stmt->execute([$requestId]);
                    
                    // 删除防重复记录
                    $stmt = $pdo->prepare("DELETE FROM pt_request_duplicates WHERE request_id = ?");
                    $stmt->execute([$requestId]);
                    
                    // 删除需求记录
                    $stmt = $pdo->prepare("DELETE FROM pt_user_requests WHERE id = ?");
                    $stmt->execute([$requestId]);
                    
                    if ($stmt->rowCount() > 0) {
                        $pdo->commit();
                        $response['success'] = true;
                        $response['message'] = 'Request deleted successfully';
                    } else {
                        $pdo->rollBack();
                        $response['message'] = 'Request not found';
                    }
                } catch (Exception $e) {
                    $pdo->rollBack();
                    throw $e;
                }
            } else {
                $response['message'] = 'Invalid request ID';
            }
            break;

        case 'edit_request':
            $requestId = intval($_POST['request_id'] ?? 0);
            $title = trim($_POST['title'] ?? '');
            $description = trim($_POST['description'] ?? '');
            $category = $_POST['category'] ?? '';
            $priority = $_POST['priority'] ?? '';
            $status = $_POST['status'] ?? '';

            // 验证必填字段
            if (empty($title) || empty($description)) {
                $response['message'] = 'Title and description are required';
                break;
            }

            // 验证分类值
            $validCategories = ['ai-tools', 'development', 'design', 'productivity', 'business', 'education', 'entertainment', 'other'];
            $validStatuses = ['pending', 'reviewing', 'accepted', 'rejected', 'completed'];
            $validPriorities = ['low', 'medium', 'high'];

            if (!in_array($category, $validCategories)) {
                $response['message'] = 'Invalid category value';
                break;
            }

            if (!in_array($status, $validStatuses)) {
                $response['message'] = 'Invalid status value';
                break;
            }

            if (!in_array($priority, $validPriorities)) {
                $response['message'] = 'Invalid priority value';
                break;
            }

            if ($requestId > 0) {
                $stmt = $pdo->prepare("
                    UPDATE pt_user_requests
                    SET title = ?, description = ?, category = ?, priority = ?, status = ?, updated_at = NOW()
                    WHERE id = ?
                ");
                $stmt->execute([$title, $description, $category, $priority, $status, $requestId]);

                if ($stmt->rowCount() > 0) {
                    $response['success'] = true;
                    $response['message'] = 'Request updated successfully';
                } else {
                    $response['message'] = 'No changes made or request not found';
                }
            } else {
                $response['message'] = 'Invalid request ID';
            }
            break;

        case 'edit_request':
            $requestId = intval($_POST['request_id'] ?? 0);
            $title = trim($_POST['title'] ?? '');
            $description = trim($_POST['description'] ?? '');
            $category = $_POST['category'] ?? '';
            $priority = $_POST['priority'] ?? '';
            $status = $_POST['status'] ?? '';

            // 验证必填字段
            if (empty($title) || empty($description)) {
                $response['message'] = 'Title and description are required';
                break;
            }

            // 从数据库获取有效分类
            $validCategoriesStmt = $pdo->query("SELECT slug FROM pt_tool_category WHERE status = 'active'");
            $validCategories = $validCategoriesStmt->fetchAll(PDO::FETCH_COLUMN);
            $validCategories[] = 'other'; // 添加其他分类

            $validStatuses = ['pending', 'reviewing', 'accepted', 'rejected', 'completed'];
            $validPriorities = ['low', 'medium', 'high'];

            if (!in_array($category, $validCategories)) {
                $response['message'] = 'Invalid category value';
                break;
            }

            if (!in_array($status, $validStatuses)) {
                $response['message'] = 'Invalid status value';
                break;
            }

            if (!in_array($priority, $validPriorities)) {
                $response['message'] = 'Invalid priority value';
                break;
            }

            if ($requestId > 0) {
                $stmt = $pdo->prepare("
                    UPDATE pt_user_requests
                    SET title = ?, description = ?, category = ?, priority = ?, status = ?, updated_at = NOW()
                    WHERE id = ?
                ");
                $stmt->execute([$title, $description, $category, $priority, $status, $requestId]);

                if ($stmt->rowCount() > 0) {
                    $response['success'] = true;
                    $response['message'] = 'Request updated successfully';
                } else {
                    $response['message'] = 'No changes made or request not found';
                }
            } else {
                $response['message'] = 'Invalid request ID';
            }
            break;

        default:
            $response['message'] = 'Invalid action';
    }
} catch (Exception $e) {
    $response['message'] = 'Error: ' . $e->getMessage();
    
    // 记录详细错误信息用于调试
    $response['debug'] = [
        'error' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'action' => $action,
        'post_data' => $_POST
    ];
}

echo json_encode($response);
?>
