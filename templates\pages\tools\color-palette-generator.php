<?php
/**
 * Color Palette Generator Tool Page
 * 遵循01-UI设计规范.md - 零圆角设计
 */

$currentPage = 'tool-color-palette-generator';
$breadcrumbs = [
    ['name' => 'Home', 'url' => '/'],
    ['name' => 'Tools', 'url' => '/tools'],
    ['name' => 'Design', 'url' => '/tools/design'],
    ['name' => 'Color Palette Generator']
];

// 包含动态SEO助手
require_once ROOT_PATH . '/app/helpers/dynamic-seo.php';

// 生成SEO数据 - 优化长度
$seoData = [
    'title' => 'Free Color Palette Generator Online - Prompt2Tool',
    'description' => 'Generate beautiful color palettes instantly. Create harmonious color schemes for web design, branding, and creative projects.',
    'keywords' => 'color palette generator, color scheme generator, color picker, hex colors, design colors, web colors, brand colors, color harmony',
    'og_title' => 'Free Color Palette Generator Online - Prompt2Tool',
    'og_description' => 'Generate beautiful color palettes instantly. Create harmonious color schemes for web design and creative projects.',
    'canonical' => getCurrentURL()
];

// 包含公共头部
include_once ROOT_PATH . '/templates/layout/header.php';
?>

<!-- 工具页面主体 -->
<div class="bg-gray-900 text-white">

    <!-- Hero Section with SEO optimized content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-12">
        <div class="text-center mb-12">
            <div class="flex items-center justify-center mb-4">
                <div class="bg-purple-600 p-3 mr-4">
                    <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z"></path>
                    </svg>
                </div>
                <div class="text-left">
                    <h1 class="text-4xl font-bold text-white mb-2">Color Palette Generator</h1>
                    <p class="text-xl text-gray-400">Generate beautiful color schemes and palettes</p>
                </div>
            </div>
        </div>

        <!-- 工具主体 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- 输入区域 -->
            <div class="bg-gray-800 border border-gray-700 p-6">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-xl font-semibold text-white">Color Input</h2>
                    <div class="flex space-x-2">
                        <button id="randomColor" class="px-3 py-1 bg-purple-600 text-white text-sm hover:bg-purple-700 transition-colors">
                            Random
                        </button>
                    </div>
                </div>
                
                <!-- 颜色选择器 -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-300 mb-2">Base Color</label>
                    <div class="flex space-x-4">
                        <input type="color" id="colorPicker" value="#3B82F6" class="w-16 h-12 border border-gray-600 bg-gray-900 cursor-pointer">
                        <input type="text" id="colorInput" value="#3B82F6" class="flex-1 bg-gray-900 border border-gray-600 text-gray-100 p-2 focus:outline-none focus:border-blue-500" placeholder="#FFFFFF">
                    </div>
                </div>
                
                <!-- 调色板类型 -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-300 mb-2">Palette Type</label>
                    <select id="paletteType" class="w-full bg-gray-900 border border-gray-600 text-gray-100 p-2 focus:outline-none focus:border-blue-500">
                        <option value="monochromatic">Monochromatic</option>
                        <option value="analogous">Analogous</option>
                        <option value="complementary">Complementary</option>
                        <option value="triadic">Triadic</option>
                        <option value="tetradic">Tetradic</option>
                        <option value="split-complementary">Split Complementary</option>
                    </select>
                </div>
                
                <!-- 颜色数量 -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-gray-300 mb-2">Number of Colors: <span id="colorCountValue">5</span></label>
                    <input type="range" id="colorCount" min="3" max="10" value="5" class="w-full">
                </div>
                
                <!-- 生成按钮 -->
                <button id="generateBtn" class="w-full bg-purple-600 text-white py-3 hover:bg-purple-700 transition-colors">
                    Generate Palette
                </button>
            </div>

            <!-- 输出区域 -->
            <div class="bg-gray-800 border border-gray-700 p-6">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-xl font-semibold text-white">Generated Palette</h2>
                    <div class="flex space-x-2">
                        <div class="relative">
                            <button id="copyPalette" class="px-3 py-1 bg-green-600 text-white text-sm hover:bg-green-700 transition-colors">
                                Copy CSS
                            </button>
                            <div id="copyTooltip" class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-700 text-white text-xs rounded pointer-events-none whitespace-nowrap z-50" style="opacity: 0; visibility: hidden; transition: opacity 0.2s, visibility 0.2s;">
                                Copy CSS variables
                            </div>
                        </div>
                        <div class="relative">
                            <button id="exportPalette" class="px-3 py-1 bg-blue-600 text-white text-sm hover:bg-blue-700 transition-colors">
                                Export
                            </button>
                            <div id="exportTooltip" class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-700 text-white text-xs rounded pointer-events-none whitespace-nowrap z-50" style="opacity: 0; visibility: hidden; transition: opacity 0.2s, visibility 0.2s;">
                                Export as JSON
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 调色板显示 -->
                <div id="paletteDisplay" class="space-y-3">
                    <!-- 颜色块将在这里动态生成 -->
                </div>
                
                <!-- CSS 输出 -->
                <div class="mt-6">
                    <label class="block text-sm font-medium text-gray-300 mb-2">CSS Variables</label>
                    <textarea id="cssOutput" class="w-full h-32 bg-gray-900 border border-gray-600 text-gray-100 p-3 font-mono text-sm focus:outline-none focus:border-blue-500" readonly placeholder="Generated CSS variables will appear here..."></textarea>
                </div>
            </div>
        </div>

        <!-- 工具信息 -->
        <div class="mt-12 grid grid-cols-1 md:grid-cols-3 gap-8">
            <div class="bg-gray-800 border border-gray-700 p-6">
                <h4 class="text-lg font-semibold text-white mb-3">Palette Types</h4>
                <ul class="text-gray-300 space-y-2">
                    <li>• Monochromatic</li>
                    <li>• Analogous</li>
                    <li>• Complementary</li>
                    <li>• Triadic</li>
                    <li>• Tetradic</li>
                </ul>
            </div>
            <div class="bg-gray-800 border border-gray-700 p-6">
                <h4 class="text-lg font-semibold text-white mb-3">Features</h4>
                <ul class="text-gray-300 space-y-2">
                    <li>• Color harmony rules</li>
                    <li>• CSS variable export</li>
                    <li>• JSON export</li>
                    <li>• Random generation</li>
                    <li>• Hex color support</li>
                </ul>
            </div>
            <div class="bg-gray-800 border border-gray-700 p-6">
                <h4 class="text-lg font-semibold text-white mb-3">Use Cases</h4>
                <ul class="text-gray-300 space-y-2">
                    <li>• Web design</li>
                    <li>• Brand identity</li>
                    <li>• UI/UX design</li>
                    <li>• Print design</li>
                    <li>• Art projects</li>
                </ul>
            </div>
        </div>

        <!-- FAQ Section -->
        <div class="px-8 pt-16 pb-16">
            <h2 class="text-3xl font-bold text-white text-center mb-8">Frequently Asked Questions</h2>
            <div class="max-w-4xl mx-auto space-y-6">
                <div class="border-b border-gray-700 pb-6">
                    <h3 class="text-xl font-semibold text-white mb-3">What is a color palette?</h3>
                    <p class="text-gray-300">A color palette is a selection of colors that work harmoniously together. It's used in design to create visual consistency and aesthetic appeal across projects.</p>
                </div>

                <div class="border-b border-gray-700 pb-6">
                    <h3 class="text-xl font-semibold text-white mb-3">How do I choose the right palette type?</h3>
                    <p class="text-gray-300">Monochromatic palettes use variations of one color, complementary uses opposite colors, triadic uses three evenly spaced colors, and analogous uses adjacent colors on the color wheel.</p>
                </div>

                <div class="border-b border-gray-700 pb-6">
                    <h3 class="text-xl font-semibold text-white mb-3">Can I use these colors in my projects?</h3>
                    <p class="text-gray-300">Yes! All generated colors are free to use in any personal or commercial project. You can copy the hex codes or export the palette as CSS variables.</p>
                </div>

                <div class="border-b border-gray-700 pb-6">
                    <h3 class="text-xl font-semibold text-white mb-3">What formats can I export?</h3>
                    <p class="text-gray-300">You can copy CSS variables directly or export the palette as JSON format for use in design tools and development projects.</p>
                </div>

                <div class="mb-0">
                    <h3 class="text-xl font-semibold text-white mb-3">How do I create accessible color combinations?</h3>
                    <p class="text-gray-300 mb-0">Ensure sufficient contrast between text and background colors. Use darker colors for text on light backgrounds and lighter colors for text on dark backgrounds.</p>
                </div>
            </div>
        </div>
    </div>

<script>
// Color Palette Generator functionality
document.addEventListener('DOMContentLoaded', function() {
    const colorPicker = document.getElementById('colorPicker');
    const colorInput = document.getElementById('colorInput');
    const paletteType = document.getElementById('paletteType');
    const colorCount = document.getElementById('colorCount');
    const colorCountValue = document.getElementById('colorCountValue');
    const generateBtn = document.getElementById('generateBtn');
    const randomColor = document.getElementById('randomColor');
    const paletteDisplay = document.getElementById('paletteDisplay');
    const cssOutput = document.getElementById('cssOutput');
    const copyPalette = document.getElementById('copyPalette');
    const exportPalette = document.getElementById('exportPalette');
    const copyTooltip = document.getElementById('copyTooltip');
    const exportTooltip = document.getElementById('exportTooltip');
    
    let currentPalette = [];

    // 同步颜色选择器和输入框
    colorPicker.addEventListener('change', () => {
        colorInput.value = colorPicker.value;
        generatePalette();
    });
    
    colorInput.addEventListener('input', () => {
        if (isValidHex(colorInput.value)) {
            colorPicker.value = colorInput.value;
            generatePalette();
        }
    });

    // 颜色数量滑块
    colorCount.addEventListener('input', () => {
        colorCountValue.textContent = colorCount.value;
        generatePalette();
    });

    // 调色板类型变化
    paletteType.addEventListener('change', generatePalette);

    // 生成按钮
    generateBtn.addEventListener('click', generatePalette);

    // 随机颜色
    randomColor.addEventListener('click', () => {
        const randomHex = '#' + Math.floor(Math.random()*16777215).toString(16).padStart(6, '0');
        colorPicker.value = randomHex;
        colorInput.value = randomHex;
        generatePalette();
    });

    // 复制CSS
    copyPalette.addEventListener('click', async () => {
        if (cssOutput.value) {
            try {
                await navigator.clipboard.writeText(cssOutput.value);
                
                if (copyTooltip) {
                    copyTooltip.textContent = 'Copied!';
                    copyTooltip.style.opacity = '1';
                    copyTooltip.style.visibility = 'visible';
                    
                    setTimeout(() => {
                        copyTooltip.textContent = 'Copy CSS variables';
                        copyTooltip.style.opacity = '0';
                        copyTooltip.style.visibility = 'hidden';
                    }, 2000);
                }
            } catch (error) {
                console.error('Failed to copy:', error);
            }
        }
    });

    // 导出JSON
    exportPalette.addEventListener('click', () => {
        if (currentPalette.length > 0) {
            const paletteData = {
                baseColor: colorInput.value,
                type: paletteType.value,
                colors: currentPalette
            };
            
            const blob = new Blob([JSON.stringify(paletteData, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'color-palette.json';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            if (exportTooltip) {
                exportTooltip.textContent = 'Exported!';
                exportTooltip.style.opacity = '1';
                exportTooltip.style.visibility = 'visible';
                
                setTimeout(() => {
                    exportTooltip.textContent = 'Export as JSON';
                    exportTooltip.style.opacity = '0';
                    exportTooltip.style.visibility = 'hidden';
                }, 2000);
            }
        }
    });

    function isValidHex(hex) {
        return /^#[0-9A-F]{6}$/i.test(hex);
    }

    function hexToHsl(hex) {
        const r = parseInt(hex.slice(1, 3), 16) / 255;
        const g = parseInt(hex.slice(3, 5), 16) / 255;
        const b = parseInt(hex.slice(5, 7), 16) / 255;

        const max = Math.max(r, g, b);
        const min = Math.min(r, g, b);
        let h, s, l = (max + min) / 2;

        if (max === min) {
            h = s = 0;
        } else {
            const d = max - min;
            s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
            switch (max) {
                case r: h = (g - b) / d + (g < b ? 6 : 0); break;
                case g: h = (b - r) / d + 2; break;
                case b: h = (r - g) / d + 4; break;
            }
            h /= 6;
        }

        return [h * 360, s * 100, l * 100];
    }

    function hslToHex(h, s, l) {
        h /= 360;
        s /= 100;
        l /= 100;

        const c = (1 - Math.abs(2 * l - 1)) * s;
        const x = c * (1 - Math.abs((h * 6) % 2 - 1));
        const m = l - c / 2;
        let r = 0, g = 0, b = 0;

        if (0 <= h && h < 1/6) {
            r = c; g = x; b = 0;
        } else if (1/6 <= h && h < 2/6) {
            r = x; g = c; b = 0;
        } else if (2/6 <= h && h < 3/6) {
            r = 0; g = c; b = x;
        } else if (3/6 <= h && h < 4/6) {
            r = 0; g = x; b = c;
        } else if (4/6 <= h && h < 5/6) {
            r = x; g = 0; b = c;
        } else if (5/6 <= h && h < 1) {
            r = c; g = 0; b = x;
        }

        r = Math.round((r + m) * 255);
        g = Math.round((g + m) * 255);
        b = Math.round((b + m) * 255);

        return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
    }

    function generatePalette() {
        const baseColor = colorInput.value;
        if (!isValidHex(baseColor)) return;

        const [h, s, l] = hexToHsl(baseColor);
        const count = parseInt(colorCount.value);
        const type = paletteType.value;
        
        currentPalette = [];
        
        switch (type) {
            case 'monochromatic':
                for (let i = 0; i < count; i++) {
                    const newL = Math.max(10, Math.min(90, l + (i - Math.floor(count/2)) * 15));
                    currentPalette.push(hslToHex(h, s, newL));
                }
                break;
            case 'analogous':
                for (let i = 0; i < count; i++) {
                    const newH = (h + (i - Math.floor(count/2)) * 30) % 360;
                    currentPalette.push(hslToHex(newH, s, l));
                }
                break;
            case 'complementary':
                currentPalette.push(baseColor);
                currentPalette.push(hslToHex((h + 180) % 360, s, l));
                for (let i = 2; i < count; i++) {
                    const newL = Math.max(10, Math.min(90, l + (i - 1) * 20));
                    currentPalette.push(hslToHex(h, s, newL));
                }
                break;
            case 'triadic':
                currentPalette.push(baseColor);
                currentPalette.push(hslToHex((h + 120) % 360, s, l));
                currentPalette.push(hslToHex((h + 240) % 360, s, l));
                for (let i = 3; i < count; i++) {
                    const newL = Math.max(10, Math.min(90, l + (i - 2) * 15));
                    currentPalette.push(hslToHex(h, s, newL));
                }
                break;
            case 'tetradic':
                currentPalette.push(baseColor);
                currentPalette.push(hslToHex((h + 90) % 360, s, l));
                currentPalette.push(hslToHex((h + 180) % 360, s, l));
                currentPalette.push(hslToHex((h + 270) % 360, s, l));
                for (let i = 4; i < count; i++) {
                    const newL = Math.max(10, Math.min(90, l + (i - 3) * 15));
                    currentPalette.push(hslToHex(h, s, newL));
                }
                break;
            case 'split-complementary':
                currentPalette.push(baseColor);
                currentPalette.push(hslToHex((h + 150) % 360, s, l));
                currentPalette.push(hslToHex((h + 210) % 360, s, l));
                for (let i = 3; i < count; i++) {
                    const newL = Math.max(10, Math.min(90, l + (i - 2) * 15));
                    currentPalette.push(hslToHex(h, s, newL));
                }
                break;
        }
        
        displayPalette();
        generateCSS();
    }

    function displayPalette() {
        paletteDisplay.innerHTML = '';
        
        currentPalette.forEach((color, index) => {
            const colorBlock = document.createElement('div');
            colorBlock.className = 'flex items-center space-x-3 p-3 bg-gray-700 border border-gray-600 hover:bg-gray-600 transition-colors cursor-pointer';
            colorBlock.innerHTML = `
                <div class="w-12 h-12 border border-gray-500" style="background-color: ${color}"></div>
                <div class="flex-1">
                    <div class="text-white font-mono">${color.toUpperCase()}</div>
                    <div class="text-gray-400 text-sm">Color ${index + 1}</div>
                </div>
                <button class="px-2 py-1 bg-gray-600 text-white text-xs hover:bg-gray-500 transition-colors" onclick="copyColor('${color}')">
                    Copy
                </button>
            `;
            paletteDisplay.appendChild(colorBlock);
        });
    }

    function generateCSS() {
        let css = ':root {\n';
        currentPalette.forEach((color, index) => {
            css += `  --color-${index + 1}: ${color};\n`;
        });
        css += '}';
        cssOutput.value = css;
    }

    // 全局函数用于复制单个颜色
    window.copyColor = async function(color) {
        try {
            await navigator.clipboard.writeText(color);
        } catch (error) {
            console.error('Failed to copy color:', error);
        }
    };

    // 初始生成
    generatePalette();
});
</script>

</div>

<?php include_once ROOT_PATH . '/templates/layout/footer.php'; ?>
