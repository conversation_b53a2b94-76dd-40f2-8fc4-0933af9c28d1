<?php
/**
 * 工具分类页面模板
 */

// 使用统一的数据库连接
require_once ROOT_PATH . '/includes/database-connection.php';

// 包含工具助手
require_once ROOT_PATH . '/app/helpers/tool-helpers.php';

// 从URL获取分类信息
$requestUri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
$pathParts = explode('/', trim($requestUri, '/'));
$categorySlug = $pathParts[1] ?? '';



// 直接从数据库查询分类数据，避免helper函数的问题
$category = null;
if (!empty($categorySlug)) {
    try {
        $stmt = $pdo->prepare("SELECT * FROM pt_tool_category WHERE slug = ? AND status = 'active' LIMIT 1");
        $stmt->execute([$categorySlug]);
        $categoryData = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($categoryData) {
            $category = [
                'id' => $categoryData['id'],
                'name' => $categoryData['name'],
                'slug' => $categoryData['slug'],
                'description' => $categoryData['description'],
                'icon' => $categoryData['icon'] ?? '📁',
                'sort_order' => $categoryData['sort_order']
            ];
        }
    } catch (Exception $e) {
        error_log("Database query failed: " . $e->getMessage());
    }
}

$tools = getToolsByCategory($categorySlug);

if (!$category) {
    // 分类不存在，显示404
    http_response_code(404);
    include ROOT_PATH . '/templates/pages/404.php';
    exit;
}

$currentPage = 'tools-category';
$breadcrumbs = [
    ['name' => 'Home', 'url' => '/'],
    ['name' => 'Tools', 'url' => '/tools'],
    ['name' => $category['name']]
];

// 包含动态SEO助手
require_once ROOT_PATH . '/app/helpers/dynamic-seo.php';

// 生成分类页面的SEO数据
$categorySlug = $category['slug'];
$categoryName = $category['name'];

// 为每个分类定制SEO内容
$seoContent = getCategorySEOContent($categorySlug, $categoryName, $category['description']);

$seoData = getDynamicSEOData('category', [
    'title' => $seoContent['title'],
    'description' => $seoContent['description'],
    'og_title' => $seoContent['og_title'],
    'og_description' => $seoContent['og_description']
]);

// 包含公共头部
include_once ROOT_PATH . '/templates/layout/header.php';
?>

<!-- 工具分类页面内容 -->
<section class="py-20 bg-black">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- 分类头部信息 -->
        <div class="text-center mb-16">
            <?php
            // 强制重新查询分类数据，确保正确性
            $stmt = $pdo->prepare("SELECT * FROM pt_tool_category WHERE slug = ? AND status = 'active' LIMIT 1");
            $stmt->execute([$categorySlug]);
            $freshCategory = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($freshCategory) {
                $displayCategory = [
                    'icon' => $freshCategory['icon'] ?? '❓',
                    'name' => $freshCategory['name'] ?? 'Unknown Category',
                    'description' => $freshCategory['description'] ?? 'No description available'
                ];
            } else {
                $displayCategory = [
                    'icon' => '❓',
                    'name' => 'Category Not Found',
                    'description' => 'The requested category could not be found.'
                ];
            }
            ?>
            <div class="text-6xl mb-6"><?= htmlspecialchars($displayCategory['icon']) ?></div>
            <h1 class="text-4xl md:text-5xl font-bold mb-6"><?= htmlspecialchars($displayCategory['name']) ?></h1>
            <p class="text-xl text-gray-400 max-w-3xl mx-auto mb-8">
                <?= htmlspecialchars($displayCategory['description']) ?>
            </p>
            
            <!-- 分类统计 -->
            <div class="flex justify-center gap-8 text-sm text-gray-400">
                <div class="flex items-center">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                    <?= count($tools) ?> tools available
                </div>
            </div>
        </div>
        
        <!-- 搜索和过滤 -->
        <div class="mb-12">
            <div class="max-w-2xl mx-auto">
                <div class="relative">
                    <input type="text" 
                           placeholder="Search tools in <?= htmlspecialchars($category['name']) ?>..." 
                           class="w-full px-6 py-4 bg-gray-900 border border-gray-700 text-white placeholder-gray-400 focus:border-accent focus:outline-none text-lg">
                    <div class="absolute right-4 top-4">
                        <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 工具网格 -->
        <?php if (!empty($tools)): ?>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
            <?php foreach ($tools as $tool): ?>
            <a href="/tools/<?= $categorySlug ?>/<?= $tool['slug'] ?>"
               class="bg-gray-900 border border-gray-800 p-8 hover:border-accent transition-all duration-300 group cursor-pointer block"
               data-tool-card
               data-tool-slug="<?= htmlspecialchars($tool['slug']) ?>"
               data-tool-category="<?= htmlspecialchars($categorySlug) ?>">
                
                <!-- 工具图标 -->
                <div class="text-5xl mb-6 group-hover:scale-110 transition-transform duration-300">
                    <?= htmlspecialchars($tool['icon']) ?>
                </div>
                
                <!-- 工具标题 -->
                <h3 class="text-2xl font-semibold mb-4 group-hover:text-accent transition-colors duration-300">
                    <?= htmlspecialchars($tool['name']) ?>
                </h3>
                
                <!-- 工具描述 -->
                <p class="text-gray-400 mb-6 leading-relaxed">
                    <?= htmlspecialchars($tool['description']) ?>
                </p>
                
                <!-- 工具元信息 -->
                <div class="flex items-center justify-between text-sm text-gray-500 mb-4">
                    <div class="flex items-center">
                        <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                        </svg>
                        <?= number_format($tool['view_count'] ?? 0) ?> views
                    </div>
                </div>
                
                <!-- 工具标签 -->
                <?php if (!empty($tool['tags'])): ?>
                <div class="flex flex-wrap gap-2 mb-6">
                    <?php
                    $tags = is_string($tool['tags']) ? explode(',', $tool['tags']) : $tool['tags'];
                    foreach (array_slice($tags, 0, 3) as $tag): ?>
                        <span class="bg-gray-800 text-gray-400 text-xs px-2 py-1">
                            <?= htmlspecialchars(trim($tag)) ?>
                        </span>
                    <?php endforeach; ?>
                </div>
                <?php endif; ?>
                
                <!-- 工具操作 -->
                <div class="flex items-center text-accent group-hover:translate-x-2 transition-transform duration-300">
                    <span class="mr-2">Try Tool</span>
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                    </svg>
                </div>
                
                <!-- 特色标签 -->
                <?php if ($tool['featured'] ?? false): ?>
                <div class="absolute top-4 right-4 bg-accent text-white text-xs px-2 py-1 font-semibold">
                    FEATURED
                </div>
                <?php endif; ?>

                <?php if ($tool['is_new'] ?? false): ?>
                <div class="absolute top-4 right-4 bg-success text-white text-xs px-2 py-1 font-semibold">
                    NEW
                </div>
                <?php endif; ?>
            </a>
            <?php endforeach; ?>
        </div>
        
        <?php else: ?>
        <!-- 空状态 -->
        <div class="text-center py-16">
            <div class="text-6xl mb-6">🔧</div>
            <h3 class="text-2xl font-bold mb-4">No Tools Available</h3>
            <p class="text-gray-400 mb-8">
                We're working on adding tools to this category. Check back soon!
            </p>
            <a href="/tools" class="bg-accent text-white px-8 py-4 text-lg font-medium hover:bg-blue-700 hover:text-white transition-all duration-200 transform hover:scale-105">
                Browse All Tools
            </a>
        </div>
        <?php endif; ?>
        
        <!-- 其他分类推荐 -->
        <div class="mt-20">
            <div class="flex items-center justify-between mb-8">
                <h3 class="text-3xl font-bold">Explore Other Categories</h3>
                <div class="flex gap-2">
                    <button id="prevBtn" class="p-2 bg-gray-800 hover:bg-gray-700 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                        </svg>
                    </button>
                    <button id="nextBtn" class="p-2 bg-gray-800 hover:bg-gray-700 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </button>
                </div>
            </div>

            <?php
            // 获取带工具数量的分类数据
            $allCategoriesWithCount = getTopToolCategories(20); // 获取更多分类用于滑动

            $otherCategories = array_filter($allCategoriesWithCount, function($cat) use ($categorySlug) {
                return $cat['slug'] !== $categorySlug && $cat['tool_count'] > 0;
            });

            // 打乱顺序，让每个分类页面显示不同的推荐分类
            shuffle($otherCategories);
            ?>

            <div class="relative overflow-hidden">
                <div id="categoriesSlider" class="flex gap-6 transition-transform duration-300 ease-in-out">
                <?php foreach ($otherCategories as $cat): ?>
                <div class="flex-none w-80">
                    <a href="/tools/<?= htmlspecialchars($cat['slug']) ?>"
                       class="bg-gray-900 border border-gray-800 p-8 hover:border-accent transition-all duration-300 group block h-full"
                       title="<?= htmlspecialchars($cat['name']) ?> - <?= $cat['tool_count'] ?> tools">

                        <!-- 工具数量徽章 -->
                        <div class="flex justify-between items-start mb-6">
                            <div class="text-5xl group-hover:scale-110 transition-transform duration-300">
                                <?= htmlspecialchars($cat['icon']) ?>
                            </div>
                            <div class="bg-accent text-white text-sm px-3 py-1 rounded-full font-medium">
                                <?= $cat['tool_count'] ?>
                            </div>
                        </div>

                        <h4 class="text-2xl font-semibold mb-3 group-hover:text-accent transition-colors duration-300">
                            <?= htmlspecialchars($cat['name']) ?>
                        </h4>
                        <p class="text-gray-400 text-base mb-6 leading-relaxed">
                            <?= htmlspecialchars($cat['description']) ?>
                        </p>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center text-accent group-hover:translate-x-2 transition-transform duration-300">
                                <span class="text-base mr-2 font-medium">Explore</span>
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                                </svg>
                            </div>
                            <span class="text-sm text-gray-500"><?= $cat['tool_count'] ?> tool<?= $cat['tool_count'] != 1 ? 's' : '' ?></span>
                        </div>
                    </a>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
</section>

<script>
// 分类滑动功能
document.addEventListener('DOMContentLoaded', function() {
    const slider = document.getElementById('categoriesSlider');
    const prevBtn = document.getElementById('prevBtn');
    const nextBtn = document.getElementById('nextBtn');

    if (!slider || !prevBtn || !nextBtn) return;

    let currentIndex = 0;
    const itemWidth = 320; // 卡片宽度 + gap
    const visibleItems = Math.floor(slider.parentElement.offsetWidth / itemWidth);
    const totalItems = slider.children.length;
    const maxIndex = Math.max(0, totalItems - visibleItems);

    // 更新按钮状态
    function updateButtons() {
        prevBtn.disabled = currentIndex <= 0;
        nextBtn.disabled = currentIndex >= maxIndex;

        prevBtn.classList.toggle('opacity-50', currentIndex <= 0);
        nextBtn.classList.toggle('opacity-50', currentIndex >= maxIndex);
    }

    // 滑动到指定位置
    function slideTo(index) {
        currentIndex = Math.max(0, Math.min(index, maxIndex));
        const translateX = -currentIndex * itemWidth;
        slider.style.transform = `translateX(${translateX}px)`;
        updateButtons();
    }

    // 向左滑动
    prevBtn.addEventListener('click', function() {
        slideTo(currentIndex - 1);
    });

    // 向右滑动
    nextBtn.addEventListener('click', function() {
        slideTo(currentIndex + 1);
    });

    // 初始化
    updateButtons();

    // 响应式处理
    window.addEventListener('resize', function() {
        const newVisibleItems = Math.floor(slider.parentElement.offsetWidth / itemWidth);
        const newMaxIndex = Math.max(0, totalItems - newVisibleItems);

        if (currentIndex > newMaxIndex) {
            slideTo(newMaxIndex);
        } else {
            updateButtons();
        }
    });
});
</script>

<?php
// 包含公共底部
include_once ROOT_PATH . '/templates/layout/footer.php';
?>
