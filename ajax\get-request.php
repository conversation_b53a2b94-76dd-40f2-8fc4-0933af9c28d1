<?php
/**
 * 获取用户需求详情
 */

session_start();
header('Content-Type: application/json');

// 检查用户登录
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'Please login first']);
    exit;
}

// 检查请求ID
$requestId = intval($_GET['id'] ?? 0);
if ($requestId <= 0) {
    echo json_encode(['success' => false, 'message' => 'Invalid request ID']);
    exit;
}

// 使用统一的数据库连接
require_once __DIR__ . '/includes/ajax-database.php';

try {

    // 获取需求详情（只能获取自己的需求）
    $stmt = $pdo->prepare("
        SELECT id, title, description, category, priority, status, votes, created_at, updated_at
        FROM pt_user_requests 
        WHERE id = ? AND user_id = ?
    ");
    $stmt->execute([$requestId, $_SESSION['user_id']]);
    $request = $stmt->fetch();

    if ($request) {
        echo json_encode([
            'success' => true,
            'request' => $request
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Request not found or access denied'
        ]);
    }

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Database error: ' . $e->getMessage()
    ]);
}
?>
