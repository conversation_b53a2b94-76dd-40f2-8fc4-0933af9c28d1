<?php
/**
 * Google Suggest API 代理
 * 用于获取Google搜索建议，解决CORS问题
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// 检查登录状态
session_start();
if (!isset($_SESSION['admin_logged_in']) || !$_SESSION['admin_logged_in']) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Unauthorized']);
    exit;
}

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => 'Method not allowed']);
    exit;
}

// 获取请求数据
$input = json_decode(file_get_contents('php://input'), true);

if (!$input || !isset($input['keyword']) || !isset($input['source'])) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => 'Missing required parameters']);
    exit;
}

$keyword = trim($input['keyword']);
$source = $input['source'];

if (empty($keyword)) {
    echo json_encode(['success' => false, 'error' => 'Keyword cannot be empty']);
    exit;
}

// API端点配置
$endpoints = [
    'firefox' => 'https://suggestqueries.google.com/complete/search?client=firefox&q=',
    'chrome' => 'https://suggestqueries.google.com/complete/search?client=chrome&q=',
    'toolbar' => 'https://suggestqueries.google.com/complete/search?output=toolbar&q=',
    'youtube' => 'https://suggestqueries.google.com/complete/search?client=firefox&ds=yt&q='
];

if (!isset($endpoints[$source])) {
    echo json_encode(['success' => false, 'error' => 'Invalid API source']);
    exit;
}

try {
    // 构建请求URL
    $url = $endpoints[$source] . urlencode($keyword);
    
    // 设置cURL选项
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_TIMEOUT => 10,
        CURLOPT_USERAGENT => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_SSL_VERIFYHOST => false
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        throw new Exception("cURL error: " . $error);
    }
    
    if ($httpCode !== 200) {
        throw new Exception("HTTP error: " . $httpCode);
    }
    
    if (empty($response)) {
        throw new Exception("Empty response from Google");
    }
    
    // 解析响应
    $suggestions = [];
    
    switch ($source) {
        case 'firefox':
        case 'youtube':
            // Firefox和YouTube返回简单的JSON数组
            $data = json_decode($response, true);
            if ($data && isset($data[1]) && is_array($data[1])) {
                $suggestions = $data[1];
            }
            break;
            
        case 'chrome':
            // Chrome返回更复杂的JSON结构
            $data = json_decode($response, true);
            if ($data && isset($data[1]) && is_array($data[1])) {
                foreach ($data[1] as $item) {
                    if (is_array($item) && isset($item[0])) {
                        $suggestions[] = $item[0];
                    } elseif (is_string($item)) {
                        $suggestions[] = $item;
                    }
                }
            }
            break;
            
        case 'toolbar':
            // Toolbar返回XML格式
            $xml = simplexml_load_string($response);
            if ($xml) {
                foreach ($xml->CompleteSuggestion as $suggestion) {
                    if (isset($suggestion->suggestion['data'])) {
                        $suggestions[] = (string)$suggestion->suggestion['data'];
                    }
                }
            }
            break;
    }
    
    // 清理和去重
    $suggestions = array_unique(array_filter(array_map('trim', $suggestions)));
    $suggestions = array_values($suggestions); // 重新索引
    
    // 记录使用日志
    error_log("Google Suggest API used - Keyword: {$keyword}, Source: {$source}, Results: " . count($suggestions));
    
    echo json_encode([
        'success' => true,
        'keyword' => $keyword,
        'source' => $source,
        'suggestions' => $suggestions,
        'count' => count($suggestions),
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    
} catch (Exception $e) {
    error_log("Google Suggest API error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Failed to fetch suggestions: ' . $e->getMessage()
    ]);
}
?>
