<?php
/**
 * 更新产品信息的AJAX处理
 */

header('Content-Type: application/json');

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit;
}

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// 检查用户登录状态
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

$userId = $_SESSION['user_id'];
$productId = $_POST['product_id'] ?? null;

if (!$productId) {
    echo json_encode(['success' => false, 'message' => 'Product ID is required']);
    exit;
}

try {
    // 数据库连接 - 使用配置文件
    if (!defined('ROOT_PATH')) {
        define('ROOT_PATH', dirname(dirname(__DIR__)));
    }

    // 定义APP_INITIALIZED常量以允许访问配置文件
    if (!defined('APP_INITIALIZED')) {
        define('APP_INITIALIZED', true);
    }

    // 检查配置文件是否存在
    $configPath = ROOT_PATH . '/config/database.php';
    if (!file_exists($configPath)) {
        echo json_encode(['success' => false, 'message' => 'Database configuration not found']);
        exit;
    }

    // 使用统一的数据库连接
    require_once ROOT_PATH . '/includes/database-connection.php';

    // 验证产品是否属于当前用户
    $checkStmt = $pdo->prepare("SELECT id FROM pt_product_launches WHERE id = ? AND user_id = ?");
    $checkStmt->execute([$productId, $userId]);
    
    if (!$checkStmt->fetch()) {
        echo json_encode(['success' => false, 'message' => 'Product not found or access denied']);
        exit;
    }

    // 验证必填字段
    $requiredFields = ['product_name', 'website_url', 'description', 'category', 'pricing_model', 'launch_status'];
    foreach ($requiredFields as $field) {
        if (empty($_POST[$field])) {
            echo json_encode(['success' => false, 'message' => "Field '{$field}' is required"]);
            exit;
        }
    }

    // 处理标签、功能和用例（转换为JSON数组）
    $tags = !empty($_POST['tags']) ? 
        array_map('trim', explode(',', $_POST['tags'])) : [];
    
    $keyFeatures = !empty($_POST['key_features']) ? 
        array_map('trim', explode(',', $_POST['key_features'])) : [];
    
    $useCases = !empty($_POST['use_cases']) ? 
        array_map('trim', explode(',', $_POST['use_cases'])) : [];

    // 处理社交链接
    $socialLinks = [];
    $socialFields = ['twitter', 'github', 'linkedin', 'website'];
    foreach ($socialFields as $field) {
        $value = $_POST["social_{$field}"] ?? '';
        if (!empty($value)) {
            $socialLinks[$field] = $value;
        }
    }

    // 生成新的slug（如果产品名称改变了）
    $currentStmt = $pdo->prepare("SELECT name, slug FROM pt_product_launches WHERE id = ?");
    $currentStmt->execute([$productId]);
    $currentProduct = $currentStmt->fetch();

    $slug = $currentProduct['slug']; // 默认保持原有slug
    if ($currentProduct['name'] !== $_POST['product_name']) {
        // 产品名称改变了，生成新的slug
        $baseSlug = strtolower(trim(preg_replace('/[^A-Za-z0-9-]+/', '-', $_POST['product_name'])));
        $slug = $baseSlug;
        
        // 检查slug是否已存在
        $counter = 1;
        while (true) {
            $slugCheckStmt = $pdo->prepare("SELECT id FROM pt_product_launches WHERE slug = ? AND id != ?");
            $slugCheckStmt->execute([$slug, $productId]);
            
            if (!$slugCheckStmt->fetch()) {
                break; // slug可用
            }
            
            $slug = $baseSlug . '-' . $counter;
            $counter++;
        }
    }

    // 处理截图数据
    $screenshots = [];
    if (!empty($_POST['screenshots'])) {
        $screenshotData = json_decode($_POST['screenshots'], true);
        if (is_array($screenshotData)) {
            // 服务端强制限制最多3张
            $screenshots = array_slice($screenshotData, 0, 3);
        }
    }

    // 更新产品信息
    $updateStmt = $pdo->prepare("
        UPDATE pt_product_launches SET
            name = ?,
            slug = ?,
            tagline = ?,
            description = ?,
            website_url = ?,
            logo_url = ?,
            category = ?,
            tech_category = ?,
            tags = ?,
            key_features = ?,
            target_audience = ?,
            use_cases = ?,
            pricing_model = ?,
            launch_status = ?,
            social_links = ?,
            video_tutorial_url = ?,
            screenshots = ?,
            updated_at = NOW()
        WHERE id = ? AND user_id = ?
    ");

    // 获取当前产品的launch_status，保持不变（只有管理员可以修改）
    $currentStatusStmt = $pdo->prepare("SELECT launch_status FROM pt_product_launches WHERE id = ? AND user_id = ?");
    $currentStatusStmt->execute([$productId, $userId]);
    $currentProduct = $currentStatusStmt->fetch();
    $currentLaunchStatus = $currentProduct['launch_status'] ?? 'coming-soon';

    $result = $updateStmt->execute([
        $_POST['product_name'],
        $slug,
        $_POST['tagline'] ?? null,
        $_POST['description'],
        $_POST['website_url'],
        $_POST['logo_url'] ?? null,
        $_POST['category'],
        $_POST['tech_category'] ?? null,
        json_encode($tags),
        json_encode($keyFeatures),
        $_POST['target_audience'] ?? null,
        json_encode($useCases),
        $_POST['pricing_model'],
        $currentLaunchStatus, // 保持数据库中的现有状态，忽略前端提交的值
        json_encode($socialLinks),
        $_POST['video_tutorial_url'] ?? null,
        json_encode($screenshots),
        $productId,
        $userId
    ]);

    if ($result) {
        echo json_encode([
            'success' => true,
            'message' => 'Product updated successfully',
            'slug' => $slug
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Failed to update product']);
    }

} catch (PDOException $e) {
    error_log("Database error in update-product.php: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Database connection error: ' . $e->getMessage()]);
} catch (Exception $e) {
    error_log("General error in update-product.php: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'An error occurred: ' . $e->getMessage()]);
}
?>
