<?php
/**
 * 用户中心头部组件
 */

$currentPage = $_GET['page'] ?? 'dashboard';

$pageTitles = [
    'dashboard' => 'Dashboard',
    'profile' => 'Profile',
    'api-usage' => 'Usage',
    'my-requests' => 'Requests',
    'my-products' => 'Products',
    'edit-product' => 'Edit Product',
    'prompt-generator' => 'Prompts',
    'tool-generator' => 'Tools',
    'my-tools' => 'My Tools'
];

$pageTitle = $pageTitles[$currentPage] ?? 'User Center';
$pageDescription = '';

switch($currentPage) {
    case 'dashboard':
        $pageDescription = 'Overview of your account and recent activity';
        break;
    case 'profile':
        $pageDescription = 'Manage your personal information and preferences';
        break;
    case 'api-usage':
        $pageDescription = 'Monitor your API usage and quota';
        break;
    case 'my-requests':
        $pageDescription = 'View your tool requests';
        break;
    case 'my-products':
        $pageDescription = 'Manage your submitted products';
        break;
    case 'edit-product':
        $pageDescription = 'Update product information';
        break;
    case 'prompt-generator':
        $pageDescription = 'Create detailed prompts for tool development';
        break;
    case 'tool-generator':
        $pageDescription = 'Generate custom tools with AI assistance';
        break;
    case 'my-tools':
        $pageDescription = 'Manage your created tools and track their performance';
        break;
    default:
        $pageDescription = 'User center dashboard';
}
?>

<div class="flex items-center justify-between h-16 px-6">
    <!-- 页面标题 -->
    <div class="flex items-center">
        <h1 class="text-2xl font-semibold text-gray-900"><?= htmlspecialchars($pageTitle) ?></h1>
        <?php if ($pageDescription): ?>
            <span class="ml-3 text-sm text-gray-500">•</span>
            <span class="ml-3 text-sm text-gray-500"><?= htmlspecialchars($pageDescription) ?></span>
        <?php endif; ?>
    </div>

    <!-- 右侧用户菜单 -->
    <div class="flex items-center space-x-4">
        <!-- 用户头像和信息 -->
        <div class="flex items-center space-x-3">
            <div class="flex items-center space-x-2">
                <?php if (!empty($user['avatar'])): ?>
                    <img src="<?= htmlspecialchars($user['avatar']) ?>"
                         alt="Profile Photo"
                         class="w-8 h-8 object-cover border border-gray-300"
                         id="header-avatar">
                <?php else: ?>
                    <div class="w-8 h-8 bg-black text-white flex items-center justify-center font-bold text-sm" id="header-avatar">
                        <?= strtoupper(substr($user['first_name'] ?? $user['username'] ?? 'U', 0, 1)) ?>
                    </div>
                <?php endif; ?>

            </div>

            <!-- 下拉菜单按钮 -->
            <div class="relative">
                <button class="flex items-center text-sm text-gray-500 hover:text-gray-700" onclick="toggleUserMenu()">
                    <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>

                <!-- 下拉菜单 -->
                <div id="userMenu" class="hidden absolute right-0 mt-2 w-48 bg-white shadow-lg py-1 z-50 border border-gray-200">
                    <a href="?page=profile" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        <i class="fas fa-user mr-2"></i>
                        Profile
                    </a>

                    <div class="border-t border-gray-100"></div>
                    <a href="/auth/logout" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        <i class="fas fa-sign-out-alt mr-2"></i>
                        Logout
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function toggleUserMenu() {
    const menu = document.getElementById('userMenu');
    menu.classList.toggle('hidden');
}

document.addEventListener('click', function(event) {
    const menu = document.getElementById('userMenu');
    const button = event.target.closest('button');
    
    if (!button || !button.onclick) {
        menu.classList.add('hidden');
    }
});
</script>
