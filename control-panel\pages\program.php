<?php
/**
 * Online Tool Generator Page
 * Generate custom tools with AI
 */

// 检查登录状态
if (!isset($_SESSION['admin_logged_in']) || !$_SESSION['admin_logged_in']) {
    header('Location: /control-panel/login.php');
    exit;
}

// 获取工具分类
try {
    $stmt = $pdo->prepare("SELECT name FROM pt_tool_category WHERE status = 'active' ORDER BY name ASC");
    $stmt->execute();
    $toolCategories = $stmt->fetchAll(PDO::FETCH_COLUMN);
} catch (Exception $e) {
    $toolCategories = [];
}

// 获取AiHubMix的APP-Code配置
try {
    $stmt = $pdo->prepare("
        SELECT config_value
        FROM pt_service_config sc
        JOIN pt_service_platform sp ON sc.platform_id = sp.id
        WHERE sp.code = 'aihubmix' AND sc.config_key = 'app_code'
    ");
    $stmt->execute();
    $appCodeData = $stmt->fetch();

    // 设置APP-Code，优先使用数据库配置，备用默认值
    $appCode = $appCodeData ? $appCodeData['config_value'] : 'KXTM3281';
} catch (Exception $e) {
    // 数据库查询失败时使用默认值
    $appCode = 'KXTM3281';
}
?>

<!-- Tool Generator Interface - Full Height -->
<div class="bg-white shadow rounded-lg overflow-hidden flex" style="width: 100%; max-width: 100%; height: calc(100vh - 120px); max-height: calc(100vh - 120px);">

    <!-- Left Panel - Input Section -->
    <div class="w-1/2 border-r border-gray-200 flex flex-col" style="height: calc(100vh - 120px); max-height: calc(100vh - 120px);">
        <!-- Header -->
        <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-indigo-50 flex-shrink-0">
            <h2 class="text-xl font-semibold text-gray-900 flex items-center">
                <svg class="w-6 h-6 mr-3 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                </svg>
                Online Tool Generator
            </h2>
            <p class="text-sm text-gray-600 mt-1">Create custom tools with AI</p>
        </div>

        <!-- Form Content -->
        <div class="flex-1 p-6 overflow-y-auto bg-gray-50" style="height: 0;">
            <!-- Name -->
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Name *</label>
                <input type="text" id="toolName"
                       value="Tool-<?= date('YmdHis') ?>"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white">
            </div>

            <!-- Slug -->
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Slug *</label>
                <input type="text" id="toolSlug"
                       value="tool-<?= date('YmdHis') ?>"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white">
            </div>

            <!-- Category -->
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Category</label>
                <select id="toolCategory" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white">
                    <option value="">Select Category</option>
                    <?php foreach ($toolCategories as $category): ?>
                        <option value="<?= htmlspecialchars($category) ?>" <?= $category === 'AI' ? 'selected' : '' ?>><?= htmlspecialchars($category) ?></option>
                    <?php endforeach; ?>
                </select>
            </div>

            <!-- Icon -->
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Icon (Emoji)</label>
                <input type="text" id="toolIcon" value="🔧"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white">
            </div>

            <!-- Description -->
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                <textarea id="toolDescription"
                          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent h-20 resize-none bg-white">Generated tool description - <?= date('Y-m-d H:i:s') ?></textarea>
            </div>

            <!-- Tags -->
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Tags (comma separated)</label>
                <input type="text" id="toolTags"
                       value="generated, tool"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white">
            </div>

            <!-- Status -->
            <div class="mb-6">
                <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                <select id="toolStatus" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white">
                    <option value="coming_soon" selected>Coming Soon</option>
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                </select>
            </div>

            <!-- Tool Prompt -->
            <div class="mb-6">
                <div class="flex items-center justify-between mb-2">
                    <label class="block text-sm font-medium text-gray-700">Tool Generation Prompt</label>
                    <div class="flex space-x-2">
                        <input type="file" id="fileInput" accept=".md,.txt" class="hidden">
                        <button id="importFile" class="text-xs bg-gray-600 hover:bg-gray-700 text-white px-3 py-1 rounded">
                            <i class="fas fa-file-import mr-1"></i>Import MD/TXT
                        </button>
                    </div>
                </div>
                <textarea id="toolPrompt"
                          placeholder="Describe what your tool should do. Be specific about inputs, outputs, and functionality. You can paste the generated specification from the Prompt page here..."
                          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent h-48 resize-y bg-white"></textarea>
                <div class="text-xs text-gray-500 mt-1">
                    <span id="promptCount">0</span>/5000 characters • Supports importing .md and .txt files
                </div>
            </div>

            <!-- Generate Button -->
            <button id="generateTool"
                    class="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
                <span id="generateText">
                    <i class="fas fa-magic mr-2"></i>Generate Tool with AI
                </span>
                <span id="loadingText" class="hidden">
                    <i class="fas fa-spinner fa-spin mr-2"></i>Generating... Please wait
                </span>
            </button>
        </div>
    </div>
    
    <!-- Right Panel - Code Output -->
    <div class="w-1/2 flex flex-col" style="height: calc(100vh - 120px); max-height: calc(100vh - 120px);">
        <!-- Header -->
        <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-gray-50 to-slate-50 flex-shrink-0">
            <h2 class="text-xl font-semibold text-gray-900 flex items-center">
                <svg class="w-6 h-6 mr-3 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
                </svg>
                Generated Code
            </h2>
            <p class="text-sm text-gray-600 mt-1">Your AI-generated tool code will appear here</p>
        </div>

        <!-- Code Content -->
        <div class="flex-1 p-2 bg-gray-50 overflow-hidden flex flex-col" style="height: 0;">
            <!-- Status Display -->
            <div id="statusDisplay" class="mb-2 p-2 bg-white border-l-4 border-gray-400 rounded-r-md hidden flex-shrink-0">
                <div class="flex items-center">
                    <i class="fas fa-info-circle text-gray-500 mr-2"></i>
                    <p class="text-gray-700"></p>
                </div>
            </div>

            <!-- Code Output -->
            <div class="flex-1 bg-gray-900 rounded-lg overflow-hidden flex flex-col min-h-0">
                <div class="flex items-center justify-between bg-gray-800 px-4 py-2 flex-shrink-0">
                    <span class="text-sm text-gray-300 flex items-center">
                        <span id="generationTime" class="hidden text-xs bg-gray-700 px-2 py-1 rounded mr-2">0s</span>
                    </span>
                    <div class="flex space-x-2">
                        <button id="copyCode" class="text-xs bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded disabled:opacity-50" disabled>
                            <i class="fas fa-copy mr-1"></i> Copy
                        </button>
                        <button id="downloadCode" class="text-xs bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded disabled:opacity-50" disabled>
                            <i class="fas fa-download mr-1"></i> Download
                        </button>
                        <button id="deployCode" class="text-xs bg-purple-600 hover:bg-purple-700 text-white px-3 py-1 rounded disabled:opacity-50" disabled>
                            <i class="fas fa-rocket mr-1"></i> Deploy
                        </button>
                    </div>
                </div>
                <div id="codeOutput" class="flex-1 p-4 overflow-auto min-h-0" style="max-width: 100%; word-break: break-all;">
                    <pre class="text-gray-400 text-sm whitespace-pre-wrap" style="word-break: break-all; overflow-wrap: break-word;">Your generated code will appear here...</pre>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// PHP数据传递到JavaScript
const appCode = '<?= $appCode ?>';

// Program Generator functionality
document.addEventListener('DOMContentLoaded', function() {
    const toolName = document.getElementById('toolName');
    const toolSlug = document.getElementById('toolSlug');
    const toolCategory = document.getElementById('toolCategory');
    const toolIcon = document.getElementById('toolIcon');
    const toolDescription = document.getElementById('toolDescription');
    const toolTags = document.getElementById('toolTags');
    const toolStatus = document.getElementById('toolStatus');
    const toolPrompt = document.getElementById('toolPrompt');
    const promptCount = document.getElementById('promptCount');
    const fileInput = document.getElementById('fileInput');
    const importFile = document.getElementById('importFile');
    const generateTool = document.getElementById('generateTool');
    const generateText = document.getElementById('generateText');
    const loadingText = document.getElementById('loadingText');
    const statusDisplay = document.getElementById('statusDisplay');
    const codeOutput = document.getElementById('codeOutput');
    const copyCode = document.getElementById('copyCode');
    const downloadCode = document.getElementById('downloadCode');
    const deployCode = document.getElementById('deployCode');
    const generationTime = document.getElementById('generationTime');

    let isGenerating = false;
    let generatedCode = '';

    // Character counter for prompt
    toolPrompt.addEventListener('input', function() {
        const count = this.value.length;
        promptCount.textContent = count;

        if (count > 5000) {
            this.value = this.value.substring(0, 5000);
            promptCount.textContent = 5000;
        }

        updateGenerateButton();
    });

    // Auto-generate slug from name
    toolName.addEventListener('input', function() {
        const slug = this.value.toLowerCase()
            .replace(/[^a-z0-9\s-]/g, '')
            .replace(/\s+/g, '-')
            .replace(/-+/g, '-')
            .trim();
        toolSlug.value = slug;
        updateGenerateButton();
    });

    // Update generate button state
    function updateGenerateButton() {
        const hasName = toolName.value.trim() !== '';
        const hasSlug = toolSlug.value.trim() !== '';
        const hasPrompt = toolPrompt.value.trim().length >= 10;

        generateTool.disabled = !hasName || !hasSlug || !hasPrompt || isGenerating;
    }

    // File import functionality
    importFile.addEventListener('click', function() {
        fileInput.click();
    });

    fileInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (!file) return;

        const reader = new FileReader();
        reader.onload = function(e) {
            const content = e.target.result;
            toolPrompt.value = content;

            // Update character count
            const count = content.length;
            promptCount.textContent = count;

            if (count > 5000) {
                toolPrompt.value = content.substring(0, 5000);
                promptCount.textContent = 5000;
            }

            updateGenerateButton();
        };

        reader.onerror = function() {
            alert('Error reading file. Please try again.');
        };

        reader.readAsText(file);
    });

    // Event listeners for form validation
    toolName.addEventListener('input', updateGenerateButton);
    toolSlug.addEventListener('input', updateGenerateButton);
    toolPrompt.addEventListener('input', updateGenerateButton);

    // Generate tool
    generateTool.addEventListener('click', async function() {
        if (isGenerating) return;
        
        isGenerating = true;
        generateTool.disabled = true;
        generateText.classList.add('hidden');
        loadingText.classList.remove('hidden');
        
        // Update status
        updateStatus('info', 'Generating your tool with AI...');
        
        const startTime = Date.now();
        
        try {
            // Prepare request data
            const requestData = {
                name: toolName.value,
                slug: toolSlug.value,
                category: toolCategory.value,
                icon: toolIcon.value,
                description: toolDescription.value,
                tags: toolTags.value,
                status: toolStatus.value,
                prompt: toolPrompt.value,
                model: 'claude-sonnet-4-20250514',
                appCode: appCode
            };

            // Make streaming API request
            const response = await fetch('../api/generate-tool.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestData)
            });

            if (!response.ok) {
                throw new Error('Generation failed');
            }

            // Handle streaming response
            const reader = response.body.getReader();
            const decoder = new TextDecoder();
            generatedCode = '';
            codeOutput.innerHTML = '<pre class="text-green-400 text-sm whitespace-pre-wrap" style="word-break: break-all; overflow-wrap: break-word;"></pre>';
            const preElement = codeOutput.querySelector('pre');

            try {
                while (true) {
                    const { done, value } = await reader.read();

                    if (done) break;

                    const chunk = decoder.decode(value, { stream: true });
                    const lines = chunk.split('\n');

                    for (const line of lines) {
                        if (line.startsWith('data: ')) {
                            const data = line.slice(6);
                            if (data === '[DONE]') {
                                // Stream completed
                                updateStatus('success', 'Tool generated successfully!');

                                // Enable action buttons
                                copyCode.disabled = false;
                                downloadCode.disabled = false;
                                deployCode.disabled = false;

                                // Show generation info
                                const endTime = Date.now();
                                const duration = ((endTime - startTime) / 1000).toFixed(1);
                                generationTime.textContent = duration + 's';
                                generationTime.classList.remove('hidden');

                                return;
                            }

                            try {
                                const parsed = JSON.parse(data);
                                if (parsed.choices && parsed.choices[0] && parsed.choices[0].delta && parsed.choices[0].delta.content) {
                                    const content = parsed.choices[0].delta.content;
                                    generatedCode += content;
                                    preElement.textContent = generatedCode;

                                    // Auto-scroll to bottom
                                    codeOutput.scrollTop = codeOutput.scrollHeight;
                                }
                            } catch (e) {
                                // Ignore JSON parse errors for partial chunks
                            }
                        }
                    }
                }
            } finally {
                reader.releaseLock();
            }
            
        } catch (error) {
            updateStatus('error', 'Generation failed. Please try again.');
            codeOutput.innerHTML = '<pre class="text-red-400 text-sm whitespace-pre-wrap" style="word-break: break-all; overflow-wrap: break-word;">Error: ' + error.message + '</pre>';
        } finally {
            isGenerating = false;
            generateTool.disabled = false;
            generateText.classList.remove('hidden');
            loadingText.classList.add('hidden');
            updateGenerateButton();
        }
    });

    // Copy code
    copyCode.addEventListener('click', async function() {
        try {
            await navigator.clipboard.writeText(generatedCode);
            this.innerHTML = '<i class="fas fa-check mr-1"></i> Copied!';
            this.classList.add('bg-green-600');
            this.classList.remove('bg-blue-600');
            
            setTimeout(() => {
                this.innerHTML = '<i class="fas fa-copy mr-1"></i> Copy';
                this.classList.remove('bg-green-600');
                this.classList.add('bg-blue-600');
            }, 2000);
        } catch (error) {
            console.error('Copy failed:', error);
        }
    });

    // Download code
    downloadCode.addEventListener('click', function() {
        const blob = new Blob([generatedCode], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${toolSlug.value || 'tool'}.php`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    });

    // Deploy code
    deployCode.addEventListener('click', async function() {
        if (!generatedCode) {
            alert('No code to deploy');
            return;
        }

        const originalText = this.innerHTML;
        this.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i> Deploying...';
        this.disabled = true;

        try {
            // Get category ID
            let categoryId = null;
            if (toolCategory.value) {
                const categoryResponse = await fetch('../api/get-category-id.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ category: toolCategory.value })
                });
                const categoryResult = await categoryResponse.json();
                if (categoryResult.success) {
                    categoryId = categoryResult.category_id;
                }
            }

            // Deploy tool
            const deployData = {
                name: toolName.value,
                slug: toolSlug.value,
                description: toolDescription.value,
                category_id: categoryId,
                icon: toolIcon.value,
                tags: toolTags.value,
                status: toolStatus.value,
                code: generatedCode
            };

            const response = await fetch('../api/deploy-tool.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(deployData)
            });

            const result = await response.json();

            if (result.success) {
                this.innerHTML = '<i class="fas fa-check mr-1"></i> Deployed!';
                this.classList.remove('bg-purple-600', 'hover:bg-purple-700');
                this.classList.add('bg-green-600', 'hover:bg-green-700');

                updateStatus('success', `Tool deployed successfully! File saved as ${result.filename}`);

                setTimeout(() => {
                    this.innerHTML = originalText;
                    this.classList.remove('bg-green-600', 'hover:bg-green-700');
                    this.classList.add('bg-purple-600', 'hover:bg-purple-700');
                    this.disabled = false;
                }, 3000);
            } else {
                throw new Error(result.error || 'Deployment failed');
            }

        } catch (error) {
            console.error('Deploy error:', error);
            updateStatus('error', 'Deployment failed: ' + error.message);
            this.innerHTML = originalText;
            this.disabled = false;
        } finally {
            // Ensure page layout remains intact
            document.body.style.overflow = '';
            document.documentElement.style.overflow = '';
        }
    });

    // Utility functions
    function updateStatus(type, message) {
        const icons = {
            info: 'fas fa-info-circle text-blue-500',
            success: 'fas fa-check-circle text-green-500',
            error: 'fas fa-exclamation-circle text-red-500'
        };

        const colors = {
            info: 'bg-blue-50',
            success: 'bg-green-50',
            error: 'bg-red-50'
        };

        statusDisplay.className = `mb-2 p-2 rounded-md ${colors[type]} flex-shrink-0`;
        statusDisplay.innerHTML = `
            <div class="flex items-center">
                <i class="${icons[type]} mr-2"></i>
                <p class="text-gray-700">${message}</p>
            </div>
        `;
        statusDisplay.classList.remove('hidden');
    }

    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // Initialize
    updateGenerateButton();
});
</script>
