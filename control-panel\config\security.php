<?php
/**
 * 安全配置文件
 * 定义系统的安全策略和防护措施
 */

// 防止直接访问
if (!defined('ROOT_PATH')) {
    exit('Direct access not allowed');
}

return [
    // 会话安全配置
    'session' => [
        'name' => 'PROMPT2TOOL_ADMIN_SESSION',
        'lifetime' => 3600,                    // 会话生命周期(秒)
        'cookie_httponly' => true,             // 仅HTTP访问Cookie
        'cookie_secure' => true,               // 仅HTTPS传输Cookie
        'cookie_samesite' => 'Strict',         // 防止CSRF攻击
        'use_strict_mode' => true,             // 严格模式
        'regenerate_id_interval' => 300,       // 会话ID重新生成间隔(秒)
        'gc_maxlifetime' => 7200,              // 垃圾回收最大生命周期
        'entropy_length' => 32,                // 熵长度
        'hash_function' => 'sha256'            // 哈希函数
    ],

    // 密码安全策略
    'password' => [
        'algorithm' => PASSWORD_ARGON2ID,      // 密码哈希算法
        'options' => [
            'memory_cost' => 65536,            // 内存成本(KB)
            'time_cost' => 4,                  // 时间成本
            'threads' => 3                     // 线程数
        ],
        'min_length' => 8,                     // 最小长度
        'max_length' => 128,                   // 最大长度
        'require_uppercase' => true,           // 要求大写字母
        'require_lowercase' => true,           // 要求小写字母
        'require_numbers' => true,             // 要求数字
        'require_symbols' => true,             // 要求特殊字符
        'forbidden_patterns' => [              // 禁止的模式
            'password',
            '123456',
            'admin',
            'prompt2tool'
        ],
        'history_count' => 5,                  // 密码历史记录数量
        'expiry_days' => 90                    // 密码过期天数
    ],

    // 登录安全配置
    'login' => [
        'max_attempts' => 5,                   // 最大尝试次数
        'lockout_duration' => 900,             // 锁定时长(秒)
        'progressive_delay' => true,           // 渐进式延迟
        'delay_base' => 2,                     // 延迟基数(秒)
        'captcha_threshold' => 3,              // 验证码阈值
        'two_factor_required' => false,        // 是否要求双因素认证
        'remember_me_duration' => 2592000,     // 记住我持续时间(30天)
        'session_fingerprint' => true,         // 会话指纹验证
        'ip_validation' => true,               // IP地址验证
        'user_agent_validation' => true       // 用户代理验证
    ],

    // IP访问控制
    'ip_control' => [
        'whitelist_enabled' => false,         // 是否启用白名单
        'whitelist' => [                      // IP白名单
            '127.0.0.1',
            '::1'
        ],
        'blacklist_enabled' => true,          // 是否启用黑名单
        'blacklist' => [                      // IP黑名单
            // 恶意IP将动态添加
        ],
        'auto_ban_enabled' => true,           // 自动封禁
        'auto_ban_threshold' => 10,           // 自动封禁阈值
        'auto_ban_duration' => 86400,         // 自动封禁时长(24小时)
        'geo_blocking' => [                   // 地理位置封锁
            'enabled' => false,
            'blocked_countries' => []
        ]
    ],

    // CSRF防护
    'csrf' => [
        'enabled' => true,                     // 启用CSRF防护
        'token_name' => 'csrf_token',          // Token名称
        'token_length' => 32,                  // Token长度
        'expire_time' => 3600,                 // Token过期时间
        'regenerate_on_use' => false,          // 使用后重新生成
        'validate_referer' => true,            // 验证Referer头
        'validate_origin' => true              // 验证Origin头
    ],

    // XSS防护
    'xss' => [
        'enabled' => true,                     // 启用XSS防护
        'filter_input' => true,                // 过滤输入
        'escape_output' => true,               // 转义输出
        'content_security_policy' => [         // 内容安全策略
            'enabled' => true,
            'default_src' => "'self'",
            'script_src' => "'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdn.tailwindcss.com",
            'style_src' => "'self' 'unsafe-inline' https://fonts.googleapis.com",
            'font_src' => "'self' https://fonts.gstatic.com",
            'img_src' => "'self' data: https:",
            'connect_src' => "'self'",
            'frame_ancestors' => "'none'",
            'base_uri' => "'self'",
            'form_action' => "'self'"
        ]
    ],

    // SQL注入防护
    'sql_injection' => [
        'enabled' => true,                     // 启用SQL注入防护
        'use_prepared_statements' => true,     // 使用预处理语句
        'validate_input' => true,              // 验证输入
        'escape_special_chars' => true,        // 转义特殊字符
        'blocked_keywords' => [                // 阻止的关键词
            'union', 'select', 'insert', 'update', 'delete',
            'drop', 'create', 'alter', 'exec', 'execute',
            'script', 'javascript', 'vbscript'
        ]
    ],

    // 文件上传安全
    'file_upload' => [
        'enabled' => true,                     // 启用文件上传
        'max_file_size' => 10485760,           // 最大文件大小(10MB)
        'allowed_extensions' => [              // 允许的扩展名
            'jpg', 'jpeg', 'png', 'gif', 'svg', 'webp',
            'pdf', 'doc', 'docx', 'txt', 'csv'
        ],
        'allowed_mime_types' => [              // 允许的MIME类型
            'image/jpeg', 'image/png', 'image/gif', 'image/svg+xml', 'image/webp',
            'application/pdf', 'text/plain', 'text/csv'
        ],
        'scan_for_malware' => true,            // 恶意软件扫描
        'quarantine_suspicious' => true,       // 隔离可疑文件
        'upload_path' => '/uploads/admin/',    // 上传路径
        'temp_path' => '/tmp/uploads/',        // 临时路径
        'virus_scan_command' => 'clamscan',    // 病毒扫描命令
        'rename_files' => true,                // 重命名文件
        'generate_thumbnails' => true          // 生成缩略图
    ],

    // 访问频率限制
    'rate_limiting' => [
        'enabled' => true,                     // 启用频率限制
        'requests_per_minute' => 60,           // 每分钟请求数
        'requests_per_hour' => 1000,           // 每小时请求数
        'requests_per_day' => 10000,           // 每天请求数
        'burst_limit' => 10,                   // 突发限制
        'whitelist_ips' => [                   // 白名单IP
            '127.0.0.1'
        ],
        'penalty_duration' => 300,             // 惩罚时长(秒)
        'progressive_penalty' => true          // 渐进式惩罚
    ],

    // 数据加密
    'encryption' => [
        'algorithm' => 'AES-256-GCM',          // 加密算法
        'key_length' => 32,                    // 密钥长度
        'iv_length' => 16,                     // 初始化向量长度
        'tag_length' => 16,                    // 标签长度
        'key_derivation' => 'PBKDF2',          // 密钥派生函数
        'iterations' => 10000,                 // 迭代次数
        'salt_length' => 32,                   // 盐长度
        'encrypt_sensitive_data' => true,      // 加密敏感数据
        'encrypt_logs' => false,               // 加密日志
        'key_rotation_days' => 90              // 密钥轮换天数
    ],

    // 安全头部
    'security_headers' => [
        'enabled' => true,                     // 启用安全头部
        'headers' => [
            'X-Frame-Options' => 'DENY',
            'X-Content-Type-Options' => 'nosniff',
            'X-XSS-Protection' => '1; mode=block',
            'Strict-Transport-Security' => 'max-age=31536000; includeSubDomains; preload',
            'Referrer-Policy' => 'strict-origin-when-cross-origin',
            'Permissions-Policy' => 'geolocation=(), microphone=(), camera=()',
            'X-Permitted-Cross-Domain-Policies' => 'none'
        ]
    ],

    // 审计日志
    'audit_logging' => [
        'enabled' => true,                     // 启用审计日志
        'log_level' => 'INFO',                 // 日志级别
        'log_file' => '/var/log/prompt2tool/admin-audit.log',
        'max_file_size' => 104857600,          // 最大文件大小(100MB)
        'max_files' => 10,                     // 最大文件数量
        'log_format' => 'json',                // 日志格式
        'include_request_data' => true,        // 包含请求数据
        'include_response_data' => false,      // 包含响应数据
        'sensitive_fields' => [                // 敏感字段
            'password', 'token', 'secret', 'key'
        ],
        'events_to_log' => [                   // 记录的事件
            'login_success',
            'login_failure',
            'logout',
            'password_change',
            'permission_change',
            'data_access',
            'data_modification',
            'system_configuration_change',
            'security_violation'
        ]
    ],

    // 入侵检测
    'intrusion_detection' => [
        'enabled' => true,                     // 启用入侵检测
        'monitor_failed_logins' => true,       // 监控登录失败
        'monitor_suspicious_patterns' => true, // 监控可疑模式
        'monitor_privilege_escalation' => true, // 监控权限提升
        'monitor_data_exfiltration' => true,   // 监控数据泄露
        'alert_threshold' => 5,                // 警报阈值
        'alert_email' => '<EMAIL>',
        'alert_webhook' => '',                 // 警报Webhook
        'auto_response' => [                   // 自动响应
            'enabled' => true,
            'block_ip' => true,                // 阻止IP
            'disable_account' => false,        // 禁用账户
            'notify_admin' => true             // 通知管理员
        ]
    ],

    // 备份安全
    'backup_security' => [
        'encrypt_backups' => true,             // 加密备份
        'backup_retention_days' => 30,         // 备份保留天数
        'offsite_backup' => true,              // 异地备份
        'backup_verification' => true,         // 备份验证
        'access_control' => [                  // 访问控制
            'require_authentication' => true,
            'allowed_roles' => ['super_admin'],
            'audit_access' => true
        ]
    ],

    // 安全监控
    'monitoring' => [
        'enabled' => true,                     // 启用安全监控
        'real_time_alerts' => true,            // 实时警报
        'security_dashboard' => true,          // 安全仪表板
        'threat_intelligence' => false,        // 威胁情报
        'vulnerability_scanning' => false,     // 漏洞扫描
        'compliance_monitoring' => true,       // 合规监控
        'metrics' => [                         // 监控指标
            'failed_login_rate',
            'suspicious_activity_count',
            'security_violation_count',
            'system_resource_usage',
            'network_traffic_anomalies'
        ]
    ],

    // 应急响应
    'incident_response' => [
        'enabled' => true,                     // 启用应急响应
        'emergency_contacts' => [              // 紧急联系人
            'primary' => '<EMAIL>',
            'secondary' => '<EMAIL>'
        ],
        'escalation_rules' => [                // 升级规则
            'critical' => 0,                   // 立即升级
            'high' => 300,                     // 5分钟后升级
            'medium' => 1800,                  // 30分钟后升级
            'low' => 3600                      // 1小时后升级
        ],
        'automated_responses' => [             // 自动响应
            'isolate_affected_systems' => false,
            'block_malicious_ips' => true,
            'disable_compromised_accounts' => true,
            'create_incident_ticket' => true
        ]
    ]
];
?>
