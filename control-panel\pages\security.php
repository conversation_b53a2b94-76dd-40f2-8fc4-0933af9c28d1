<?php
/**
 * Security Settings Page
 */

// Security check
if (!defined('SECURE_ACCESS')) {
    exit('Direct access not allowed');
}

// 使用统一的数据库连接
require_once dirname(dirname(__DIR__)) . '/includes/database-connection.php';
require_once __DIR__ . '/../includes/GoogleAuthenticator.php';

// Time formatting function (define early)
if (!function_exists('formatTimeAgo')) {
    function formatTimeAgo($datetime) {
        if (empty($datetime)) {
            return 'Unknown time';
        }

        $timestamp = strtotime($datetime);
        if ($timestamp === false) {
            return 'Invalid time';
        }

        $diff = time() - $timestamp;

        // Handle future dates
        if ($diff < 0) {
            return 'Just now';
        }

        if ($diff < 60) {
            return 'Just now';
        } elseif ($diff < 3600) {
            $minutes = floor($diff / 60);
            return $minutes . ' minute' . ($minutes > 1 ? 's' : '') . ' ago';
        } elseif ($diff < 86400) {
            $hours = floor($diff / 3600);
            return $hours . ' hour' . ($hours > 1 ? 's' : '') . ' ago';
        } elseif ($diff < 2592000) { // 30 days
            $days = floor($diff / 86400);
            return $days . ' day' . ($days > 1 ? 's' : '') . ' ago';
        } else {
            // For older dates, show the actual date
            return date('M j, Y', $timestamp);
        }
    }
}



// Get current admin data and security settings
$adminId = $_SESSION['admin_id'] ?? 1;
$adminData = [];
$securitySettings = [
    '2fa_enabled' => false,
    '2fa_secret' => null,
    'active_sessions' => []
];

try {
    // Get admin data
    $adminSql = "SELECT two_factor_enabled, two_factor_secret FROM pt_manager WHERE id = ?";
    $adminStmt = $pdo->prepare($adminSql);
    $adminStmt->execute([$adminId]);
    $adminData = $adminStmt->fetch();

    if ($adminData) {
        $securitySettings['2fa_enabled'] = (bool)$adminData['two_factor_enabled'];
        $securitySettings['2fa_secret'] = $adminData['two_factor_secret'];
    }

    // Get active sessions
    $sessionsSql = "SELECT session_id, ip_address, user_agent, device_info, location, last_activity, is_active
                    FROM pt_manager_session
                    WHERE manager_id = ? AND is_active = 1
                    ORDER BY last_activity DESC";
    $sessionsStmt = $pdo->prepare($sessionsSql);
    $sessionsStmt->execute([$adminId]);
    $sessions = $sessionsStmt->fetchAll();

    $currentSessionId = session_id();
    foreach ($sessions as $session) {
        $isCurrentSession = ($session['session_id'] === $currentSessionId);
        $lastActivity = strtotime($session['last_activity']);
        $timeAgo = $isCurrentSession ? 'Active now' : formatTimeAgo($session['last_activity']);

        $securitySettings['active_sessions'][] = [
            'id' => $session['session_id'],
            'device' => $session['device_info'] ?: parseUserAgent($session['user_agent']),
            'ip' => $session['ip_address'],
            'location' => $session['location'] ?: 'Unknown',
            'last_active' => $timeAgo,
            'is_current' => $isCurrentSession
        ];
    }

} catch (PDOException $e) {
    error_log("Database error in security page: " . $e->getMessage());
}

// Get recent security activities (same as Profile page)
$recentActivities = [];
try {
    $adminId = $_SESSION['admin_id'] ?? 1;
    $activitySql = "SELECT action, description, icon, color, created_at
                    FROM pt_activity_log
                    WHERE manager_id = ?
                    ORDER BY created_at DESC
                    LIMIT 5";
    $activityStmt = $pdo->prepare($activitySql);
    $activityStmt->execute([$adminId]);
    $recentActivities = $activityStmt->fetchAll();
} catch (PDOException $e) {
    error_log("Database error getting security activities: " . $e->getMessage());
    $recentActivities = [];
}

// Helper functions
if (!function_exists('timeAgo')) {
    function timeAgo($timestamp) {
        $diff = time() - $timestamp;
        if ($diff < 60) return 'Just now';
        if ($diff < 3600) return floor($diff / 60) . ' minutes ago';
        if ($diff < 86400) return floor($diff / 3600) . ' hours ago';
        return floor($diff / 86400) . ' days ago';
    }
}

if (!function_exists('parseUserAgent')) {
    function parseUserAgent($userAgent) {
        if (strpos($userAgent, 'Chrome') !== false) return 'Chrome Browser';
        if (strpos($userAgent, 'Firefox') !== false) return 'Firefox Browser';
        if (strpos($userAgent, 'Safari') !== false) return 'Safari Browser';
        if (strpos($userAgent, 'Edge') !== false) return 'Edge Browser';
        return 'Unknown Browser';
    }
}


?>

<div class="max-w-4xl mx-auto">
    <div class="mb-6">
        <h1 class="text-2xl font-semibold text-gray-900">Security Settings</h1>
        <p class="text-gray-600 mt-2">Manage your account security and access controls</p>
    </div>

    <!-- Two-Factor Authentication -->
    <div class="bg-white shadow border border-gray-200 mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-medium text-gray-900">Two-Factor Authentication</h2>
        </div>
        <div class="p-6">
            <div class="flex items-center justify-between">
                <div class="flex-1">
                    <h3 class="text-sm font-medium text-gray-900">Enable 2FA</h3>
                    <p class="text-sm text-gray-600 mt-1">Add an extra layer of security to your account</p>
                </div>
                <div class="flex-shrink-0">
                    <label class="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" class="sr-only peer" id="enable-2fa" <?= $securitySettings['2fa_enabled'] ? 'checked' : '' ?>>
                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:h-5 after:w-5 after:transition-all peer-checked:bg-accent"></div>
                    </label>
                </div>
            </div>
            
            <?php if (!$securitySettings['2fa_enabled']): ?>
            <div class="mt-4 p-4 bg-yellow-50 border border-yellow-200">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <i class="fas fa-exclamation-triangle text-yellow-400"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm text-yellow-700">
                            Two-factor authentication is currently disabled. Enable it to secure your account.
                        </p>
                    </div>
                </div>
            </div>
            <?php else: ?>
            <div class="mt-4 p-4 bg-green-50 border border-green-200">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <i class="fas fa-check-circle text-green-400"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm text-green-700">
                            Two-factor authentication is enabled and protecting your account.
                        </p>
                    </div>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Login Sessions -->
    <div class="bg-white shadow border border-gray-200 mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-medium text-gray-900">Active Sessions</h2>
        </div>
        <div class="p-6">
            <div class="space-y-4" id="sessions-container">
                <?php foreach ($securitySettings['active_sessions'] as $session): ?>
                <div class="flex items-center justify-between p-4 border border-gray-200 <?= $session['is_current'] ? 'bg-green-50' : '' ?>">
                    <div class="flex items-center space-x-3">
                        <div class="flex-shrink-0">
                            <i class="fas <?= $session['is_current'] ? 'fa-desktop text-green-600' : 'fa-mobile-alt text-gray-600' ?>"></i>
                        </div>
                        <div>
                            <p class="text-sm font-medium text-gray-900"><?= $session['is_current'] ? 'Current Session' : 'Other Device' ?></p>
                            <p class="text-xs text-gray-600"><?= htmlspecialchars($session['device']) ?> • <?= htmlspecialchars($session['ip']) ?></p>
                            <p class="text-xs text-gray-500"><?= htmlspecialchars($session['last_active']) ?></p>
                        </div>
                    </div>
                    <?php if ($session['is_current']): ?>
                        <span class="inline-flex px-2 py-1 text-xs font-medium bg-green-100 text-green-800">
                            Current
                        </span>
                    <?php else: ?>
                        <button class="text-sm text-red-600 hover:text-red-800 revoke-session" data-session-id="<?= $session['id'] ?>">
                            Revoke
                        </button>
                    <?php endif; ?>
                </div>
                <?php endforeach; ?>
            </div>
            
            <div class="mt-4">
                <button class="bg-red-600 text-white px-4 py-2 text-sm font-medium hover:bg-red-700" id="revoke-all-sessions">
                    Revoke All Other Sessions
                </button>
            </div>
        </div>
    </div>

    <!-- Security Logs -->
    <div class="bg-white shadow border border-gray-200 mb-6">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-medium text-gray-900">Security Activity</h2>
        </div>
        <div class="p-6">
            <div class="space-y-4">
                <?php if (!empty($recentActivities)): ?>
                    <?php foreach ($recentActivities as $activity): ?>
                        <div class="flex items-center space-x-3">
                            <div class="flex-shrink-0">
                                <i class="<?= htmlspecialchars($activity['icon'] ?? 'fas fa-info-circle') ?> text-<?= htmlspecialchars($activity['color'] ?? 'blue') ?>-500"></i>
                            </div>
                            <div class="flex-1">
                                <p class="text-sm text-gray-900"><?= htmlspecialchars($activity['description'] ?? 'Unknown activity') ?></p>
                                <p class="text-xs text-gray-500"><?= formatTimeAgo($activity['created_at']) ?></p>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="text-center py-8">
                        <i class="fas fa-shield-alt text-gray-400 text-3xl mb-3"></i>
                        <p class="text-gray-500">No recent security activity</p>
                        <p class="text-xs text-gray-400 mt-1">Security events will appear here</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Security Recommendations -->
    <div class="bg-white shadow border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-medium text-gray-900">Security Recommendations</h2>
        </div>
        <div class="p-6">
            <div class="space-y-4">
                <div class="flex items-start space-x-3">
                    <div class="flex-shrink-0 mt-1">
                        <i class="fas fa-shield-alt text-accent"></i>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-gray-900">Enable Two-Factor Authentication</h4>
                        <p class="text-sm text-gray-600">Protect your account with an additional security layer.</p>
                    </div>
                </div>

                <div class="flex items-start space-x-3">
                    <div class="flex-shrink-0 mt-1">
                        <i class="fas fa-clock text-warning"></i>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-gray-900">Regular Password Updates</h4>
                        <p class="text-sm text-gray-600">Change your password every 90 days for better security.</p>
                    </div>
                </div>

                <div class="flex items-start space-x-3">
                    <div class="flex-shrink-0 mt-1">
                        <i class="fas fa-eye text-success"></i>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-gray-900">Monitor Login Activity</h4>
                        <p class="text-sm text-gray-600">Regularly review your login sessions and security logs.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 2FA Disable Modal -->
<div id="twofa-disable-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white max-w-md w-full p-6 border border-gray-200">
            <div class="flex items-center mb-4">
                <div class="flex-shrink-0">
                    <i class="fas fa-shield-alt text-red-500 text-xl"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-lg font-medium text-gray-900">Disable Two-Factor Authentication</h3>
                </div>
            </div>

            <div class="mb-4">
                <p class="text-sm text-gray-600 mb-4">
                    To disable 2FA, please verify your identity by entering your current 2FA code:
                </p>

                <div class="mb-4">
                    <label for="disable-verification-code" class="block text-sm font-medium text-gray-700 mb-2">
                        Enter current 2FA code:
                    </label>
                    <input type="text" id="disable-verification-code" maxlength="6"
                           class="w-full px-3 py-2 border border-gray-300 focus:outline-none focus:border-accent text-center text-lg font-mono"
                           placeholder="000000">
                </div>
            </div>

            <div class="flex justify-end space-x-3">
                <button type="button" id="twofa-disable-cancel" class="px-4 py-2 border border-gray-300 text-gray-700 hover:bg-gray-50">
                    Cancel
                </button>
                <button type="button" id="twofa-disable-confirm" class="px-4 py-2 bg-red-600 text-white hover:bg-red-700">
                    Disable 2FA
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 2FA Setup Modal -->
<div id="twofa-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white max-w-md w-full p-6 border border-gray-200">
            <div class="flex items-center mb-4">
                <div class="flex-shrink-0">
                    <i class="fas fa-shield-alt text-blue-500 text-xl"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-lg font-medium text-gray-900">Setup Two-Factor Authentication</h3>
                </div>
            </div>

            <div id="twofa-setup-step">
                <div class="mb-4">
                    <p class="text-sm text-gray-600 mb-4">
                        Scan this QR code with Google Authenticator app:
                    </p>
                    <div class="text-center mb-4">
                        <img id="qr-code-image" src="" alt="QR Code" class="mx-auto border border-gray-200">
                    </div>
                    <div class="mb-4">
                        <p class="text-xs text-gray-500 mb-2">Or enter this key manually:</p>
                        <div class="bg-gray-100 p-2 text-xs font-mono break-all" id="manual-key"></div>
                    </div>
                </div>

                <div class="mb-4">
                    <label for="verification-code" class="block text-sm font-medium text-gray-700 mb-2">
                        Enter verification code from your app:
                    </label>
                    <input type="text" id="verification-code" maxlength="6"
                           class="w-full px-3 py-2 border border-gray-300 focus:outline-none focus:border-accent text-center text-lg font-mono"
                           placeholder="000000">
                </div>
            </div>

            <div class="flex justify-end space-x-3">
                <button type="button" id="twofa-cancel" class="px-4 py-2 border border-gray-300 text-gray-700 hover:bg-gray-50">
                    Cancel
                </button>
                <button type="button" id="twofa-verify" class="px-4 py-2 bg-blue-600 text-white hover:bg-blue-700">
                    Verify & Enable
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Confirmation Modal -->
<div id="confirmation-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white max-w-md w-full p-6 border border-gray-200">
            <div class="flex items-center mb-4">
                <div class="flex-shrink-0">
                    <i class="fas fa-exclamation-triangle text-yellow-400 text-xl"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-lg font-medium text-gray-900" id="modal-title">Confirm Action</h3>
                </div>
            </div>
            <div class="mb-6">
                <p class="text-sm text-gray-600" id="modal-message">Are you sure you want to proceed?</p>
            </div>
            <div class="flex justify-end space-x-3">
                <button type="button" id="modal-cancel" class="px-4 py-2 border border-gray-300 text-gray-700 hover:bg-gray-50">
                    Cancel
                </button>
                <button type="button" id="modal-confirm" class="px-4 py-2 bg-red-600 text-white hover:bg-red-700">
                    Confirm
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Page load - ensure 2FA switch reflects actual database state
document.addEventListener('DOMContentLoaded', function() {
    const twofaSwitch = document.getElementById('enable-2fa');
    const actualState = <?= $securitySettings['2fa_enabled'] ? 'true' : 'false' ?>;
    twofaSwitch.checked = actualState;
});

// Toggle 2FA
document.getElementById('enable-2fa').addEventListener('change', function() {
    const enabled = this.checked;

    if (enabled) {
        // Show 2FA setup modal
        show2FASetup();
        this.checked = false; // Reset until setup is complete
    } else {
        // Show 2FA disable verification modal
        show2FADisableModal();
        this.checked = true; // Keep checked until verified
    }
});

// 2FA Setup functions
function show2FASetup() {
    fetch('ajax/security.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'action=toggle_2fa&enabled=true'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.qr_code) {
            document.getElementById('qr-code-image').src = data.qr_code;
            document.getElementById('manual-key').textContent = data.secret;
            document.getElementById('twofa-modal').classList.remove('hidden');
        } else {
            showMessage(data.message || 'Failed to setup 2FA', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showMessage('Failed to setup 2FA', 'error');
    });
}

// Show 2FA disable modal
function show2FADisableModal() {
    document.getElementById('twofa-disable-modal').classList.remove('hidden');
    document.getElementById('disable-verification-code').focus();
}

// 2FA Disable Modal handlers
document.getElementById('twofa-disable-cancel').addEventListener('click', function() {
    document.getElementById('twofa-disable-modal').classList.add('hidden');
    document.getElementById('disable-verification-code').value = '';
    // Reset the switch to enabled
    document.getElementById('enable-2fa').checked = true;
});

document.getElementById('twofa-disable-confirm').addEventListener('click', function() {
    const code = document.getElementById('disable-verification-code').value;

    if (code.length !== 6) {
        showMessage('Please enter a 6-digit verification code', 'error');
        return;
    }

    // Verify the code first
    fetch('ajax/security.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `action=verify_2fa_disable&code=${code}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Code verified, now disable 2FA
            fetch('ajax/security.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=toggle_2fa&enabled=false'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('twofa-disable-modal').classList.add('hidden');
                    document.getElementById('enable-2fa').checked = false;
                    showMessage('2FA disabled successfully!', 'success');
                    setTimeout(() => location.reload(), 1500);
                } else {
                    showMessage(data.message || 'Failed to disable 2FA', 'error');
                }
            });
        } else {
            showMessage(data.message || 'Invalid verification code', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showMessage('Failed to verify 2FA code', 'error');
    });
});

// 2FA Modal handlers
document.getElementById('twofa-cancel').addEventListener('click', function() {
    // Cancel 2FA setup - remove temporary secret
    fetch('ajax/security.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'action=cancel_2fa_setup'
    })
    .then(response => response.json())
    .then(data => {
        document.getElementById('twofa-modal').classList.add('hidden');
        document.getElementById('verification-code').value = '';
        if (data.success) {
            showMessage('2FA setup cancelled', 'info');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        document.getElementById('twofa-modal').classList.add('hidden');
        document.getElementById('verification-code').value = '';
    });
});

document.getElementById('twofa-verify').addEventListener('click', function() {
    const code = document.getElementById('verification-code').value;

    if (code.length !== 6) {
        showMessage('Please enter a 6-digit verification code', 'error');
        return;
    }

    fetch('ajax/security.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `action=verify_2fa&code=${code}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById('twofa-modal').classList.add('hidden');
            document.getElementById('enable-2fa').checked = true;
            showMessage('2FA enabled successfully!', 'success');
            setTimeout(() => location.reload(), 1500);
        } else {
            showMessage(data.message || 'Invalid verification code', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showMessage('Failed to verify 2FA code', 'error');
    });
});

// Revoke individual session
document.querySelectorAll('.revoke-session').forEach(button => {
    button.addEventListener('click', function() {
        const sessionId = this.dataset.sessionId;
        const sessionElement = this.closest('.flex');

        showConfirmation(
            'Revoke Session',
            'Are you sure you want to revoke this session? The device will be logged out immediately.',
            () => {
                fetch('ajax/security.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `action=revoke_session&session_id=${sessionId}`
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.success) {
                        showMessage(data.message, 'success');
                        sessionElement.remove();
                    } else {
                        showMessage(data.message || 'Failed to revoke session', 'error');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showMessage('Failed to revoke session', 'error');
                });
            }
        );
    });
});

// Revoke all sessions
document.getElementById('revoke-all-sessions').addEventListener('click', function() {
    showConfirmation(
        'Revoke All Sessions',
        'Are you sure you want to revoke all other sessions? This will log out all other devices immediately.',
        () => {
            fetch('ajax/security.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=revoke_all_sessions'
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    showMessage(data.message, 'success');
                    // Remove all non-current sessions
                    document.querySelectorAll('.revoke-session').forEach(button => {
                        button.closest('.flex').remove();
                    });
                } else {
                    showMessage(data.message || 'Failed to revoke sessions', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showMessage('Failed to revoke sessions', 'error');
            });
        }
    );
});

// Show message function
function showMessage(message, type) {
    // Remove existing messages
    const existingMessage = document.querySelector('.message-alert');
    if (existingMessage) {
        existingMessage.remove();
    }

    // Create new message
    const messageDiv = document.createElement('div');
    messageDiv.className = `message-alert mb-6 p-4 border ${type === 'success' ? 'bg-green-50 border-green-200 text-green-700' : 'bg-red-50 border-red-200 text-red-700'}`;
    messageDiv.innerHTML = `
        <div class="flex">
            <div class="flex-shrink-0">
                <i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'}"></i>
            </div>
            <div class="ml-3">
                <p class="text-sm">${message}</p>
            </div>
        </div>
    `;

    // Insert at the top of the container
    const container = document.querySelector('.max-w-4xl');
    container.insertBefore(messageDiv, container.children[1]);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        messageDiv.remove();
    }, 5000);
}

// Show confirmation modal
function showConfirmation(title, message, onConfirm) {
    const modal = document.getElementById('confirmation-modal');
    const modalTitle = document.getElementById('modal-title');
    const modalMessage = document.getElementById('modal-message');
    const confirmBtn = document.getElementById('modal-confirm');
    const cancelBtn = document.getElementById('modal-cancel');

    modalTitle.textContent = title;
    modalMessage.textContent = message;

    // Show modal
    modal.classList.remove('hidden');

    // Handle confirm
    const handleConfirm = () => {
        modal.classList.add('hidden');
        onConfirm();
        confirmBtn.removeEventListener('click', handleConfirm);
        cancelBtn.removeEventListener('click', handleCancel);
        document.removeEventListener('keydown', handleEscape);
    };

    // Handle cancel
    const handleCancel = () => {
        modal.classList.add('hidden');
        confirmBtn.removeEventListener('click', handleConfirm);
        cancelBtn.removeEventListener('click', handleCancel);
        document.removeEventListener('keydown', handleEscape);
    };

    // Handle escape key
    const handleEscape = (e) => {
        if (e.key === 'Escape') {
            handleCancel();
        }
    };

    // Handle click outside modal
    const handleClickOutside = (e) => {
        if (e.target === modal) {
            handleCancel();
        }
    };

    confirmBtn.addEventListener('click', handleConfirm);
    cancelBtn.addEventListener('click', handleCancel);
    document.addEventListener('keydown', handleEscape);
    modal.addEventListener('click', handleClickOutside);
}
</script>
