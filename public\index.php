<?php
/**
 * Prompt2Tool - 前端入口文件
 * 基于 PHP 8.3.21 + Tailwind CSS v4.1
 */





// 设置错误报告 (开发环境)
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// 禁用日志文件
ini_set('log_errors', 0);
ini_set('error_log', null);

// 定义根目录常量 - 这个必须在加载init.php之前定义
if (!defined('ROOT_PATH')) {
    define('ROOT_PATH', dirname(__DIR__));
}
if (!defined('PUBLIC_PATH')) {
    define('PUBLIC_PATH', __DIR__);
}

// 应用初始化
require_once ROOT_PATH . '/app/init.php';

/**
 * 查找工具文件，支持多种文件类型
 * @param string $slug 工具slug
 * @return string|null 找到的文件路径，未找到返回null
 */
function findToolFile($slug) {
    $supportedExtensions = ['.php', '.html', '.htm'];

    foreach ($supportedExtensions as $ext) {
        $filePath = ROOT_PATH . '/templates/pages/tools/' . $slug . $ext;
        if (file_exists($filePath)) {
            return $filePath;
        }
    }

    return null;
}

/**
 * 获取工具文件的类型
 * @param string $slug 工具slug
 * @return string 文件类型 (php|html|htm)，未找到返回空字符串
 */
function getToolFileType($slug) {
    $supportedExtensions = ['.php', '.html', '.htm'];

    foreach ($supportedExtensions as $ext) {
        $filePath = ROOT_PATH . '/templates/pages/tools/' . $slug . $ext;
        if (file_exists($filePath)) {
            return ltrim($ext, '.');
        }
    }

    return '';
}

// 简单路由处理
$requestUri = $_SERVER['REQUEST_URI'] ?? '/';
$requestUri = parse_url($requestUri, PHP_URL_PATH);

// 基础路由
switch ($requestUri) {
    case '/':
        include ROOT_PATH . '/templates/pages/index.php';
        break;
    case '/tools':
        include ROOT_PATH . '/templates/pages/tools.php';
        break;
    case '/categories':
        include ROOT_PATH . '/templates/pages/categories.php';
        break;
    case '/requests':
        include ROOT_PATH . '/templates/pages/requests.php';
        break;
    case '/launches':
        include ROOT_PATH . '/templates/pages/launches.php';
        break;
    case '/submit-launch':
        include ROOT_PATH . '/templates/pages/submit-launch.php';
        break;
    case '/about':
        include ROOT_PATH . '/templates/pages/about.php';
        break;
    case '/pricing':
        include ROOT_PATH . '/templates/pages/pricing.php';
        break;
    case '/contact':
        include ROOT_PATH . '/templates/pages/contact.php';
        break;
    case '/help':
        include ROOT_PATH . '/templates/pages/help.php';
        break;
    case '/privacy':
        include ROOT_PATH . '/templates/pages/privacy.php';
        break;
    case '/terms':
        include ROOT_PATH . '/templates/pages/terms.php';
        break;
    case '/auth/login':
        include ROOT_PATH . '/templates/pages/auth/login.php';
        break;
    case '/auth/register':
        include ROOT_PATH . '/templates/pages/auth/register.php';
        break;
    case '/auth/verify-email':
        include ROOT_PATH . '/templates/pages/auth/verify-email.php';
        break;
    case '/auth/logout':
        include ROOT_PATH . '/api/v1/auth/logout.php';
        break;
    case '/dashboard':
        // 检查用户是否已登录，未登录则跳转到登录页
        session_start();
        if (!isset($_SESSION['user_id'])) {
            header('Location: /auth/login');
            exit;
        }
        header('Location: /user-center/index.php?page=dashboard');
        exit;
        break;
    case '/profile':
        // 检查用户是否已登录，未登录则跳转到登录页
        session_start();
        if (!isset($_SESSION['user_id'])) {
            header('Location: /auth/login');
            exit;
        }
        header('Location: /user-center/index.php?page=profile');
        exit;
        break;
    case '/favorites':
        // 检查用户是否已登录，未登录则跳转到登录页
        session_start();
        if (!isset($_SESSION['user_id'])) {
            header('Location: /auth/login');
            exit;
        }
        // 创建收藏页面或重定向到用户中心
        header('Location: /user-center/index.php?page=favorites');
        exit;
        break;

    case '/user-center':
    case '/user-center/':
        header('Location: /user-center/index.php?page=dashboard');
        exit;
        break;
    default:

        // 检查是否为API请求
        if (preg_match('/^\/api\/v1\/auth\/(.+)$/', $requestUri, $matches)) {
            $endpoint = $matches[1];
            $apiFile = ROOT_PATH . '/api/v1/auth/' . $endpoint . '.php';

            if (file_exists($apiFile)) {
                include $apiFile;
                exit;
            } else {
                http_response_code(404);
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'message' => 'API endpoint not found']);
                exit;
            }
        }
        // 检查是否为用户API请求
        elseif (preg_match('/^\/api\/v1\/user\/(.+)$/', $requestUri, $matches)) {
            $endpoint = $matches[1];
            $apiFile = ROOT_PATH . '/api/v1/user/' . $endpoint . '.php';

            if (file_exists($apiFile)) {
                include $apiFile;
                exit;
            } else {
                http_response_code(404);
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'message' => 'User API endpoint not found']);
                exit;
            }
        }
        // 检查是否为用户中心页面
        elseif (preg_match('/^\/user-center\/(.*)$/', $requestUri, $matches)) {
            $page = $matches[1];
            if (empty($page) || $page === 'index.php') {
                include ROOT_PATH . '/user-center/index.php';
                exit;
            } else {
                // 重定向到用户中心主页处理
                header('Location: /user-center/?page=' . urlencode($page));
                exit;
            }
        }
        // 检查是否为需求详情页面
        elseif (preg_match('/^\/ideas\/([^\/]+)$/', $requestUri, $matches)) {
            $slug = $matches[1];
            $_GET['slug'] = $slug; // 设置GET参数供页面使用
            include ROOT_PATH . '/templates/pages/request-detail.php';
            exit;
        }
        // 检查是否为产品启动详情页面
        elseif (preg_match('/^\/launch\/([^\/]+)$/', $requestUri, $matches)) {
            $slug = $matches[1];
            $_GET['slug'] = $slug; // 设置GET参数供页面使用
            include ROOT_PATH . '/templates/pages/launch-detail.php';
            exit;
        }
        // 检查是否为具体工具页面 (带分类) - 唯一允许的工具URL格式
        elseif (preg_match('/^\/tools\/([^\/]+)\/([^\/]+)$/', $requestUri, $matches)) {
            $category = $matches[1];
            $toolSlug = $matches[2];

            // 验证分类是否存在
            require_once ROOT_PATH . '/app/helpers/tool-helpers.php';
            $categories = getToolCategories();

            if (!isset($categories[$category])) {
                // 分类不存在，显示404
                http_response_code(404);
                include ROOT_PATH . '/templates/pages/404.php';
                exit;
            }

            // 在包含任何工具页面之前，先进行权限检查
            require_once ROOT_PATH . '/app/helpers/tool-helpers.php';
            $toolData = getToolBySlug($toolSlug);

            if (!$toolData) {
                // 工具不存在，显示404
                http_response_code(404);
                include ROOT_PATH . '/templates/pages/404.php';
                exit;
            }

            // 检查工具状态和用户访问权限
            if ($toolData['status'] !== 'active') {
                // 会话应该已经在 app/init.php 中启动了
                if (session_status() === PHP_SESSION_NONE) {
                    session_start();
                }

                // 检查用户是否登录
                if (!isset($_SESSION['user_id'])) {
                    // 未登录用户不能访问非发布状态的工具，返回404以保护SEO
                    http_response_code(404);
                    include ROOT_PATH . '/templates/pages/404.php';
                    exit;
                }
            }

            // 权限检查通过，查找工具文件（支持多种文件类型）
            $toolPagePath = findToolFile($toolSlug);
            if ($toolPagePath) {
                // 直接包含具体的工具页面
                include $toolPagePath;
            } else {
                // 使用通用工具详情页
                $tool = $toolSlug;
                include ROOT_PATH . '/templates/pages/tool-detail.php';
            }
        } elseif (preg_match('/^\/tools\/([^\/]+)$/', $requestUri, $matches)) {
            $slug = $matches[1];

            // 首先检查是否是工具分类
            require_once ROOT_PATH . '/app/helpers/tool-helpers.php';
            $categories = getToolCategories();

            if (isset($categories[$slug])) {
                // 是工具分类页
                $category = $slug;
                include ROOT_PATH . '/templates/pages/tools/category.php';
            } else {
                // 检查是否是工具，如果是则重定向到正确的分类URL
                try {
                    // 使用统一的数据库连接
                    require_once ROOT_PATH . '/includes/database-connection.php';

                    $stmt = $pdo->prepare("
                        SELECT t.slug, c.slug as category_slug
                        FROM pt_tool t
                        LEFT JOIN pt_tool_category c ON t.category_id = c.id
                        WHERE t.slug = ? AND t.status = 'active'
                    ");
                    $stmt->execute([$slug]);
                    $tool = $stmt->fetch();

                    if ($tool && !empty($tool['category_slug'])) {
                        // 重定向到正确的分类URL
                        $correctUrl = "/tools/{$tool['category_slug']}/{$tool['slug']}";
                        header("Location: $correctUrl", true, 301); // 301永久重定向
                        exit;
                    }
                } catch (Exception $e) {
                    error_log('Database error in routing: ' . $e->getMessage());
                }

                // 不是工具也不是分类，显示404
                http_response_code(404);
                include ROOT_PATH . '/templates/pages/404.php';
            }
        } else {
            // 404页面
            http_response_code(404);
            include ROOT_PATH . '/templates/pages/404.php';
        }
        break;
}
