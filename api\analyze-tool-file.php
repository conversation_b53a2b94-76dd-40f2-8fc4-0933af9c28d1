<?php
/**
 * 文件上传和AI分析API
 * 处理工具文件上传，提取内容并使用GLM-4.5-Flash进行分析
 */

// 设置响应头
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// 定义根目录
define('ROOT_PATH', dirname(__DIR__));

// 检查用户登录状态
session_start();
if (!isset($_SESSION['user_id'])) {
    sendError('Please login first', 401);
}

// 错误报告设置
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// 加载必要的类
try {
    require_once ROOT_PATH . '/classes/AIService.php';
    require_once ROOT_PATH . '/includes/database-connection.php';
    require_once ROOT_PATH . '/classes/QuotaManager.php';
} catch (Exception $e) {
    sendError('Failed to load required classes: ' . $e->getMessage(), 500);
}

// 错误处理函数
function sendError($message, $code = 400) {
    http_response_code($code);
    echo json_encode([
        'success' => false,
        'error' => $message
    ]);
    exit;
}

// 成功响应函数
function sendSuccess($data) {
    echo json_encode([
        'success' => true,
        'data' => $data
    ]);
    exit;
}

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    sendError('Only POST method allowed. This API endpoint is for file upload analysis.', 405);
}

// 检查文件上传
if (!isset($_FILES['file']) || $_FILES['file']['error'] !== UPLOAD_ERR_OK) {
    sendError('No file uploaded or upload error');
}

$uploadedFile = $_FILES['file'];

// 验证文件类型
$allowedExtensions = ['php', 'html', 'htm'];
$fileExtension = strtolower(pathinfo($uploadedFile['name'], PATHINFO_EXTENSION));

if (!in_array($fileExtension, $allowedExtensions)) {
    sendError('Invalid file type. Only PHP, HTML, and HTM files are allowed.');
}

// 验证文件大小 (最大2MB)
$maxFileSize = 2 * 1024 * 1024; // 2MB
if ($uploadedFile['size'] > $maxFileSize) {
    sendError('File too large. Maximum size is 2MB.');
}

// 读取文件内容
$fileContent = file_get_contents($uploadedFile['tmp_name']);
if ($fileContent === false) {
    sendError('Failed to read file content');
}

// 检查配额是否足够
try {
    $pdo = getDatabaseConnection();
    $quotaManager = new QuotaManager($pdo);
    $userId = $_SESSION['user_id'];

    if (!$quotaManager->hasEnoughQuota($userId, 'chat_interaction')) {
        $message = $quotaManager->getInsufficientQuotaMessage('chat_interaction');
        sendError($message, 402); // 402 Payment Required
    }
} catch (Exception $e) {
    sendError('Failed to check quota: ' . $e->getMessage(), 500);
}

// 基础内容提取
function extractBasicInfo($content, $filename) {
    $info = [
        'original_name' => $filename,
        'size' => strlen($content),
        'hash' => md5($content),
        'detected_type' => 'unknown'
    ];
    
    // 检测文件类型
    if (strpos($content, '<?php') !== false) {
        $info['detected_type'] = 'php';
    } elseif (strpos($content, '<!DOCTYPE html') !== false || strpos($content, '<html') !== false) {
        $info['detected_type'] = 'html';
    }
    
    // 提取标题
    if (preg_match('/<title[^>]*>(.*?)<\/title>/is', $content, $matches)) {
        $info['title'] = trim(strip_tags($matches[1]));
    } elseif (preg_match('/<h1[^>]*>(.*?)<\/h1>/is', $content, $matches)) {
        $info['title'] = trim(strip_tags($matches[1]));
    }
    
    // 提取描述
    if (preg_match('/<meta[^>]*name=["\']description["\'][^>]*content=["\']([^"\']*)["\'][^>]*>/i', $content, $matches)) {
        $info['description'] = trim($matches[1]);
    }
    
    // 提取关键词
    if (preg_match('/<meta[^>]*name=["\']keywords["\'][^>]*content=["\']([^"\']*)["\'][^>]*>/i', $content, $matches)) {
        $info['keywords'] = trim($matches[1]);
    }
    
    return $info;
}

// 提取基础信息
$basicInfo = extractBasicInfo($fileContent, $uploadedFile['name']);

// 准备AI分析的内容（截取前5000字符以避免token限制）
$contentForAI = substr($fileContent, 0, 5000);



// 生成备用分析结果（降级处理）
function generateFallbackAnalysis($basicInfo) {
    $name = $basicInfo['title'] ?? 'Uploaded Tool';
    $slug = strtolower(preg_replace('/[^a-zA-Z0-9\s]/', '', $name));
    $slug = preg_replace('/\s+/', '-', trim($slug));
    $slug = $slug ?: 'uploaded-tool';

    $tags = [];
    if (isset($basicInfo['keywords'])) {
        $tags = array_map('trim', explode(',', $basicInfo['keywords']));
    }
    if (empty($tags)) {
        $tags = ['tool', 'utility'];
    }

    // 根据内容推测图标
    $icon = '🔧'; // 默认图标
    $nameAndDesc = strtolower(($basicInfo['title'] ?? '') . ' ' . ($basicInfo['description'] ?? ''));

    if (strpos($nameAndDesc, 'color') !== false) $icon = '🎨';
    elseif (strpos($nameAndDesc, 'password') !== false) $icon = '🔐';
    elseif (strpos($nameAndDesc, 'qr') !== false || strpos($nameAndDesc, 'code') !== false) $icon = '📱';
    elseif (strpos($nameAndDesc, 'calculator') !== false) $icon = '🧮';
    elseif (strpos($nameAndDesc, 'text') !== false || strpos($nameAndDesc, 'editor') !== false) $icon = '📝';
    elseif (strpos($nameAndDesc, 'image') !== false || strpos($nameAndDesc, 'photo') !== false) $icon = '🖼️';

    // 推测分类
    $category = 'utility';
    if (strpos($nameAndDesc, 'design') !== false || strpos($nameAndDesc, 'color') !== false) $category = 'design';
    elseif (strpos($nameAndDesc, 'security') !== false || strpos($nameAndDesc, 'password') !== false) $category = 'security';
    elseif (strpos($nameAndDesc, 'developer') !== false || strpos($nameAndDesc, 'code') !== false) $category = 'development';
    elseif (strpos($nameAndDesc, 'text') !== false || strpos($nameAndDesc, 'editor') !== false) $category = 'text';

    return [
        'name' => $name,
        'slug' => $slug,
        'description' => $basicInfo['description'] ?? 'A useful tool uploaded for analysis',
        'tags' => $tags,
        'icon' => $icon,
        'category' => $category,
        'difficulty' => 'intermediate',
        'file_type' => $basicInfo['detected_type'],
        'confidence' => 0.7,
        'fallback' => true,
        'method' => 'fallback_analysis'
    ];
}

try {
    // 创建AI服务实例
    $aiService = new AIService();

    // 尝试AI分析
    if ($aiService->isAvailable()) {
        try {
            $aiAnalysis = $aiService->analyzeToolFile($fileContent, $basicInfo);

            // AI分析成功，扣除配额
            $quotaResult = $quotaManager->consumeQuota($userId, 'chat_interaction', 'AI Tool File Analysis');
            if (!$quotaResult['success']) {
                error_log("Failed to consume quota for user $userId: " . $quotaResult['error']);
            }

            $result = array_merge($aiAnalysis, [
                'file_info' => $basicInfo,
                'analysis_timestamp' => date('Y-m-d H:i:s'),
                'ai_service_available' => true,
                'method' => 'ai_analysis'
            ]);

            sendSuccess($result);

        } catch (Exception $aiError) {
            // AI分析失败，使用备用分析
            $fallbackAnalysis = generateFallbackAnalysis($basicInfo);
            $result = array_merge($fallbackAnalysis, [
                'file_info' => $basicInfo,
                'analysis_timestamp' => date('Y-m-d H:i:s'),
                'ai_service_available' => false
            ]);

            sendSuccess($result);
        }
    } else {
        // AI服务不可用，直接使用备用分析
        $fallbackAnalysis = generateFallbackAnalysis($basicInfo);
        $result = array_merge($fallbackAnalysis, [
            'file_info' => $basicInfo,
            'analysis_timestamp' => date('Y-m-d H:i:s'),
            'ai_service_available' => false,
            'method' => 'fallback_analysis'
        ]);

        sendSuccess($result);
    }

} catch (Exception $e) {
    // 完全失败，返回错误
    sendError('Analysis failed: ' . $e->getMessage(), 500);
}
?>
