<?php
/**
 * Profile页面AJAX处理文件
 */

// 禁用错误显示，防止破坏JSON响应
ini_set('display_errors', 0);
error_reporting(E_ALL);

// 设置JSON响应头
header('Content-Type: application/json');

// 安全检查
if (!defined('SECURE_ACCESS')) {
    define('SECURE_ACCESS', true);
}

define('ROOT_PATH', dirname(dirname(__DIR__)));
require_once ROOT_PATH . '/app/init.php';

// 检查用户登录状态
if (!isset($_SESSION['user_id'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

// 使用统一的数据库连接
require_once ROOT_PATH . '/includes/database-connection.php';

// 获取用户信息
try {
    
    $stmt = $pdo->prepare("SELECT * FROM pt_member WHERE id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => 'User not found']);
        exit;
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => 'Database error']);
    exit;
}

$response = ['success' => false, 'message' => ''];

try {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'update_profile':
            // 更新基本信息
            $username = trim($_POST['username'] ?? '');
            $first_name = trim($_POST['first_name'] ?? '');
            $last_name = trim($_POST['last_name'] ?? '');
            $bio = trim($_POST['bio'] ?? '');

            // 验证字段长度
            if (strlen($username) > 50) {
                throw new Exception('Username is too long (max 50 characters)');
            }

            if (strlen($first_name) > 50) {
                throw new Exception('First name is too long (max 50 characters)');
            }

            if (strlen($last_name) > 50) {
                throw new Exception('Last name is too long (max 50 characters)');
            }

            if (strlen($bio) > 200) {
                throw new Exception('Bio is too long (max 200 characters)');
            }

            // 验证用户名唯一性
            if ($username !== $user['username']) {
                $stmt = $pdo->prepare("SELECT id FROM pt_member WHERE username = ? AND id != ?");
                $stmt->execute([$username, $user['id']]);
                if ($stmt->fetch()) {
                    throw new Exception('Username already exists');
                }
            }

            // 更新用户信息
            $stmt = $pdo->prepare("
                UPDATE pt_member
                SET username = ?, first_name = ?, last_name = ?, bio = ?, updated_at = NOW()
                WHERE id = ?
            ");
            $stmt->execute([$username, $first_name, $last_name, $bio, $user['id']]);

            // 记录活动日志
            $stmt = $pdo->prepare("
                INSERT INTO pt_member_activity_log (user_id, action, description, ip_address, created_at)
                VALUES (?, 'profile_update', 'Updated profile information', ?, NOW())
            ");
            $stmt->execute([$user['id'], $_SERVER['REMOTE_ADDR'] ?? '']);

            $response['success'] = true;
            $response['message'] = 'Profile updated successfully';
            break;

        case 'change_password':
            // 只有邮箱注册用户才能修改密码
            if (empty($user['password'])) {
                throw new Exception('Google users cannot change password here');
            }

            $current_password = $_POST['current_password'] ?? '';
            $new_password = $_POST['new_password'] ?? '';
            $confirm_password = $_POST['confirm_password'] ?? '';

            // 验证当前密码
            if (!password_verify($current_password, $user['password'])) {
                throw new Exception('Current password is incorrect');
            }

            // 验证新密码
            if (strlen($new_password) < 8) {
                throw new Exception('New password must be at least 8 characters');
            }

            if ($new_password !== $confirm_password) {
                throw new Exception('New passwords do not match');
            }

            // 更新密码
            $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
            $stmt = $pdo->prepare("UPDATE pt_member SET password = ?, updated_at = NOW() WHERE id = ?");
            $stmt->execute([$hashed_password, $user['id']]);

            // 记录活动日志
            $stmt = $pdo->prepare("
                INSERT INTO pt_member_activity_log (user_id, action, description, ip_address, created_at)
                VALUES (?, 'password_change', 'Changed account password', ?, NOW())
            ");
            $stmt->execute([$user['id'], $_SERVER['REMOTE_ADDR'] ?? '']);

            $response['success'] = true;
            $response['message'] = 'Password changed successfully';
            break;

        case 'resend_verification':
            // 重新发送邮箱验证
            if ($user['email_verified']) {
                throw new Exception('Email is already verified');
            }

            // 生成新的验证码
            $verification_code = sprintf('%06d', mt_rand(100000, 999999));
            $stmt = $pdo->prepare("UPDATE pt_member SET email_verification_token = ? WHERE id = ?");
            $stmt->execute([$verification_code, $user['id']]);

            // 这里应该发送邮件，暂时只返回成功消息
            $response['success'] = true;
            $response['message'] = 'Verification email sent successfully';
            break;

        case 'upload_avatar':
            // 处理头像上传
            if (!isset($_FILES['avatar']) || $_FILES['avatar']['error'] !== UPLOAD_ERR_OK) {
                throw new Exception('No file uploaded or upload error');
            }
            
            $file = $_FILES['avatar'];
            $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
            $maxSize = 5 * 1024 * 1024; // 5MB
            
            // 验证文件类型
            if (!in_array($file['type'], $allowedTypes)) {
                throw new Exception('Invalid file type. Only JPEG, PNG, GIF, and WebP are allowed');
            }
            
            // 验证文件大小
            if ($file['size'] > $maxSize) {
                throw new Exception('File too large. Maximum size is 5MB');
            }
            
            // 创建上传目录
            $uploadDir = ROOT_PATH . '/public/uploads/avatars/';
            if (!is_dir($uploadDir)) {
                mkdir($uploadDir, 0755, true);
            }
            
            // 生成唯一文件名
            $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
            $filename = 'avatar_' . $user['id'] . '_' . time() . '.' . $extension;
            $filepath = $uploadDir . $filename;
            
            // 移动文件
            if (!move_uploaded_file($file['tmp_name'], $filepath)) {
                throw new Exception('Failed to save uploaded file');
            }
            
            // 删除旧头像
            if ($user['avatar'] && file_exists(ROOT_PATH . '/public' . $user['avatar'])) {
                unlink(ROOT_PATH . '/public' . $user['avatar']);
            }
            
            // 更新数据库
            $avatarUrl = '/uploads/avatars/' . $filename;
            $stmt = $pdo->prepare("UPDATE pt_member SET avatar = ?, updated_at = NOW() WHERE id = ?");
            $stmt->execute([$avatarUrl, $user['id']]);
            
            // 记录活动日志
            $stmt = $pdo->prepare("
                INSERT INTO pt_member_activity_log (user_id, action, description, ip_address, created_at)
                VALUES (?, 'avatar_update', 'Updated profile avatar', ?, NOW())
            ");
            $stmt->execute([$user['id'], $_SERVER['REMOTE_ADDR'] ?? '']);
            
            $response['success'] = true;
            $response['message'] = 'Avatar updated successfully';
            $response['avatar_url'] = $avatarUrl;
            break;
            
        case 'create_api_key':
            // 创建API密钥
            $name = trim($_POST['name'] ?? '');
            $permissions = $_POST['permissions'] ?? [];
            $expires_days = intval($_POST['expires_days'] ?? 0);
            
            if (empty($name)) {
                throw new Exception('API key name is required');
            }
            
            if (strlen($name) > 100) {
                throw new Exception('API key name is too long (max 100 characters)');
            }
            
            // 检查用户API密钥数量限制
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM pt_member_tokens WHERE user_id = ? AND type = 'api_key'");
            $stmt->execute([$user['id']]);
            $keyCount = $stmt->fetchColumn();

            // 根据用户订阅类型设置限制
            $maxKeys = 3; // 默认限制
            switch ($user['subscription_type']) {
                case 'free':
                    $maxKeys = 1;
                    break;
                case 'basic':
                    $maxKeys = 3;
                    break;
                case 'premium':
                    $maxKeys = 10;
                    break;
                case 'enterprise':
                    $maxKeys = 50;
                    break;
            }

            if ($keyCount >= $maxKeys) {
                throw new Exception("You have reached the maximum number of API keys ({$maxKeys}) for your subscription plan");
            }

            // 检查用户是否已有同名API密钥
            $stmt = $pdo->prepare("SELECT id FROM pt_member_tokens WHERE user_id = ? AND type = 'api_key' AND name = ?");
            $stmt->execute([$user['id'], $name]);
            if ($stmt->fetch()) {
                throw new Exception('An API key with this name already exists');
            }
            
            // 生成规范的API密钥 (参考Claude和OpenAI格式)
            // 格式: pt-proj-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
            // 总长度: 64位 (pt-proj- 8位 + 随机字符 56位)
            $randomPart = '';
            $chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
            for ($i = 0; $i < 56; $i++) {
                $randomPart .= $chars[random_int(0, strlen($chars) - 1)];
            }
            $token = 'pt-proj-' . $randomPart;
            
            // 设置过期时间
            $expires_at = null;
            if ($expires_days > 0) {
                $expires_at = date('Y-m-d H:i:s', strtotime("+{$expires_days} days"));
            }
            
            // 处理权限
            $permissionsJson = null;
            if (!empty($permissions) && is_array($permissions)) {
                $permissionsJson = json_encode($permissions);
            }
            
            // 插入数据库
            $stmt = $pdo->prepare("
                INSERT INTO pt_member_tokens (user_id, token, type, name, permissions, expires_at, created_at)
                VALUES (?, ?, 'api_key', ?, ?, ?, NOW())
            ");
            $stmt->execute([$user['id'], $token, $name, $permissionsJson, $expires_at]);
            
            // 记录活动日志
            $stmt = $pdo->prepare("
                INSERT INTO pt_member_activity_log (user_id, action, description, ip_address, created_at)
                VALUES (?, 'api_key_create', ?, ?, NOW())
            ");
            $stmt->execute([$user['id'], "Created API key: {$name}", $_SERVER['REMOTE_ADDR'] ?? '']);
            
            $response['success'] = true;
            $response['message'] = 'API key created successfully';
            $response['token'] = $token;
            $response['key_data'] = [
                'id' => $pdo->lastInsertId(),
                'name' => $name,
                'token' => $token,
                'expires_at' => $expires_at,
                'created_at' => date('Y-m-d H:i:s')
            ];
            break;
            
        case 'delete_api_key':
            // 删除API密钥
            $keyId = intval($_POST['key_id'] ?? 0);
            
            if ($keyId <= 0) {
                throw new Exception('Invalid API key ID');
            }
            
            // 验证API密钥属于当前用户
            $stmt = $pdo->prepare("SELECT name FROM pt_member_tokens WHERE id = ? AND user_id = ? AND type = 'api_key'");
            $stmt->execute([$keyId, $user['id']]);
            $keyData = $stmt->fetch();
            
            if (!$keyData) {
                throw new Exception('API key not found or access denied');
            }
            
            // 删除API密钥
            $stmt = $pdo->prepare("DELETE FROM pt_member_tokens WHERE id = ? AND user_id = ?");
            $deleteResult = $stmt->execute([$keyId, $user['id']]);

            // 检查是否真的删除了记录
            if ($stmt->rowCount() === 0) {
                throw new Exception('API key could not be deleted - it may have already been removed');
            }

            // 记录活动日志
            try {
                $stmt = $pdo->prepare("
                    INSERT INTO pt_member_activity_log (user_id, action, description, ip_address, created_at)
                    VALUES (?, 'api_key_delete', ?, ?, NOW())
                ");
                $stmt->execute([$user['id'], "Deleted API key: {$keyData['name']}", $_SERVER['REMOTE_ADDR'] ?? '']);
            } catch (Exception $logError) {
                // 日志记录失败不应该影响删除操作
                error_log("Failed to log API key deletion: " . $logError->getMessage());
            }

            $response['success'] = true;
            $response['message'] = 'API key deleted successfully';
            break;

        case 'delete_account':
            // 删除账户
            $confirmEmail = trim(strtolower($_POST['confirm_email'] ?? ''));

            if (empty($confirmEmail)) {
                throw new Exception('Email confirmation is required');
            }

            // 验证邮箱匹配
            if ($confirmEmail !== strtolower($user['email'])) {
                throw new Exception('Email address does not match your account email');
            }

            // 开始事务
            $pdo->beginTransaction();

            try {
                // 删除用户相关的所有数据

                // 1. 删除API密钥
                $stmt = $pdo->prepare("DELETE FROM pt_member_tokens WHERE user_id = ?");
                $stmt->execute([$user['id']]);

                // 2. 删除活动日志
                $stmt = $pdo->prepare("DELETE FROM pt_member_activity_log WHERE user_id = ?");
                $stmt->execute([$user['id']]);

                // 3. 删除通知
                $stmt = $pdo->prepare("DELETE FROM pt_member_notifications WHERE user_id = ?");
                $stmt->execute([$user['id']]);

                // 4. 删除会话
                $stmt = $pdo->prepare("DELETE FROM pt_member_sessions WHERE user_id = ?");
                $stmt->execute([$user['id']]);

                // 5. 最后删除用户主记录
                $stmt = $pdo->prepare("DELETE FROM pt_member WHERE id = ?");
                $stmt->execute([$user['id']]);

                // 提交事务
                $pdo->commit();

                // 销毁当前会话
                if (session_status() === PHP_SESSION_NONE) {
                    session_start();
                }
                session_destroy();

                // 清除记住我cookie
                if (isset($_COOKIE['remember_token'])) {
                    setcookie('remember_token', '', time() - 3600, '/', '', false, true);
                }

                $response['success'] = true;
                $response['message'] = 'Account deleted successfully';

            } catch (Exception $e) {
                // 回滚事务
                $pdo->rollBack();
                throw new Exception('Failed to delete account: ' . $e->getMessage());
            }
            break;

        default:
            throw new Exception('Invalid action');
    }
    
} catch (Exception $e) {
    // 清理输出缓冲区，确保干净的JSON响应
    if (ob_get_level()) {
        ob_clean();
    }

    $response['message'] = $e->getMessage();

    // 记录详细错误信息用于调试
    error_log("Profile action error - Action: " . ($action ?? 'unknown') . ", User ID: " . ($user['id'] ?? 'unknown') . ", Error: " . $e->getMessage());

    // 如果是数据库错误，提供更友好的错误信息
    if (strpos($e->getMessage(), 'SQLSTATE') !== false) {
        $response['message'] = 'Database error occurred. Please try again later.';
    }
}

header('Content-Type: application/json');
echo json_encode($response);
