<?php
/**
 * 权限配置文件
 * 定义系统中所有可用的权限和角色
 */

// 防止直接访问
if (!defined('ROOT_PATH')) {
    exit('Direct access not allowed');
}

return [
    // 权限定义
    'permissions' => [
        // 仪表板权限
        'dashboard' => [
            'view' => 'View dashboard and system overview'
        ],

        // 工具管理权限
        'tools' => [
            'view' => 'View tools list and details',
            'create' => 'Create new tools',
            'edit' => 'Edit existing tools',
            'delete' => 'Delete tools',
            'publish' => 'Publish/unpublish tools',
            'featured' => 'Mark tools as featured',
            'categories' => 'Manage tool categories'
        ],

        // 用户管理权限
        'users' => [
            'view' => 'View users list and details',
            'create' => 'Create new users',
            'edit' => 'Edit user information',
            'delete' => 'Delete users',
            'ban' => 'Ban/unban users',
            'export' => 'Export user data'
        ],

        // 数据分析权限
        'analytics' => [
            'view' => 'View analytics and reports',
            'export' => 'Export analytics data',
            'advanced' => 'Access advanced analytics features'
        ],

        // 系统设置权限
        'settings' => [
            'view' => 'View system settings',
            'edit' => 'Edit system settings',
            'security' => 'Manage security settings',
            'backup' => 'Manage backup settings',
            'maintenance' => 'Enable/disable maintenance mode'
        ],

        // 日志管理权限
        'logs' => [
            'view' => 'View system logs',
            'download' => 'Download log files',
            'clear' => 'Clear log files',
            'security' => 'View security logs'
        ],

        // 管理员管理权限
        'admins' => [
            'view' => 'View admin list',
            'create' => 'Create new admin accounts',
            'edit' => 'Edit admin accounts',
            'delete' => 'Delete admin accounts',
            'permissions' => 'Manage admin permissions'
        ],

        // 系统管理权限
        'system' => [
            'cache' => 'Manage system cache',
            'database' => 'Access database management',
            'files' => 'Manage system files',
            'updates' => 'Manage system updates'
        ]
    ],

    // 角色定义
    'roles' => [
        'super_admin' => [
            'name' => 'Super Administrator',
            'description' => 'Full system access with all permissions',
            'level' => 100,
            'permissions' => ['*'], // 通配符表示所有权限
            'color' => '#e74a3b',
            'icon' => 'fas fa-crown'
        ],

        'admin' => [
            'name' => 'Administrator',
            'description' => 'System administrator with most permissions',
            'level' => 80,
            'permissions' => [
                'dashboard.view',
                'tools.*',
                'users.view',
                'users.edit',
                'users.ban',
                'analytics.*',
                'settings.view',
                'settings.edit',
                'logs.view',
                'logs.download',
                'system.cache'
            ],
            'color' => '#f6c23e',
            'icon' => 'fas fa-user-shield'
        ],

        'manager' => [
            'name' => 'Manager',
            'description' => 'Content manager with editing permissions',
            'level' => 70,
            'permissions' => [
                'dashboard.view',
                'tools.view',
                'tools.create',
                'tools.edit',
                'tools.publish',
                'tools.categories',
                'users.view',
                'analytics.view',
                'analytics.export'
            ],
            'color' => '#36b9cc',
            'icon' => 'fas fa-user-tie'
        ],

        'editor' => [
            'name' => 'Editor',
            'description' => 'Content editor with limited permissions',
            'level' => 60,
            'permissions' => [
                'dashboard.view',
                'tools.view',
                'tools.create',
                'tools.edit',
                'analytics.view'
            ],
            'color' => '#1cc88a',
            'icon' => 'fas fa-user-edit'
        ],

        'viewer' => [
            'name' => 'Viewer',
            'description' => 'Read-only access to system data',
            'level' => 40,
            'permissions' => [
                'dashboard.view',
                'tools.view',
                'users.view',
                'analytics.view'
            ],
            'color' => '#858796',
            'icon' => 'fas fa-user'
        ]
    ],

    // 权限组
    'permission_groups' => [
        'content' => [
            'name' => 'Content Management',
            'description' => 'Permissions related to content management',
            'permissions' => [
                'tools.view',
                'tools.create',
                'tools.edit',
                'tools.delete',
                'tools.publish',
                'tools.featured',
                'tools.categories'
            ]
        ],

        'users' => [
            'name' => 'User Management',
            'description' => 'Permissions related to user management',
            'permissions' => [
                'users.view',
                'users.create',
                'users.edit',
                'users.delete',
                'users.ban',
                'users.export'
            ]
        ],

        'analytics' => [
            'name' => 'Analytics & Reports',
            'description' => 'Permissions related to analytics and reporting',
            'permissions' => [
                'analytics.view',
                'analytics.export',
                'analytics.advanced'
            ]
        ],

        'system' => [
            'name' => 'System Administration',
            'description' => 'Permissions related to system administration',
            'permissions' => [
                'settings.view',
                'settings.edit',
                'settings.security',
                'settings.backup',
                'settings.maintenance',
                'logs.view',
                'logs.download',
                'logs.clear',
                'logs.security',
                'system.cache',
                'system.database',
                'system.files',
                'system.updates'
            ]
        ],

        'admin_management' => [
            'name' => 'Admin Management',
            'description' => 'Permissions related to admin account management',
            'permissions' => [
                'admins.view',
                'admins.create',
                'admins.edit',
                'admins.delete',
                'admins.permissions'
            ]
        ]
    ],

    // 默认权限设置
    'defaults' => [
        'new_admin_role' => 'viewer',
        'guest_permissions' => [],
        'require_permission_for_api' => true
    ],

    // 权限继承规则
    'inheritance' => [
        'enabled' => true,
        'hierarchy' => [
            'super_admin' => ['admin', 'manager', 'editor', 'viewer'],
            'admin' => ['manager', 'editor', 'viewer'],
            'manager' => ['editor', 'viewer'],
            'editor' => ['viewer']
        ]
    ]
];
?>
