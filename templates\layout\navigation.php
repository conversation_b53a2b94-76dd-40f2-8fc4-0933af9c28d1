<?php
/**
 * 主导航栏组件
 * 包含Logo、菜单、搜索和用户操作
 */

$currentPage = $currentPage ?? '';
$toolCategories = getToolCategories();
?>

<!-- 主导航栏 -->
<nav class="navbar fixed top-0 left-0 right-0 z-50 bg-black border-b border-gray-800 transition-all duration-300">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
            <!-- Logo和品牌 -->
            <div class="flex items-center">
                <a href="/" class="flex items-center space-x-3 text-white hover:text-accent transition-colors duration-200">
                    <div class="w-8 h-8 bg-accent text-white flex items-center justify-center font-bold text-lg">
                        P
                    </div>
                    <span class="font-bold text-xl">Prompt2Tool</span>
                </a>
            </div>

            <!-- 桌面端导航菜单 -->
            <div class="hidden md:flex items-center space-x-8">
                <a href="/" 
                   class="text-gray-300 hover:text-white transition-colors duration-200 <?= $currentPage === 'home' ? 'text-accent' : '' ?>">
                    Home
                </a>
                
                <!-- 工具下拉菜单 -->
                <div class="relative group">
                    <a href="/tools" 
                       class="text-gray-300 hover:text-white transition-colors duration-200 flex items-center <?= str_contains($currentPage, 'tools') ? 'text-accent' : '' ?>">
                        Tools
                        <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </a>
                    
                    <!-- 下拉菜单 -->
                    <div class="absolute top-full left-0 mt-2 w-64 bg-gray-900 border border-gray-700 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
                        <div class="p-2">
                            <a href="/tools" class="block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800 transition-colors duration-200">
                                <div class="flex items-center">
                                    <span class="text-lg mr-3">🛠️</span>
                                    <div>
                                        <div class="font-medium">All Tools</div>
                                        <div class="text-xs text-gray-400">Browse all available tools</div>
                                    </div>
                                </div>
                            </a>
                            
                            <div class="border-t border-gray-700 my-2"></div>

                            <?php
                            // 获取工具数量最多的6个分类
                            $topCategories = getTopToolCategories(6);
                            foreach ($topCategories as $slug => $category): ?>
                            <a href="/tools/<?= $slug ?>"
                               class="block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800 transition-colors duration-200">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <span class="text-lg mr-3"><?= htmlspecialchars($category['icon']) ?></span>
                                        <div>
                                            <div class="font-medium"><?= htmlspecialchars($category['name']) ?></div>
                                            <div class="text-xs text-gray-400"><?= htmlspecialchars(truncateText($category['description'], 35)) ?></div>
                                        </div>
                                    </div>
                                    <div class="text-xs text-gray-500 bg-gray-800 px-2 py-1 rounded">
                                        <?= $category['tool_count'] ?>
                                    </div>
                                </div>
                            </a>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
                
                <a href="/about" 
                   class="text-gray-300 hover:text-white transition-colors duration-200 <?= $currentPage === 'about' ? 'text-accent' : '' ?>">
                    About
                </a>
                
                <a href="/contact" 
                   class="text-gray-300 hover:text-white transition-colors duration-200 <?= $currentPage === 'contact' ? 'text-accent' : '' ?>">
                    Contact
                </a>
            </div>

            <!-- 右侧操作区域 -->
            <div class="flex items-center space-x-4">
                <!-- 搜索按钮 -->
                <button type="button" 
                        class="text-gray-300 hover:text-white transition-colors duration-200 p-2"
                        data-modal="search-modal"
                        title="Search tools">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                </button>

                <!-- 移动端菜单按钮 -->
                <button type="button" 
                        class="md:hidden text-gray-300 hover:text-white transition-colors duration-200 p-2"
                        id="mobile-menu-btn">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <!-- 移动端菜单 -->
    <div class="md:hidden hidden bg-gray-900 border-t border-gray-700" id="mobile-menu">
        <div class="px-4 py-4 space-y-2">
            <a href="/" 
               class="block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800 transition-colors duration-200 <?= $currentPage === 'home' ? 'text-accent bg-gray-800' : '' ?>">
                Home
            </a>
            
            <a href="/tools" 
               class="block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800 transition-colors duration-200 <?= str_contains($currentPage, 'tools') ? 'text-accent bg-gray-800' : '' ?>">
                All Tools
            </a>
            
            <!-- 移动端工具分类 -->
            <?php
            $topCategories = getTopToolCategories(6);
            foreach ($topCategories as $slug => $category): ?>
            <a href="/tools/<?= $slug ?>"
               class="block px-8 py-2 text-gray-400 hover:text-white hover:bg-gray-800 transition-colors duration-200 text-sm">
                <?= htmlspecialchars($category['icon']) ?> <?= htmlspecialchars($category['name']) ?>
            </a>
            <?php endforeach; ?>

            <a href="/categories" class="block px-8 py-2 text-gray-500 hover:text-white hover:bg-gray-800 transition-colors duration-200 text-xs">
                View All Categories →
            </a>
            
            <div class="border-t border-gray-700 my-2"></div>
            
            <a href="/about" 
               class="block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800 transition-colors duration-200 <?= $currentPage === 'about' ? 'text-accent bg-gray-800' : '' ?>">
                About
            </a>
            
            <a href="/contact" 
               class="block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800 transition-colors duration-200 <?= $currentPage === 'contact' ? 'text-accent bg-gray-800' : '' ?>">
                Contact
            </a>
        </div>
    </div>
</nav>

<!-- 搜索模态框 -->
<div id="search-modal" class="modal fixed inset-0 bg-black bg-opacity-50 hidden flex items-center justify-center z-50">
    <div class="modal-content bg-gray-900 border border-gray-700 w-full max-w-2xl mx-4">
        <!-- 搜索头部 -->
        <div class="flex items-center justify-between p-6 border-b border-gray-700">
            <h3 class="text-lg font-semibold text-white">Search Tools</h3>
            <button type="button" 
                    class="text-gray-400 hover:text-white transition-colors duration-200"
                    data-modal-close>
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        
        <!-- 搜索输入 -->
        <div class="p-6">
            <div class="relative">
                <input type="text" 
                       placeholder="Search for tools..." 
                       class="w-full px-4 py-3 bg-black border border-gray-600 text-white placeholder-gray-400 focus:border-accent focus:outline-none"
                       id="search-input">
                <div class="absolute right-3 top-3">
                    <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                </div>
            </div>
            
            <!-- 搜索结果 -->
            <div id="search-results" class="mt-4 max-h-64 overflow-y-auto">
                <div class="text-center text-gray-400 py-8">
                    <svg class="w-12 h-12 mx-auto mb-4 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                    <p>Start typing to search for tools...</p>
                </div>
            </div>
        </div>
        
        <!-- 搜索底部 -->
        <div class="px-6 py-4 bg-gray-800 border-t border-gray-700">
            <div class="flex items-center justify-between text-sm text-gray-400">
                <div class="flex items-center space-x-4">
                    <span>Press <kbd class="px-2 py-1 bg-gray-700 text-gray-300">Enter</kbd> to search</span>
                    <span>Press <kbd class="px-2 py-1 bg-gray-700 text-gray-300">Esc</kbd> to close</span>
                </div>
                <div>
                    <span>Powered by Prompt2Tool</span>
                </div>
            </div>
        </div>
    </div>
</div>
