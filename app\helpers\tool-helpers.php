<?php
/**
 * 工具相关辅助函数
 * 专门为工具功能提供支持
 */

// 使用统一的数据库连接
require_once dirname(dirname(__DIR__)) . '/includes/database-connection.php';

/**
 * 获取工具分类数据
 */
function getToolCategories() {
    global $pdo;

    try {
        $stmt = $pdo->query("
            SELECT * FROM pt_tool_category
            WHERE status = 'active'
            ORDER BY sort_order, name
        ");
        $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $result = [];
        foreach ($categories as $category) {
            $result[$category['slug']] = [
                'id' => $category['id'],
                'name' => $category['name'],
                'slug' => $category['slug'],
                'description' => $category['description'],
                'icon' => $category['icon'] ?? '📁',
                'sort_order' => $category['sort_order']
            ];
        }

        return $result;
    } catch (Exception $e) {
        // 如果数据库查询失败，返回空数组
        return [];
    }
}

/**
 * 获取工具数量最多的分类（用于导航菜单）
 */
function getTopToolCategories($limit = 8) {
    global $pdo;

    try {
        $stmt = $pdo->query("
            SELECT tc.*, COUNT(t.id) as tool_count
            FROM pt_tool_category tc
            LEFT JOIN pt_tool t ON tc.id = t.category_id AND t.status = 'active'
            WHERE tc.status = 'active'
            GROUP BY tc.id
            HAVING tool_count > 0
            ORDER BY tool_count DESC, tc.sort_order, tc.name
            LIMIT " . intval($limit)
        );
        $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $result = [];
        foreach ($categories as $category) {
            $result[$category['slug']] = [
                'id' => $category['id'],
                'name' => $category['name'],
                'slug' => $category['slug'],
                'description' => $category['description'],
                'icon' => $category['icon'] ?? '📁',
                'sort_order' => $category['sort_order'],
                'tool_count' => $category['tool_count']
            ];
        }

        return $result;
    } catch (Exception $e) {
        // 如果数据库查询失败，返回前几个分类作为后备
        $allCategories = getToolCategories();
        return array_slice($allCategories, 0, $limit, true);
    }
}

/**
 * 获取单个工具分类
 */
function getToolCategory($slug) {
    $categories = getToolCategories();
    return $categories[$slug] ?? null;
}

/**
 * 获取工具数据 (示例数据)
 */
function getToolsData() {
    return [
        'development' => [
            [
                'name' => 'HTML Formatter',
                'slug' => 'html-formatter',
                'description' => 'Format and beautify your HTML code with proper indentation and structure.',
                'icon' => '🔧',
                'category' => 'Development',
                'category_slug' => 'development',
                'view_count' => 15234,
                'rating' => 4.8,
                'is_featured' => true,
                'is_new' => false,
                'tags' => ['HTML', 'Formatter', 'Code', 'Beautify'],
                'difficulty' => 'Easy',
                'estimated_time' => '< 1 min'
            ],
            [
                'name' => 'CSS Minifier',
                'slug' => 'css-minifier',
                'description' => 'Compress and minify CSS files to reduce file size and improve website loading speed.',
                'icon' => '📦',
                'category' => 'Development',
                'category_slug' => 'development',
                'view_count' => 12876,
                'rating' => 4.7,
                'is_featured' => false,
                'is_new' => false,
                'tags' => ['CSS', 'Minify', 'Optimize', 'Compress'],
                'difficulty' => 'Easy',
                'estimated_time' => '< 1 min'
            ],
            [
                'name' => 'JavaScript Formatter',
                'slug' => 'js-formatter',
                'description' => 'Format and beautify JavaScript code with proper indentation and syntax highlighting.',
                'icon' => '⚡',
                'category' => 'Development',
                'category_slug' => 'development',
                'view_count' => 11543,
                'rating' => 4.6,
                'is_featured' => false,
                'is_new' => false,
                'tags' => ['JavaScript', 'Formatter', 'Code'],
                'difficulty' => 'Easy',
                'estimated_time' => '< 1 min'
            ]
        ],
        'design' => [
            [
                'name' => 'Image Converter',
                'slug' => 'image-converter',
                'description' => 'Convert images between different formats like JPG, PNG, WebP, GIF, and more with high quality.',
                'icon' => '🖼️',
                'category' => 'Design & Media',
                'category_slug' => 'design',
                'view_count' => 18567,
                'rating' => 4.9,
                'is_featured' => false,
                'is_new' => true,
                'tags' => ['Image', 'Convert', 'Format', 'JPG', 'PNG'],
                'difficulty' => 'Easy',
                'estimated_time' => '< 2 min'
            ],
            [
                'name' => 'Color Palette Generator',
                'slug' => 'color-palette-generator',
                'description' => 'Generate beautiful color palettes for your design projects with various harmony rules.',
                'icon' => '🎨',
                'category' => 'Design & Media',
                'category_slug' => 'design',
                'view_count' => 7654,
                'rating' => 4.6,
                'is_featured' => false,
                'is_new' => true,
                'tags' => ['Color', 'Palette', 'Design', 'Harmony'],
                'difficulty' => 'Easy',
                'estimated_time' => '< 1 min'
            ]
        ],
        'productivity' => [
            [
                'name' => 'Text Counter',
                'slug' => 'text-counter',
                'description' => 'Count characters, words, sentences, and paragraphs in your text with detailed statistics.',
                'icon' => '📊',
                'category' => 'Productivity',
                'category_slug' => 'productivity',
                'view_count' => 9876,
                'rating' => 4.5,
                'is_featured' => false,
                'is_new' => false,
                'tags' => ['Text', 'Counter', 'Statistics', 'Words'],
                'difficulty' => 'Easy',
                'estimated_time' => '< 1 min'
            ]
        ],
        'utilities' => [
            [
                'name' => 'QR Code Generator',
                'slug' => 'qr-generator',
                'description' => 'Generate QR codes for URLs, text, WiFi credentials, contact info, and other data types.',
                'icon' => '📱',
                'category' => 'Utilities',
                'category_slug' => 'utilities',
                'view_count' => 22145,
                'rating' => 4.8,
                'is_featured' => true,
                'is_new' => false,
                'tags' => ['QR Code', 'Generate', 'Mobile', 'URL'],
                'difficulty' => 'Easy',
                'estimated_time' => '< 1 min'
            ]
        ],
        'marketing' => [
            [
                'name' => 'UTM Builder',
                'slug' => 'utm-builder',
                'description' => 'Generate UTM tracking parameters for your marketing campaigns to track traffic sources and campaign performance.',
                'icon' => '📊',
                'category' => 'Marketing & SEO',
                'category_slug' => 'marketing',
                'view_count' => 8542,
                'rating' => 4.6,
                'is_featured' => true,
                'is_new' => true,
                'tags' => ['UTM', 'Analytics', 'Tracking', 'Marketing', 'Google Analytics'],
                'difficulty' => 'Easy',
                'estimated_time' => '< 1 min'
            ]
        ],
        'security' => [
            [
                'name' => 'Password Generator',
                'slug' => 'password-generator',
                'description' => 'Generate strong, secure passwords with customizable length and character sets.',
                'icon' => '🔐',
                'category' => 'Security & Privacy',
                'category_slug' => 'security',
                'view_count' => 14321,
                'rating' => 4.7,
                'is_featured' => false,
                'is_new' => false,
                'tags' => ['Password', 'Security', 'Generate', 'Strong'],
                'difficulty' => 'Easy',
                'estimated_time' => '< 1 min'
            ]
        ]
    ];
}

/**
 * 获取指定分类的工具
 */
function getToolsByCategory($categorySlug) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("
            SELECT t.*
            FROM pt_tool t
            LEFT JOIN pt_tool_category tc ON t.category_id = tc.id
            WHERE tc.slug = ? AND t.status = 'active'
            ORDER BY t.sort_order, t.name
        ");
        $stmt->execute([$categorySlug]);
        $tools_data = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $result = [];
        foreach ($tools_data as $tool) {
            $result[] = [
                'id' => $tool['id'],
                'name' => $tool['name'],
                'slug' => $tool['slug'],
                'description' => $tool['description'],
                'url' => $tool['url'] ?: '/tools/' . $tool['slug'],
                'icon' => $tool['icon'] ?: '🔧',
                'tags' => $tool['tags'] ? explode(',', $tool['tags']) : [],
                'view_count' => $tool['view_count'] ?: 0,
                'featured' => (bool)$tool['is_featured'],
                'status' => $tool['status']
            ];
        }
        return $result;
    } catch (Exception $e) {
        // 如果数据库查询失败，返回硬编码数据作为后备
        $tools = getToolsData();
        return $tools[$categorySlug] ?? [];
    }
}

/**
 * 获取所有工具 (扁平化数组)
 */
function getAllTools() {
    global $pdo;

    try {
        $stmt = $pdo->query("
            SELECT t.*, tc.slug as category_slug
            FROM pt_tool t
            LEFT JOIN pt_tool_category tc ON t.category_id = tc.id
            WHERE t.status = 'active'
            ORDER BY t.sort_order, t.name
        ");
        $tools_data = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $result = [];
        foreach ($tools_data as $tool) {
            // 判断是否为新工具（30天内创建的）
            $isNew = false;
            if (isset($tool['created_at'])) {
                $createdTime = strtotime($tool['created_at']);
                $thirtyDaysAgo = time() - (30 * 24 * 60 * 60);
                $isNew = $createdTime > $thirtyDaysAgo;
            }

            $result[] = [
                'id' => $tool['id'],
                'name' => $tool['name'],
                'slug' => $tool['slug'],
                'description' => $tool['description'],
                'url' => $tool['url'] ?: '/tools/' . $tool['slug'],
                'icon' => $tool['icon'] ?: '🔧',
                'tags' => $tool['tags'] ? explode(',', $tool['tags']) : [],
                'view_count' => $tool['view_count'] ?: 0,
                'featured' => (bool)$tool['is_featured'],
                'is_new' => $isNew,
                'status' => $tool['status'],
                'category_slug' => $tool['category_slug'],
                'rating' => (float)$tool['rating'] ?: 5.0,
                'rating_count' => (int)$tool['rating_count'] ?: 0
            ];
        }
        return $result;
    } catch (Exception $e) {
        // 如果数据库查询失败，返回硬编码数据作为后备
        $allTools = [];
        $toolsData = getToolsData();

        foreach ($toolsData as $categoryTools) {
            $allTools = array_merge($allTools, $categoryTools);
        }

        return $allTools;
    }
}

/**
 * 根据slug获取工具（包含所有状态，权限控制在页面级别处理）
 */
function getToolBySlug($slug) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("
            SELECT t.*, tc.slug as category_slug, tc.name as category_name
            FROM pt_tool t
            LEFT JOIN pt_tool_category tc ON t.category_id = tc.id
            WHERE t.slug = ?
            LIMIT 1
        ");
        $stmt->execute([$slug]);
        $tool_data = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$tool_data) {
            return null;
        }

        // 判断是否为新工具（30天内创建的）
        $isNew = false;
        if (isset($tool_data['created_at'])) {
            $createdTime = strtotime($tool_data['created_at']);
            $thirtyDaysAgo = time() - (30 * 24 * 60 * 60);
            $isNew = $createdTime > $thirtyDaysAgo;
        }

        return [
            'id' => $tool_data['id'],
            'name' => $tool_data['name'],
            'slug' => $tool_data['slug'],
            'description' => $tool_data['description'],
            'url' => $tool_data['url'] ?: '/tools/' . $tool_data['slug'],
            'icon' => $tool_data['icon'] ?: '🔧',
            'tags' => $tool_data['tags'] ? explode(',', $tool_data['tags']) : [],
            'view_count' => $tool_data['view_count'] ?: 0,
            'featured' => (bool)$tool_data['is_featured'],
            'is_new' => $isNew,
            'status' => $tool_data['status'],
            'category_slug' => $tool_data['category_slug'],
            'category_name' => $tool_data['category_name'],
            'rating' => (float)$tool_data['rating'] ?: 5.0,
            'rating_count' => (int)$tool_data['rating_count'] ?: 0
        ];

    } catch (Exception $e) {
        error_log("Error in getToolBySlug: " . $e->getMessage());
        return null;
    }
}

/**
 * 获取热门工具
 */
function getPopularTools($limit = 8) {
    $allTools = getAllTools();

    // 按浏览量排序
    usort($allTools, function($a, $b) {
        return $b['view_count'] - $a['view_count'];
    });

    return array_slice($allTools, 0, $limit);
}

/**
 * 格式化数字为简洁形式
 * @param int $number 要格式化的数字
 * @return string 格式化后的字符串
 */
function formatNumber($number) {
    $number = (int)$number;

    if ($number < 1000) {
        return number_format($number) . '+';
    } elseif ($number < 1000000) {
        $formatted = $number / 1000;
        if ($formatted == floor($formatted)) {
            return number_format($formatted, 0) . 'K+';
        } else {
            return number_format($formatted, 1) . 'K+';
        }
    } elseif ($number < 1000000000) {
        $formatted = $number / 1000000;
        if ($formatted == floor($formatted)) {
            return number_format($formatted, 0) . 'M+';
        } else {
            return number_format($formatted, 1) . 'M+';
        }
    } else {
        $formatted = $number / 1000000000;
        if ($formatted == floor($formatted)) {
            return number_format($formatted, 0) . 'B+';
        } else {
            return number_format($formatted, 1) . 'B+';
        }
    }
}

/**
 * 获取特色工具
 */
function getFeaturedTools() {
    $allTools = getAllTools();

    return array_filter($allTools, function($tool) {
        return $tool['featured'] ?? false;
    });
}

/**
 * 获取新工具 - 按最新发布时间排序
 */
function getNewTools($limit = 4) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("
            SELECT t.*, tc.slug as category_slug
            FROM pt_tool t
            LEFT JOIN pt_tool_category tc ON t.category_id = tc.id
            WHERE t.status = 'active'
            ORDER BY t.created_at DESC, t.id DESC
            LIMIT ?
        ");
        $stmt->execute([$limit]);
        $tools_data = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $result = [];
        foreach ($tools_data as $tool) {
            $result[] = [
                'id' => $tool['id'],
                'name' => $tool['name'],
                'slug' => $tool['slug'],
                'description' => $tool['description'],
                'url' => $tool['url'] ?: '/tools/' . $tool['slug'],
                'icon' => $tool['icon'] ?: '🔧',
                'tags' => $tool['tags'] ? explode(',', $tool['tags']) : [],
                'view_count' => $tool['view_count'] ?: 0,
                'featured' => (bool)$tool['is_featured'],
                'is_new' => true, // 所有返回的都是新工具
                'status' => $tool['status'],
                'category_slug' => $tool['category_slug'],
                'created_at' => $tool['created_at'],
                'rating' => 4.5
            ];
        }
        return $result;
    } catch (Exception $e) {
        // 如果数据库查询失败，返回所有工具按创建时间排序
        $allTools = getAllTools();

        // 按创建时间排序（如果有的话）
        usort($allTools, function($a, $b) {
            $dateA = strtotime($a['created_at'] ?? '1970-01-01');
            $dateB = strtotime($b['created_at'] ?? '1970-01-01');
            return $dateB - $dateA;
        });

        return array_slice($allTools, 0, $limit);
    }
}

/**
 * 搜索工具
 */
function searchTools($query, $limit = 20) {
    $allTools = getAllTools();
    $results = [];
    
    $query = strtolower($query);
    
    foreach ($allTools as $tool) {
        $score = 0;
        
        // 名称匹配 (权重最高)
        if (strpos(strtolower($tool['name']), $query) !== false) {
            $score += 10;
        }
        
        // 描述匹配
        if (strpos(strtolower($tool['description']), $query) !== false) {
            $score += 5;
        }
        
        // 标签匹配
        foreach ($tool['tags'] as $tag) {
            if (strpos(strtolower($tag), $query) !== false) {
                $score += 3;
            }
        }
        
        // 分类匹配
        if (strpos(strtolower($tool['category']), $query) !== false) {
            $score += 2;
        }
        
        if ($score > 0) {
            $tool['search_score'] = $score;
            $results[] = $tool;
        }
    }
    
    // 按相关性排序
    usort($results, function($a, $b) {
        return $b['search_score'] - $a['search_score'];
    });
    
    return array_slice($results, 0, $limit);
}

/**
 * 增加工具浏览量 (示例函数)
 */
function incrementToolViews($slug) {
    // 这里应该更新数据库
    // 暂时只在会话中记录
    if (!isset($_SESSION['tool_views'])) {
        $_SESSION['tool_views'] = [];
    }
    
    if (!isset($_SESSION['tool_views'][$slug])) {
        $_SESSION['tool_views'][$slug] = 0;
    }
    
    $_SESSION['tool_views'][$slug]++;
}

/**
 * 获取工具统计信息
 */
function getToolStats() {
    $allTools = getAllTools();
    $categories = getToolCategories();
    
    $totalViews = array_sum(array_column($allTools, 'view_count'));
    $avgRating = round(array_sum(array_column($allTools, 'rating')) / count($allTools), 1);
    $featuredCount = count(getFeaturedTools());
    $newCount = count(getNewTools());
    
    return [
        'total_tools' => count($allTools),
        'total_categories' => count($categories),
        'total_views' => $totalViews,
        'average_rating' => $avgRating,
        'featured_count' => $featuredCount,
        'new_count' => $newCount
    ];
}
