<?php
/**
 * Google OAuth Login API
 * 处理Google Identity Services的JWT token登录
 */

// 根据请求类型设置适当的头部
$contentType = $_SERVER['CONTENT_TYPE'] ?? '';
if (strpos($contentType, 'application/json') !== false) {
    header('Content-Type: application/json');
    header('Access-Control-Allow-Origin: *');
    header('Access-Control-Allow-Methods: POST');
    header('Access-Control-Allow-Headers: Content-Type');
}

// 允许POST和GET请求（用于处理重定向）
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// 使用统一的数据库连接
require_once dirname(dirname(dirname(__DIR__))) . '/includes/database-connection.php';
require_once __DIR__ . '/google-jwt-helper.php';

try {
    // 获取请求数据（支持JSON和表单数据）
    $input = null;
    $credential = null;
    $redirect = '/dashboard';

    // 检查是否是JSON请求
    $contentType = $_SERVER['CONTENT_TYPE'] ?? '';
    if (strpos($contentType, 'application/json') !== false) {
        $input = json_decode(file_get_contents('php://input'), true);
        $credential = $input['credential'] ?? null;
        $redirect = $input['redirect'] ?? '/dashboard';
    } else {
        // 表单数据
        $credential = $_POST['credential'] ?? null;
        $redirect = $_POST['redirect'] ?? '/dashboard';
    }

    if (!$credential) {
        throw new Exception('Missing Google credential');
    }
    
    // 获取Google OAuth配置
    $stmt = $pdo->prepare("SELECT setting_value FROM pt_system_config WHERE setting_key = ?");
    $stmt->execute(['google_oauth_client_id']);
    $clientId = $stmt->fetchColumn();
    
    if (!$clientId) {
        throw new Exception('Google OAuth not configured');
    }
    
    // 验证JWT token
    $userInfo = GoogleJWTHelper::verifyGoogleJWT($credential, $clientId);
    
    if (!$userInfo) {
        throw new Exception('Invalid Google credential');
    }
    
    // 检查用户是否已存在
    $stmt = $pdo->prepare("SELECT * FROM pt_member WHERE email = ?");
    $stmt->execute([$userInfo['email']]);
    $user = $stmt->fetch();
    
    if ($user) {
        // 用户已存在，更新登录信息
        $stmt = $pdo->prepare("UPDATE pt_member SET last_login = NOW() WHERE id = ?");
        $stmt->execute([$user['id']]);
        
        // 创建会话
        session_start();
        session_regenerate_id(true); // 重新生成会话ID以提高安全性
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['user_email'] = $user['email'];
        $_SESSION['user_name'] = $user['first_name'] . ' ' . $user['last_name'];
        $_SESSION['username'] = $user['username'];
        $_SESSION['login_time'] = time();
        $_SESSION['login_method'] = 'google';
        
        // 检查是否是AJAX请求
        $isAjax = !empty($_SERVER['HTTP_X_REQUESTED_WITH']) &&
                  strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest';
        $isJsonRequest = strpos($_SERVER['CONTENT_TYPE'] ?? '', 'application/json') !== false;

        if ($isAjax || $isJsonRequest) {
            // AJAX请求，返回JSON
            echo json_encode([
                'success' => true,
                'message' => 'Login successful',
                'data' => [
                    'redirect' => $redirect,
                    'user' => [
                        'id' => $user['id'],
                        'email' => $user['email'],
                        'name' => $user['first_name'] . ' ' . $user['last_name']
                    ]
                ]
            ]);
        } else {
            // 表单提交，直接重定向
            header('Location: ' . $redirect);
            exit;
        }
    } else {
        // 用户不存在，自动注册
        $firstName = $userInfo['given_name'] ?? '';
        $lastName = $userInfo['family_name'] ?? '';
        $avatar = $userInfo['picture'] ?? null;
        
        $stmt = $pdo->prepare("
            INSERT INTO pt_member (
                username, email, password, first_name, last_name, avatar,
                status, email_verified, created_at, last_login
            ) VALUES (?, ?, '', ?, ?, ?, 'active', 1, NOW(), NOW())
        ");
        
        // 生成唯一用户名
        $username = generateUniqueUsername($userInfo['email'], $pdo);
        
        $stmt->execute([
            $username,
            $userInfo['email'],
            $firstName,
            $lastName,
            $avatar
        ]);
        
        $userId = $pdo->lastInsertId();
        
        // 创建会话
        session_start();
        session_regenerate_id(true); // 重新生成会话ID以提高安全性
        $_SESSION['user_id'] = $userId;
        $_SESSION['user_email'] = $userInfo['email'];
        $_SESSION['user_name'] = trim($firstName . ' ' . $lastName);
        $_SESSION['username'] = $username;
        $_SESSION['login_time'] = time();
        $_SESSION['login_method'] = 'google';
        
        // 检查是否是AJAX请求
        $isAjax = !empty($_SERVER['HTTP_X_REQUESTED_WITH']) &&
                  strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest';
        $isJsonRequest = strpos($_SERVER['CONTENT_TYPE'] ?? '', 'application/json') !== false;

        if ($isAjax || $isJsonRequest) {
            // AJAX请求，返回JSON
            echo json_encode([
                'success' => true,
                'message' => 'Registration and login successful',
                'data' => [
                    'redirect' => $redirect,
                    'user' => [
                        'id' => $userId,
                        'email' => $userInfo['email'],
                        'name' => trim($firstName . ' ' . $lastName)
                    ]
                ]
            ]);
        } else {
            // 表单提交，直接重定向
            header('Location: ' . $redirect);
            exit;
        }
    }
    
} catch (Exception $e) {
    error_log("Google login error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}



/**
 * 生成唯一用户名
 */
function generateUniqueUsername($email, $pdo) {
    $baseUsername = strtolower(explode('@', $email)[0]);
    $baseUsername = preg_replace('/[^a-z0-9_]/', '', $baseUsername);
    
    $username = $baseUsername;
    $counter = 1;
    
    while (true) {
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM pt_member WHERE username = ?");
        $stmt->execute([$username]);
        
        if ($stmt->fetchColumn() == 0) {
            return $username;
        }
        
        $username = $baseUsername . '_' . $counter;
        $counter++;
        
        if ($counter > 1000) {
            $username = $baseUsername . '_' . uniqid();
            break;
        }
    }
    
    return $username;
}
?>
